.ice-design-header-aside-footer-responsive-layout-#{$theme} {
    /* LAYOUT */
    &.ice-design-layout {
        padding: 0 20px;
        width: 100%;

        background-color: $ice-layout-bg;
        background-repeat: no-repeat;
        background-size: 100%;

        /* HEADER */
        .ice-design-layout-header {
            display: flex;

            margin: 0px auto;
            padding: 24px 0;
            width: 100%;

            background: transparent;

            box-shadow: none;

            justify-content: space-between;
            align-items: center;

            .ice-design-layout-header-item {
                display: flex;

                align-items: center;

                .header-item {
                    display: flex;

                    margin-right: 24px;

                    color: #ffffff;

                    align-items: center;

                    .header-item-count-icon {
                        &.ice-icon-stable-medium:before {
                            font-size: 16px;
                        }
                    }

                    .header-item-label {
                        margin-left: 12px;

                        color: #ffffff;
                    }

                    .basic-example {
                        color: #ffffff;
                    }

                    .ice-design-header-userpannel {
                        display: flex;

                        align-items: center;

                        .user-avatar {
                            width: 32px;
                            height: 32px;
                        }

                        .user-profile {
                            display: flex;

                            margin-right: 12px;
                            margin-left: 12px;

                            flex-direction: column;

                            .user-name,
                            .user-department {
                                font-size: 14px;

                                color: #ffffff;
                            }
                        }
                    }
                }
            }
        }

        /* ASIDE */
        .ice-design-layout-aside {
            background: $ice-aside-bg;

            .collapse-btn {
                display: block;

                margin-bottom: 20px;
                width: 100%;

                text-align: center;

                color: $ice-collapse-btn-color;

                cursor: pointer;
            }

            .ice-menu {
                background: $ice-aside-menu-bg;

                transition: all 0.3s ease;

                &.ice-menu-inline .ice-icon-stable::before {
                    font-weight: normal;
                }
            }

            .ice-menu.ice-menu-root.ice-menu-inline > .ice-menu-submenu > .ice-menu-submenu-title,
            .ice-menu.ice-menu-root.ice-menu-inline > .ice-menu-item {
                margin-bottom: 1px;

                font-weight: bold;

                color: $ice-aside-text-color;
            }

            .ice-menu.ice-menu-root .ice-menu-inline > .ice-menu-item {
                margin-bottom: 1px;

                color: $ice-aside-sub-text-color;
            }

            .ice-menu .ice-menu-item:hover,
            .ice-menu.ice-menu-horizontal > .ice-menu-item:hover,
            .ice-menu.ice-menu-root > .ice-menu-item:hover {
                border-radius: 6px;

                color: #ffffff;
                background-image: linear-gradient(
                90deg,
                $ice-aside-menu-item-selected-gradient-left-bg 0%,
                $ice-aside-menu-item-selected-gradient-right-bg 100%
                );
            }

            .ice-menu .ice-menu-item > a:hover,
            .ice-menu.ice-menu-horizontal > .ice-menu-item:hover,
            .ice-menu.ice-menu-root > .ice-menu-item > a:hover {
                color: $ice-aside-text-hover-color;
            }

            .ice-menu:not(.ice-menu-horizontal) .ice-menu-item-selected {
                border-radius: 6px;

                background-image: linear-gradient(
                90deg,
                $ice-aside-menu-item-selected-gradient-left-bg 0%,
                $ice-aside-menu-item-selected-gradient-right-bg 100%
                );
            }

            .ice-menu .ice-menu-submenu-vertical > .ice-menu:after {
                background: $ice-aside-menu-submenu-vertical-bg;
            }

            .ice-menu:not(.ice-menu-horizontal) .ice-menu-item-selected:before,
            .ice-menu-sub .ice-menu-item:hover:before {
                width: 0;
            }

            .ice-menu-item-selected > a {
                color: $ice-aside-menu-item-selected-text-color;
            }

            .ice-menu-item:hover {
                color: $ice-aside-text-hover-color;
                background: $ice-aside-menu-submenu-item-hover-bg;
            }

            /* ASIDE COLLAPSED */
            .ice-menu.ice-menu-collapse {
                color: $ice-aside-collapse-menu-text-color;

                > .ice-menu-item {
                    text-align: center;
                }

                .ice-menu-submenu {
                    &:hover {
                        border-radius: 6px;

                        color: $ice-aside-menu-item-selected-text-color;
                        background-image: linear-gradient(
                        90deg,
                        $ice-aside-menu-item-selected-gradient-left-bg 0%,
                        $ice-aside-menu-item-selected-gradient-right-bg 100%
                        );

                        transition: initial;
                    }
                }

                .ice-menu-sub {
                    background: #ffffff;
                }

                .ice-menu .ice-menu-item > a:hover {
                    color: $ice-aside-menu-collapse-selected-text-color;
                }

                .ice-menu-item-selected {
                    text-align: left;

                    color: $ice-aside-menu-item-selected-text-color;
                }

                .ice-menu .ice-menu-item:hover,
                .ice-menu.ice-menu-horizontal > .ice-menu-item:hover,
                .ice-menu.ice-menu-root > .ice-menu-item:hover {
                    color: $ice-aside-menu-collapse-selected-text-color;
                    background: transparent;
                }
            }

            .ice-menu.ice-menu-collapse .ice-menu-submenu-title {
                text-align: center;
            }

            .ice-menu.ice-menu-vertical .ice-icon-stable {
                margin-right: 0;
            }

            .ice-menu.ice-menu-collapse .ice-menu-submenu.ice-menu-submenu-active > .ice-menu-submenu-title {
                color: #ffffff;
                background: transparent;

                &:hover:before {
                    width: 0;
                }
            }

            .ice-menu.ice-menu-collapse > .ice-menu-submenu-active > .ice-menu-submenu-title:before,
            .ice-menu.ice-menu-collapse > .ice-menu-submenu-selected > .ice-menu-submenu-title:before,
            .ice-menu.ice-menu-collapse .ice-menu-item:hover:before {
                width: 0;
            }

            .ice-menu.ice-menu-collapse .ice-menu:not(.ice-menu-horizontal) .ice-menu-item-selected {
                background: $ice-aside-menu-collapse-bg;

                a {
                    color: $ice-aside-menu-collapse-selected-text-color;
                }
            }

            .ice-menu.ice-menu-collapse > .ice-menu-submenu-selected > .ice-menu-submenu-title {
                color: $ice-aside-menu-item-selected-text-color;
                background-image: linear-gradient(
                90deg,
                $ice-aside-menu-item-selected-gradient-left-bg 0%,
                $ice-aside-menu-item-selected-gradient-right-bg 100%
                );

                &:before {
                    width: 0;
                }
            }
        }

        /* MAIN */
        .ice-layout-main {
            overflow: hidden;

            padding: 0 0 0 20px;
        }

        /* FOOTER */
        .ice-design-layout-footer-body {
            display: flex;

            margin: 20px auto;
            width: 100%;

            justify-content: space-between;
            align-items: center;

            .copyright {
                font-size: 12px;
                line-height: 1.5;
                text-align: left;

                color: $ice-copyright-text-color;
            }

            .copyright-link {
                color: $ice-copyright-text-color;

                &:hover {
                    text-decoration: none;

                    color: $ice-copyright-text-hover-color;
                }
            }
        }
    }
}

.user-profile-menu-item {
    display: flex;

    height: 36px;

    line-height: 36px;

    cursor: pointer;

    align-items: center;
    justify-content: center;
}

@media screen and (max-width: 1200px) {
}

@media screen and (max-width: 721px) and (min-width: 1199px) {
}

@media screen and (max-width: 720px) {
    .ice-design-header-aside-footer-responsive-layout-#{$theme} {
        .logo {
            width: 80px;

            .logo-text {
                font-size: 22px;
            }
        }

        &.ice-design-layout {
            .ice-design-layout-header {
                .ice-design-header-userpannel {
                    margin-left: 0;

                    .user-avatar {
                        width: 32px;
                        height: 32px;
                    }

                    .user-profile {
                        .user-name,
                        .user-department {
                            font-size: 14px;
                        }
                    }
                }

                .ice-design-layout-header-menu {
                    .ice-menu .ice-menu-item {
                        padding: 0 5px;
                    }
                }
            }

            .ice-design-layout-aside {
                position: fixed;
                top: 0px;
                bottom: 0;
                left: -240px;
                z-index: 9999;

                padding: 0 20px;

                background: #2e323e;
            }

            .ice-layout-main {
                padding: 0;
            }

            .open-drawer {
                transform: translateX(240px);

                .logo {
                    margin: 20px 0;
                    width: 200px;

                    text-align: center;
                }
            }

            .open-drawer-bg {
                position: fixed;
                top: 0px;
                right: 0;
                bottom: 0;
                left: 0;
                z-index: 999;

                background: #000000;

                opacity: 0.3;
            }
        }
    }
}
