// 菜单配置
// headerMenuConfig：头部导航配置
// asideMenuConfig：侧边导航配置

const headerMenuConfig = [];

const asideMenuConfig = [
    {
        name: '首页',
        path: '/',
        icon: 'home',
    },
    {
        name: '收费管理',
        path: '/order',
        icon: 'coupons',
        children: [
            { name: '柜台缴费', path: '/order/SaleWater' },
            { name: '订单管理', path: '/order/OrderSearch' },
        ],
    },
    {
        name: '用户管理',
        path: '/user',
        icon: 'yonghu',
        children: [
            {name: '用户信息', path: '/user/UserSearch'},
            {name: '预开户', path: '/user/beforeUserOpen'},
            // { name: '预换表', path: '/user/beforeWaterMeterChange' },
            {name: '用户开户', path: '/user/UserOpen'},
            {name: '自主开户', path: '/user/selfOpen'},
            {name: '销户记录', path: '/user/UserDisbursements'},
            {name: '水表更换', path: '/user/UserExchange'},
            {name: '水表更换记录', path: '/user/meterChangeRecord'},
            {name: '修改记录', path: '/user/modifyRecord'},
            {name: '预换表记录', path: '/user/BeforehandChangeRecord'},
            {name: '不动产过户', path: '/user/BDCTransferRecord'},
        ],
    },
    {
        name: 'IC卡管理',
        path: '/card',
        icon: 'directory',
        children: [
            { name: '补卡', path: '/card/ReplacementCard' },
            { name: '补卡记录查询', path: '/card/CardRecord' },
            { name: '管理卡制作', path: '/card/systemCard' },
            { name: 'IC卡终端管理', path: '/card/CardTerminal' },
        ],
    },
    {
        name: '水表管理',
        path: '/watermeter',
        icon: 'item',
        children: [
            { name: '水表信息', path: '/watermeter/WatermeterSearch' },
            { name: '二维码更换记录', path: '/watermeter/QRCode' },
        ],
    },
    {
        name: '远传表管理',
        path: '/remoteManage',
        icon: 'link',
        children: [
            { name: '恒信集中器管理', path: '/remoteManage/concentrator' },
            { name: '恒信远传表管理', path: '/remoteManage/remoteMeterList' },
            { name: '恒信远传表补卡', path: '/remoteManage/blueCard' },
            { name: '恒信远传表补卡记录', path: '/remoteManage/blueCardRecord' },
            { name: '民生集中器管理', path: '/remoteManage/MSconcentrator' },
            {name: '民生远传表管理', path: '/remoteManage/MSremoteMeterList'},
            {name: '民生抄表管理', path: '/remoteManage/MSMeterReading'},
            {name: '抄表任务管理', path: '/remoteManage/MSTask'},
            {name: '山科远传表管理', path: '/remoteManage/SKremoteMeterList'},
            {name: '杭州竞达远传表管理', path: '/remoteManage/JDRemoteMeterList'},
            {name: '山东科德远传表管理', path: '/remoteManage/KDRemoteMeterList'},
            {name: '湖南威铭远传表管理', path: '/remoteManage/WMRemoteMeterList'},
            {name: '河南新天远传表管理', path: '/remoteManage/XTRemoteMeterList'},
            {name: '宁夏隆基远传表管理', path: '/remoteManage/LJRemoteMeterList'},
            {name: '威傲远传表管理', path: '/remoteManage/WARemoteMeterList'},
            {name: '远传表开关阀记录', path: '/remoteManage/MSopenCloseRecord'},
            {name: '远传表账单', path: '/remoteManage/remoteMeterBill'},
            {name: '远传表手动批量出账', path: '/remoteManage/batchManualAccount'},
            {name: '远传表数据', path: '/remoteManage/remoteMeterDataList'},
        ],
    },
    {
        name: '机械表管理',
        path: '/machineryMeter',
        icon: 'table',
        children: [
            { name: '机械表用户管理', path: '/machineryMeter/machineryMeterUser' },
            { name: '抄表任务管理', path: '/machineryMeter/publishedTask' },
            { name: '机械表账单管理', path: '/machineryMeter/machineryMeterBill' },
            { name: '账单待打发票', path: '/machineryMeter/billInvoice' },
            { name: '账单发票打印记录', path: '/machineryMeter/billInvoicePrintLog' },
            { name: '抄表轨迹', path: '/machineryMeter/pathMap' },
        ],
    },
    {
        name: '系统设置',
        path: '/System',
        icon: 'shezhi',
        children: [
            { name: '小区管理', path: '/System/CommunityManage' },
            { name: '片区管理', path: '/System/areaManage' },
            { name: '用水性质配置', path: '/System/rule' },
            { name: '分组管理', path: '/System/GroupManagement' },
        ],
    },
    {
        name: '发票管理',
        path: '/Invoice',
        icon: 'cascades',
        children: [
            { name: '发票领用', path: '/Invoice/getInvoices' },
            { name: '发票领用记录', path: '/Invoice/invoiceRecords' },
            { name: '开票记录', path: '/Invoice/makeOutinvoiceRecords' },
            { name: '发票模板', path: '/Invoice/formwork' },
            { name: '开票信息维护', path: '/Invoice/invoiceInformationMaintenance' },
            { name: '工商税务信息维护', path: '/Invoice/businessInfoManage' },
        ],
    },
    {
        name: '消息管理',
        path: '/msgManage',
        icon: 'speaker',
        children: [{ name: '发送记录', path: '/msgManage/reminderRecord' }],
    },
    {
        name: '统计报表',
        path: '/statement',
        icon: 'copy',
        children: [
            { name: '水费报表', path: '/statement/waterRateStatement' },
            { name: '其他报表', path: '/statement/otherStatement' },
            // { name: '每日对账异常订单查询', path: '/statement/dailyReconciliation' }
        ],
    },
    {
        name: 'IC卡表查表',
        path: '/CheckWaterMeter',
        icon: 'table',
        children: [
            {
                name: '卡表用户管理',
                path: '/CheckWaterMeter/CardTableUserManagement',
            },
            {
                name: '查表任务管理',
                path: '/CheckWaterMeter/TableLookupTaskManagement',
            },
        ],
    },
    {
        name: '流程管理',
        path: '/ProcessManagement',
        icon: 'copy',
        children: [
            { name: '我的申请', path: '/ProcessManagement/MyApplication' },
            { name: '我的审批', path: '/ProcessManagement/MyApproval' },
            { name: '申请查询', path: '/ProcessManagement/ApplyForInquiry' },
            { name: '审批流配置', path: '/ProcessManagement/ApprovalConfigure' },
        ],
    },
    {
        name: '消息公告',
        path: '/NewsAndNotice',
        icon: 'speaker',
        children: [
            { name: '通知公告', path: '/NewsAndNotice/NoticeList' },
            { name: '新闻资讯', path: '/NewsAndNotice/NewsList' },
            { name: '消息通知', path: '/NewsAndNotice/NotificationList' },
        ],
    },
];

export { headerMenuConfig, asideMenuConfig };
