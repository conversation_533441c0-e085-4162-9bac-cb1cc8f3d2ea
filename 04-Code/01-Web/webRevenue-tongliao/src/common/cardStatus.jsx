function cardStatus(i) {
  let temp=Number(i)
  switch (temp){
    case 10:
      return '水表编号非法';
    case 100:
      return '水表类型不正确';
    case 101:
      return '读卡失败';
    case 102:
      return '蓝色钥匙卡写卡失败';
    case 103:
      return '蓝色钥匙卡读卡失败';
    case 104:
      return '蓝色钥匙卡坏卡';
    case 200:
      return '灰色钥匙卡初始化失败';
    case 201:
      return '灰色钥匙卡验证失败';
    case 202:
      return '灰色钥匙卡写卡失败';
    case 300:
      return '圆卡读卡失败';
    case 301:
      return '圆卡读卡失败';
    case 302:
      return '圆卡写卡失败';
  }
}

export default (cardStatus)
