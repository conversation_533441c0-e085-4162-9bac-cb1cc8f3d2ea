// 水表类型
export const waterMeterType = [
    { label: '全部', value: '' },
    { label: '机械表', value: '机械表' },
    { label: 'IC卡表', value: 'IC卡表' },
    { label: '远传表', value: '远传表' },
];

// 水表种类
export const waterMeterKind = [
    { label: '全部', value: '' },
    { label: '预付费2', value: '预付费2' },
    { label: '预付费5', value: '预付费5' },
    { label: '阶梯4428', value: '阶梯4428' },
    { label: '预付费4442', value: '预付费4442' },
    { label: '机械表', value: '机械表' },
    { label: '无线远传', value: '无线远传' },
    { label: '有线远传', value: '有线远传' },
];

// 水表状态
export const waterMeterStatus = [
    { label: '全部', value: '' },
    { label: '库存', value: 0 },
    { label: '已使用', value: 1 },
    { label: '返修', value: 2 },
    { label: '废弃', value: 3 },
    { label: '失窃', value: 4 },
];

// 水表厂家
export const waterMeterCompany = [
    { label: '全部', value: '' },
    { label: '扬州恒信', value: '扬州恒信' },
    { label: '深圳华旭', value: '深圳华旭' },
    { label: '辽宁民生', value: '辽宁民生' },
    { label: '山科', value: '山科' },
    { label: '机械表厂家', value: '机械表厂家' },
    { label: '杭州竞达', value: '杭州竞达' },
    { label: '山东科德', value: '山东科德' },
    { label: '湖南威铭', value: '湖南威铭' },
    { label: '河南新天', value: '河南新天' },
    {label: '宁夏隆基', value: '宁夏隆基'},
    {label: '威傲', value: '威傲'},
];

// 远传表水表厂家
export const remoteWaterMeterCompany = [
    { label: '全部', value: '' },
    { label: '扬州恒信', value: '扬州恒信' },
    { label: '辽宁民生', value: '辽宁民生' },
    { label: '山科', value: '山科' },
    { label: '杭州竞达', value: '杭州竞达' },
    { label: '山东科德', value: '山东科德' },
    { label: '湖南威铭', value: '湖南威铭' },
    { label: '河南新天', value: '河南新天' },
    {label: '宁夏隆基', value: '宁夏隆基'},
    {label: '威傲', value: '威傲'},
];

// 水表口径
export const waterMeterCaliber = [
    { label: 15, value: '15' },
    { label: 20, value: '20' },
    { label: 25, value: '25' },
    { label: 40, value: '40' },
    { label: 50, value: '50' },
    { label: 80, value: '80' },
    { label: 100, value: '100' },
    { label: 150, value: '150' },
    { label: 200, value: '200' },
    { label: `100*32`, value: `100*32` },
];

// 水表安装位置
export const location = [
    { label: '全部', value: '' },
    { label: '厨房', value: '厨房' },
    { label: '卫生间', value: '卫生间' },
    { label: '车库', value: '车库' },
    { label: '管道井', value: '管道井' },
    { label: '仓房', value: '仓房' },
    { label: '地下室', value: '地下室' },
    { label: '井表', value: '井表' },
    { label: '换热站', value: '换热站' },
    { label: '其它位置', value: '其它位置' },
];

// 远传表阀门状态
export const valveStatus = [
    { label: '全部', value: '' },
    { label: '开阀', value: '1' },
    { label: '关阀', value: '2' },
    { label: '半悬', value: '3' },
];

// 抄表状态
export const publishedTaskStatus = [
    { label: '全部', value: '' },
    { label: '未抄表', value: '0' },
    { label: '已抄表', value: '1' },
    { label: '已删除', value: '2' },
    { label: '已过期', value: '3' },
];

// 欠费状态
export const billStatusStatus = [
    { value: '', label: '全部' },
    { value: '0', label: '欠费' },
    { value: '1', label: '不欠费' },
];
