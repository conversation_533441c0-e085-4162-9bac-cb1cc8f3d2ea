
export default function waterPrice(record) {
  let waterPrice = record.descr
  let str = ''
  if (record.stepBalance == null || record.stepBalance == '{}') {
    str = waterPrice.substring(waterPrice.lastIndexOf(":") + 1, waterPrice.length)
    if (str.indexOf('+')>-1){
      str=str.substring(0,str.indexOf('+'))
    }
  } else {
    if (waterPrice.indexOf("污水") > waterPrice.indexOf("第")) {
      waterPrice = waterPrice.substring(0, waterPrice.indexOf("+污水"));
    } else {
      waterPrice = waterPrice.substring(waterPrice.indexOf("第"));
    }
    let arr = waterPrice.split("第")
    for (let i = 0; i < arr.length; i++) {
      if (arr[i].indexOf('阶梯') != -1) {
        str += arr[i].substring(arr[i].indexOf(':') + 1, arr[i].length)
      }
    }
  }

  // let price = str.split('+');
  // let priceStr = '';
  // for (let i = 0; i < price.length; i++) {
  //   priceStr += price[i].split('*')[1] + ('\\r\\n');
  // }
  // return priceStr;
  return str.split('+').join('\\r\\n');
}
