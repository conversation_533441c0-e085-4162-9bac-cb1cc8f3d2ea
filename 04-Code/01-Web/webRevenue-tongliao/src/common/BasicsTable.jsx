import React from 'react';
import {Pagination, Table} from '@icedesign/base';

const BasicsTable = (props) => {
    return (
        <>
            <Table
                dataSource={props.dataSource}
                isLoading={props.dataLoading}
                rowSelection={
                    props.rowSelection
                        ? {
                              ...props.rowSelection,
                              selectedRowKeys: props.selectedRowKeys,
                          }
                        : null
                }
            >
                {props.columns.map((item) => {
                    return (
                        <Table.Column
                            key={item.title}
                            title={item.title}
                            dataIndex={item.dataIndex}
                            cell={item.cell}
                            align="center"
                            width={item.width}
                        />
                    );
                })}
            </Table>
            <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
                <Pagination
                    style={{ marginTop: 14 }}
                    pageSizeSelector="dropdown"
                    onChange={(value) => props.changePage(value)}
                    onPageSizeChange={(value) => props.onPageSizeChange(value)}
                    total={props.total}
                    pageSize={props.pageSize}
                    current={props.page}
                    size="small"
                    pageSizeList={props.pageSizeList}
                />
                <div style={{ marginTop: 16, marginLeft: 12 }}>共 {props.total} 条记录</div>
            </div>
        </>
    );
};
export default BasicsTable;
