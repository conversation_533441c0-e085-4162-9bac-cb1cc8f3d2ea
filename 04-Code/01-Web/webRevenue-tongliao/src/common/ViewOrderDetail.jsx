import React, {Component} from 'react';
import {
  Button,
  Dialog,
  Field,
  Form,
  Grid,
  Icon,
  Input,
  Select
} from '@icedesign/base';
import {Link} from 'react-router-dom';

const { Row } = Grid;
const FormItem = Form.Item;

export default class ViewOrderDetail extends Component {
    constructor(props) {
        super(props);
        this.state = {
            visible: false,
        };
        this.field = new Field(this, { autoUnmount: true });
    }

    /* 开启弹窗 */
    onOpen(record) {
        this.field.setValues({ ...record });
        this.setState({ visible: true });
    }

    /* 关闭弹窗 */
    handleCancel = () => {
        this.setState({ visible: false });
    };

    render() {
        const { init } = this.field;
        const { record, type } = this.props;
        const { visible } = this.state;
        const formItemLayout = {
            labelCol: { fixedSpan: 6 },
        };
        const footer = (
            <Button type="primary" onClick={this.handleCancel}>
                关闭
            </Button>
        );
        return (
            <div>
                {type === 1 ? (
                    <Icon
                        title="查看详情"
                        type="browse"
                        size="small"
                        style={{ color: '#1DC11D', cursor: 'pointer' }}
                        onClick={() => this.onOpen(record)}
                    />
                ) : (
                    <a onClick={() => this.onOpen(record)}>
                        <span style={{ color: '#1C86EE', cursor: 'pointer' }}>查看详情</span>
                    </a>
                )}
                <Dialog
                    visible={visible}
                    isFullScreen
                    onClose={() => this.handleCancel()}
                    title="查看订单详情"
                    footer={footer}
                    footerAlign="center"
                    style={{ width: 700 }}
                >
                    {record.watermeterType === 'IC卡表' ? (
                        <Form field={this.field} direction="hoz">
                            <Row>
                                <FormItem {...formItemLayout} label="订单编号：">
                                    <Input {...init('id')} style={styles.inputStyle} readOnly />
                                </FormItem>
                                <FormItem {...formItemLayout} label="缴费时间：" {...formItemLayout}>
                                    <Input {...init('createTime')} style={styles.inputStyle} readOnly />
                                </FormItem>
                            </Row>
                            <Row>
                                <FormItem {...formItemLayout} label="购买水量：">
                                    <Input {...init('tunnage')} style={styles.inputStyle} readOnly />
                                </FormItem>
                                <FormItem {...formItemLayout} label="付款方式：" {...formItemLayout}>
                                    <Select
                                        {...init('payWay')}
                                        style={{ color: 'black', width: 160 }}
                                        hasArrow={false}
                                        dataSource={[
                                            { value: '1', label: '现金' },
                                            { value: '0', label: '刷卡' },
                                            { value: '2', label: '微信' },
                                            { value: '3', label: '现金-APP' },
                                            { value: '4', label: '农行' },
                                        ]}
                                        disabled
                                    />
                                </FormItem>
                            </Row>
                            <Row>
                                <FormItem {...formItemLayout} label="应收金额(元)：">
                                    <Input {...init('fee')} style={styles.inputStyle} readOnly />
                                </FormItem>

                                <FormItem {...formItemLayout} label="优惠金额(元)：" {...formItemLayout}>
                                    <Input {...init('preferentialPrice')} style={styles.inputStyle} readOnly />
                                </FormItem>
                            </Row>
                            <Row>
                                <FormItem {...formItemLayout} label="账户抵扣(元)：">
                                    <Input {...init('account')} style={styles.inputStyle} readOnly />
                                </FormItem>
                                <FormItem {...formItemLayout} label="收款金额(元)：" {...formItemLayout}>
                                    <Input {...init('pay')} style={styles.inputStyle} readOnly />
                                </FormItem>
                            </Row>
                            <Row>
                                <FormItem {...formItemLayout} label="剩余金额(元)：">
                                    <Input {...init('changeAmount')} style={styles.inputStyle} readOnly />
                                </FormItem>
                                <FormItem {...formItemLayout} label="结余方式：" {...formItemLayout}>
                                    <Select
                                        {...init('clearingWay')}
                                        style={{ color: 'black', width: 160 }}
                                        hasArrow={false}
                                        placeholder=" "
                                        dataSource={[
                                            { value: '2', label: '存入账户' },
                                            { value: '1', label: '找零' },
                                        ]}
                                        disabled
                                    />
                                </FormItem>
                            </Row>
                            <Row>
                                <FormItem {...formItemLayout} label="是否开纸票：">
                                    <Select
                                        {...init('isNote')}
                                        style={{ color: 'black', width: 160 }}
                                        hasArrow={false}
                                        placeholder=" "
                                        dataSource={[
                                            { value: 2, label: '未开纸票' },
                                            { value: 1, label: '已开纸票' },
                                            { value: 0, label: '已开发票' },
                                        ]}
                                        disabled
                                    />
                                </FormItem>
                                <FormItem {...formItemLayout} label="是否开税票：">
                                    <Select
                                        {...init('isTax')}
                                        style={{ color: 'black', width: 160 }}
                                        hasArrow={false}
                                        placeholder=" "
                                        dataSource={[
                                            { value: 3, label: '开票失败' },
                                            { value: 2, label: '开票中' },
                                            { value: 1, label: '已开税票' },
                                            { value: 0, label: '未开税票' },
                                        ]}
                                        disabled
                                    />
                                </FormItem>
                            </Row>
                          <Row>
                            {record.terminalId === null ? (
                                void 0
                            ) : (
                                <FormItem {...formItemLayout}
                                          label="写卡终端编号：">
                                  <Input {...init('terminalId')}
                                         style={styles.inputStyle} readOnly/>
                                </FormItem>
                            )}
                            </Row>
                          <Row>
                            <FormItem {...formItemLayout} label="开票失败原因：">
                              <Input {...init('reasonOfFailure')}
                                     style={{width: 500, height: 50}} readOnly/>
                            </FormItem>
                          </Row>
                            <Row>
                                <FormItem {...formItemLayout} label="备注：">
                                    <Input {...init('remarks')} style={{ width: 500, height: 50 }} readOnly />
                                </FormItem>
                            </Row>
                            <Row>
                                <FormItem {...formItemLayout} label="明细：">
                                    <Input {...init('descr')} style={{ width: 500, height: 50 }} readOnly />
                                </FormItem>
                            </Row>
                            {record.status === '2' ? (
                                <Row>
                                    <FormItem {...formItemLayout} label="退水时间：">
                                        <Input {...init('closeCreateTime')} style={styles.inputStyle} readOnly />
                                    </FormItem>
                                    <FormItem {...formItemLayout} label="退水操作人：" {...formItemLayout}>
                                        <Input {...init('closeCreateName')} style={styles.inputStyle} readOnly />
                                    </FormItem>
                                </Row>
                            ) : (
                                void 0
                            )}
                        </Form>
                    ) : record.watermeterType === '远传表' ? (
                        <Form field={this.field} direction="hoz">
                            <Row>
                                <FormItem {...formItemLayout} label="订单编号：">
                                    <Input {...init('id')} style={styles.inputStyle} readOnly />
                                </FormItem>
                                <FormItem {...formItemLayout} label="缴费时间：">
                                    <Input {...init('createTime')} style={styles.inputStyle} readOnly />
                                </FormItem>
                            </Row>
                            <Row>
                                <FormItem {...formItemLayout} label="欠费金额(元)：">
                                    <Input {...init('totalArrearage')} style={styles.inputStyle} readOnly />
                                </FormItem>
                                <FormItem {...formItemLayout} label="滞纳金(元)：">
                                    <Input {...init('lateAmount')} style={styles.inputStyle} readOnly />
                                </FormItem>
                            </Row>
                            <Row>
                                <FormItem {...formItemLayout} label="应收金额(元)：">
                                    <Input {...init('fee')} style={styles.inputStyle} readOnly />
                                </FormItem>

                                <FormItem {...formItemLayout} label="优惠金额(元)：">
                                    <Input {...init('preferentialPrice')} style={styles.inputStyle} readOnly />
                                </FormItem>
                            </Row>
                            <Row>
                                <FormItem {...formItemLayout} label="收款金额(元)：">
                                    <Input {...init('pay')} style={styles.inputStyle} readOnly />
                                </FormItem>
                                <FormItem {...formItemLayout} label="剩余金额(元)：">
                                    <Input {...init('changeAmount')} style={styles.inputStyle} readOnly />
                                </FormItem>
                            </Row>
                            {record.status == '2' ? (
                                <Row>
                                    <FormItem {...formItemLayout} label="退款时间：">
                                        <Input {...init('closeCreateTime')} style={styles.inputStyle} readOnly />
                                    </FormItem>
                                    <FormItem {...formItemLayout} label="退款操作人：" {...formItemLayout}>
                                        <Input {...init('closeCreateName')} style={styles.inputStyle} readOnly />
                                    </FormItem>
                                </Row>
                            ) : (
                                void 0
                            )}
                            <Row>
                                <FormItem {...formItemLayout} label="结余方式：">
                                    <Select
                                        {...init('clearingWay')}
                                        style={styles.inputStyle}
                                        hasArrow={false}
                                        dataSource={[
                                            { value: '2', label: '存入账户' },
                                            { value: '1', label: '找零' },
                                        ]}
                                        disabled
                                    />
                                </FormItem>
                            </Row>
                            <Row>
                                <FormItem {...formItemLayout} label="是否开纸票：">
                                    <Select
                                        {...init('isNote')}
                                        style={{ color: 'black', width: 160 }}
                                        hasArrow={false}
                                        placeholder=" "
                                        dataSource={[
                                            { value: 2, label: '未开纸票' },
                                            { value: 1, label: '已开纸票' },
                                            { value: 0, label: '已开发票' },
                                        ]}
                                        disabled
                                    />
                                </FormItem>
                                <FormItem {...formItemLayout} label="是否开税票：">
                                    <Select
                                        {...init('isTax')}
                                        style={{ color: 'black', width: 160 }}
                                        hasArrow={false}
                                        placeholder=" "
                                        dataSource={[
                                            { value: 3, label: '开票失败' },
                                            { value: 2, label: '开票中' },
                                            { value: 1, label: '已开税票' },
                                            { value: 0, label: '未开税票' },
                                        ]}
                                        disabled
                                    />
                                </FormItem>
                            </Row>
                            <Row>
                                <FormItem {...formItemLayout} label="开票失败原因：">
                                    <Input {...init('reasonOfFailure')} style={{ width: 500, height: 50 }} readOnly />
                                </FormItem>
                            </Row>
                            <Row>
                                <FormItem {...formItemLayout} label="备注：">
                                    <Input {...init('descr')} style={{ width: 500, height: 50 }} readOnly />
                                </FormItem>
                            </Row>
                        </Form>
                    ) : (
                        <Form field={this.field} direction="hoz">
                            <Row>
                                <FormItem {...formItemLayout} label="订单编号：">
                                    <Input {...init('id')} style={styles.inputStyle} readOnly />
                                </FormItem>
                                <FormItem {...formItemLayout} label="缴费时间：">
                                    <Input {...init('createTime')} style={styles.inputStyle} readOnly />
                                </FormItem>
                            </Row>
                            <Row>
                                <FormItem {...formItemLayout} label="欠费金额(元)：">
                                    <Input {...init('totalArrearage')} style={styles.inputStyle} readOnly />
                                </FormItem>
                                <FormItem {...formItemLayout} label="滞纳金(元)：">
                                    <Input {...init('lateAmount')} style={styles.inputStyle} readOnly />
                                </FormItem>
                            </Row>
                            <Row>
                                <FormItem {...formItemLayout} label="应收金额(元)：">
                                    <Input {...init('fee')} style={styles.inputStyle} readOnly />
                                </FormItem>

                                <FormItem {...formItemLayout} label="优惠金额(元)：">
                                    <Input {...init('preferentialPrice')} style={styles.inputStyle} readOnly />
                                </FormItem>
                            </Row>
                            <Row>
                                <FormItem {...formItemLayout} label="收款金额(元)：">
                                    <Input {...init('pay')} style={styles.inputStyle} readOnly />
                                </FormItem>
                                <FormItem {...formItemLayout} label="剩余金额(元)：">
                                    <Input {...init('changeAmount')} style={styles.inputStyle} readOnly />
                                </FormItem>
                            </Row>
                            {record.status == '2' ? (
                                <Row>
                                    <FormItem {...formItemLayout} label="退款时间：">
                                        <Input {...init('closeCreateTime')} style={styles.inputStyle} readOnly />
                                    </FormItem>
                                    <FormItem {...formItemLayout} label="退款操作人：" {...formItemLayout}>
                                        <Input {...init('closeCreateName')} style={styles.inputStyle} readOnly />
                                    </FormItem>
                                </Row>
                            ) : (
                                void 0
                            )}
                            <Row>
                                <FormItem {...formItemLayout} label="结余方式：">
                                    <Select
                                        {...init('clearingWay')}
                                        style={styles.inputStyle}
                                        hasArrow={false}
                                        dataSource={[
                                            { value: '2', label: '存入账户' },
                                            { value: '1', label: '找零' },
                                        ]}
                                        disabled
                                    />
                                </FormItem>
                            </Row>
                            <Row>
                                <FormItem {...formItemLayout} label="是否开纸票：">
                                    <Select
                                        {...init('isNote')}
                                        style={{ color: 'black', width: 160 }}
                                        hasArrow={false}
                                        placeholder=" "
                                        dataSource={[
                                            { value: 2, label: '未开纸票' },
                                            { value: 1, label: '已开纸票' },
                                            { value: 0, label: '已开发票' },
                                        ]}
                                        disabled
                                    />
                                </FormItem>
                                <FormItem {...formItemLayout} label="是否开税票：">
                                    <Select
                                        {...init('isTax')}
                                        style={{ color: 'black', width: 160 }}
                                        hasArrow={false}
                                        placeholder=" "
                                        dataSource={[
                                            { value: 3, label: '开票失败' },
                                            { value: 2, label: '开票中' },
                                            { value: 1, label: '已开税票' },
                                            { value: 0, label: '未开税票' },
                                        ]}
                                        disabled
                                    />
                                </FormItem>
                            </Row>
                            <FormItem {...formItemLayout} label="开票失败原因：">
                                <Input {...init('reasonOfFailure')} style={{ width: 500, height: 50 }} readOnly />
                            </FormItem>
                            <Row>
                                <FormItem {...formItemLayout} label="备注：">
                                    <Input {...init('descr')} style={{ width: 500, height: 50 }} readOnly />
                                </FormItem>
                            </Row>
                            <Row>
                                <FormItem {...formItemLayout} label="对应账单：">
                                    <Link
                                        to={{
                                            pathname: `/machineryMeter/machineryMeterBill`,
                                            state: { orderId: record.id },
                                        }}
                                    >
                                        <span
                                            style={{ display: 'block', textAlign: 'right' }}
                                            className="next-form-text-align"
                                        >
                                            查看对应账单
                                            <Icon size="xs" type="arrow-right" />
                                        </span>
                                    </Link>
                                </FormItem>
                            </Row>
                        </Form>
                    )}
                </Dialog>
            </div>
        );
    }
}

const styles = {
    inputStyle: {
        width: 160,
    },
    selectStyle: {
        width: 160,
        color: 'black',
    },
    buyWater: {
        width: 150,
    },
};
