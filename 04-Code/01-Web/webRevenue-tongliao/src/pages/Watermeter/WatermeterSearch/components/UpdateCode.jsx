import React, {Component, Fragment} from 'react'
import {
  Balloon,
  Button,
  Dialog,
  Feedback,
  Field,
  Form,
  Grid,
  Icon,
  Input
} from '@icedesign/base';
import {formItemLayout} from '../../../../common/FormCollocation';
import axios from "axios/index";
import qs from 'qs';
import {url} from "../../../../components/URL";

const {Row} = Grid
const FormItem = Form.Item
const {Tooltip} = Balloon;

export default class UpdateCode extends Component {

  constructor(props) {
    super(props);
    this.state = {
      dataLoading: false,
      visible: false,
      createId: sessionStorage.getItem("stuffId"),
      createName: sessionStorage.getItem("realName"),
    };
    this.field = new Field(this, {autoUnmount: true});
  }

  onOpen() {
    const {record} = this.props
    this.field.setValue('oldQrCode', record.qrCode)
    this.setState({visible: true})
  }

  onClose() {
    this.setState({visible: false})
  }

  handleOk() {
    const {record} = this.props
    const {createId, createName} = this.state
    let list = this.field.getValues()
    let watermeterId = record.watermeterId
    list.watermeterId = watermeterId
    list.status = record.status
    list.createName = createName
    list.createId = createId
    this.field.validate((errors, values) => {
      if (errors) {
        return;
      } else {
        axios.post(`${url}revenue/watermeter/modifyQrCode`, qs.stringify(list))
          .then((response) => {
            if (response.data.code == "0") {
              Feedback.toast.success("修改成功");
              this.props.queryAll();
              this.setState({visible: false});
            } else {
              Feedback.toast.error("修改失败:" + response.data.msg);
            }
          }).catch((error) => {
          Feedback.toast.error("网络错误,请稍后再试")
          this.setState({dataLoading: false})
        })
      }
    })
  }

  render() {
    const {init} = this.field
    const {visible, dataLoading} = this.state
    const footer = (
      <div>
        <Button type="primary" loading={dataLoading} onClick={() => this.handleOk()}>
          确定
        </Button>
        <Button onClick={() => this.onClose()} style={{marginLeft: 20}}>
          取消
        </Button>
      </div>
    );
    const edit = (
      <Icon type="form" style={{color: "#3399ff", cursor: "pointer"}} size="small" onClick={() => this.onOpen()}/>
    )
    return (

      <Fragment>
        <Tooltip trigger={edit} align='t' text='修改二维码编号'/>

        <Dialog  style={{width:550}} visible={visible} onClose={() => this.onClose()}
          footer={footer} title='修改二维码信息' footerAlign="center">

          <Form field={this.field}>
            <Row>
              <FormItem {...formItemLayout} label="原二维码：">
                <Input  {...init('oldQrCode')} style={{width: 200, backgroundColor: '#E6E6E6'}} readOnly/>
              </FormItem>
            </Row>

            <Row>
              <FormItem {...formItemLayout} label="新二维码：">
                <Input {...init('newQrCode', {rules: [{required: true, message: '新二维码编号必填'}]})} style={{width:200}}/>
              </FormItem>
            </Row>

            <Row>

            </Row>
            <Row>
              <FormItem label="更换原因：" {...formItemLayout} >
                <Input  {...init('remark')} style={{width: 300}}/>
              </FormItem>
            </Row>
            <Row>
              <FormItem label="备注：" {...formItemLayout} >
                <Input multiple {...init('comment')} style={{width: 300}}/>
              </FormItem>
            </Row>
          </Form>
        </Dialog>
      </Fragment>
    )
  }
}
