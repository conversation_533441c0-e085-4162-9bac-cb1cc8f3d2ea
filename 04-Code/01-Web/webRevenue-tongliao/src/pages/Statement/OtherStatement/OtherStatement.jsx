import React, {Component, Fragment} from 'react'
import axios from "axios/index";
import {url} from "../../../components/URL";
import DetailedSale from './components/DetailedSale'
import TicketRate from './components/TicketRate'
import IncreaseAndArrears from './components/IncreaseAndArrears'
import Prestorage from './components/Prestorage'
import Statistics from './components/Statistics'
import MajorAccount from './components/MajorAccount'
import RandomUser from './components/RandomUser'
import ExcelTicketUser from './components/ExcelTicketUser'
import ExcelOneTwo from './components/ExcelOneTwo'
import ExcelBigUser from './components/ExcelBigUser'
import qs from 'qs';
import UserYearTunnage
  from "@/pages/Statement/OtherStatement/components/userYearTunnage";
import UsersWhoHaveNotUsedWaterForManyConsecutiveMonths
  from "@/pages/Statement/OtherStatement/components/UsersWhoHaveNotUsedWaterForManyConsecutiveMonths";

export default class OtherStatement extends Component {

  constructor(props) {
    super(props);
    this.state = {
      areaList: [],  //片区
      regionList: [], //小区
      feeNameList: [], //用水性质
      ZoneCodeList: [], //区号
      unitPrice: [],  //单价
      roleNameList:[], //抄表员
    }
  }

  componentDidMount() {
    this.queryArea()
    this.queryRegion()
    this.queryFeeName()
    this.queryZoneCodeList()
    this.queryUnitPrice()
    this.queryRoleName()
  }

  //查询片区
  queryArea() {
    axios.get(`${url}revenue/area/getAll`)
      .then((response) => {
        let areaList = []
        response.data.datas.map((item) => {
          areaList.push({label: item.name, value: item.id})
        })
        this.setState({areaList: areaList})
      })
  }

  //查询小区
  queryRegion() {
    axios.post(`${url}revenue/region/regionlist`)
      .then(response => {
        let regionList = []
        response.data.datas.map((item) => {
          regionList.push({label: item.regionName, value: item.id})
        })
        this.setState({regionList: regionList})
      })
  }

  //查询用水性质
  queryFeeName() {
    axios.post(`${url}revenue/fee/queryList`)
      .then((response) => {
        let feeNameList = []
        response.data.datas.map((item) => {
          feeNameList.push({label: item.name, value: item.id})
        })
        this.setState({feeNameList: feeNameList})
      })
  }

  //查询区号
  queryZoneCodeList() {
    axios.post(`${url}revenue/exportStatementApi/zoneCodeList`)
      .then((response) => {
        let zoneCodeList = []
        response.data.datas.map((item) => {
          zoneCodeList.push({label: item, value: item})
        })
        this.setState({zoneCodeList: zoneCodeList})
      })
  }

  //查询水价
  queryUnitPrice() {
    axios.post(`${url}revenue/exportStatementApi/unitPrice`)
      .then((response) => {
        let unitPrice = []
        response.data.datas.map((item) => {
          unitPrice.push({label: item, value: item})
        })
        this.setState({unitPrice: unitPrice})
      })
  }

  //查询抄表员
  queryRoleName() {
    axios.post(`${url}revenue/staff/findByRoleName`, qs.stringify({roleName: '抄表员'}))
      .then((response) => {
        let roleNameList = []
        response.data.datas.map((item) => {
          roleNameList.push({label: item.realName, value: item.userId})
        })
        this.setState({roleNameList: roleNameList})
      })
  }

  render() {
    const {areaList, regionList, feeNameList, zoneCodeList, unitPrice,roleNameList} = this.state;
    return (
      <Fragment>

        <DetailedSale areaList={areaList}/>

        <TicketRate zoneCodeList={zoneCodeList}/>

          <IncreaseAndArrears areaList={areaList} regionList={regionList} unitPrice={unitPrice}
                              zoneCodeList={zoneCodeList}/>

          <Prestorage areaList={areaList} regionList={regionList} unitPrice={unitPrice} zoneCodeList={zoneCodeList}/>

          <Statistics areaList={areaList} zoneCodeList={zoneCodeList}/>

          <MajorAccount feeNameList={feeNameList} areaList={areaList} zoneCodeList={zoneCodeList}
                        unitPrice={unitPrice}/>

          <RandomUser feeNameList={feeNameList} unitPrice={unitPrice} zoneCodeList={zoneCodeList}/>

          <ExcelTicketUser areaList={areaList} zoneCodeList={zoneCodeList} regionList={regionList}
                           roleNameList={roleNameList}/>

          <ExcelOneTwo areaList={areaList} zoneCodeList={zoneCodeList} roleNameList={roleNameList}/>

          <ExcelBigUser areaList={areaList} zoneCodeList={zoneCodeList} regionList={regionList} unitPrice={unitPrice}
                        feeNameList={feeNameList}/>

          <UserYearTunnage/>

          <UsersWhoHaveNotUsedWaterForManyConsecutiveMonths/>

      </Fragment>
    )
  }
}
