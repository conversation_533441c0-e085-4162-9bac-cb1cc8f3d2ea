import React, {Component} from 'react'
import IceContainer from '@icedesign/container';
import {
  <PERSON><PERSON>,
  DatePicker,
  Feedback,
  Field,
  Form,
  Grid,
  Input,
  moment,
  Select
} from "@icedesign/base";
import {url} from "../../../../components/URL";
import axios from "axios";
import qs from 'qs';
import {RangePicker} from "@icedesign/base/lib/date-picker";

const FormItem = Form.Item;
const { Combobox } = Select
const { Row, Col } = Grid;
const { MonthPicker } = DatePicker;
export default class ExcelBigUser extends Component {

  constructor(props) {
    super(props);
    this.state = {
      feeNameList: []
    }
    this.field = new Field(this, { autoUnmount: true })
  }
  // oneTimeOnchange(val, str) {
  //   this.field.setValue("currentTimeStart", str[0]);
  //   this.field.setValue("currentTimeEnd", str[1]);
  // }
  // twoTimeOnchange(val, str) {
  //   this.field.setValue("contrastTimeStart", str[0]);
  //   this.field.setValue("contrastTimeEnd", str[1]);
  // }
  //导出排污增收明细表
  excelBigUser() {
    let values = this.field.getValues();
    // if (Number(values.endTunnage) < Number(values.startTunnage)) {
    //   alert('差值水量截至水量必须大于开始水量')
    // } else {
      this.field.validate((errors, values) => {
        if (!errors) {
          if (values.currentTime && values.currentTime.length == 2) {
            values.currentTimeStart = values.currentTime[0]
            values.currentTimeEnd = values.currentTime[1]
          }
          if (values.compareTime && values.compareTime.length == 2) {
            values.contrastTimeStart = values.compareTime[0]
            values.contrastTimeEnd = values.compareTime[1]
          }
          let url1 = `${url}revenue/exportStatementApi/excelBigUser?n=1`;
          if (values.areaId) {
            url1 += '&areaId=' + values.areaId;
          }
          if (values.zoneCode) {
            url1 += '&zoneCode=' + values.zoneCode;
          }
          if (values.regionId) {
            url1 += '&regionId=' + values.regionId;
          }
          if (values.feeIdList) {
            url1 += '&feeIdList=' + values.feeIdList;
          }
          if (values.currentTimeStart) {
            url1 += '&currentTimeStart=' + moment(values.currentTimeStart).format('YYYY-MM-DD');
          }
          if (values.currentTimeEnd) {
            url1 += '&currentTimeEnd=' + moment(values.currentTimeEnd).format('YYYY-MM-DD');
          }
          if (values.contrastTimeStart) {
            url1 += '&contrastTimeStart=' + moment(values.contrastTimeStart).format('YYYY-MM-DD');
          }
          if (values.contrastTimeEnd) {
            url1 += '&contrastTimeEnd=' + moment(values.contrastTimeEnd).format('YYYY-MM-DD');
          }
          if (values.upTunnage) {
            url1 += '&upTunnage=' + values.upTunnage;
          }
          if (values.downTunnage) {
            url1 += '&downTunnage=' + values.downTunnage;
          }
          if (values.unitPrice) {
            url1 += '&unitPrice=' + values.unitPrice;
          }
          window.open(encodeURI(url1), 'about:blank');
        }
      })
   // }
  }

  //区域onchange
  onChange(value, option) {
    this.field.setValue('unitPrice', value);
    let feeNameList = []
    axios.post(`${url}revenue/exportStatementApi/unitPriceAndFeeName`, qs.stringify({ unitPrice: value })).then(response => {
      response.data.datas.map((item) => {
        feeNameList.push({ label: item.name, value: item.id })
      })
      this.setState({ feeNameList: feeNameList })
    }).catch(error => {
      Feedback.toast.error("请求异常", error);
    })

  }

  render() {
    const { areaList, zoneCodeList, regionList, feeNameList, unitPrice } = this.props
    const { init } = this.field
    const span = { xss: 24, xs: 24, s: 8, m: 12, l: 8, xl: 8 }
    const formItemLayout = { labelCol: { fixedSpan: 5 } }
    return (
      <IceContainer title="用水大户对比明细表报表">
        <Form field={this.field}>
          <Row wrap>
            <Col {...span}>
              <FormItem label="片区：" {...formItemLayout}>
                <Select {...init('areaId')} placeholder="--请选择--"
                  dataSource={areaList} style={{ width: 150 }} />
              </FormItem>
            </Col>

            {/*<Col {...span}>*/}
            {/*  <FormItem label="区号：" {...formItemLayout}>*/}
            {/*    <Select {...init('zoneCode')} placeholder="--请选择--" dataSource={zoneCodeList}*/}
            {/*            fillProps="label" multiple style={{width: 150}}/>*/}
            {/*  </FormItem>*/}
            {/*</Col>*/}

            <Col {...span}>
              <FormItem label="区号：" {...formItemLayout}>
                <Combobox {...init('zoneCode')} placeholder="--请选择或输入区号--"
                  fillProps="label" hasClear
                  style={{ width: 200 }} dataSource={zoneCodeList} />
              </FormItem>
            </Col>

            <Col {...span}>
              <FormItem label="小区：" {...formItemLayout}>
                <Combobox {...init('regionId')} placeholder="--请选择小区--"
                  fillProps="label" hasClear
                  style={{ width: 170 }} dataSource={regionList} />
              </FormItem>
            </Col>

            <Col {...span}>
              <FormItem label="当前时间：" {...formItemLayout}>
                <RangePicker  {...init('currentTime', { rules: [{ required: true, message: "请选择当前时间" }] })} />
              </FormItem>
            </Col>

            <Col {...span}>
              <FormItem label="对比时间：" {...formItemLayout}>
                <RangePicker  {...init('compareTime', { rules: [{ required: true, message: "请选择对比时间" }] })} />
              </FormItem>
            </Col>

            <Col {...span}>
              <FormItem label="上升水量：" {...formItemLayout}>
                <Input {...init('upTunnage')} style={{ width: 85 }} />
              </FormItem>
            </Col>
            <Col {...span}>
              <FormItem label="下降水量：" {...formItemLayout}>
                <Input {...init('downTunnage')} style={{ width: 85 }} />
              </FormItem>
            </Col>

            <Col {...span}>
              <FormItem label="清水单价：" {...formItemLayout}>
                <Select {...init('unitPrice', { rules: [{ required: true, message: "请选择清水单价" }] })} placeholder="请选择"
                  dataSource={unitPrice} style={{ width: 170 }}
                  onChange={(value, option) => this.onChange(value, option)} />
              </FormItem>
            </Col>

            <Col {...span}>
              <FormItem label="用水性质：" {...formItemLayout}>
                <Select
                  {...init('feeIdList', { rules: [{ required: true, message: "请选择用水性质" }] })}
                  placeholder="--请选择或输入--" multiple
                  style={{ width: 170 }} dataSource={this.state.feeNameList} placeholder="请先选择清水单价" />
              </FormItem>
            </Col>

          </Row>
        </Form>

        <Row>
          <Button type="primary" className="button" onClick={() => this.excelBigUser()}>
            导出用水大户对比明细表报表
          </Button>
        </Row>

      </IceContainer>
    )
  }
}
const styles = {
  infoColumnTitle: {
    margin: '5px 0',
    paddingLeft: '5px',
    borderLeft: '3px solid #3080fe',
    fontSize: '16px'
  }
}
