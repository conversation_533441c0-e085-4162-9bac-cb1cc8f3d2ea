import React, {Component} from 'react';
import IceContainer from '@icedesign/container';
import {
  Button,
  DatePicker,
  Field,
  Form,
  Grid,
  Input,
  moment,
  Select
} from '@icedesign/base';
import {url} from '../../../../components/URL';

const FormItem = Form.Item;
const { Combobox } = Select;
const { Row, Col } = Grid;
const { RangePicker } = DatePicker;
export default class RandomUser extends Component {
    constructor(props) {
        super(props);
        this.state = {};
        this.field = new Field(this, { autoUnmount: true });
    }
    // 时间onchange
    timeOnchange = (val, str) => {
        this.field.setValue('openTime', val);
        this.field.setValue('startTime', str[0]);
        this.field.setValue('endTime', str[1]);
    };
    // 导出随机用户报表
    randomUsers() {
        let values = this.field.getValues();
        let url1 = `${url}revenue/exportOther/randomUsers?n=1`;
        if (values.time) {
            url1 += '&time=' + moment(values.time).format('YYYY-MM');
        }
        if (values.unitPrice) {
            url1 += '&unitPrice=' + values.unitPrice;
        }
        if (values.feeIds) {
            url1 += '&feeIds=' + values.feeIds;
        }
        if (values.tunnageS) {
            url1 += '&tunnageS=' + values.tunnageS;
        }
        if (values.tunnageE) {
            url1 += '&tunnageE=' + values.tunnageE;
        }
        if (values.zoneCode) {
            url1 += '&zoneCode=' + values.zoneCode;
        }
        if (values.startTime) {
            url1 += '&startTime=' + values.startTime;
        }
        if (values.endTime) {
            url1 += '&endTime=' + values.endTime;
        }
        window.open(encodeURI(url1), 'about:blank');
    }

    render() {
        const { unitPrice, feeNameList, zoneCodeList } = this.props;
        const { init } = this.field;
        const span = { xss: 24, xs: 24, s: 8, m: 12, l: 8, xl: 8 };
        const formItemLayout = { labelCol: { fixedSpan: 5 } };
        return (
            <IceContainer>
                <h5 style={styles.infoColumnTitle}>随机用户报表</h5>
                <Form field={this.field}>
                    <Row wrap>
                        <Col {...span}>
                            <Form.Item label="统计时间：" {...formItemLayout}>
                                <RangePicker
                                    {...init('openTime')}
                                    onChange={(val, str) => this.timeOnchange(val, str)}
                                    style={{ width: '220px' }}
                                />
                            </Form.Item>
                        </Col>

                        <Col {...span}>
                            <FormItem label="清水费单价：" {...formItemLayout}>
                                <Combobox
                                    {...init('unitPrice')}
                                    placeholder="--请选择或输入--"
                                    dataSource={unitPrice}
                                    fillProps="label"
                                />
                            </FormItem>
                        </Col>

                        <Col {...span}>
                            <FormItem label="用水性质：" {...formItemLayout}>
                                <Combobox
                                    {...init('feeIds')}
                                    placeholder="--请选择或输入--"
                                    fillProps="label"
                                    hasClear
                                    style={{ width: '170px' }}
                                    dataSource={feeNameList}
                                    multiple
                                />
                            </FormItem>
                        </Col>

                        <Col {...span}>
                            <FormItem label="用水量：" {...formItemLayout}>
                                <Input {...init('tunnageS')} htmlType="number" style={{ width: '85px' }} />
                                <span>—</span>
                                <Input {...init('tunnageE')} htmlType="number" style={{ width: '85px' }} />
                            </FormItem>
                        </Col>

                        {/* <Col {...span}> */}
                        {/*  <FormItem label="区号：" {...formItemLayout}> */}
                        {/*    <Select {...init('zoneCode')} placeholder="--请选择--" dataSource={zoneCodeList} */}
                        {/*            fillProps="label" style={{width: 150}}/> */}
                        {/*  </FormItem> */}
                        {/* </Col> */}

                        <Col {...span}>
                            <FormItem label="区号：" {...formItemLayout}>
                                <Combobox
                                    {...init('zoneCode')}
                                    placeholder="--请选择或输入区号--"
                                    fillProps="label"
                                    hasClear
                                    style={{ width: 200 }}
                                    dataSource={zoneCodeList}
                                />
                            </FormItem>
                        </Col>
                    </Row>
                </Form>

                <Row>
                    <Button type="primary" className="button" onClick={() => this.randomUsers()}>
                        导出随机用户报表
                    </Button>
                </Row>
            </IceContainer>
        );
    }
}
const styles = {
    infoColumnTitle: {
        margin: '5px 0',
        paddingLeft: '5px',
        borderLeft: '3px solid #3080fe',
        fontSize: '16px',
    },
};
