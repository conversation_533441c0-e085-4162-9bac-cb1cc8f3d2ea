import React, {Component} from 'react'
import IceContainer from '@icedesign/container';
import {<PERSON><PERSON>, DatePicker, Field, Form, Grid, Select} from "@icedesign/base";
import {url} from "../../../../components/URL";

const FormItem = Form.Item;
const {Combobox} = Select
const {Row, Col} = Grid;
const {RangePicker} = DatePicker;
export default class Prestorage extends Component {

  constructor(props) {
    super(props);
    this.field = new Field(this, {autoUnmount: true})
  }

  //时间onchang
  timeOnchange(val, str) {
    this.field.setValue("startTime", str[0]);
    this.field.setValue("endTime", str[1]);
  }

  excelWaterPrepaid() {
    let values = this.field.getValues();
    let url1 = `${url}revenue/exportStatementApi/excelWaterPrepaid?n=1`;
    if (values.areaId) {
      url1 += '&areaId=' + values.areaId;
    }
    if (values.regionId) {
      url1 += '&regionId=' + values.regionId;
    }
    if (values.startTime) {
      url1 += '&startTime=' + values.startTime;
    }
    if (values.endTime) {
      url1 += '&endTime=' + values.endTime;
    }
    if (values.unitPrice) {
      url1 += '&unitPrice=' + values.unitPrice;
    }
    if (values.zoneCode) {
      url1 += '&zoneCode=' + values.zoneCode;
    }
    window.open(encodeURI(url1), 'about:blank');
  }

  render() {
    const {areaList, regionList, unitPrice,zoneCodeList} = this.props
    const {init} = this.field
    const span = {xss: 24, xs: 24, s: 8, m: 12, l: 8, xl: 8}
    const formItemLayout = {labelCol: {fixedSpan: 5}}
    return (
      <IceContainer>
        <h5 style={styles.infoColumnTitle}>水费预存明细表</h5>
        <Form field={this.field}>

          <Row wrap>
            <Col {...span}>
              <FormItem label="片区：" {...formItemLayout}>
                <Select {...init('areaId')} placeholder="--请选择--" dataSource={areaList} style={{width: 220}}/>
              </FormItem>
            </Col>

            <Col {...span}>
              <FormItem label="小区：" {...formItemLayout}>
                <Combobox {...init('regionId')} placeholder="--请选择小区--"
                          fillProps="label"
                          hasClear
                          style={{width: 220}} dataSource={regionList}/>
              </FormItem>
            </Col>

            <Col {...span}>
              <FormItem label="报表周期：" {...formItemLayout}>
                <RangePicker onChange={(val, str) => this.timeOnchange(val, str)}/>
              </FormItem>
            </Col>

            <Col {...span}>
              <FormItem label="清水单价：" {...formItemLayout}>
                <Select {...init('unitPrice')} placeholder="请选择"
                        dataSource={unitPrice} style={{width: 220}}
                />
              </FormItem>
            </Col>

            {/*<Col {...span}>*/}
            {/*  <FormItem label="区号：" {...formItemLayout}>*/}
            {/*    <Combobox {...init('zoneCode')} placeholder="--请选择区号--"*/}
            {/*              fillProps="label"*/}
            {/*              hasClear*/}
            {/*              style={{width: 220}} dataSource={zoneCodeList}/>*/}
            {/*  </FormItem>*/}
            {/*</Col>*/}

            <Col {...span}>
              <FormItem label="区号：" {...formItemLayout}>
                <Combobox {...init('zoneCode')} placeholder="--请选择或输入区号--"
                          fillProps="label" hasClear
                          style={{width: 200}} dataSource={zoneCodeList}/>
              </FormItem>
            </Col>
          </Row>
        </Form>

        <Row>
          <Button type="primary" className="button" onClick={() => this.excelWaterPrepaid()}>
            导出水费预存明细表
          </Button>
        </Row>

      </IceContainer>
    )
  }
}
const styles = {
  infoColumnTitle: {
    margin: '5px 0',
    paddingLeft: '5px',
    borderLeft: '3px solid #3080fe',
    fontSize: '16px'
  }
}
