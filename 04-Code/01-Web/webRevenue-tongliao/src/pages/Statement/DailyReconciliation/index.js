import React, {Component} from 'react';
import IceContainer from '@icedesign/container';
import {
  <PERSON><PERSON>,
  DatePicker,
  Dialog,
  Feedback,
  Field,
  Form,
  Grid,
  Icon,
  Select
} from '@icedesign/base';
import {formItemLayout, span} from "../../../common/FormCollocation";
import BasicsTable from "../../../common/BasicsTable";
import axios from 'axios';
import qs from 'qs';
import {url} from '../../../components/URL/index';

const { Row, Col } = Grid;
const FormItem = Form.Item;
const { RangePicker } = DatePicker;
const { Combobox } = Select

class Index extends Component {
  constructor() {
    super();
    this.state = {
      dataSource: [],
      page: 1,
      pageSize: 10,
      visible: false,
      typeOfEidt: '',
      selectData: {}
    }
    this.field = new Field(this, { autoUnmount: true });
  }

  changeTime = (value, str) => {
    if (value) {
      this.field.setValue('csTime', str[0]);
      this.field.setValue('ceTime', str[1]);
    }
  }

  //查询
  refreshTable(page, pageSize) {
    this.setState({ dataLoading: true });
    let values = this.field.getValues();
    values.page = page
    values.pageSize = pageSize
    delete values.queryTime
    axios.post(`${url}revenue/dailyReconciliation/query`, qs.stringify(values))
      .then((response) => {
        this.setState({
          dataSource: response.data.datas,
          page: page,
          pageSize: pageSize,
          total: response.data.totalSize,
          dataLoading: false
        })
      }).catch((error) => {
        Feedback.toast.error("网络错误请刷新后重试");
        this.setState({ dataLoading: false })
      })
  }

  queryLog = () => {
    this.refreshTable(1, 10)
  }

  edit = (record) => {
    this.setState({
      visible: true,
      selectData: record
    })
  }

  onChangeOfDialog = (e) => {
    this.setState({
      typeOfEidt: e
    })
  }

  onClose = () => {
    this.setState({
      visible: false
    })
  }

  onOk = () => {
    const { typeOfEidt, selectData } = this.state;
    let postData = {}
    postData.type = typeOfEidt;
    postData.orderId = selectData.orderId;
    postData.createId = sessionStorage.getItem("stuffId");
    postData.createName = sessionStorage.getItem("realName");
    axios.post(`${url}revenue/dailyReconciliation/disposeExceptionOrder`, qs.stringify(postData))
      .then((response) => {
        if (response.data.code == "0") {
          this.setState({
            visible: false
          }, () => {
            this.refreshTable(1, 10);
            Feedback.toast.success('修改成功');
          })
        } else {
          Feedback.toast.error(response.data.msg);
        }
      }).catch((error) => {
        Feedback.toast.error("网络错误请刷新后重试");
      })
  }

  render() {
    const { dataSource, dataLoading, total, page, pageSize } = this.state;
    const { init } = this.field;
    const columns = [
      {
        title: '订单编号',
        dataIndex: 'orderId',
        key: 'orderId'
      },
      {
        title: '支付方式',
        dataIndex: 'payWay',
        key: 'payWay',
        cell: (value, index, record) => {
          if (record.payWay == '0') {
            return <span>微信</span>
          } else if (record.payWay == '1') {
            return <span>支付宝</span>
          }
        }
      },
      {
        title: '处理状态',
        dataIndex: 'type',
        key: 'type',
        cell: (value, index, record) => {
          if (record.type == '0') {
            return <span>未处理</span>
          } else if (record.type == '1') {
            return <span>已处理</span>
          } else if (record.type == '2') {
            return <span>处理失败</span>
          }
        }
      },
      {
        title: '订单错误详情',
        dataIndex: 'msg',
        key: 'msg'
      },
      {
        title: '时间',
        dataIndex: 'createTime',
        key: 'createTime'
      },
      {
        title: '错误状态',
        dataIndex: 'status',
        key: 'status',
        cell: (value, index, record) => {
          if (record.status == '0') {
            return <span>对账失败</span>
          } else if (record.status == '1') {
            return <span>商户存在系统不存在</span>
          } else if (record.status == '2') {
            return <span>系统存在商户不存在</span>
          } else if (record.status == '3') {
            return <span>商户和系统状态不一致</span>
          }
        }
      },
      {
        title: '处理人',
        dataIndex: 'createName',
        key: 'createName'
      },
      {
        title: '操作',
        width: 120,
        cell: (value, index, record) => {
          return (
            <div style={{ display: 'flex', justifyContent: 'space-around' }}>
              <Button type="primary" onClick={() => this.edit(record)}>编辑</Button>
            </div>
          )
        },
        key: 'caozuo'
      },
    ]

    return (
      <div>
        <IceContainer title="每日对账异常订单查询">
          <Form direction="hoz" field={this.field}>
            <Row justify="space-between">
              <FormItem label="错误状态">
                <Select {...init('status')} placeholder="请选择" style={{ width: '170px' }}
                  dataSource={[
                    { label: '请选择', value: '' },
                    { label: '对账失败  ', value: '0' },
                    { label: '商户存在系统不存在.', value: '1' },
                    { label: '系统存在商户不存在.', value: '2' },
                    { label: '商户和系统状态不一致.', value: '3' },
                  ]} />
              </FormItem>
              <FormItem label="处理状态">
                <Select {...init('type')} placeholder="请选择" style={{ width: '170px' }}
                  dataSource={[
                    { label: '请选择', value: '' },
                    { label: '未处理  ', value: '0' },
                    { label: '已处理.', value: '1' },
                    { label: '处理失败.', value: '2' },
                  ]} />
              </FormItem>
              <FormItem label="订单异常时间：" {...formItemLayout}>
                <RangePicker
                  {...init('queryTime', {
                    props: { onChange: (value, str) => this.changeTime(value, str) }
                  })}
                />
              </FormItem>
            </Row>
            <Row>
              <FormItem label="支付方式">
                <Select {...init('payWay')} placeholder="请选择" style={{ width: '170px' }}
                  dataSource={[
                    { label: '请选择', value: '' },
                    { label: '微信  ', value: '0' },
                    { label: '支付宝.', value: '1' }
                  ]} />
              </FormItem>
            </Row>

          </Form>
          <div align="center">
            <Button type="primary" className="button"
              onClick={() => this.queryLog()}>
              <Icon type="search" />查询
            </Button>
            <Button type="secondary" className="button"
              onClick={() => this.reset()} style={{ marginLeft: 20 }}>
              <Icon type="refresh" />重置
            </Button>
          </div>
        </IceContainer>

        <IceContainer title="每日对账异常订单列表">
          <BasicsTable columns={columns} dataSource={dataSource} total={total} pageSize={pageSize} page={page}
            dataLoading={dataLoading}
            changePage={(value) => this.changePage(value)}
            onPageSizeChange={(value) => this.onPageSizeChange(value)} pageSizeList={[10, 50, 100]} />
        </IceContainer>

        <Dialog
          visible={this.state.visible}
          onClose={this.onClose}
          onCancel={this.onClose}
          onOk={this.onOk}
          style={{ width: '30%' }}
          title="修改"
        >
          <div>
            <Row style={{ margin: '10px 20px' }}>
              <Form>
                <FormItem label="处理状态">
                  <Select
                    placeholder="请选择"
                    onChange={this.onChangeOfDialog}
                    dataSource={[
                      { label: '未处理  ', value: '0' },
                      { label: '已处理.', value: '1' },
                      { label: '处理失败.', value: '2' },
                    ]} />
                </FormItem>
              </Form>
            </Row>
          </div>
        </Dialog>
      </div>
    )
  }
}

export default Index;
