import React, {Component} from 'react';
import {
  Button,
  DatePicker,
  Dialog,
  Field,
  Form,
  Grid,
  Icon,
  Input,
  Radio,
  Select
} from '@icedesign/base';

const { Row, Col } = Grid;
const { Group: RadioGroup } = Radio;
const { Combobox } = Select;

export default class BillDetailInfo extends Component {
  static displayName = 'BillDetailInfo';

  static defaultProps = {};

  constructor(props) {
    super(props);
    this.state = {
        visible: false,
        isVisible: "none",
    };
    this.field = new Field(this, { autoUnmount: true });
  }

  onClose = () => {
      this.setState({
        visible: false,
      });
  };

  componentDidMount(){
  }

  onOpen = (record) => {
    this.field.setValues({ ...record });
    this.setState({
      visible: true,
    });
  };

  render() {
    const init = this.field.init;
    //id为申请单id，auditId为审批id，opt为操作类型，取值有view和flow
    const {record, auditId, opt } = this.props;
    const formItemLayout = {
      labelCol : {
        fixedSpan: 6,
      },
      wrapperCol :{
        span: 12,
      }
    };

    const footer = (
        <Button type="primary" onClick={this.onClose}>
          关闭
        </Button>
    );

    return (
    <div style={styles.buttonStyle}>
        <a onClick={ () => this.onOpen(record)} title="查看" >
          <Icon type="browse" style={{ color: "#3399ff",cursor:"pointer"}} size="small"/>
        </a>
        <Dialog
          minMargin={10}
          style={{ width: 700 }}
          visible={this.state.visible}
          onClose={this.onClose}
          footer={footer}
          title="账单详情"
          footerAlign="center"
        >
          <Form direction="hoz" field={this.field}>
          <Row>
            <Form.Item label="用户编号：" {...formItemLayout}>
              <Input {...init("cno")} readOnly/>
            </Form.Item>
            <Form.Item label="用户名称：" {...formItemLayout}>
              <Input {...init("cname")} readOnly/>
            </Form.Item>
          </Row>
          <Row>
            <Form.Item label="上次示数：" {...formItemLayout}>
              <Input {...init("lastNum")} readOnly/>
            </Form.Item>
            <Form.Item label="本次示数：" {...formItemLayout}>
              <Input {...init("thisNum")} readOnly/>
            </Form.Item>
          </Row>
          <Row>
            <Form.Item label="用水量（m³）：" {...formItemLayout}>
              <Input {...init('tunnage')} readOnly/>
            </Form.Item>
            <Form.Item label="出账日期：" {...formItemLayout}>
              <DatePicker {...init('createTime')} hasClear={false} style={{width: 140}} disabled/>
            </Form.Item>
          </Row>
          <Row>
            <Form.Item label="账单金额：" {...formItemLayout}>
              <Input {...init("amount")} readOnly/>
            </Form.Item>
            <Form.Item label="已结金额：" {...formItemLayout}>
              <Input {...init("settle")} readOnly/>
            </Form.Item>
          </Row>
          <Row>
            <Form.Item label="欠费金额：" {...formItemLayout} >
              <Input {...init("unpaid")} readOnly style={{color: '#ff0000'}}/>
            </Form.Item>
            <Form.Item label="账单状态：" {...formItemLayout}>
              <Select {...init("billStatus")} style={{width: 140, color: '#1DC11D'}} hasArrow={false} disabled
                      dataSource={[
                        {label: '已结清', value: '1'},
                        {label: '未结清', value: '0'}
                      ]}/>
            </Form.Item>
          </Row>
          <Row>
            <Form.Item label="账单明细：" {...formItemLayout}>
              <Input style={{width: 440, height: 70}}
                     {...init("detail")}
                     autocomplete="off"
              />
            </Form.Item>
          </Row>
        </Form>
        </Dialog>
      </div>
    );
  }
}
const styles = {
    buttonStyle:{
        display: 'inline-block',
        marginRight: '2px',
    }
};
