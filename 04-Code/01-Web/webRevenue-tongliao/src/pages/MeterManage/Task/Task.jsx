import React, {Component} from 'react';
import IceContainer from '@icedesign/container';
import {
  <PERSON><PERSON>,
  DatePicker,
  Feedback,
  Field,
  Form,
  moment,
  Pagination,
  Select,
  Tab,
  Table
} from '@icedesign/base';
import axios from "axios/index";
import qs from 'qs'
import {url} from "../../../components/URL";

const FormItem = Form.Item
const TabPane = Tab.TabPane
const {Option} = Select
const {MonthPicker} = DatePicker

export default class Task extends Component {

  constructor(props) {
    super(props);
    this.state = {
      formValue: [],
      disabled: false,
      current: 1,
      pageSize: 1,
      totalSize: 0,
      dataLoading: false,
      selectedRowKeys: [],
      selectedRecord: [],
      key: '1',
      formValue1: [],
      regions: [],
      roleName:[]
    }
    this.field = new Field(this, {autoUnmount: true});
    this.rowSelection = {
      onChange: (ids, records) => {
        this.setState({
          selectedRowKeys: ids,
          selectedRecord: records,
        })
      },
      getProps: record => {
        return {
          disabled: record.id === 100306660941
        };
      }
    }
  }

  componentDidMount() {
    const {key} = this.state
    this.queryTask(key)
    this.renderRegion()
    this.queryRoleName()
  }

  /*查询未派发的任务*/
  queryTask(key) {
    const {current} = this.state
    this.setState({dataLoading: true});
    if (key === '1') {
      //查询未派发的任务
      let values = this.field.getValues()
      values.page = current
      axios({
        method: 'post',
        url: `${url}revenue/copyTask/query`,
        data: qs.stringify(values),
      })
        .then((response) => {
          this.setState({
            formValue: response.data.datas,
            page: response.data.page,
            pageSize: response.data.pageSize,
            totalSize: response.data.totalSize,
            dataLoading: false
          })
        })
        .catch((error) => {
          Feedback.toast.error("请求错误：" + error);
          this.setState({dataLoading: false,})
        })
    } else {
      //查询已派发的任务
      let values = []
      values.page = current
      values.uid = this.field.getValue('uid')
      values.yearMonth = this.field.getValue('yearMonth') ? moment(this.field.getValue('yearMonth')).format('YYYY-MM') : void (0)
      axios({
        method: 'post',
        url: `${url}revenue/copyTask/bquery`,
        data: qs.stringify(values),
      })
        .then((response) => {
          this.setState({
            formValue1: response.data.datas,
            page: response.data.page,
            pageSize: response.data.pageSize,
            totalSize: response.data.totalSize,
            dataLoading: false
          })
        })
        .catch((error) => {
          Feedback.toast.error("请求错误：" + error);
          this.setState({dataLoading: false,})
        })
    }


  }

  /*任务切换分页*/
  changePage(pageIndex){
    this.setState({dataLoading: true})
    const {key} =this.state
    if (key==='1'){
      let values = []
      values.page = pageIndex
      axios({
        method: 'post',
        url: `${url}revenue/copyTask/query`,
        data: qs.stringify(values),
      })
        .then((response) => {
          this.setState({
            formValue: response.data.datas,
            page: response.data.page,
            pageSize: response.data.pageSize,
            totalSize: response.data.totalSize,
            dataLoading: false
          })
        })
        .catch((error) => {
          Feedback.toast.error("请求错误：" + error);
          this.setState({dataLoading: false,})
        })
    } else {
      let values = []
      values.page = pageIndex
      values.uid = this.field.getValue('uid')
      values.yearMonth = this.field.getValue('yearMonth') ? moment(this.field.getValue('yearMonth')).format('YYYY-MM') : void (0)
      axios({
        method: 'post',
        url: `${url}revenue/copyTask/bquery`,
        data: qs.stringify(values),
      })
        .then((response) => {
          this.setState({
            formValue1: response.data.datas,
            page: response.data.page,
            pageSize: response.data.pageSize,
            totalSize: response.data.totalSize,
            dataLoading: false
          })
        })
        .catch((error) => {
          Feedback.toast.error("请求错误：" + error);
          this.setState({dataLoading: false,})
        })
    }

  }

  /*查询角色*/
  queryRoleName() {
    axios({
      method: 'post',
      url: `${url}revenue/staff/findByRoleName`,
      data: qs.stringify({roleName: '抄表员'})
    })
      .then((response) => {
        this.setState({
          roleName: response.data.datas,
        })
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
      })
  }

  /*渲染角色*/
  renderRoleName() {
    const {roleName} = this.state
    return roleName && roleName.length > 0 ? roleName.map((item) => {
      return <Option value={item.userId}>{item.userName}</Option>
    }) : void(0)
  }

  /*渲染地区*/
  area() {
    const {regions} = this.state
    return regions && regions.length > 0 ? regions.map((item) => {
      return <Option value={item.id}>{item.areaName}</Option>
    }) : void (0)
  }

  /*任务派发*/
  dispatch() {
    const {selectedRecord} = this.state
    let value = selectedRecord
    value.map((item) => {
      item.uid = this.field.getValue('uid')
    })
    this.field.validate((errors, values) => {
      if (errors) {
        return
      } else {
        if (value.length > 0) {
          axios({
            method: 'post',
            url: `${url}revenue/copyTask/add`,
            data: {list: value}
          })
            .then((response) => {
              if (response.data.code == "0") {
                Feedback.toast.success('任务派发成功');
                this.queryTask(this.state.key)
                this.field.reset()
              } else {
                Feedback.toast.error('任务派发成功');
              }
            })
            .catch((error) => {
              Feedback.toast.error("请求错误：" + error);
              this.setState({dataLoading: false,})
            })
        } else {
          Feedback.toast.error('请选择至少一个区域派发');
        }
      }
    })
  }

  /*查询区域*/
  renderRegion() {
    axios({
      method: 'post',
      url: `${url}revenue/region/all`,
    })
      .then((response) => {
        this.setState({
          regions: response.data.datas
        })
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
      })
  }

  /*列表切换*/
  tabChange(key) {
    this.setState({key: key,current: 1, pageSize: 1, totalSize: 1,})
    this.field.reset()
    this.queryTask(key)
  }

  //格式化时间
  formateDate=(value,str)=>{
    return str;
  }

  render() {
    const {init} = this.field
    const {formValue, dataLoading, current, pageSize, totalSize, selectedRowKeys, key, formValue1} = this.state
    return (

      <div className='Metermanage'>

        {
          key === '1' ?
            <IceContainer title="搜索">

              <Form direction="hoz" field={this.field} style={{textAlign: 'center'}}>
                <FormItem label="抄表区域：">
                  <Select multiple placeholder="请选择区域" {...init('areas')}
                          style={{width: 205}}>
                    {this.area()}
                  </Select>
                </FormItem>
                <Button onClick={() => this.queryTask(key)} type="primary">查询</Button>
              </Form>

            </IceContainer>
            :
            void (0)
        }


        <IceContainer title="区域信息">

          <Form direction="hoz" field={this.field} style={{display: 'flex', justifyContent: 'flex-end'}}>
            {
              key === '1' ?
                <FormItem label="抄表员：">
                  <Select placeholder="请选择" style={{width: 80}} {...init('uid', {
                    rules: [{required: true, message: "请选抄表员"}]})}>
                    {this.renderRoleName()}
                  </Select>
                </FormItem> :
                <div>
                  <FormItem label="抄表员：">
                    <Select placeholder="请选择" style={{width: 150}} {...init('uid')}>
                      {this.renderRoleName()}
                    </Select>
                  </FormItem>
                  <FormItem label="任务派发时间：">
                    <MonthPicker {...init('yearMonth',{ getValueFromEvent: this.formateDate})}/>
                  </FormItem>
                </div>
            }

            {
              key === '1' ? <Button type="primary" onClick={() => this.dispatch()}>任务派发</Button> :
                <Button type="primary" onClick={() => this.queryTask(key)}>查询</Button>
            }

          </Form>

          <Tab size="small" onChange={(key) => this.tabChange(key)} style={{marginLeft: 40}}>

            <TabPane tab="任务发布" key="1">
              <Table dataSource={formValue} isLoading={dataLoading} rowSelection={{
                ...this.rowSelection,
                selectedRowKeys: selectedRowKeys,
              }} primaryKey="wid">
                <Table.Column title="用户编号" dataIndex="cno" align="center"/>
                <Table.Column title="用户名" dataIndex="cname" align="center"/>
                <Table.Column title="水表编号" dataIndex="wid" align="center"/>
                <Table.Column title="安装地址" dataIndex="location" align="center"/>
                <Table.Column title="用户地址" dataIndex="address" align="center"/>
                <Table.Column title="上次抄表日期" dataIndex="copyTime" align="center"/>
                <Table.Column title="用水性质" dataIndex="fname" align="center"/>
                <Table.Column title="上期行度" dataIndex="lastNum" align="center"/>
              </Table>
              <Pagination
                style={{textAlign: 'right', marginTop: 15}}
                onChange={(current) => this.changePage(current)}
                current={current}
                pageSize={pageSize}
                total={totalSize}
                size="small"
              />
            </TabPane>

            <TabPane tab="已发布的任务" key="2">
              <Table dataSource={formValue1} isLoading={dataLoading} primaryKey="wid">
                <Table.Column title="用户编号" dataIndex="cno" align="center"/>
                <Table.Column title="用户名" dataIndex="cname" align="center"/>
                <Table.Column title="水表编号" dataIndex="wid" align="center"/>
                <Table.Column title="上期行度" dataIndex="lastNum" align="center"/>
                <Table.Column title="本期行度" dataIndex="thisNum" align="center"/>
                <Table.Column title="用户地址" dataIndex="address" align="center"/>
                <Table.Column title="任务派发时间" dataIndex="createTime" align="center"/>
                <Table.Column title="抄表员" dataIndex="uid" align="center"/>
              </Table>
              <Pagination
                style={{textAlign: 'right', marginTop: 15}}
                onChange={(current) => this.changePage(current)}
                current={current}
                pageSize={pageSize}
                total={totalSize}
                size="small"
              />
            </TabPane>
          </Tab>
        </IceContainer>

      </div>
    )
  }
}
