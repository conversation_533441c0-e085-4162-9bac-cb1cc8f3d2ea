import React, {Component} from 'react';
import {
  <PERSON>oon,
  <PERSON><PERSON>,
  <PERSON>alog,
  <PERSON><PERSON><PERSON>,
  Field,
  Form,
  Icon,
  Input,
  moment,
  Radio
} from '@icedesign/base';
import axios from 'axios';
import qs from 'qs';
import getLodop from '../../../../common/LodopFuncs'
import {url} from '../../../../components/URL/index'

const FormItem = Form.Item
const {Group: RadioGroup} = Radio
const Tooltip = Balloon.Tooltip;
var LODOP = getLodop();

export default class BillPrint extends Component {

  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      save: false,
      formwork: [],
      createId: sessionStorage.getItem("stuffId") ,
      createName: sessionStorage.getItem("realName") ,
      latestInvoice: null,
      latestPrintTime: null
    }
    this.field = new Field(this, {autoUnmount: true});
  }

  componentDidMount() {
    this.field.setValues({ printType: '0' });
  }

  //打开弹窗 并且调用模板
  onOpen() {
    const printId = sessionStorage.getItem('stuffId');
    // const printId = "952e75e3f285433abe97a78432224916";
    axios.post(`${url}revenue/billCopyInvoice/getLastInvoiceNo`, { printId: printId }).then((response) => {
      this.setState({latestInvoice: response.data.datas?.invoiceNo});
      this.setState({latestPrintTime: response.data.datas?.printTime});
    })

    axios({
      method: 'post',
      url: `${url}revenue/printTemplate/get`,
      data: qs.stringify({templateName: '机械表账单发票'}),
    }).then(response => {
      let ajaxData = response.data;
      if (ajaxData.code == "0") {
        this.setState({formwork: ajaxData.datas});
      }
    }).catch(error => {
      Feedback.toast.error("系统异常请稍后再试", error);
    })
    this.setState({visible: true})
  };

  //关闭弹窗
  onClose() {
    this.setState({visible: false})
  }

  //确定打印
  print() {
    //打印所需参数
    let values = this.field.getValues();
    const printType = values.printType;
    const waterInvoiceNo = values.waterInvoiceNo;
    const sewageInvoiceNo = values.sewageInvoiceNo;

    if(printType === '2' && waterInvoiceNo === sewageInvoiceNo){
      alert('清水发票号码和污水发票号码不能相同！');
    }else{
      axios.post(`${url}revenue/billCopyInvoice/checkIfUsed`, 
        {invoiceNo: values.invoiceNo, waterInvoiceNo: values.waterInvoiceNo, sewageInvoiceNo: values.sewageInvoiceNo }
      ).then((response) => {
        if(response.data.code === '1'){
            alert(response.data.msg);
        }else {
            if (values.printType == '0') {
                this.printAll()
            } else {
                this.printWaterTotalFee()
            }
        }
      })
    }
  }

  //总水费
  printAll() {
    this.setState({save: true})
    const {record} = this.props
    LODOP = getLodop()
    const {formwork, createName, createId} = this.state
    //模板数据
    let templateCode = formwork.templateCode
    //打印记录所需参数
    let values = {}
    values.printName = createName
    values.printId = createId
    values.printType = '0'
    values.id = record.id
    values.invoiceNo = this.field.getValue('invoiceNo')
    let temp = templateCode.replace("日期", moment(new Date()).format('YYYY-MM'))
      .replace("用户编号", record.hno)
      .replace("用户名", record.cname)
      .replace("应收金额1", record.amountTotal.toFixed(2))
      .replace("现示数1", record.thisNumTotal)
      .replace("水量1", record.tunnageTotal)
      .replace("清水单价", record.price)
      .replace("污水单价", record.sewageTotalFee.toFixed(2))
      .replace("水资源费1", record.sourceTotalFee.toFixed(2))
      .replace("操作员", record.uname)
      .replace("日期1", moment(new Date()).format('YYYY-MM'))
      .replace("用户编号1", record.hno)
      .replace("用户名1", record.cname)
      .replace("应收金额2", record.amountTotal.toFixed(2))
      .replace("前示数1", record.lastNumTotal)
      .replace("现示数2", record.thisNumTotal)
      .replace("水量2", record.tunnageTotal)
      .replace("清水单价1", record.price)
      .replace("污水单价1", record.sewageTotalFee.toFixed(2))
      .replace("操作员1",record.uname)
      .replace("水资源费2", record.sourceTotalFee.toFixed(2))
      .replace("制表人1", createName)
    eval(temp);
    let flag = LODOP.PRINT()
    if (flag) {
      axios({
        method: 'post',
        url: `${url}revenue/billCopyInvoice/updatePrintStatus`,
        data: qs.stringify(values)
      }).then(response => {
        if (response.data.code == "0") {
          this.setState({visible: false, save: false})
          Feedback.toast.success("添加记录成功");
          this.props.queryUnprint()
        } else {
          this.setState({save: false})
          Feedback.toast.error("添加记录失败");
        }
      }).catch(error => {
        this.setState({save: false})
        Feedback.toast.error("系统异常请稍后再试" + error);
      })
    }
  }

  //清水费
  printWaterTotalFee() {
    this.setState({save: true})
    const {record} = this.props
    LODOP = getLodop()
    const {formwork, createName} = this.state
    //模板数据
    let templateCode = formwork.templateCode
    let fee=Number(record.waterTotalFee)+Number(record.sourceTotalFee)
    let temp = templateCode.replace("日期", moment(new Date()).format('YYYY-MM')).replace("用户编号", record.hno)
      .replace("用户名", record.cname)
      .replace("应收金额1", fee.toFixed(2))
      .replace("现示数1", record.thisNumTotal)
      .replace("水量1", record.tunnageTotal)
      .replace("清水单价", record.price)
      .replace("污水单价", '')
      .replace("水资源费1", record.sourceTotalFee.toFixed(2))
      .replace("操作员", record.uname)
      .replace("日期1", moment(new Date()).format('YYYY-MM'))
      .replace("用户编号1", record.hno)
      .replace("用户名1", record.cname)
      .replace("应收金额2", fee.toFixed(2))
      .replace("前示数1", record.lastNumTotal)
      .replace("现示数2", record.thisNumTotal)
      .replace("水量2", record.tunnageTotal)
      .replace("清水单价1", record.price)
      .replace("污水单价1", '')
      .replace("操作员1", record.uname)
      .replace("水资源费2", record.sourceTotalFee.toFixed(2))
      .replace("制表人1", createName)
    eval(temp);
    let flag = LODOP.PRINT()
    if (flag) {
      this.printSewageTotalFee()
    }
  }

  //污水费
  printSewageTotalFee() {
    const {record} = this.props;
    const {formwork, createName, createId} = this.state
    LODOP = getLodop()
    let values = {}
    values.printName = createName
    values.printId = createId
    values.printType = '2'
    values.id = record.id
    values.waterInvoiceNo = this.field.getValue('waterInvoiceNo')
    values.sewageInvoiceNo = this.field.getValue('sewageInvoiceNo')
    //模板数据
    let templateCode = formwork.templateCode
    //打印记录所需参数
    let temp = templateCode.replace("日期", moment(new Date()).format('YYYY-MM')).replace("用户编号", record.hno)
      .replace("用户名", record.cname)
      .replace("应收金额1", record.sewageTotalFee.toFixed(2))
      .replace("现示数1", record.thisNumTotal)
      .replace("水量1", record.tunnageTotal)
      .replace("清水单价", '')
      .replace("污水单价", record.sewageFee.toFixed(2))
      .replace("水资源费1", '')
      .replace("操作员", record.uname)
      .replace("日期1", moment(new Date()).format('YYYY-MM'))
      .replace("用户编号1", record.hno)
      .replace("用户名1", record.cname)
      .replace("应收金额2", record.sewageTotalFee.toFixed(2))
      .replace("前示数1", record.lastNumTotal)
      .replace("现示数2", record.thisNumTotal)
      .replace("水量2", record.tunnageTotal)
      .replace("清水单价1",'')
      .replace("污水单价1",record.sewageFee.toFixed(2))
      .replace("操作员1", record.uname)
      .replace("水资源费2", '')
      .replace("制表人1", createName)
    eval(temp);
    let flag = LODOP.PRINT()
    if (flag) {
      axios({
        method: 'post',
        url: `${url}revenue/billCopyInvoice/updatePrintStatus`,
        data: qs.stringify(values)
      }).then(response => {
        if (response.data.code == "0") {
          this.setState({visible: false,save:false})
          Feedback.toast.success("添加记录成功");
          this.props.queryUnprint()
        } else {
          this.setState({save: false})
          Feedback.toast.error("添加记录失败");
        }
      }).catch(error => {
        this.setState({save: false})
        Feedback.toast.error("系统异常请稍后再试" + error);
      })
    }
  }

  copyInvoice = () => {
    const { latestInvoice } = this.state;
    const textArea = document.createElement("textarea");
    textArea.value = latestInvoice;

    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        return document.execCommand('copy');
    } catch (err) {
        return false;
    } finally {
        document.body.removeChild(textArea);
    }
  }

  render() {
    const init = this.field.init;
    const {record} = this.props;
    const {save, latestInvoice, latestPrintTime} = this.state
    const formItemLayout = {labelCol: {fixedSpan: 7}};
    const footer = (
      <div>
        <Button type="primary" onClick={() => this.copyInvoice()}>
          复制发票号码
        </Button>
        <Button type="primary" loading={save} style={{marginLeft: 20}} onClick={() => this.print()}>
          确定打印
        </Button>
        <Button onClick={() => this.onClose()} style={{marginLeft: 20}}>
          取消
        </Button>
      </div>
    )
    const print = (
      <a onClick={() => this.onOpen(record)} title="打印发票">
        <Icon type="print" style={{color: "#3399ff", cursor: "pointer"}} size="small"/>
      </a>
    )
    return (
      <div>

        <Tooltip trigger={print} align='t' text='打印发票'/>

        <Dialog
          style={{width: 400}}
          visible={this.state.visible}
          onClose={()=>this.onClose()}
          footer={footer}
          title='打印发票确认'
          footerAlign="center"
        >
          <Form field={this.field}>
            <FormItem {...formItemLayout} label="打印方式：">
              <RadioGroup {...init('printType', {rules: [{required: true, message: '必填'}]})}
                          dataSource={[
                            {value: '0', label: "合并打印"},
                            {value: '2', label: "清污分离打印"}
                          ]}
                          defaultValue={'0'}
              />
            </FormItem>

            {
              this.field.getValue('printType') === '0' ?
                  <FormItem label="发票号码：" {...formItemLayout}>
                    <Input {...init('invoiceNo', { rules: [{ required: true, message: '必填' }] })} 
                      style={{width: 200}} />
                  </FormItem>:
                void (0)
            }
            {
              this.field.getValue('printType') === '2' ?
                (
                  <>
                    <FormItem label="清水发票号码：" {...formItemLayout}>
                      <Input {...init('waterInvoiceNo', { rules: [{ required: true, message: '必填' }] })} 
                        style={{width: 200}} />
                    </FormItem>
                    <FormItem label="污水发票号码：" {...formItemLayout}>
                      <Input {...init('sewageInvoiceNo', { rules: [{ required: true, message: '必填' }] })} 
                        style={{width: 200}} />
                    </FormItem>
                  </>
                ) :
                void (0)
            }

            <div>
                <FormItem label="上次使用发票号码：" {...formItemLayout} >
                    <Input
                      value={latestInvoice}
                      readOnly
                      style={{width: 200, backgroundColor: '#E6E6E6'}}
                    />
                </FormItem>
            </div>

            <div>
                <FormItem label="上次开票时间：" {...formItemLayout} >
                    <Input
                      value={latestPrintTime}
                      readOnly
                      style={{width: 200, backgroundColor: '#E6E6E6'}}
                    />
                </FormItem>
            </div>

          </Form>
        </Dialog>

      </div>
    );
  }
}
