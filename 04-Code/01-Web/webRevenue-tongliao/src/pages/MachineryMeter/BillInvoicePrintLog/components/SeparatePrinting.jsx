import React, {Component} from 'react'
import {But<PERSON>, Feedback, moment, Table} from '@icedesign/base';
import {FormBinderWrapper} from '@icedesign/form-binder';
import IceContainer from '@icedesign/container';
import ArtcleList from './ArtcleList'
import {Link} from 'react-router-dom';
import qs from 'qs';
import axios from "axios/index";
import {url} from "../../../../components/URL";
import getLodop from "../../../../common/LodopFuncs";

export default class SeparatePrinting extends Component {

  constructor(props) {
    super(props);
    this.state = {
      save: false,
      record: {},
      billInvoiceList: [],
      value: {
        printList: [{}]
      },
      createId: sessionStorage.getItem("stuffId"),
      createName: sessionStorage.getItem("realName"),
    };
  }

  componentDidMount() {
    this.findByBillIds()
  }

  findByBillIds() {
    const param = this.props.location.state
    if (param == undefined) {
      this.props.history.push('/machineryMeter/billInvoicePrintLog');
      return;
    }
    this.setState({record: param.billInvoiceRecord})
    axios({
      method: 'post',
      url: `${url}revenue/billCopy/findByBillIds`,
      data: qs.stringify({billIds: param.billInvoiceRecord.billIds}),
    })
      .then((response) => {
        this.setState({
          billInvoiceList: response.data.datas,
        })
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
        this.setState({dataLoading: false,})
      })
  }

  addItem = () => {
    this.state.value.printList.push({});
    this.setState({value: this.state.value});
  };

  formChange = value => {
    this.setState({value});
  };

  removeItem = (index) => {
    this.state.value.printList.splice(index, 1);
    this.setState({
      value: this.state.value
    });
  }

  validateAllFormField = () => {
    const {value, record} = this.state;

    const invoiceArray = [];
    const paramsArray = []

    value.printList.map((item) => {
      invoiceArray.push(item.invoiceNo);
      paramsArray.push({invoiceNo: item.invoiceNo});
    })

    if(Array.from(new Set(invoiceArray)).length != invoiceArray.length) {
      alert("发票中有重复发票编号！");
    }else{
      axios.post(`${url}revenue/billCopyInvoice/checkIfUsedSeperated`, paramsArray).then((response) => {
        if(response.data.code === '1'){
          alert(response.data.msg);
        }else{
          let allPrice = 0
          value.printList.map((item, key) => {
            allPrice += Number(item.amount)*100
          })
          allPrice= allPrice/100
          this.refs.form.validateAll((errors, values) => {
            if (errors) {
              return
            } else {
              if (allPrice==record.amountTotal) {
                axios({
                  method: 'post',
                  url: `${url}revenue/printTemplate/get`,
                  data: qs.stringify({templateName: '机械表账单发票'}),
                }).then(response => {
                  if (response.data.code == "0") {
                    this.setState({formwork: response.data.datas});
                    this.print()
                  }
                }).catch(error => {
                  Feedback.toast.error("系统异常请稍后再试"+error);
                })
              }else {
                Feedback.toast.error("待打印的发票的应收金额之和不等于账单总金额");
              }
            }
          });
        }
      });
    }
  }

  print() {
    this.setState({save: true})
    let LODOP = getLodop()
    const {formwork, createId, createName, value, record} = this.state
    //模板数据
    let templateCode = formwork.templateCode
    //打印记录所需参数
    let list = {}
    list.id = record.id
    list.printId = createId
    list.printName = createName
    list.printType = '1'
    list.printList = value.printList
    value.printList.map((item, key) => {
      let temp = templateCode.replace("日期", moment(new Date()).format('YYYY-MM'))
        .replace("用户编号", item.cno)
        .replace("用户名", item.cname)
        .replace("应收金额1", item.amount)
        .replace("现示数1", item.thisNum)
        .replace("水量1", item.tunnage)
        .replace("清水单价", record.price)
        .replace("污水单价", item.sewageFee)
        .replace("水资源费1", item.sourceFee)
        .replace("操作员", record.uname)
        .replace("日期1", moment(new Date()).format('YYYY-MM'))
        .replace("用户编号1", item.cno)
        .replace("用户名1", item.cname)
        .replace("应收金额2", item.amount)
        .replace("前示数1", item.lastNum)
        .replace("现示数2", item.thisNum)
        .replace("水量2", item.tunnage)
        .replace("清水单价1", record.price)
        .replace("污水单价1", item.sewageFee)
        .replace("操作员1", record.uname)
        .replace("水资源费2", item.sourceFee)
        .replace("制表人1", createName)
      eval(temp);
      LODOP.PRINT()
    })
    axios({
      method: 'post',
      url: `${url}revenue/billCopyInvoice/updatePrintStatusSeparate`,
      data: list
    }).then(response => {
      if (response.data.code == "0") {
        Feedback.toast.success("添加记录成功");
        this.setState({save: false})
      } else {
        this.setState({printStatus: false})
        Feedback.toast.error("添加记录失败");
      }
    }).catch(error => {
      this.setState({save: false})
      Feedback.toast.error("系统异常请稍后再试" + error);
    })
  }

  render() {
    const {billInvoiceList, value, save} = this.state
    return (
      <div>

        <IceContainer title="账单详情列表">
          <Table dataSource={billInvoiceList}>
            <Table.Column title="用户编号" dataIndex="cno" align="center"/>
            <Table.Column title="水表编号" dataIndex="watermeterId" align="center"/>
            <Table.Column title="用户名称" dataIndex="cname" align="center"/>
            <Table.Column title="用水性质" dataIndex="feeName" align="center"/>
            <Table.Column title="出账日期" dataIndex="createTime" align="center"/>
            <Table.Column title="上期示数" dataIndex="lastNum" align="center"/>
            <Table.Column title="本期示数" dataIndex="thisNum" align="center"/>
            <Table.Column title="用水量" dataIndex="tunnage" align="center"/>
            <Table.Column title="账单金额" dataIndex="amount" align="center"/>
          </Table>
        </IceContainer>

        <IceContainer title='编辑发票'>
          <FormBinderWrapper value={value} onChange={this.formChange} ref="form">
            <ArtcleList printList={value.printList} addItem={this.addItem} removeItem={this.removeItem}
                        validateAllFormField1={this.validateAllFormField1}/>
          </FormBinderWrapper>
          <div style={{textAlign: 'center', marginTop: 10}}>
            <Button type="primary" loading={save} onClick={this.validateAllFormField}>
              确定打印
            </Button>
            <Link to={{
              pathname: `/machineryMeter/billInvoicePrintLog`,
            }}>
              <Button type="primary" style={{marginLeft: 10}}>
                返回
              </Button>
            </Link>
          </div>
        </IceContainer>

      </div>
    );
  }
}







