import React, {Component} from 'react';
import {url} from '../../../../components/URL';
import {
  Balloon,
  Button,
  Dialog,
  Feedback,
  Field,
  Form,
  Grid,
  Icon,
  Input,
  moment,
  Upload
} from '@icedesign/base';
import axios from 'axios/index';
import qs from 'qs';
import './index.scss'

const Tooltip = Balloon.Tooltip;
const FormItem = Form.Item;
const { Row } = Grid;
export default class PublishedTaskDialog extends Component {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      notBilltoyearTunnage: null, // 判断有没有账单是否显示年度购水量
      notBilltolastNum: null, // 判断有没有账单是否允许修改上期示数
      createId: sessionStorage.getItem('stuffId'),
      createName: sessionStorage.getItem('realName'),
      isFreeTunnage: false,
      imgUrl: ''
    };
    this.field = new Field(this, {autoUnmount: true});
  }

  beforeUpload = (info) => {
    // console.log("beforeUpload callback : ", info);
  }

  onChangeUpload = (info) => {
    console.log("onChange callback : ", info);
  }

  onSuccess = (res, file) => {
    this.setState({imgUrl: res.datas.src});
  }

  // 开启弹窗
  openDialog(record) {
    this.checkFirstByHno(record);
    this.checkFirstByCnoAndWatermeterId(record);
    this.field.setValues({...record});
    this.field.setValue('createTime',
        moment(new Date()).format('YYYY-MM-DD HH:mm:ss'));
    this.setState({visible: true});
  }

  // 判断有没有账单是否显示年度购水量
  checkFirstByHno(record) {
    axios({
      method: 'post',
      url: `${url}revenue/billCopy/checkFirstByHno`,
      data: qs.stringify({ hno: record.hno }),
    })
      .then((response) => {
        if (response.data.code == '0') {
          if (response.data.datas == '1') {
            this.setState({ notBilltoyearTunnage: true });
          } else {
            this.setState({ notBilltoyearTunnage: false });
          }
        } else {
          Feedback.toast.error(response.data.msg);
        }
      })
      .catch((error) => {
        Feedback.toast.error(`请求错误：${error}`);
      });
  }

  // 判断有没有账单是否允许修改上期示数
  checkFirstByCnoAndWatermeterId(record) {
    axios({
      method: 'post',
      url: `${url}revenue/billCopy/checkFirstByCno`,
      data: qs.stringify({ cno: record.cno, watermeterId: record.watermeterId }),
    })
      .then((response) => {
        if (response.data.code == '0') {
          this.setState({ notBilltolastNum: response.data.datas });
        } else {
          Feedback.toast.error(response.data.msg);
        }
      })
      .catch((error) => {
        Feedback.toast.error(`请求错误：${error}`);
      });
  }

  // 关闭弹窗
  handleCancel() {
    this.setState({ visible: false });
  }

  // 弹窗确定回掉函数
  handleOk() {
    this.field.validate((errors, values) => {
      this.setState({ save: true });
      const {notBilltoyearTunnage, notBilltolastNum, createName, createId, imgUrl} = this.state;
      const {record} = this.props;
      const list = {};
      list.id = record.id;
      list.thisNum = values.thisNum;
      list.type = '2';
      list.updateId = createId;
      list.updateName = createName;
      list.imgs = imgUrl;
      list.way = '2';
      list.tunnageForYear = values.tunnageForYear ? values.tunnageForYear
        : void (0);
      list.freeTunnage = values.freeTunnage;
      list.remark = values.remark
      if (errors) {
        this.setState({ save: false });
        return;
      }
      if (values.detail) {
        if (notBilltoyearTunnage) {
          if (notBilltolastNum == '1') {
            axios({
              method: 'post',
              url: `${url}revenue/billCopy/updateLastNum`,
              data: qs.stringify({
                cno: record.cno,
                watermeterId: record.watermeterId,
                lastNum: values.lastNum,
                uid: createId,
                uname: createName
              }),
            }).then((response) => {
              if (response.data.code == '0') {
                this.upload(list);
              } else {
                this.setState({ save: false });
                Feedback.toast.error(response.data.msg);
              }
            }).catch((error) => {
              this.setState({ save: false });
              Feedback.toast.error(`请求错误：${error}`);
            });
          } else {
            this.upload(list);
          }

          /* axios({
              method: 'post',
              url: `${url}revenue/billCopy/updateYearTunnage`,
              data: qs.stringify({cno: record.cno, tunnageForYear: values.tunnageForYear}),
            }).then((response) => {
              if (response.data.code == "0") {

              } else {
                this.setState({save: false})
                Feedback.toast.error(response.data.msg);
              }
            }).catch((error) => {
              this.setState({save: false})
              Feedback.toast.error("请求错误：" + error);
            }); */
        } else if (notBilltolastNum == '1') {
          axios({
            method: 'post',
            url: `${url}revenue/billCopy/updateLastNum`,
            data: qs.stringify({
              cno: record.cno,
              watermeterId: record.watermeterId,
              lastNum: values.lastNum
            }),
          }).then((response) => {
            if (response.data.code == '0') {
              this.upload(list);
            } else {
              Feedback.toast.error(response.data.msg);
            }
          }).catch((error) => {
            this.setState({ save: false });
            Feedback.toast.error(`请求错误：${error}`);
          });
        } else {
          this.upload(list);
        }
      }
    });
  }

  // 抄表
  upload(list) {
    axios({
      method: 'post',
      url: `${url}revenue/copyTask/upload`,
      data: qs.stringify(list),
    }).then((response) => {
      if (response.data.code == '0') {
        Feedback.toast.success('抄表成功');
        this.props.queryTask();
        this.setState({ visible: false, save: false });
      } else {
        this.setState({ save: false });
        Feedback.toast.error(response.data.msg);
      }
    }).catch((error) => {
      this.setState({ save: false });
      Feedback.toast.error(`请求错误：${error}`);
    });
  }

  // 失去焦点计算
  handleOnBlur(record) {
    const { notBilltoyearTunnage } = this.state;
    const thisNum = this.field.getValue('thisNum');
    const lastNum = this.field.getValue('lastNum');
    const freeTunnage = this.field.getValue('freeTunnage');
    const thisTunnage = thisNum - lastNum - (freeTunnage ? freeTunnage : 0);
    if (notBilltoyearTunnage) {
      const tunnageForYear = this.field.getValue('tunnageForYear');
      const values = {};
      values.cno = record.cno;
      values.thisNum = thisNum;
      values.tunnage = thisTunnage;
      values.tunnageForYear = tunnageForYear;
      values.id = record.id;
      if (tunnageForYear) {
        if (Number(lastNum) <= Number(thisNum)) {
          axios.post(
            `${url}revenue/copyTask/account`,
            qs.stringify(values)
          ).then((response) => {
            const jsondata = response.data;
            if (jsondata.code == '0') {
              this.field.setValue('tunnage', thisTunnage);
              this.field.setValue('amount', jsondata.datas.amount);
              this.field.setValue('detail', jsondata.datas.detail);
            } else {
              Feedback.toast.error(jsondata.msg);
            }
          }).catch((error) => {
            Feedback.toast.error(`请求错误：${error}`);
          });
        } else {
          Feedback.toast.error('本期示数不能小于上期示数！');
        }
      } else {
        this.field.reset('thisNum');
        Feedback.toast.error('请先填写年度水量');
      }
    } else if (Number(lastNum) <= Number(thisNum)) {
      axios.post(`${url}revenue/copyTask/account`,
        qs.stringify({ cno: record.cno, tunnage: thisTunnage, id: record.id }))
        .then((response) => {
          const jsondata = response.data;
          if (jsondata.code == '0') {
            this.field.setValue('tunnage', thisTunnage);
            this.field.setValue('amount', jsondata.datas.amount);
            this.field.setValue('detail', jsondata.datas.detail);
          } else {
            Feedback.toast.error(jsondata.msg);
          }
        }).catch((error) => {
          Feedback.toast.error(`请求错误：${error}`);
        });
    } else {
      Feedback.toast.error('本期示数不能小于上期示数！');
    }
  }

  handleonChange(value) {
    this.field.setValue('tunnageForYear', value);
    this.field.reset('thisNum');
  }

  checkedPay = (rule, value, callback) => {
    if (value) {
      if (value < 0) {
        callback('年度购水量不能为负数');
      } else if (isNaN(value)) {
        callback('年度购水量必须是数字');
      } else if (value.indexOf('.') > 0) {
        callback('年度购水量不能为小数');
      } else {
        callback();
      }
    } else {
      callback('必填');
    }
  }

  checkedFreeTunnage = (rule, value, callback) => {
    const thisNum = this.field.getValue('thisNum');
    const lastNum = this.field.getValue('lastNum');
    if (value) {
      if (Number(thisNum) - Number(lastNum) < value) {
        callback('减免吨数必须小于等于本期示数-上期示数');
      } else {
        callback();
      }
    } else {
      callback('必填');
    }
  }

  render() {
    const {visible, notBilltolastNum, notBilltoyearTunnage, save, isFreeTunnage, imgUrl} = this.state;
    const {record} = this.props;
    const { init } = this.field;
    const formItemLayout = {
      labelCol: { fixedSpan: 7 },
    };
    const footer = (
      <div style={{ marginTop: 20 }} align="center">
        <Button type="primary" loading={save} onClick={() => this.handleOk()}>
          确定
        </Button>
        <Button onClick={() => this.handleCancel()} style={{ marginLeft: 20 }}>
          取消
        </Button>
      </div>
    );
    const edit = (<Icon type="clock"
      size="small"
      style={{ color: '#1DC11D', cursor: 'pointer' }}
      onClick={() => this.openDialog(record)}
    />
    );
    return (
      <section>

        <Tooltip trigger={edit} align="t" text="抄表" />

        <Dialog
          minMargin={10}
          style={{ width: 700 }}
          visible={visible}
          onClose={() => this.handleCancel()}
          footer={footer}
          title="抄表"
          footerAlign="center"
        >
          <Row>
            {
              isFreeTunnage == true ?
                <div style={{ margin: '10px auto' }}>
                  <Button
                    className="isFreeTunnageBtn"
                    style={{ backgroundColor: '#36a6ff' }}
                    type="secondary"
                    onClick={() => {
                      this.setState({
                        isFreeTunnage: !this.state.isFreeTunnage, freeTunnage: 0
                      }, () => {
                        this.field.setValue('thisNum', '')
                      })
                    }}>
                    减免吨数 <span className="isFreeTunnageSpan">&#8594; 非减免</span>
                  </Button>
                </div> :
                <div style={{ margin: '10px auto' }}>
                  <Button className="isFreeTunnageBtn" style={{ backgroundColor: '#36a6ff' }} type="secondary" onClick={() => { this.setState({ isFreeTunnage: !this.state.isFreeTunnage }) }}>
                    非减免 <span className="isFreeTunnageSpan">&#8594; 减免吨数</span>
                  </Button>
                </div>
            }
          </Row>
          <Form field={this.field}>
            <Row>
              <FormItem {...formItemLayout} label="用户编号：">
                <Input {...init('cno')}
                  style={{ backgroundColor: '#E6E6E6', width: 170 }}
                  readOnly />
              </FormItem>

              <FormItem {...formItemLayout} label="用户名称：">
                <Input {...init('cname')}
                  style={{ backgroundColor: '#E6E6E6', width: 170 }}
                  readOnly />
              </FormItem>
            </Row>

            <Row>
              {
                notBilltolastNum == '1' ?
                  <FormItem {...formItemLayout} label="上期示数：">
                    <Input {...init('lastNum')} style={{ width: 170 }} />
                  </FormItem> :
                  <FormItem {...formItemLayout} label="上期示数：">
                    <Input {...init('lastNum')} readOnly
                      style={{ backgroundColor: '#E6E6E6', width: 170 }} />
                  </FormItem>
              }

              <FormItem {...formItemLayout} label="本期示数：">
                <Input {...init('thisNum',
                  { rules: [{ required: true, message: '本期示数必填' }] })}
                  style={{ width: 170 }}
                  onBlur={() => this.handleOnBlur(record)}
                />
              </FormItem>
            </Row>
            <Row>
              {
                isFreeTunnage == true &&
                <FormItem {...formItemLayout} label="减免吨数：">
                  <Input {...init('freeTunnage', { rules: [{ required: true, validator: this.checkedFreeTunnage }] })}
                    onBlur={() => this.handleOnBlur(record)}
                    style={{ width: 170 }} />
                </FormItem>
              }
            </Row>

            <Row>
              <FormItem {...formItemLayout} label="抄表时间：">
                <Input {...init('createTime')}
                  style={{ backgroundColor: '#E6E6E6', width: 170 }}
                  readOnly />
              </FormItem>

              <FormItem {...formItemLayout} label="本期水量：">
                <Input {...init('tunnage')}
                  style={{ backgroundColor: '#E6E6E6', width: 170 }}
                  readOnly />
              </FormItem>
            </Row>

            <Row>
              <FormItem {...formItemLayout} label="用水性质：">
                <Input {...init('waterName')}
                  style={{ backgroundColor: '#E6E6E6', width: 170 }}
                  readOnly />
              </FormItem>

              {
                notBilltoyearTunnage ? <FormItem {...formItemLayout}
                  label="年度水量：">
                  <Input {...init('tunnageForYear',
                    { rules: [{ required: true, validator: this.checkedPay }] })}
                    style={{ width: 170 }}
                    onChange={value => this.handleonChange(value)}
                  />
                </FormItem> : void (0)
              }
            </Row>

            <Row>
              <FormItem label="水费金额:" {...formItemLayout} >
                <Input {...init('amount')}
                  style={{ backgroundColor: '#E6E6E6', width: 170 }}
                  readOnly />
              </FormItem>
            </Row>

            <Row>
              <FormItem label="水费明细：" {...formItemLayout} >
                <Input {...init('detail')}
                  style={{ backgroundColor: '#E6E6E6', width: 400 }}
                  readOnly />
              </FormItem>
            </Row>
            <Row>
              {
                isFreeTunnage == true &&
                <FormItem {...formItemLayout} label="备注">
                  <Input {...init('remark', {rules: [{required: true, message: '备注必填'}]})} style={{width: 400}}/>
                </FormItem>
              }
            </Row>
            <FormItem {...formItemLayout} label="上传图片：">
              <Upload
                  listType="text-image"
                  action={`${url}revenue/uploadMaterial`}
                  accept="image/png, image/jpg, image/jpeg, image/gif, image/bmp"
                  beforeUpload={this.beforeUpload}
                  onChange={this.onChangeUpload}
                  onSuccess={this.onSuccess}
                  withCredentials={true}
                  limit={1}
                  // {...init("upload", {
                  //   valueName: "fileList",
                  //   initValue: fileList
                  // })}
              >
                <Button type="primary" style={{margin: "0 0 10px"}}>上传文件</Button>
              </Upload>
            </FormItem>

          </Form>
        </Dialog>

      </section>
    );
  }
}
