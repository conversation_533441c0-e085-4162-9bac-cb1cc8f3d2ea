import React, {Component} from 'react';
import {
  Button,
  Dialog,
  Field,
  Form,
  Grid,
  Input,
  Select,
  Table
} from '@icedesign/base';
import IceContainer from '@icedesign/container';
import Img from '@icedesign/img';
import {Link} from 'react-router-dom';
import {url} from "../../../../components/URL";

const { Row } = Grid;

export default class MeterTaskInfo extends Component {

  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      dataIndex: null,
      dataSource: [],
      image: '',
      selectRow: {},
      selectValues: {}
    };
    this.field = new Field(this, { autoUnmount: true });
  }

  componentDidMount() {
    const param = this.props.location.state;
    if (param == undefined) {
      this.props.history.push('/machineryMeter/publishedTask');
      return;
    }
    const record = param.record;
    const values = param.values;
    this.setState({
      image: record.imgs,
      selectRow: record,
      selectValues: values
    });
    this.field.setValues({ ...record });
    if (record.longitude != null) {
      var BMap = window.BMap
      var map = new BMap.Map("allmap"); // 创建Map实例
      var point = new BMap.Point(record.longitude, record.latitude);//标注的点
      map.centerAndZoom(new BMap.Point(119.40, 32.40), 11)
      map.centerAndZoom(point, 15) // 初始化地图,设置中心点坐标和地图级别
      map.addControl(new BMap.MapTypeControl()) //添加地图类型控件
      map.setCurrentCity("扬州") // 设置地图显示的城市 此项是必须设置的
      map.enableScrollWheelZoom(true) //开启鼠标滚轮缩放
      var marker = new BMap.Marker(point);  // 创建标注
      map.addOverlay(marker);               // 将标注添加到地图中
      marker.setAnimation(BMAP_ANIMATION_BOUNCE); //跳动的动画
    }
  }

  preview() {
    this.setState({ visible: true })
  }

  onClose() {
    this.setState({ visible: false })
  }

  render() {
    const init = this.field.init;
    const { image, visible, selectRow } = this.state;
    const formItemLayout = {
      labelCol: { fixedSpan: 6 }
    };
    const footer = (
      <Button onClick={() => this.onClose()} style={{ marginLeft: 20 }}>
        关闭
      </Button>
    );

    return (
      <div>
        <IceContainer title="任务基本信息">
          <Form direction="hoz" field={this.field}>
            <Row>
              <Form.Item label="&emsp;用户编号：">
                <Input {...init('cno')} readOnly={true} style={styles.inputLength} />
              </Form.Item>
              <Form.Item label="用户姓名：" {...formItemLayout} >
                <Input {...init('cname')} readOnly={true} style={styles.inputLength} />
              </Form.Item>
              <Form.Item label="水表编号：" {...formItemLayout}>
                <Input {...init('watermeterId')} readOnly={true} style={styles.inputLength} />
              </Form.Item>
            </Row>
            <Row>
              <Form.Item label="&emsp;派发时间：">
                <Input {...init('createTime')} readOnly={true} style={styles.inputLength} />
              </Form.Item>
              <Form.Item label="区&emsp;&emsp;域：" {...formItemLayout}>
                <Input {...init('areaName')} readOnly={true} style={styles.inputLength} />
              </Form.Item>
              <Form.Item label="区&emsp;&emsp;册：" {...formItemLayout}>
                <Input {...init('regionName')} readOnly={true} style={styles.inputLength} />
              </Form.Item>
            </Row>
            <Row>
              <Form.Item label="&emsp;上期示数：">
                <Input {...init('lastNum')} readOnly={true} />
              </Form.Item>
              <Form.Item label="本期示数：" {...formItemLayout}>
                <Input {...init('thisNum')} readOnly={true} style={styles.inputLength} />
              </Form.Item>
              <Form.Item label="抄表员：" {...formItemLayout}>
                <Input {...init('uname')} readOnly={true} style={styles.inputLength} />
              </Form.Item>
            </Row>
            <Row>
              <Form.Item label="&emsp;抄表时间：">
                <Input {...init('copyTime')} readOnly={true} style={styles.inputLength} />
              </Form.Item>
              <Form.Item label="上传时间：" {...formItemLayout}>
                <Input {...init('updateTime')} readOnly={true} style={styles.inputLength} />
              </Form.Item>
              <Form.Item label="抄表状态：" {...formItemLayout}>
                <Select {...init('status')} disabled={true} style={styles.inputLength}
                  dataSource={[
                    { label: '未抄表', value: '0' },
                    { label: '已抄表', value: '1' },
                    { label: '已删除', value: '2' }
                  ]} />
              </Form.Item>
            </Row>
            {this.field.getValue('closeName') ?
              <Row>
                <Form.Item label="删除操作员：">
                  <Input {...init('closeName')} readOnly={true} style={styles.inputLength} />
                </Form.Item>

                <Form.Item label="删除时间：" {...formItemLayout}>
                  <Input {...init('closeTime')} readOnly={true} style={styles.inputLength} />
                </Form.Item>

                <Form.Item label="删除原因：" {...formItemLayout}>
                  <Input {...init('closeReason')} readOnly={true} style={{ width: 250 }} />
                </Form.Item>
              </Row> : void (0)}
            <Row>
              <Form.Item label="&emsp;地&emsp;&emsp;址：">
                <Input {...init('address')} readOnly={true} style={{ width: 300 }} />
              </Form.Item>
            </Row>
          </Form>
        </IceContainer>
        {selectRow.copyTaskExceptions &&
          <IceContainer title="抄表异常信息">
            <div>
              <Table dataSource={selectRow.copyTaskExceptions}>
                <Table.Column title="抄表异常类型" dataIndex="exception_type" align="center"  />
                <Table.Column title="抄表异常描述" dataIndex="exception_description" align="center"  />
                <Table.Column title="抄表异常备注" dataIndex="exception_remark" align="center" />
                <Table.Column title="抄表异常提交人" dataIndex="exception_creator" align="center"  />
                <Table.Column title="抄表异常提交时间" dataIndex="exception_time" align="center"  />
              </Table>
            </div>
          </IceContainer>}
        <IceContainer title="抄表照片">
          {
            image ? <div onClick={() => this.preview()} style={{ cursor: 'pointer', width: 300 }}>
              <Img
                width={300}
                height={200}
                src={`${url}` + image}
                type="contain"
                shape="sharp"
                title="点击预览"
                style={{ border: '0px solid #ccc', margin: '10px' }}
              />
            </div> : '暂无抄表照片'
          }
        </IceContainer>
        <IceContainer title="抄表位置">
          <div id='allmap' style={{ height: image ? '300px' : '5px' }} />
          {
            image ? '' : '暂无抄表位置'
          }
          <div align="center" style={{ marginTop: 20 }}>
            <Link to={{
              pathname: `/machineryMeter/publishedTask`,
               state: {selectPage: this.state.selectValues}
            }}> <Button type="primary">返回</Button>
            </Link>
          </div>
        </IceContainer>
        <Dialog
          minMargin={10}
          style={{ width: 700 }}
          visible={visible}
          onClose={() => this.onClose()}
          footer={footer}
          title='预览'
        >
          <Img
            width={700}
            height={700}
            src={`${url}` + image}
            type="contain"
            shape="sharp"
          />
        </Dialog>
      </div>

    )
  }
}

const styles = {
  inputLength: {
    width: 160,
    color: 'black'
  }
};
