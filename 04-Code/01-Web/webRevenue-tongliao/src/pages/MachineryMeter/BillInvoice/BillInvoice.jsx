import React, {Component} from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>ton,
  Dialog,
  <PERSON><PERSON>back,
  Field,
  Form,
  Icon,
  Input,
  moment,
  Pagination,
  Select,
  Table,
} from '@icedesign/base';
import {Link} from 'react-router-dom';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import qs from 'qs';
import {url} from '../../../components/URL/index';
import BillPrint from './components/BillPrint';
import getLodop from '../../../common/LodopFuncs';
import './index.scss'

const FormItem = Form.Item;
const { Combobox } = Select;
const Tooltip = Balloon.Tooltip;

export default class BillInvoice extends Component {
  constructor(props) {
    super(props);
    this.state = {
      formValue: [],
      areaList: [], // 片区
      regionList: [], // 小区
      feeNameList: [],
      page: 1,
      pageSize: 10,
      totalSize: 0,
      roleNameList: [],
      dataLoading: false,
      printStatus: false,
      selectedRecord: [],
      createId: sessionStorage.getItem('stuffId'),
      createName: sessionStorage.getItem('realName'),
      selectPage: {},
      modifyUserNameModal: false,
    };
    this.field = new Field(this, { autoUnmount: true });
    this.rowSelection = {
      onChange: (ids, records) => {
        this.setState({ selectedRowKeys: ids, selectedRecord: records });
      },
    };
  }

  componentDidMount() {
    const param = this.props.location.state
    if (param) {
      this.setState({
        selectPage: param.selectPage
      }, () => {
        this.setState({ dataLoading: true });
        axios({
          method: 'post',
          url: `${url}revenue/billCopyInvoice/queryUnprint`,
          data: qs.stringify(this.state.selectPage),
        })
            .then((response) => {
              this.field.setValues(this.state.selectPage)
              this.setState({
                formValue: response.data.datas,
                page: this.state.selectPage.page,
                pageSize: this.state.selectPage.pageSize,
                totalSize: response.data.totalSize,
                dataLoading: false,
              });
            })
            .catch((error) => {
              Feedback.toast.error(`请求错误：${error}`);
              this.setState({ dataLoading: false });
            });
      })
    } else {
      this.queryUnprint();
    }
    this.queryRegion();
    this.queryArea();
    this.queryUname();

    this.queryFeeName();
  }

  // 查询
  queryUnprint(page, pageSize) {
    this.setState({ dataLoading: true });
    const values = this.field.getValues();
    values.page = page;
    values.pageSize = pageSize;
    axios({
      method: 'post',
      url: `${url}revenue/billCopyInvoice/queryUnprint`,
      data: qs.stringify(values),
    })
        .then((response) => {
          this.queryTotal();
          this.setState({
            formValue: response.data.datas,
            page: response.data.page,
            pageSize: response.data.pageSize,
            totalSize: response.data.totalSize,
            dataLoading: false,
          });
        })
        .catch((error) => {
          Feedback.toast.error(`请求错误：${error}`);
          this.setState({ dataLoading: false });
        });
  }

  // 翻页
  changePage(page) {
    const { pageSize } = this.state;
    this.queryUnprint(page, pageSize);
  }

  // 改变pageSize
  changePageSize(pageSize) {
    this.queryUnprint(1, pageSize);
  }

  // 重置
  reset() {
    this.field.reset();
  }

  // 查询抄表员
  queryUname() {
    axios({
      method: 'get',
      url: `${url}revenue/staff/getAllCname`,
    })
        .then((response) => {
          if (response.data.code == 0) {
            const roleNameList = [];
            response.data.datas.map((item) => {
              roleNameList.push({ label: item.label, value: item.id });
            });
            this.setState({ roleNameList });
          } else {
            Feedback.toast.error(response.data.msg);
          }
        })
        .catch((error) => {
          Feedback.toast.error(`请求错误：${error}`);
        });
  }

  // 查询小区
  queryRegion() {
    axios({
      method: 'post',
      url: `${url}revenue/region/regionlist`,
      // data: qs.stringify(areaId)
    }).then((response) => {
      const regionList = [];
      response.data.datas.map((item) => {
        regionList.push({ label: item.regionName, value: item.id });
      });
      this.setState({ regionList });
    }).catch((error) => {
      Feedback.toast.error('请求异常', error);
    });
  }

  // 查询片区
  queryArea() {
    axios({
      method: 'get',
      url: `${url}revenue/area/getAll`,
    })
        .then((response) => {
          const areaList = [];
          response.data.datas.map((item) => {
            areaList.push({ label: item.name, value: item.id });
          });
          this.setState({ areaList });
        })
        .catch((error) => {
          Feedback.toast.error(`请求错误：${error}`);
        });
  }

  rowOptionRender(record) {
    const { page, pageSize } = this.state;
    const toValues = this.field.getValues();
    toValues.page = page;
    toValues.pageSize = pageSize;
    const billInvoiceDetails = (
        <Link to={{ pathname: './billInvoice/billInvoiceDetails', state: { billInvoiceRecord: record, values: toValues } }}>
          <a>
            <Icon type="browse" size="small" style={{ color: '#FFA003', cursor: 'pointer' }} />
          </a>
        </Link>
    );

    const separatePrinting = (
        <Link to={{ pathname: './billInvoice/separatePrinting', state: { billInvoiceRecord: record } }}>
          <a>
            <Icon type="cut" size="small" style={{ color: '#1DC11D', cursor: 'pointer' }} />
          </a>
        </Link>
    );

    return (
        <div className="operation">
          <Tooltip trigger={billInvoiceDetails} align="t" text="查看详情" />
          <BillPrint record={record} queryUnprint={() => this.queryUnprint(page, pageSize)} />
          <Tooltip trigger={separatePrinting} align="t" text="分打发票" />
          <Icon
              title="修改用户名称"
              type="account"
              size="small"
              style={{ cursor: 'pointer' }}
              onClick={() => this.setModifyUserNameModal(record)}
          />
        </div>
    );
  }

  setModifyUserNameModal = (record) => {
    this.setState({ modifyUserNameModal: true, currentRecordId: record.id });
  };
  onModifyUserNameModalClose = () => {
    this.setState({ modifyUserNameModal: false, currentRecordId: null });
  };
  modifyUserName = () => {
    const cname = this.field.getValue('newUsername');
    axios
        .post(`${url}revenue/billCopyInvoice/updateInvoiceCname`, {
          id: this.state.currentRecordId,
          cname
        })
        .then((response) => {
          const { status, data } = response;
          if (status === 200 && data.code === '0') {
            Feedback.toast.success('操作成功！');
            this.queryUnprint(1, 10);
          } else {
            Feedback.toast.error('操作失败！');
          }
          this.onModifyUserNameModalClose();
        });
  };

  // 查询用水性质
  queryFeeName() {
    axios({
      method: 'post',
      url: `${url}revenue/fee/queryList`,
    })
        .then((response) => {
          const feeNameList = [];
          response.data.datas.map((item) => {
            feeNameList.push({ label: item.name, value: item.id });
          });
          this.setState({ feeNameList });
        })
        .catch((error) => {
          Feedback.toast.error(`请求错误：${error}`);
        });
  }

  // 批量打印
  batchPrinting() {
    const { selectedRecord } = this.state;
    if (selectedRecord.length > 0) {
      axios({
        method: 'post',
        url: `${url}revenue/printTemplate/get`,
        data: qs.stringify({ templateName: '机械表账单发票' }),
      }).then((response) => {
        if (response.data.code == '0') {
          this.setState({ formwork: response.data.datas });
          this.print();
        }
      }).catch((error) => {
        Feedback.toast.error('系统异常请稍后再试', error);
      });
    } else {
      Feedback.toast.error('请至少勾选一笔账单');
    }
  }

  // 批量打印
  print() {
    this.setState({ printStatus: true });
    const LODOP = getLodop();
    const { formwork, createId, createName, selectedRecord } = this.state;
    // 模板数据
    const templateCode = formwork.templateCode;
    // 打印记录所需参数
    const values = {};
    values.printName = createName || '小明';
    values.printId = createId || '001';
    values.printType = '0';
    selectedRecord.map((item, key) => {
      values.id = item.id;
      const temp = templateCode.replace('日期', moment(new Date()).format('YYYY-MM')).replace('用户编号', item.hno)
          .replace('用户名', item.cname)
          .replace('应收金额1', item.amountTotal.toFixed(2))
          .replace('现示数1', item.thisNumTotal)
          .replace('水量1', item.tunnageTotal)
          .replace('清水单价', item.price)
          .replace('污水单价', item.sewageTotalFee.toFixed(2))
          .replace('水资源费1', item.sourceTotalFee.toFixed(2))
          .replace('操作员', item.uname)
          .replace('日期1', moment(new Date()).format('YYYY-MM'))
          .replace('用户编号1', item.hno)
          .replace('用户名1', item.cname)
          .replace('应收金额2', item.amountTotal.toFixed(2))
          .replace('前示数1', item.lastNumTotal)
          .replace('现示数2', item.thisNumTotal)
          .replace('水量2', item.tunnageTotal)
          .replace('清水单价1', item.price)
          .replace('污水单价1', item.sewageTotalFee.toFixed(2))
          .replace('操作员1', item.uname)
          .replace('水资源费2', item.sourceTotalFee.toFixed(2))
          .replace('制表人1', createName);
      eval(temp);
      const flag = LODOP.PRINT();
      if (flag == 1) {
        axios({
          method: 'post',
          url: `${url}revenue/billCopyInvoice/updatePrintStatus`,
          data: qs.stringify(values),
        }).then((response) => {
          if (response.data.code == '0') {
            if (key == selectedRecord.length - 1) {
              Feedback.toast.success('添加记录成功');
              this.setState({ printStatus: false });
              this.queryUnprint(1, 10);
            }
          } else {
            this.setState({ printStatus: false });
            Feedback.toast.error('添加记录失败');
          }
        }).catch((error) => {
          this.setState({ printStatus: false });
          Feedback.toast.error(`系统异常请稍后再试${error}`);
        });
      }
    });
  }

  queryTotal() {
    const values = this.field.getValues();
    values.printStatus = '0';
    axios({
      method: 'post',
      url: `${url}revenue/billCopyInvoice/total`,
      data: qs.stringify(values),
    })
        .then((response) => {
          if (response.data.code == '0') {
            const resultDates = response.data.datas;
            this.setState({
              totalHno: resultDates.totalHno,
              amountTotal: resultDates.amountTotal,
              balanceTotal: resultDates.balanceTotal,
              waterTotalFee: resultDates.waterTotalFee,
              sewageTotalFee: resultDates.sewageTotalFee,
              sourceTotalFee: resultDates.sourceTotalFee,
            });
          } else {
            Feedback.toast.error(response.data.msg);
          }
        })
        .catch((error) => {
          Feedback.toast.error(`请求错误：${error}`);
        });
  }

  render() {
    const {
      totalHno,
      amountTotal,
      balanceTotal,
      waterTotalFee,
      sourceTotalFee,
      sewageTotalFee,
      selectedRowKeys,
      formValue,
      page,
      totalSize,
      pageSize,
      dataLoading,
      roleNameList,
      areaList,
      regionList,
      feeNameList,
      printStatus
    } = this.state;
    const { init } = this.field;
    const formItemLayout = { labelCol: { fixedSpan: 4 } };

    return (
        <div>
          <IceContainer title="账单待打发票">
            <Form field={this.field}>
              <div style={{ display: 'flex', justifyContent: 'space-around' }}>
                <div style={{ display: 'flex', flexDirection: 'column' }}>
                  <FormItem label="用户户号：" {...formItemLayout}>
                    <Input {...init('hno')} placeholder="请输入" style={{ width: '160px' }} />
                  </FormItem>
                  <FormItem label="片区：" {...formItemLayout}>
                    <Select {...init('areaId')}
                            placeholder="请选择"
                            dataSource={areaList}
                            style={{ width: 160 }}
                    />
                  </FormItem>
                </div>
                <div style={{ display: 'flex', flexDirection: 'column' }}>
                  <FormItem label="用水性质：" {...formItemLayout}>
                    <Combobox
                        {...init('feeId')}
                        placeholder="--请选择或输入--"
                        fillProps="label"
                        hasClear
                        style={{ width: 170 }}
                        dataSource={feeNameList}
                    />
                  </FormItem>

                  <FormItem label="小区：" {...formItemLayout}>
                    <Combobox
                        {...init('regionId')}
                        placeholder="--请选择小区--"
                        fillProps="label"
                        hasClear
                        style={{ width: 170 }}
                        dataSource={regionList}
                    />
                  </FormItem>

                </div>

                <div style={{ display: 'flex', flexDirection: 'column' }}>
                  <FormItem {...formItemLayout} label="抄表员：">
                    <Combobox
                        {...init('uids')}
                        placeholder="--请选择--"
                        fillProps="label"
                        hasClear
                        style={{ width: 160 }}
                        dataSource={roleNameList}
                        multiple
                    />
                  </FormItem>
                  <FormItem label="账单类型：" {...formItemLayout}>
                    <Select {...init('type')}
                            dataSource={[
                              { label: '请选择', value: '' },
                              { label: '普通出账', value: '0' },
                              { label: '增收', value: '1' },
                            ]}
                            style={{ width: '160px' }}
                    />
                  </FormItem>
                </div>

              </div>
            </Form>

            <div align="center">
              <Button type="primary"
                      className="button"
                      onClick={() => this.queryUnprint(1, 10)}
                      style={{ marginRight: 30 }}
              >
                <Icon type="search" />查询
              </Button>
              <Button type="secondary" className="button" onClick={() => this.reset()}>
                <Icon type="refresh" />重置
              </Button>
            </div>

          </IceContainer>

          <IceContainer title="账单发票列表">

            <div style={{ marginBottom: 10 }}>
              <Button type="primary"
                      className="button"
                      style={{ marginRight: 10 }}
                      onClick={() => this.batchPrinting()}
                      loading={printStatus}
              >
                <Icon type="print" />
                批量开票
              </Button>
            </div>

            <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: 10 }}>
              <div>总户数：{totalHno || 0}户</div>
              <div style={{ marginLeft: 10 }}>未结总金额：{amountTotal || 0}元</div>
              <div style={{ marginLeft: 10 }}>抵扣总金额：{balanceTotal || 0}元</div>
              <div style={{ marginLeft: 10 }}>总清水费：{waterTotalFee || 0}元</div>
              <div style={{ marginLeft: 10 }}>总污水费：{sewageTotalFee || 0}元</div>
              <div style={{ marginLeft: 10 }}>总水资源税：{sourceTotalFee || 0}元</div>
            </div>

            <Table dataSource={formValue}
                   isLoading={dataLoading}
                   rowSelection={{ ...this.rowSelection, selectedRowKeys }}
                   primaryKey="id"
            >
              <Table.Column title="用户户号" dataIndex="hno" align="center"
                            width={200}/>
              <Table.Column title="用户名称" dataIndex="cnames" align="center"
                            width={320}/>
              <Table.Column title="片区" dataIndex="areaName" align="center"
                            width={100}/>
              <Table.Column title="区号" dataIndex="zoneCode" align="center" width={100} />
              <Table.Column title="小区" dataIndex="regionName" align="center" width={100} />
              <Table.Column title="抄表员" dataIndex="uname" align="center" width={160} />
              <Table.Column title="用水性质" dataIndex="feeName" align="center" width={200} />
              <Table.Column title="账单数量" dataIndex="billCount" align="center" width={100} />
              <Table.Column title="上期示数" dataIndex="lastNumTotal" align="center" width={100} />
              <Table.Column title="本期示数" dataIndex="thisNumTotal" align="center" width={100} />
              <Table.Column title="总水量" dataIndex="tunnageTotal" align="center" width={100} />
              <Table.Column title="未结金额" dataIndex="amountTotal" align="center" width={100} />
              <Table.Column title="余额抵扣金额" dataIndex="balance" align="center" width={120} />
              <Table.Column title="总清水费" dataIndex="waterTotalFee" align="center" width={100} />
              <Table.Column title="总污水费" dataIndex="sewageTotalFee" align="center" width={100} />
              <Table.Column title="总水资源税" dataIndex="sourceTotalFee" align="center" width={100} />
              <Table.Column title="账单类型" dataIndex="type" align="center" cell={value => (value == '0' ? '普通' : '增收')} width={100} />
              <Table.Column title="操作"
                            align="center"
                            cell={(value, index, record) => this.rowOptionRender(record)}
                            width={120}
                            lock="right"
              />
            </Table>

            <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Pagination
                  pageSizeSelector="dropdown"
                  onPageSizeChange={pageSize => this.changePageSize(pageSize)}
                  style={{ marginTop: 15 }}
                  pageSizeList={[100, 300, 500]}
                  current={page}
                  pageSize={pageSize}
                  total={totalSize}
                  size="small"
                  onChange={page => this.changePage(page)}
              />
              <div style={{ lineHeight: '58px', marginLeft: 10 }}>共{totalSize}条记录</div>
            </div>
          </IceContainer>

          <Dialog
              visible={this.state.modifyUserNameModal}
              onOk={this.modifyUserName}
              onCancel={this.onModifyUserNameModalClose}
              onClose={this.onModifyUserNameModalClose}
              title="修改用户名称"
              footerAlign="center"
          >
            <Form field={this.field}>
              <FormItem label="新用户名称：">
                <Input {...init('newUsername')} placeholder="请输入"  />
              </FormItem>
            </Form>
          </Dialog>
        </div>
    );
  }
}
