import React, {Component} from 'react';
import IceContainer from '@icedesign/container';
import {
  <PERSON><PERSON>,
  DatePicker,
  Feedback,
  Field,
  Form,
  Icon,
  Select
} from '@icedesign/base';
import axios from "axios/index"
import qs from 'qs'
import {url} from "../../../components/URL/index"

const FormItem = Form.Item
export default class PathMap extends Component {

  constructor(props) {
    super(props);
    this.state = {
      roleName: []
    }
    this.field = new Field(this, {autoUnmount: true})
  }

  componentDidMount() {
    let BMap = window.BMap;
    let map = new BMap.Map("allmap");// 创建Map实例
    map.centerAndZoom(new BMap.Point(122.2884502266, 43.6216810204), 14); // 初始化地图,设置中心点坐标和地图级别
    map.addControl(new BMap.MapTypeControl()); //添加地图类型控件
    map.setCurrentCity("通辽");// 设置地图显示的城市 此项是必须设置的
    map.enableScrollWheelZoom(true); //开启鼠标滚轮缩放
    this.queryRoleName()
  }

  //查询抄表员
  queryRoleName() {
    axios({
      method: 'post',
      url: `${url}revenue/staff/findByRoleName`,
      data: qs.stringify({roleName: '抄表员'})
    })
      .then((response) => {
        if (response.data.code == '0') {
          let roleNameList = []
          response.data.datas.map((item) => {
            roleNameList.push({label: item.realName, value: item.userId})
          })
          this.setState({roleNameList: roleNameList})
        } else {
          Feedback.toast.error(response.data.msg);
        }
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
      })
  }

  /*查询轨迹*/
  queryRoute() {
    this.field.validate((errors, values) => {
      if (errors) {
        return
      }
      else {
        axios({
          method: 'post',
          url: `${url}revenue/location/get`,
          data: qs.stringify(values),
        })
          .then((response) => {
            let json = response.data;
            let data = JSON.parse(json.datas);
            
            if (json.code == 0) {
              let BMap = window.BMap;
              var map = new BMap.Map("allmap");
              map.enableScrollWheelZoom();
              let points = data.points;
              if (points != null && points.length > 0) {
                let PointArr = [];
                for (let i = 0; i < points.length; i++) {
                  let point2 = new BMap.Point(points[i].longitude, points[i].latitude);
                  PointArr.push(point2);
                }
                var curve = new BMapLib.CurveLine(PointArr, {strokeColor: "blue", strokeWeight: 5, strokeOpacity: 1}); //创建弧线对象
                map.addOverlay(curve); //添加到地图中

                var options = {
                  size: BMAP_POINT_SIZE_BIG,
                  shape: BMAP_POINT_SHAPE_STAR,
                  color: '#d340c3'
                };
                var pointCollection = new BMap.PointCollection(PointArr, options);  // 初始化PointCollection
                map.addOverlay(pointCollection);  // 添加Overlay
                map.centerAndZoom(new BMap.Point(points[0].longitude, points[0].latitude), 15);
              }
              else {
                alert('该抄表员没有路线轨迹');
                map.centerAndZoom(new BMap.Point(122.249808, 43.659468), 15);
              }
            }
            else {
              alert('系统繁忙，请稍后重试')
            }
          })
          .catch((error) => {
            Feedback.toast.error("请求错误：" + error)
          });
      }
    })
  }

  /**
   * 输入用户名称
   */
  inputStaffName = (value) => {
    this.field.setValue('entityName', value);
  }

  datelineFm = (value, str) => {
    return str;
  }

  /*渲染角色*/
  renderRoleName() {
    const {roleName} = this.state
    return roleName && roleName.length > 0 ? roleName.map((item) => {
      return <Option value={item.userId}>{item.realName}</Option>
    }) : void(0)
  }

  //抄表员下拉
  onChange(value) {
    const {roleNameList} = this.state
    let newList = roleNameList.filter((item) => item.value == value)
    this.field.setValue('roleName', newList[0].label)
    this.field.setValue('roleId', value);
    this.field.setValue('entityName', value);
  }

  render() {
    const {init} = this.field
    const {roleNameList} = this.state
    return (
      <div>
        <IceContainer title="搜索">
          <Form direction="hoz" field={this.field} style={{display: 'flex', justifyContent: 'center'}}>
            <div>
              <FormItem label="抄表员：">
                <Select placeholder="--请选择--" {...init('roleId', {rules: [{required: true, message: '请选择抄表员'}]})}
                        style={{width: 160}} dataSource={roleNameList} onChange={(value) => this.onChange(value)}/>
              </FormItem>
              <FormItem label="时间：">
                <DatePicker {...init('dateline', {
                  rules: [{required: true, message: '时间必填'}],
                  getValueFromEvent: this.datelineFm
                })}/>
              </FormItem>
            </div>
            <Button type="primary" onClick={() => this.queryRoute()}><Icon type="search"/>查询</Button>
          </Form>
        </IceContainer>

        <IceContainer>
          <div id='allmap' style={{width: '100%', height: '75vh'}}/>
        </IceContainer>
      </div>
    )
  }
}
