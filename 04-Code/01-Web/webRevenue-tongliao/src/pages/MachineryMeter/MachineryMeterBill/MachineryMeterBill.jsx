import React, {Component} from 'react';
import {
  <PERSON><PERSON>,
  DatePicker,
  <PERSON><PERSON><PERSON>,
  Field,
  Form,
  Grid,
  Icon,
  Input,
  Select
} from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import qs from 'qs';
import {url} from '../../../components/URL/index';
import BillDetailInfo from './components/BillDetailInfo';
import BillInvalid from './components/BillInvalid';
import styles from './index.module.scss';
import {formItemLayout, span} from '../../../common/FormCollocation';
import {billStatus, billTypes} from '../../../common/UserCollocation';
import BasicsTable from '../../../common/BasicsTable';

const FormItem = Form.Item;
const {RangePicker} = DatePicker;
const {Combobox} = Select;
const {Row, Col} = Grid;

export default class MachineryMeterBill extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource: [],
            page: 1,
            pageSize: 10,
            total: 0,
            areaList: [], // 片区
            regionList: [], // 小区
            feeNameList: [], // 用水性质
            priceList: [], // 清水单价
            createNameList: [],
            roleNameList: [],
            dataLoading: false,
        };
        this.field = new Field(this, { autoUnmount: true });
    }

    componentDidMount() {
        let param = this.props.location.state;
        if (param !== undefined) {
            this.field.setValue('orderId', param.orderId);
            this.field.setValue('cno', param.cno);
        }
        // this.queryBillCopy(1, 10);
        this.queryRegion();
        this.queryArea();
        this.queryUname();
        this.queryCreateName();
        this.queryFeeName();
        this.queryPriceList();
        // this.queryBillTotal();
    }

    // 查询列表数据
    queryBillCopy(page, pageSize) {
        this.setState({ dataLoading: true });
        let values = this.field.getValues();
        values.page = page;
        values.pageSize = pageSize;
        delete values.queryBillTime;
        delete values.querySettleTime;
        axios
            .post(`${url}revenue/billCopy/query`, qs.stringify(values))
            .then((response) => {
                this.queryBillTotal(values);
                this.setState({
                    dataSource: response.data.datas,
                    page: response.data.page,
                    pageSize: response.data.pageSize,
                    total: response.data.totalSize,
                    dataLoading: false,
                });
            })
            .catch((error) => {
                Feedback.toast.error('网络错误请刷新页面重试');
                this.setState({ dataLoading: false });
            });
    }

    // 翻页
    changePage(page) {
        const { pageSize } = this.state;
        this.queryBillCopy(page, pageSize);
    }

    // 改变pageSize
    onPageSizeChange(pageSize) {
        this.queryBillCopy(1, pageSize);
    }

    // 查询总金额和总水量
    queryBillTotal(values) {
        axios
            .post(`${url}revenue/billCopy/billTotal`, qs.stringify(values))
            .then((response) => {
                this.setState({
                    amount: response.data.datas.amount,
                    freeTunnageTotal: response.data.datas.freeTunnageTotal,
                    tunnage: response.data.datas.tunnage,
                    waterTotalFee: response.data.datas.waterTotalFee,
                    sewageTotalFee: response.data.datas.sewageTotalFee,
                    sourceTotalFee: response.data.datas.sourceTotalFee,
                });
            })
            .catch((error) => {
                Feedback.toast.error('网络错误请刷新页面重试');
            });
    }

    // 查询用水性质
    queryFeeName() {
        axios.post(`${url}revenue/fee/queryList`).then((response) => {
            let feeNameList = [];
            response.data.datas.map((item) => {
                feeNameList.push({ label: item.name, value: item.id });
            });
            this.setState({ feeNameList: feeNameList });
        });
    }

    // 查询清水费单价
    queryPriceList() {
        axios.post(`${url}revenue/fee/priceList`).then((response) => {
            this.setState({ priceList: response.data.datas });
        });
    }

    // 时间onchang
    timeOnchange = (val, str) => {
        this.field.setValue('csTime', str[0]);
        this.field.setValue('ceTime', str[1]);
    };

    // 时间onchang
    settleTimeOnchange = (val, str) => {
        this.field.setValue('settlementStartTime', str[0]);
        this.field.setValue('settlementEndTime', str[1]);
    };

    // 重置
    reset() {
        this.field.reset();
    }

    // 查询抄表员
    queryUname() {
        axios.get(`${url}revenue/staff/getAllCname`).then((response) => {
            if (response.data.code == 0) {
                let roleNameList = [];
                response.data.datas.map((item) => {
                    roleNameList.push({ label: item.label, value: item.id });
                });
                this.setState({ roleNameList: roleNameList });
            }
        });
    }

    // 查询操作员
    queryCreateName() {
        axios.post(`${url}revenue/billCopy/createName`).then((response) => {
            if (response.data.code == 0) {
                let createNameList = [];
                response.data.datas.map((item) => {
                    createNameList.push({ label: item.createName, value: item.createId });
                });
                this.setState({ createNameList: createNameList });
            }
        });
    }

    // 查询片区
    queryArea() {
        axios.get(`${url}revenue/area/getAll`).then((response) => {
            let areaList = [];
            response.data.datas.map((item) => {
                areaList.push({ label: item.name, value: item.id });
            });
            this.setState({ areaList: areaList });
        });
    }

    // 查询小区
    queryRegion() {
        axios.post(`${url}revenue/region/regionlist`).then((response) => {
            let regionList = [];
            response.data.datas.map((item) => {
                regionList.push({ label: item.regionName, value: item.id });
            });
            this.setState({ regionList: regionList });
        });
    }

    // 校验输入的水量
    checkedInput = (rule, value, callback) => {
        if (value) {
            if (value.indexOf('.') == 1) {
                callback('只能输入正整数');
            } else {
                callback();
            }
        } else {
            callback();
        }
    };

    // 导出账单明细
    downloadFile() {
        let values = this.field.getValues();
        let url1 = `${url}/revenue/excel/exportBillCopyDetail?n=1`;
        if (values.cno) {
            url1 += '&cno=' + values.cno;
        }
        if (values.billStatus) {
            url1 += '&billStatus=' + values.billStatus;
        }
        if (values.hno) {
            url1 += '&hno=' + values.hno;
        }
        if (values.csTime) {
            url1 += '&csTime=' + values.csTime;
        }
        if (values.ceTime) {
            url1 += '&ceTime=' + values.ceTime;
        }
        if (values.cname) {
            url1 += '&cname=' + values.cname;
        }
        if (values.createId) {
            url1 += '&createId=' + values.createId;
        }
        if (values.regionId) {
            url1 += '&regionId=' + values.regionId;
        }
        if (values.type) {
            url1 += '&type=' + values.type;
        }
        if (values.areaId) {
            url1 += '&areaId=' + values.areaId;
        }
        if (values.tunnageS) {
            url1 += '&tunnageS=' + values.tunnageS;
        }
        if (values.tunnageE) {
            url1 += '&tunnageE=' + values.tunnageE;
        }
        if (values.waterFee) {
            url1 += '&waterFee=' + values.waterFee;
        }
        if (values.feeId) {
            url1 += '&feeId=' + values.feeId;
        }
        if (values.types) {
            url1 += '&types=' + values.types;
        }
        if (values.zoneCode) {
            url1 += '&zoneCode=' + values.zoneCode;
        }
        if (values.uids) {
            url1 += '&uids=' + values.uids;
        }
        if (values.settlementStartTime) {
            url1 += '&settlementStartTime=' + values.settlementStartTime;
        }
        if (values.settlementEndTime) {
            url1 += '&settlementEndTime=' + values.settlementEndTime;
        }
        if (values.freeTunnage) {
            url1 += '&freeTunnage=' + values.freeTunnage;
        }
        window.open(encodeURI(url1), 'about:blank');
    }

    // 改变pageSize
    onPageSizeChange(pageSize) {
        this.queryBillCopy(1, pageSize);
    }

    render() {
        const {
            dataSource,
            page,
            pageSize,
            total,
            amount,
            freeTunnageTotal,
            tunnage,
            waterTotalFee,
            sewageTotalFee,
            sourceTotalFee,
            dataLoading,
            roleNameList,
            areaList,
            regionList,
            feeNameList,
            priceList,
            createNameList,
        } = this.state;
        const { init } = this.field;
        const columns = [
            {
                title: '用户编号',
                dataIndex: 'cno',
                key: 'cno',
            },
            {
                title: '用户户号',
                dataIndex: 'hno',
                key: 'hno',
            },
            {
                title: '用户名称',
                dataIndex: 'cname',
                key: 'cname',
            },
            {
                title: '用水性质',
                dataIndex: 'feeName',
                key: 'feeName',
            },
            {
                title: '片区',
                dataIndex: 'areaName',
                key: 'areaName',
            },
            {
                title: '小区',
                dataIndex: 'regionName',
                key: 'regionName',
            },
            {
                title: '出账日期',
                dataIndex: 'createTime',
                key: 'createTime',
            },
            {
                title: '抄表员',
                dataIndex: 'uname',
                key: 'uname',
            },
            {
                title: '用水量',
                dataIndex: 'tunnage',
                key: 'tunnage',
            },
            {
                title: '减免吨数',
                dataIndex: 'freeTunnage',
                key: 'freeTunnage',
            },
            {
                title: '账单金额',
                dataIndex: 'amount',
                key: 'amount',
            },
            {
                title: '未结金额',
                dataIndex: 'unpaid',
                key: 'unpaid',
            },
            {
                title: '账单类型',
                dataIndex: 'type',
                key: 'type',
                cell: (value) => {
                    if (value === '0') {
                        return <span>app出账</span>;
                    } else if (value === '1') {
                        return <span>增收出账</span>;
                    } else if (value === '2') {
                        return <span>web出账</span>;
                    } else if (value === '3') {
                        return <span>excel出账</span>;
                    } else if (value === '4') {
                        return <span>手动出账</span>;
                    } else if (value === '5') {
                        return <span>换表出账</span>;
                    } else if (value === '6') {
                        return <span>定量用户出账</span>;
                    } else if (value === '7') {
                        return <span>销户出账</span>;
                    }
                },
            },
            {
                title: '账单状态',
                dataIndex: 'billStatus',
                key: 'billStatus',
                cell: (value) => {
                    if (value === '1') {
                        return <span style={{ color: '#1DC11D' }}>已结清</span>;
                    } else if (value === '0') {
                        return <span style={{ color: '#ff0000' }}>未结清</span>;
                    } else {
                        return <span style={{ color: '#ff0000' }}>作废</span>;
                    }
                },
            },
            {
                title: '结清时间',
                dataIndex: 'settlementTime',
                key: 'settlementTime',
            },
            {
                title: '账单创建人',
                dataIndex: 'createName',
                key: 'createName',
            },
            {
                title: '操作',
                width: 80,
                cell: (value, index, record) => {
                    const { page, pageSize } = this.state;
                    return (
                        <div className="operation">
                            <BillDetailInfo record={record} />
                            {record.billStatus === '2' ? (
                                <Icon title="不可作废" type="ashbin" size="small" style={{ color: '#A9A9A9' }} />
                            ) : (
                                <BillInvalid record={record} queryBillCopy={() => this.queryBillCopy(page, pageSize)} />
                            )}
                        </div>
                    );
                },
            },
        ];
        return (
            <>
                <IceContainer title="机械表账单管理">
                    <Form field={this.field}>
                        <Row wrap>
                            <Col {...span}>
                                <FormItem label="用户编号：" {...formItemLayout}>
                                    <Input {...init('cno')} placeholder="请输入用户编号" style={{ width: 220 }} />
                                </FormItem>
                            </Col>

                            <Col {...span}>
                                <FormItem label="用户名称：" {...formItemLayout}>
                                    <Input {...init('cname')} placeholder="请输入用户名称" style={{ width: 220 }} />
                                </FormItem>
                            </Col>

                            <Col {...span}>
                                <FormItem label="用户户号：" {...formItemLayout}>
                                    <Input {...init('hno')} placeholder="请输入用户编号" style={{ width: 220 }} />
                                </FormItem>
                            </Col>

                            <Col {...span}>
                                <FormItem label="片区：" {...formItemLayout}>
                                    <Select
                                        {...init('areaId')}
                                        dataSource={areaList}
                                        placeholder="请选择片区"
                                        style={{ width: 220 }}
                                    />
                                </FormItem>
                            </Col>

                            <Col {...span}>
                                <FormItem label="小区：" {...formItemLayout}>
                                    <Combobox
                                        {...init('regionId')}
                                        dataSource={regionList}
                                        fillProps="label"
                                        hasClear
                                        placeholder="请选择小区"
                                        style={{ width: 220 }}
                                    />
                                </FormItem>
                            </Col>

                            <Col {...span}>
                                <FormItem label="抄表员：" {...formItemLayout}>
                                    <Combobox
                                        {...init('uids')}
                                        dataSource={roleNameList}
                                        multiple
                                        fillProps="label"
                                        hasClear
                                        placeholder="请选择或输入抄表员"
                                        style={{ width: 220 }}
                                    />
                                </FormItem>
                            </Col>

                            <Col {...span}>
                                <FormItem label="账单状态：" {...formItemLayout}>
                                    <Select
                                        {...init('billStatus')}
                                        dataSource={billStatus}
                                        placeholder="请选择账单状态"
                                        style={{ width: 220 }}
                                    />
                                </FormItem>
                            </Col>

                            <Col {...span}>
                                <FormItem label="账单类型：" {...formItemLayout}>
                                    <Select
                                        {...init('types')}
                                        dataSource={billTypes}
                                        multiple
                                        placeholder="请选账单类型"
                                        style={{ width: 220 }}
                                    />
                                </FormItem>
                            </Col>

                            <Col {...span}>
                                <FormItem label="账单创建人：" {...formItemLayout}>
                                    <Combobox
                                        {...init('createId')}
                                        dataSource={createNameList}
                                        fillProps="label"
                                        hasClear
                                        placeholder="请选择或输入抄表员"
                                        style={{ width: 220 }}
                                    />
                                </FormItem>
                            </Col>

                            <Col {...span}>
                                <FormItem label="用水性质：" {...formItemLayout}>
                                    <Combobox
                                        {...init('feeId')}
                                        dataSource={feeNameList}
                                        placeholder="请选择或输入用水性质"
                                        fillProps="label"
                                        hasClear
                                        style={{ width: 220 }}
                                    />
                                </FormItem>
                            </Col>

                            <Col {...span}>
                                <FormItem label="水量范围：" {...formItemLayout}>
                                    <Input
                                        {...init('tunnageS', { rules: [{ validator: this.checkedInput }] })}
                                        style={{ width: 105 }}
                                    />
                                    <span>&nbsp;—&nbsp;</span>
                                    <Input
                                        {...init('tunnageE', { rules: [{ validator: this.checkedInput }] })}
                                        style={{ width: 105 }}
                                    />
                                </FormItem>
                            </Col>

                            <Col {...span}>
                                <FormItem label="清水单价：" {...formItemLayout}>
                                    <Combobox
                                        {...init('waterFee')}
                                        dataSource={priceList}
                                        fillProps="label"
                                        hasClear
                                        placeholder="请选则或输入清水单价"
                                        style={{ width: 220 }}
                                    />
                                </FormItem>
                            </Col>

                            <Col {...span}>
                                <FormItem label="区号：" {...formItemLayout}>
                                    <Input {...init('zoneCode')} placeholder="请输入区号" style={{ width: 220 }} />
                                </FormItem>
                            </Col>

                            <Col {...span}>
                                <FormItem label="出账日期：" {...formItemLayout}>
                                    <RangePicker
                                        {...init('queryBillTime', {
                                            props: {
                                                onChange: (val, str) => {
                                                    this.timeOnchange(val, str);
                                                },
                                            },
                                        })}
                                        style={{ with: 220 }}
                                    />
                                </FormItem>
                            </Col>

                            <Col {...span}>
                                <FormItem label="结清时间：" {...formItemLayout}>
                                    <RangePicker
                                        {...init('querySettleTime', {
                                            props: {
                                                onChange: (val, str) => {
                                                    this.settleTimeOnchange(val, str);
                                                },
                                            },
                                        })}
                                        style={{ with: 220 }}
                                    />
                                </FormItem>
                            </Col>

                            <Col {...span}>
                                <FormItem label="订单编号：" {...formItemLayout}>
                                    <Input {...init('orderId')} readOnly style={{ width: 220 }} />
                                </FormItem>
                            </Col>

                            <Col {...span}>
                                <FormItem label="是否减免：" {...formItemLayout}>
                                    <Select {...init('freeTunnage')} placeholder="请选择" style={{ width: 220 }}>
                                        <option value="1">是</option>
                                        <option value="0">否</option>
                                    </Select>
                                </FormItem>
                            </Col>
                        </Row>
                    </Form>

                    <div align="center">
                        <Button type="primary" onClick={() => this.queryBillCopy(1, 10)} className={styles.btn}>
                            <Icon type="search" />
                            查询
                        </Button>
                        <Button type="secondary" onClick={() => this.reset()} className={styles.btn}>
                            <Icon type="refresh" />
                            重置
                        </Button>
                    </div>
                </IceContainer>

                <IceContainer title="用户列表">
                    <div style={{ marginBottom: 10 }}>
                        <Button type="primary" className="button" onClick={() => this.downloadFile()}>
                            <Icon type="download" />
                            导出账单明细
                        </Button>
                    </div>

                    <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: 10 }}>
                        <div>总金额：{amount ? amount : 0}元</div>
                        <div style={{ marginLeft: 10 }}>总减免吨数：{freeTunnageTotal ? freeTunnageTotal : 0}吨</div>
                        <div style={{ marginLeft: 10 }}>总用水量：{tunnage ? tunnage : 0}吨</div>
                        <div style={{ marginLeft: 10 }}>清水费：{waterTotalFee ? waterTotalFee : 0}元</div>
                        <div style={{ marginLeft: 10 }}>污水费：{sewageTotalFee ? sewageTotalFee : 0}元</div>
                        <div style={{ marginLeft: 10 }}>不征税自来水：{sourceTotalFee ? sourceTotalFee : 0}元</div>
                    </div>

                    <BasicsTable
                        columns={columns}
                        dataSource={dataSource}
                        total={total}
                        pageSize={pageSize}
                        page={page}
                        dataLoading={dataLoading}
                        changePage={(value) => this.changePage(value)}
                        onPageSizeChange={(value) => this.onPageSizeChange(value)}
                        pageSizeList={[50, 100, 200]}
                    />
                </IceContainer>
            </>
        );
    }
}
