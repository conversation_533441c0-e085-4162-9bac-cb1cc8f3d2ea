/* eslint-disable react/no-unused-state */
import React, {Component} from 'react'
import {
  <PERSON><PERSON>,
  DatePicker,
  <PERSON><PERSON>back,
  Field,
  Form,
  Grid,
  Icon,
  Input,
  moment,
  Pagination,
  Select,
  Table
} from '@icedesign/base'
import IceContainer from '@icedesign/container'
import axios from 'axios'
import qs from 'qs'
import {url} from '../../../components/URL'
import BillDetailInfo from './components/BillDetailInfo';
import BillInvalid from './components/BillInvalid'
import styles from '../../Watermeter/WatermeterSearch/index.module.scss';
import {remoteWaterMeterCompany} from '../../../common/WaterMeterCollocation';

const FormItem = Form.Item;
const { Row } = Grid;
const { RangePicker } = DatePicker;
const { Combobox } = Select;

export default class RemoteMeterBill extends Component {

  constructor(props) {
    super(props);
    this.rowSelection = {
      onChange: (ids, records) => {
        this.setState({
          selectedRowKeys: ids,
          selectedRecords: records,
        })
      },
      getProps: (record) => {
        return {
          disabled: record.出账状态 == '失败',
        };
      },
    };
    this.state = {
      page: 1,
      pageSize: 10,
      total: 0,
      dataLoading: false,
      dataSource: [],
      selectedRowKeys: [],
      selectedRecords: [],
      areaList: [],
      regionList: [],
    }
    this.field = new Field(this, { autoUnmount: true })
  }

  componentWillMount() {
    let param = this.props.location.state;
    if (param != null) {
      this.field.setValue('cno', param.cno);
    }
    this.queryArea();
    this.queryRegion();
  }

  //查询片区
  queryArea = () => {
    axios({
      method: 'get',
      url: url + 'revenue/area/getAll'
    })
      .then((response) => {
        let areaList = []
        response.data.datas.map((item) => {
          areaList.push({ label: item.name, value: item.id })
        })
        this.setState({ areaList: areaList })
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
      })
  }

  //查询小区
  queryRegion = () => {
    axios({
      method: 'post',
      url: `${url}revenue/region/regionlist`,
    }).then(response => {
      let regionList = []
      response.data.datas.map((item) => {
        regionList.push({ label: item.regionName, value: item.id })
      })
      this.setState({ regionList: regionList })

    }).catch(error => {
      Feedback.toast.error("请求异常", error);
    })
  }

  refreshTable = (page, pageSize) => {
    this.setState({ dataLoading: true });
    let values = this.field.getValues();
    values.page = page;
    values.pageSize = pageSize;
    delete values.queryExpensesTime;
    axios.post(`${url}revenue/bill/queryTeleteter`, qs.stringify(values))
      .then((response) => {
        this.setState({ dataLoading: false });
        let jsondata = response.data;
        this.setState({
          dataSource: jsondata.datas,
          page: page,
          pageSize: pageSize,
          total: jsondata.totalSize,
          selectedRowKeys: [],
          selectedRecords: [],
        });
      }).catch((error) => {
        this.setState({ dataLoading: false });
        Feedback.toast.error('axios请求异常:' + error);
      });
  };

  //查询
  doSearch = () => {
    const { pageSize } = this.state;
    this.refreshTable(1, pageSize);
  }

  //重置
  reset = () => {
    this.field.reset();
  }

  //翻页
  changePage = (page) => {
    const { pageSize } = this.state;
    this.refreshTable(page, pageSize);
  }

  //改变pageSize
  onPageSizeChange = (pageSize) => {
    this.refreshTable(1, pageSize);
  }

  /*表格操作栏*/
  renderOper = (value, index, record) => {
    if (record.status == 0) {
      return (
        <a title="出账" onClick={() => this.outBill(value, index, record)}>
          <Icon type="store" style={{ color: '#3399ff', cursor: 'pointer' }} />
        </a>
      )
    } else {
      return (
        <div style={{ display: 'flex', justifyContent: 'space-around' }}>
          <BillDetailInfo record={record} />

          {
            record.billStatus != 2 ? <BillInvalid record={record} refreshTable={this.doSearch} /> :
              void (0)
          }

        </div>
      )
    }
  }

  rowIndex = (value, index) => {
    const { pageSize, page } = this.state;
    if (page == 1) {
      return index + 1;
    } else {
      return (index + 1) + (page - 1) * pageSize;
    }
  }

  /**
   * 出账状态Fm
   */
  outBillFm = (value) => {
    if (value == 1) {
      return <span style={{ color: '#1DC11D' }}>出账成功</span>;
    } else if (value == 0) {
      return <span style={{ color: '#ff3333' }}>出账失败</span>
    } else if (value == 2) {
      return <span style={{ color: '#ff3333' }}>账单作废</span>
    } else if (value == 3) {
      return <span style={{color: '#ff3333'}}>出账失败-水表未上线</span>
    } else if (value == 4) {
      return <span style={{ color: '#ff3333' }}>出账失败-本期示数小于上期</span>
    } else if (value == 5) {
      return <span style={{ color: '#ff3333' }}>出账失败-水量大于历史账单</span>
    } else if (value == 6) {
      return <span style={{ color: '#ff3333' }}>出账失败-水量过大</span>
    } else if (value == 7) {
      return <span style={{ color: '#ff3333' }}>本期读数异常（1111/9999）</span>
    }
  }

  /**
   * 账单状态Fm
   */
  billState = (value, index, record) => {
    if (record.status == 0) {
      return;
    }
    if (record.billStatus == 1) {
      return <span style={{ color: '#1DC11D' }}>已结清</span>;
    } else if (record.billStatus == 0) {
      return <span style={{ color: '#ff3333' }}>未结清</span>;
    } else if (record.billStatus == 2) {
      return <span style={{ color: '#ff3333' }}>作废</span>;
    }
  }

  /**
   * 单个出账
   */
  outBill = (value, index, record) => {
    let values = {};
    values.id = record.id;
    this.doOutBillAxios(values);
  }

  /**
   * 批量出账
   */
  batchOutBill = () => {
    const { selectedRecords } = this.state;
    if (selectedRecords.length == 0) {
      alert('至少选择一条记录');
      return
    }
    let ids = '';
    selectedRecords.map(item => {
      ids += item.id + ',';
    })
    ids = ids.substring(0, ids.length - 1);
    let values = {};
    values.id = ids;
    this.doOutBillAxios(values);
  }

  /**
   * 出账调接口
   */
  doOutBillAxios = (values) => {
    axios({
      method: 'post',
      url: `${url}revenue/bill/account`,
      data: qs.stringify(values),
    })
      .then((response) => {
        this.setState({
          dataLoading: false,
        });
        let jsondata = response.data;
        if (jsondata.code == 0) {
          alert('出账成功');
          this.refreshTable(1, 10);
        } else {
          alert(jsondata.msg);
        }
      })
      .catch((error) => {
        this.setState({
          dataLoading: false,
        });
        Feedback.toast.error('axios请求异常:' + error);
      });
  }

  /**
   * 时间格式Fm
   */
  dataFm = (value, index, record) => {
    if (value) {
      return moment(value).format('YYYY-MM-DD');
    }
  }

  /**
   * 选择时间区间Onchange
   */
  timeOnchange = (value, str) => {
    this.field.setValue('csTime', str[0]);
    this.field.setValue('ceTime', str[1]);
  }

  //导出账单
  downLoadBill() {
    let values = this.field.getValues();
    let url1 = `${url}revenue/excel/exportTeleteterBill?n=1`;
    if (values.cno) {
      url1 += '&cno=' + values.cno;
    }
    if (values.watermeterId) {
      url1 += '&watermeterId=' + values.watermeterId;
    }
    if (values.billStatus) {
      url1 += '&billStatus=' + values.billStatus;
    }
    if (values.watermeterCompany) {
      url1 += '&watermeterCompany=' + encodeURI(values.watermeterCompany);
    }
    if (values.statuses) {
      url1 += '&statuses=' + values.statuses;
    }
    if (values.status) {
      url1 += '&status=' + values.status;
    }
    if (values.csTime) {
      url1 += '&csTime=' + values.csTime;
    }
    if (values.ceTime) {
      url1 += '&ceTime=' + values.ceTime;
    }
    if (values.areaId) {
      url1 += '&areaId=' + values.areaId;
    }
    if (values.regionId) {
      url1 += '&regionId=' + values.regionId;
    }
    if (values.cname) {
      url1 += '&cname=' + encodeURIComponent(values.cname);
    }
    if (values.tunnageS) {
      url1 += '&tunnageS=' + values.tunnageS;
    }
    if (values.tunnageE) {
      url1 += '&tunnageE=' + values.tunnageE;
    }
    if (values.address) {
      url1 += '&address=' + values.address;
    }
    window.open(url1, 'about:blank');
  }

  checkedInput = (rule, value, callback) => {
    if (value) {
      if (value.indexOf(".") == 1) {
        callback("只能输入正整数");
      } else {
        callback();
      }
    } else {
      callback();
    }
  }

  render() {
    const { dataSource, page, pageSize, total, dataLoading, selectedRowKeys, regionList, areaList } = this.state
    const { init } = this.field
    const formItemLayout = {
      labelCol: { fixedSpan: 6 }
    }
    const formItemLayout1 = {
      labelCol: { fixedSpan: 14 }
    }
    return (
      <div>
        <IceContainer title="远传表账单">
          <Form field={this.field}>
            <div style={{ display: 'flex', justifyContent: 'space-around' }}>

              <div style={{ display: 'flex', flexDirection: 'column' }}>
                <FormItem label="水表编号：" {...formItemLayout}>
                  <Input {...init('watermeterId')} placeholder="请输入" style={{ width: '160px' }} />
                </FormItem>
                <FormItem label="水表厂家：" {...formItemLayout}>
                  <Select {...init('watermeterCompany')}
                    dataSource={remoteWaterMeterCompany}
                    placeholder="请选择水表厂家" className={styles.selectWidth} />
                </FormItem>
                <FormItem label="账单状态：" {...formItemLayout}>
                  <Select {...init('billStatus')}
                    dataSource={[
                      { label: '请选择', value: '' },
                      { label: '已结清', value: '1' },
                      { label: '未结清', value: '0' },
                      { label: '作废', value: '2' }
                    ]}
                    style={{ width: '160px' }}
                  >
                  </Select>
                </FormItem>
                <FormItem label="用水量范围：" {...formItemLayout}>
                  <Input {...init('tunnageS',
                    { rules: [{ validator: this.checkedInput }] })} htmlType="number" style={{ width: 90 }} />
                  <span>—</span>
                  <Input {...init('tunnageE')} htmlType="number" style={{ width: 85 }} />
                </FormItem>
              </div>

              <div style={{ display: 'flex', flexDirection: 'column' }}>
                <FormItem label="用户编号：" {...formItemLayout}>
                  <Input {...init('cno')} placeholder="请输入" style={{ width: '160px' }} />
                </FormItem>
                <FormItem label="片&emsp;&emsp;区："{...formItemLayout}>
                  <Select {...init('areaId')} dataSource={areaList} placeholder="请输入" style={{ width: 160 }} />
                </FormItem>
                <FormItem label="出账日期：" {...formItemLayout}>
                  <RangePicker
                    {...init('queryExpensesTime', {
                      props: { onChange: (v, s) => this.timeOnchange(v, s) }
                    })}
                  />
                </FormItem>
                <FormItem label="抵扣吨数：" {...formItemLayout}>
                  <Select {...init('remainType')}
                          dataSource={[
                            { label: '请选择', value: '' },
                            { label: '是', value: '1' },
                            { label: '否', value: '0' }
                          ]}
                          style={{ width: '160px' }}
                  >
                  </Select>
                </FormItem>
              </div>

              <div style={{ display: 'flex', flexDirection: 'column' }}>
                <FormItem label="出账状态：" {...formItemLayout}>
                  <Select {...init('statuses')}
                    dataSource={[
                      {label: '请选择', value: ''},
                      {label: '出账失败', value: '0'},
                      {label: '出账成功', value: '1'},
                      {label: '账单作废', value: '2'},
                      {label: '出账失败-水表未上线', value: '3'},
                      {label: '出账失败-本期示数小于上期', value: '4'},
                      {label: '出账失败-水量大于历史账单', value: '5'},
                      {label: '出账失败-水量过大', value: '6'},
                      {label: '出账失败-本期读数异常（1111/9999）', value: '7'}
                    ]}
                    style={{ width: '240px' }} multiple
                  >
                  </Select>
                </FormItem>
                <FormItem label="小&emsp;&emsp;区：" {...formItemLayout}>
                  <Combobox
                    {...init('regionId')}
                    dataSource={regionList}
                    placeholder="请输入"
                    style={{ width: 160 }}
                    fillProps="label"
                    hasClear
                  />
                </FormItem>
                <FormItem label="用户地址：" {...formItemLayout}>
                  <Input {...init('address')} placeholder="请输入" style={{ width: '160px' }} />
                </FormItem>
              </div>

            </div>
          </Form>

          <div align="center">
            <Button type="primary" className="button" onClick={() => this.doSearch()} style={{ marginRight: 30 }}>
              <Icon type="search" />查询
            </Button>
            <Button type="secondary" className="button" onClick={() => this.reset()}>
              <Icon type="refresh" />重置
            </Button>
          </div>
        </IceContainer>

        <IceContainer title="账单列表">
          <div style={{ marginBottom: 10 }}>
            <Button className="button" type="primary" onClick={this.batchOutBill}>
              <Icon type="store" style={{ color: "#ffffff" }} />
              批量出账
            </Button>

            <Button className="button" type="primary" onClick={() => this.downLoadBill()} style={{ marginLeft: 10 }}>
              <Icon type="download" style={{ color: "#ffffff" }} />
              导出账单
            </Button>
          </div>

          <Table dataSource={dataSource} isLoading={dataLoading}
            rowSelection={{
              ...this.rowSelection,
              selectedRowKeys: selectedRowKeys,
            }}
          >
            <Table.Column title="用户编号" dataIndex="cno" align="center" />
            <Table.Column title="水表编号" dataIndex="watermeterId" align="center" />
            <Table.Column title="水表地址" dataIndex="address" align="center" />
            <Table.Column title="水表厂家" dataIndex="watermeterCompany"
              align="center" />
            <Table.Column title="片区" dataIndex="areaName" align="center" />
            <Table.Column title="小区" dataIndex="regionName" align="center" />
            <Table.Column title="出账日期" dataIndex="createTime" align="center"
              cell={this.dataFm} />
            <Table.Column title="抄表日期" dataIndex="thisUpdateTime" align="center"
              cell={this.dataFm} />
            <Table.Column title="上期示数" dataIndex="lastNum" align="center" />
            <Table.Column title="本期示数" dataIndex="thisNum" align="center" />
            <Table.Column title="用水量" dataIndex="tunnage" align="center" />
            <Table.Column title="抵扣吨数" dataIndex="remainTunnage" align="center" />
            <Table.Column title="账单金额" dataIndex="amount" align="center" />
            <Table.Column title="出账状态" dataIndex="status" align="center" />
            <Table.Column title="出账操作员" dataIndex="createName" align="center" />
            <Table.Column title="出账方式" dataIndex="type" align="center"
              cell={(value) => value == 0 ? '手动生成' : '系统生成'} />
            <Table.Column title="账单状态" dataIndex="billStatus" align="center"
              cell={this.billState} />
            <Table.Column title="操作" cell={this.renderOper} align='center' />
          </Table>
          <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Pagination
              style={{ textAlign: 'right', marginTop: 15 }}
              pageSizeSelector="dropdown"
              onChange={this.changePage}
              onPageSizeChange={this.onPageSizeChange}
              total={total}
              pageSize={pageSize}
              current={page}
              size="small"
              pageSizeList={[10, 30, 50, 100]}
            />
            <div style={{ lineHeight: '58px', marginLeft: 10 }}>共 {total} 条记录</div>
          </div>
        </IceContainer>

      </div>
    )
  }
}
