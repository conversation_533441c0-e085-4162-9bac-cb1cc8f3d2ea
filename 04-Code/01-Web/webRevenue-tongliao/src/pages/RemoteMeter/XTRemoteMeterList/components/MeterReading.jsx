import React, {Component} from 'react';
import {Balloon, Button, Feedback, Icon} from '@icedesign/base';
import axios from 'axios';
import {url} from '../../../../components/URL/index';
import qs from 'qs';

export default class MeterReading extends Component {
    constructor(props) {
        super(props);
        this.state = {
            visible: false,
            stuffId: sessionStorage.getItem('stuffId'),
            stuffName: sessionStorage.getItem('realName'),
        };
    }

    handleHide() {
        this.setState({ visible: false });
    }

    handleVisible() {
        this.setState({ visible: true });
    }

    handleOk(record) {
        let values = {};
        values.watermeterId = record.watermeterId;
        values.cid = record.cid;
        values.watermeterCompany = '河南新天';
        axios
            .post(`${url}revenue/thirdPartyRemoteWatermeter/singlePolling`, qs.stringify(values))
            .then((response) => {
                let jsondata = response.data;
                if (jsondata.code === '0') {
                    Feedback.toast.success('抄表成功');
                    this.props.refreshTable();
                } else {
                    alert(jsondata.msg);
                }
            })
            .catch((error) => {
                Feedback.toast.error('网络错误请刷新后重试');
            });
        this.setState({ visible: false });
    }

    render() {
        const { record } = this.props;
        const openTrigger = (
            <a title="抄表">
                <Icon type="process" size="small" style={{ cursor: 'pointer', color: '#1DC11D' }} />
            </a>
        );
        const content = (
            <div>
                <div style={styles.contentText}>是否对编号为:{record.watermeterId}抄表?</div>
                <Button
                    id="confirmBtn"
                    size="small"
                    type="normal"
                    shape="warning"
                    style={{ marginRight: '5px' }}
                    onClick={() => this.handleOk(record)}
                >
                    确认
                </Button>
                <Button id="cancelBtn" size="small" onClick={() => this.handleHide()}>
                    关闭
                </Button>
            </div>
        );

        return (
            <Balloon
                trigger={openTrigger}
                triggerType="click"
                visible={this.state.visible}
                onVisibleChange={() => this.handleVisible()}
            >
                {content}
            </Balloon>
        );
    }
}

const styles = {
    contentText: {
        padding: '5px 0 15px',
    },
};
