import React, {Component} from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Field,
  Form,
  Grid,
  Icon,
  Input,
  Pagination,
  Select,
  Table,
} from '@icedesign/base'
import IceContainer from '@icedesign/container'
import axios from 'axios'
import qs from 'qs'
import {url} from '../../../components/URL/index'
import {Link} from 'react-router-dom';

const FormItem = Form.Item
const {Row} = Grid;

export default class MSConcentratorTable extends Component {

  constructor(props) {
    super(props);
    this.state = {
      page: 1,
      pageSize: 10,
      total: 0,
      dataLoading: false,
      dataSource: [],
      selectedRowKeys: [],
      selectedRecords: [],
      searchAll: false,
      getReaders: false,
      stuffId: sessionStorage.getItem("stuffId"),
      createName: sessionStorage.getItem("createName"),
    }
    this.field = new Field(this, {autoUnmount: true});
    this.rowSelection = {
      onChange: (ids, records) => {
        this.setState({
          selectedRowKeys: ids,
          selectedRecords: records,
        })
      },
      getProps: (record) => {
        return {
          disabled: record.useState != '02'
        }
      }
    }
  }

  componentWillMount() {
    let param = this.props.location.state;
    if (param != null) {
      this.field.setValues({...param.searchValue})
    }
    this.refreshTable(1, 10);
  }

  refreshTable = (page, pageSize) => {
    this.setState({
      dataLoading: true,
      selectedRowKeys: [],
      selectedRecords: [],
    });
    let values = this.field.getValues();
    values.page = page;
    values.pageSize = pageSize;
    axios({
      method: 'post',
      url: `${url}revenue/minsheng/concentrator/query`,
      data: qs.stringify(values),
    })
      .then((response) => {
        this.setState({
          dataLoading: false,
        });
        let jsondata = response.data;
        this.setState({
          dataSource: jsondata.datas,
          page: page,
          pageSize: pageSize,
          total: jsondata.totalSize,
        });
      })
      .catch((error) => {
        this.setState({
          dataLoading: false,
        });
        Feedback.toast.error('axios请求异常:' + error);
      });
  };

  //查询
  doSearch = () => {
    const {pageSize} = this.state;
    this.refreshTable(1, pageSize);
  }

  //翻页
  changePage = (page) => {
    const {pageSize} = this.state;
    this.refreshTable(page, pageSize);
  }

  //改变pageSize
  onPageSizeChange = (pageSize) => {
    this.refreshTable(1, pageSize);
  }

  //重置
  reset = () => {
    this.field.reset();
  }

  //查询全部集中器状态
  getAllState = () => {
    this.setState({
      searchAll: true,
    });
    axios({
      method: 'post',
      url: `${url}revenue/concentrator/updateConcentratorStatus`,
    })
      .then((response) => {
        this.setState({
          searchAll: false,
        });
        let jsondata = response.data;
        if (jsondata.code == 0) {
          Feedback.toast.success('集中器状态查询成功');
          this.refreshTable(1, 10);
        }
        else {
          alert(jsondata.msg);
        }
      })
      .catch((error) => {
        this.setState({
          searchAll: false,
        });
        Feedback.toast.error('axios请求异常:' + error);
      });
  }

  //轮询
  getNewReaders = (record) => {
    this.setState({
      getReaders: true,
    })
    const {selectedRecords} = this.state;
    if (selectedRecords.length == 0) {
      alert('至少选择一条记录');
      return;
    }
    let values = {};
    values.concentrators = selectedRecords;
    axios({
      method: 'post',
      url: `${url}revenue/concentrator/polling`,
      data: values,
    })
      .then((response) => {
        this.setState({
          getReaders: false,
        })
        let jsondata = response.data;
        if (jsondata.code == 0) {
          alert(jsondata.datas.str);
        }
        else {
          alert(jsondata.msg);
        }
      })
      .catch((error) => {
        this.setState({
          getReaders: false,
        });
        Feedback.toast.error('axios请求异常:' + error);
      });
  }

  /*表格操作栏*/
  renderOper = (value, index, record) => {
    return (
      <span style={{display: "flex", justifyContent: "space-around"}}>
              {
                <Link to={{
                  pathname: './MSconcentrator/MSwatermeter',
                  state: {record: record, searchValue: this.field.getValues()}
                }}>
                  <a>
                    <Icon title="查看挂表" type="browse" style={{color: "#1DC11D", cursor: "pointer"}}/>
                  </a>
                </Link>
              }
        {
          record.useState == '1' ?
            <a onClick={() => this.getNewReaders(record)} title="轮询">
              <Icon type="compass" style={{color: "#1DC11D", cursor: "pointer"}}/>
            </a>
            :
            void(0)
        }
            </span>
    )
  }

  rowIndex = (value, index) => {
    const {pageSize, page} = this.state;
    if (page == 1) {
      return index + 1;
    }
    else {
      return (index + 1) + (page - 1) * pageSize;
    }
  }

  useStateFm = (value) => {
    if (value == '00') {
      return '离线';
    }
    else if (value == '01') {
      return '正在工作';
    }
    else if (value == '02') {
      return '在线';
    }
  }

  //批量关阀
  batchClose() {
    const {selectedRecords, stuffId, createName} = this.state
    let concentrators = []
    selectedRecords.map((itme) => {
      concentrators.push({cid: itme.cid, createId: stuffId, createName: createName})
    })
    if (selectedRecords.length > 0) {
      axios({
        method: 'post',
        url: `${url}revenue/minsheng/concentrator/valveBatch`,
        data: {concentrators: concentrators},
      })
        .then((response) => {
          if (response.data.code == '0') {
            Feedback.toast.success('批量关阀成功');
          }
        })
        .catch((error) => {
          Feedback.toast.error('系统正忙请稍后再试');
        });
    } else {
      Feedback.toast.error('请至少选择一个集中器');
    }
  }

  render() {
    const {dataSource, page, pageSize, total, dataLoading, selectedRowKeys, searchAll, getReaders} = this.state
    const {init} = this.field
    const formItemLayout = {
      labelCol: {fixedSpan: 6}
    }
    return (
      <div>

        <IceContainer title="搜索">
          <Form field={this.field}>
            <Row justify="space-around">
              <FormItem label="集中器编号：">
                <Input {...init('cid')} placeholder="请输入" style={{width: '160px'}}/>
              </FormItem>
              <FormItem label="集中器位置：" {...formItemLayout}>
                <Input {...init('fixedAddress')} placeholder="请输入" style={{width: '160px'}}/>
              </FormItem>
              <FormItem label="集中器状态：" {...formItemLayout}>
                <Select {...init('useState')}
                        dataSource={[
                          {label: '在线', value: '02'},
                          {label: '离线', value: '00'},
                          {label: '正在工作', value: '01'},

                        ]}
                        style={{width: '160px'}}
                >
                </Select>
              </FormItem>
            </Row>
          </Form>
          <div align="center">
            <Button type="primary" className="button" onClick={() => this.doSearch()} style={{marginRight: 30}}>
              <Icon type="search"/>查询
            </Button>
            <Button type="secondary" className="button" onClick={() => this.reset()} style={{marginRight: 30}}>
              <Icon type="refresh"/>重置
            </Button>
            {/*<Button type="primary" className="button" onClick={() => this.getAllState()} loading={searchAll}>
              查询全部集中器状态
            </Button>*/}
          </div>
        </IceContainer>

        <IceContainer title="民生集中器列表">

          <div style={{marginBottom: 10}}>
            <Button className="button" type="primary" onClick={this.getNewReaders}>
              <Icon type="share" style={{color: "#ffffff"}}/>
              抄表
            </Button>

            <Button className="button" type="primary" onClick={() => this.batchClose()} style={{marginLeft: 10}}>
              <Icon type="stop" style={{color: "#ffffff"}}/>
              批量关阀
            </Button>
          </div>

          <Table dataSource={dataSource}
                 isLoading={dataLoading}
                 rowSelection={{
                   ...this.rowSelection,
                   selectedRowKeys: selectedRowKeys,
                 }}
          >
            <Table.Column title="序号" cell={this.rowIndex} align="center" width={80}/>
            <Table.Column title="集中器编号" dataIndex="cid" align="center"/>
            <Table.Column title="集中器位置" dataIndex="fixedAddress" align="center"/>
            <Table.Column title="挂表数量" dataIndex="watermeterNum" align="center" />
            <Table.Column title="集中器状态" dataIndex="useState" align="center" cell={this.useStateFm}/>
            <Table.Column title="更新时间" dataIndex="createTime" align="center"/>
            <Table.Column title="操作" cell={this.renderOper} align='center'/>
          </Table>

          <div style={{display: 'flex', justifyContent: 'flex-end'}}>
            <Pagination
              style={{textAlign: 'right', marginTop: 15}}
              pageSizeSelector="dropdown"
              onChange={this.changePage}
              onPageSizeChange={this.onPageSizeChange}
              total={total}
              pageSize={pageSize}
              current={page}
              size="small"
              pageSizeList={[10, 30, 50, 100]}
            />
            <div style={{lineHeight: '58px', marginLeft: 10}}>共 {total} 条记录</div>
          </div>
        </IceContainer>

      </div>
    )
  }
}

