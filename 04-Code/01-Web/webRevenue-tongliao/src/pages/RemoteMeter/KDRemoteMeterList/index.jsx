import React, {Component} from 'react';
import {
    <PERSON><PERSON>,
    <PERSON>alog,
    Feedback,
    <PERSON>,
    Form,
    Grid,
    Icon,
    Input,
    moment,
    Pagination,
    Select,
    Table,
} from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import qs from 'qs';
import {url} from '../../../components/URL/index';
import styles from './index.module.scss';
import MeterReading from './components/MeterReading';
import ManualBill from './components/ManualBill';
import getLodop from '../../../common/LodopFuncs';
import XLSX from 'xlsx';
import BatchAccounting from '../RemoteMeterList/components/BatchAccounting';
import {formItemLayout, span} from '../../../common/FormCollocation';
import {billStatusStatus} from '../../../common/WaterMeterCollocation';
import ManuallyGenerateBills
    from '../RemoteMeterList/components/ManuallyGenerateBills';
import BatchSendSms from '../KDRemoteMeterList/components/BatchSendSms';
import CloseValveBalloon
    from '../KDRemoteMeterList/components/CloseValveBalloon';
import OpenValveBalloon from '../KDRemoteMeterList/components/OpenValveBalloon';

const {Row, Col} = Grid;
const FormItem = Form.Item;
const {Combobox} = Select;
let LODOP = getLodop();

export default class SKRemoteMeterTable extends Component {
    constructor(props) {
        super(props);
        // 表格可以勾选配置项
        this.rowSelection = {
            // 表格发生勾选状态变化时触发，ids可以将所有勾选的行ID获取到
            onChange: (ids, records) => {
                this.setState({
                    selectedRowKeys: ids,
                    selectedRecord: records,
                });
            },
        };
        this.state = {
            roleNameList: [], //抄表员
            formValue: [],
            page: 1,
            pageSize: 10,
            total: 0,
            visible: false,
            dataLoading: false,
            openValve: true,
            selectedRowKeys: [],
            selectedRecord: [],
            stuffId: sessionStorage.getItem('stuffId'),
            stuffName: sessionStorage.getItem('realName'),
            areaList: [],
            regionList: [],
            arr: [],
        };
        this.field = new Field(this, {autoUnmount: true});
    }

    componentDidMount() {
        this.queryArea();
        this.queryRegion();
        this.queryRoleName();
    }

    //查询抄表员
    queryRoleName() {
        axios({
            method: 'get',
            url: `${url}revenue/staff/getAllCname`,
        })
        .then((response) => {
            if (response.data.code == 0) {
                let roleNameList = [];
                response.data.datas.map((item) => {
                    roleNameList.push({label: item.label, value: item.id});
                });
                this.setState({roleNameList: roleNameList});
            } else {
                Feedback.toast.error(response.data.msg);
            }

        })
        .catch((error) => {
            Feedback.toast.error('请求错误：' + error);
        });
    }

    refreshTable = (page, pageSize) => {
        const {arr} = this.state;
        this.setState({dataLoading: true});
        let values = this.field.getValues();
        values.page = page;
        values.pageSize = pageSize;
        values.phones = arr ? arr : void 0;
        values.watermeterCompany = '山东科德';
        axios
        .post(`${url}revenue/thirdPartyRemoteWatermeter/query`, qs.stringify(values))
        .then((response) => {
            this.setState({ dataLoading: false });
            let jsondata = response.data;
            this.setState({
                formValue: jsondata.datas,
                page: page,
                pageSize: pageSize,
                total: jsondata.totalSize,
                selectedRowKeys: [],
                selectedRecord: [],
            });
            document.getElementById('excel').value = '';
        })
        .catch((error) => {
            this.setState({ dataLoading: false });
            Feedback.toast.error('网络错误请刷新后重试');
        });
    };

    querySKWaterMeter(page, pageSize) {
        const { arr } = this.state;
        this.setState({ dataLoading: true });
        let values = this.field.getValues();
        values.page = page;
        values.pageSize = pageSize;
        values.phones = arr ? arr : null;
        values.watermeterCompany = '山东科德';
        axios
        .post(`${url}revenue/thirdPartyRemoteWatermeter/query`, qs.stringify(values))
        .then((response) => {
            let resultData = response.data;
            this.setState({
                formValue: resultData.datas,
                page: page,
                pageSize: pageSize,
                total: resultData.totalSize,
                selectedRowKeys: [],
                selectedRecord: [],
                dataLoading: false,
            });
            document.getElementById('excel').value = '';
        })
        .catch((error) => {
            this.setState({ dataLoading: false });
            Feedback.toast.error('网络异常请刷新页面重试');
        });
    }

    // 查询片区
    queryArea() {
        axios.get(`${url}revenue/area/getAll`).then((response) => {
            let areaList = [];
            response.data.datas.map((item) => {
                areaList.push({ label: item.name, value: item.id });
            });
            this.setState({ areaList: areaList });
        });
    }

    // 查询小区
    queryRegion() {
        axios.post(`${url}revenue/region/regionlist`).then((response) => {
            let regionList = [];
            response.data.datas.map((item) => {
                regionList.push({ label: item.regionName, value: item.id });
            });
            this.setState({ regionList: regionList });
        });
    }

    // 翻页
    changePage = (page) => {
        const { pageSize } = this.state;
        this.querySKWaterMeter(page, pageSize);
    };

    // 改变pageSize
    onPageSizeChange = (pageSize) => {
        this.querySKWaterMeter(1, pageSize);
    };

    // 重置
    reset() {
        this.field.reset();
        this.setState({ arr: [] });
    }

    // 表格操作栏按钮
    turnOnOrOff = (value, index, record) => {
        const { page, pageSize } = this.state;
        return (
            <div style={{ display: 'flex', justifyContent: 'space-around' }}>
                <CloseValveBalloon record={record} refreshTable={() => this.querySKWaterMeter(page, pageSize)} />
                <OpenValveBalloon record={record} refreshTable={() => this.querySKWaterMeter(page, pageSize)} />
                <MeterReading record={record} refreshTable={() => this.querySKWaterMeter(page, pageSize)} />
                <ManualBill record={record} refreshTable={() => this.querySKWaterMeter(page, pageSize)} />
                <ManuallyGenerateBills record={record} refreshTable={() => this.querySKWaterMeter(page, pageSize)} />
            </div>
        );
    };

    // 批量关阀
    batchClose = () => {
        const { selectedRecord, stuffId, stuffName } = this.state;
        if (selectedRecord.length == 0) {
            alert('至少选择一条记录');
            return;
        }
        for (let i = 0; i < selectedRecord.length - 1; i++) {
            for (let j = i + 1; j < selectedRecord.length; j++) {
                if (selectedRecord[i].watermeterKind != selectedRecord[i + 1].watermeterKind) {
                    alert('无线和有线远传表不能同时关阀，请筛选');
                    return;
                }
            }
        }
        let values = {};
        values.createId = stuffId;
        values.createName = stuffName;
        values.remoteWatermeters = selectedRecord.map((item) => {
            return { ...item, valve: '2' };
        });
        axios({
            method: 'POST',
            url: `${url}revenue/thirdPartyRemoteWatermeter/switchValve`,
            data: values,
        })
        .then((response) => {
            let jsondata = response.data;
            if (jsondata.code == 0) {
                alert('关阀指令发送成功');
                this.refreshTable();
            } else {
                alert(jsondata.msg);
            }
        })
        .catch((error) => {
            Feedback.toast.error('ajax请求出错: ' + error);
        });
    };

    checkedInput = (rule, value, callback) => {
        if (value) {
            if (value.indexOf('.') == 1) {
                callback('只能输入正整数');
            } else {
                callback();
            }
        } else {
            callback();
        }
    };

    onChangeInput = (value) => {
        this.field.setValue('unuploadDaysEnd', Math.round(value));
    };

    // 导出欠费数据
    downLoadArrearage() {
        let values = this.field.getValues();
        let url1 = `${url}revenue/thirdPartyRemoteWatermeter/downAllArrearagerExcel?watermeterCompany=山东科德`;
        if (values.watermeterId) {
            url1 += '&watermeterId=' + values.watermeterId;
        }
        if (values.cno) {
            url1 += '&cno=' + values.cno;
        }
        if (values.valve) {
            url1 += '&valve=' + values.valve;
        }
        if (values.watermeterKind) {
            url1 += '&watermeterKind=' + values.watermeterKind;
        }
        if (values.fixedAddress) {
            url1 += '&fixedAddress=' + values.fixedAddress;
        }
        if (values.areaId) {
            url1 += '&areaId=' + values.areaId;
        }
        if (values.regionId) {
            url1 += '&regionId=' + values.regionId;
        }
        if (values.unuploadDaysStar) {
            url1 += '&unuploadDaysStar=' + values.unuploadDaysStar;
        }
        if (values.unuploadDaysEnd) {
            url1 += '&unuploadDaysEnd=' + values.unuploadDaysEnd;
        }
        if (values.billStatus) {
            url1 += '&billStatus=' + values.billStatus;
        }
        if (values.account) {
            url1 += '&account=' + values.account;
        }
        if (values.copyerId) {
            url1 += '&copyerId=' + values.copyerId;
        }
        if (values.zoneCode) {
            url1 += '&zoneCode=' + values.zoneCode;
        }
        window.open(encodeURI(url1), 'about:blank');
    }

    // 导出余额不足的用户
    downLoadAccount() {
        let values = this.field.getValues();
        let url1 = `${url}revenue/thirdPartyRemoteWatermeter/exportBalanceIsNotEnough?watermeterCompany=山东科德`;
        if (values.watermeterId) {
            url1 += '&watermeterId=' + values.watermeterId;
        }
        if (values.cno) {
            url1 += '&cno=' + values.cno;
        }
        if (values.valve) {
            url1 += '&valve=' + values.valve;
        }
        if (values.watermeterKind) {
            url1 += '&watermeterKind=' + values.watermeterKind;
        }
        if (values.fixedAddress) {
            url1 += '&fixedAddress=' + values.fixedAddress;
        }
        if (values.areaId) {
            url1 += '&areaId=' + values.areaId;
        }
        if (values.regionId) {
            url1 += '&regionId=' + values.regionId;
        }
        if (values.unuploadDaysStar) {
            url1 += '&unuploadDaysStar=' + values.unuploadDaysStar;
        }
        if (values.unuploadDaysEnd) {
            url1 += '&unuploadDaysEnd=' + values.unuploadDaysEnd;
        }
        if (values.billStatus) {
            url1 += '&billStatus=' + values.billStatus;
        }
        if (values.account) {
            url1 += '&account=' + values.account;
        }
        if (values.copyerId) {
            url1 += '&copyerId=' + values.copyerId;
        }
        if (values.zoneCode) {
            url1 += '&zoneCode=' + values.zoneCode;
        }
        window.open(encodeURI(url1), 'about:blank');
    }

    // 导出用户
    downUser() {
        let values = this.field.getValues();
        let url1 = `${url}revenue/thirdPartyRemoteWatermeter/export?watermeterCompany=山东科德`;
        if (values.watermeterId) {
            url1 += '&watermeterId=' + values.watermeterId;
        }
        if (values.cno) {
            url1 += '&cno=' + values.cno;
        }
        if (values.valve) {
            url1 += '&valve=' + values.valve;
        }
        if (values.watermeterKind) {
            url1 += '&watermeterKind=' + values.watermeterKind;
        }
        if (values.fixedAddress) {
            url1 += '&fixedAddress=' + values.fixedAddress;
        }
        if (values.areaId) {
            url1 += '&areaId=' + values.areaId;
        }
        if (values.regionId) {
            url1 += '&regionId=' + values.regionId;
        }
        if (values.unuploadDaysStar) {
            url1 += '&unuploadDaysStar=' + values.unuploadDaysStar;
        }
        if (values.unuploadDaysEnd) {
            url1 += '&unuploadDaysEnd=' + values.unuploadDaysEnd;
        }
        if (values.billStatus) {
            url1 += '&billStatus=' + values.billStatus;
        }
        if (values.account) {
            url1 += '&account=' + values.account;
        }
        if (values.copyerId) {
            url1 += '&copyerId=' + values.copyerId;
        }
        if (values.zoneCode) {
            url1 += '&zoneCode=' + values.zoneCode;
        }
        window.open(encodeURI(url1), 'about:blank');
    }

    /* 渲染阀门状态 */
    renderState(value) {
        if (value == '1') {
            return <span style={{ color: '#1DC11D' }}>开启</span>;
        } else if (value == '2') {
            return <span style={{ color: '#ff0000' }}>关闭</span>;
        } else {
            return <span style={{ color: '#ff0000' }}>未知</span>;
        }
    }

    // 批量打印欠费通知
    printNotice() {
        LODOP = getLodop();
        const { selectedRecord } = this.state;
        if (selectedRecord.length == 0) {
            alert('至少选择一条记录');
            return;
        }
        axios
        .post(`${url}revenue/printTemplate/get`, qs.stringify({ templateName: '远传表欠费通知单' }))
        .then((response) => {
            let ajaxData = response.data;
            if (ajaxData.code == '0') {
                // 模板数据
                let templateCode = ajaxData.datas.templateCode;
                selectedRecord.map((item, key) => {
                    let name = item.cname + item.fixedAddress;
                    let temp = templateCode
                    .replace('time', moment(new Date()).format('YYYY-MM-DD'))
                    .replace('cno', item.cno)
                    .replace('cname', name)
                    .replace('price', item.feeName)
                    .replace('thisNum', item.thisNum)
                    .replace('tunnage', item.tunnage)
                    .replace('totalPrice', item.amount)
                    .replace('acount', item.account)
                    .replace('arrears', item.unpaid)
                    .replace('time1', moment(new Date()).format('YYYY-MM-DD'))
                    .replace('cno1', item.cno)
                    .replace('cname1', name)
                    .replace('price1', item.feeName)
                    .replace('thisNum1', item.thisNum)
                    .replace('tunnage1', item.tunnage)
                    .replace('totalPrice1', item.amount)
                    .replace('acount1', item.account)
                    .replace('arrears1', item.unpaid);
                    eval(temp);
                    LODOP.PRINT();
                });
            }
        })
        .catch((error) => {
            Feedback.toast.error('系统异常请稍后再试' + error);
        });
    }

    // 根据搜索条件批量派发任务
    batchDistributed = () => {
        const { stuffId, stuffName } = this.state;
        let values = this.field.getValues();
        let params = {
            createId: stuffId,
            createName: stuffName,
            watermeterCompany: '山东科德',
        };
        params = Object.assign(params, values);
        Dialog.confirm({
            content: `是否批量派发任务`,
            title: '提示',
            onOk: () => {
                this.setState({ LoadingVisible: true });
                axios
                .post(`${url}revenue/remoteWatermeter/assignTaskByCondition`, qs.stringify(params))
                .then((response) => {
                    if (response.data.code === '0') {
                        Feedback.toast.success(response.data.datas);
                    } else {
                        Feedback.toast.error(response.data.msg);
                    }
                    this.setState({ LoadingVisible: false });
                });
            },
        });
    };

    // excel导入电话查询
    telSearch(e) {
        const { files } = e.target;
        const reader = new FileReader();
        reader.readAsArrayBuffer(files[0]);
        reader.onload = (evt) => {
            try {
                let result = evt.target.result;
                const workbook = XLSX.read(result, { type: 'array' });
                let data = [];

                for (const sheet in workbook.Sheets) {
                    if (workbook.Sheets.hasOwnProperty(sheet)) {
                        data = data.concat(XLSX.utils.sheet_to_json(workbook.Sheets[sheet]));
                        break;
                    }
                }
                let arr = [];
                for (let i in data) {
                    for (let x in data[i]) {
                        arr.push(data[i][x]); // 属性
                    }
                }
                this.setState({ arr: arr });
                this.querySKWaterMeter(1, 10);
            } catch (e) {
                Feedback.toast.error(e);
                document.getElementById('excel').value = '';
            }
        };
        document.getElementById('excel').value = '';
    }

    render() {
        const {
            formValue,
            page,
            total,
            pageSize,
            dataLoading,
            selectedRowKeys,
            areaList,
            regionList,
            selectedRecord,
            roleNameList
        } =
            this.state;
        const {init} = this.field;

        return (
            <>
                <IceContainer title="山东科德远传表管理">
                    <Form field={this.field}>
                        <Row wrap>
                            <Col {...span}>
                                <FormItem label="用户编号：" {...formItemLayout}>
                                    <Input {...init('cno')} placeholder="请输入用户编号" style={{ width: 220 }} />
                                </FormItem>
                            </Col>
                            <Col {...span}>
                                <FormItem label="用户名称：" {...formItemLayout}>
                                    <Input {...init('cname')} placeholder="请输入用户名称" style={{ width: 220 }} />
                                </FormItem>
                            </Col>
                            <Col {...span}>
                                <FormItem label="片区：" {...formItemLayout}>
                                    <Select
                                        {...init('areaId')}
                                        dataSource={areaList}
                                        placeholder="请选择片区"
                                        className={styles.selectWidth}
                                    />
                                </FormItem>
                            </Col>
                            <Col {...span}>
                                <FormItem label="小区：" {...formItemLayout}>
                                    <Combobox
                                        {...init('regionId')}
                                        dataSource={regionList}
                                        fillProps="label"
                                        hasClear
                                        placeholder="请选择小区"
                                        className={styles.selectWidth}
                                    />
                                </FormItem>
                            </Col>
                            <Col {...span}>
                                <FormItem label="水表编号：" {...formItemLayout}>
                                    <Input
                                        {...init('watermeterId')}
                                        placeholder="请输入水表编号"
                                        style={{ width: 220 }}
                                    />
                                </FormItem>
                            </Col>
                            <Col {...span}>
                                <FormItem label="水表种类：" {...formItemLayout}>
                                    <Select
                                        {...init('watermeterKind')}
                                        dataSource={[
                                            {
                                                label: '有线远传',
                                                value: '有线远传'
                                            },
                                            {
                                                label: '无线远传',
                                                value: '无线远传'
                                            },
                                        ]}
                                        placeholder="请选择水表种类"
                                        className={styles.selectWidth}
                                    />
                                </FormItem>
                            </Col>
                            <Col {...span}>
                                <FormItem label="余额小于或等于：" {...formItemLayout}>
                                    <Input {...init('account')} placeholder="请输入余额" style={{ width: 220 }} />
                                </FormItem>
                            </Col>
                            <Col {...span}>
                                <FormItem label="未上传数据天数：" {...formItemLayout}>
                                    <Input
                                        {...init('unuploadDaysStar', { rules: [{ validator: this.checkedInput }] })}
                                        style={{ width: 105 }}
                                    />
                                    <span>—</span>
                                    <Input
                                        {...init('unuploadDaysEnd')}
                                        style={{ width: 105 }}
                                        onChange={this.onChangeInput}
                                    />
                                </FormItem>
                            </Col>
                            <Col {...span}>
                                <FormItem label="欠费状态：" {...formItemLayout}>
                                    <Select
                                        {...init('billStatus')}
                                        dataSource={billStatusStatus}
                                        placeholder="请选择欠费状态"
                                        className={styles.selectWidth}
                                    />
                                </FormItem>
                            </Col>
                            <Col {...span}>
                                <FormItem label="电话是否完善：" {...formItemLayout}>
                                    <Select
                                        {...init('isHaveTel')}
                                        dataSource={[
                                            { value: '', label: '全部' },
                                            { value: '0', label: '否' },
                                            { value: '1', label: '是' },
                                        ]}
                                        placeholder="请选择电话是否完善"
                                        className={styles.selectWidth}
                                    />
                                </FormItem>
                            </Col>
                            <Col {...span}>
                                <FormItem label="用户地址：" {...formItemLayout}>
                                    <Input
                                        {...init('fixedAddress')}
                                        placeholder="请输入用户地址"
                                        style={{ width: 220 }}
                                    />
                                </FormItem>
                            </Col>
                            <Col {...span}>
                                <FormItem label="阀门状态：" {...formItemLayout}>
                                    <Select
                                        {...init('valve')}
                                        dataSource={[
                                            {label: '请选择', value: ''},
                                            {label: '开启', value: '1'},
                                            {label: '关闭', value: '2'},
                                            {label: '未知', value: '3'},
                                        ]}
                                        placeholder="请选择阀门状态"
                                        className={styles.selectWidth}
                                    />
                                </FormItem>
                            </Col>
                            <Col {...span}>
                                <FormItem label="查收员：" {...formItemLayout}>
                                    <Combobox
                                        {...init('copyerId')}
                                        dataSource={roleNameList}
                                        fillProps="label"
                                        hasClear
                                        placeholder="请选择查收员"
                                        className={styles.selectWidth}
                                    />
                                </FormItem>
                            </Col>

                            <Col {...span}>
                                <FormItem label="区号：" {...formItemLayout}>
                                    <Input
                                        {...init('zoneCode')}
                                        placeholder="请输入区号"
                                        style={{width: 220}}
                                    />
                                </FormItem>
                            </Col>
                        </Row>
                    </Form>
                    <div align="center">
                        <Button type="primary" className="button" onClick={() => this.querySKWaterMeter(1, 10)}>
                            <Icon type="search" />
                            查询
                        </Button>
                        <Button
                            type="secondary"
                            className="button"
                            onClick={() => this.reset()}
                            style={{ marginLeft: 20 }}
                        >
                            <Icon type="refresh" />
                            重置
                        </Button>
                        <span
                            style={{
                                marginLeft: 20,
                                position: 'absolute',
                                borderRadius: 4,
                                border: '1px solid',
                                width: 100,
                                height: 28,
                                lineHeight: '26px',
                                backgroundColor: '#2077FF',
                            }}
                        >
                            <input
                                id="excel"
                                style={{
                                    cursor: 'pointer',
                                    position: 'absolute',
                                    outline: 'none',
                                    opacity: 0,
                                    width: 100,
                                    height: 28,
                                }}
                                type="file"
                                accept=".xlsx,.xls"
                                onChange={(e) => this.telSearch(e)}
                            />
                            <span style={{ color: 'white' }}>导入电话查询</span>
                        </span>
                    </div>
                </IceContainer>
                <IceContainer title="山东科德远传表列表">
                    <div style={{ marginBottom: 10 }}>
                        <Button className="button" type="primary" onClick={this.batchClose} style={{ marginRight: 10 }}>
                            批量关阀
                        </Button>
                        <Button
                            className="button"
                            type="primary"
                            onClick={() => this.downLoadArrearage()}
                            style={{ marginRight: 10 }}
                        >
                            <Icon type="download" style={{ color: '#ffffff' }} />
                            导出欠费数据
                        </Button>
                        <Button
                            className="button"
                            type="primary"
                            onClick={() => this.downLoadAccount()}
                            style={{ marginRight: 10 }}
                        >
                            <Icon type="download" style={{ color: '#ffffff' }} />
                            导出余额不足用户
                        </Button>
                        <Button
                            className="button"
                            type="primary"
                            onClick={() => this.downUser()}
                            style={{ marginRight: 10 }}
                        >
                            <Icon type="download" style={{ color: '#ffffff' }} />
                            导出用户
                        </Button>
                        <Button
                            className="button"
                            type="primary"
                            onClick={() => this.printNotice()}
                            style={{ marginRight: 10 }}
                        >
                            <Icon type="download" style={{ color: '#ffffff' }} />
                            批量打印欠费通知
                        </Button>
                        <BatchAccounting
                            selectedRecord={selectedRecord}
                            refreshTable={() => this.querySKWaterMeter(1, 10)}
                        />
                        <BatchSendSms
                            selectedRecord={this.field.getValues()}
                            refreshTable={() => this.querySKWaterMeter(1, 10)}
                        />
                        <Button
                            style={{ margin: 10 }}
                            className="button"
                            type="primary"
                            onClick={this.batchDistributed}
                        >
                            批量派发抄表任务
                        </Button>
                    </div>
                    <Table
                        dataSource={formValue}
                        isLoading={dataLoading}
                        rowSelection={{
                            ...this.rowSelection,
                            selectedRowKeys: selectedRowKeys
                        }}
                        primaryKey="id"
                    >
                        <Table.Column title="用户编号" dataIndex="cno"
                                      align="center"/>
                        <Table.Column title="用户名称" dataIndex="cname"
                                      align="center"/>
                        <Table.Column title="水表编号" dataIndex="watermeterId"
                                      align="center"/>
                        <Table.Column title="用户地址" dataIndex="fixedAddress"
                                      align="center"/>
                        <Table.Column title="区号" dataIndex="zoneCode"
                                      align="center"/>
                        <Table.Column title="查收员" dataIndex="copyerName"
                                      align="center"/>
                        <Table.Column title="字轮读数" dataIndex="thisNum"
                                      align="center"/>
                        <Table.Column title="通讯时间" dataIndex="contactTime"
                                      align="center"/>
                        <Table.Column title="水表通讯状态" dataIndex="contactState"
                                      align="center"/>
                        <Table.Column title="未上传数据天数" dataIndex="unUploadDays"
                                      align="center"/>
                        <Table.Column
                            title="欠费状态"
                            dataIndex="billStatus"
                            align="center"
                            cell={(value) => (value == '0' ? '欠费' : '不欠费')}
                        />
                        <Table.Column title="欠费金额" dataIndex="unpaid" align="center" />
                        <Table.Column title="账户余额" dataIndex="account" align="center" />
                        <Table.Column
                            title="阀门状态"
                            dataIndex="valve"
                            cell={(value) => this.renderState(value)}
                            align="center"
                        />
                        <Table.Column title="操作" cell={this.turnOnOrOff} align="center" width={120} />
                    </Table>
                    <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
                        <Pagination
                            style={{ textAlign: 'right', marginTop: 15 }}
                            pageSizeSelector="dropdown"
                            onChange={this.changePage}
                            onPageSizeChange={this.onPageSizeChange}
                            total={total}
                            pageSize={pageSize}
                            current={page}
                            size="small"
                            pageSizeList={[10, 30, 50, 100]}
                        />
                        <div style={{ lineHeight: '58px', marginLeft: 10 }}>共 {total} 条记录</div>
                    </div>
                </IceContainer>
            </>
        );
    }
}
