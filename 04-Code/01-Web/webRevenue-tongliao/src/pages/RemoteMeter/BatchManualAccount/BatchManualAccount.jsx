/* eslint-disable react/no-unused-state */
import React, {Component} from 'react';
import {
    <PERSON><PERSON>,
    DatePicker,
    Dialog,
    Feedback,
    Field,
    Form,
    Grid,
    Icon,
    Input,
    moment,
    Pagination,
    Select,
    Table,
    Upload,
} from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import qs from 'qs';
import {url} from '../../../components/URL';
import BillDetailInfo from './components/BillDetailInfo';

const FormItem = Form.Item;
const { Row } = Grid;
const { RangePicker } = DatePicker;
const { Combobox } = Select;

export default class BatchManualAccount extends Component {
    constructor(props) {
        super(props);
        this.rowSelection = {
            onChange: (ids, records) => {
                this.setState({
                    selectedRowKeys: ids,
                    selectedRecords: records,
                });
            },
            getProps: (record) => {
                return {
                    disabled: record.出账状态 == '失败',
                };
            },
        };
        this.state = {
            page: 1,
            pageSize: 10,
            total: 0,
            dataLoading: false,
            dataSource: [],
            selectedRowKeys: [],
            selectedRecords: [],
            areaList: [],
            regionList: [],
            roleNameList: [], // 查询操作员
            createId: sessionStorage.getItem('stuffId'),
            createName: sessionStorage.getItem('realName'),
            isShowBatchBillingDialog: false,
            isCompleteBilling: false,
            accountingBills: '查询中',
        };
        this.field = new Field(this, { autoUnmount: true });
    }

    componentDidMount() {
        this.queryArea();
        this.queryRegion();
        this.queryRoleName();
    }

    // 查询操作员
    queryRoleName() {
        axios.get(`${url}revenue/user/createName`).then((response) => {
            if (response.data.code == 0) {
                this.setState({
                    roleNameList: response.data.datas,
                });
            }
        });
    }

    // 查询片区
    queryArea = () => {
        axios({
            method: 'get',
            url: url + 'revenue/area/getAll',
        })
            .then((response) => {
                let areaList = [];
                response.data.datas.map((item) => {
                    areaList.push({ label: item.name, value: item.id });
                });
                this.setState({ areaList: areaList });
            })
            .catch((error) => {
                Feedback.toast.error('请求错误：' + error);
            });
    };

    // 查询小区
    queryRegion = () => {
        axios({
            method: 'post',
            url: `${url}revenue/region/regionlist`,
        })
            .then((response) => {
                let regionList = [];
                response.data.datas.map((item) => {
                    regionList.push({ label: item.regionName, value: item.id });
                });
                this.setState({ regionList: regionList });
            })
            .catch((error) => {
                Feedback.toast.error('请求异常', error);
            });
    };

    refreshTable = (page, pageSize) => {
        this.setState({ dataLoading: true });
        let values = this.field.getValues();
        values.page = page;
        values.pageSize = pageSize;
        delete values.queryExpensesTime;
        axios
            .post(`${url}revenue/bill/listBatchManualAccountStatus`, qs.stringify(values))
            .then((response) => {
                this.setState({ dataLoading: false });
                let jsondata = response.data;
                this.setState({
                    dataSource: jsondata.datas,
                    page: page,
                    pageSize: pageSize,
                    total: jsondata.totalSize,
                    selectedRowKeys: [],
                    selectedRecords: [],
                });
            })
            .catch((error) => {
                this.setState({ dataLoading: false });
                Feedback.toast.error('axios请求异常:' + error);
            });
    };

    // 查询
    doSearch = () => {
        const { pageSize } = this.state;
        this.refreshTable(1, pageSize);
    };

    // 重置
    reset = () => {
        this.field.reset();
        const { pageSize } = this.state;
        this.refreshTable(1, pageSize);
    };

    // 翻页
    changePage = (page) => {
        const { pageSize } = this.state;
        this.refreshTable(page, pageSize);
    };

    // 改变pageSize
    onPageSizeChange = (pageSize) => {
        this.refreshTable(1, pageSize);
    };

    /* 表格操作栏 */
    renderOper = (value, index, record) => {
        if (record.remark == '出账成功') {
            return <BillDetailInfo record={record} />;
        }
    };

    rowIndex = (value, index) => {
        const { pageSize, page } = this.state;
        if (page == 1) {
            return index + 1;
        } else {
            return index + 1 + (page - 1) * pageSize;
        }
    };

    /**
     * 出账状态Fm
     */
    outBillFm = (value) => {
        if (value == 1) {
            return <span style={{ color: '#1DC11D' }}>出账结束</span>;
        } else if (value == 0) {
            return <span style={{ color: '#ff3333' }}>未出账</span>;
        } else if (value == 2) {
            return <span style={{ color: '#ff3333' }}>出账中</span>;
        } else {
            return <span style={{ color: '#ff3333' }}>未知</span>;
        }
    };

    /**
     * 账单状态Fm
     */
    billState = (value, index, record) => {
        if (record.status == 0) {
            return;
        }
        if (record.billStatus == 1) {
            return <span style={{ color: '#1DC11D' }}>已结清</span>;
        } else if (record.billStatus == 0) {
            return <span style={{ color: '#ff3333' }}>未结清</span>;
        } else if (record.billStatus == 2) {
            return <span style={{ color: '#ff3333' }}>作废</span>;
        }
    };

    /**
     * 单个出账
     */
    outBill = (value, index, record) => {
        let values = {};
        values.id = record.id;
        this.doOutBillAxios(values);
    };

    queryBillingProgress = () => {
        axios({
            method: 'post',
            url: `${url}revenue/bill/getAccountResult`,
        }).then((response) => {
            console.log('🆑 => BatchManualAccount => response', response);
            if (response.data.code === '0') {
                const { accountingBills } = response.data.datas;
                this.setState({ accountingBills });
                if (accountingBills === 0) {
                    this.setState({isCompleteBilling: true});
                    this.setState({accountingBills: '已完成!'});
                } else {
                    setTimeout(() => {
                        this.queryBillingProgress();
                    }, 2000);
                }
            } else Feedback.toast.error(response.data.msg);
        });
    };

    showBatchBillingDialog = () => {
        this.setState({ isShowBatchBillingDialog: !this.state.isShowBatchBillingDialog, accountingBills: '查询中' });
    };

    /**
     * 出账调接口
     */
    doOutBillAxios = () => {
        this.showBatchBillingDialog();
        setTimeout(() => {
            this.queryBillingProgress();
        }, 2000);
        axios({
            method: 'post',
            url: `${url}revenue/bill/batchManulAccount`,
            // data: qs.stringify(values),
        })
            .then((response) => {
                let jsondata = response.data;
                if (jsondata.code == 0) {
                    Feedback.toast.success('出账成功');
                    this.refreshTable(1, 10);
                } else {
                    Feedback.toast.error(jsondata.msg);
                }
            })
            .catch((error) => {
                Feedback.toast.error('axios请求异常:' + error);
            })
            .finally(() => {
                this.setState({ dataLoading: false });
            });
    };

    uploadSuccess = (response) => {
        if (response.code == 0) {
            alert(`导入完成,请确认无误后点击批量出账.`);
            this.refreshTable(1, 10);
        }
    };

    uploadError = (response) => {
        if (response.response.code == 1) {
            alert(response.response.msg);
        }
    };

    /**
     * 时间格式Fm
     */
    dataFm = (value) => {
        if (value) {
            return moment(value).format('YYYY-MM-DD');
        }
    };

    /**
     * 选择时间区间Onchange
     */
    timeOnchange = (value, str) => {
        this.field.setValue('bcsTime', str[0]);
        this.field.setValue('bceTime', str[1]);
    };
    /**
     * 选择时间区间Onchange
     */
    timeOnchanged = (value, str) => {
        this.field.setValue('dcsTime', str[0]);
        this.field.setValue('dceTime', str[1]);
    };

    // 导出账单
    downLoadBill() {
        let values = this.field.getValues();
        let url1 = `${url}revenue/excel/exportTeleteterBill?n=1`;
        if (values.cno) {
            url1 += '&cno=' + values.cno;
        }
        if (values.watermeterId) {
            url1 += '&watermeterId=' + values.watermeterId;
        }
        if (values.billStatus) {
            url1 += '&billStatus=' + values.billStatus;
        }
        if (values.watermeterCompany) {
            url1 += '&watermeterCompany=' + encodeURI(values.watermeterCompany);
        }
        if (values.statuses) {
            url1 += '&statuses=' + values.statuses;
        }
        if (values.status) {
            url1 += '&status=' + values.status;
        }
        if (values.csTime) {
            url1 += '&csTime=' + values.csTime;
        }
        if (values.ceTime) {
            url1 += '&ceTime=' + values.ceTime;
        }
        if (values.areaId) {
            url1 += '&areaId=' + values.areaId;
        }
        if (values.regionId) {
            url1 += '&regionId=' + values.regionId;
        }
        if (values.cname) {
            url1 += '&cname=' + encodeURIComponent(values.cname);
        }
        if (values.tunnageS) {
            url1 += '&tunnageS=' + values.tunnageS;
        }
        if (values.tunnageE) {
            url1 += '&tunnageE=' + values.tunnageE;
        }
        if (values.address) {
            url1 += '&address=' + values.address;
        }
        window.open(url1, 'about:blank');
    }

    checkedInput = (rule, value, callback) => {
        if (value) {
            if (value.indexOf('.') == 1) {
                callback('只能输入正整数');
            } else {
                callback();
            }
        } else {
            callback();
        }
    };

    render() {
        const { dataSource, page, pageSize, total, dataLoading, createName, createId, roleNameList } = this.state;
        const { init } = this.field;
        const formItemLayout = {
            labelCol: { fixedSpan: 6 },
        };
        return (
            <div>
                <IceContainer title="远传表手动批量出账">
                    <Form field={this.field}>
                        <div style={{ display: 'flex', justifyContent: 'space-around' }}>
                            <div style={{ display: 'flex', flexDirection: 'column' }}>
                                <FormItem label="水表编号：" {...formItemLayout}>
                                    <Input {...init('watermeterId')} placeholder="请输入" style={{ width: '160px' }} />
                                </FormItem>
                                <FormItem label="出账状态：" {...formItemLayout}>
                                    <Select
                                        {...init('status')}
                                        dataSource={[
                                            { label: '请选择', value: '' },
                                            { label: '未出账', value: '0' },
                                            { label: '出账中', value: '2' },
                                            { label: '出账结束', value: '1' },
                                        ]}
                                        style={{ width: '160px' }}
                                    />
                                </FormItem>
                                <FormItem label="&emsp;&emsp;导入操作员：">
                                    <Combobox
                                        {...init('createId')}
                                        placeholder="--请选择--"
                                        fillProps="label"
                                        hasClear
                                        style={{ width: '160px' }}
                                        dataSource={roleNameList}
                                    />
                                </FormItem>
                            </div>

                            <div style={{ display: 'flex', flexDirection: 'column' }}>
                                <FormItem label="用户编号：" {...formItemLayout}>
                                    <Input {...init('cno')} placeholder="请输入" style={{ width: '160px' }} />
                                </FormItem>
                                <FormItem label="账单日期：" {...formItemLayout}>
                                    <RangePicker
                                        {...init('queryBillTime', {
                                            props: { onChange: (v, s) => this.timeOnchange(v, s) },
                                        })}
                                    />
                                </FormItem>
                                <FormItem label="导入日期：" {...formItemLayout}>
                                    <RangePicker
                                        {...init('queryImportTime', {
                                            props: { onChange: (v, s) => this.timeOnchanged(v, s) },
                                        })}
                                    />
                                </FormItem>
                            </div>
                        </div>
                    </Form>

                    <div align="center">
                        <Button
                            type="primary"
                            className="button"
                            onClick={() => this.doSearch()}
                            style={{ marginRight: 30 }}
                        >
                            <Icon type="search" />
                            查询
                        </Button>
                        <Button type="secondary" className="button" onClick={() => this.reset()}>
                            <Icon type="refresh" />
                            重置
                        </Button>
                    </div>
                </IceContainer>

                <IceContainer title="记录列表">
                    <div style={{ marginBottom: 10 }}>
                        <a
                            style={{
                                // height: '26px',
                                margin: 10,
                                padding: '5px 16px',
                                color: '#fff',
                                backgroundColor: 'blue',
                                borderColor: '#000',
                                textDecoration: 'none',
                            }}
                            href={`${url}revenue/bill/downLoadBatchManualAccountTemplate`}
                            className="button"
                            type="primary"
                        >
                            [下载]批量手动出账模板
                        </a>

                        <Upload
                            action={`${url}revenue/bill/batchManualAccountImport?createName=${encodeURIComponent(
                                this.state.createName)}&createId=${createId}`}
                            // action={`${url}revenue/bill/batchManualAccountImport?createName=测试&createId=111`}
                            onSuccess={this.uploadSuccess}
                            onError={this.uploadError}
                            showUploadList={false}
                            accept=".xls,.xlsx"
                            withCredentials={true}
                        >
                            <Button
                                type="primary"
                                className="button"
                                style={{ marginRight: 10, backgroundColor: 'green' }}
                            >
                                [上传]批量手动出账数据
                            </Button>
                        </Upload>

                        <Button className="button" type="primary" onClick={this.doOutBillAxios}>
                            <Icon type="store" style={{ color: '#ffffff' }} />
                            批量出账
                        </Button>
                    </div>

                    <Table
                        dataSource={dataSource}
                        isLoading={dataLoading}
                        // rowSelection={{
                        //   ...this.rowSelection,
                        //   selectedRowKeys: selectedRowKeys,
                        // }}
                    >
                        <Table.Column title="用户编号" dataIndex="cno" align="center" />
                        <Table.Column title="水表编号" dataIndex="watermeterId" align="center" />
                        <Table.Column title="账单日期" dataIndex="billTime" align="center" cell={this.dataFm} />
                        <Table.Column title="上期示数" dataIndex="lastNum" align="center" />
                        <Table.Column title="本期示数" dataIndex="thisNum" align="center" />
                        <Table.Column title="用水性质" dataIndex="feeName" align="center" />
                        <Table.Column title="抄表时间" dataIndex="thisUpdateTime" align="center" cell={this.dataFm} />
                        <Table.Column title="出账状态" dataIndex="status" align="center" cell={this.outBillFm} />
                        <Table.Column title="备注" dataIndex="remark" align="center" />
                        <Table.Column title="导入人" dataIndex="createName" align="center" />
                        <Table.Column title="导入时间" dataIndex="createTime" align="center" cell={this.dataFm} />
                        <Table.Column title="操作" cell={this.renderOper} align="center" />
                    </Table>
                    <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
                        <Pagination
                            style={{ textAlign: 'right', marginTop: 15 }}
                            pageSizeSelector="dropdown"
                            onChange={this.changePage}
                            onPageSizeChange={this.onPageSizeChange}
                            total={total}
                            pageSize={pageSize}
                            current={page}
                            size="small"
                            pageSizeList={[10, 30, 50, 100]}
                        />
                        <div style={{ lineHeight: '58px', marginLeft: 10 }}>共 {total} 条记录</div>
                    </div>
                </IceContainer>
                <Dialog
                    visible={this.state.isShowBatchBillingDialog}
                    onOk={this.showBatchBillingDialog}
                    onCancel={this.showBatchBillingDialog}
                    onClose={this.showBatchBillingDialog}
                    title="正在出账中"
                    footerAlign="center"
                >
                    <div
                        style={{
                            width: 600,
                            height: 80,
                            textAlign: 'center',
                            marginTop: 36,
                            fontSize: 24
                        }}
                    >{`剩余待出账数量: ${this.state.accountingBills}`}</div>
                </Dialog>
            </div>
        );
    }
}
