import React, {Component} from 'react';
import {
  <PERSON>oon,
  <PERSON>ton,
  DatePicker,
  Dialog,
  Feedback,
  Field,
  Form,
  Grid,
  Icon,
  Input,
  moment,
  Select
} from '@icedesign/base';
import axios from 'axios';
import qs from 'qs';
import {url} from '../../../../components/URL/index'

const {Row} = Grid;
const FormItem = Form.Item
const Tooltip = Balloon.Tooltip
export default class WatermeterInfo extends Component {

  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      doLoading: false,
      feeNameList: [], //用水性质
      createId: sessionStorage.getItem("stuffId")?'111':'222',
      createName: sessionStorage.getItem("realName"),
    };
    this.field = new Field(this, {autoUnmount: true});
  }

  //打开弹窗
  openDialog(record) {
    this.setState({visible: true})
    this.queryFeeName()
    this.field.setValue('feeId', record.feeId)
  }

  //关闭弹窗
  onClose() {
    this.setState({visible: false})
  }

  //创建账单成功
  createBill() {
    this.field.validate((errors, values) => {
      if (errors) {
        return
      } else {
        this.setState({doLoading: true})
        const {createId, createName} = this.state
        const {record} = this.props
        values.cno = record.cno
        values.hno=record.hno
        values.watermeterId = record.watermeterId
        values.areaId = record.areaId
        values.regionId = record.regionId
        values.address = record.fixedAddress
        values.hno = record.hno
        values.createId = createId
        values.createName = createName
        values.thisUpdateTime = moment(this.field.getValue('thisUpdateTime')).format('YYYY-MM-DD HH:mm:ss')
        values.createTime = moment(this.field.getValue('createTime')).format('YYYY-MM-DD HH:mm:ss')
        axios({
          method: 'post',
          url: `${url}revenue/bill/createBill`,
          data: qs.stringify(values),
        })
          .then((response) => {
            if (response.data.code == "0") {
              Feedback.toast.success("创建账单成功");
              this.setState({visible: false, doLoading: false})
              this.props.refreshTable();
            } else {
              this.setState({visible: false, doLoading: false})
              Feedback.toast.error("修改失败:" + response.data.msg);
            }
          })
          .catch((error) => {
            this.setState({doLoading: false})
            Feedback.toast.error("请求错误：" + error);
          })
      }
    })
  }

  //查询用水性质
  queryFeeName() {
    axios({
      method: 'post',
      url: `${url}revenue/fee/queryList`,
    })
      .then((response) => {
        let feeNameList = []
        response.data.datas.map((item) => {
          feeNameList.push({label: item.name, value: item.id})
        })
        this.setState({feeNameList: feeNameList})
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
      })
  }

  //校验示数 水量
  checkedPay = (rule, value, callback) => {
    if (value) {
      if (value < 0) {
        callback('收款金额不能为负数')
      } else if (isNaN(value)) {
        callback('收款金额必须是数字')
      } else {
        callback();
      }
    } else {
      callback('上期示数必填');
    }
  }

  checkedThisNum = (rule, value, callback) => {
    let thisNum = this.field.getValue('thisNum')
    let lastNum = this.field.getValue('lastNum')
    if (value) {
      if (Number(thisNum) < Number(lastNum)) {
        callback('本期示数必须大于或等于上期示数')
      }
      if (value < 0) {
        callback('收款金额不能为负数')
      } else if (isNaN(value)) {
        callback('收款金额必须是数字')
      } else {
        callback();
      }
    } else {
      callback('上期示数必填');
    }
  }

  //计算金额
  amount(value) {
    this.field.setValue('tunnage', value)
    const {record} = this.props
    axios({
      method: 'post',
      url: `${url}revenue/bill/amount`,
      data: qs.stringify({cno: record.cno, tunnage: value}),
    })
      .then((response) => {
        if (response.data.code == "0") {
          let resultData = response.data.datas
          this.field.setValue('amount', resultData.amount)
          this.field.setValue('detail', resultData.detail)
        } else {

        }
      })
      .catch((error) => {
        Feedback.toast.error("系统繁忙,请稍后再试" + error)
      })
  }

  render() {
    const init = this.field.init;
    const {record} = this.props
    const {feeNameList, doLoading,visible} = this.state
    const formItemLayout = {
      labelCol: {
        fixedSpan: 7
      }
    }
    const footer = (
      <div style={{marginTop: 20}} align="center">
        <Button type="primary" loading={doLoading} onClick={() => this.createBill()}>
          确定
        </Button>
        <Button onClick={() => this.onClose()} style={{marginLeft: 20}}>
          取消
        </Button>
      </div>
    );
    const edit = (<Icon type="text" size="small" style={{color: "#1DC11D", cursor: "pointer"}}
                        onClick={() => this.openDialog(record)}/>
    )

    return (
      <section>

        <Tooltip trigger={edit} align='t' text='手动生成账单'/>

        <Dialog minMargin={10} style={{width: 700}} visible={visible}
                onClose={() => this.onClose()}
                footer={footer}
                title='添加账单'
                footerAlign="center"
        >
          <Form field={this.field}>

            <Row>
              <FormItem {...formItemLayout} label="上期示数：">
                <Input  {...init('lastNum', {rules: [{required: true, validator: this.checkedPay}]})}
                        style={{width: 160}}/>
              </FormItem>

              <FormItem {...formItemLayout} label="本期示数：">
                <Input {...init('thisNum', {rules: [{required: true, validator: this.checkedThisNum}]})}
                       style={{width: 160}}/>
              </FormItem>
            </Row>

            <Row>
              <FormItem {...formItemLayout} label="用水性质：">
                <Select {...init('feeId', {rules: [{required: true, message: '用水性质必填'}]})} placeholder="请选择"
                        dataSource={feeNameList} style={{width: 160, backgroundColor: '#E6E6E6'}} disabled/>
              </FormItem>

              <FormItem label="抄表水量：" {...formItemLayout}>
                <Input {...init('tunnage', {rules: [{required: true, validator: this.checkedPay}]})}
                       style={{width: 160}}
                       onChange={(value) => this.amount(value)}/>
              </FormItem>
            </Row>

            <Row>
              <FormItem label="抄表日期：" {...formItemLayout} >
                <DatePicker format="YYYY年MM月DD日 "{...init("thisUpdateTime")} defaultValue={moment()}/>
              </FormItem>

              <FormItem label="出账日期：" {...formItemLayout} >
                <DatePicker format="YYYY年MM月DD日 "{...init("createTime")} defaultValue={moment()}/>
              </FormItem>
            </Row>
            <Row>
              <FormItem label="账单金额：" {...formItemLayout} >
                <Input  {...init('amount', {rules: [{required: true, message: '账单金额必填'}]})}
                        style={{width: 170, backgroundColor: '#E6E6E6'}} onBlur={() => this.field.reset('detail')}/>
              </FormItem>
            </Row>

            <Row>
              <FormItem label="账单明细：" {...formItemLayout} >
                <Input  {...init('detail')}
                        style={{width: 450, backgroundColor: '#E6E6E6'}} readOnly/>
              </FormItem>
            </Row>

          </Form>
        </Dialog>

      </section>
    );
  }
}
