import React, {Component} from 'react';
import {Balloon, Button, Feedback, Icon} from '@icedesign/base';
import axios from 'axios';
import {url} from '../../../../components/URL/index';
import qs from 'qs';

export default class ManualBill extends Component {
    constructor(props) {
        super(props);
        this.state = {
            visible: false,
            stuffId: sessionStorage.getItem('stuffId'),
            stuffName: sessionStorage.getItem('realName'),
        };
    }

    handleHide = (visible, record, code) => {
        if (code === 1) {
            axios
                .post(
                    `${url}/revenue/bill/reaccount`,
                    qs.stringify({
                        cno: record.cno,
                        createId: sessionStorage.getItem('stuffId'),
                        createName: sessionStorage.getItem('realName'),
                    }),
                )
                .then((response) => {
                    let jsondata = response.data;
                    if (jsondata.code === '0') {
                        Feedback.toast.success('出账成功');
                        this.props.refreshTable();
                    } else {
                        alert(jsondata.msg);
                    }
                })
                .catch((error) => {
                    Feedback.toast.error('网络错误请刷新后重试');
                });
        }
        this.setState({ visible: false });
    };

    handleVisible = (visible) => {
        this.setState({ visible });
    };

    render() {
        const { record } = this.props;
        const closeTrigger = (
            <a title="手动出账">
                <Icon type="survey" size="small" style={{ cursor: 'pointer', color: '#1DC11D' }} />
            </a>
        );
        const content = (
            <>
                <div style={styles.contentText}>确认手动出账吗?</div>
                <Button
                    id="confirmBtn"
                    size="small"
                    type="normal"
                    shape="warning"
                    style={{ marginRight: '5px' }}
                    onClick={(visible) => this.handleHide(visible, record, 1)}
                >
                    确认
                </Button>
                <Button id="cancelBtn" size="small" onClick={(visible) => this.handleHide(visible, record, 0)}>
                    关闭
                </Button>
            </>
        );
        return (
            <Balloon
                trigger={closeTrigger}
                triggerType="click"
                visible={this.state.visible}
                onVisibleChange={this.handleVisible}
            >
                {content}
            </Balloon>
        );
    }
}

const styles = {
    contentText: {
        padding: '5px 0 15px',
    },
};
