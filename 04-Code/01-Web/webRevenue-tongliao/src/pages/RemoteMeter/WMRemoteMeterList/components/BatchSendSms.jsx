import React, {Component} from 'react';
import {<PERSON><PERSON>, Dialog} from '@icedesign/base';
import axios from 'axios';
import {url} from '../../../../components/URL/index';
import qs from 'qs';

export default class BatchSendSms extends Component {
    constructor(props) {
        super(props);
        this.state = {
            visible: false,
            doLoading: false,
            stuffId: sessionStorage.getItem('stuffId'),
            stuffName: sessionStorage.getItem('realName'),
        };
    }

    // 打开确认弹窗
    handleVisible = (visible) => {
        const { selectedRecord } = this.props;
        selectedRecord.page = 1;
        selectedRecord.pageSize = 10;
        selectedRecord.watermeterCompany = '湖南威铭';
        axios.post(`${url}revenue/thirdPartyRemoteWatermeter/query`, qs.stringify(selectedRecord)).then((response) => {
            let jsondata = response.data;
            this.setState({
                visible: visible,
                total: jsondata.totalSize,
            });
        });
    };

    // 批量发送短信欠费通知
    handleOk() {
        const {selectedRecord, refreshTable} = this.props;
        this.setState({doLoading: true});
        selectedRecord.watermeterCompany = '湖南威铭';
        selectedRecord.billStatus = '0';
        axios.post(`${url}revenue/sms/SendBatchSmsThirdParty`, qs.stringify(selectedRecord)).then((res) => {
            if (res.data.code === '0') {
                let result = res.data.datas;
                alert(
                    `发送短信记录成功,欠费用户共：${result.totalNum}户,成功:${result.successNum}户,失败：${result.errorNum}户`,
                );
            } else {
                alert(`发送短信记录失败`);
            }
        });
        this.setState({visible: false, doLoading: false});
        refreshTable();
    }

    render() {
        const { visible, doLoading, total } = this.state;
        const footer = (
            <div align="center">
                <Button type="primary" onClick={() => this.handleOk()} loading={doLoading}>
                    确定
                </Button>
                <Button onClick={() => this.handleVisible(false)} style={{ marginLeft: 20 }} disabled={doLoading}>
                    取消
                </Button>
            </div>
        );
        return (
            <>
                <Button className="button" type="primary" onClick={() => this.handleVisible(true)}>
                    批量发送欠费短信
                </Button>

                <Dialog
                    style={{ width: 350 }}
                    visible={visible}
                    onClose={() => this.handleVisible(false)}
                    footer={footer}
                    title="提示"
                >
                    <div style={{ fontWeight: 600, marginTop: 5, marginBottom: 20, textAlign: 'center' }}>
                        根据条件搜索共：<span style={{ color: 'red' }}>{total}</span>户是否确认发送？
                    </div>
                </Dialog>
            </>
        );
    }
}
