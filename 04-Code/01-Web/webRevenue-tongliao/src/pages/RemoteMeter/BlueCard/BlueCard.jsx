import React, {Component} from 'react';
import {
  <PERSON>oon,
  <PERSON>ton,
  Dialog,
  <PERSON><PERSON>back,
  Field,
  Form,
  Icon,
  Input,
  Pagination,
  Radio,
  Select,
  Table
} from '@icedesign/base';
import IceContainer from '@icedesign/container'
import axios from 'axios';
import qs from 'qs';
import {url} from '../../../components/URL/index'

const FormItem = Form.Item;
const Tooltip = Balloon.Tooltip
const {Combobox} = Select;
const {Group: RadioGroup} = Radio;

export default class BlueCard extends Component {

  constructor(props) {
    super(props);
    this.state = {
      formValue: [],
      page: 1,
      pageSize: 10,
      totalSize: 0,
      changeVisible: false,
      visible: false,
      dataLoading: false,
      record: '',
      orderValues: {},
      cardValues: [],
      code: '',
      areaCopy: [],
      areas: [],
      flag: '',
      time: 0,
      save: false,
      status: false,
      isUse: false,
      roleNameList: [],
      createId: sessionStorage.getItem("stuffId"),
      createName: sessionStorage.getItem("realName"),
    }
    this.field = new Field(this, {autoUnmount: true})
  }

  componentDidMount() {
    this.getArea()
    this.queryRoleName()
  }

  /*查询操作员*/
  queryRoleName = () => {
    axios({
      method: 'get',
      url: `${url}revenue/iccard/createName` + '?n=' + Math.random(),
    })
    .then((response) => {
      if (response.data.code == 0) {
        this.setState({
          roleNameList: response.data.datas
        })
      }
    })
    .catch((error) => {
      Feedback.toast.error("请求错误：" + error);
    })
  }

  //查询
  queryUser(page, pageSize) {
    this.setState({dataLoading: true});
    let values = this.field.getValues();
    values.page = page
    values.pageSize = pageSize
    axios({
      method: 'post',
      url: `${url}revenue/remoteWatermeter/query`,
      data: qs.stringify(values),
    })
    .then((response) => {
      this.setState({
        formValue: response.data.datas,
        page: page,
        pageSize: pageSize,
        totalSize: response.data.totalSize,
        dataLoading: false
      })
    })
    .catch((error) => {
      Feedback.toast.error("请求错误：" + error);
      this.setState({dataLoading: false,})
    })
  }

  //翻页
  changePage(page) {
    const {pageSize} = this.state;
    this.queryUser(page, pageSize);
  }

  //改变pageSize
  onPageSizeChange(pageSize) {
    this.queryUser(1, pageSize);
  }

  //表格操作
  renderOper(record) {
    const edit = (<Icon type="survey" size="xs"
                        style={{color: "#1DC11D", cursor: 'pointer'}}
                        onClick={() => this.openEditDialog(record)}/>)
    return (
        <Tooltip trigger={edit} align='t' text='补卡'/>
    )
  }

  //补卡按钮
  openEditDialog(record) {
    this.field.setValue('cno', record.cno)
    this.field.setValue('watermeterId', record.watermeterId)
    this.field.setValue('createId', this.state.createId)
    this.setState({visible: true, record: record})
    // this.queryLastOrder(record.cno)
  }

  //查询最后一笔有效订单
  queryLastOrder(cno) {
    axios({
      method: 'post',
      url: `${url}revenue/order/findLastOrdersByCnoAndStatus`,
      data: qs.stringify({cno: cno, resultSize: 1}),
    }).then((response) => {
      if (response.data.code == "0") {
        this.setState({cardValues: response.data.datas})
      }
    }).catch((error) => {
      Feedback.toast.error("请求错误：" + error);
    })
  }

  /*关闭弹窗*/
  handleCancel() {
    this.setState({visible: false})
  }

  //补卡调用接口添加记录
  saveRecord() {
    this.field.validate((errors, values) => {
      console.log(values)
      const {createId, createName} = this.state
      // record.amount = values.amount
      // record.createId = createId
      values.createName = createName
      axios({
        method: 'post',
        url: `${url}revenue/blue/save`,
        data: values,
      }).then((response) => {
        if (response.data.code == '0') {
          Feedback.toast.success('补卡成功');
          this.setState({visible: false, save: false});
        } else {
          Feedback.toast.error('补卡失败，请重试')
        }
      }).catch((error) => {
        Feedback.toast.error("请求错误：" + error);
      })
    })
  }



  //重置
  reset() {
    this.field.reset()
    this.setState({areaCopy: null})
  }

  //渲染区域
  area() {
    const {areas} = this.state;
    return areas && areas.length > 0 ? areas.map((item) => {
      return <Option value={item.id}> {item.name} </Option>
    }) : void (0)
  }

  //区域查询
  getArea() {
    axios({
      method: "get",
      url: `${url}revenue/area/getAll`,
    }).then(response => {
      this.setState({
        areas: response.data.datas
      })
    }).catch(error => {
      Feedback.toast.error("AJAX请求错误", error)
    })
  }

  //区册下拉
  areaCopy() {
    const {areaCopy} = this.state
    if (areaCopy == null || areaCopy == undefined) {
      return <Option value='1' disabled> 请先选择区域 </Option>
    } else {
      return areaCopy && areaCopy.length > 0 ? areaCopy.map((item) => {
        return <Option value={item.id}>{item.regionName}</Option>
      }) : void (0)
    }
  }

  //区域onchange
  onChange(value) {
    this.field.setValue('areaId', value);
    //区册查询
    let areaId = [];
    areaId.areaId = value;
    axios({
      method: 'post',
      url: `${url}revenue/region/paging`,
      data: qs.stringify(areaId)
    }).then(response => {
      this.setState({
        areaCopy: response.data.datas
      })
    }).catch(error => {
      Feedback.toast.error("请求异常", error);
    })
    this.field.reset('regionId')
  }

  //字段过长隐藏
  omit(value) {
    const look = (
        <section style={{
          whiteSpace: "nowrap",
          textOverflow: 'ellipsis',
          overflow: 'hidden'
        }}>{value}</section>)
    return <Tooltip trigger={look} align='t' text={value}
                    style={{backgroundColor: '#f9f9f9'}}/>
  }

  render() {
    const footer = (
        <div align="center">
          <a onClick={() => this.saveRecord()}>
            <Button size="medium" type="primary">
              补卡
            </Button>
          </a>
          <Button size="medium" onClick={() => this.handleCancel()}>
            取消
          </Button>
        </div>
    )

    const {
      roleNameList,
      formValue,
      page,
      totalSize,
      pageSize,
      record,
      dataLoading,
      visible,
      createName
    } = this.state
    const {init} = this.field
    const formItemLayout = {
      labelCol: {fixedSpan: 8},
    }
    return (

        <div>

          <IceContainer title="搜索">

            <Form field={this.field}>
              <div style={{ display: 'flex', justifyContent: 'space-around' }}>
                <div style={{ display: 'flex', flexDirection: 'column' }}>
                  <FormItem label="用户编号：">
                    <Input {...init('cno')} placeholder="--请输入--" style={{ width: 170 }} />
                  </FormItem>
                  {/*<FormItem label="用户地址：">*/}
                  {/*  <Input {...init('address')} style={{ width: 170 }} placeholder="--请输入--" />*/}
                  {/*</FormItem>*/}
                </div>
                <div style={{ display: 'flex', flexDirection: 'column' }}>
                  <FormItem label="用户名称：">
                    <Input {...init('cname')} style={{ width: 170 }} placeholder="--请输入--" />
                  </FormItem>
                  {/*<FormItem label="小&emsp;&emsp;区：">*/}
                  {/*  <Combobox*/}
                  {/*      {...init('regionId')}*/}
                  {/*      placeholder="--请先选择片区--"*/}
                  {/*      fillProps="label"*/}
                  {/*      hasClear*/}
                  {/*      style={{ width: '170px' }}>*/}
                  {/*    {this.areaCopy()}*/}
                  {/*  </Combobox>*/}
                  {/*</FormItem>*/}
                </div>
                <div style={{ display: 'flex', flexDirection: 'column' }}>
                  <FormItem label="水表编号：">
                    <Input {...init('watermeterId')} style={{ width: 170 }} placeholder="--请输入--" />
                  </FormItem>
                </div>
              </div>
            </Form>

            <div style={{display: 'flex', justifyContent: 'center'}}>
              <Button type="primary" className="button"
                      onClick={() => this.queryUser()}
                      style={{marginRight: 10}}>
                <Icon type="search"/>查询
              </Button>
              <Button type="secondary" className="button"
                      onClick={() => this.reset()} style={{marginRight: 10}}>
                <Icon type="refresh"/>重置
              </Button>
            </div>
          </IceContainer>

          <IceContainer title="用户列表">
            <Table dataSource={formValue} isLoading={dataLoading}>
              <Table.Column title="用户编号" dataIndex="cno" align="center"/>
              <Table.Column title="水表编号" dataIndex="watermeterId"
                            align="center"/>
              <Table.Column title="用户名称" dataIndex="cname" align="center"/>
              <Table.Column title="用户地址" dataIndex="fixedAddress" align="center"
                            cell={(value) => this.omit(value)}/>
              {/*<Table.Column title="水表种类" dataIndex="watermeterKind" align="center" />*/}
              {/*<Table.Column title="水表厂家" dataIndex="watermeterCompany" align="center" />*/}
              <Table.Column title="操作"
                            cell={(value, index, record) => this.renderOper(
                                record)}
                            align="center" width="80px"/>
            </Table>
            <div style={{display: 'flex', justifyContent: 'flex-end'}}>
              <Pagination
                  pageSizeSelector="dropdown"
                  onPageSizeChange={(current) => this.onPageSizeChange(current)}
                  style={{marginTop: 15}}
                  current={page}
                  pageSize={pageSize}
                  total={totalSize}
                  size="small"
                  onChange={(current) => this.changePage(current)}
              />
              <div
                  style={{lineHeight: '53px', marginLeft: 10}}>共{totalSize}条记录
              </div>
            </div>
          </IceContainer>

          <Dialog style={{width: 550}} visible={visible}
                  onClose={() => this.handleCancel()}
                  onCancel={() => this.handleCancel()}
                  footer={footer}
                  title="补卡操作">

            <Form field={this.field}>

              <FormItem {...formItemLayout} label="用户编号：">
                <Input  {...init('cno')} style={{width: 230}} readOnly/>
              </FormItem>
              <FormItem {...formItemLayout} label="水表编号：">
                <Input  {...init('watermeterId')} style={{width: 230}} readOnly/>
              </FormItem>

              <FormItem {...formItemLayout} label="用户名：">
                <section style={{textAlign: 'left'}}>
                  <div style={{marginTop: 5}}>{record.cname}</div>
                </section>
              </FormItem>

              <FormItem label="收费(元)：" {...formItemLayout} >
                <Select {...init('amount',
                    {rules: [{required: true, message: '必填'}]})}
                        style={{width: 230}}
                        dataSource={[
                          {label: '请选择', value: ''},
                          {label: '0元', value: '0'},
                          {label: '10元', value: '10'}
                        ]}
                />
              </FormItem>


              <FormItem label="补卡原因：" {...formItemLayout} >
                <Input multiple {...init('reason')}
                       style={{width: 230}}/>
              </FormItem>

            </Form>

          </Dialog>

        </div>
    )
  }
}
