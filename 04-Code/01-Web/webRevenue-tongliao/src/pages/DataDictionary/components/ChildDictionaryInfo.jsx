import React, {Component} from 'react';
import {
  Button,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Field,
  Form,
  Grid,
  Icon,
  Input
} from '@icedesign/base';
import axios from 'axios';
import qs from 'qs';
import {url} from '../../../components/URL';

const { Row, Col } = Grid;

export default class ChildDictionaryInfo extends Component {
  static displayName = 'ChildDictionaryInfo';

  static defaultProps = {};

  constructor(props) {
    super(props);
    this.state = {
        visible: false,
        dataIndex: null,
        dataSource: [],
        save:false,
    };
    this.field = new Field(this,{
      autoUnmount: true
    });
  }

  onClose = () => {
    this.setState({
    visible: false,
    });
  };

  onOpen = (record) => {
    this.field.setValues({ ...record });
    this.setState({
        visible: true,
    });
  }

  editOrAdd = (record, opt) => {
    if(opt == 'edit'){
      return(
        <a onClick={() => this.onOpen(record)} title="编辑">
          <Icon type="edit" style={{ cursor:"pointer",color: "#1DC11D"}} size="small"/>
        </a>
      );
    }
    else{
      return(
        <Button type="primary" onClick={() => this.onOpen(record)} className="button">
            <Icon type="add"/>
            新增
        </Button>
      );
    }
  }

  //保存
  addWorkSave = (record, opt) => {   
    this.field.validate((errors, values) =>{
      if (errors) {
        return;
      }
      this.setState({
        save: true,
       });
      value.parentId = record.parentId;
      //后台保存成功，再更新table
      if(opt == 'edit'){
        axios({
          method: 'post',
          url: `${url}revenue/dictionary/update`,
          data: qs.stringify(values),
        })
        .then((response) => {
          this.setState({
            save: false,
           });
          var jsondata = response.data;
          if(jsondata.code == 0){
            Feedback.toast.success("修改成功");
            this.props.queryChildDataDictionary(record.parentId);
            this.setState({
              visible: false,
            });
          }
          else{
            alert(jsondata.msg);
          }
        })
        .catch((error) => {
          this.setState({
            save: false,
           });
          Feedback.toast.error("ajax请求异常：" + error)
        });
      }
      else{
        axios({
          method: 'post',
          url: `${url}revenue/dictionary/addItem`,
          data: qs.stringify(values),
        })
        .then((response) => {
          this.setState({
            save: false,
           });
          var jsondata = response.data;
          if(jsondata.code == 0){
            Feedback.toast.success("新增成功");
            this.props.queryChildDataDictionary(record.parentId);
            this.setState({
              visible: false,
            });
          }
          else{
            alert(jsondata.msg);
          }
        })
        .catch((error) => {
          this.setState({
            save: false,
           });
          Feedback.toast.error("ajax请求异常：" + error)
        });
      }
    });
  }

  render() {
    const init = this.field.init;
    const { record, opt } = this.props;
    const formItemLayout = {
        labelCol: { fixedSpan: 6 }
      };
    const footer = (
      <div>
        <Button type="primary" className="button" onClick={() => this.addWorkSave(record, opt)} loading={this.state.save}>
           保存
        </Button>
        <Button className="button" onClick={this.onClose} style={{ marginLeft: 20}}>
           取消
        </Button>
      </div>
    );

    return (
    <div style={styles.buttonStyle} >
        {this.editOrAdd(record, opt)}
        <Dialog
           style={{ width: 500 }}
           visible={this.state.visible}
           onClose={this.onClose}
           footer={footer}
           footerAlign="center"
           title={opt=='edit'?'编辑数据字典':'新增数据字典'}
        >
        <Form direction="ver" field={this.field} >
        <Input {...init('type', {initValue: 'ITEM'})} htmlType="hidden"/>
        <Row>
            <Form.Item label="字典名称：" {...formItemLayout}>
              <Input { ...init('name', {rules: {required: true, message: '必填'}}) } style={styles.inputStyle}/>
            </Form.Item>
            
        </Row>
        <Row>
            <Form.Item label="字典键值：" {...formItemLayout}>
              <Input { ...init('value', {rules: {required: true, message: '必填'}}) } style={styles.inputStyle}/>
            </Form.Item>
        </Row>
        <Row>
            <Form.Item label="顺&emsp;&emsp;序：" {...formItemLayout}>
              <Input { ...init('priority') } style={styles.inputStyle}/>
            </Form.Item>
        </Row>
        </Form>
      </Dialog>
    </div>
    );
  }
}
const styles = {
    buttonStyle:{ 
        display: 'inline-block', 
        marginRight: '2px',
    },
    inputStyle: {
        width: 180
    }
};