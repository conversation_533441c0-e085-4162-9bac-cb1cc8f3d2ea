/* eslint-disable react/no-unused-state */
import React, {Component} from 'react';
import {
  Balloon,
  Button,
  Feedback,
  Field,
  Form,
  Icon,
  Input,
  moment,
  Pagination,
  Table
} from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import qs from 'qs';
import {url} from '../../components/URL/index';
import ParentDictionaryInfo from './components/ParentDictionaryInfo';
import DeleteBalloon from './components/DeleteBalloon';
import ChildDictionaryTable from './components/ChildDictionaryTable';

const FormItem = Form.Item;
const Toast = Feedback.toast;
const Tooltip = Balloon.Tooltip;

export default class UserExchange extends Component {
  static displayName = 'UserExchange';
  static propTypes = {};
  static defaultProps = {};
  constructor(props) {
    super(props);
    this.state = {
      formValue: [],
      title: 1,
      selectWater: '',
      visible: false,
      visible1: false,
      visible2: false,
      dataLoading: false,
      list: [],
      status: 0,
      watermeterValue: '',
      tableValue: [],
      collapse: false,
      waterKind: [],
      areaCopy: [],
      areas: [],
      current: 1,
      pageSize: 10,
      totalSize: 0,
      submitButton: false,
      yesButton: false,
      noButton: false,
      record: {},
    }
    this.field = new Field(this, { autoUnmount: true });
  }

  componentDidMount() {
    this.queryDataDictionary(1);
    this.getArea()
  }

  /**
   * 查询、分页
   * page: 当前页数
   */
  queryDataDictionary = (page) => {
    this.setState({ dataLoading: true });
    let values = this.field.getValues();
    values.page = page;
    axios({
      method: 'post',
      url: `${url}revenue/dictionary/queryThemeList`,
      data: qs.stringify(values),
    })
      .then((response) => {
        this.setState({
          formValue: response.data.datas,
          current: page,
          pageSize: response.data.pageSize,
          totalSize: response.data.totalSize,
          dataLoading: false
        })
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
        this.setState({ dataLoading: false, })
      });
  }

  changePage = (page) => {
    this.queryDataDictionary(page);
  }

  /*表格操作*/
  renderOper(value, index, record) {
    return (
      <span style={styles.rowButtonStyle}>
        <ParentDictionaryInfo
          opt='edit'
          record={record}
          queryDataDictionary={() => this.queryDataDictionary(1)}
        />
        <ChildDictionaryTable parentId={record.id} />
        <DeleteBalloon
          id={record.id}
          queryDataDictionary={() => this.queryDataDictionary(1)}
          type="theme"
        />
      </span>
    );
  }

  /*格式化日期*/
  formatData(value, index, record) {
    const time = record.createTime ? moment(record.createTime).format('YYYY-MM-DD') : void (0);
    return time;
  }

  /*读卡*/
  doRead = () => {
    var i = MYAvtiveX.readcard()
    if (isNaN(i)) {
      let cardNo = i.split(';')[1];
      if (cardNo.length == 10) {
        this.field.setValue('watermeterId', cardNo);
      }
      else {
        this.field.setValue('cno', cardNo);
      }
      this.queryUser(1);
    } else {
      Feedback.toast.error("读卡失败：错误代码" + i)
    }

  }

  //重置
  reset = () => {
    this.field.reset()
    this.setState({ areaCopy: null })
  }

  //渲染区域
  area = () => {
    const { areas } = this.state;
    return areas && areas.length > 0 ? areas.map((item) => {
      return <Option value={item.id}> {item.name} </Option>
    }) : void (0)
  }

  //区域查询
  getArea = () => {
    axios({
      method: "get",
      url: `${url}revenue/area/getAll`,
    }).then(response => {
      this.setState({
        areas: response.data.datas
      })
    }).catch(error => {
      Feedback.toast.error("AJAX请求错误", error)
    })
  }

  //区册下拉
  areaCopy = () => {
    const { areaCopy } = this.state
    if (areaCopy == null || areaCopy == undefined) {
      return <Option value='1' disabled> 请先选择区域 </Option>
    }
    else {
      return areaCopy && areaCopy.length > 0 ? areaCopy.map((item) => {
        return <Option value={item.id}>{item.regionName}</Option>
      }) : void (0)
    }
  }

  //区域onchange
  onChange = (value) => {
    this.field.setValue('areaId', value);
    //区册查询
    let areaId = [];
    areaId.areaId = value;
    axios({
      method: 'post',
      url: `${url}revenue/region/paging`,
      data: qs.stringify(areaId)
    }).then(response => {
      this.setState({
        areaCopy: response.data.datas
      })
    }).catch(error => {
      Feedback.toast.error("请求异常", error);
    })
    this.field.reset('regionId')
  }

  /*隐藏表格过长数据*/
  omit(value) {
    const look = (
      <section style={{ whiteSpace: "nowrap", textOverflow: 'ellipsis', overflow: 'hidden' }}>{value}</section>)
    return <Tooltip trigger={look} align='t' text={value} style={{ backgroundColor: '#f9f9f9' }} />
  }

  //改变水表类型
  changeWaterType = (value, option) => {
    this.field.setValue('watermeterType', value);
    this.field.reset('watermeterKind');
    let temp = [];
    if (value == 'IC卡表') {
      temp = [
        { label: "请选择", value: '' },
        { label: '预付费2', value: '预付费2' },
        { label: '预付费5', value: '预付费5' },
      ];
    }
    else if (value == '机械表') {
      temp = [
        { label: "请选择", value: '' },
        { label: '机械表', value: '机械表' }
      ];
    }
    else if (value == '远传表') {
      temp = [
        { label: "请选择", value: '' },
        { label: '无线远传', value: '无线远传' },
        { label: '有线远传', value: '有线远传' }
      ];
    }
    this.setState({
      waterKind: temp,
    });
  }

  render() {
    const { formValue, totalSize, pageSize, current, dataLoading, waterKind } = this.state
    const { init } = this.field
    const formItemLayout = {
      labelCol: { fixedSpan: 6 }
    };
    return (
      <div>
        <IceContainer title="搜索">
          <Form field={this.field}>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <FormItem label="字典名称：" {...formItemLayout}>
                <Input {...init('name')} placeholder="请输入" style={styles.searchInput} />
              </FormItem>
              <FormItem label="字典键值："  {...formItemLayout}>
                <Input {...init('value')} placeholder="请输入" style={styles.searchInput} />
              </FormItem>
              <div style={{ width: 180 }}></div>
              <Button type="primary" className="button" onClick={() => this.queryDataDictionary(1)}>
                <Icon type="search" />查询
              </Button>
              <Button type="secondary" className="button" onClick={() => this.reset()}>
                <Icon type="refresh" />重置
              </Button>
            </div>
          </Form>
        </IceContainer>
        <IceContainer title="字典列表">
          <div align="right">
            <ParentDictionaryInfo
              queryDataDictionary={() => this.queryDataDictionary(1)}
            />
          </div>
          <Table dataSource={formValue} isLoading={dataLoading}>
            <Table.Column title="字典名称" dataIndex="name" align="center" />
            <Table.Column title="字典键值" dataIndex="value" align="center" />
            <Table.Column title="操作" cell={(value, index, record) => this.renderOper(value, index, record)}
              align="center" />
          </Table>
        </IceContainer>
        <Pagination
          style={{ textAlign: 'right', marginTop: 15 }}
          current={current}
          pageSize={pageSize}
          total={totalSize}
          onChange={this.changePage}
        />
      </div>
    )
  }
}

const styles = {
  searchInput: {
    width: 160
  },
  searchAddress: {
    width: 300
  },
  rowButtonStyle: {
    display: 'flex',
    justifyContent: "space-around"
  }
}
