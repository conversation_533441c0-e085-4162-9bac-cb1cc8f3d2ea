import React, {Component, Fragment} from 'react'
import {
  Balloon,
  Button,
  Dialog,
  <PERSON>edback,
  Field,
  Form,
  Grid,
  Icon,
  Input,
  Loading,
  Radio
} from '@icedesign/base';
import {url} from "../../../../components/URL";
import axios from "axios/index";
import qs from 'qs';

const {Row} = Grid;
const FormItem = Form.Item;
const {Tooltip} = Balloon;
const {Group: RadioGroup} = Radio;

export default class Invoicing extends Component {

  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      purchase: false,
      stuffId: sessionStorage.getItem("stuffId"),
      createName: sessionStorage.getItem("realName"),
    }
    this.field = new Field(this, {autoUnmount: true})
  }

  openDialog(record) {
    this.field.setValues({...record})
    this.setState({visible: true})
  }

  purchase() {
    this.field.validate((error, values) => {
      if (!error) {
        this.setState({purchase: true})
        let value = {}
        value.hno = values.hno
        value.headType = values.headType
        value.cname = values.cname
        value.taxnum = values.taxnum
        value.phone = values.phone
        value.accountNo = values.accountNo
        value.address = values.address
        value.email = values.email
        this.editInvoiceCustomerInfo(value)
      }
    })
  }


  //保存发票信息
  editInvoiceCustomerInfo(value) {
    this.setState({purchase: true})
    axios.post(`${url}revenue/invoiceCustomerInfo/update`, qs.stringify(value))
      .then((res) => {
        if (res.data.code === "0") {
          Feedback.toast.success("修改成功");
          this.props.queryDate()
          this.setState({visible: false, purchase: false})
        } else {
          Feedback.toast.error("修改失败");
          this.setState({purchase: false})
        }
      }).catch((e) => {
      Feedback.toast.error("网络错误请刷新后重试")
      this.setState({purchase: false})
    })
  }

  onClose() {
    this.setState({visible: false, purchase: false})
  }

  //检查邮箱
  checkedEmail = (rule, value, callback) => {
    let pattern = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/
    if (value) {
      if (!pattern.test(value)) {
        callback('邮箱格式不正确');
      } else {
        callback();
      }
    } else {
      callback();
    }
  }

  //检查手机号码
  checkedPhone = (rule, value, callback) => {
    let pattern = /(\(\d{3,4}\)|\d{3,4}-|\s)?\d{7,14}/
    if (value) {
      if (!pattern.test(value)) {
        callback('请填写正确的电话号码');
      } else {
        callback();
      }
    } else {
      callback('手机号码');
    }
  }

  render() {
    const {visible, purchase} = this.state
    const {record}=this.props
    const {init} = this.field
    const footer = (
      <span>
        <Button className="button" onClick={() => this.purchase()} loading={purchase} type="primary"
                style={{marginRight: 20}}>
          提交
        </Button>

        <Button className="button" onClick={() => this.onClose()}>
         取消
        </Button>

      </span>
    )
    const buyItemLayout = {
      labelCol: {fixedSpan: 8}
    }

    const edit = (
      <Icon type="edit" style={{color: "#3399ff", cursor: "pointer"}} size="small" onClick={() => this.openDialog(record)}/>
    )

    return (
      <Fragment>

        <Tooltip trigger={edit} align='t' text='编辑'/>

        <Dialog
          visible={visible}
          onClose={() => this.onClose()}
          title='开票信息'
          footer={footer}
          footerAlign="center"
          style={{width: 600}}>
          <Loading shape="fusion-reactor" tip="开票中..." visible={purchase}>
            <div style={{fontWeight: 600, marginTop: 5, marginBottom: 20}}>请填写并确认开票信息：</div>
            <Form direction="hoz" field={this.field}>

              <Row>
                <FormItem label="用户户号：" {...buyItemLayout}>
                  <Input {...init('hno')} placeholder="请输入" style={{width: 250, backgroundColor: '#eee'}} readOnly/>
                </FormItem>
              </Row>

              <Row>
                <FormItem label="抬头类型：" {...buyItemLayout}>
                  <RadioGroup {...init('headType', {rules: [{required: true, message: '必填'}]})}
                              dataSource={[
                                {value: 1, label: "个人"},
                                {value: 2, label: "企业或事业单位"},
                              ]} defaultValue={1}/>
                </FormItem>
              </Row>

              <Row>
                <FormItem label="发票抬头：" {...buyItemLayout}>
                  <Input maxLength={100} {...init('cname', {rules: [{required: true, message: '必填'}]})}
                         placeholder="请输入"
                         style={{width: 250}}/>
                </FormItem>
              </Row>

              {
                this.field.getValue('headType') === 2 ?
                  <Row>
                    <FormItem label="购方税号：" {...buyItemLayout}>
                      <Input {...init('taxnum', {rules: [{required: true, min: 6, message: "税号长度不能小于6位"}]})}
                             placeholder="请输入"
                             style={{width: 250}} maxLength={20}/>
                    </FormItem>
                  </Row> : void (0)
              }

              {
                this.field.getValue('headType') === 2 ?
                  <Row>
                    <Row>
                      <FormItem label="企业电话：" {...buyItemLayout}>
                        <Input {...init('phone', {rules: [{required: true, message: '必填'}]})} placeholder="请输入"
                               style={{width: 250}}/>
                      </FormItem>
                    </Row>
                  </Row> :
                  <Row>
                    <FormItem label="电话：" {...buyItemLayout}>
                      <Input {...init('phone', {rules: [{required: true, validator: this.checkedPhone}]})}
                             placeholder="请输入"
                             style={{width: 250}}/>
                    </FormItem>
                  </Row>
              }

              {
                this.field.getValue('headType') === 2 ?
                  <Row>
                    <FormItem label="开户行及账号：" {...buyItemLayout}>
                      <Input maxLength={100} {...init('accountNo')} placeholder="请输入" style={{width: 250}}/>
                    </FormItem>
                  </Row> : void (0)
              }

              {
                this.field.getValue('headType') === 2 ?
                  <Row>
                    <FormItem label="企业地址：" {...buyItemLayout} >
                      <Input maxLength={100} {...init('address')} placeholder="请输入" style={{width: 250}}/>
                    </FormItem>
                  </Row>
                  : void (0)
              }

              <Row>
                <FormItem label="推送邮箱：" {...buyItemLayout} style={{color: 'red'}}>
                  <Input {...init('email', {rules: [{validator: this.checkedEmail}]})} placeholder="请输入"
                         style={{width: 250}}/>
                </FormItem>
              </Row>

              {/*<Row>
                <FormItem label="发票内容：" {...buyItemLayout}>
                  <Input {...init('descr')} style={{border: 0, width: 350, backgroundColor: '#eee'}} readOnly/>
                </FormItem>
              </Row>*/}
            </Form>
          </Loading>
        </Dialog>
      </Fragment>
    )
  }
}
