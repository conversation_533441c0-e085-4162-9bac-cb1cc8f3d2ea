import {
  But<PERSON>,
  <PERSON><PERSON>back,
  <PERSON>,
  Form,
  Grid,
  Icon,
  Input,
  Select
} from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import qs from 'qs';
import React, {Component, Fragment} from 'react';
import {url} from '../../../components/URL/index';
import {formItemLayout, span} from '../../../common/FormCollocation';
import styles from './index.module.scss'
import BasicsTable from "../../../common/BasicsTable";
import EditInvoice from "./components/editInvoice";
import Invoice from "./components/Invoice";
import DeleteInvoice from "./components/deleteInvoice"

const FormItem = Form.Item
const {Row, Col} = Grid;

export default class InvoiceInformationMaintenance extends Component {
  constructor(props) {
    super(props);
    this.state = {
      dataSource: [],
      printNameList: [],
      createNameList: [],
      page: 1,
      pageSize: 10,
      total: 0,
      dataLoading: false,
      createName: sessionStorage.getItem("realName")
    }
    this.field = new Field(this, {autoUnmount: true});
  }

  componentDidMount() {
    this.queryDate(1, 10);
  }

  //查询
  queryDate(page, pageSize) {
    this.setState({dataLoading: true});
    let values = this.field.getValues();
    values.page = page;
    values.pageSize = pageSize;
    axios.post(`${url}revenue/invoiceCustomerInfo/listByPage`, qs.stringify(values))
      .then((response) => {
        this.setState({
          dataSource: response.data.datas,
          page: response.data.page,
          pageSize: response.data.pageSize,
          total: response.data.totalSize,
          dataLoading: false
        })
      }).catch((error) => {
      Feedback.toast.error("请求错误：" + error);
      this.setState({dataLoading: false,})
    });
  }

  //翻页
  changePage(page) {
    const {pageSize} = this.state;
    this.queryDate(page, pageSize)
  }

  //重置
  reset() {
    this.field.reset();
  }

  export = () => {
    let values = this.field.getValues();
    console.log(values)
    let url1 = `${url}/revenue/invoiceCustomerInfo/export?n=1`;

    if (values.hno) {
      url1 += '&hno=' + values.hno;
    }
    if (values.cname) {
      url1 += '&cname=' + values.cname;
    }
    if (values.phone) {
      url1 += '&phone=' + values.phone;
    }
    if (values.headType) {
      url1 += '&headType=' + values.headType;
    }
    window.open(encodeURI(url1), 'about:blank');
  };
  //改变显示记录数
  onPageSizeChange(pageSize) {
    this.queryDate(1, pageSize)
  }

  render() {
    const {dataSource, page, pageSize, total, dataLoading} = this.state
    const {init} = this.field
    const columns = [
      {
        title: '用户户号',
        dataIndex: 'hno',
        key: 1
      },
      {
        title: '抬头类型',
        dataIndex: 'headType',
        key: 2,
        cell: (value, index, record) => {
          return (
            value === 1 ? '个人' : '企业或事业单位'
          )
        }
      },
      {
        title: '发票抬头',
        dataIndex: 'cname',
        key: 3
      },
      {
        title: '手机号码',
        dataIndex: 'phone',
        key: 4
      },
      {
        title: '税号',
        dataIndex: 'taxnum',
        key: 5
      },
      {
        title: '地址',
        dataIndex: 'address',
        key: 6
      },
      {
        title: '开户行及账号',
        dataIndex: 'accountNo',
        key: 7
      },
      {
        title: '邮箱',
        dataIndex: 'email',
        key: 8
      },
      {
        title: '操作',
        width: 120,
        cell: (value, index, record) => {
          return (
            <div style={{display: 'flex', justifyContent: 'space-around'}}>
              <EditInvoice record={record} queryDate={() => this.queryDate(page, pageSize)}/>
              <DeleteInvoice record={record} queryDate={() => this.queryDate(page, pageSize)}/>
            </div>
          )
        },
        key: 9
      },
    ];
    return (
      <Fragment>
        <IceContainer title="开票信息维护">
          <Form field={this.field}>
            <Row wrap>
              <Col {...span}>
                <FormItem label="用户户号：" {...formItemLayout}>
                  <Input {...init('hno')} placeholder="请输入用户户号" style={{width: 220}}/>
                </FormItem>
              </Col>
              <Col {...span}>
                <FormItem label="手机号码：" {...formItemLayout}>
                  <Input {...init('phone')} placeholder="请输入手机号码" style={{width: 220}}/>
                </FormItem>
              </Col>
              <Col {...span}>
                <FormItem label="发票抬头：" {...formItemLayout}>
                  <Input {...init('cname')} placeholder="请输入发票抬头" style={{width: 220}}/>
                </FormItem>
              </Col>
              <Col {...span}>
                <FormItem label="抬头类型：" {...formItemLayout}>
                  <Select  {...init('headType')} dataSource={[
                    {value: 1, label: "个人"},
                    {value: 2, label: "企业或事业单位"},
                  ]} placeholder="请选择抬头类型" className={styles.selectWidth}/>
                </FormItem>
              </Col>
            </Row>
          </Form>
          <div align="center">
            <Button type="primary" className="button" onClick={() => this.queryDate(1, 10)}>
              <Icon type="search"/>查询
            </Button>
            <Button type="secondary" className="button" onClick={() => this.reset()} style={{marginLeft: 20}}>
              <Icon type="refresh"/>重置
            </Button>
          </div>
        </IceContainer>
        <IceContainer title="开票信息列表">
          <Invoice queryDate={() => this.queryDate(1, 10)}/>
          &nbsp;&nbsp;
          <Button type="primary" className="button" onClick={() => this.export()}
                  style={{marginBottom: 10}}>导出</Button>
          <BasicsTable columns={columns} dataSource={dataSource} total={total} pageSize={pageSize} page={page}
                       dataLoading={dataLoading}
                       changePage={(value) => this.changePage(value)}
                       onPageSizeChange={(value) => this.onPageSizeChange(value)} pageSizeList={[10, 50, 100]}/>
        </IceContainer>
      </Fragment>
    )
  }
}
