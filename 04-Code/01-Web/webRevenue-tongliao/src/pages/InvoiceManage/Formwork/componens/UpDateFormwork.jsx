import React, {Component} from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>back,
  Field,
  Form,
  Grid,
  Icon,
  Input,
} from '@icedesign/base';
import axios from 'axios';
import qs from 'qs';
import {url} from '../../../../components/URL/index'
import getLodop from '../../../../common/LodopFuncs'

const { Row } = Grid;
const FormItem = Form.Item
var LODOP = getLodop();
export default class AddFormwork extends Component {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      createId: sessionStorage.getItem("stuffId"),
      createName: sessionStorage.getItem("realName")
    };
    this.field = new Field(this, { autoUnmount: true })
  }

  //打开对话框
  onOpen = (record) => {
    this.field.setValues({ ...record })
    this.setState({ visible: true, });
  };

  //关闭对话框
  onClose = () => {
    this.setState({ visible: false })
  }

  /*弹窗确定回掉函数*/
  handleOk() {
    this.field.validate((errors, values) => {
      values.createId = this.state.createId
      values.createName = this.state.createName
      if (errors) {
        return
      } else {
        axios({
          method: 'post',
          url: `${url}revenue/printTemplate/update`,
          data: qs.stringify(values),
        })
          .then((response) => {
            if (response.data.code == "0") {
              Feedback.toast.success("修改成功");
              this.setState({ visible: false });
              this.props.queryFormwork()
              this.field.reset()
            } else {
              Feedback.toast.error("修改成功" + response.data.msg);
            }
          })
          .catch((error) => {
            Feedback.toast.error("请求错误：" + error);
          });
      }
    })
  }

  //循环设计
  printDesignAgain() {
    eval(this.field.getValue('templateCode'))
    if (LODOP.CVERSION) {
      CLODOP.On_Return = function (TaskID, Value) {
        this.field.setValue('templateCode', Value)
      };
    }
    this.field.setValue('templateCode', LODOP.PRINT_DESIGN())
  }


  render() {
    const record = this.props.record
    const { init } = this.field;
    const formItemLayout = { labelCol: { span: 6 } };
    const footer = (
      <div style={{ marginTop: 20 }} align="center">
        <Button type="primary" loading={this.state.save} onClick={() => this.handleOk()}>
          确定
        </Button>
        <Button onClick={this.onClose} style={{ marginLeft: 20 }}>
          取消
        </Button>
      </div>
    );
    return (
      <div style={styles.buttonStyle}>
        <Icon type="survey" size="small" style={{ color: "#1DC11D", cursor: 'pointer', marginRight: 15 }}
          onClick={() => this.onOpen(record)} title='修改' />
        <Dialog
          minMargin={10}
          style={{ width: 600 }}
          visible={this.state.visible}
          onClose={this.onClose}
          footer={footer}
          title='添加模板信息'
          footerAlign="center"
        >
          <Form field={this.field}>
            <Row style={{ display: 'flex', justifyContent: 'center', marginBottom: 10 }}>
              <Button type="primary" style={{ marginLeft: 20 }} onClick={() => this.printDesignAgain()}>
                修改发票
              </Button>
            </Row>
            <Row>
              <FormItem {...formItemLayout} label="模板名称:">
                <Input {...init('templateName', { rules: [{ required: true, message: "模板名称必填" }] })} style={{ width: 400 }} />
              </FormItem>
            </Row>
            <Row>
              <FormItem {...formItemLayout} label="模板数据:">
                <Input {...init('templateCode', { rules: [{ required: true, message: "模板数据必填" }] })} multiple
                  style={{ width: 400 }} rows={10} />
              </FormItem>
            </Row>
          </Form>
        </Dialog>
      </div>
    );
  }
}

const styles = {
  buttonStyle: {
    display: "inline-block",
    marginRight: "2px"
  }
};
