import React, {Component, Fragment} from 'react'
import {
  Balloon,
  Button,
  Feedback,
  Field,
  Form,
  Grid,
  Icon,
  Radio
} from '@icedesign/base';
import {url} from "../../../../components/URL";
import axios from "axios/index";
import qs from 'qs';

const {Row} = Grid;
const FormItem = Form.Item;
const {Tooltip} = Balloon;
const {Group: RadioGroup} = Radio;

export default class DeleteInfo extends Component {

  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      purchase: false,
      stuffId: sessionStorage.getItem("stuffId"),
      createName: sessionStorage.getItem("realName"),
    }
    this.field = new Field(this, {autoUnmount: true})
  }

  //删除
  handleHide(record) {
    this.setState({purchase: true})
    axios.post(`${url}revenue/businessInfo/delete`, qs.stringify({id:record.id}))
      .then((res) => {
        if (res.data.code === "0") {
          Feedback.toast.success("删除成功");
          this.props.queryDate()
          this.setState({visible: false, purchase: false})
        } else {
          Feedback.toast.error("删除失败");
          this.setState({purchase: false})
        }
      }).catch((e) => {
      Feedback.toast.error("网络错误请刷新后重试")
      this.setState({purchase: false})
    })
  }

  //点击提示
  handleVisible = (visible) => {
    this.setState({visible});
  };


  render() {
    const {visible, purchase} = this.state
    const {record} = this.props

    const visibleTrigger = (
      <Icon title="删除" type="ashbin" size="small" style={{color: "#FF3333", cursor: 'pointer'}}/>
    );
    const content = (
      <div>
        <div>确认删除？</div>
        <Button id="confirmBtn" size="small" type="normal" shape="warning" style={{marginRight: '5px'}}
                onClick={() => this.handleHide(record)}>确认</Button>
        <Button id="cancelBtn" size="small" onClick={() => this.handleVisible()}>关闭</Button>
      </div>
    );

    return (
      <Fragment>

        <Balloon
          trigger={visibleTrigger}
          triggerType="click"
          visible={visible}
          onVisibleChange={this.handleVisible}>
          {content}
        </Balloon>

      </Fragment>
    )
  }
}
