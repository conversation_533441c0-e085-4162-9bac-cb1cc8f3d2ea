import {
  But<PERSON>,
  <PERSON><PERSON>back,
  <PERSON>,
  Form,
  Grid,
  Icon,
  Input,
  Upload
} from '@icedesign/base';
import <PERSON><PERSON>ontainer from '@icedesign/container';
import axios from 'axios';
import qs from 'qs';
import React, {Component, Fragment} from 'react';
import {url} from '../../../components/URL/index';
import {formItemLayout, span} from '../../../common/FormCollocation';
import BasicsTable from "../../../common/BasicsTable";
import AddBusinessInfo from "./components/addBusinessInfo";
import EditInfo from "./components/editInfo";
import DeleteInfo from "./components/deleteInfo"

const FormItem = Form.Item
const {Row, Col} = Grid;

export default class BusinessInfoManage extends Component {
  constructor(props) {
    super(props);
    this.state = {
      dataSource: [],
      printNameList: [],
      createNameList: [],
      page: 1,
      pageSize: 10,
      total: 0,
      dataLoading: false,
      createName: sessionStorage.getItem("realName")
    }
    this.field = new Field(this, {autoUnmount: true});
  }

  componentDidMount() {
    this.queryDate(1, 10);
  }

  //查询
  queryDate(page, pageSize) {
    this.setState({dataLoading: true});
    let values = this.field.getValues();
    values.page = page;
    values.pageSize = pageSize;
    axios.post(`${url}revenue/businessInfo/listPage`, qs.stringify(values))
    .then((response) => {
      this.setState({
        dataSource: response.data.datas,
        page: response.data.page,
        pageSize: response.data.pageSize,
        total: response.data.totalSize,
        dataLoading: false
      })
    }).catch((error) => {
      Feedback.toast.error("请求错误：" + error);
      this.setState({dataLoading: false,})
    });
  }

  //翻页
  changePage(page) {
    const {pageSize} = this.state;
    this.queryDate(page, pageSize)
  }

  //重置
  reset() {
    this.field.reset();
  }

  export = () => {
    let values = this.field.getValues();
    console.log(values)
    let url1 = `${url}revenue/businessInfo/export?n=1`;

    if (values.hno) {
      url1 += '&hno=' + values.hno;
    }
    if (values.cname) {
      url1 += '&cname=' + values.cname;
    }
    if (values.creditName) {
      url1 += '&creditName=' + values.creditName;
    }
    if (values.creditCode) {
      url1 += '&creditCode=' + values.creditCode;
    }
    if (values.businessNo) {
      url1 += '&businessNo=' + values.businessNo;
    }
    if (values.organizationCode) {
      url1 += '&organizationCode=' + values.organizationCode;
    }
    if (values.taxNo) {
      url1 += '&taxNo=' + values.taxNo;
    }
    if (values.institutionNo) {
      url1 += '&institutionNo=' + values.institutionNo;
    }
    if (values.socialNo) {
      url1 += '&socialNo=' + values.socialNo;
    }

    window.open(encodeURI(url1), 'about:blank');
  };
  downloadTemp = () => {
    let values = this.field.getValues();
    console.log(values)
    let url1 = `${url}revenue/businessInfo/downLoadTemplate?n=1`;
    window.open(encodeURI(url1), 'about:blank');
  };
  uploadSuccess = (response) => {
    if (response.code == 0) {
      Feedback.toast.success('导入成功');
      this.queryDate(1, 10);
    }
  }

  uploadError = (response) => {
    if (response.response.code == 1) {
      alert(response.response.msg);
    }
  }
  //改变显示记录数
  onPageSizeChange(pageSize) {
    this.queryDate(1, pageSize)
  }

  render() {
    const {dataSource, page, pageSize, total, dataLoading} = this.state
    const {init} = this.field
    const columns = [{
      title: '用户户号', dataIndex: 'hno', key: 1
    }, {
      title: '用户名称', dataIndex: 'cname', key: 2,
    }, {
      title: '信用主体名称', dataIndex: 'creditName', key: 3
    }, {
      title: '统一社会信用代码', dataIndex: 'creditCode', key: 4
    }, {
      title: '工商注册号', dataIndex: 'businessNo', key: 5
    }, {
      title: '组织机构代码', dataIndex: 'organizationCode', key: 6
    }, {
      title: '税务登记号', dataIndex: 'taxNo', key: 7
    }, {
      title: '事业单位证书号', dataIndex: 'institutionNo', key: 8
    }, {
      title: '社会组织登记证号', dataIndex: 'socialNo', key: 9
    }, {
      // eslint-disable-next-line react/no-unstable-nested-components
      title: '操作', width: 120, cell: (value, index, record) => {
        return (<div style={{display: 'flex', justifyContent: 'space-around'}}>
          <EditInfo record={record}
                       queryDate={() => this.queryDate(page, pageSize)}/>
          <DeleteInfo record={record}
                      queryDate={() => this.queryDate(page, pageSize)}/>
        </div>)
      }, key: 10
    },];
    return (<Fragment>
      <IceContainer title="企事业单位工商税务信息管理">
        <Form field={this.field}>
          <Row wrap>
            <Col {...span}>
              <FormItem label="用户户号：" {...formItemLayout}>
                <Input {...init('hno')} placeholder="请输入用户户号"
                       style={{width: 220}}/>
              </FormItem>
            </Col>
            <Col {...span}>
              <FormItem label="用户名称：" {...formItemLayout}>
                <Input {...init('cname')} placeholder="请输入用户名称"
                       style={{width: 220}}/>
              </FormItem>
            </Col>
            <Col {...span}>
              <FormItem label="信用主体名称：" {...formItemLayout}>
                <Input {...init('creditName')}
                       placeholder="请输入信用主体名称"
                       style={{width: 220}}/>
              </FormItem>
            </Col>
            <Col {...span}>
              <FormItem label="统一社会信用代码：" {...formItemLayout}>
                <Input {...init('creditCode')}
                       placeholder="请输入统一社会信用代码"
                       style={{width: 220}}/>
              </FormItem>
            </Col>
            <Col {...span}>
              <FormItem label="工商注册号：" {...formItemLayout}>
                <Input {...init('businessNo')}
                       placeholder="请输入工商注册号" style={{width: 220}}/>
              </FormItem>
            </Col>
            <Col {...span}>
              <FormItem label="组织机构代码：" {...formItemLayout}>
                <Input {...init('organizationCode')}
                       placeholder="请输入组织机构代码"
                       style={{width: 220}}/>
              </FormItem>
            </Col>
            <Col {...span}>
              <FormItem label="税务登记号：" {...formItemLayout}>
                <Input {...init('taxNo')} placeholder="请输入税务登记号"
                       style={{width: 220}}/>
              </FormItem>
            </Col>
            <Col {...span}>
              <FormItem label="事业单位证书号：" {...formItemLayout}>
                <Input {...init('institutionNo')}
                       placeholder="请输入事业单位证书号"
                       style={{width: 220}}/>
              </FormItem>
            </Col>
            <Col {...span}>
              <FormItem label="社会组织登记证号：" {...formItemLayout}>
                <Input {...init('socialNo')}
                       placeholder="请输入社会组织登记证号"
                       style={{width: 220}}/>
              </FormItem>
            </Col>
          </Row>
        </Form>
        <div align="center">
          <Button type="primary" className="button"
                  onClick={() => this.queryDate(1, 10)}>
            <Icon type="search"/>查询
          </Button>
          <Button type="secondary" className="button"
                  onClick={() => this.reset()} style={{marginLeft: 20}}>
            <Icon type="refresh"/>重置
          </Button>
        </div>
      </IceContainer>
      <IceContainer title="企事业单位工商税务信息列表">
        <AddBusinessInfo queryDate={() => this.queryDate(1, 10)}/>
        &nbsp;&nbsp;
        <Button type="primary" className="button"
                onClick={() => this.export()}
                style={{marginBottom: 10}}>导出</Button>
        &nbsp;&nbsp;
        <Button type="primary" className="button"
                onClick={() => this.downloadTemp()}
                style={{marginBottom: 10}}>下载模板</Button>
        &nbsp;&nbsp;
        <Upload
            action={`${url}revenue/businessInfo/importData`}
            onSuccess={this.uploadSuccess}
            onError={this.uploadError}
            showUploadList={false}
            withCredentials={true}
        >
          <Button type="primary" className="button" style={{marginBottom: 10}}>
            <Icon type="share" />
            导入数据
          </Button>
        </Upload>
        <BasicsTable columns={columns} dataSource={dataSource} total={total}
                     pageSize={pageSize} page={page}
                     dataLoading={dataLoading}
                     changePage={(value) => this.changePage(value)}
                     onPageSizeChange={(value) => this.onPageSizeChange(value)}
                     pageSizeList={[10, 50, 100]}/>
      </IceContainer>
    </Fragment>)
  }
}
