.modal-size {
    width: 75%;
    min-width: 1000px;
    min-height: 180px;

    ul {
        li {
            display: flex;

            padding-top: 6px;
            padding-bottom: 6px;

            span {
                width: 160px;
            }
        }
    }
}

.notice-pagination {
    margin-top: 24px;
    padding-right: 16px;
    padding-left: 16px;

    .next-pagination {
        display: flex;

        justify-content: flex-end;
    }
}

.notification-pagination {
    display: flex;

    margin-top: 24px;
    padding-right: 16px;
    padding-left: 16px;

    justify-content: space-between;

    .next-pagination {
        display: flex;

        justify-content: flex-end;
    }
}

.add-record-form {
    .add-record-form-item {
        display: flex;

        .next-form-item-control {
            flex-grow: 1;
        }
    }

    .add-record-form-item-content {
        display: flex;

        .next-form-item-control {
            flex-grow: 1;
        }
    }

    .add-record-input {
        width: 100%;
    }
}

.add-record {
    display: flex;

    margin-bottom: 16px;

    justify-content: flex-end;
}

.info-record-content {
    .info-record-content-label {
        overflow: hidden;

        border-right: 1px solid #eeeff3;
        border-left: 1px solid #eeeff3;
        padding: 12px 12px;

        font-weight: bold;
        text-align: center;
        text-overflow: ellipsis;
        word-break: break-all;
    }

    .info-record-content-item {
        overflow-y: scroll;

        border: 1px solid #eeeff3;
        padding: 12px;
        width: 100%;
        height: 480px;

        &.notice {
            overflow-y: auto;

            height: 240px;
        }
    }

    .info-record-content-item-img-list {
        display: flex;

        height: 280px;

        .img-box {
            margin-right: 12px;
            margin-left: 12px;
            border: 1px solid #f4f4f4;
            height: 240px;

            img {
                width: auto;
                max-width: 100%;
                height: auto;
                max-height: 240px;
            }
        }
    }
}
