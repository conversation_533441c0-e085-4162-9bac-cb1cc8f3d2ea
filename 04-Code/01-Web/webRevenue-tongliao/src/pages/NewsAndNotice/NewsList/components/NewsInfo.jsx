import React, {Component} from 'react';
import {Button, Dialog, Table} from '@icedesign/base';
import {url} from '../../../../components/URL/index';

export class NoticeInfo extends Component {
    renderImgList = () => {
        return;
    };
    render() {
        const { infoModal, setInfoModal, dataRecord } = this.props;
        const { content, imgList } = dataRecord;
        return (
            <Dialog
                className="modal-size"
                visible={infoModal}
                title="查看新闻详情"
                footer={
                    <Button type="primary" onClick={() => setInfoModal(false)}>
                        关闭
                    </Button>
                }
                footerAlign="center"
                onClose={() => setInfoModal(false)}
            >
                <Table dataSource={[dataRecord]}>
                    <Table.Column title="新闻标题" dataIndex="title" align="center" />
                    <Table.Column title="新闻创建人" dataIndex="createName" align="center" />
                    <Table.Column title="创建人ID" dataIndex="createId" align="center" />
                    <Table.Column title="创建时间" dataIndex="createTime" align="center" />
                </Table>
                <div className="info-record-content">
                    <div className="info-record-content-label">预览图片</div>
                    <div className="info-record-content-item info-record-content-item-img-list">
                        {imgList?.length
                            ? imgList.map((item) => (
                                  <div key={item} className="img-box">
                                      <img src={`${url}${item}`} />
                                  </div>
                              ))
                            : null}
                    </div>
                </div>
                <div className="info-record-content">
                    <div className="info-record-content-label">新闻内容</div>
                    <div className="info-record-content-item" dangerouslySetInnerHTML={{ __html: content }} />
                </div>
            </Dialog>
        );
    }
}

export default NoticeInfo;
