import React, {Component} from 'react';
import {
  But<PERSON>,
  DatePicker,
  Field,
  Form,
  Icon,
  Input,
  Select
} from '@icedesign/base';

const { RangePicker } = DatePicker;
export class ApplySearch extends Component {
    field = new Field(this);
    searchApplication = () => {
        const values = this.field.getValues();
        const params = {
            ...values,
            ...this.props.pagination,
            page: 1,
        };
        console.log(params);
        if (this.props.tabKey === 'not') {
            this.props.getNotList(params);
        } else {
            this.props.getHasList(params);
        }
    };
    timeOnchange(str) {
        this.field.setValue('startTime', str[0]);
        this.field.setValue('endTime', str[1]);
    }
    reset() {
        this.field.reset();
    }
    render() {
        const { init } = this.field;
        return (
            <Form field={this.field}>
                <div className="search-form-apply">
                    <Form.Item className="search-form-apply-form-item" label="来源编号：">
                        <Input {...init('sourceId')} placeholder="请输入" />
                    </Form.Item>
                    <Form.Item className="search-form-apply-form-item" label="消息类型：">
                        <Select {...init('msgType')} placeholder="请选择">
                            <li value="REVENUE_APPLY">申请消息</li>
                            <li value="REVENUE_AUDIT">审批消息</li>
                        </Select>
                    </Form.Item>
                    <Form.Item className="search-form-apply-form-item" label="消息标题：">
                        <Input {...init('msgTitle')} placeholder="请输入" />
                    </Form.Item>
                    {/* <Form.Item className="search-form-apply-form-item" label="阅读状态：">
                        <Select {...init('readStatus')} placeholder="请选择">
                            <li value={0}>未读</li>
                            <li value={1}>已读</li>
                        </Select>
                    </Form.Item> */}
                </div>
                <div className="search-form-apply">
                    <Form.Item className="search-form-apply-form-item" label="消息时间：">
                        <RangePicker
                            {...init('ApplicationTime', {
                                props: { onChange: (val, str) => this.timeOnchange(str) },
                            })}
                        />
                    </Form.Item>
                </div>
                <Form.Item style={{ justifyContent: 'center' }}>
                    <Button
                        type="primary"
                        className="button"
                        onClick={() => this.searchApplication()}
                        style={{ marginRight: 10 }}
                    >
                        <Icon type="search" />
                        查询
                    </Button>
                    <Button
                        type="secondary"
                        className="button"
                        onClick={() => this.reset()}
                        style={{ marginRight: 10 }}
                    >
                        <Icon type="refresh" />
                        重置
                    </Button>
                </Form.Item>
            </Form>
        );
    }
}

export default ApplySearch;
