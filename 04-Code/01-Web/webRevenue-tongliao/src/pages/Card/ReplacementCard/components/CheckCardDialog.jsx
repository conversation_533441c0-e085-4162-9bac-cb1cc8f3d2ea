import React, {Component} from 'react';
import {Button, Dialog, Field, Form, Icon, Input, Select} from '@icedesign/base'
import {url} from "../../../../components/URL";
import axios from "axios/index";
import qs from 'qs';
import {Feedback} from "@icedesign/base/index";

const FormItem = Form.Item;
const {Combobox} = Select;

export default class CheckCardDialog extends Component {

  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      orderValues: [],
      save: false,
      createId: sessionStorage.getItem("stuffId"),
      createName: sessionStorage.getItem("realName")
    }
    this.field = new Field(this, {autoUnmount: true});
  }

  //打开检查卡补卡弹窗
  openCheckCard() {
    try {
      let reulst = hxdll.chk_card()
      if (reulst == '9') {
        let readstr = hxdll.copyusercx()
        let cardNo = readstr.split(',')[0] //表内卡号
        let time = readstr.split(',')[1]//表内购水次数
        let flag = readstr.split(',')[2] //标识位
        let code = readstr.split(',')[3] //表内区域码
        if (readstr.length > 4) {
          this.field.setValue('replacerId', this.state.createId)
          this.field.setValue('cardNo', cardNo)
          this.queryLastOrder(cardNo)
          this.setState({visible: true, flag: flag, time: time, code: code, cardNo: cardNo})
        }
        else {
          if (readstr == '10') {
            Feedback.toast.error("读卡错误无卡");
          } else if (readstr == '11') {
            Feedback.toast.error("读卡错误设备错误");
          } else if (readstr == '12') {
            Feedback.toast.error("读卡错误无卡");
          }
        }
      } else {
        Feedback.toast.error("该卡非检查卡");
      }
    }
    catch (e) {
      alert('浏览器不支持ActiveX控件，请使用IE');
    }
  }

  //关闭弹窗
  handleCancel() {
    this.setState({visible: false, isCheckCard: false,})
  }

  //查询订单判断卡有没有使用过
  queryLastOrder(number) {
    axios({
      method: 'post',
      url: `${url}revenue/order/findLastOrdersByCnoAndStatus`,
      data: qs.stringify({cardNo: number,resultSize:1}),
    }).then((response) => {
      let totalTimes = response.data.datas.length>0 ? response.data.datas[0].totalTimes : 0
      let isUse = Number(totalTimes) == Number(this.state.time)
      if (isUse) {
        this.field.setValue('isUse', '0')
      } else {
        this.field.setValue('isUse', '1')
      }
      if (response.data.code == "0") {
        this.setState({orderValues: response.data.datas, isUse: isUse})
      }
    }).catch((error) => {
      Feedback.toast.error("请求错误：" + error);
    })
  }

  //调用检查卡补卡dll控件
  write() {
    this.field.validate((errors, values) => {
      if (errors) {
        return;
      }
      else {
        const {cardNo, orderValues, flag, code} = this.state
        let times = orderValues.length>0 ? orderValues[0].totalTimes : 0
        let water = 0
        let i = 1
        try {
          //先判断卡有没有刷过表
          if (this.field.getValue('isUse') == '1') {
            //未使用查询最后一笔有效订单
            if (orderValues.length>0) {
              water = orderValues[0].tunnage
            }
          }
          i = hxdll.copytouser(code, cardNo, water, times, flag)
          if (i == 0) {
            axios({
              method: 'post',
              url: `${url}revenue/order/complete`,
              data: qs.stringify({id: orderValues[0].id}),
            })
              .then((response) => {
                if (response.data.code == '0') {
                  this.reissue()
                }
              })
              .catch((error) => {
                Feedback.toast.error("请求错误：" + error);
              })
          } else {
            if (i == 10) {
              Feedback.toast.error('补卡操作错误：设备失败')
            } else if (i == 11) {
              Feedback.toast.error('补卡操作错误：写卡失败')
            } else if (i == 12) {
              Feedback.toast.error('补卡操作错误：读卡失败')
            } else if (i == 13) {
              Feedback.toast.error('补卡操作错误：坏卡')
            }
          }
        }
        catch (e) {
          alert('浏览器不支持ActiveX控件，请使用IE');
        }
      }
    })
  }

  //补卡调用接口添加记录
  reissue() {
    this.setState({save: true})
    const {orderValues, createId, createName} = this.state
    let list = {}
    list.cno = orderValues[0].cno
    list.isUse = this.field.getValue('isUse')
    list.reissueWater = list.isUse == '0' ? '0' : orderValues[0].tunnage
    list.cardNo = this.field.getValue('cardNo')
    list.amount = this.field.getValue('amount')
    list.createId = createId
    list.createName = createName
    list.replacerId = this.field.getValue('replacerId')
    list.replacerName = createName
    list.watermeterId = orderValues[0].watermeterId
    axios({
      method: 'post',
      url: `${url}revenue/iccard/reissue`,
      data: qs.stringify(list),
    }).then((response) => {
      if (response.data.code == '0') {
        Feedback.toast.success('补卡成功');
        this.setState({visible: false, save: false});
      } else {
        Feedback.toast.error('补卡失败，请重试')
      }
    }).catch((error) => {
      Feedback.toast.error("请求错误：" + error);
    })
  }

  render() {
    const {init} = this.field
    const {roleNameList} = this.props
    const {visible, orderValues} = this.state
    const formItemLayout = {
      labelCol: {fixedSpan: 8},
    }
    const footer = (
      <div align="center">
        <a onClick={() => this.write()}>
          <Button size="medium" type="primary" loading={this.state.save}>
            补卡
          </Button>
        </a>
        <Button size="medium" onClick={() => this.handleCancel()}>
          取消
        </Button>
      </div>
    )
    return (
      <div>
        <Button type="primary" className="button" onClick={() => this.openCheckCard()} style={{marginRight: 10}}>
          <Icon type="set"/>恒信检查卡补卡
        </Button>

        <Dialog style={{width: 550}} visible={visible}
                onClose={() => this.handleCancel()}
                onCancel={() => this.handleCancel()}
                footer={footer}
                title="补卡操作">

          <Form field={this.field}>

            <FormItem {...formItemLayout} label="用户卡号：">
              <Input  {...init('cardNo')} style={{width: 230}} readOnly/>
            </FormItem>

            <FormItem {...formItemLayout} label="用户名：">
              <section style={{textAlign: 'left'}}>
                <div style={{marginTop: 5}}>{orderValues.length>0?orderValues[0].cname:void(0)}</div>
              </section>
            </FormItem>

            <FormItem {...formItemLayout} label="使用状态：">
              <Select placeholder="请选择" style={{width: 230, color: 'black'}} {...init('isUse')} disabled={true}>
                <Select.Option value='0'>已使用</Select.Option>
                <Select.Option value='1'>未使用</Select.Option>
              </Select>
            </FormItem>

            <FormItem {...formItemLayout} label="提示：">
              <section style={{textAlign: 'left'}}>
                <div style={{color: '#ff0000', marginTop: 5}}>如果已刷卡则显示已使用，未刷卡显示未使用</div>
              </section>
            </FormItem>

            <FormItem label="收费(元)：" {...formItemLayout} >
              <Input {...init('amount', {rules: [{required: true, message: '必填'}]})}
                     style={{width: 230}}/>
            </FormItem>

            <FormItem label="补卡人：" {...formItemLayout}>
              <Combobox
                {...init('replacerId', {rules: [{required: true, message: '必填'}]})}
                placeholder="--请选择--"
                fillProps="label"
                hasClear
                style={{width: 230}} dataSource={roleNameList}
                disabled
              />
            </FormItem>

            <FormItem label="换卡原因：" {...formItemLayout} >
              <Input multiple {...init('reason')}
                     style={{width: 230}}/>
            </FormItem>

          </Form>

        </Dialog>

      </div>
    )
  }
}
