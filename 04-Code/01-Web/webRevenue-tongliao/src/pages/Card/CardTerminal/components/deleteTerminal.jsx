import React, {Component} from 'react';
import {Balloon, Button, <PERSON><PERSON>back, Icon} from '@icedesign/base';
import axios from 'axios';
import qs from 'qs';
import {url} from '../../../../components/URL/index'

export default class DeleteTerminal extends Component {

  constructor(props) {
    super(props);
    this.state = {
      visible: false,
    };
  }

  handleHide(record) {
    axios({
      method: 'post',
      url: `${url}revenue/terminal/delete`,
      data: qs.stringify({id: record.id}),
    })
      .then((response) => {
        if (response.data.code == "0") {
          Feedback.toast.success("删除水表成功");
          this.props.queryTerminal()
        } else {
          Feedback.toast.error("删除水表失败");
        }
      })
      .catch((error) => {
        Feedback.toast.error("请求出错: " + error);
      })
    this.setState({visible: false,});
  }

  handleVisible = (visible) => {
    this.setState({visible});
  };

  render() {
    const {record} = this.props
    const visibleTrigger = (
      <Icon title="删除" type="ashbin" size="small" style={{color: "#FF3333", cursor: 'pointer'}}/>
    );

    const content = (
      <div>
        <div style={styles.contentText}>确认删除？</div>
        <Button id="confirmBtn" size="small" type="normal" shape="warning" style={{marginRight: '5px'}}
                onClick={(visible) => this.handleHide(record)}>确认</Button>
        <Button id="cancelBtn" size="small" onClick={() => this.handleVisible()}>关闭</Button>
      </div>
    );

    return (
      <Balloon
        trigger={visibleTrigger}
        triggerType="click"
        visible={this.state.visible}
        onVisibleChange={this.handleVisible}
      >
        {content}
      </Balloon>
    );
  }
}

const styles = {
  contentText: {
    padding: '5px 0 15px',
  },
};
