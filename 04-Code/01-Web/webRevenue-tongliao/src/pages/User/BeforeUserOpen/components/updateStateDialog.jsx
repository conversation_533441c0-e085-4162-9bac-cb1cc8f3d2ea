import React, {Component, Fragment} from 'react';
import {<PERSON>ton, Dialog, Field, Form, Icon, Select} from '@icedesign/base'
import {url} from "../../../../components/URL";
import axios from "axios/index";
import qs from 'qs';
import {Feedback} from "@icedesign/base/index";

const FormItem = Form.Item

export default class updateStateDialog extends Component {

  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      stuffId: sessionStorage.getItem('stuffId'),
      stuffName: sessionStorage.getItem('realName'),
    }
    this.field = new Field(this, {autoUnmount: true});
  }

  //开启弹窗
  openDialog() {
    const {selectedRecord} = this.props;
    let ids = []
    if (selectedRecord.length <= 0) {
      alert('至少选择一条记录');
    } else {
      selectedRecord.map((item) => {
          ids.push(item.id)
          this.setState({visible: true, ids: ids})
      })
    }
  }

  //关闭弹窗
  handleCancel() {
    this.setState({visible: false})
  }

  //弹窗确定回掉函数
  handleOk() {
    this.field.validate((errors, values) => {
      if (errors) {
        return;
      } else {
        const {ids} = this.state
        values.ids = ids
        axios.post(`${url}revenue/preOpening/updateStatus`, qs.stringify(values))
          .then((response) => {
            if (response.data.code == "0") {
              Feedback.toast.success("修改成功");
              this.props.refreshTable()
              this.setState({visible: false});
            } else {
              Feedback.toast.error(response.data.msg);
            }
          }).catch((error) => {
          Feedback.toast.error("网络错误,请稍后再试")
        });
      }
    })
  }

  render() {
    const {visible} = this.state
    const {init} = this.field
    const formItemLayout = {
      labelCol: {fixedSpan: 6},
      wrapperCol: {span: 14}
    }

    return (
      <Fragment>

        <Button type="primary" onClick={() => this.openDialog()}
                style={{marginLeft: 10, marginBottom: 10, width: 150}}>
          <Icon type="edit" style={{color: "#ffffff"}}/>
          修改用户状态
        </Button>

        <Dialog style={{width: 450}} visible={visible} onClose={() => this.handleCancel()}
                onCancel={() => this.handleCancel()}
                onOk={() => this.handleOk()}
                title="修改预开户用户状态"
                isFullScreen
                footerAlign="center">
          <Form field={this.field}>

            <FormItem {...formItemLayout} label="用户状态：">
              <Select placeholder="--请选择--" {...init('status', {rules: [{required: true, message: '用户状态必选'}]})}
                      style={{width: 180}}>
                <Select.Option value="">请选择</Select.Option>
                <Select.Option value="0">待开户</Select.Option>
                <Select.Option value="2">异常用户</Select.Option>
                <Select.Option value="3">作废</Select.Option>
              </Select>
            </FormItem>

          </Form>

        </Dialog>

      </Fragment>
    )
  }
}
