/* eslint-disable react/no-unused-state */
import React, {Component} from 'react';
import {Button, Feedback, Form, Icon, moment, Table} from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import qs from 'qs';
import '../../UserSearch/index.scss'
import {url} from '../../../../components/URL/index'
import {Link, withRouter} from 'react-router-dom'
import AccountDetailsDialog
  from '../../UserSearch/components/accountDetailsDialog'
import BillingRecord from '../../UserSearch/components/billingRecord'
import ViewOrderDetail from '../../../../common/ViewOrderDetail'
import FoundationSymbol from 'foundation-symbol';

const FormItem = Form.Item;

@withRouter
export default class CheckUser extends Component {

  constructor(props) {
    super(props);
    this.state = {
      bills: [],
      sameInfoList: [],
      watermeters: [],
      watermeterChanges: [],
      orders: [],
      iccards: [],
      selectUser: '',
      searchValue: {},
    }
  }

  componentDidMount() {
    const param = this.props.location.state
    if (param == undefined) {
      this.props.history.push('/user/userDisbursements');
      return;
    } else {
      this.setState({
        selectUser: param.selectUser,
        searchValue: param.searchValue,
      })
    }
    let values = []
    //用户唯一标识id
    values.cno = param.selectUser.cno
    axios({
      method: 'post',
      url: `${url}/revenue/user/view`,
      data: qs.stringify(values),
    })
      .then((response) => {
        this.setState({
          bills: response.data.datas.bills,
          watermeters: response.data.datas.watermeters,
          watermeterChanges: response.data.datas.watermeterChanges,
          orders: response.data.datas.orders,
          iccards: response.data.datas.iccards
        })
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
      })

    this.findSameInfoByCno(param.selectUser.cno)
  }


  findSameInfoByCno(cno) {
    axios({
      method: 'get',
      url: `${url}/revenue/user/findSameInfoByCno?cno=` + cno,
    }).then((response) => {
      let sameInfoList = response.data.datas ? response.data.datas.filter((item) => item.cno != cno) : []
      if (response.data.code == '0') {
        this.setState({sameInfoList: sameInfoList})
      }
    }).catch((error) => {
      Feedback.toast.error("系统繁忙请稍后再试" + error)
    })
  }

  rowOptionRender = (value, index, record) => {
    return <ViewOrderDetail record={record}/>
  }

  paymentRecords = (value, index, record) => {
    return <ViewOrderDetail record={record}/>
  }

  billingRecord(value, index, record) {
    return <BillingRecord record={record}/>
  }

  /*渲染订单状态*/
  renderStatus = (value) => {
    if (value == 3) {
      return <span style={{color: '#1DC11D'}}>已完成</span>
    } else if (value == 2) {
      return <span style={{color: '#ff0000'}}>退款</span>
    } else if (value == 1) {
      return <span style={{color: '#ffa631'}}>已支付未刷卡</span>
    }
  }

  /*渲染表格中状态栏*/
  renderPayWay = (value) => {
    if (value == 0) {
      return <span>刷卡</span>
    } else if (value == 1) {
      return <span>现金</span>
    } else {
      return <span>微信</span>
    }
  }

  rowIndex = (value, index) => {
    if (index == 0) {
      return index + 1
    } else {
      return index + 1
    }
  }

  render() {
    const {searchValue, bills, selectUser, watermeterChanges, orders, iccards, sameInfoList} = this.state
    return (
      <section>

        <div align="left" style={{marginBottom: 5}}>
          <Link to={{
            pathname: `/user/userDisbursements`,
            state: {searchValue: searchValue}
          }}>
            <Button className="button" type="primary">
              <FoundationSymbol type="backward" style={{marginRight: 5}} size="small"/>
              返回
            </Button>
          </Link>
        </div>

        <IceContainer title="基本信息">
          <div style={{display: 'flex', justifyContent: 'space-around'}}>

            <div style={{display: 'flex', flexDirection: 'column'}}>

              <FormItem label="&emsp;用户编号：" style={{display: 'flex'}}>
                {selectUser ? selectUser.cno : void(0)}
              </FormItem>

              <FormItem label="&emsp;用水性质：" style={{display: 'flex'}}>
                {selectUser ? selectUser.feeName : void(0)}
              </FormItem>

              <FormItem label="联系人名称：" style={{display: 'flex'}}>
                {selectUser ? selectUser.contact : void(0)}
              </FormItem>

              {
                selectUser.watermeterType == 'IC卡表' ?
                  <FormItem label='累计购水量：' style={{display: 'flex'}}>
                    {selectUser ? selectUser.totalTunnage : void(0)}
                  </FormItem> :
                  <FormItem label='累计用水量：' style={{display: 'flex'}}>
                    {selectUser ? selectUser.totalTunnage : void(0)}
                  </FormItem>
              }

              <FormItem label="&emsp;开户日期：" style={{display: 'flex'}}>
                {selectUser ? selectUser.openTime : void(0)}
              </FormItem>

              <FormItem label="&emsp;联系号码：" style={{display: 'flex'}}>
                {selectUser ? selectUser.phone ? selectUser.phone : selectUser.tel : void(0)}
              </FormItem>


            </div>

            <div style={{display: 'flex', flexDirection: 'column'}}>

              <FormItem label="&emsp;&emsp;&emsp;用户名称：" style={{display: 'flex'}}>
                {selectUser ? selectUser.cname : void(0)}
              </FormItem>

              <FormItem label="&emsp;&emsp;&emsp;用户片区：" style={{display: 'flex'}}>
                {selectUser ? selectUser.areaName : void(0)}
              </FormItem>

              <FormItem label="&emsp;&emsp;联系人号码：" style={{display: 'flex'}}>
                {selectUser ? selectUser.tel : void(0)}
              </FormItem>

              {
                selectUser.watermeterType == 'IC卡表' ?
                  <FormItem label='&emsp;累计购水次数：' style={{display: 'flex'}}>
                    {selectUser ? selectUser.totalTimes : void(0)}
                  </FormItem> :
                  <FormItem label='&emsp;&emsp;&emsp;累计次数：' style={{display: 'flex'}}>
                    {selectUser ? selectUser.totalTimes : void(0)}
                  </FormItem>
              }

              <FormItem label="&emsp;&emsp;&emsp;账户余额：" style={{display: 'flex'}}>
                {selectUser ? selectUser.account : void(0)}
              </FormItem>

              <FormItem label="&emsp;&emsp;&emsp;用户地址：" style={{display: 'flex'}}>
                {selectUser ? selectUser.address : void(0)}
              </FormItem>

            </div>

            <div style={{display: 'flex', flexDirection: 'column'}}>

              <FormItem label="&emsp;户籍人数：" style={{display: 'flex'}}>
                {selectUser ? selectUser.domicileNum : void(0)}
              </FormItem>

              <FormItem label="&emsp;用户小区：" style={{display: 'flex'}}>
                {selectUser ? selectUser.regionName : void(0)}
              </FormItem>

              <FormItem label="&emsp;信用额度：" style={{display: 'flex'}}>
                {selectUser ? selectUser.creditLine : void(0)}
              </FormItem>

              <FormItem label="当年购水量：" style={{display: 'flex'}}>
                {selectUser ? selectUser.yearTunnage : void(0)}
              </FormItem>

              <FormItem label="&emsp;账户详情：" style={{display: 'flex'}}>
                <AccountDetailsDialog cno={selectUser.cno}/>
              </FormItem>

              <FormItem label="&emsp;&emsp;&emsp;备注：" style={{display: 'flex'}}>
                {selectUser ? selectUser.remark : void(0)}
              </FormItem>

            </div>

          </div>

        </IceContainer>

        {
          sameInfoList.length > 0 ? <IceContainer title="同户信息">
            <Table dataSource={sameInfoList}>
              <Table.Column title="序号" cell={this.rowIndex} align="center" width={60}/>
              <Table.Column title='同户用户编号' dataIndex='cno' align='center' width={100}/>
              <Table.Column title='同户水表编号' dataIndex='watermeterId' align='center' width={100}/>
            </Table>
          </IceContainer> : void (0)
        }

        {
          selectUser.watermeterType === 'IC卡表' ?
            <IceContainer>
              <h5 className='infoColumnTitle'>补卡记录</h5>
              <Table dataSource={iccards}>
                <Table.Column title="用户编号" dataIndex="cno" align="center"/>
                <Table.Column title="用户卡号" dataIndex="cardNo" align="center"/>
                <Table.Column title="补发水量" dataIndex="reissueWater" align="center" cell={(value) => value ? value : 0}/>
                <Table.Column title="使用状态" dataIndex="isUse" align="center"
                              cell={(value) => value == '1' ? '未使用' : '已使用'}/>
                <Table.Column title="补卡时间" dataIndex="createTime" align="center"/>
                <Table.Column title="操作员" dataIndex="createName" align="center"/>
              </Table>
            </IceContainer>
            :
            <IceContainer>
              <h5 className='infoColumnTitle'>账单记录</h5>
              <Table dataSource={bills} fixedHeader maxBodyHeight={200}>
                <Table.Column title="出账日期" dataIndex="createTime" align="center"
                              cell={(value) => moment(value).format('YYYY-MM-DD')}
                />
                <Table.Column title="上次示数" dataIndex="lastNum" align="center"/>
                <Table.Column title="本次示数" dataIndex="thisNum" align="center"/>
                <Table.Column title="结算水量" dataIndex="tunnage" align="center"/>
                <Table.Column title="账单状态" dataIndex="billStatus" align="center"
                              cell={(value) => value == 0 ? '未结清' : '已结清'}/>
                <Table.Column title="操作"
                              cell={(value, index, record) => this.billingRecord(value, index, record)}
                              align="center"/>
              </Table>
              {
                selectUser.watermeterType == '机械表' ?
                  <Link to={{pathname: `/machineryMeter/machineryMeterBill`, state: {cno: selectUser.cno}}}>
                    <h6 style={{display: 'block', textAlign: 'right'}}>查看更多账单<Icon size="xs" type="arrow-right"/></h6>
                  </Link>
                  :
                  <Link to={{pathname: `/remoteManage/remoteMeterBill`, state: {cno: selectUser.cno}}}>
                    <h6 style={{display: 'block', textAlign: 'right'}}>查看更多账单<Icon size="xs" type="arrow-right"/></h6>
                  </Link>
              }
            </IceContainer>
        }

        <IceContainer title="订单记录">
          <Table dataSource={orders} fixedHeader maxBodyHeight={300}>
            <Table.Column title="水表编号" dataIndex="watermeterId" align="center" lock width={120}/>
            <Table.Column title="水表种类" dataIndex="watermeterKind" align="center"/>
            <Table.Column title="订单来源" dataIndex="source" align="center"/>
            <Table.Column title="支付方式" dataIndex="payWay" align="center" cell={this.renderPayWay}/>
            <Table.Column title="用水性质" dataIndex="feeName" align="center"/>
            <Table.Column title="订单水量" dataIndex="tunnage" align="center"/>
            <Table.Column title="实收金额" dataIndex="reallyFee" align="center"/>
            <Table.Column title="订单状态" dataIndex="status" align="center" cell={this.renderStatus}/>
            <Table.Column title="提交时间" dataIndex="createTime" align="center"/>
            <Table.Column title="操作员" dataIndex="createName" align="center"/>
            <Table.Column title="操作" align="center" cell={this.rowOptionRender}/>
          </Table>
        </IceContainer>


        <IceContainer title="换表记录">
          <Table dataSource={watermeterChanges}>
            <Table.Column title="旧表编号" dataIndex="oldWid" align="center"/>
            <Table.Column title="旧表种类" dataIndex="oldWatermeterKind" align="center"/>
            <Table.Column title="旧表累计购水量" dataIndex="oldTotalTunnage" align="center"/>
            <Table.Column title="剩余水量" dataIndex="leaveTunage" align="center"/>
            <Table.Column title="新表编号" dataIndex="newWid" align="center"/>
            <Table.Column title="新表种类" dataIndex="newWatermeterKind" align="center"/>
            <Table.Column title="换表时间" dataIndex="createTime" align="center"/>
            <Table.Column title="操作员" dataIndex="createName" align="center"/>
            <Table.Column title="更换原因" dataIndex="reason" align="center"/>
          </Table>

          <div align="center" style={{marginTop: 20}}>
            <Link to={{
              pathname: `/user/userDisbursements`,
              state: {searchValue: searchValue}
            }}>
              <Button className="button" type="primary"> 返回 </Button>
            </Link>
          </div>
        </IceContainer>

      </section>
    )
  }
}
