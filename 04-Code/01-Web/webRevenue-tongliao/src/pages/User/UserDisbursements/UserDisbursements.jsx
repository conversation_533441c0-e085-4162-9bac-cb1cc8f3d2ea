import React, {Component} from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  DatePicker,
  <PERSON><PERSON><PERSON>,
  Field,
  Form,
  Icon,
  Input,
  Pagination,
  Select,
  Table
} from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import qs from 'qs';
import {url} from '../../../components/URL/index'
import {Link} from 'react-router-dom';

const FormItem = Form.Item;
const Tooltip = Balloon.Tooltip;
const {RangePicker} = DatePicker;
const {Combobox} = Select
export default class UserDisbursements extends Component {

  constructor(props) {
    super(props);
    this.state = {
      formSource: [],
      page: 1,
      pageSize: 10,
      totalSize: 0,
      roleNameList: [], //查询操作员
      unNameList: [],     //查询抄表员
      areaList: [],  //片区
      regionList: [], //小区
      dataLoading: false,
      createId: sessionStorage.getItem("stuffId"),
      createName: sessionStorage.getItem("realName"),
    }
    this.field = new Field(this, { autoUnmount: true });
  }

  componentDidMount() {
    this.queryUserDisbursements(1, 10);
    this.queryRoleName();
    this.queryArea()
    this.queryRegion()
    this.queryUname()
  }

  //查询
  queryUserDisbursements(page, pageSize) {
    this.setState({ dataLoading: true });
    let values = this.field.getValues();
    values.page = page
    values.pageSize = pageSize
    delete values.querySubmitTime
    axios({
      method: 'post',
      url: `${url}revenue/customerCancel/query`,
      data: qs.stringify(values),
    })
      .then((response) => {
        this.setState({
          formSource: response.data.datas,
          page: page,
          pageSize: pageSize,
          totalSize: response.data.totalSize,
          dataLoading: false
        })
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
        this.setState({ dataLoading: false })
      })
  }

  //翻页
  changePage(page) {
    const { pageSize } = this.state;
    this.queryUserDisbursements(page, pageSize);
  }

  //改变pageSize
  onPageSizeChange(pageSize) {
    this.queryUserDisbursements(1, pageSize);
  }

  //查询操作员
  queryRoleName() {
    const { createId } = this.state;
    axios({
      method: 'get',
      url: `${url}revenue/customerCancel/createName` + '?n=' + Math.random(),
      data: qs.stringify({ createId: createId }),
    })
      .then((response) => {
        if (response.data.code == 0) {
          this.setState({
            roleNameList: response.data.datas
          })
        }
      }).catch((error) => {
        Feedback.toast.error("请求错误：" + error);
      })
  }

  //查询片区
  queryArea() {
    axios({
      method: 'get',
      url: url + 'revenue/area/getAll'
    })
      .then((response) => {
        let areaList = []
        response.data.datas.map((item) => {
          areaList.push({ label: item.name, value: item.id })
        })
        this.setState({ areaList: areaList })
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
      })

  }

  //查询小区
  queryRegion() {
    axios({
      method: 'post',
      url: `${url}revenue/region/regionlist`,
      //data: qs.stringify(areaId)
    }).then(response => {
      let regionList = []
      response.data.datas.map((item) => {
        regionList.push({ label: item.regionName, value: item.id })
      })
      this.setState({ regionList: regionList })

    }).catch(error => {
      Feedback.toast.error("请求异常", error);
    })
  }

  //查询抄表员
  queryUname() {
    axios({
      method: 'post',
      url: `${url}revenue/staff/findByRoleName`,
      data: qs.stringify({ roleName: '抄表员' })
    })
      .then((response) => {
        let unNameList = []
        response.data.datas.map((item) => {
          unNameList.push({ label: item.realName, value: item.userId })
        })
        this.setState({ unNameList: unNameList })
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
      })
  }

  //重置
  reset() {
    this.field.reset()
    this.field.setValue("csTime", undefined);
    this.field.setValue("ceTime", undefined);
  }

  //时间onchang
  timeOnchange(val, str) {
    this.field.setValue("csTime", str[0]);
    this.field.setValue("ceTime", str[1]);
  }

  /*表格操作栏*/
  renderOper = (value, index, record) => {
    const look = (
      <Link to={{
        pathname: `./userDisbursements/FindUser`,
        state: { selectUser: record, searchValue: this.field.getValues() }
      }}>
        <a>
          <Icon type="browse" size="small" style={{ color: "#FFA003", cursor: 'pointer' }} />
        </a>
      </Link>
    )
    return (
      <div className="operation">
        <Tooltip trigger={look} align='t' text='查看' />
      </div>
    )
  }

  render() {
    const { regionList, areaList, dataLoading, page, pageSize, totalSize, formSource, roleNameList, unNameList } = this.state
    const { init } = this.field
    return (
      <div>
        <IceContainer title="销户记录">
          <Form field={this.field}>
            <div style={{ display: 'flex', justifyContent: 'space-around' }}>
              <div>
                <FormItem label="用户编号：">
                  <Input {...init('cno')} placeholder="--请输入--" style={{ width: 170 }} />
                </FormItem>

                <FormItem label="提交时间：">
                  <RangePicker
                    {...init('querySubmitTime', {
                      props: { onChange: (val, str) => this.timeOnchange(val, str) }
                    })}
                    style={{ width: 211 }} />
                </FormItem>

                <FormItem label="&emsp;抄表员：">
                  <Combobox {...init('cid')} placeholder="请选择"
                    dataSource={unNameList} style={{ width: 170 }}
                    fillProps="label"
                    hasClear
                  />
                </FormItem>
              </div>

              <div>
                <FormItem label="用户名称：">
                  <Input {...init('cname')} placeholder="--请输入--"
                         style={{width: 170}}/>
                </FormItem>
                <FormItem label="&emsp;&emsp;片区：">
                  <Select {...init('areaId')} placeholder="请选择"
                          dataSource={areaList} style={{width: 170}}/>
                </FormItem>
                <FormItem label="用户户号：">
                  <Input {...init('hno')} placeholder="--请输入--"
                         style={{width: 170}}/>
                </FormItem>
              </div>

              <div>
                <FormItem label="&emsp;操作员：">
                  <Combobox
                    {...init('createId')}
                    placeholder="--请选择--"
                    fillProps="label"
                    hasClear
                    style={{ width: 170 }} dataSource={roleNameList} />
                </FormItem>
                <FormItem label="&emsp;&emsp;小区：">
                  <Combobox
                    {...init('regionId')}
                    placeholder="--请选择小区--"
                    fillProps="label"
                    hasClear
                    style={{ width: 170 }} dataSource={regionList} />
                </FormItem>
              </div>


            </div>
          </Form>

          <div style={{ textAlign: 'center' }}>
            <Button type="primary" className="button" onClick={() => this.queryUserDisbursements()}
              style={{ marginRight: 10 }}>
              <Icon type="search" />查询
            </Button>
            <Button type="secondary" className="button" onClick={() => this.reset()} style={{ marginRight: 10 }}>
              <Icon type="refresh" />重置
            </Button>
          </div>
        </IceContainer>

        <IceContainer title="销户记录列表">
          <Table dataSource={formSource} isLoading={dataLoading}>
            <Table.Column title="销户时间" dataIndex="createTime" align="center" />
            <Table.Column title="用户编号" dataIndex="cno" align="center" />
            <Table.Column title="用户户号" dataIndex="hno" align="center" />
            <Table.Column title="用户名称" dataIndex="cname" align="center" />
            <Table.Column title="用户地址" dataIndex="address" align="center" />
            <Table.Column title="累计购水量" dataIndex="tunnage" align="center" />
            <Table.Column title="退还总金额(元)" dataIndex="amount" align="center" />
            <Table.Column title="销户原因" dataIndex="reason" align="center" />
            <Table.Column title="操作员" dataIndex="createName" align="center" />
            <Table.Column title="抄表员" dataIndex="coName" align="center" />
            <Table.Column title="操作" cell={this.renderOper}
              align="center" width={110} />
          </Table>

          <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Pagination
              pageSizeSelector="dropdown"
              onPageSizeChange={(page) => this.onPageSizeChange(page)}
              style={{ marginTop: 15 }}
              current={page}
              pageSize={pageSize}
              total={totalSize}
              onChange={(current) => this.changePage(current)}
            />
            <div style={{ lineHeight: '58px', marginLeft: 10 }}>共{totalSize}条记录</div>
          </div>

        </IceContainer>

      </div>
    )
  }
}
