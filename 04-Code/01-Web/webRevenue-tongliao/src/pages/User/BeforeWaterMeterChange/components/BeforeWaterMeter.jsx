import React, {Component, Fragment} from 'react';
import {
  Balloon,
  Button,
  DatePicker,
  Dialog,
  Feedback,
  Field,
  Form,
  Grid,
  Icon,
  Input,
  moment,
  Select
} from '@icedesign/base';
import {
  location,
  waterMeterCaliber,
  waterMeterCompany,
  waterMeterType
} from '../../../../common/WaterMeterCollocation';
import {formItemLayout, span} from "../../../../common/FormCollocation";
import axios from "axios";
import {url} from "../../../../components/URL";
import qs from 'qs';
import watermeterKindNo from "../../../../common/watermeterKindNo";
import {areaCode} from "../../../../components/areaCode/areaCode";

const Tooltip = Balloon.Tooltip
const FormItem = Form.Item;
const {Row, Col} = Grid;
export default class BeforeWaterMeter extends Component {

  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      value: null,
      changeButton:false
    }
    this.field = new Field(this, {autoUnmount: true})
  }

  //打开预换表弹窗
  onOpen() {
    axios.post(`${url}revenue/preChange/checkPreChange`, {cno: this.props.record.cno})
      .then((res) => {
        if (res.data.code === '0') {
          this.setState({visible: true})
        } else {
          Feedback.toast.error(res.data.msg);
        }
      }).catch(() => {
      Feedback.toast.error("网络错误请刷新页面重试！");
    })
  }

  //关闭预换表弹窗
  onClose() {
    this.setState({visible: false})
  }

  //失去焦点查询水表
  handleOnBlur() {
    let watermeterId = this.field.getValue('newWid')
    let values = {};
    values.oldWatermeterId = this.props.record.watermeterId;
    values.newWatermeterId = watermeterId;
    axios.post(`${url}revenue/watermeter/one`, qs.stringify(values))
      .then((response) => {
        let jsondata = response.data;
        if (jsondata.code == "0") {
          let jsonData = response.data.datas
          this.field.setValue('newWatermeterCompany', jsonData.watermeterCompany)
          this.field.setValue('newWatermeterType', jsonData.watermeterType)
          this.field.setValue('newWatermeterCaliber', jsonData.watermeterCaliber)
          this.field.setValue('newWatermeterKind', jsonData.watermeterKind)
          this.field.setValue('newLocation', jsonData.location)
          this.field.setValue('qrCode', jsonData.qrCode)
          this.setState({value: jsonData})
        } else {
          if (response.data.msg == '水表已被绑定') {
            Feedback.toast.error(response.data.msg);
            this.field.reset('newWid')
          }
          this.setState({value: null})
          this.field.reset('newWatermeterCaliber')
          this.field.reset('newWatermeterKind')
        }
      }).catch((error) => {
      Feedback.toast.error("请求错误：" + error);
    })
  }

  //校验水表编号
  checkedTunnage = (rule, value, callback) => {
    if (value) {
      if (isNaN(value)) {
        callback('水表编号只能是数字');
      } else {
        callback();
      }
    } else {
      callback('必填');
    }
  }

  //确认换表
  changeWaterMeter(){
    if (this.field.getValue('newWatermeterType') == 'IC卡表') {
      this.DllChangeTable();
    }
    else {
      this.doAxiosChangeMeter();
    }
  }

  //dll控件
  DllChangeTable () {
    let newWatermeterKind=this.field.getValue('waterMeterType')
    //水表类型
    let type = watermeterKindNo(newWatermeterKind)
    //本次购水量
    let water = this.field.getValue('tunnage');
    //卡号
    let cardno = '';
    let i = 1;
    if (type == '3') {
      cardno = tableValue[0].newWid;
      this.setState({cardNo: cardno})
      let i = hxdll.hbconfirm(areaCode, cardno, cardno, water, type, 0, 0, 0, 0, 0)
      if (i == 0) {
        this.doAxiosChangeMeter(cardno);
      }
      else {
        this.setState({changeButton: false})
        alert('换表失败：' + changeWatermeter(i));
      }
    } else if (type == '1' || type == '2') {
      cardno = this.field.getValue('cno');
      let i = hxdll.hbconfirm(areaCode, cardno, "0000000000", water, type, 0, 0, 0, 0, 0)
      if (i == 0) {
        this.doAxiosChangeMeter(cardno);
      }
      else {
        this.setState({changeButton: false});
        alert('换表失败：' + changeWatermeter(i));
      }
    }
  }

  doAxiosChangeMeter = (cardNo) => {
    const {tableValue, stuffId, stuffName} = this.state;
    let cno = this.field.getValue('cno');
    let cname = this.field.getValue('cname');
    let oldWid = this.field.getValue('watermeterId');
    let oldWatermeterType = this.field.getValue('watermeterType');
    let oldWatermeterKind = this.field.getValue('watermeterKind');
    let oldWatermeterCompany = this.field.getValue('watermeterCompany');
    let oldtotalTunnage = this.field.getValue('totalTunnage');
    let meterReader = this.field.getValue('meterReader');
    let newWid = tableValue[0].newWid;
    let newWatermeterType = tableValue[0].newWatermeterType;
    let newWatermeterKind = tableValue[0].newWatermeterKind;
    let newWatermeterCompany = tableValue[0].newWatermeterCompany;
    let newWatermeterCaliber = tableValue[0].newWatermeterCaliber
    let newLockNo = tableValue[0].newLockNo;
    let newWheelNumber = tableValue[0].newWheelNumber;
    let newLocation = tableValue[0].newLocation;
    let reason = this.field.getValue('reason');
    let leaveMoney = this.field.getValue('changeMoney');
    let leaveTunage = this.field.getValue('tunnage');
    let account = this.field.getValue('account');
    let createId = stuffId;
    let createName = stuffName;

    //新表的用水性质
    let newFeeId = this.field.getValue('newFeeId');
    //户籍人数
    let domicileNum = this.field.getValue('domicileNum');

    let cardno = cardNo;

    //异表更换
    let isDiff = this.field.getValue('isDiff')
    let oldYearTunnage = this.field.getValue('oldYearTunnage')
    let oldWatermeterStatus = this.field.getValue('oldWatermeterStatus')

    let values = {};
    values.cno = cno;
    values.cname = cname;
    values.oldWid = oldWid;
    values.oldWatermeterType = oldWatermeterType;
    values.oldWatermeterKind = oldWatermeterKind;
    values.oldWatermeterCompany = oldWatermeterCompany;
    values.oldTotalTunnage = oldtotalTunnage;
    values.meterReader = meterReader;
    values.newWid = newWid;
    values.newWatermeterType = newWatermeterType;
    values.newWatermeterKind = newWatermeterKind;
    values.newWatermeterCompany = newWatermeterCompany;
    values.newWatermeterCaliber = newWatermeterCaliber
    values.newLockNo = newLockNo;
    values.newWheelNumber = newWheelNumber;
    values.newLocation = newLocation;
    values.reason = reason;
    values.leaveTunage = leaveTunage;
    values.leaveMoney = leaveMoney;
    values.account = account;
    values.createId = createId;
    values.createName = createName;
    //为了保存用户水表的关系
    values.totalTunnage = leaveTunage;
    values.totalFee = leaveMoney?leaveMoney:0;
    values.totalTimes = 1;
    values.cardNo = cardno;

    values.newFeeId = newFeeId;
    values.domicileNum = domicileNum;

    values.isDiff = isDiff ? '1' : '0'
    values.oldYearTunnage = oldYearTunnage
    values.oldWatermeterStatus = oldWatermeterStatus
    values.createTime=moment(this.field.getValue('time')).format('YYYY-MM-DD h:mm:ss')

    axios({
      method: 'post',
      url: `${url}revenue/watermeterChange/change`,
      data: qs.stringify(values)
    })
      .then(response => {
        this.setState({changeButton: false})
        let jsondata = response.data;
        if (jsondata.code == 0) {
          alert('换表成功');
          this.props.history.push('/user/UserExchange');
        }
        else {
          alert(jsondata.msg);
        }
      })
      .catch(error => {
        this.setState({changeButton: false})
        Feedback.toast.error('axios请求失败' + error);
      })
  }

  render() {
    const {visible, value,changeButton} = this.state
    const {init} = this.field;
    const refresh = (
      <Icon type="refresh" style={{color: "#3399ff", cursor: "pointer"}} size="small"
            onClick={() => this.onOpen()}/>
    )
    const footer = (
      <span>
        <Button onClick={()=>this.changeWaterMeter()}  loading={changeButton}
          type="primary">确认提交</Button>
        <Button onClick={() => this.onClose()} style={{marginLeft: 20}}>取消</Button>
      </span>
    )
    return (
      <Fragment>
        <Tooltip trigger={refresh} align='t' text='预换表'/>

        <Dialog visible={visible} title='预换表' footer={footer} footerAlign="center"
                onClose={() => this.onClose()} style={{width: 1200}}>
          <Form field={this.field}>
            <Row wrap>
              <Col {...span}>
                <FormItem label="水表编号：" {...formItemLayout}>
                  <Input  {...init('newWid', {
                    rules: [{required: true, message: '水表编号必填'}, {validator: this.checkedTunnage}, {
                      pattern: /^\S*$/,
                      message: '不能输入空格'
                    }]
                  })} style={{width: 200, color: 'black'}}
                          onBlur={() => this.handleOnBlur()}/>
                </FormItem>
              </Col>

              <Col {...span}>
                <FormItem label="水表类型：" {...formItemLayout}>
                  <Select placeholder="请选择" style={{width: 200, color: 'black'}} {...init('newWatermeterType', {
                    rules: [{required: true, message: '水表类型必填'}]
                  })}
                          disabled={value ? true : false} onChange={this.changeWaterType}
                          dataSource={waterMeterType}>
                  </Select>
                </FormItem>
              </Col>

              <Col {...span}>
                <FormItem label="水表种类：" {...formItemLayout}>
                  <Select placeholder="请先选择水表类型"
                          style={{width: 200, color: 'black'}} {...init('newWatermeterKind', {
                    rules: [{required: true, message: '必填'}]
                  })}
                          dataSource={this.state.waterKind}
                          disabled={value ? true : false}
                  />
                </FormItem>
              </Col>

              <Col {...span}>
                <FormItem label="水表厂家：" {...formItemLayout}>
                  <Select  {...init('newWatermeterCompany', {
                    rules: [{required: true, message: '水表厂家必填'}], initValue: '扬州恒信'
                  })}
                           style={{width: 200, color: 'black'}}
                           dataSource={waterMeterCompany}
                           disabled={value ? true : false}
                  />
                </FormItem>
              </Col>

              <Col {...span}>
                <FormItem label="换表时间：" {...formItemLayout}>
                  <DatePicker {...init('time', {rules: [{required: true, message: '换表时间必填'}], initValue: new Date()})}
                              style={{width: 200, color: 'black'}}/>
                </FormItem>
              </Col>

              <Col {...span}>
                <FormItem label="安装位置：" {...formItemLayout}>
                  <Select placeholder="--请选择--" style={{width: 200, color: 'black'}}
                          {...init('newLocation', {
                            rules: [{required: true, message: '安装位置必填'}],
                            initValue: '厨房'
                          })} dataSource={location}/>
                </FormItem>
              </Col>

              <Col {...span}>
                <FormItem label="水表口径：" {...formItemLayout}>
                  <Select {...init('newWatermeterCaliber', {
                    rules: [{required: true, message: "水表口径必填"}],
                    initValue: '15'
                  })}
                          placeholder="--请选择--"
                          style={{width: 200, color: 'black'}}
                          dataSource={waterMeterCaliber}
                          disabled={value ? true : false}
                  />
                </FormItem>
              </Col>

              <Col {...span}>
                <FormItem label="表锁号：" {...formItemLayout}>
                  <Input {...init('newLockNo')} placeholder="--请输入--" style={{width: 200, color: 'black'}}/>
                </FormItem>
              </Col>

              <Col {...span}>
                <FormItem label="字轮基数：" {...formItemLayout}>
                  <Input {...init('newWheelNumber', {
                    rules: [{required: true, message: '字轮基数必填'}],
                    initValue: '0'
                  })}
                         style={{width: 200, color: 'black'}}
                         disabled={value ? true : false}
                  />
                </FormItem>
              </Col>

              <Col {...span}>
                <FormItem label="二维码编号：" {...formItemLayout}>
                  <Input  {...init('qrCode',
                    {rules: [{pattern: /^\S*$/, message: '不能输入空格'}]})}
                          style={{width: 200, color: 'black'}}
                          onBlur={() => this.checkQRCode()}/>
                </FormItem>
              </Col>

              <Col {...span}>
                <FormItem label="上期示数：" {...formItemLayout}>
                  <Input {...init('lastNum',
                    {rules: [{required: true, message: '上期示数必填'}]})}
                         style={{width: 200, color: 'black'}}/>
                </FormItem>
              </Col>

              <Col {...span}>
                <FormItem label="本期示数：" {...formItemLayout}>
                  <Input {...init('thisNum',
                    {rules: [{required: true, message: '本期示数必填'}]})}
                         style={{width: 200, color: 'black'}}/>
                </FormItem>
              </Col>

            </Row>
          </Form>
        </Dialog>
      </Fragment>
    )
  }
}
