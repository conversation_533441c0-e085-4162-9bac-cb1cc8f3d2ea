import React, {Component} from 'react';
import IceContainer from '@icedesign/container';
import {
  Button,
  DatePicker,
  Field,
  Form,
  Grid,
  Icon,
  Input,
  Select
} from '@icedesign/base';
import {formItemLayout, span} from '@/common/FormCollocation';
import {
  waterMeterCompany,
  waterMeterKind
} from '@/common/WaterMeterCollocation';

const { Row, Col } = Grid;
const { RangePicker } = DatePicker;
export class Search extends Component {
    field = new Field(this, { autoUnmount: true });
    // 查询
    async refreshTable() {
        const { getRecord, pagination } = this.props;
        this.setState({ dataLoading: true });
        let values = this.field.getValues();
        const params = { pagination, ...values };
        await getRecord(params);
        this.setState({ dataLoading: false });
    }
    // 搜搜控件日期选择
    changeTimeFm = (value, str) => {
        if (value) {
            this.field.setValue('startTime', str[0]);
            this.field.setValue('endTime', str[1]);
        }
    };
    // 重置
    reset() {
        this.field.reset();
        this.field.setValue('startTime', undefined);
        this.field.setValue('endTime', undefined);
    }
    render() {
        const { init } = this.field;
        return (
            <IceContainer title="搜索预换表记录">
                <Form field={this.field}>
                    <Row wrap>
                        <Col {...span}>
                            <Form.Item label="用户编号：" {...formItemLayout}>
                                <Input {...init('cno')} placeholder="请输入用户编号" style={{ width: 220 }} />
                            </Form.Item>
                        </Col>
                        <Col {...span}>
                            <Form.Item label="用户名称：" {...formItemLayout}>
                                <Input {...init('cname')} placeholder="请输入用户名称" style={{ width: 220 }} />
                            </Form.Item>
                        </Col>
                        <Col {...span}>
                            <Form.Item label="旧表厂家：" {...formItemLayout}>
                                <Select
                                    {...init('oldWatermeterCompany')}
                                    dataSource={waterMeterCompany}
                                    placeholder="请选择旧表厂家"
                                    style={{ width: 220 }}
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row wrap>
                        <Col {...span}>
                            <Form.Item label="旧水表号：" {...formItemLayout}>
                                <Input
                                    {...init('oldWatermeterId')}
                                    placeholder="请输入旧水表号"
                                    style={{ width: 220 }}
                                />
                            </Form.Item>
                        </Col>
                        <Col {...span}>
                            <Form.Item label="新水表号：" {...formItemLayout}>
                                <Input
                                    {...init('newWatermeterId')}
                                    placeholder="请输入新水表号"
                                    style={{ width: 220 }}
                                />
                            </Form.Item>
                        </Col>
                        <Col {...span}>
                            <Form.Item label="新表厂家：" {...formItemLayout}>
                                <Select
                                    {...init('watermeterCompany')}
                                    dataSource={waterMeterCompany}
                                    placeholder="请选择新表厂家"
                                    style={{ width: 220 }}
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row wrap>
                        <Col {...span}>
                            <Form.Item label="旧表种类：" {...formItemLayout}>
                                <Select
                                    {...init('oldWatermeterKind')}
                                    dataSource={waterMeterKind}
                                    placeholder="请选择旧表种类"
                                    style={{ width: 220 }}
                                />
                            </Form.Item>
                        </Col>
                        <Col {...span}>
                            <Form.Item label="新表种类：" {...formItemLayout}>
                                <Select
                                    {...init('watermeterKind')}
                                    dataSource={waterMeterKind}
                                    placeholder="请选择新表种类"
                                    style={{ width: 220 }}
                                />
                            </Form.Item>
                        </Col>
                        <Col {...span}>
                            <Form.Item label="换表时间：" {...formItemLayout}>
                                <RangePicker
                                    {...init('queryWatchTime', {
                                        props: { onChange: (value, str) => this.changeTimeFm(value, str) },
                                    })}
                                    style={{ width: 220 }}
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
                <div align="center">
                    <Button
                        type="primary"
                        className="button"
                        onClick={() => this.refreshTable()}
                        style={{ marginRight: 30 }}
                    >
                        <Icon type="search" />
                        查询
                    </Button>
                    <Button type="secondary" className="button" onClick={() => this.reset()}>
                        <Icon type="refresh" />
                        重置
                    </Button>
                </div>
            </IceContainer>
        );
    }
}

export default Search;
