import React, { Component } from 'react';
import axios from 'axios';
import qs from 'qs';
import BasicsTable from '@/common/BasicsTable';
import { url } from '@/components/URL';
import { Button, Feedback } from '@icedesign/base';
import IceContainer from '@icedesign/container';
import RealEstateDetail from './RealEstateDetail';
import UpdateHno from './UpdateHno';

export class PageList extends Component {
    state = {
        realEstateModalVisible: false,
        realEstateRecord: {},
        updateHnoModalVisible: false,
    };

    componentDidMount = () => {
        this.props.getRecord({ ...this.props.pagination });
    };

    // 翻页
    changePage = (pageIndex) => {
        this.props.getRecord({ ...this.props.pagination, page: pageIndex, ...this.props.searchParams });
    };

    // 改编页码
    onPageSizeChange = (pageSize) => {
        const { changePageSize } = this.props;
        changePageSize(pageSize);
    };

    // 查看按钮点击触发
    viewRealEstate = (record) => {
        console.log('record dddd', record);
        axios({
            method: 'post',
            url: `${url}revenue/userTransfer/getUserTransferDetail`,
            data: qs.stringify({ slid: record.slid }),
        })
            .then((response) => {
                this.setState({ realEstateRecord: response.data }, () => {
                    this.setRealEstateModalVisible(true);
                });
            })
            .catch((error) => {
                Feedback.toast.error('请求错误：' + error);
            });
    };

    updateHno = (record) => {
        this.setState({ realEstateRecord: record }, () => {
            this.setUpdateHnoModalVisible(true);
        });
    };

    // 切换查看不动产弹窗的显示/隐藏
    setRealEstateModalVisible = (boolean) => {
        this.setState({ realEstateModalVisible: boolean });
    };

    // 切换查看修改户号弹窗的显示/隐藏
    setUpdateHnoModalVisible = (boolean) => {
        this.setState({ updateHnoModalVisible: boolean });
    };

    renderActionButton = (record) => (
        <div style={{ justifyContent: 'space-around' }}>
            {record.allowTransfer === '1' ? (
                <div>
                    <Button type="primary" size="medium" onClick={() => this.viewRealEstate(record)}>
                        查看
                    </Button>
                    <br />
                    <br />
                </div>
            ) : null}
            {record.status === 0 ? (
                <div>
                    <Button
                        type="primary"
                        size="medium"
                        onClick={() => {
                            this.updateHno(record);
                        }}
                    >
                        修改用户编号
                    </Button>
                </div>
            ) : null}
        </div>
    );

    render() {
        const { pagination, dataSource } = this.props;
        const { updateHnoModalVisible, realEstateModalVisible, realEstateRecord } = this.state;
        const columns = [
            {
                title: '不动产受理号',
                dataIndex: 'slid',
                key: 'slid',
            },
            {
                title: '用户编号',
                dataIndex: 'cno',
                key: 'cno',
            },
            {
                title: '户号对应用户名称',
                dataIndex: 'sysName',
                key: 'sysName',
            },
            {
                title: '原用户名称',
                dataIndex: 'oldName',
                key: 'oldName',
            },
            {
                title: '新用户名称',
                dataIndex: 'newName',
                key: 'newName',
            },
            {
                title: '新联系电话',
                dataIndex: 'newContact',
                key: 'newContact',
            },
            {
                title: '不动产证号',
                dataIndex: 'cercode',
                key: 'cercode',
            },
            {
                title: '户号对应用户地址',
                dataIndex: 'sysAddress',
                key: 'sysAddress',
            },
            {
                title: '小区',
                dataIndex: 'regionName',
                key: 'regionName',
            },
            {
                title: '微机员',
                dataIndex: 'computerName',
                key: 'computerName',
            },
            {
                title: '抄表员',
                dataIndex: 'cname',
                key: 'cname',
            },
            {
                title: '不动产房屋坐落',
                dataIndex: 'address',
                key: 'address',
            },
            {
                title: '不动产提交时间',
                dataIndex: 'sLastUpdatetime',
                key: 'sLastUpdatetime',
            },
            {
                title: '过户状态',
                dataIndex: 'status',
                key: 'status',
                // eslint-disable-next-line react/no-unstable-nested-components
                cell: (value) =>
                    value === 0 ? (
                        <span style={{ color: 'red' }}>未过户</span>
                    ) : value === 1 ? (
                        <span style={{ color: '#6ee7b7' }}>已过户</span>
                    ) : (
                        '否'
                    ),
            },
            {
                title: '过户操作员',
                dataIndex: 'transferOperName',
                key: 'transferOperName',
                cell: (value) => (value ? value : '/'),
            },
            // {
            //     title: '过户时间',
            //     dataIndex: 'transferTime',
            //     key: 'transferTime',
            //     cell: (value) => (value ? value : '/'),
            // },
            {
                title: '操作',
                dataIndex: 'action',
                key: 'action',
                width: 160,
                cell: (action, index, record) => this.renderActionButton(record),
            },
        ];
        return (
            <IceContainer title="不动产过户记录列表">
                <BasicsTable
                    columns={columns}
                    dataSource={dataSource}
                    total={pagination.totalSize}
                    pageSize={pagination.pageSize}
                    page={pagination.page}
                    changePage={(value) => this.changePage(value)}
                    onPageSizeChange={(value) => this.onPageSizeChange(value)}
                    pageSizeList={[10, 50, 100]}
                />
                <RealEstateDetail
                    realEstateModalVisible={realEstateModalVisible}
                    realEstateRecord={realEstateRecord}
                    setRealEstateModalVisible={this.setRealEstateModalVisible}
                    getRecord={this.props.getRecord}
                    searchParams={this.props.searchParams}
                    pagination={this.props.pagination}
                />
                <UpdateHno
                    updateHnoModalVisible={updateHnoModalVisible}
                    realEstateRecord={realEstateRecord}
                    setUpdateHnoModalVisible={this.setUpdateHnoModalVisible}
                    getRecord={this.props.getRecord}
                    searchParams={this.props.searchParams}
                    pagination={this.props.pagination}
                />
            </IceContainer>
        );
    }
}

export default PageList;
