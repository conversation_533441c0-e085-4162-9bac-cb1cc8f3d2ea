import './RealEstateDetail.scss';
import React, {Component} from 'react';
import axios from 'axios';
import {url} from '@/components/URL';
import {
    Button,
    Dialog,
    Feedback,
    Field,
    Form,
    Grid,
    Input,
    Table
} from '@icedesign/base';

const { Row, Col } = Grid;
const FormItem = Form.Item;
const TableColumn = Table.Column;

const topColumnArray = [
    { label: '不动产受理号：', title: 'slid', disabled: true },
    { label: '用户编号：', title: 'cno', disabled: true },
    { label: '过户时间：', title: 'transferTime', disabled: true },
    { label: '新用户名称：', title: 'newName', disabled: false },
    { label: '新用户证件号码：', title: 'newIdc', disabled: false },
    { label: '新用户联系电话：', title: 'newContact', disabled: false },
    { label: '原用户名称：', title: 'oldName', disabled: true },
    { label: '原用户证件号码：', title: 'oldIdc', disabled: true },
    { label: '原用户联系方式：', title: 'oldContact', disabled: true },
    { label: '不动产单元号：', title: 'unitNo', disabled: true },
    { label: '房屋坐落：', title: 'address', disabled: true },
    { label: '不动产证号：', title: 'cercode', disabled: true },
    { label: '系统用户名：', title: 'sysName', disabled: true },
    { label: '系统地址：', title: 'sysAddress', disabled: true },
    { label: '操作员姓名：', title: 'transferOperName', disabled: true },
];

const columns = [
    { title: '用户编号', dataIndex: 'cno', key: 'cno' },
    { title: '用户名称', dataIndex: 'cname', key: 'cname' },
    { title: '联系方式', dataIndex: 'contact', key: 'contact' },
    { title: '水表编号', dataIndex: 'watermeterId', key: 'watermeterId' },
    { title: '水表类型', dataIndex: 'watermeterType', key: 'watermeterType' },
    { title: '水表厂家', dataIndex: 'watermeterCompany', key: 'watermeterCompany' },
    { title: '用户余额', dataIndex: 'account', key: 'account' },
    {
        title: '远传表账单状态',
        dataIndex: 'rbillStatus',
        key: 'rbillStatus',
        cell: (value) =>
            value === '0' ? (
                <span style={{ color: 'red' }}>未结清</span>
            ) : value === '1' ? (
                <span style={{ color: '#6ee7b7' }}>已结清</span>
            ) : (
                ''
            ),
    },
    {
        title: '机械表账单状态',
        dataIndex: 'mbillStatus',
        key: 'mbillStatus',
        cell: (value) =>
            value === '0' ? (
                <span style={{ color: 'red' }}>未结清</span>
            ) : value === '1' ? (
                <span style={{ color: '#6ee7b7' }}>已结清</span>
            ) : (
                ''
            ),
    },
];

export class RealEstateDetail extends Component {
    constructor(props) {
        super(props);
        this.field = new Field(this, { autoUnmount: true });
    }

    componentWillReceiveProps(nextProps) {
        this.field.setValues(nextProps.realEstateRecord);
    }

    closeRealEstateModal = () => {
        const { setRealEstateModalVisible } = this.props;
        setRealEstateModalVisible(false);
        this.setState({});
    };

    popupConfirm = () => {
        const { setRealEstateModalVisible, getRecord, pagination, searchParams, realEstateRecord } = this.props;

        Dialog.confirm({
            v2: true,
            title: '确认过户',
            content: '确认要过户吗？',
            onOk: () => {
                const staffName = sessionStorage.getItem('realName');
                const staffId = sessionStorage.getItem('stuffId');
                axios({
                    method: 'post',
                    url: `${url}revenue/userTransfer/execTransfer`,
                    data: {
                        slid: realEstateRecord.slid,
                        newName: this.field.getValue('newName'),
                        newIdc: this.field.getValue('newIdc'),
                        newContact: this.field.getValue('newContact'),
                        transferOperId: staffId,
                        transferOperName: staffName,
                    },
                }).then((response) => {
                    const { status, data } = response;
                    if (status === 200 && data.code === '0') {
                        Feedback.toast.success('过户成功！');

                        setRealEstateModalVisible(false);
                        getRecord({ pagination, ...searchParams });
                    } else if (status === 201 && data.code === '1') {
                        Feedback.toast.error('过户更名失败！该条记录已过户完成。');
                    } else {
                        Feedback.toast.error('请求错误');
                    }
                });
            },
        });
    };

    render() {
        const { realEstateModalVisible, realEstateRecord } = this.props;
        const { init } = this.field;
        console.log('🆑 => RealEstateDetail => render => this.props =>', realEstateRecord, this.props);

        return (
            <Dialog
                title="不动产过户详情"
                className="modal-size"
                visible={realEstateModalVisible}
                onClose={() => this.closeRealEstateModal()}
                footer={
                    realEstateRecord.status === 0 ? (
                        <>
                            <Button type="primary" size="large" onClick={() => this.popupConfirm()}>
                                确认过户
                            </Button>
                            <Button size="large" onClick={() => this.closeRealEstateModal()}>
                                取消
                            </Button>
                        </>
                    ) : (
                        false
                    )
                }
            >
                <Form>
                    <Row wrap={true}>
                        {topColumnArray.map((item, index) => {
                            return (
                                <Col span={8} key={index}>
                                    <FormItem
                                        label={item.label}
                                        labelCol={{ span: 8 }}
                                        wrapperCol={{ span: 16 }}
                                        style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}
                                    >
                                        <Input
                                            disabled={item.disabled}
                                            {...init(item.title, {
                                                initValue: realEstateRecord[item.title],
                                            })}
                                            className="disabledInput"
                                        />
                                    </FormItem>
                                </Col>
                            );
                        })}
                    </Row>
                </Form>

                <Table dataSource={realEstateRecord.customerWatermeterList}>
                    {columns.map((item, index) => {
                        return (
                            <TableColumn
                                key={index}
                                title={item.title}
                                dataIndex={item.dataIndex}
                                cell={item.cell}
                                align="center"
                            />
                        );
                    })}
                </Table>
            </Dialog>
        );
    }
}

export default RealEstateDetail;
