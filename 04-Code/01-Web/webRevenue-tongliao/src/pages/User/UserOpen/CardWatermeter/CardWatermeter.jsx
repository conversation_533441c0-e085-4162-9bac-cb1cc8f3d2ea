import React, {Component} from 'react';
import {
    <PERSON><PERSON>,
    Feedback,
    Field,
    Form,
    Grid,
    Input,
    moment,
    Select,
    Upload
} from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import qs from 'qs';
import {url} from '../../../../components/URL/index';
import {
    areaCode,
    systemCode28,
    systemCode42
} from '../../../../components/areaCode/areaCode';
import watermeterKindNo from '../../../../common/watermeterKindNo';
import cardStatus from '../../../../common/cardStatus';
import withRouter from 'react-router-dom/es/withRouter';
import {location} from '../../../../common/WaterMeterCollocation';

const { Row, Col } = Grid;
const FormItem = Form.Item;
const { Combobox } = Select;

const formItemLayout = {
    labelCol: {
        fixedSpan: 6,
    },
    wrapperCol: {
        span: 14,
    },
};

@withRouter
export default class CardWatermeter extends Component {
    constructor(props) {
        super(props);
        this.state = {
            regionList: [], //小区
            openButton: false,
            flag: false,
            watermeterValue: null,
            searchValue: {},
            fileNameList: '',
            createId: sessionStorage.getItem('stuffId'),
            createName: sessionStorage.getItem('realName'),
            locationValue: '',
        };
        this.field = new Field(this, { autoUnmount: true });
    }

    componentWillMount() {
        let param = this.props.location.state;
        if (param != undefined || param != null) {
            this.field.setValues({ ...param.record });
            this.setState({ searchValue: param.searchValue });
        }
    }

    //片区联动小区
    areaOnChange(value) {
        this.field.setValue('areaId', value);
        this.field.reset('regionId');
        axios({
            method: 'post',
            url: `${url}revenue/region/regionlist`,
            data: qs.stringify({ areaId: value }),
        })
            .then((response) => {
                let regionList = [];
                response.data.datas.map((item) => {
                    regionList.push({ label: item.regionName, value: item.id });
                });
                this.setState({ regionList: regionList });
            })
            .catch((error) => {
                Feedback.toast.error('网络连接错误');
            });
    }

    onChangeOfRegionId(value) {
        this.field.setValue('regionId', value);
        axios({
            method: 'get',
            url: `${url}revenue/user/getNextCopyNoByRegionId?regionId=${value}`,
            data: null,
        })
            .then((response) => {
                this.field.setValue('copyNo', response.data.datas);
            })
            .catch((error) => {
                Feedback.toast.error('网络连接错误');
            });
    }

    //失去焦点查询水表
    handleOnBlur() {
        let watermeterId = this.field.getValue('watermeterId');
        axios({
            method: 'post',
            url: `${url}revenue/watermeter/get`,
            data: qs.stringify({ watermeterId: watermeterId }),
        })
            .then((response) => {
                if (response.data.code == '0') {
                    let value = response.data.datas;
                    if (value.watermeterType == '机械表') {
                        alert('机械表请从机械表标签页开户');
                        this.field.reset('watermeterId');
                        this.field.reset('watermeterType');
                        this.field.reset('watermeterKind');
                    } else {
                        this.field.setValues({ ...response.data.datas });
                        this.setState({ watermeterValue: value });
                    }
                } else if (response.data.msg == '水表不存在') {
                    this.setState({ flag: true, watermeterValue: null });
                    this.field.reset('watermeterType');
                    this.field.reset('watermeterKind');
                } else {
                    Feedback.toast.error(response.data.msg);
                    this.field.reset('watermeterId');
                }
            })
            .catch((error) => {
                Feedback.toast.error('网络连接错误');
            });
    }

    //校验水表编号是否合法
    checkedTunnage = (rule, value, callback) => {
        if (value) {
            if (isNaN(value) || value.indexOf('.') > 0) {
                callback('水表编号只能是数字');
            } else {
                callback();
            }
        } else {
            callback('必填');
        }
    };

    //校验信用额度
    checkedCreditLine = (rule, value, callback) => {
        if (value) {
            if (value.indexOf('.') > 0) {
                callback('信用额度不能为小数');
            } else {
                callback();
            }
        } else {
            callback();
        }
    };

    checkCopyNo = (rule, value, callback) => {
        if (value) {
            if (value.toString().indexOf('.') > 0) {
                callback('只能输入正整数');
            } else if (value <= 0) {
                callback('只能输入正整数');
            } else {
                callback();
            }
        } else {
            callback();
        }
    };

    //改变水表类型
    changeWaterType(value) {
        this.field.setValue('watermeterType', value);
        this.field.reset('watermeterKind');
        let temp = [];
        if (value == 'IC卡表') {
            temp = [
                { label: '预付费2', value: '预付费2' },
                { label: '预付费5', value: '预付费5' },
                { label: '阶梯4428', value: '阶梯4428' },
                { label: '预付费4442', value: '预付费4442' },
            ];
        } else if (value == '远传表') {
            temp = [
                { label: '无线远传', value: '无线远传' },
                { label: '有线远传', value: '有线远传' },
            ];
        }
        this.setState({
            waterKind: temp,
        });
    }

    //校验现场电话是否合法
    checkedNumber = (rule, value, callback) => {
        if (value) {
            if (isNaN(value) || value.indexOf('.') > 0) {
                callback('电话号码只能是数字');
            } else {
                callback();
            }
        } else {
            callback();
        }
    };

    //表单验证
    doSave() {
        this.field.validate((errors, values) => {
            if (errors) {
                return;
            } else {
                this.setState({ openButton: true });
                axios({
                    method: 'post',
                    url: `${url}revenue/user/getCno`,
                })
                    .then((response) => {
                        if (response.data.code == '0') {
                            let cno = response.data.datas;
                            if (values.location === '其它位置') {
                                values.location = values.locationText;
                                delete values.locationText;
                            }
                            this.open(values, cno);
                        } else {
                            this.setState({ openButton: false });
                            Feedback.toast.error('错误：' + response.data.msg);
                        }
                    })
                    .catch((error) => {
                        this.setState({ openButton: false });
                        Feedback.toast.error('网络连接错误' + error);
                    });
            }
        });
    }

    //开户
    open(values, cno) {
        const { createId, createName, fileNameList } = this.state;
        let dataList = {};
        let watermeterId = this.field.getValue('watermeterId');
        let watermeterKind = this.field.getValue('watermeterKind');
        let type = watermeterKindNo(watermeterKind);
        let data = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
        let id = createId.substring(0, 4);
        let userCno = '';
        if (values.watermeterType == 'IC卡表') {
            if (values.watermeterCompany == '扬州恒信') {
                userCno = '10' + cno;
            } else {
                userCno = '0010000000' + cno;
            }
        } else if (values.watermeterType == '远传表') {
            userCno = '11' + cno;
        }
        let cardNo = '';
        if (watermeterKind == '预付费5') {
            cardNo = watermeterId;
        } else if (values.watermeterType == '远传表') {
            cardNo = '';
        } else {
            cardNo = userCno;
        }
        dataList.cno = userCno;
        dataList.cname = values.cname;
        dataList.phone = values.phone;
        dataList.domicileNum = values.domicileNum;
        dataList.areaId = values.areaId;
        dataList.creditLine = values.creditLine;
        dataList.regionId = values.regionId;
        dataList.remark = values.remark;
        dataList.feeId = values.feeId;
        dataList.identityCard = values.identityCard;
        dataList.address = values.address;
        dataList.createId = createId;
        dataList.createName = createName;
        dataList.customerWatermeters = [
            {
                watermeterId: watermeterId,
                cardNo: cardNo,
                watermeterType: values.watermeterType,
                watermeterCompany: values.watermeterCompany,
                watermeterCaliber: values.watermeterCaliber,
                watermeterKind: watermeterKind,
                lockNo: values.lockNo,
                location: values.location,
                wheelNumber: values.wheelNumber,
                cno: userCno,
            },
        ];
        dataList.materialImgs = fileNameList;
        dataList.actualName = values.actualName;
        dataList.actualAddress = values.actualAddress;
        dataList.actualContact = values.actualContact;

        if (values.watermeterType == 'IC卡表') {
            if (type == '1') {
                //钥匙开卡
                try {
                    let i = hxdll.newuser(userCno, '0000000000', 'yff', areaCode, type);
                    if (i == 0) {
                        axios({
                            method: 'post',
                            url: `${url}revenue/user/open`,
                            data: dataList,
                        })
                            .then((response) => {
                                this.setState({ openButton: false });
                                if (response.data.code === '0') {
                                    Feedback.toast.success('开户成功');
                                    let param = this.props.location.state;
                                    if (param != null || param != undefined) {
                                        this.setBeforeOpenUser(param.record.id);
                                    }
                                    alert('开户成功!客户的用户编号为：' + userCno);
                                    this.field.reset();
                                    this.setState({ locationValue: '' });
                                } else {
                                    Feedback.toast.error('开户失败:' + response.data.msg);
                                }
                            })
                            .catch((error) => {
                                this.setState({ openButton: false });
                                Feedback.toast.error('网络连接错误');
                            });
                    } else {
                        this.setState({ openButton: false });
                        Feedback.toast.error('开卡失败：' + cardStatus(i));
                    }
                } catch (e) {
                    this.setState({ openButton: false });
                    Feedback.toast.error('读卡控件失败或浏览器不支持,请使用IE');
                }
            } else if (type == '3') {
                //圆卡开卡
                try {
                    let i = hxdll.newuser(watermeterId, watermeterId, 'yff', areaCode, type);
                    if (i === 0) {
                        axios({
                            method: 'post',
                            url: `${url}revenue/user/open`,
                            data: dataList,
                        })
                            .then((response) => {
                                this.setState({ openButton: false });
                                if (response.data.code === '0') {
                                    Feedback.toast.success('开户成功');
                                    let param = this.props.location.state;
                                    if (param != null || param != undefined) {
                                        this.setBeforeOpenUser(param.record.id);
                                    }
                                    alert('开户成功!客户的用户编号为：' + userCno);
                                    this.field.reset('watermeterId');
                                    this.field.reset('watermeterType');
                                    this.field.reset('watermeterCompany');
                                    this.field.reset('watermeterCaliber');
                                    this.field.reset('watermeterKind');
                                    this.field.reset('location');
                                    this.setState({ locationValue: '' });
                                    this.field.reset('wheelNumber');
                                } else {
                                    Feedback.toast.error('开户失败:' + response.data.msg);
                                }
                            })
                            .catch((error) => {
                                this.setState({
                                    openButton: false,
                                });
                                Feedback.toast.error('请求错误：' + error);
                            });
                    } else {
                        this.setState({
                            openButton: false,
                        });
                        Feedback.toast.error('开卡失败错误代码：' + cardStatus(i));
                    }
                } catch (e) {
                    this.setState({ openButton: false });
                    alert('浏览器不支持ActiveX控件，请使用IE');
                }
            } else if (type == '6') {
                //预付费卡4442
                try {
                    //系统码|子表号|电子表号|购买量|关阀报警|囤积限量|购水次数|有效卡标志|IC卡号|用户编码|操作员|总购量|
                    const openCard4442Data =
                        systemCode42 +
                        '|1|' +
                        watermeterId.substring(0, 8) +
                        '|0|3|0|0|1|1|' +
                        userCno.substring(2) +
                        '|' +
                        id +
                        '|0|';
                    console.log('openCard4442Data -> ', openCard4442Data);
                    let i = SZHXMETERCARD_Web.HXCD_4442_UserCard_Web(openCard4442Data).split('|');
                    console.log('openCard4442Result -> ', i);
                    if (i[0] > 0) {
                        axios({
                            method: 'post',
                            url: `${url}revenue/user/open`,
                            data: dataList,
                        })
                            .then((response) => {
                                this.setState({ openButton: false });
                                if (response.data.code === '0') {
                                    let param = this.props.location.state;
                                    if (param != null || param != undefined) {
                                        this.setBeforeOpenUser(param.record.id);
                                    }
                                    Feedback.toast.success('开户成功');
                                    alert('开户成功!客户的用户编号为：' + userCno);
                                    this.field.reset('watermeterId');
                                    this.field.reset('watermeterType');
                                    this.field.reset('watermeterCompany');
                                    this.field.reset('watermeterCaliber');
                                    this.field.reset('watermeterKind');
                                    this.field.reset('location');
                                    this.setState({ locationValue: '' });
                                    this.field.reset('wheelNumber');
                                } else {
                                    Feedback.toast.error('开户失败:' + response.data.msg);
                                }
                            })
                            .catch((error) => {
                                this.setState({
                                    openButton: false,
                                });
                                Feedback.toast.error('请求错误：' + error);
                            });
                    } else {
                        this.setState({ openButton: false });
                        Feedback.toast.error('开卡失败:' + i[1]);
                    }
                } catch (e) {
                    this.setState({ openButton: false });
                    alert('浏览器不支持ActiveX控件，请使用IE');
                }
            } else if (type == '7') {
                //华旭阶梯4428开卡
                try {
                    //系统码|子表号|用户编号|操作员ID|购水次数|关阀报警|购水日期(yyyy-mm-dd HH:MM:SS)|购水额|累计购水额|囤积限额|限量1|限量2|限量3|单价1|单价2|单价3|单价4|购水类型|前次备份|首购时初始使用量|首购时初始使用额|
                    let i = SZHXMETERCARD_Web.HXCD_2046_UserCard_Web(
                        systemCode28 + '|1|' + userCno + '|' + id + '|0|3|' + data + '|0|0|0|0|1|2|1|1|1|1|5| |0|0|',
                    ).split('|');
                    if (i[0] > 0) {
                        axios({
                            method: 'post',
                            url: `${url}revenue/user/open`,
                            data: dataList,
                        })
                            .then((response) => {
                                this.setState({ openButton: false });
                                if (response.data.code === '0') {
                                    Feedback.toast.success('开户成功');
                                    let param = this.props.location.state;
                                    if (param != null || param != undefined) {
                                        this.setBeforeOpenUser(param.record.id);
                                    }
                                    alert('开户成功!客户的用户编号为：' + userCno);
                                    this.field.reset('watermeterId');
                                    this.field.reset('watermeterType');
                                    this.field.reset('watermeterCompany');
                                    this.field.reset('watermeterCaliber');
                                    this.field.reset('watermeterKind');
                                    this.field.reset('location');
                                    this.setState({ locationValue: '' });
                                    this.field.reset('wheelNumber');
                                } else {
                                    Feedback.toast.error('开户失败:' + response.data.msg);
                                }
                            })
                            .catch((error) => {
                                this.setState({ openButton: false });
                                Feedback.toast.error('请求错误：' + error);
                            });
                    } else {
                        this.setState({ openButton: false });
                        Feedback.toast.error('开卡失败:' + i[1]);
                    }
                } catch (e) {
                    this.setState({ openButton: false });
                    alert('浏览器不支持ActiveX控件，请使用IE');
                }
            }
        } else {
            axios({
                method: 'post',
                url: `${url}revenue/user/open`,
                data: dataList,
            })
                .then((response) => {
                    this.setState({ openButton: false });
                    if (response.data.code == '0') {
                        Feedback.toast.success('开户成功');
                        let param = this.props.location.state;
                        if (param != null || param != undefined) {
                            this.setBeforeOpenUser(param.record.id);
                        }
                        alert('开户成功!客户的用户编号为：' + userCno);
                        this.field.reset();
                        this.setState({ locationValue: '' });
                    } else {
                        this.setState({ openButton: false });
                        Feedback.toast.error('开户失败:' + response.data.msg);
                    }
                })
                .catch((error) => {
                    this.setState({ openButton: false });
                    Feedback.toast.error('网络连接错误');
                });
        }
    }
    //修改预开户的用户状态
    setBeforeOpenUser(id) {
        axios({
            method: 'post',
            url: `${url}revenue/preOpening/update`,
            data: qs.stringify({ id: id }),
        })
            .then((response) => {
                if (response.data.code == '0') {
                    Feedback.toast.success('状态修改成功');
                } else {
                    Feedback.toast.error('状态修改失败');
                }
            })
            .catch((error) => {
                Feedback.toast.error('网络连接错误');
            });
    }

    checkFee(rule, value, callback) {
        if (
            this.field.getValue('watermeterCompany').indexOf('华旭') > -1 &&
            this.getFeeName(this.field.getValue('feeId')).indexOf('华旭') == -1
        ) {
            callback('请选择华旭水表的用水性质');
        } else {
            callback();
        }
    }

    getFeeName(feeId) {
        const { propertiesList } = this.props;
        let feeName = '';
        propertiesList.forEach((item) => {
            if (item.value == feeId) {
                feeName = item.label;
            }
        });
        return feeName;
    }

    beforeUpload = (info) => {
        // console.log("beforeUpload callback : ", info);
    };

    onChangeUpload = (info) => {
        // console.log("onChange callback : ", info);
    };

    onSuccess = (res, file) => {
        const { fileNameList } = this.state;
        // console.log("onSuccess callback : ", res, file);
        this.setState({
            fileNameList: fileNameList ? fileNameList + ',' + res.datas.src : res.datas.src,
        });
    };

    render() {
        const { flag, watermeterValue, openButton, regionList } = this.state;
        const { areaList, propertiesList } = this.props;
        const { init } = this.field;
        const watermeterCaliber = [
            { label: 15, value: '15' },
            { label: 20, value: '20' },
            { label: 25, value: '25' },
            { label: 40, value: '40' },
            { label: 50, value: '50' },
            { label: 80, value: '80' },
            { label: 100, value: '100' },
            { label: 150, value: '150' },
            { label: 200, value: '200' },
            { label: `100*32`, value: `100*32` },
        ];
        return (
            <div>
                <IceContainer title="用户资料登记">
                    <Form field={this.field} labelTextAlign="right">
                        <Row wrap>
                            <Col span={8}>
                                <FormItem label="用户名称：" {...formItemLayout}>
                                    <Input
                                        style={{ width: '90%' }}
                                        {...init('cname', { rules: [{ required: true, message: '请输入用户名称' }] })}
                                        placeholder="请输入用户名称"
                                    />
                                </FormItem>
                            </Col>
                            <Col span={8}>
                                <FormItem label="身份证号：" {...formItemLayout}>
                                    <Input
                                        style={{ width: '90%' }}
                                        maxLength={18}
                                        {...init('identityCard', {
                                            rules: [
                                                { required: true, min: 18, max: 18, message: '身份证号长度为18位' },
                                            ],
                                        })}
                                        placeholder="请输入身份证号"
                                    />
                                </FormItem>
                            </Col>
                            <Col span={8}>
                                <FormItem label="联系号码：" {...formItemLayout}>
                                    <Input
                                        style={{ width: '90%' }}
                                        maxLength={11}
                                        {...init('phone', {
                                            rules: [{ required: true, message: '请填写联系号码', min: 11, max: 11 }],
                                        })}
                                        placeholder="请输入联系号码"
                                    />
                                </FormItem>
                            </Col>
                        </Row>
                        <Row wrap>
                            <Col span={8}>
                                <FormItem label="选择片区：" {...formItemLayout}>
                                    <Select
                                        style={{ width: '90%' }}
                                        placeholder="请输入片区"
                                        {...init('areaId', { rules: [{ required: true, message: '请选择片区' }] })}
                                        dataSource={areaList}
                                        onChange={(value) => this.areaOnChange(value)}
                                    />
                                </FormItem>
                            </Col>
                            <Col span={8}>
                                <FormItem label="选择小区：" {...formItemLayout}>
                                    <Combobox
                                        style={{ width: '90%' }}
                                        {...init('regionId', { rules: [{ required: true, message: '请选择小区' }] })}
                                        placeholder="请先选择片区"
                                        fillProps="label"
                                        hasClear
                                        dataSource={regionList}
                                        onChange={(value) => {
                                            this.onChangeOfRegionId(value);
                                        }}
                                    />
                                </FormItem>
                            </Col>
                            <Col span={8}>
                                <FormItem label="用户地址：" {...formItemLayout}>
                                    <Input
                                        style={{ width: '90%' }}
                                        {...init('address', { rules: [{ required: true, message: '请填写地址' }] })}
                                        placeholder="请输入用户地址"
                                    />
                                </FormItem>
                            </Col>
                        </Row>
                        <Row wrap>
                            <Col span={8}>
                                <FormItem label="用水性质：" {...formItemLayout}>
                                    <Combobox
                                        style={{ width: '90%' }}
                                        {...init('feeId', {
                                            rules: [
                                                { required: true, message: '请选择用水性质' },
                                                { validator: this.checkFee.bind(this) },
                                            ],
                                        })}
                                        placeholder="--请选择或输入--"
                                        fillProps="label"
                                        hasClear
                                        dataSource={propertiesList}
                                    />
                                </FormItem>
                            </Col>
                            <Col span={8}>
                                <FormItem label="户籍人数(人)：" {...formItemLayout}>
                                    <Input
                                        style={{ width: '90%' }}
                                        {...init('domicileNum', {
                                            rules: [{ required: true, message: '请填写户籍人数' }],
                                            initValue: 3,
                                        })}
                                        placeholder="请输入户籍人数"
                                    />
                                </FormItem>
                            </Col>
                            <Col span={8}>
                                <FormItem label="信用额度(元)：" {...formItemLayout}>
                                    <Input
                                        style={{ width: '90%' }}
                                        {...init('creditLine', {
                                            initValue: 0,
                                            rules: [{ validator: this.checkedCreditLine }],
                                        })}
                                        placeholder="请输入信用额度"
                                    />
                                </FormItem>
                            </Col>
                        </Row>
                        <Row wrap>
                            <Col span={16}>
                                <FormItem label="备注：" {...formItemLayout}>
                                    <Input style={{ width: '100%' }} {...init('remark')} placeholder="请输入备注" />
                                </FormItem>
                            </Col>
                            <Col span={8}>
                                <FormItem label="抄表序号：" {...formItemLayout}>
                                    <Input
                                        {...init('copyNo', {
                                            rules: [
                                                { required: true, message: '请输入抄表序号' },
                                                { validator: this.checkCopyNo },
                                            ],
                                        })}
                                        htmlType="number"
                                        style={{ width: 90 }}
                                    />
                                </FormItem>
                            </Col>
                            <Col span={8}>
                                <FormItem {...formItemLayout} label="上传资料：">
                                    <Upload
                                        listType="text-image"
                                        action={`${url}/revenue/uploadMaterial`}
                                        accept="image/png, image/jpg, image/jpeg, image/gif, image/bmp"
                                        beforeUpload={this.beforeUpload}
                                        onChange={this.onChangeUpload}
                                        onSuccess={this.onSuccess}
                                        withCredentials={true}
                                        limit={2}
                                        // {...init("upload", {
                                        //   valueName: "fileList",
                                        //   initValue: fileList
                                        // })}
                                    >
                                        <Button type="primary" style={{ margin: '0 0 10px' }}>
                                            上传文件
                                        </Button>
                                    </Upload>
                                </FormItem>
                            </Col>
                        </Row>
                    </Form>
                </IceContainer>

                <IceContainer title="现场信息登记">
                    <Form field={this.field} labelTextAlign="right">
                        <Row wrap>
                            <Col span={8}>
                                <FormItem label="牌匾：" {...formItemLayout}>
                                    <Input
                                        style={{ width: '100%' }}
                                        {...init('actualName', { rules: [{ required: false, message: '请填写' }] })}
                                        placeholder="请输入实际用户名称"
                                    />
                                </FormItem>
                            </Col>
                            <Col span={8}>
                                <FormItem label="位置：" {...formItemLayout}>
                                    <Input
                                        style={{ width: '100%' }}
                                        {...init('actualAddress', { rules: [{ required: false, message: '请填写' }] })}
                                        placeholder="请输入实际地址"
                                    />
                                </FormItem>
                            </Col>
                            <Col span={8}>
                                <FormItem label="电话：" {...formItemLayout}>
                                    <Input
                                        style={{ width: '100%' }}
                                        {...init('actualContact', {
                                            rules: [{ required: false, validator: this.checkedNumber }],
                                        })}
                                        placeholder="请输入实际用户电话"
                                    />
                                </FormItem>
                            </Col>
                        </Row>
                    </Form>
                </IceContainer>

                <IceContainer title="水表信息录入">
                    <Form field={this.field}>
                        <div style={{ display: 'flex', justifyContent: 'space-around' }}>
                            <div style={{ display: 'flex', flexDirection: 'column' }}>
                                <FormItem label="水表编号：">
                                    <Input
                                        {...init('watermeterId', {
                                            rules: [
                                                { required: true, message: '水表编号必填' },
                                                { validator: this.checkedTunnage },
                                                { pattern: /^\S*$/, message: '不能输入空格' },
                                            ],
                                        })}
                                        style={{ width: '160px' }}
                                        onBlur={() => this.handleOnBlur()}
                                    />
                                </FormItem>
                                <FormItem label="水表类型：">
                                    {flag ? (
                                        <Select
                                            placeholder="请选择水表类型"
                                            style={{ width: '160px' }}
                                            {...init('watermeterType', {
                                                rules: [{ required: true, message: '水表类型必填' }],
                                            })}
                                            onChange={(value) => this.changeWaterType(value)}
                                            disabled={watermeterValue ? true : false}
                                        >
                                            <Select.Option value="IC卡表">IC卡表</Select.Option>
                                        </Select>
                                    ) : (
                                        <Select
                                            placeholder="请选择"
                                            style={{ width: '160px', color: 'black' }}
                                            {...init('watermeterType', {
                                                rules: [{ required: true, message: '水表类型必填' }],
                                            })}
                                            onChange={(value) => this.changeWaterType(value)}
                                            disabled={watermeterValue ? true : false}
                                        >
                                            <Select.Option value="IC卡表">IC卡表</Select.Option>
                                            <Select.Option value="远传表">远传表</Select.Option>
                                        </Select>
                                    )}
                                </FormItem>
                                <FormItem label="水表厂家：">
                                    <Select
                                        {...init('watermeterCompany', {
                                            rules: [{ required: true, message: '水表厂家必填' }],
                                            initValue: '扬州恒信',
                                        })}
                                        style={{ width: '160px', color: 'black' }}
                                        maxLength={30}
                                        dataSource={[
                                            { label: '请选择', value: '' },
                                            { label: '扬州恒信', value: '扬州恒信' },
                                            { label: '辽宁民生', value: '辽宁民生' },
                                            { label: '深圳华旭', value: '深圳华旭' },
                                            { label: '山科', value: '山科' },
                                            { label: '杭州竞达', value: '杭州竞达' },
                                            { label: '山东科德', value: '山东科德' },
                                            { label: '湖南威铭', value: '湖南威铭' },
                                            { label: '河南新天', value: '河南新天' },
                                            {
                                                label: '宁夏隆基',
                                                value: '宁夏隆基'
                                            },
                                            { label: '威傲', value: '威傲' },
                                        ]}
                                        disabled={watermeterValue ? true : false}
                                    />
                                </FormItem>
                            </div>
                            <div style={{ display: 'flex', flexDirection: 'column' }}>
                                <FormItem label="水表口径：">
                                    <Select
                                        {...init('watermeterCaliber', {
                                            rules: [{ required: true, message: '水表口径必填' }],
                                        })}
                                        placeholder="--请选择--"
                                        style={{ width: 160, color: 'black' }}
                                        dataSource={watermeterCaliber}
                                        disabled={watermeterValue ? true : false}
                                        defaultValue={watermeterValue ? watermeterValue.watermeterCaliber : '15'}
                                    />
                                </FormItem>
                                <FormItem label="水表种类：">
                                    <Select
                                        placeholder="请先选择水表类型"
                                        style={{ width: '160px', color: 'black' }}
                                        {...init('watermeterKind', { rules: [{ required: true, message: '必填' }] })}
                                        dataSource={this.state.waterKind}
                                        disabled={watermeterValue ? true : false}
                                    />
                                </FormItem>
                                <FormItem label="&emsp;&emsp;表锁号：">
                                    <Input {...init('lockNo')} placeholder="--请输入--" style={{ width: 160 }} />
                                </FormItem>
                            </div>
                            <div style={{ display: 'flex', flexDirection: 'column' }}>
                                <FormItem>
                                    <div style={{ display: 'flex' }}>
                                        <FormItem label="安装位置：">
                                            <Select
                                                placeholder="--请选择--"
                                                style={{ width: '160px' }}
                                                {...init('location', {
                                                    rules: [{ required: true, message: '安装位置必填' }],
                                                    initValue: '厨房',
                                                })}
                                                dataSource={location}
                                                onChange={(value) => {
                                                    if (value === '其它位置') {
                                                        this.setState({ locationValue: value });
                                                    } else {
                                                        this.setState({ locationValue: '' });
                                                    }
                                                    this.field.setValue('location', value);
                                                }}
                                            />
                                        </FormItem>
                                        <FormItem>
                                            <Input
                                                {...init('locationText', {
                                                    rules: [
                                                        {
                                                            required: this.state.locationValue === '其它位置',
                                                            message: '安装位置必填',
                                                        },
                                                    ],
                                                })}
                                                placeholder="--请输入--"
                                                style={{
                                                    width: 160,
                                                    marginLeft: 8,
                                                    display: this.state.locationValue === '其它位置' ? '' : 'none',
                                                }}
                                            />
                                        </FormItem>
                                    </div>
                                </FormItem>
                                <FormItem label="字轮基数：">
                                    <Input
                                        {...init('wheelNumber', {
                                            rules: [{ required: true, message: '字轮基数必填' }],
                                            initValue: '0',
                                        })}
                                        style={{ width: '160px', color: 'black' }}
                                        maxLength={10}
                                        disabled={watermeterValue ? true : false}
                                    />
                                </FormItem>
                            </div>
                        </div>
                        <div style={{ textAlign: 'center', marginTop: 20 }}>
                            <Button
                                type="primary"
                                className="button"
                                onClick={() => this.doSave()}
                                loading={openButton}
                            >
                                开户
                            </Button>
                        </div>
                    </Form>
                </IceContainer>
            </div>
        );
    }
}
