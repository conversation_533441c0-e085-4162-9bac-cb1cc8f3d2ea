import React, {Component, Fragment} from 'react';
import {
  <PERSON><PERSON>,
  DatePicker,
  <PERSON><PERSON>back,
  Field,
  Form,
  Grid,
  Icon,
  Input,
  Pagination,
  Select,
  Table
} from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import qs from 'qs';
import {url} from '../../../components/URL/index';
import {Link} from 'react-router-dom';
import ViewDialog from './components/viewDialog'
import ReviewDialog from './components/reviewDialog'
import styles from "./index.module.scss";
import {formItemLayout, span} from "../../../common/FormCollocation";
import {
  waterMeterCompany,
  waterMeterKind,
  waterMeterType
} from "../../../common/WaterMeterCollocation";
import {selfOpenStatus} from "../../../common/UserCollocation";

const FormItem = Form.Item
const {Row, Col} = Grid;
const {RangePicker} = DatePicker;
const {Combobox} = Select
export default class SelfOpen extends Component {

  constructor(props) {
    super(props);
    this.state = {
      dataSource: [],
      propertiesList: [],
      roleNameList: [],
      page: 1,
      pageSize: 10,
      total: 0,
      visible: false,
      dataLoading: false,
      openValve: true,
      stuffId: sessionStorage.getItem('stuffId'),
      stuffName: sessionStorage.getItem('realName'),
      areaList: [],
      regionList: [],
      selectedRowKeys: [],
      selectedRecord: [],
    }
    this.field = new Field(this, {autoUnmount: true});
    this.rowSelection = {
      onChange: (ids, records) => {
        this.setState({
          selectedRowKeys: ids, selectedRecord: records,
        });
      },
    };
  }

  componentDidMount() {
    let param = this.props.location.state;
    if (param != null) {
      this.field.setValues({...param.searchValue})
    }
    this.querySelfOpening(1, 10);
    this.queryRoleName()
  }

  //查询列表
  querySelfOpening(page, pageSize) {
    this.setState({dataLoading: true});
    let values = this.field.getValues();
    values.page = page;
    values.pageSize = pageSize;
    delete values.queryOpenTime;
    delete values.timeOnchangeImport;
    axios.post(`${url}revenue/selfOpen/findByPagingForWeb`,
        qs.stringify(values))
    .then((response) => {
      this.setState({dataLoading: false});
      let jsondata = response.data;
      this.setState({
        dataSource: jsondata.datas,
        page: page,
        pageSize: pageSize,
        total: jsondata.totalSize,
        selectedRowKeys: [],
        selectedRecord: [],
      });
    }).catch((error) => {
      this.setState({dataLoading: false});
      Feedback.toast.error('axios请求异常:' + error);
    });
  };

  //重置
  reset() {
    this.field.reset();
    this.field.setValue("startTime", undefined);
    this.field.setValue("endTime", undefined);

  }

  //翻页
  changePage(page) {
    const {pageSize} = this.state;
    this.querySelfOpening(page, pageSize);
  }

  //改变pageSize
  onPageSizeChange(pageSize) {
    this.querySelfOpening(1, pageSize);
  }

  openUser = (value, index, record) => {
    const {page, pageSize} = this.state
    if (record.status == '2' || record.status == '3') {
      return (<div style={{display: 'flex', justifyContent: 'space-between'}}>
        <ViewDialog record={record}
                    refreshTable={() => this.querySelfOpening(page, pageSize)}/>
      </div>)
    }
    if (record.status == '0') {
      return (<div style={{display: 'flex', justifyContent: 'space-between'}}>
        <ReviewDialog record={record}
                      refreshTable={() => this.querySelfOpening(page,
                          pageSize)}/>
      </div>)
    }
    if (record.status == '1') {
      return (<div style={{display: 'flex', justifyContent: 'space-between'}}>
        <ViewDialog record={record}
                    refreshTable={() => this.querySelfOpening(page, pageSize)}/>
        <Link to={{
          pathname: '/user/UserOpen',
          state: {record: record, searchValue: this.field.getValues()}
        }}>
          <span style={{color: "#1DC11D", cursor: "pointer"}}>开户</span>
        </Link>
      </div>)
    }
  }

  uploadSuccess = (response) => {
    if (response.code == 0) {
      Feedback.toast.success('上传成功');
      this.querySelfOpening(1, 10);
    }
  }

  uploadError = (response) => {
    if (response.response.code == 1) {
      alert(response.response.msg);
    }
  }

  statusFm(value) {
    if (value == '1') {
      return <span style={{color: '#1DC11D'}}>已通过</span>;
    } else if (value == '0') {
      return <span style={{color: '#1C86EE'}}>进行中</span>;
    } else if (value == '2') {
      return <span style={{color: '#ff0000'}}>已拒绝</span>;
    } else if (value == '3') {
      return <span style={{color: '#1DC11D'}}>已开户</span>;
    }
  }

  //批量关阀

  timeOnchange = (val, str) => {
    this.field.setValue("startTime", str[0]);
    this.field.setValue("endTime", str[1]);
  }

  reviewTimeOnchange = (val, str) => {
    this.field.setValue("reviewStartTime", str[0]);
    this.field.setValue("reviewEndTime", str[1]);
  }

  timeOnchangeImport = (val, str) => {
    this.field.setValue("isTime", str[0]);
    this.field.setValue("ieTime", str[1]);
  }

  //导出
  downloadFile() {
    let values = this.field.getValues();
    let url1 = `${url}revenue/preOpening/exportExcel?n=1`;
    if (values.watermeterId) {
      url1 += '&watermeterId=' + values.watermeterId;
    }
    if (values.address) {
      url1 += '&address=' + encodeURIComponent(values.address);
    }
    if (values.watermeterKind) {
      url1 += '&watermeterKind=' + values.watermeterKind;
    }
    if (values.status) {
      url1 += '&status=' + values.status;
    }
    if (values.valve) {
      url1 += '&valve=' + values.valve;
    }
    if (values.feeId) {
      url1 += '&feeId=' + values.feeId;
    }
    if (values.csTime) {
      url1 += '&csTime=' + values.csTime;
    }
    if (values.ceTime) {
      url1 += '&ceTime=' + values.ceTime;
    }
    if (values.isTime) {
      url1 += '&isTime=' + values.isTime;
    }
    if (values.ieTime) {
      url1 += '&ieTime=' + values.ieTime;
    }
    if (values.createId) {
      url1 += '&createId=' + values.createId;
    }
    window.open(url1, 'about:blank');
  }

  //查询操作员
  queryRoleName() {
    const {createId} = this.state;
    axios({
      method: 'get',
      url: `${url}revenue/selfOpen/getReviewName` + '?n=' + Math.random(),
      data: qs.stringify({createId: createId}),
    })
    .then((response) => {
      if (response.data.code == 0) {
        this.setState({
          roleNameList: response.data.datas
        })
      }
    }).catch((error) => {
      Feedback.toast.error("请求错误：" + error);
    })
  }

  render() {
    const {
      dataSource,
      page,
      total,
      stuffId,
      stuffName,
      pageSize,
      dataLoading,
      roleNameList,
      createName
    } = this.state;
    const {init} = this.field;
    return (<Fragment>
      <IceContainer title="自主开户">
        <Form field={this.field}>
          <Row wrap>
            <Col {...span}>
              <FormItem label="水表编号：" {...formItemLayout}>
                <Input {...init('watermeterId')}
                       placeholder="请输入水表编号" style={{width: 220}}/>
              </FormItem>
            </Col>

            <Col {...span}>
              <FormItem label="用户编号：" {...formItemLayout}>
                <Input {...init('cno')} placeholder="请输入用户编号"
                       style={{width: 220}}/>
              </FormItem>
            </Col>
            <Col {...span}>
              <FormItem label="水表厂家：" {...formItemLayout}>
                <Select
                    {...init('watermeterCompany')}
                    dataSource={waterMeterCompany}
                    placeholder="请选择水表厂家"
                    className={styles.selectWidth}
                />
              </FormItem>
            </Col>
            <Col {...span}>
              <FormItem label="水表类型：" {...formItemLayout}>
                <Select {...init('watermeterType')}
                        dataSource={waterMeterType}
                        placeholder="请选择水表类型"
                        className={styles.selectWidth}/>
              </FormItem>
            </Col>

            <Col {...span}>
              <FormItem label="水表种类：" {...formItemLayout}>
                <Select  {...init('watermeterKind')}
                         dataSource={waterMeterKind}
                         placeholder="请选择水表种类"
                         className={styles.selectWidth}/>
              </FormItem>
            </Col>

            <Col {...span}>
              <FormItem label="申请状态：" {...formItemLayout}>
                <Select  {...init('status')} dataSource={selfOpenStatus}
                         placeholder="请选择申请状态"
                         className={styles.selectWidth}/>
              </FormItem>
            </Col>

            <Col {...span}>
              <FormItem label="申请时间：" {...formItemLayout}>
                <RangePicker
                    {...init('createTime', {
                      props: {onChange: (v, s) => this.timeOnchange(v, s)}
                    })}
                    style={{width: 220}}/>
              </FormItem>
            </Col>

            <Col {...span}>
              <FormItem label="审批时间：" {...formItemLayout}>
                <RangePicker
                    {...init('reviewTime', {
                      props: {
                        onChange: (v, s) => this.reviewTimeOnchange(v, s)
                      }
                    })}
                    style={{width: 220}}/>
              </FormItem>
            </Col>
            <Col {...span}>
              <FormItem label="&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;审批人：">
                <Combobox
                    {...init('reviewUid')}
                    placeholder="--请选择--"
                    fillProps="label"
                    hasClear
                    style={{width: 220}} dataSource={roleNameList}/>
              </FormItem>
            </Col>
          </Row>
        </Form>

        <div align="center">
          <Button type="primary" onClick={() => this.querySelfOpening(1, 10)}
                  className={styles.btn}>
            <Icon type="search"/>查询
          </Button>
          <Button type="secondary" onClick={() => this.reset()}
                  className={styles.btn}>
            <Icon type="refresh"/>重置
          </Button>
        </div>
      </IceContainer>

      <IceContainer title="自主开户列表">
        <div>
          {/*<Button type="primary" onClick={() => this.downloadFile()}*/}
          {/*        style={{marginLeft: 10, marginBottom: 10, width: 100}}>*/}
          {/*  <Icon type="share" style={{color: "#ffffff"}}/>*/}
          {/*  导出*/}
          {/*</Button>*/}
        </div>

        <Table dataSource={dataSource} isLoading={dataLoading}
               primaryKey="id">
          <Table.Column title="水表编号" dataIndex="watermeterId"
                        align="center"/>
          <Table.Column title="水表类型" dataIndex="watermeterType"
                        align="center"/>
          <Table.Column title="水表种类" dataIndex="watermeterKind"
                        align="center"/>
          <Table.Column title="水表厂家" dataIndex="watermeterCompany"
                        align="center"/>
          <Table.Column title="申请时间" dataIndex="createTime"
                        align="center"/>
          <Table.Column title="申请状态" dataIndex="status" align="center"
                        cell={(value) => this.statusFm(value)}/>
          <Table.Column title="用户编号" dataIndex="cno"
                        align="center"/>
          <Table.Column title="操作" cell={this.openUser} align="center"/>
        </Table>
        <div style={{display: 'flex', justifyContent: 'flex-end'}}>
          <Pagination
              style={{textAlign: 'right', marginTop: 15}}
              pageSizeSelector="dropdown"
              onChange={(page) => this.changePage(page)}
              onPageSizeChange={(pageSize) => this.onPageSizeChange(pageSize)}
              total={total}
              pageSize={pageSize}
              current={page}
              size="small"
              pageSizeList={[10, 30, 50, 100]}
          />
          <div
              style={{lineHeight: '58px', marginLeft: 10}}>共 {total} 条记录
          </div>
        </div>
      </IceContainer>
    </Fragment>)
  }
}
