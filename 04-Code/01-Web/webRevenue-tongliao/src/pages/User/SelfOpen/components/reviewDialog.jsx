import React, {Component} from 'react';
import {
  <PERSON><PERSON>,
  Button,
  <PERSON>alog,
  <PERSON><PERSON><PERSON>,
  Field,
  Form,
  Input
} from '@icedesign/base';
import {url} from '@/components/URL';
import axios from "axios";

const Tooltip = Balloon.Tooltip;

export default class ReviewDialog extends Component {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
    };
    this.field = new Field(this, {autoUnmount: true});
  }

  // 开启弹窗
  openDialog(record) {
    this.field.setValue('watermeterId', record.watermeterId);
    this.field.setValue('contractImgList', record.contractImgList);
    this.field.setValue('idCardImgList', record.idCardImgList);
    this.field.setValue('noticeImgList', record.noticeImgList);
    this.setState({visible: true});
  }

  handleReview(params) {
    this.field.validate((errors, values) => {
      const {record} = this.props;
      values.id = record.id;
      values.reviewName = sessionStorage.getItem('realName');
      values.reviewUid = sessionStorage.getItem('stuffId');
      values.status = params.reviewStatus;
      //拒绝status
      if (!errors) {
        axios({
          method: 'post', url: `${url}revenue/selfOpen/update`, data: values,
        })
        .then((response) => {
          if (response.data.code === '0') {
            Feedback.toast.success('成功');
            this.props.refreshTable();
            this.setState({visible: false});
          } else {
            Feedback.toast.error(response.data.msg);
          }
        })
        .catch((error) => {
          Feedback.toast.error('请求错误：' + error);
        });
      }
    });
  }

  // 关闭弹窗
  handleCancel() {
    this.setState({visible: false});
  }

  showBigImg = (i) => {
    this.setState({imgModal: true, imgUrl: i});
  };

  render() {
    const {visible} = this.state;
    const {record} = this.props;
    const view = (<span style={{color: '#1DC11D', cursor: 'pointer'}}
                        onClick={() => this.openDialog(record)}>
                审批
            </span>);
    const {init} = this.field;
    const {
      watermeterId,
      contractImgList,
      idCardImgList,
      noticeImgList,
      reviewName,
      reviewContent,
      status
    } = record;
    return (<section>
      <Tooltip trigger={view} align="t" text="审批"/>

      <Dialog
          style={{width: 900}}
          visible={visible}
          onClose={() => this.handleCancel()}
          onCancel={() => this.handleCancel()}
          //再加个拒绝按钮
          // onOk={() => this.handleOk()}
          title="审批"
          isFullScreen
          footer={
            <div className="approval-operation-area">
                  <>
                    <div className="operation-button">
                      <Button
                          type="primary"
                          onClick={() => {
                            const params = {
                              reviewStatus: 1,
                            };
                            this.handleReview(params);
                          }}
                      >
                        同意
                      </Button>
                    </div>
                    <div className="operation-button">
                      <Button
                          className="operation-button"
                          type="primary"
                          shape="warning"
                          onClick={() => {
                            const params = {
                              reviewStatus: 2,
                            };
                            this.handleReview(params);
                          }}
                      >
                        拒绝
                      </Button>
                    </div>
                  </>
              <div className="operation-button">
                <Button className="operation-button" type="normal" onClick={() => this.handleCancel()}>
                  取消
                </Button>
              </div>
            </div>
          }
      >
        <Form field={this.field}>
          <div className="application-info-table">
            <div className="table-col">
              <div className="table-item">
                <div className="table-title">水表编号：</div>
                <div className="table-content">{watermeterId}</div>
              </div>
            </div>
            <div className="table-col">
              <div className="table-item table-item-size-3">
                <div className="table-title">购房合同照片：</div>
                <div className="table-content">
                  {contractImgList && contractImgList.map((i, index) => {
                    return (<div key={index} className="table-content-img-box">
                      <figure>
                        <figcaption onClick={() => this.showBigImg(i)}>
                          查看大图
                        </figcaption>
                        <img src={`${url}${i}`}/>
                      </figure>
                    </div>);
                  })}
                </div>
              </div>
            </div>
            <div className="table-col">
              <div className="table-item table-item-size-3">
                <div className="table-title">身份证照片：</div>
                <div className="table-content">
                  {idCardImgList && idCardImgList.map((i, index) => {
                    return (<div key={index} className="table-content-img-box">
                      <figure>
                        <figcaption onClick={() => this.showBigImg(i)}>
                          查看大图
                        </figcaption>
                        <img src={`${url}${i}`}/>
                      </figure>
                    </div>);
                  })}
                </div>
              </div>
            </div>
            <div className="table-col">
              <div className="table-item table-item-size-3">
                <div className="table-title">通知单照片：</div>
                <div className="table-content">
                  {noticeImgList && noticeImgList.map((i, index) => {
                    return (<div key={index} className="table-content-img-box">
                      <figure>
                        <figcaption onClick={() => this.showBigImg(i)}>
                          查看大图
                        </figcaption>
                        <img src={`${url}${i}`}/>
                      </figure>
                    </div>);
                  })}
                </div>
              </div>
            </div>
            {status == '0' ? (<div className="table-col">
              <div className="table-item table-item-size-3">
                <div className="table-title">审批意见：</div>
                <div className="table-content">
                  <Input
                      className="table-content-input-multiple"
                      multiple
                      placeholder="请输入审批意见"
                      {...init('reviewContent')}
                  />
                </div>
              </div>
            </div>) : (<div key={item.id} className="table-col">
              <div className="table-item">
                <div className="table-title">审批人：</div>
                <div className="table-content">{reviewName}</div>
              </div>
              <div className="table-item table-item-size-2">
                <div className="table-title">审批意见：</div>
                <div className="table-content">{reviewContent}</div>
              </div>
            </div>)}
          </div>
        </Form>
      </Dialog>
      <Dialog
          className="modal-size show-img-modal"
          visible={this.state.imgModal}
          footer={false}
          onClose={() => this.setState({ imgModal: false })}
      >
        <img src={`${url}${this.state.imgUrl}`} />
      </Dialog>
    </section>);
  }
}
