import React, {Component} from 'react';
import {
  Balloon,
  Button,
  Dialog,
  Field,
  Form,
  Icon,
  Input
} from '@icedesign/base';
import {url} from '../../../../components/URL';
import axios from 'axios/index';
import qs from 'qs';
import {Feedback, Grid} from '@icedesign/base/index';

const Tooltip = Balloon.Tooltip;
const FormItem = Form.Item;
const { Row } = Grid;

export default class updateYearTunnageDialog extends Component {
    constructor(props) {
        super(props);
        this.state = {
            sVisible: false,
            loading: false,
            createId: sessionStorage.getItem('stuffId'),
            createName: sessionStorage.getItem('realName'),
        };
        this.field = new Field(this, { autoUnmount: true });
    }

    // 关闭弹窗
    handleCancel() {
        this.setState({ sVisible: false });
    }

    // 打开弹窗
    openSDialog(record) {
        this.setState({ sVisible: true });
        this.field.setValue('yearTunnage', record.yearTunnage);
    }

    // 修改用户特殊信息
    sUpdate(record) {
        this.field.validate((errors, values) => {
            if (!errors) {
                this.setState({ loading: true });
                const { createId, createName } = this.state;
                const list = {
                    yearTunnage: values.newYearTunnage,
                    reason: values.reason,
                    cno: record.cno,
                    createId,
                    createName,
                };
                console.log('🆑 => updateYearTunnageDialog => this.field.validate => list', list);
                axios
                    .post(`${url}revenue/billCopy/updateYearTunnage`, qs.stringify(list))
                    .then((response) => {
                        console.log('🆑 => updateYearTunnageDialog => .then => response', response);
                        if (response.data.code === '0') {
                            Feedback.toast.success('修改成功');
                            this.setState({ sVisible: false, loading: false });
                            this.props.queryUser();
                        } else {
                            Feedback.toast.error('修改失败');
                            this.setState({ loading: false });
                        }
                    })
                    .catch((error) => {
                        Feedback.toast.error('网络错误请刷新页面重试');
                    });
            }
        });
    }

    checkedPay = (rule, value, callback) => {
        if (value) {
            if (rule.field === 'newYearTunnage' && value < 0) {
                callback('年度购水量不能为负数');
            } else {
                callback();
            }
        } else {
            callback('必填');
        }
    };

    render() {
        const { sVisible } = this.state;
        const { init } = this.field;
        const formItemLayout = {
            labelCol: { span: 6 },
            wrapperCol: { span: 18 },
        };
        const { record } = this.props;
        const sUpdate = (
            <Icon
                type="form"
                size="small"
                style={{ color: '#1DC11D', cursor: 'pointer' }}
                onClick={() => this.openSDialog(record)}
            />
        );
        const footer = (
            <div style={{ textAlign: 'center' }}>
                <Button
                    className="button"
                    type="primary"
                    onClick={() => this.sUpdate(record)}
                    loading={this.state.loading}
                    style={{ marginRight: 24 }}
                >
                    修改
                </Button>
                <Button className="button" type="primary" onClick={() => this.handleCancel()}>
                    取消
                </Button>
            </div>
        );
        return (
            <section>
                <Tooltip trigger={sUpdate} align="t" text="修改年度购水量" />

                <Dialog
                    style={{ width: 700 }}
                    visible={sVisible}
                    onClose={() => this.handleCancel()}
                    onCancel={() => this.handleCancel()}
                    footer={footer}
                    title="修改年度购水量"
                    isFullScreen
                >
                    <Form field={this.field} direction="hoz">
                        <Row>
                            <FormItem {...formItemLayout} label="当前用户年度购水量：" style={{ width: '100%' }}>
                                <Input {...init('yearTunnage')} style={{ backgroundColor: '#E6E6E6' }} readOnly />
                            </FormItem>
                        </Row>
                        <Row>
                            <FormItem {...formItemLayout} label="修改后年度购水量：" style={{ width: '100%' }}>
                                <Input
                                    htmlType="number"
                                    {...init('newYearTunnage', { rules: [{ validator: this.checkedPay }] })}
                                />
                            </FormItem>
                        </Row>
                        <Row>
                            <FormItem {...formItemLayout} label="修改原因：" style={{ width: '100%' }}>
                                <Input {...init('reason', { rules: [{ validator: this.checkedPay }] })} />
                            </FormItem>
                        </Row>
                    </Form>
                </Dialog>
            </section>
        );
    }
}
