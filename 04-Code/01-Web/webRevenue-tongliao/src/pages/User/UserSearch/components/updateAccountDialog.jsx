import React, {Component} from 'react';
import {
  <PERSON>oon,
  Button,
  Dialog,
  Field,
  Form,
  Icon,
  Input
} from '@icedesign/base'
import {url} from "../../../../components/URL";
import axios from "axios/index";
import qs from 'qs';
import {Feedback, Grid} from "@icedesign/base/index";

const Tooltip = Balloon.Tooltip
const FormItem = Form.Item
const {Row} = Grid

export default class UpdateAccountDialog extends Component {

  constructor(props) {
    super(props);
    this.state = {
      sVisible: false,
      loading: false,
      createId: sessionStorage.getItem("stuffId"),
      createName: sessionStorage.getItem("realName"),
    }
    this.field = new Field(this, {autoUnmount: true});
  }

  //关闭弹窗
  handleCancel() {
    this.setState({sVisible: false})
  }

  //打开弹窗
  openSDialog(record) {
    this.setState({sVisible: true})
    this.field.setValue('account', record.account)
  }

  //修改用户特殊信息
  sUpdate(record) {
    this.setState({loading: true})
    const {createId, createName} = this.state
    let values = {}
    values.cno = record.cno
    values.account = this.field.getValue('newAccount') ? this.field.getValue('newAccount') : '0'
    values.createId = createId
    values.createName = createName
    values.reason = this.field.getValue('reason')
    axios({
      method: 'post',
      url: `${url}revenue/user/changeAccount`,
      data: qs.stringify(values),
    })
      .then((response) => {
        if (response.data.code == "0") {
          Feedback.toast.success("修改成功");
          this.setState({sVisible: false, loading: false})
          this.props.queryUser()
        } else {
          Feedback.toast.error("修改失败");
        }
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
      });
  }

  checkedPay = (rule, value, callback) => {
    if (value) {
      if (value < 0) {
        callback('余额不能为负数')
      } else if (isNaN(value)) {
        callback('余额必须是数字')
      } else {
        callback();
      }
    } else {
      callback('必填')
    }
  }

  render() {
    const {sVisible} = this.state
    const {init} = this.field
    const formItemLayout = {
      labelCol: {fixedSpan: 8}
    }
    const {record} = this.props
    const sUpdate = (<Icon type="dollar" size="small" style={{color: "#1DC11D", cursor: "pointer"}}
                           onClick={() => this.openSDialog(record)}/>)
    const footer = (
      <div style={{textAlign: 'center'}}>
        <Button className="button" type="primary" onClick={() => this.sUpdate(record)} loading={this.state.loading}
                style={{marginRight: 20}}>修改</Button>
        <Button className="button" type="primary" onClick={() => this.handleCancel()}>取消</Button>
      </div>
    )
    return (
      <section>

        <Tooltip trigger={sUpdate} align='t' text='修改余额'/>

        <Dialog style={{width: 700}} visible={sVisible} onClose={() => this.handleCancel()}
                onCancel={() => this.handleCancel()}
                footer={footer}
                title="修改用户余额"
                isFullScreen
        >

          <Form field={this.field} direction="hoz">

            <Row>
              <FormItem {...formItemLayout} label="当前用户余额：">
                <Input  {...init('account')} style={{width: 150, backgroundColor: '#E6E6E6'}} readOnly/>
              </FormItem>

              <FormItem {...formItemLayout} label="修改余额：">
                <Input  {...init('newAccount', {rules: [{validator: this.checkedPay}]})} style={{width: 150}}/>
              </FormItem>
            </Row>

            <Row>
              <FormItem {...formItemLayout} label="修改原因：">
                <Input  {...init('reason')} style={{width: 150}} />
              </FormItem>
            </Row>

          </Form>

        </Dialog>

      </section>
    )
  }
}
