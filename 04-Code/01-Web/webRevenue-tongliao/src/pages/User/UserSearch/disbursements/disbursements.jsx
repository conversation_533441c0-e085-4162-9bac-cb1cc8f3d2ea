import React, {Component, Fragment} from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Form,
  Grid,
  Input
} from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import qs from 'qs';
import {url} from '../../../../components/URL/index';
import {withRouter} from 'react-router-dom';

const { Row } = Grid;
const FormItem = Form.Item;

@withRouter
export default class Disbursements extends Component {

  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      selectUser: '',
      purchase: false,
      watermeters: '',
      waterByPay: false,
      createId: sessionStorage.getItem('stuffId'),
      createName: sessionStorage.getItem('realName'),
    };
    this.field = new Field(this, { autoUnmount: true });
  }

  componentDidMount() {
    const temp = this.props.location.state;
    const param = this.props.location.state;
    if (param == undefined || temp == undefined) {
      this.props.history.push('/user/UserSearch');
      return;
    } else {
      if (param) {
        this.setState({ selectUser: param.selectUser });
        let icCard = param.selectUser;
        this.field.setValues({ ...icCard });
      } else {
        this.setState({ selectUser: temp.selectUser });
        let nCard = temp.selectUser;
        this.field.setValues({ ...nCard });
      }
    }
  }

  //打开销户弹窗
  confirm() {
    this.setState({ visible: true });
  }

  //关闭弹窗
  handleCancel() {
    this.setState({ visible: false });
  }

  //计算剩余水量
  calcTunnage(value) {
    this.field.setValue('wheelNumber', value);
    let totalTunnage = this.field.getValue('totalTunnage');
    if (value > totalTunnage) {
      alert('输入的字轮读数不能大于总购水量');
      this.field.setValue('wheelNumber', '');
      this.field.setValue('surplusWater', '');
      return;
    } else {
      if (!value) {
        this.field.setValue('surplusWater', '');
      } else {
        let surplusWater = Number(totalTunnage) - Number(value);
        this.field.setValue('surplusWater', surplusWater);
      }
    }
  }

  //根据水量计算金额
  computerWater(selectUser) {
    this.field.validate((errors, values) => {
      if (errors) {
        return;
      } else {
        let cno = selectUser.cno;
        let tunnage = this.field.getValue('surplusWater');
        let param = {};
        param.tunnage = tunnage;
        param.cno = cno;
        this.setState({
          waterByPay: true,
        });
        axios({
          method: 'post',
          url: `${url}revenue/fee/clear`,
          data: qs.stringify(param),
        })
          .then(response => {
            let jsondata = response.data.datas;
            if (response.data.code == '0') {
              let surplusPrice = jsondata.amount;
              let account = selectUser.account;
              let returnPrice = (Number(surplusPrice) + Number(account)).toFixed(2);
              this.field.setValue('surplusPrice', surplusPrice);
              this.field.setValue('returnPrice', returnPrice);
              this.setState({ waterByPay: false });
            } else {
              alert(response.data.msg);
              this.setState({ waterByPay: false });
            }
          })
          .catch(error => {
            this.setState({ waterByPay: false });
            Feedback.toast.error('Axios请求失败' + error);
          });
      }
    });
  }

  //销户
  disbursements(selectUser) {
    this.setState({ purchase: true });
    const { createId, createName } = this.state;
    let values = [];
    values.createId = createId;
    values.createName = createName;
    values.cno = selectUser.cno;
    {
      selectUser.watermeterType == 'IC卡表' ?
        values.tunnage = 0 : void (0);
    }
    {
      selectUser.watermeterType == 'IC卡表' ?
        values.amount = 0
        :
        values.amount = selectUser.account;
    }

    values.watermeterId = selectUser.watermeterId;
    values.reason = this.field.getValue('reason');
    values.customerId = selectUser.customerId;
    //暂时设为固定值0
    values.wheelNumber = 0;
    values.returnPrice = 0;
    axios({
      method: 'post',
      url: `${url}revenue/customerCancel/cancel`,
      data: qs.stringify(values),
    })
      .then(response => {
        let jsondata = response.data.datas;
        if (response.data.code == '0') {
          Feedback.toast.success('销户成功');
          this.setState({ visible: false, purchase: false });
          this.props.history.push('/user/UserSearch');
        } else {
          Feedback.toast.error('销户失败' + response.data.msg);
        }
      })
      .catch(error => {
        this.setState({ waterByPay: false, purchase: false });
        Feedback.toast.error('Axios请求失败' + error);
      });
  }

  render() {
    const { selectUser, visible } = this.state;
    const { init } = this.field;
    const buyItemLayout = {
      labelCol: {
        fixedSpan: 12,
      },
    };
    const buyItemLayout1 = {
      labelCol: {
        fixedSpan: 8,
      },
    };
    const footer = (
      <span>
        <Button className="button" onClick={() => this.disbursements(selectUser)} loading={this.state.purchase}
                type="primary"
                style={{ marginRight: 20 }}>
          确认销户
        </Button>
        <Button className="button" onClick={() => this.handleCancel()}>
          &emsp;取消&emsp;
        </Button>
      </span>
    );
    return (
      <Fragment>

        <IceContainer title='基本信息'>

          <div style={{ display: 'flex', justifyContent: 'space-around', fontSize: 16 }}>

            <div style={{ display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>

              <FormItem label="用户编号：" style={{ display: 'flex' }}>
                {selectUser ? selectUser.cno : void (0)}
              </FormItem>

              <FormItem label="联系电话：" style={{ display: 'flex' }}>
                {selectUser ? selectUser.phone : void (0)}
              </FormItem>


              <FormItem label="用户地址：" style={{ display: 'flex' }}>
                {selectUser ? selectUser.address : void (0)}
              </FormItem>

            </div>

            <div style={{ display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>

              <FormItem label="用户名称：" style={{ display: 'flex' }}>
                {selectUser ? selectUser.cname : void (0)}
              </FormItem>

              <FormItem label="&emsp;&emsp;小区：" style={{ display: 'flex' }}>
                {selectUser ? selectUser.regionName : void (0)}
              </FormItem>

              <FormItem label="身份证号：" style={{ display: 'flex' }}>
                {selectUser ? selectUser.identityCard : void (0)}
              </FormItem>

            </div>

            <div style={{ display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>

              <FormItem label="用水性质：" style={{ display: 'flex' }}>
                {selectUser ? selectUser.feeName : void (0)}
              </FormItem>


              <FormItem label="&emsp;&emsp;片区：" style={{ display: 'flex' }}>
                {selectUser ? selectUser.areaName : void (0)}
              </FormItem>

              <FormItem label="余&emsp;&emsp;额：" style={{ display: 'flex' }}>
                {selectUser ? selectUser.account : void (0)}
              </FormItem>

            </div>

          </div>

        </IceContainer>

        {
          selectUser.watermeterType === 'IC卡表' ?
            <IceContainer title='水表信息'>
              <Form field={this.field} direction="hoz">

                <Row>
                  <FormItem label="水表编号：" {...buyItemLayout}>
                    <Input {...init('watermeterId')} style={styles.readOnlyStyle} readOnly/>
                  </FormItem>
                  <FormItem label="水表种类：" labelCol={{ fixedSpan: 14 }}>
                    <Input {...init('watermeterKind')} style={styles.readOnlyStyle} readOnly/>
                  </FormItem>
                </Row>

                {
                  selectUser.watermeterKind == '阶梯4428' ?
                    <Row>
                      <FormItem label="累计金额" {...buyItemLayout} >
                        <Input {...init('totalFee', { initValue: 0 })} style={styles.readOnlyStyle}/>
                      </FormItem>
                    </Row> :
                    <Row>
                      <FormItem label="累计购水量" {...buyItemLayout} >
                        <Input {...init('totalTunnage', { initValue: 0 })} style={styles.readOnlyStyle}/>
                      </FormItem>
                    </Row>
                }


                <Row>
                  <FormItem label="销户原因：" {...buyItemLayout}>
                    <Input {...init('reason')} style={styles.buyWater} style={{ width: 460 }}/>
                  </FormItem>
                </Row>

                <div align="center" style={{ marginTop: 20 }}>
                  <Button type="primary" className="button" onClick={() => this.confirm()}>销户</Button>
                </div>
              </Form>
            </IceContainer>
            :
            <IceContainer title='水表信息'>

              <Form field={this.field} direction="hoz">

                <FormItem label="水表编号：" {...buyItemLayout}>
                  <Input {...init('watermeterId')} style={styles.buyWater} readOnly/>
                </FormItem>

                <FormItem label="水表种类：" labelCol={{ fixedSpan: 14 }}>
                  <Input {...init('watermeterKind')} style={styles.readOnlyStyle} readOnly/>
                </FormItem>

                <FormItem label="退还总金额（元）：" {...buyItemLayout} >
                  <Input {...init('account', { initValue: 0 })} style={styles.buyWater} readOnly/>
                </FormItem>

                <FormItem label="销户原因：" labelCol={{ fixedSpan: 14 }}>
                  <Input {...init('reason')} style={{ width: 250 }}/>
                </FormItem>

                <FormItem label="字轮基数："  {...buyItemLayout}>
                  <Input {...init('wheelNumber')} style={styles.buyWater} readOnly />
                </FormItem>

                <FormItem label="本期示数："  labelCol={{ fixedSpan: 14 }}>
                  <Input {...init('thisNum')} readOnly />
                </FormItem>

                <div align="center" style={{ marginTop: 20 }}>
                  <Button type="primary" className="button" onClick={() => this.confirm()}>销户</Button>
                </div>
              </Form>
            </IceContainer>
        }

        <Dialog visible={visible}
                onClose={() => this.handleCancel()} title='销户' footer={footer} footerAlign="center"
                style={{ width: 450 }}>
          <section style={{ textAlign: 'left' }}>
            <div style={{ color: '#ff0000', marginTop: 5 }}>提示:销户将作废该用户【未结清】的账单或【未完成】的订单</div>
            <div style={{ textAlign: 'center', marginTop: 5 }}>是否确认销户？</div>
          </section>
        </Dialog>
      </Fragment>
    );
  }
}

const styles = {
  contentText: {
    padding: '5px 0 15px',
  },
  inputStyle: {
    width: 160,
    border: 0,
  },
  buyWater: {
    width: 150,
  },
  buyRecord: {
    width: 100,
    border: 0,
  },
  readOnlyStyle: {
    width: 150,
    backgroundColor: '#E6E6E6',
  },
  confimInput: {
    width: 120,
    color: 'red',
  },
  confimSpecialInput: {
    width: 120,
    backgroundColor: '#F3DEDF',
  },
};
