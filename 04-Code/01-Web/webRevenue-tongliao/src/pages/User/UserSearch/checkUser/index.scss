.userSearch{
  display: flex;
  justify-content: flex-start;
  margin-bottom: 10px;
}
.basicDetailTitle {
  margin: 10px 0;
  font-size: 16px;
}

.infoColumn {
  margin-bottom: 20px;
  margin-right: 100px;
}

.infoColumnTitle {
  margin: 15px 0;
  margin-bottom: 5px;
  padding-left: 30px;
  font-size:18px;
  font-weight: bold;
  padding: 12px;
}

.infoItems {
  padding: 0;
  margin-left: 25px;
  margin-top: 20px;
  font-size: 18px;
}
.infos{
  padding: 0;
  margin-left: 25px;
  margin-top: 20px;
}
.infoItem {
  margin-bottom: 18px;
  list-style: none;
  font-size: 14px;
  margin-top: 5px;
  margin-left: 50px;
}

.infoItemLabel {
  min-width: 70px;
  font-size: 14px;

}

.infoItemValue {
  margin-left: 6px
}

.attachLabel {
  min-width: 70px;
  color: #999;
  float: left;
}

.attachPics {
  width: 80px;
  height: 80px;
  border: 1px solid #eee;
  margin-right: 10px
}
.Accordion{
  border: 0px;
  color: rgb(95, 134, 243);
  margin-left: 10px

}


