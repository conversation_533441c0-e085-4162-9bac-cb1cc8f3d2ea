import React, {Component} from 'react';
import {
    <PERSON>oon,
    Button,
    DatePicker,
    Dialog,
    Feedback,
    Field,
    Form,
    Icon,
    Input,
    Pagination,
    Select,
    Table,
    Upload,
} from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import qs from 'qs';
import './index.scss';
import {url} from '../../../components/URL/index';
import {Link} from 'react-router-dom';
import {LoginURL} from '../../../components/URL/LoginURL';
import readCard from '../../../common/readCard';
import UpdateDialog from './components/updateDialog';
import SpecialInformationDialog from './components/specialInformationDialog';
import UpdateAccountDialog from './components/updateAccountDialog';
import UpdateYearTunnageDialog from './components/updateYearTunnageDialog';
import {systemCode28} from '../../../components/areaCode/areaCode';
import {
    waterMeterCaliber,
    waterMeterKind
} from '../../../common/WaterMeterCollocation';

const FormItem = Form.Item;
const Tooltip = Balloon.Tooltip;
const {RangePicker} = DatePicker;
const {Combobox} = Select;

export default class UserSearch extends Component {
    constructor(props) {
        super(props);
        this.state = {
            areaList: [], // 片区
            regionList: [], // 小区
            feeNameList: [], // 用水性质
            formSource: [], // 表格数据
            roleNameList: [], // 查询操作员
            groupNameList: [], // 查询操作员分组
            propertiesName: [],
            countHno: '', // 总户数
            page: 1,
            pageSize: 10,
            totalSize: 0,
            selectUser: '',
            index: 0,
            visible: false,
            dataLoading: false,
            expanded: false,
            roleName: [],
            collapse: false,
            addVisible: false,
            tableValue: [],
            status: 0,
            disbursementsDialog: false,
            visible2: false,
            visible3: false,
            submitButton: false,
            sVisible: false,
            createId: sessionStorage.getItem('stuffId'),
            createName: sessionStorage.getItem('realName'),
            buttons: [],
            loginURL: LoginURL,
            totalAccount: 0,
            zoneCodeList: [], // 区号
        };
        this.field = new Field(this, { autoUnmount: true });
    }

    componentDidMount() {
        const param = this.props.location.state;
        if (param != undefined) {
            this.field.setValues({ ...param.searchValue });
            this.field.setValue('openTime', [param.searchValue.csTime, param.searchValue.ceTime]);
            this.queryUser(1, 10);
        }
        this.getMenuByRole();
        this.queryFeeName();
        this.queryArea();
        this.queryRegion();
        this.queryRoleName();
        this.queryGroupNames();
        this.queryZoneCodeList();
    }

    // 查询总户数
    queryCountHno(values) {
        axios.post(`${url}revenue/user/countHno`, qs.stringify(values)).then((response) => {
            if (response.data.code == '0') {
                this.setState({
                    countHno: response.data.datas,
                });
            }
        });
    }

    // 查询总余额
    queryTotalAccount(values) {
        axios.post(`${url}revenue/user/sumTotalAccount`, qs.stringify(values)).then((response) => {
            if (response.data.code == '0') {
                this.setState({
                    totalAccount: response.data.datas,
                });
            }
        });
    }

    // 查询片区
    queryArea() {
        axios.get(`${url}revenue/area/getAll`).then((response) => {
            let areaList = [];
            response.data.datas.map((item) => {
                areaList.push({ label: item.name, value: item.id });
            });
            this.setState({ areaList: areaList });
        });
    }

    // 查询小区
    queryRegion() {
        axios.post(`${url}revenue/region/regionlist`).then((response) => {
            let regionList = [];
            response.data.datas.map((item) => {
                regionList.push({ label: item.regionName, value: item.id });
            });
            this.setState({ regionList: regionList });
        });
    }

    // 查询用水性质
    queryFeeName() {
        axios.post(`${url}revenue/fee/queryList`).then((response) => {
            let feeNameList = [];
            response.data.datas.map((item) => {
                feeNameList.push({ label: item.name, value: item.id });
            });
            this.setState({ feeNameList: feeNameList });
        });
    }

    // 查询操作员
    queryRoleName() {
        axios.get(`${url}revenue/user/createName`).then((response) => {
            if (response.data.code == 0) {
                this.setState({
                    roleNameList: response.data.datas,
                });
            }
        });
    }

    // 查询操作员分组
    queryGroupNames() {
        axios({
            method: 'get',
            url: `${url}revenue/operatorGroup/getGroupListForSelection`,
        })
            .then((response) => {
                if (response.data.code == 0) {
                    this.setState({
                        groupNameList: response.data.datas,
                    });
                }
            })
            .catch((error) => {
                Feedback.toast.error('请求错误：' + error);
            });
    }

    // 获取权限
    getMenuByRole = () => {
        const { loginURL } = this.state;
        axios
            .get(loginURL + '/getAuthority', {
                withCredentials: true,
            })
            .then((response) => {
                let jsondata = response.data;
                if (jsondata.statusCode == 0) {
                    this.setState({
                        buttons: jsondata.buttons,
                    });
                } else {
                    Feedback.toast.error('获取权限失败');
                }
            });
    };

    // 查询表格数据
    queryUser(page, pageSize) {
        this.setState({ dataLoading: true });
        let values = this.field.getValues();
        values.page = page;
        values.pageSize = pageSize;
        axios
            .post(`${url}revenue/customerCancel/findByPaging`, qs.stringify(values))
            .then((response) => {
                this.queryTotalAccount(values);
                this.queryCountHno(values);
                this.setState({
                    formSource: response.data.datas,
                    page: page,
                    pageSize: pageSize,
                    totalSize: response.data.totalSize,
                    dataLoading: false,
                });
            })
            .catch((error) => {
                Feedback.toast.error('网络错误请刷新页面重试');
                this.setState({ dataLoading: false });
            });
    }

    // 查询区号
    queryZoneCodeList() {
        axios.post(`${url}revenue/exportStatementApi/zoneCodeList`).then((response) => {
            let zoneCodeList = [];
            response.data.datas.map((item) => {
                zoneCodeList.push({ label: item, value: item });
            });
            this.setState({ zoneCodeList: zoneCodeList });
        });
    }

    // 翻页
    changePage = (page) => {
        const { pageSize } = this.state;
        this.queryUser(page, pageSize);
        window.scrollTo(0, 500);
    };

    // 改变每页显示
    changePageSize(pageSize) {
        this.queryUser(1, pageSize);
    }

    // 带条件查询
    queryUser1(cno) {
        this.setState({ dataLoading: true });
        let values = this.field.getValues();
        values.cno = cno;
        axios({
            method: 'post',
            url: `${url}revenue/user/sale`,
            data: qs.stringify(values),
        })
            .then((response) => {
                this.setState({
                    formSource: response.data.datas,
                    current: 1,
                    pageSize: 10,
                    totalSize: response.data.totalSize,
                    dataLoading: false,
                });
                const { selectUser, index } = this.state;
                this.props.history.push({ pathname: './UserSearch/disbursements', state: { selectUser: selectUser } });
            })
            .catch((error) => {
                Feedback.toast.error('请求错误：' + error);
                this.setState({ dataLoading: false });
            });
    }

    /* 表格操作栏 */
    operation = (value, index, record) => {
        const { regionList, areaList, feeNameList, buttons, page, pageSize } = this.state;
        // 查看用户信息
        const look = (
            <Link
                to={{
                    pathname: `./UserSearch/checkUser`,
                    state: { selectUser: record, searchValue: this.field.getValues() },
                }}
            >
                <Icon type="browse" size="small" style={{ color: '#FFA003', cursor: 'pointer' }} />
            </Link>
        );

        // 销户
        const disbursements =
            record.watermeterType === 'IC卡表' ? (
                <Link to={{ pathname: `./UserSearch/Disbursements`, state: { selectUser: record } }}>
                    <Icon type="ashbin" size="small" style={{ color: '#FF3333', cursor: 'pointer' }} />
                </Link>
            ) : (
                <Icon
                    type="ashbin"
                    size="small"
                    style={{ color: '#FF3333', cursor: 'pointer' }}
                    onClick={() => this.setState({ disbursementsDialog: true, selectUser: record, index: index })}
                />
            );

        let ishasSepcial = false;
        let ishasAcount = false;
        let isAbletoUpdateYearTunnage = false
        for (let i = 0; i < buttons.length; i++) {
            if (buttons[i].menuPath == '/user/UserSearch') {
                if (buttons[i].buttonCode == 'updateKeyInfo') {
                    ishasSepcial = true;
                }
                if (buttons[i].buttonCode == 'updateAccout') {
                    ishasAcount = true;
                }
                if (buttons[i].buttonCode == 'updateYearTunnage') {
                    isAbletoUpdateYearTunnage = true;
                }
            }
        }

        return (
            <div className="operation">
                <Tooltip trigger={look} align="t" text="查看" />

                {record.status == 0 ? (
                    void 0
                ) : (
                    <UpdateDialog
                        regionList={regionList}
                        areaList={areaList}
                        record={record}
                        queryUser={() => this.queryUser(page, pageSize)}
                    />
                )}

                {record.status != 0 && ishasSepcial ? (
                    <SpecialInformationDialog
                        feeNameList={feeNameList}
                        record={record}
                        queryUser={() => this.queryUser(1, 10)}
                    />
                ) : (
                    void 0
                )}

                {record.status != 0 && ishasAcount ? (
                    <UpdateAccountDialog record={record} queryUser={() => this.queryUser(1, 10)}/>
                ) : (
                    void 0
                )}

                {record.status == 0 ? void 0 : <Tooltip trigger={disbursements} align="t" text="销户"/>}

                {record.status != 0 && isAbletoUpdateYearTunnage ? (
                    <UpdateYearTunnageDialog record={record} queryUser={() => this.queryUser(1, 10)}/>
                ) : (
                    void 0
                )}


            </div>
        );
    };

    /* 表格状态栏 */
    renderStatus(value) {
        if (value == 0) {
            return <span style={{ color: '#ff0000' }}>销户</span>;
        } else if (value == 1) {
            return <span style={{ color: '#1DC11D' }}>正常</span>;
        } else if (value == 3) {
            return <span style={{ color: '#000000' }}>换机械表</span>;
        } else {
            return <span style={{ color: '#ffa631' }}>异常</span>;
        }
    }

    /* 打开弹窗 */
    openDialog(record) {
        this.setState({ visible: true, selectUser: record });
        this.field.setValue('hno', record.hno);
        this.field.setValue('cno', record.cno);
        this.field.setValue('domicileNum', record.domicileNum);
        this.field.setValue('cname', record.cname);
        this.field.setValue('waterId', record.waterId);
        this.field.setValue('address', record.address);
        this.field.setValue('areaId', record.areaId);
        this.field.setValue('regionId', record.regionId);
        this.field.setValue('phone', record.phone);
        this.field.setValue('identityCard', record.identityCard);
        this.field.setValue('poor', record.poor);
        this.field.setValue('creditLine', record.creditLine);
        this.field.setValue('remark', record.remark);
        this.field.setValue('status', record.status);
    }

    /* 关闭弹窗 */
    handleCancel() {
        this.setState({
            visible: false,
            addVisible: false,
            status: 0,
            disbursementsDialog: false,
            visible2: false,
            visible3: false,
            sVisible: false,
        });
    }

    /* 弹窗确定回掉函数 */
    handleOk() {
        const { selectUser } = this.state;
        this.field.validate((errors, values) => {
            let list = [];
            list.cname = values.cname;
            list.address = values.address;
            list.identityCard = values.identityCard;
            list.phone = values.phone;
            list.creditLine = values.creditLine;
            list.remark = values.remark;
            list.id = selectUser.customerId;
            list.hno = values.hno;
            list.areaId = values.areaId;
            list.regionId = values.regionId;
            list.status = values.status;
            list.cno = values.cno;
            if (errors) {
                return;
            } else {
                axios({
                    method: 'post',
                    url: `${url}revenue/user/modify`,
                    data: qs.stringify(list),
                })
                    .then((response) => {
                        if (response.data.code == '0') {
                            Feedback.toast.success('修改成功');
                            this.queryUser();
                        } else {
                            alert(response.data.msg);
                        }
                    })
                    .catch((error) => {
                        Feedback.toast.error('请求错误：' + error);
                    });
            }
            this.setState({ visible: false });
        });
    }

    // 重置
    reset() {
        this.field.reset();
    }

    // 结算水量:1.打开填示数的Dialog,2.获取上期示数
    countMoney(selectUser) {
        this.setState({ visible3: true });
        let cno = selectUser.cno;
        let watermeterType = selectUser.watermeterType;
        let values = {};
        values.cno = cno;
        values.watermeterType = watermeterType;
        values.watermeterId = selectUser.watermeterId;
        axios.post(`${url}revenue/watermeterChange/lastNum`, qs.stringify(values)).then((response) => {
            let jsondata = response.data;
            if (jsondata.code == 0) {
                this.field.setValue('lastNum', jsondata.datas);
            } else {
                alert(jsondata.msg);
            }
        });
    }

    // 不结算水量
    noCountMoney = () => {
        const { selectUser } = this.state;
        this.setState({ disbursementsDialog: false });
        this.judgeHasUnpaidBill(selectUser);
    };

    // 判断是否有未结清账单
    judgeHasUnpaidBill(selectUser) {
        let cno = selectUser.cno;
        axios.post(`${url}revenue/watermeterChange/checkUnpaid`, qs.stringify({ cno: cno })).then((response) => {
            let jsondata = response.data;
            if (jsondata.code === '0') {
                if (jsondata.datas === 0) {
                    this.queryUser1(cno);
                } else {
                    // 展示提示有未结清账单的Dialog
                    this.setState({ visible2: true });
                }
            } else {
                alert(jsondata.msg);
            }
        });
    }

    // 提交：上传示数
    submitReader = (selectUser) => {
        const { createId, createName } = this.state;
        let cno = selectUser.cno;
        let watermeterType = selectUser.watermeterType;
        let lastNum = this.field.getValue('lastNum');
        let thisNum = this.field.getValue('thisNum');
        if (thisNum.indexOf('.') < 0) {
            this.setState({ submitButton: true });
            let values = {};
            values.cno = cno;
            values.watermeterType = watermeterType;
            values.lastNum = lastNum;
            values.thisNum = thisNum;
            values.createId = createId;
            values.createName = createName;
            values.type = '7';
            axios({
                method: 'post',
                url: `${url}revenue/watermeterChange/upload`,
                data: qs.stringify(values),
            })
                .then((response) => {
                    this.setState({ submitButton: false });
                    let jsondata = response.data;
                    if (jsondata.code == 0) {
                        // 关闭上传示数的页面
                        this.setState({ visible1: false });
                        this.judgeHasUnpaidBill(selectUser);
                    } else {
                        alert(jsondata.msg);
                    }
                })
                .catch((error) => {
                    this.setState({
                        submitButton: false,
                    });
                    Feedback.toast.error('axios请求失败' + error);
                });
        } else {
            Feedback.toast.error('字轮读数不能为小数');
        }
    };

    // 读卡
    doRead() {
        try {
            // 读恒信卡
            let readtype = hxdll.chk_card();
            console.log('readtype -> ', readtype)
            // let readtype = 1
            // 读华旭卡
            let readtypeHuaxu = SZHXMETERCARD_Web.HXCD_2046_DisposeData_web('1|1|' + systemCode28 + '|', '').split('|');
            console.log('readtypeHuaxu -> ', readtypeHuaxu)
            let readtypeHuaxu_4442 = SZHXMETERCARD_Web.HXCD_4442_DisposeData_Web('1|1|', '').split('|');
            console.log('readtypeHuaxu_4442 -> ', readtypeHuaxu_4442)
            if (readtypeHuaxu_4442[0] > -1) {
                if (readtypeHuaxu_4442[0] == 1 || readtypeHuaxu_4442[0] == 9) {
                    if (readtypeHuaxu_4442[0] == 9) {
                        let cno = readtypeHuaxu_4442[3]
                        console.log('cno -> ', cno)
                        this.field.setValue('cno', '00' + cno)
                        this.queryUser(1, 10);
                    } else {
                        let cno = readtypeHuaxu_4442[26]
                        console.log('cno -> ', cno)
                        this.field.setValue('cno', '00' + cno)
                        this.queryUser(1, 10);
                        // console.log('近12月用量', readtypeHuaxu_4442[23])  //近12月用量
                        // console.log(readtypeHuaxu_4442[24])  //刷表日期(yyyy-mm-dd HH:MM:SS) "1900-01-01 00:00:00"
                        // console.log('华旭', readtypeHuaxu_4442);
                        // const paramsData = {
                        //   watermeterId: readtypeHuaxu_4442[1],
                        //   tunnageList: [readtypeHuaxu_4442[12], readtypeHuaxu_4442[13], readtypeHuaxu_4442[14], readtypeHuaxu_4442[15], readtypeHuaxu_4442[16], readtypeHuaxu_4442[17], readtypeHuaxu_4442[18], readtypeHuaxu_4442[19], readtypeHuaxu_4442[20], readtypeHuaxu_4442[21], readtypeHuaxu_4442[22], readtypeHuaxu_4442[23]],
                        //   date: readtypeHuaxu_4442[24]
                        // }
                        // TODO: 数据存储到后台
                        // axios.post(`${url}revenue/user/saveHxtunnage`, paramsData)
                    }
                } else {
                    Feedback.toast.error('华旭卡读卡失败：' + readtypeHuaxu_4442[1]);
                }
                return
            }
            if (readtype > 1 && readtypeHuaxu[0] < 1) {
                if (readtype == '11' || readtype == '25') {
                    let readstr = hxdll.user_card();
                    let type = readstr.substring(1, 2);
                    let cardNo = 0;
                    if (type == '3' || type == '5') {
                        cardNo = readstr.substring(2, 12);
                    } else {
                        cardNo = readstr.substring(2, 10);
                    }
                    if (readstr.length > 5) {
                        this.field.setValue('cardNo', cardNo);
                        this.queryUser(1, 10);
                    } else {
                        Feedback.toast.error('恒信卡读卡失败：' + readCard(readstr));
                    }
                } else {
                    switch (readtype) {
                        case 0:
                            Feedback.toast.error('恒信卡读卡失败：设备失败');
                            return;
                        case 1:
                            Feedback.toast.error('恒信卡读卡失败：读卡器无卡');
                            return;
                        case 2:
                            Feedback.toast.error('恒信卡读卡失败：不存在的卡型，非恒信卡');
                            return;
                        case 3:
                            Feedback.toast.error('恒信卡读卡失败：读卡失败');
                            return;
                        case 4:
                            Feedback.toast.error('恒信卡读卡失败：坏卡');
                            return;
                        default:
                            Feedback.toast.error('恒信卡读卡失败：非用户卡');
                            return;
                    }
                }
            } else if (readtypeHuaxu[0] > -1 && readtype < 2) {
                if (readtypeHuaxu[0] == 1 || readtypeHuaxu[0] == 9 || readtypeHuaxu[0] == 6) {
                    if (readtypeHuaxu.length > 29 || readtypeHuaxu[0] == 6) {
                        let cardNo = readtypeHuaxu[2];
                        this.field.setValue('cardNo', cardNo);
                        this.queryUser(1, 10);
                    } else {
                        let cardNo = readtypeHuaxu[26];
                        this.field.setValue('cardNo', cardNo);
                        this.queryUser(1, 10);
                    }
                } else if (readtypeHuaxu[0] == 0) {
                    Feedback.toast.error('华旭卡读卡失败：非华旭卡');
                } else if (readtypeHuaxu[0] == -1) {
                    Feedback.toast.error('华旭卡读卡失败');
                } else if (readtypeHuaxu[0] == -2) {
                    Feedback.toast.error('华旭卡读卡失败：子表号数据无效');
                } else if (readtypeHuaxu[0] == 7) {
                    Feedback.toast.error('空卡');
                } else {
                    Feedback.toast.error('华旭卡读卡失败：非用户卡');
                }
            } else if (readtypeHuaxu[0] > -1 && readtype > 1) {
                Feedback.toast.error('读卡器上存在多张卡请检查！');
            } else if (readtype == 1 && readtypeHuaxu[1] == '未插卡!') {
                Feedback.toast.error('读卡器无卡');
            } else {
                Feedback.toast.error('设备失败');
            }
        } catch (e) {
            Feedback.toast.error('系统繁忙,请稍后再试');
        }
    }

    // 时间onchange
    timeOnchange = (val, str) => {
        this.field.setValue('openTime', val);
        this.field.setValue('csTime', str[0]);
        this.field.setValue('ceTime', str[1]);
    };

    // 片区onchange
    onChange(value, type) {
        this.field.setValue('areaId', value);
        // 区册查询
        axios({
            method: 'post',
            url: `${url}revenue/region/regionlist`,
            data: qs.stringify({ areaId: value }),
        })
            .then((response) => {
                let regionList = [];
                response.data.datas.map((item) => {
                    regionList.push({ label: item.regionName, value: item.id });
                });
                this.setState({ regionList: regionList });
            })
            .catch((error) => {
                Feedback.toast.error('请求异常', error);
            });
        if (type == 0) {
            this.field.reset('regionId');
        }
    }

    // 导出
    downloadFile() {
        let values = this.field.getValues();
        let url1 = `${url}revenue/excel/exportUserInfo?n=1`;
        if (values.cno) {
            url1 += '&cno=' + encodeURIComponent(values.cno);
        }
        if (values.cardNo) {
            url1 += '&cardNo=' + encodeURIComponent(values.cardNo);
        }
        if (values.watermeterId) {
            url1 += '&watermeterId=' + encodeURIComponent(values.watermeterId);
        }
        if (values.feeId) {
            url1 += '&feeId=' + encodeURIComponent(values.feeId);
        }
        if (values.csTime) {
            url1 += '&csTime=' + values.csTime;
        }else {
            return  Feedback.toast.error('开户时间不能为空！');
        }
        if (values.ceTime) {
            url1 += '&ceTime=' + values.ceTime;
        }else {
            return  Feedback.toast.error('开户时间不能为空！');
        }
        if (values.areaId) {
            url1 += '&areaId=' + encodeURIComponent(values.areaId);
        }
        if (values.regionId) {
            url1 += '&regionId=' + encodeURIComponent(values.regionId);
        }
        // encodeURIComponent
        if (values.cname) {
            url1 += '&cname=' + encodeURIComponent(values.cname);
        }
        if (values.hno) {
            url1 += '&hno=' + encodeURIComponent(values.hno);
        }
        if (values.statusli) {
            url1 += '&statusli=' + encodeURIComponent(values.statusli);
        }
        if (values.watermeterTypes) {
            console.log('🆑 => UserSearch => downloadFile => watermeterTypes', values.watermeterTypes);
            // url1 += '&watermeterTypes=' + encodeURIComponent(values.watermeterTypes);
            url1 +=
                '&watermeterTypes=' +
                encodeURIComponent(
                    `${values.watermeterTypes[0] ?? ''}${
                        values.watermeterTypes[1]
                            ? `,${values.watermeterTypes[1]}`
                            : values.watermeterTypes[2]
                            ? `,${values.watermeterTypes[2]}`
                            : ''
                    }`,
                );
        }
        if (values.createId) {
            url1 += '&createId=' + encodeURIComponent(values.createId);
        }
        if (values.watermeterKind) {
            url1 += '&watermeterKind=' + encodeURIComponent(values.watermeterKind);
        }
        // encodeURIComponent
        // encodeURI
        if (values.address) {
            url1 += '&address=' + encodeURIComponent(values.address);
        }
        if (values.remark) {
            url1 += '&remark=' + encodeURIComponent(values.remark);
        }
        if (values.watermeterCompany) {
            url1 += '&watermeterCompany=' + encodeURIComponent(values.watermeterCompany);
        }
        if (values.watermeterCaliber) {
            url1 += '&watermeterCaliber=' + encodeURIComponent(values.watermeterCaliber);
        }
        if (values.zoneCode) {
            url1 += '&zoneCode=' + encodeURIComponent(values.zoneCode);
        }
        if (values.groupId) {
            url1 += '&groupId=' + values.groupId;
        }
        window.open(url1, 'about:blank');
    }

    downloadBase() {
        let values = this.field.getValues();
        let url1 = `${url}revenue/excel/exportUserInfoBase?n=1`;
        if (values.cno) {
            url1 += '&cno=' + encodeURIComponent(values.cno);
        }
        if (values.cardNo) {
            url1 += '&cardNo=' + encodeURIComponent(values.cardNo);
        }
        if (values.watermeterId) {
            url1 += '&watermeterId=' + encodeURIComponent(values.watermeterId);
        }
        if (values.feeId) {
            url1 += '&feeId=' + encodeURIComponent(values.feeId);
        }
        if (values.csTime) {
            url1 += '&csTime=' + encodeURIComponent(values.csTime);
        }
        if (values.ceTime) {
            url1 += '&ceTime=' + encodeURIComponent(values.ceTime);
        }
        if (values.areaId) {
            url1 += '&areaId=' + encodeURIComponent(values.areaId);
        }
        if (values.regionId) {
            url1 += '&regionId=' + encodeURIComponent(values.regionId);
        }
        // encodeURIComponent
        if (values.cname) {
            url1 += '&cname=' + encodeURIComponent(values.cname);
        }
        if (values.hno) {
            url1 += '&hno=' + encodeURIComponent(values.hno);
        }
        if (values.statusli) {
            url1 += '&statusli=' + encodeURIComponent(values.statusli);
        }
        if (values.watermeterTypes) {
            console.log('🆑 => UserSearch => downloadFile => watermeterTypes', values.watermeterTypes);
            // url1 += '&watermeterTypes=' + encodeURIComponent(values.watermeterTypes);
            url1 +=
                '&watermeterTypes=' +
                encodeURIComponent(
                    `${values.watermeterTypes[0] ?? ''}${
                        values.watermeterTypes[1]
                            ? `,${values.watermeterTypes[1]}`
                            : values.watermeterTypes[2]
                                ? `,${values.watermeterTypes[2]}`
                                : ''
                    }`,
                );
        }
        if (values.createId) {
            url1 += '&createId=' + encodeURIComponent(values.createId);
        }
        if (values.watermeterKind) {
            url1 += '&watermeterKind=' + encodeURIComponent(values.watermeterKind);
        }
        // encodeURIComponent
        // encodeURI
        if (values.address) {
            url1 += '&address=' + encodeURIComponent(values.address);
        }
        if (values.remark) {
            url1 += '&remark=' + encodeURIComponent(values.remark);
        }
        if (values.watermeterCompany) {
            url1 += '&watermeterCompany=' + encodeURIComponent(values.watermeterCompany);
        }
        if (values.watermeterCaliber) {
            url1 += '&watermeterCaliber=' + encodeURIComponent(values.watermeterCaliber);
        }
        if (values.zoneCode) {
            url1 += '&zoneCode=' + encodeURIComponent(values.zoneCode);
        }
        if (values.groupId) {
            url1 += '&groupId=' + values.groupId;
        }
        window.open(url1, 'about:blank');
    }

    uploadSuccess = (response) => {
        if (response.code == 0) {
            Feedback.toast.success('导入成功');
            this.queryTerminal(1, 10);
        }
    };


    batchUpdateSuccess = (response) => {
        if (response.code == 0) {
            Feedback.toast.success('修改成功');
            this.queryTerminal(1, 10);
        }
    };

    uploadError = (response) => {
        if (response.response.code == 1) {
            alert(response.response.msg);
        }
    };

    render() {
        const {
            countHno,
            roleNameList,
            groupNameList,
            regionList,
            areaList,
            feeNameList,
            totalAccount,
            submitButton,
            visible3,
            disbursementsDialog,
            page,
            totalSize,
            pageSize,
            selectUser,
            dataLoading,
            formSource,
            visible2,
            zoneCodeList,
            createName,
            createId,
        } = this.state;
        const { init } = this.field;
        const formItemLayout = {
            labelCol: { fixedSpan: 8 },
        };

        const footer = (
            <div style={{ textAlign: 'center' }}>
                <Button
                    className="button"
                    type="primary"
                    onClick={() => this.countMoney(selectUser)}
                    style={{ marginRight: 20 }}
                >
                    结算
                </Button>
                <Button className="button" type="primary" onClick={() => this.noCountMoney()}>
                    不结算
                </Button>
            </div>
        );

        const footer1 = (
            <Button
                className="button"
                type="primary"
                onClick={() => this.submitReader(selectUser)}
                loading={submitButton}
            >
                提交
            </Button>
        );
        const footer2 = (
            <Link to={{ pathname: `/order/SaleWater` }}>
                <Button className="button" type="primary">
                    去缴费
                </Button>
            </Link>
        );
        return (
            <>
                <IceContainer title="用户信息">
                    <Form field={this.field}>
                        <div style={{ display: 'flex', justifyContent: 'space-around' }}>
                            <div style={{ display: 'flex', flexDirection: 'column' }}>
                                <FormItem label="用户编号：">
                                    <Input {...init('cno')} placeholder="--请输入--" style={{ width: 170 }} />
                                </FormItem>
                                <FormItem label="用户卡号：">
                                    <Input {...init('cardNo')} placeholder="--请输入--" style={{ width: 170 }} />
                                </FormItem>
                                <FormItem label="水表编号：">
                                    <Input {...init('watermeterId')} placeholder="--请输入--" style={{ width: 170 }} />
                                </FormItem>
                                <FormItem label="用水性质：">
                                    <Combobox
                                        {...init('feeId')}
                                        placeholder="--请选择或输入--"
                                        fillProps="label"
                                        hasClear
                                        style={{ width: 170 }}
                                        dataSource={feeNameList}
                                    />
                                </FormItem>
                                <FormItem label="开户时间：">
                                    <RangePicker
                                        // {...init('openTime')}
                                        onChange={(val, str) => this.timeOnchange(val, str)}
                                        style={{ width: '220px' }}
                                    />
                                </FormItem>
                                <FormItem label="&emsp;&emsp;备注：">
                                    <Input {...init('remark')} placeholder="--请输入--" style={{ width: 170 }} />
                                </FormItem>
                                <FormItem label="水表口径：">
                                    <Select
                                        {...init('watermeterCaliber')}
                                        placeholder="--请选择--"
                                        style={{ width: 170 }}
                                        dataSource={waterMeterCaliber}
                                    />
                                </FormItem>
                            </div>
                            <div style={{ display: 'flex', flexDirection: 'column' }}>
                                <FormItem label="&emsp;&emsp;&emsp;片区：">
                                    <Select
                                        {...init('areaId')}
                                        placeholder="请选择"
                                        dataSource={areaList}
                                        style={{ width: 170 }}
                                    />
                                </FormItem>
                                <FormItem label="&emsp;&emsp;&emsp;小区：">
                                    <Combobox
                                        {...init('regionId')}
                                        placeholder="--请选择小区--"
                                        fillProps="label"
                                        hasClear
                                        style={{ width: 170 }}
                                        dataSource={regionList}
                                    />
                                </FormItem>
                                <FormItem label="&emsp;用户名称：">
                                    <Input {...init('cname')} placeholder="--请输入--" style={{ width: 170 }} />
                                </FormItem>
                                <FormItem label="&emsp;用户户号：">
                                    <Input {...init('hno')} style={{ width: 170 }} placeholder="--请输入--" />
                                </FormItem>
                                <FormItem label="&emsp;用户状态：">
                                    <Select
                                        placeholder="请选择"
                                        style={{ width: '170px' }}
                                        {...init('statusli')}
                                        dataSource={[
                                            { label: '正常', value: '1' },
                                            { label: '销户', value: '0' },
                                            { label: '异常', value: '2' },
                                            { label: '换机械表', value: '3' },
                                        ]}
                                        multiple
                                    />
                                </FormItem>
                                <FormItem label="&emsp;水表厂家：">
                                    <Select
                                        {...init('watermeterCompany')}
                                        placeholder="请输入"
                                        style={{ width: '170px' }}
                                        dataSource={[
                                            {label: '请选择', value: ''},
                                            {label: '扬州恒信', value: '扬州恒信'},
                                            {label: '深圳华旭', value: '深圳华旭'},
                                            {label: '辽宁民生', value: '辽宁民生'},
                                            {label: '山科', value: '山科'},
                                            {label: '机械表厂家', value: '机械表厂家'},
                                            {label: '杭州竞达', value: '杭州竞达'},
                                            {label: '山东科德', value: '山东科德'},
                                            {label: '湖南威铭', value: '湖南威铭'},
                                            {label: '河南新天', value: '河南新天'},
                                            {
                                                label: '宁夏隆基',
                                                value: '宁夏隆基'
                                            },
                                            {label: '威傲', value: '威傲'},
                                        ]}
                                    />
                                </FormItem>
                                <FormItem label="二维码编号：">
                                    <Input {...init('qrCode')} placeholder="--请输入--" style={{ width: 170 }} />
                                </FormItem>
                            </div>
                            <div style={{ display: 'flex', flexDirection: 'column' }}>
                                <FormItem label="水表类型：">
                                    <Select
                                        placeholder="请选择"
                                        style={{ width: '170px' }}
                                        {...init('watermeterTypes')}
                                        // dataSource={waterMeterType}
                                        dataSource={[
                                            { label: '请选择', value: '' },
                                            { label: '机械表', value: '机械表' },
                                            { label: 'IC卡表', value: 'IC卡表' },
                                            { label: '远传表', value: '远传表' },
                                        ]}
                                        multiple
                                    />
                                </FormItem>
                                <FormItem label="&emsp;操作员：">
                                    <Combobox
                                        {...init('createId')}
                                        placeholder="--请选择--"
                                        fillProps="label"
                                        hasClear
                                        style={{ width: '170px' }}
                                        dataSource={roleNameList}
                                    />
                                </FormItem>
                                <FormItem label="操作员分组：">
                                    <Combobox
                                        {...init('groupId')}
                                        placeholder="--请选择--"
                                        fillProps="label"
                                        hasClear
                                        style={{ width: '170px' }}
                                        dataSource={groupNameList}
                                    />
                                </FormItem>
                                <FormItem label="水表种类：">
                                    <Select
                                        placeholder="请选择"
                                        style={{ width: '170px' }}
                                        {...init('watermeterKind')}
                                        dataSource={waterMeterKind}
                                    />
                                </FormItem>
                                <FormItem label="用户地址：">
                                    <Input {...init('address')} style={{ width: 170 }} placeholder="--请输入--" />
                                </FormItem>
                                <FormItem label="开户状态：">
                                    <Select
                                        placeholder="请选择"
                                        style={{ width: '170px' }}
                                        {...init('isOpening')}
                                        dataSource={[
                                            { label: '请选择', value: '' },
                                            { label: '未开户', value: '0' },
                                            { label: '已开户', value: '1' },
                                        ]}
                                    />
                                </FormItem>
                                <FormItem label="身份证号：">
                                    <Input {...init('identityCard')} placeholder="仅限华旭用户搜索" />
                                </FormItem>
                                <FormItem label="&emsp;&emsp;区号：">
                                    <Combobox
                                        {...init('zoneCode')}
                                        placeholder="--请选择区号--"
                                        fillProps="label"
                                        hasClear
                                        style={{ width: 170 }}
                                        dataSource={zoneCodeList}
                                    />
                                </FormItem>
                            </div>
                        </div>
                        <div style={{ textAlign: 'center' }}>
                            <Button
                                type="primary"
                                className="button"
                                onClick={() => this.queryUser(1, 10)}
                                style={{ marginRight: 10 }}
                            >
                                <Icon type="search" />
                                查询
                            </Button>
                            <Button
                                type="secondary"
                                className="button"
                                onClick={() => this.reset()}
                                style={{ marginRight: 10 }}
                            >
                                <Icon type="refresh" />
                                重置
                            </Button>
                            <Button type="primary" className="button" onClick={() => this.doRead()}>
                                <Icon type="text" />
                                读卡
                            </Button>
                        </div>
                    </Form>
                </IceContainer>
                <IceContainer title="用户列表">
                    <div style={{ marginBottom: 10 }}>
                        <Button type="primary" className="button" onClick={() => this.downloadBase()}>
                            <Icon type="download" />
                            导出用户信息(不含末次水量)
                        </Button>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <Button type="primary" className="button" onClick={() => this.downloadFile()}>
                            <Icon type="download" />
                            导出用户数据
                        </Button>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <a href={`${url}/revenue/user/downTemp`} target="view_window">
                            <Button type="primary" className="button" style={{backgroundColor: 'orange'}}>
                                <Icon type="download"/>
                                下载序号模板
                            </Button>
                        </a>
                        <Upload
                            action={`${url}revenue/user/importCustomerNO`}
                            onSuccess={this.uploadSuccess}
                            onError={this.uploadError}
                            showUploadList={false}
                            withCredentials={true}
                        >
                            <Button type="primary" className="button"
                                    style={{marginLeft: 20, backgroundColor: 'orange'}}>
                                <Icon type="share"/>
                                导入小区序号
                            </Button>
                        </Upload>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <a
                            href={`${url}revenue/user/downloadBatchUserInfoTemplate`}
                            className="button"
                            type="primary"
                        >

                            <Button type="primary" className="button" style={{backgroundColor: 'green'}}>
                                <Icon type="download"/>
                                下载用户信息批量修改模板
                            </Button>
                        </a>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <Upload
                            action={`${url}revenue/user/importAndBatchUpdateUserInfo?updateId=${createId}&updateName=${encodeURIComponent(
                                createName)}`}
                            onSuccess={this.batchUpdateSuccess}
                            onError={this.uploadError}
                            showUploadList={false}
                            accept=".xls,.xlsx"
                            withCredentials={true}
                        >
                            <Button
                                type="primary"
                                className="button"
                                style={{marginRight: 10, backgroundColor: 'green'}}
                            >
                                <Icon type="share"/>
                                导入用户信息批量修改结果
                            </Button>
                        </Upload>
                    </div>
                    <div style={{display: 'flex', justifyContent: 'flex-end', marginBottom: 10}}>
                        <div>总余额：{totalAccount}元</div>
                        <div style={{marginLeft: 10}}>总户数：{countHno ? countHno : 0}户</div>
                    </div>
                    <Table dataSource={formSource} isLoading={dataLoading}>
                        <Table.Column title="用户编号" dataIndex="cno" align="center" />
                        <Table.Column title="用户户号" dataIndex="hno" align="center" />
                        <Table.Column title="二维码编号" dataIndex="qrCode" align="center" />
                        <Table.Column title="水表编号" dataIndex="watermeterId" align="center" />
                        <Table.Column title="水表种类" dataIndex="watermeterKind" align="center" />
                        <Table.Column title="水表厂家" dataIndex="watermeterCompany" align="center" />
                        <Table.Column title="用户名称" dataIndex="cname" align="center" />
                        <Table.Column title="片区" dataIndex="areaName" align="center" />
                        <Table.Column title="小区" dataIndex="regionName" align="center" />
                        <Table.Column title="用水性质" dataIndex="feeName" align="center" />
                        <Table.Column title="用户地址" dataIndex="address" align="center" width={200} />
                        <Table.Column title="抄表序号" dataIndex="copyNo" align="center" />
                        <Table.Column
                            title="账户余额(元)"
                            dataIndex="account"
                            align="center"
                            cell={(value) => (value ? value : '0')}
                        />
                        <Table.Column
                            title="结余水量(吨)"
                            dataIndex="remainTunnage"
                            align="center"
                            cell={(value) => (value ? value : '0')}
                        />
                        <Table.Column
                            title="用户状态"
                            dataIndex="status"
                            align="center"
                            cell={(value) => this.renderStatus(value)}
                        />
                        <Table.Column title="操作员" dataIndex="createName" align="center" />
                        <Table.Column title="操作" cell={this.operation} align="center" width={150} />
                    </Table>
                    <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
                        <Pagination
                            pageSizeSelector="dropdown"
                            onPageSizeChange={(value) => this.changePageSize(value)}
                            pageSizeList={[10, 30, 50, 100]}
                            style={{ marginTop: 15 }}
                            current={page}
                            pageSize={pageSize}
                            total={totalSize}
                            size="small"
                            onChange={(value) => this.changePage(value)}
                        />
                        <div style={{ lineHeight: '53px', marginLeft: 10 }}>共{totalSize}条记录</div>
                    </div>
                </IceContainer>
                <Dialog
                    style={{ width: 500 }}
                    visible={disbursementsDialog}
                    onClose={() => this.handleCancel()}
                    onCancel={() => this.handleCancel()}
                    footer={footer}
                    title="销户"
                >
                    <div style={{ fontWeight: 600, marginTop: 5, marginBottom: 20, textAlign: 'center' }}>
                        是否上传水表的字轮读数，结算未结水量？
                    </div>
                </Dialog>
                <Dialog
                    visible={visible2}
                    onClose={() => this.handleCancel()}
                    onCancel={() => this.handleCancel()}
                    title="销户"
                    footer={footer2}
                    footerAlign="center"
                    style={{ width: 500 }}
                >
                    <div style={{ fontWeight: 600, marginTop: 5, marginBottom: 20 }}>
                        您有未结清的账单，请结清账单后再销户
                    </div>
                </Dialog>
                <Dialog
                    visible={visible3}
                    onClose={() => this.handleCancel()}
                    onCancel={() => this.handleCancel()}
                    title="销户"
                    footer={footer1}
                    footerAlign="center"
                    style={{ width: 450 }}
                >
                    <div style={{ fontWeight: 600, marginTop: 5, marginBottom: 20 }}>
                        请填写水表字轮读数，结账后再继续销户：
                    </div>
                    <Form direction="hoz">
                        <Input {...init('watermeterType')} htmlType="hidden" />
                        <FormItem label="上期示数：" {...formItemLayout}>
                            <Input {...init('lastNum')} readOnly />
                        </FormItem>
                        <FormItem label="字轮读数：" {...formItemLayout}>
                            <Input {...init('thisNum')} />
                        </FormItem>
                    </Form>
                </Dialog>
            </>
        );
    }
}
