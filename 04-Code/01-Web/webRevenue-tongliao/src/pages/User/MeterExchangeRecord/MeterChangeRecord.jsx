import React, {Component, Fragment} from 'react';
import {
  <PERSON><PERSON>,
  DatePicker,
  <PERSON>edback,
  Field,
  Form,
  Grid,
  Icon,
  Input,
  Select
} from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import qs from 'qs';
import {url} from '../../../components/URL';
import ViewChangeInfo from './components/ViewChangeInfo';
import ViewOrdinaryChangeInfo from './components/ViewOrdinaryChangeInfo';
import UpdateChangeTime from './components/UpdateChangeTime'
import {formItemLayout, span} from "../../../common/FormCollocation";
import {
  waterMeterCompany,
  waterMeterKind
} from "../../../common/WaterMeterCollocation";
import styles from "./index.module.scss";
import BasicsTable from "../../../common/BasicsTable";

const {Row, Col} = Grid;
const FormItem = Form.Item;
const {RangePicker} = DatePicker;
const {Combobox} = Select

export default class MeterChangeRecord extends Component {

  constructor(props) {
    super(props);
    this.state = {
      page: 1,
      pageSize: 10,
      total: 0,
      dataSource: [],
      areaList: [],  //片区
      propertiesList: [], //用水性质
      roleNameList: [],   //操作员
      groupNameList: [], // 查询操作员分组
      dataLoading: false,
    }
    this.field = new Field(this, { autoUnmount: true });
  }

  componentDidMount() {
    this.refreshTable(1, 10);
    this.queryRoleName();
    this.queryArea()
    this.queryWaterMeterName()
    this.queryGroupNames()
  }

  //查询
  refreshTable(page, pageSize) {
    this.setState({ dataLoading: true });
    let values = this.field.getValues();
    values.page = page
    values.pageSize = pageSize
    delete values.queryWatchTime
    axios.post(`${url}revenue/watermeterChange/query`, qs.stringify(values))
      .then((response) => {
        this.setState({
          dataSource: response.data.datas,
          page: page,
          pageSize: pageSize,
          total: response.data.totalSize,
          dataLoading: false
        })
      }).catch((error) => {
        Feedback.toast.error("网络错误请刷新后重试");
        this.setState({ dataLoading: false })
      })
  }

  //翻页
  changePage = (pageIndex) => {
    const { pageSize } = this.state;
    this.refreshTable(pageIndex, pageSize);
  }

  //改变记录大小
  changePageSize = (pageSize) => {
    this.refreshTable(1, pageSize);
  };

  //查询操作员
  queryRoleName = () => {
    axios.get(`${url}revenue/watermeterChange/createName` + '?n=' + Math.random())
      .then((response) => {
        if (response.data.code == 0) {
          this.setState({
            roleNameList: response.data.datas
          })
        }
      })
  }

  //查询片区
  queryArea() {
    axios.get(`${url}revenue/area/getAll`)
      .then((response) => {
        let areaList = []
        response.data.datas.map((item) => {
          areaList.push({ label: item.name, value: item.id })
        })
        this.setState({ areaList: areaList })
      })
  }

  //查询用水性质
  queryWaterMeterName() {
    axios.post(`${url}revenue/fee/queryList`)
      .then((response) => {
        let propertiesList = []
        response.data.datas.map((item) => {
          propertiesList.push({ label: item.name, value: item.id })
        })
        this.setState({ propertiesList: propertiesList })
      })
  }

  //搜搜控件日期选择
  changeTimeFm = (value, str) => {
    if (value) {
      this.field.setValue('csTime', str[0]);
      this.field.setValue('ceTime', str[1]);
    }
  }

  //重置
  reset() {
    this.field.reset();
    this.field.setValue('csTime', undefined);
    this.field.setValue('ceTime', undefined);
  }

  //导出
  downloadFile() {
    let values = this.field.getValues();
    let url1 = `${url}revenue/excel/exportWatermeterChangeDetail?n=1`;
    if (values.cno) {
      url1 += '&cno=' + values.cno;
    }
    if (values.cname) {
      url1 += '&cname=' + values.cname;
    }
    if (values.oldWid) {
      url1 += '&oldWid=' + values.oldWid;
    }
    if (values.newWid) {
      url1 += '&newWid=' + values.newWid;
    }
    if (values.oldWatermeterCompany) {
      url1 += '&oldWatermeterCompany=' + values.oldWatermeterCompany;
    }
    if (values.newWatermeterCompany) {
      url1 += '&newWatermeterCompany=' + values.newWatermeterCompany;
    }
    if (values.oldWatermeterKind) {
      url1 += '&oldWatermeterKind=' + values.oldWatermeterKind;
    }
    if (values.newWatermeterKind) {
      url1 += '&newWatermeterKind=' + values.newWatermeterKind;
    }
    if (values.createId) {
      url1 += '&createId=' + values.createId;
    }
    if (values.areaId) {
      url1 += '&areaId=' + values.areaId;
    }
    if (values.oldFeeId) {
      url1 += '&oldFeeId=' + values.oldFeeId;
    }
    if (values.newFeeId) {
      url1 += '&newFeeId=' + values.newFeeId;
    }
    if (values.oldWatermeterStatus) {
      url1 += '&oldWatermeterStatus=' + values.oldWatermeterStatus;
    }
    if (values.csTime) {
      url1 += '&csTime=' + values.csTime;
    }
    if (values.ceTime) {
      url1 += '&ceTime=' + values.ceTime;
    }
    if (values.isDiff) {
      url1 += '&isDiff=' + values.isDiff;
    }
    if (values.groupId) {
      url1 += '&groupId=' + values.groupId;
    }

    window.open(encodeURI(url1), 'about:blank');
  }

  // 查询操作员分组
  queryGroupNames() {
    axios({
      method: 'get',
      url: `${url}revenue/operatorGroup/getGroupListForSelection`,
    })
        .then((response) => {
          if (response.data.code == 0) {
            this.setState({
              groupNameList: response.data.datas,
            });
          }
        })
        .catch((error) => {
          Feedback.toast.error('请求错误：' + error);
        });
  }

  render() {
    const {
      page,
      pageSize,
      total,
      dataSource,
      roleNameList,
      dataLoading,
      areaList,
      propertiesList,
      groupNameList
    } = this.state
    const {init} = this.field;
    const columns = [
      {
        title: '用户编号',
        dataIndex: 'cno',
        key: 'cno'
      },
      {
        title: '用户户号',
        dataIndex: 'hno',
        key: 'hno'
      },
      {
        title: '用户名称',
        dataIndex: 'cname',
        key: 'cname'
      },
      {
        title: '用户地址',
        dataIndex: 'address',
        key: 'address'
      },
      {
        title: '旧表编号',
        dataIndex: 'oldWid',
        key: 'oldWid'
      },
      {
        title: '旧表种类',
        dataIndex: 'oldWatermeterKind',
        key: 'oldWatermeterKind'
      },
      {
        title: '用水性质(旧)',
        dataIndex: 'oldFeeName',
        key: 'oldFeeName'
      },
      {
        title: '新表编号',
        dataIndex: 'newWid',
        key: 'newWid'
      },
      {
        title: '新表种类',
        dataIndex: 'newWatermeterKind',
        key: 'newWatermeterKind'
      },
      {
        title: '用水性质(新)',
        dataIndex: 'newFeeName',
        key: 'newFeeName'
      },
      {
        title: '异表更换',
        dataIndex: 'isDiff',
        key: 'isDiff',
        cell: (value) => {
          if (value === '0') {
            return "否"
          } else if (value === '1') {
            return "是"
          }
        },
      },
      {
        title: '换表时间',
        dataIndex: 'createTime',
        key: 'createTime'
      },
      {
        title: '机表读数',
        dataIndex: 'meterReader',
        key: 'meterReader'
      },
      {
        title: '操作员',
        dataIndex: 'createName',
        key: 'createName'
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        cell: (value) => {
          return value === 1 ?
            <span style={{ color: '#1DC11D' }}>正常</span> : <span style={{ color: '#0000FF' }}>撤销换表</span>
        }
      },
      {
        title: '操作',
        width: 80,
        cell: (value, index, record) => {
          return (
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              {
                record.oldWatermeterType === 'IC卡表' ?
                  <ViewChangeInfo record={record} refreshTable={() => this.refreshTable(page, pageSize)} />
                  :
                  <ViewOrdinaryChangeInfo record={record} refreshTable={() => this.refreshTable(page, pageSize)} />
              }

              <UpdateChangeTime record={record} refreshTable={() => this.refreshTable(page, pageSize)} />
            </div>
          )
        },
        key: 9
      },
    ];
    return (
      <Fragment>
        <IceContainer title="水表更换记录">
          <Form field={this.field}>
            <Row wrap>
              <Col {...span}>
                <FormItem label="用户编号：" {...formItemLayout}>
                  <Input {...init('cno')} placeholder="请输入用户编号" style={{ width: 220 }} />
                </FormItem>
              </Col>
              <Col {...span}>
                <FormItem label="用户名称：" {...formItemLayout}>
                  <Input {...init('cname')} placeholder="请输入用户名称" style={{ width: 220 }} />
                </FormItem>
              </Col>
              <Col {...span}>
                <FormItem label="用水性质：" {...formItemLayout}>
                  <Select {...init('newFeeId')} dataSource={propertiesList}
                          placeholder="请选择用水性质" className={styles.selectWidth}/>
                </FormItem>
              </Col>
              <Col {...span}>
                <FormItem label="旧表编号：" {...formItemLayout}>
                  <Input {...init('oldWid')} placeholder="请输入旧表编号" style={{ width: 220 }} />
                </FormItem>
              </Col>
              <Col {...span}>
                <FormItem label="新表编号：" {...formItemLayout}>
                  <Input {...init('newWid')} placeholder="请输入新表编号" style={{ width: 220 }} />
                </FormItem>
              </Col>
              <Col {...span}>
                <FormItem label="旧表厂家：" {...formItemLayout}>
                  <Select {...init('oldWatermeterCompany')} dataSource={waterMeterCompany}
                    placeholder="请选择旧表厂家" className={styles.selectWidth} />
                </FormItem>
              </Col>
              <Col {...span}>
                <FormItem label="新表厂家：" {...formItemLayout}>
                  <Select {...init('newWatermeterCompany')} dataSource={waterMeterCompany}
                    placeholder="请选择新表厂家" className={styles.selectWidth} />
                </FormItem>
              </Col>
              <Col {...span}>
                <FormItem label="旧表种类：" {...formItemLayout}>
                  <Select  {...init('oldWatermeterKind')} dataSource={waterMeterKind}
                    placeholder="请选择旧表种类" className={styles.selectWidth} />
                </FormItem>
              </Col>
              <Col {...span}>
                <FormItem label="新表种类：" {...formItemLayout}>
                  <Select  {...init('newWatermeterKind')} dataSource={waterMeterKind}
                    placeholder="请选择水表种类" className={styles.selectWidth} />
                </FormItem>
              </Col>
              <Col {...span}>
                <FormItem label="操作员：" {...formItemLayout}>
                  <Combobox {...init('createId')} dataSource={roleNameList}
                    fillProps="label" hasClear
                    placeholder="请选择操作员" className={styles.selectWidth} />
                </FormItem>
              </Col>

              <Col {...span}>
                <FormItem label="片区：" {...formItemLayout}>
                  <Select {...init('areaId')} dataSource={areaList}
                    placeholder="请选择片区" className={styles.selectWidth} />
                </FormItem>
              </Col>

              <Col {...span}>
                <FormItem label="换表时间：" {...formItemLayout}>
                  <RangePicker
                      {...init('queryWatchTime', {
                        props: {onChange: (value, str) => this.changeTimeFm(value, str)}
                      })}
                      className={styles.selectWidth}/>
                </FormItem>
              </Col>
              <Col {...span}>
                <FormItem label="操作员分组：" {...formItemLayout}>
                  <Combobox {...init('groupId')} dataSource={groupNameList}
                            fillProps="label" hasClear
                            placeholder="请选择操作员" className={styles.selectWidth}/>
                </FormItem>
              </Col>

              <Col {...span}>
                <FormItem label="是否异表更换：" {...formItemLayout}>
                  <Select  {...init('isDiff')} dataSource={[
                    {label: '是', value: '1'},
                    {label: '否', value: '0'}
                  ]} placeholder="请选是否异表更换" className={styles.selectWidth}/>
                </FormItem>
              </Col>

              <Col {...span}>
                <FormItem label="旧表处理状态：" {...formItemLayout}>
                  <Select  {...init('oldWatermeterStatus')} dataSource={[
                    { label: '返修', value: '2' },
                    { label: '废弃', value: '3' },
                  ]} placeholder="请选旧表处理状态" className={styles.selectWidth} />
                </FormItem>
              </Col>

            </Row>
          </Form>

          <div align="center">
            <Button type="primary" className="button" onClick={() => this.refreshTable(1, 10)}
              style={{ marginRight: 30 }}>
              <Icon type="search" />查询
            </Button>
            <Button type="secondary" className="button" onClick={() => this.reset()}>
              <Icon type="refresh" />重置
            </Button>
          </div>
        </IceContainer>

        <IceContainer title="水表更换记录列表">
          <div style={{ marginBottom: 10 }}>
            <Button type="primary" className="button" onClick={() => this.downloadFile()}>
              导出换表记录
            </Button>
          </div>
          <BasicsTable columns={columns} dataSource={dataSource} total={total} pageSize={pageSize} page={page}
            dataLoading={dataLoading}
            changePage={(value) => this.changePage(value)}
            onPageSizeChange={(value) => this.onPageSizeChange(value)} pageSizeList={[10, 50, 100]} />
        </IceContainer>

      </Fragment>
    )
  }
}
