import React, {Component, Fragment} from 'react';
import {
  <PERSON>ton,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Field,
  Form,
  Grid,
  Icon,
  Input
} from '@icedesign/base';
import {url} from "../../../../components/URL";
import axios from "axios/index"
import qs from 'qs';

const {Row} = Grid;

export default class ViewAddWorkInfo extends Component {
  static displayName = 'ViewAddWorkInfo';

  static defaultProps = {};

  constructor(props) {
    super(props);
    this.state = {
      visible: false,
    };
    this.field = new Field(this);
  }

  onClose = () => {
    this.setState({
      visible: false,
    });
  };

  onOpen = (record) => {
    this.field.setValues({...record});
    this.setState({
      visible: true,
    });
  }

  //修改换表原因
  updateReason(record) {
    let reason = this.field.getValue('reason')
    axios({
      method: 'post',
      url: `${url}revenue/watermeterChange/changeReason`,
      data: qs.stringify({id: record.id, reason: reason}),
    })
      .then((response) => {
        if (response.data.code == "0") {
          Feedback.toast.success("修改成功");
          this.props.refreshTable()
          this.setState({visible: false,});
        } else {
          Feedback.toast.error(response.data.msg);
        }
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
      })
  }

  render() {
    const init = this.field.init;
    const {record} = this.props;
    const formItemLayout = {
      labelCol: {
        fixedSpan: 7,
      }
    };
    const footer = (
      <div>
        <Button type='primary' onClick={() => this.updateReason(record)}>修改</Button>
        <Button type='primary' onClick={this.onClose}>关闭</Button>
      </div>
    )
    return (
      <Fragment>
        <a onClick={() => this.onOpen(record)} title="查看/修改">
          <Icon type="browse" style={{color: "#3399ff", cursor: "pointer"}} size="small"/>
        </a>
        <Dialog
          minMargin={10}
          style={{width: 700}}
          visible={this.state.visible}
          onClose={this.onClose}
          footer={footer}
          title="查看换表详情"
          footerAlign="center"
        >
          <Form direction="ver" field={this.field}>
            <Row>
              <Form.Item label="剩余水量：" {...formItemLayout}>
                <Input {...init("leaveTunage")} readOnly="true"/>
              </Form.Item>
              <Form.Item label="剩余金额：" {...formItemLayout}>
                <Input {...init("leaveMoney")} readOnly="true"/>
              </Form.Item>
            </Row>
            <Row>
              <Form.Item label="旧表字轮读数：" {...formItemLayout}>
                <Input {...init("meterReader")} readOnly="true"/>
              </Form.Item>
              <Form.Item label="旧表累计购水量：" {...formItemLayout}>
                <Input {...init("oldTotalTunnage")} readOnly="true"/>
              </Form.Item>
            </Row>
            <Row>
              <Form.Item label="剩余处理：" {...formItemLayout}>
                <Input value={this.field.getValue('newWatermeterType') == 'IC卡表' ? '剩余水量写入新卡' : '剩余金额转入余额'}/>
              </Form.Item>
            </Row>
            <Row>
              <Form.Item label="换表原因：" {...formItemLayout}>
                <Input {...init("reason")} style={{width: 460, height: 70}}/>
              </Form.Item>
            </Row>
          </Form>
        </Dialog>
      </Fragment>
    );
  }
}
