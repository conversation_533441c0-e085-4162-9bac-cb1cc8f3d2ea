import React, {Component, Fragment} from 'react'
import {
  Balloon,
  Button,
  DatePicker,
  Dialog,
  Feedback,
  Field,
  Form,
  Icon
} from '@icedesign/base';
import axios from "axios/index";
import qs from 'qs';
import {url} from "../../../../components/URL";
import {moment} from "@icedesign/base/index";

const FormItem = Form.Item
const Tooltip = Balloon.Tooltip
export default class UpdateChangeTime extends Component {

  constructor(props) {
    super(props);
    this.state = {
      dataLoading: false,
      visible: false,
      createId: sessionStorage.getItem("stuffId"),
      createName: sessionStorage.getItem("realName"),
    };
    this.field = new Field(this, {autoUnmount: true});
  }

  onOpen(record) {
    this.field.setValue('createTime',record.createTime)
    this.setState({visible: true})
  }

  onClose() {
    this.setState({visible: false})
  }

  handleOk() {
    const {record} = this.props
    const {createId,createName}=this.state
    this.field.validate((errors, values) => {
      if (errors) {
        return;
      } else {
        values.id = record.id
        values.createTime=moment(values.createTime).format('YYYY-MM-DD H:mm:ss')
        values.changeName=createName
        values.changeId=createId
        axios({
          method: 'post',
          url: `${url}revenue/watermeterChange/updatechangeTime`,
          data: qs.stringify(values),
        })
          .then((response) => {
            if (response.data.code == "0") {
              Feedback.toast.success("修改成功");
              this.props.refreshTable();
              this.setState({visible: false});
            } else {
              Feedback.toast.error("修改失败:" + response.data.msg);
            }
          })
          .catch((error) => {
            Feedback.toast.error("请求错误：" + error)
          })
        this.setState({dataLoading: false})
      }
    })
  }

  render() {
    const {init} = this.field
    const {visible, dataLoading} = this.state
    const {record}=this.props
    const formItemLayout = {
      labelCol: {fixedSpan: 7}
    }
    const icon =  <Icon type="form" style={{color: "#3399ff", cursor: "pointer"}} size="small" onClick={() => this.onOpen(record)}/>
    const footer = (
      <div>
        <Button type="primary" loading={dataLoading} onClick={() => this.handleOk()}>
          确定
        </Button>
        <Button onClick={() => this.onClose()} style={{marginLeft: 20}}>
          取消
        </Button>
      </div>
    );
    return (
      <Fragment>

        <Tooltip trigger={icon} align='t' text='修改换表时间'/>

        <Dialog
          style={{width: 450}}
          visible={visible}
          onClose={() => this.onClose()}
          footer={footer}
          title='修改换表时间'
          footerAlign="center"
        >
          <Form field={this.field}>
            <FormItem {...formItemLayout} label="换表时间：">
              <DatePicker {...init('createTime')}showTime/>
            </FormItem>

          </Form>
        </Dialog>
      </Fragment>
    )
  }

}
