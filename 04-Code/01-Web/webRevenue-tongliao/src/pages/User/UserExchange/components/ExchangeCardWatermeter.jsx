// IC卡表
import React, {Component} from 'react';
import {
    Button,
    Checkbox,
    DatePicker,
    Dialog,
    Feedback,
    Field,
    Form,
    Grid,
    Icon,
    Input,
    moment,
    Radio,
    Select,
    Switch,
} from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import qs from 'qs';
import {url} from '../../../../components/URL';
import {areaCode} from '../../../../components/areaCode/areaCode';
import watermeterKindNo from '../../../../common/watermeterKindNo';
import changeWatermeter from '../../../../common/changeWatermeter';
import {Link} from 'react-router-dom';
import FoundationSymbol from 'foundation-symbol';
import {location} from '../../../../common/WaterMeterCollocation';
import {getBeforehandChangeData} from '../api';

const { Row } = Grid;
const FormItem = Form.Item;
const { Group: CheckboxGroup } = Checkbox;
const { Group: RadioGroup } = Radio;
const styles = {
    rowButton: {
        display: 'flex',
        justifyContent: 'space-around',
    },
    inputStyle: {
        width: 160,
        border: 0,
    },
    buyWater: {
        width: 150,
    },
    buyRecord: {
        width: 100,
        border: 0,
    },
    readOnlyStyle: {
        width: 150,
        backgroundColor: '#E6E6E6',
    },
    confimInput: {
        width: 120,
        color: 'red',
    },
    confimSpecialInput: {
        width: 150,
        backgroundColor: 'white',
    },
};

export default class ExchangeCardWatermeter extends Component {
    constructor(props) {
        super(props);
        this.state = {
            ladderDataSource: [],
            tableDataSource: [],
            value: null,
            purchase: false, // 缴费按钮状态
            waterByPay: false, // 根据收款计算水量按钮状态
            payByWater: false, // 根据水量计算价格按钮状态
            stuffId: sessionStorage.getItem('stuffId'),
            stuffName: sessionStorage.getItem('realName'),
            visible: false, // 点击换表弹出确认Dialog
            visible1: false, // 点击查询
            watermeterList: [], // 所有未使用水表列表
            ruleList: [], // 用水性质列表
            tableValue: [],
            CheckboxList: [],
            changeButton: false,
            cardNo: '',
            oldWatermeterCompany: '',
            oldWatermeterRecord: {},
            hasBound: false,
            isSecondConfirmationVisible: false,
            settlementMethod: 0,
            blueCard:0,
        };
        this.field = new Field(this, { autoUnmount: true });
    }

    componentDidMount() {
        const param = this.props.location.state;
        if (param) {
            const record = param.record;
            // 将旧水表参数写入页面
            this.field.setValues(record);
            this.setState({
                oldWatermeterCompany: record?.watermeterCompany,
                oldWatermeterRecord: record,
            });
            // 查询所有未使用的水表
            axios({
                method: 'post',
                url: `${url}revenue/watermeterChange/meterList`,
                data: qs.stringify({ oldWid: record.watermeterId }),
            })
                .then((response) => {
                    this.setState({
                        watermeterList: response?.data?.datas,
                    });
                })
                .catch((error) => {
                    Feedback.toast.error('请求错误：' + error);
                });
            // 查询所有的用水性质
            axios({
                method: 'post',
                url: `${url}revenue/fee/queryList`,
            })
                .then((response) => {
                    this.setState({
                        ruleList: response.data.datas,
                    });
                })
                .catch((error) => {
                    Feedback.toast.error('请求错误：' + error);
                });
            getBeforehandChangeData(record.cno).then((response) => {
                const {
                    data: { code, datas },
                } = response;
                if (code === '0') {
                    datas.watermeterType === '远传表'
                        ? this.setState({ isNeedDisable: true })
                        : this.setState({ isNeedDisable: false });
                    this.field.setValues({
                        newWid: datas.newWatermeterId,
                        newWatermeterCaliber: datas.watermeterCaliber,
                        newWheelNumber: datas.wheelNumber,
                        newWatermeterType: datas.watermeterType,
                        newWatermeterKind: datas.watermeterKind,
                        newWatermeterCompany: datas.watermeterCompany,
                        newLocation: datas.location,
                        newLockNo: datas.newLockNo,
                        qrCode: datas.qrCode,
                    });
                } else {
                    Feedback.toast.error('获取预换表信息失败！');
                }
            });
        } else {
            this.props.history.push('/user/UserExchange');
        }
    }

    // 根据水量计算价格
    computePriceByWater = () => {
        let tunnage = this.field.getValue('tunnage');
        let cno = this.field.getValue('cno');
        let param = {};
        param.tunnage = tunnage;
        param.cno = cno;
        this.setState({ payByWater: true });
        axios({
            method: 'post',
            url: `${url}revenue/fee/clear`,
            data: qs.stringify(param),
        })
            .then((response) => {
                this.setState({ payByWater: false });
                if (response.data.code == 0) {
                    let jsondata = response.data.datas;
                    // 设置剩余金额
                    this.field.setValue('changeMoney', jsondata.amount);
                } else {
                    alert(response.data.msg);
                }
            })
            .catch((error) => {
                Feedback.toast.error('Axios请求失败' + error);
                this.setState({
                    payByWater: false,
                });
            });
    };

    // 字轮读数改变剩余水量与金额
    changeTunageByReader = () => {
        const value = this.field.getValue('meterReader');
        const totalTunnage = this.field.getValue('totalTunnage');
        if (value>totalTunnage){
            Feedback.toast.error('字轮读数不能大于累计购水量!');
            return;
        }
        const tunnage = Number(totalTunnage) - Number(value);
        if (this.state.settlementMethod) {
            /^\d+$/.test(value) && this.field.setValue('tunnage', tunnage >= 0 ? tunnage : 0);
            this.field.reset('descr');
            this.field.setValue('changeMoney', 0);
            return;
        }
        if (value || value === 0) {
            this.field.setValue('meterReader', value);
            if (/^\d+$/.test(value)) {
                if (tunnage > 0) {
                    this.field.setValue('tunnage', tunnage);
                    const params = {
                        cno: this.field.getValue('cno'),
                        tunnage,
                    };
                    axios({
                        method: 'post',
                        url: `${url}revenue/order/changeCalculate`,
                        data: qs.stringify(params),
                    }).then((response) => {
                        if (response?.data?.code === '0') {
                            Dialog.confirm({
                                content: (
                                    <div style={{ fontSize: 18 }}>
                                        <div>
                                            剩余金额：
                                            <span style={{ color: 'red', fontWeight: 'bold' }}>
                                                {response?.data?.datas?.amount}
                                            </span>
                                        </div>
                                        <br />
                                        <div>计算明细：{response?.data?.datas?.str}</div>
                                        <br />
                                        <div>
                                            是否应用计算结果？
                                            <span style={{ color: 'red', fontWeight: 'bold' }}>
                                                （请确认客户已知晓并同意）
                                            </span>
                                        </div>
                                    </div>
                                ),
                                title: '剩余金额计算提示',
                                onOk: () => {
                                    this.field.setValue('changeMoney', response?.data?.datas?.amount);
                                    this.field.setValue('descr', response?.data?.datas?.str);
                                },
                                onCancel: () => {
                                    this.field.reset(['tunnage', 'changeMoney']);
                                },
                                footerAlign: 'center',
                            });
                        }
                    });
                } else {
                    this.field.setValue('tunnage', 0);
                    this.field.setValue('changeMoney', 0);
                }
            } else {
                Feedback.toast.error('请输入非负整数');
            }
        } else {
            this.field.reset(['meterReader']);
        }
    };

    // 渲染水表
    renderwatermeter = () => {
        const { watermeterList } = this.state;
        return watermeterList && watermeterList.length > 0
            ? watermeterList.map((item) => {
                  return (
                      <Select.Option key={item.id} value={item.id}>
                          {item.id}
                      </Select.Option>
                  );
              })
            : null;
    };

    doSearch = () => {
        const { tableValue } = this.state;
        let watermeterIdArray = [];
        tableValue.forEach((item) => {
            watermeterIdArray.push(item.watermeterId);
        });
        let watermeterId = this.field.getValue('watermeterId1');
        if (watermeterIdArray.includes(watermeterId)) {
            Feedback.toast.error('该水表已被选中');
            return;
        } else {
            if (tableValue.length === 0) {
                this.getWatermeterMethod(watermeterId);
            } else {
                Feedback.toast.error('暂时只支持绑定一个水表');
            }
        }
    };

    // 根据水表ID获取水表详情
    getWatermeterMethod = (watermeterId) => {
        const { tableValue } = this.state;
        let checkbox = [];
        let values = {};
        values.oldWatermeterId = this.field.getValue('watermeterId');
        values.newWatermeterId = watermeterId;
        axios({
            method: 'post',
            url: `${url}revenue/watermeter/one`,
            data: qs.stringify(values),
        })
            .then((response) => {
                let jsondata = response.data;
                if (jsondata.code === '0') {
                    tableValue.push(jsondata.datas);
                    if (jsondata.datas.watermeterType === 'IC卡表') {
                        checkbox.push({ label: '剩余水量写入新卡', value: true });
                    } else {
                        checkbox.push({ label: '剩余金额转入余额', value: true });
                    }
                    this.setState({
                        tableValue,
                        CheckboxList: checkbox,
                    });
                } else {
                    Feedback.toast.error(response.data.msg);
                }
            })
            .catch((error) => {
                Feedback.toast.error('请求错误：' + error);
            });
    };
    changeWatermeterId = (value) => {
        this.field.setValue('watermeterId1', value);
    };
    rowButtonRender = (value, index, record) => {
        if (record.watermeterType === 'IC卡表') {
            return (
                <span style={styles.rowButton}>
                    <a onClick={() => this.delChoosedWatermeter(index)} title="删除">
                        <Icon type="ashbin" size="small" style={{ color: '#ff0000', cursor: 'pointer' }} />
                    </a>
                </span>
            );
        } else {
            return (
                <span style={styles.rowButton}>
                    <a onClick={() => this.delChoosedWatermeter(index)} title="删除">
                        <Icon type="ashbin" size="small" style={{ color: '#ff0000', cursor: 'pointer' }} />
                    </a>
                </span>
            );
        }
    };

    delChoosedWatermeter = (index) => {
        const { tableValue } = this.state;
        tableValue.splice(index, 1);
        this.setState({ tableValue });
    };

    confirmDialog = () => {
        this.handleOnBlur();
        if (!this.state.hasBound) {
            let tableValue = [];
            tableValue.push(this.field.getValues());
            this.setState({ tableValue: tableValue });
            this.field.validate((error, values) => {
                if (!error) {
                    if (tableValue[0].newWatermeterType !== 'IC卡表') {
                        if (this.state.settlementMethod || tableValue[0].changeMoney) {
                            this.setState({ visible: true });
                        } else {
                            Feedback.toast.error('非卡表换表剩余金额不能为空');
                        }
                    } else {
                        this.setState({ visible: true });
                    }
                }
            });
        }
    };

    onClose() {
        this.setState({ visible: false });
    }

    /**
     * 换表（卡表换其他表）
     * 1、如果是卡表，调用OCX换表接口，如果是普通表，则不用调用接口，
     * 2、生成一条换表记录
     * 3、解除原本绑定的用户水表
     * 4、绑定新的水表
     */
    changeWatermeter = () => {
        const { tableValue } = this.state;
        if (tableValue.length < 1) {
            alert('请选择要换的水表');
            return;
        } else {
            this.setState({ changeButton: true });
            if (tableValue[0].newWatermeterType === 'IC卡表') {
                this.DllChangeTable();
            } else {
                this.doAxiosChangeMeter();
            }
        }
    };

    // dll控件
    DllChangeTable = () => {
        const { tableValue } = this.state;
        // 水表类型
        let type = watermeterKindNo(tableValue[0].newWatermeterKind);
        // 本次购水量
        let water = this.field.getValue('tunnage');
        // 卡号
        let cardno = '';
        let i = 1;
        if (type == '3') {
            cardno = tableValue[0].newWid;
            this.setState({ cardNo: cardno });
            let i = hxdll.hbconfirm(areaCode, cardno, cardno, water, type, 0, 0, 0, 0, 0);
            if (i == 0) {
                this.doAxiosChangeMeter(cardno);
            } else {
                this.setState({ changeButton: false });
                alert('换表失败：' + changeWatermeter(i));
            }
        } else if (type == '1' || type == '2') {
            cardno = this.field.getValue('cno');
            let i = hxdll.hbconfirm(areaCode, cardno, '0000000000', water, type, 0, 0, 0, 0, 0);
            if (i == 0) {
                this.doAxiosChangeMeter(cardno);
            } else {
                this.setState({ changeButton: false });
                alert('换表失败：' + changeWatermeter(i));
            }
        }
    };

    /**
     * dll接口调用成功，则需要
     * 1、生成一条换表记录
     * 2、解除原本绑定的用户水表
     * 3、绑定新的水表
     */
    doAxiosChangeMeter = (cardNo) => {
        const { tableValue, stuffId, stuffName } = this.state;
        let cno = this.field.getValue('cno');
        let cname = this.field.getValue('cname');
        let oldWid = this.field.getValue('watermeterId');
        let oldWatermeterType = this.field.getValue('watermeterType');
        let oldWatermeterKind = this.field.getValue('watermeterKind');
        let oldWatermeterCompany = this.field.getValue('watermeterCompany');
        let oldtotalTunnage = this.field.getValue('totalTunnage');
        let meterReader = this.field.getValue('meterReader');
        let changeType = this.state.settlementMethod;
        let newWid = tableValue[0].newWid;
        let newWatermeterType = tableValue[0].newWatermeterType;
        let newWatermeterKind = tableValue[0].newWatermeterKind;
        let newWatermeterCompany = tableValue[0].newWatermeterCompany;
        let newWatermeterCaliber = tableValue[0].newWatermeterCaliber;
        let newLockNo = tableValue[0].newLockNo;
        let newWheelNumber = tableValue[0].newWheelNumber;
        let newLocation = tableValue[0].newLocation;
        let reason = this.field.getValue('reason');
        let leaveMoney = this.field.getValue('changeMoney');
        let leaveTunage = this.field.getValue('tunnage');
        let account = this.field.getValue('account');
        let createId = stuffId;
        let createName = stuffName;
        let descr = this.field.getValue('descr');
        let blueCardType = this.state.blueCard;
        // 新表的用水性质
        let newFeeId = this.field.getValue('newFeeId');
        // 户籍人数
        let domicileNum = this.field.getValue('domicileNum');

        let cardno = cardNo;

        // 异表更换
        let isDiff = this.field.getValue('isDiff');
        let oldYearTunnage = this.field.getValue('oldYearTunnage');
        let oldWatermeterStatus = this.field.getValue('oldWatermeterStatus');

        let values = {};
        values.cno = cno;
        values.cname = cname;
        values.oldWid = oldWid;
        values.oldWatermeterType = oldWatermeterType;
        values.oldWatermeterKind = oldWatermeterKind;
        values.oldWatermeterCompany = oldWatermeterCompany;
        values.oldTotalTunnage = oldtotalTunnage;
        values.meterReader = meterReader;
        values.changeType = changeType;
        values.newWid = newWid;
        values.newWatermeterType = newWatermeterType;
        values.newWatermeterKind = newWatermeterKind;
        values.newWatermeterCompany = newWatermeterCompany;
        values.newWatermeterCaliber = newWatermeterCaliber;
        values.newLockNo = newLockNo;
        values.newWheelNumber = newWheelNumber;
        values.newLocation = newLocation;
        values.reason = reason;
        values.leaveTunage = leaveTunage;
        values.leaveMoney = leaveMoney;
        values.account = account;
        values.createId = createId;
        values.createName = createName;
        // 为了保存用户水表的关系
        values.totalTunnage = leaveTunage;
        values.totalFee = leaveMoney ? leaveMoney : 0;
        values.totalTimes = 1;
        values.cardNo = cardno;
        values.newFeeId = newFeeId;
        values.domicileNum = domicileNum;
        values.isDiff = isDiff ? '1' : '0';
        values.oldYearTunnage = oldYearTunnage;
        values.oldWatermeterStatus = oldWatermeterStatus;
        values.createTime = moment(this.field.getValue('time')).format('YYYY-MM-DD HH:mm:ss');
        values.remainTunnage = leaveTunage;
        values.descr = descr;
        values.blueCardType=blueCardType;
        axios({
            method: 'post',
            url: `${url}revenue/watermeterChange/change`,
            data: qs.stringify(values),
        })
            .then((response) => {
                this.setState({ changeButton: false });
                let jsondata = response.data;
                if (jsondata.code == 0) {
                    alert('换表成功');
                    this.props.history.push('/user/UserExchange');
                } else {
                    alert(jsondata.msg);
                }
            })
            .catch((error) => {
                this.setState({ changeButton: false });
                Feedback.toast.error('axios请求失败' + error);
            });
    };

    renderRule = () => {
        const { ruleList } = this.state;
        return ruleList && ruleList.length > 0
            ? ruleList.map((item) => {
                  return (
                      <Select.Option key={item.id} value={item.id}>
                          {item.name}
                      </Select.Option>
                  );
              })
            : null;
    };

    renderUseWaterType = () => {
        const newFeeId = this.field.getValue('newFeeId');
        if (!newFeeId) return;
        const { ruleList } = this.state;
        return ruleList.filter((item) => item.id === newFeeId)[0]?.name;
    };

    /* 复选框选择 */
    switchOnChange(value) {
        if (value) {
            this.field.setValue('isDiff', value);
            this.field.setValue('watermeterCompany', '深圳华旭');
        } else {
            const { oldWatermeterCompany } = this.state;
            this.field.setValue('isDiff', value);
            this.field.setValue('watermeterCompany', oldWatermeterCompany);
        }
    }

    // 失去焦点查询水表
    handleOnBlur() {
        let checkbox = [];
        const values = {
            oldWatermeterId: this.state.oldWatermeterRecord.watermeterId,
            newWatermeterId: this.field.getValue('newWid'),
        };
        axios({
            method: 'post',
            url: `${url}revenue/watermeter/one`,
            data: qs.stringify(values),
        })
            .then((response) => {
                if (response?.data?.code === '0') {
                    const { datas } = response.data;
                    datas?.watermeterCompany && this.field.setValue('newWatermeterCompany', datas?.watermeterCompany);
                    datas?.watermeterType && this.field.setValue('newWatermeterType', datas?.watermeterType);
                    datas?.watermeterCaliber && this.field.setValue('newWatermeterCaliber', datas?.watermeterCaliber);
                    datas?.watermeterKind && this.field.setValue('newWatermeterKind', datas?.watermeterKind);
                    datas?.location && this.field.setValue('newLocation', datas?.location);
                    datas?.qrCode && this.field.setValue('qrCode', datas?.qrCode);
                    datas?.watermeterType === '远传表'
                        ? this.setState({ isNeedDisable: true })
                        : this.setState({ isNeedDisable: false });
                    if (datas.watermeterType === 'IC卡表') {
                        checkbox.push({ label: '剩余水量写入新卡', value: true });
                    } else {
                        checkbox.push({ label: '剩余金额转入余额', value: true });
                    }
                    this.setState({ value: datas, CheckboxList: checkbox });
                } else {
                    Feedback.toast.error(response.data.msg);
                    if (response.data.msg === '水表已被绑定') {
                        this.field.reset('newWid');
                        this.setState({ hasBound: true });
                    } else {
                        this.setState({ hasBound: false });
                    }
                    this.field.reset(['newWatermeterCaliber', 'newWatermeterKind']);
                }
            })
            .catch((error) => {
                Feedback.toast.error('请求错误：' + error);
            });
    }

    // 校验水表编号
    checkedTunnage = (rule, value, callback) => {
        if (value) {
            if (isNaN(value)) {
                callback('水表编号只能是数字');
            } else {
                callback();
            }
        } else {
            callback('必填');
        }
    };

    // 改变水表类型
    changeWaterType = (value, option) => {
        this.field.setValue('newWatermeterType', value);
        this.field.reset('newWatermeterKind');
        let temp = [];
        if (value === 'IC卡表') {
            temp = [
                { label: '预付费2', value: '预付费2' },
                { label: '预付费5', value: '预付费5' },
            ];
        } else if (value === '机械表') {
            temp = [{ label: '机械表', value: '机械表' }];
        } else if (value === '远传表') {
            temp = [
                { label: '无线远传', value: '无线远传' },
                { label: '有线远传', value: '有线远传' },
            ];
        }
        this.setState({ waterKind: temp });
    };

    // 失去焦点检查二维码
    checkQRCode() {
        let qrCode = this.field.getValue('qrCode');
        let watermeterId = this.field.getValue('newWid');
        axios({
            method: 'post',
            url: `${url}revenue/watermeter/findByQrCodeAndWatermeterId`,
            data: qs.stringify({ qrCode: qrCode, watermeterId: watermeterId }),
        })
            .then((response) => {
                if (response.data.code !== '0') {
                    this.field.reset('qrCode');
                    Feedback.toast.error('该二维码已绑定水表，请查正后重试！！');
                }
            })
            .catch((error) => {
                Feedback.toast.error('请求错误：' + error);
            });
    }

    secondConfirmation = () => {
        this.field.validate(['newFeeId', 'oldWatermeterStatus'], (errors, values) => {
            if (values.newFeeId && values.oldWatermeterStatus) {
                this.setState({ isSecondConfirmationVisible: true });
            } else {
                !values.newFeeId && Feedback.toast.error('请选择用水性质');
                !values.oldWatermeterStatus && Feedback.toast.error('请选择旧表状态');
            }
        });
    };

    onRadioGroupChange = (type) => {
        this.setState({ settlementMethod: type });
        this.field.reset(['changeMoney', 'descr']);
    };
    onRadioChange = (type) => {
        this.setState({ blueCard: type });
    };

    render() {
        const { init } = this.field;
        const { tableValue, CheckboxList, visible, changeButton, value, isSecondConfirmationVisible, isNeedDisable } =
            this.state;
        const watermeterCaliber = [
            { label: 15, value: '15' },
            { label: 20, value: '20' },
            { label: 25, value: '25' },
            { label: 40, value: '40' },
            { label: 50, value: '50' },
            { label: 80, value: '80' },
            { label: 100, value: '100' },
            { label: 150, value: '150' },
            { label: 200, value: '200' },
            { label: `100*32`, value: `100*32` },
        ];
        const buyItemLayout = {
            labelCol: { fixedSpan: 9 },
        };
        let list = [{ value: 1 }];
        return (
            <div className="payment">
                <div style={{ marginBottom: 5 }}>
                    <Link
                        to={{
                            pathname: `/user/UserExchange`,
                        }}
                    >
                        <Button className="button" type="primary">
                            <FoundationSymbol type="backward" style={{ marginRight: 5 }} size="small" />
                            返回
                        </Button>
                    </Link>
                </div>

                <IceContainer title="换表">
                    <Form field={this.field} direction="hoz">
                        <Input {...init('cno')} htmlType="hidden" />
                        <Input {...init('stepBalance')} htmlType="hidden" />
                        <Input {...init('domicileNum')} htmlType="hidden" />
                        <Input {...init('watermeterKind')} htmlType="hidden" />
                        <Row>
                            <FormItem label="旧水表编号：" {...buyItemLayout}>
                                <Input {...init('watermeterId')} style={styles.readOnlyStyle} readOnly />
                            </FormItem>

                            <FormItem label="旧水表厂家：" labelCol={{ fixedSpan: 8 }}>
                                <Select
                                    placeholder="请选择"
                                    style={{ width: '150px' }}
                                    {...init('watermeterCompany')}
                                    dataSource={[
                                        { label: '扬州恒信', value: '扬州恒信' },
                                        { label: '深圳华旭', value: '深圳华旭' },
                                        { label: '辽宁民生', value: '辽宁民生' },
                                        { label: '山科', value: '山科' },
                                        { label: '机械表厂家', value: '机械表厂家' },
                                        { label: '杭州竞达', value: '杭州竞达' },
                                        { label: '山东科德', value: '山东科德' },
                                        { label: '湖南威铭', value: '湖南威铭' },
                                        { label: '河南新天', value: '河南新天' },
                                      {label: '宁夏隆基', value: '宁夏隆基'},
                                    ]}
                                />
                            </FormItem>
                        </Row>

                        <Row>
                            <FormItem label="累计购水量：" {...buyItemLayout}>
                                <Input {...init('totalTunnage')} style={styles.readOnlyStyle} readOnly />
                            </FormItem>
                            <FormItem label="账户余额（元）：" labelCol={{ fixedSpan: 8 }}>
                                <Input {...init('account')} style={styles.readOnlyStyle} readOnly />
                            </FormItem>
                        </Row>

                        <Row>
                            <FormItem label="异表更换：" {...buyItemLayout}>
                                <Switch
                                    {...init('isDiff')}
                                    dataSource={list}
                                    onChange={(value) => this.switchOnChange(value)}
                                    defaultChecked={false}
                                />
                            </FormItem>
                        </Row>

                        {this.field.getValue('isDiff') ? (
                            <FormItem label="年度购水量（m³）：" {...buyItemLayout}>
                                <Input
                                    {...init('oldYearTunnage', { rules: [{ required: true, message: '必填' }] })}
                                    style={styles.buyWater}
                                />
                            </FormItem>
                        ) : null}
                        <Row>
                            <FormItem label="结算方式：" labelCol={{ fixedSpan: 8 }}>
                                <RadioGroup
                                    dataSource={[
                                        {
                                            value: 0,
                                            label: '结算金额',
                                        },
                                        {
                                            value: 1,
                                            label: '结算水量',
                                        },
                                    ]}
                                    value={this.state.settlementMethod}
                                    onChange={this.onRadioGroupChange}
                                    style={{ marginTop: 4 }}
                                />
                            </FormItem>
                        </Row>
                        {
                            this.field.getValue('newWatermeterType') === "远传表"
                            && this.field.getValue('newWatermeterCompany') === "扬州恒信" ?
                                <Row>
                                    <FormItem label="是否已发蓝扣："
                                              labelCol={{fixedSpan: 8}}>
                                        <RadioGroup
                                            dataSource={[
                                                {
                                                    value: 0,
                                                    label: '否',
                                                },
                                                {
                                                    value: 1,
                                                    label: '是',
                                                },
                                            ]}
                                            style={{marginTop: 4}}
                                            value={this.state.blueCard}
                                            onChange={this.onRadioChange}
                                        />
                                    </FormItem>
                                </Row> : void (0)
                        }
                        <Row>
                            <FormItem label="字轮读数（m³）：" {...buyItemLayout}>
                                <Input
                                    {...init('meterReader', { rules: [{ required: true, message: '必填' }] })}
                                    style={styles.buyWater}
                                />
                                <Button onClick={this.changeTunageByReader}>结算</Button>
                            </FormItem>
                        </Row>

                        <Row>
                            <FormItem label="剩余金额（元）：" {...buyItemLayout}>
                                <Input
                                    {...init('changeMoney', { rules: [{ required: true, message: '必填' }] })}
                                    disabled={
                                        !(
                                            this.state.stuffId === '829a59bde7eb4c1e84d4b2bcde6c6977' ||
                                            this.state.stuffId === 'a4dcaff9ff46443485e651a234eb8a37'
                                        )
                                    }
                                    className="buyWaterDisabled"
                                />
                            </FormItem>
                            <FormItem label="剩余水量（m³）：" labelCol={{ fixedSpan: 8 }}>
                                <Input
                                    {...init('tunnage', { rules: [{ required: true, message: '必填' }] })}
                                    disabled
                                    className="buyWaterDisabled"
                                />
                            </FormItem>
                        </Row>
                        <Row>
                            <FormItem label="明细：" {...buyItemLayout}>
                                <Input
                                    {...init('descr')}
                                    style={{ width: 500, height: 60 }}
                                    disabled
                                    className="descDisabled"
                                />
                            </FormItem>
                            <FormItem label="换表原因：" {...buyItemLayout}>
                                <Input {...init('reason')} style={{ width: 500, height: 60 }} />
                            </FormItem>
                        </Row>
                    </Form>
                </IceContainer>

                <IceContainer title="选择新表">
                    <Form field={this.field}>
                        <div style={{ display: 'flex', justifyContent: 'space-around' }}>
                            <div style={{ display: 'flex', flexDirection: 'column' }}>
                                <FormItem label="水表编号：">
                                    <Input
                                        {...init('newWid', {
                                            rules: [
                                                { required: true, message: '水表编号必填' },
                                                { validator: this.checkedTunnage },
                                                { pattern: /^\S*$/, message: '不能输入空格' },
                                            ],
                                        })}
                                        style={{ width: '160px' }}
                                        onBlur={() => this.handleOnBlur()}
                                    />
                                </FormItem>

                                <FormItem label="水表类型：">
                                    <Select
                                        placeholder="请选择"
                                        style={{ width: '160px', color: 'black' }}
                                        {...init('newWatermeterType', {
                                            rules: [{ required: true, message: '水表类型必填' }],
                                        })}
                                        onChange={this.changeWaterType}
                                        disabled={value || isNeedDisable}
                                    >
                                        <Select.Option value="机械表">机械表</Select.Option>
                                        <Select.Option value="IC卡表">IC卡表</Select.Option>
                                    </Select>
                                </FormItem>

                                <FormItem label="安装位置：">
                                    <Select
                                        placeholder="--请选择--"
                                        style={{ width: '160px' }}
                                        {...init('newLocation', {
                                            rules: [{ required: true, message: '安装位置必填' }],
                                        })}
                                        defaultValue="厨房"
                                        dataSource={location}
                                    />
                                </FormItem>

                                <FormItem label="换表时间：">
                                    <DatePicker
                                        showTime={{ defaultValue: new Date() }}
                                        format="YYYY-MM-DD HH:mm:ss"
                                        {...init('time', {
                                            rules: [{ required: true, message: '换表时间必填' }],
                                            initValue: new Date(),
                                        })}
                                    />
                                </FormItem>
                            </div>

                            <div style={{ display: 'flex', flexDirection: 'column' }}>
                                <FormItem label="水表口径：">
                                    <Select
                                        {...init('newWatermeterCaliber', {
                                            rules: [{ required: true, message: '水表口径必填' }],
                                        })}
                                        placeholder="--请选择--"
                                        style={{ width: 160, color: 'black' }}
                                        dataSource={watermeterCaliber}
                                        disabled={value || isNeedDisable}
                                        defaultValue={value ? value : '15'}
                                    />
                                </FormItem>

                                <FormItem label="水表种类：">
                                    <Select
                                        placeholder="请先选择水表类型"
                                        style={{ width: '160px', color: 'black' }}
                                        {...init('newWatermeterKind', {
                                            rules: [
                                                {
                                                    required: true,
                                                    message: '必填',
                                                },
                                            ],
                                        })}
                                        dataSource={this.state.waterKind}
                                        disabled={value || isNeedDisable}
                                    />
                                </FormItem>

                                <FormItem label="&emsp;&emsp;表锁号：">
                                    <Input {...init('newLockNo')} placeholder="--请输入--" style={{ width: 160 }} />
                                </FormItem>
                            </div>

                            <div style={{ display: 'flex', flexDirection: 'column' }}>
                                <FormItem label="字轮基数：">
                                    <Input
                                        {...init('newWheelNumber', {
                                            rules: [{ required: true, message: '字轮基数必填' }],
                                        })}
                                        style={{ width: '160px' }}
                                        defaultValue="0"
                                        maxLength={10}
                                        readOnly={value ? true : false}
                                    />
                                </FormItem>

                                <FormItem label="水表厂家：">
                                    <Select
                                        {...init('newWatermeterCompany', {
                                            rules: [{ required: true, message: '水表厂家必填' }],
                                            initValue: '扬州恒信',
                                        })}
                                        style={{ width: '160px', color: 'black' }}
                                        maxLength={30}
                                        dataSource={[
                                            { label: '请选择', value: '' },
                                            { label: '扬州恒信', value: '扬州恒信' },
                                            { label: '深圳华旭', value: '深圳华旭' },
                                            { label: '辽宁民生', value: '辽宁民生' },
                                            { label: '山科', value: '山科' },
                                            { label: '机械表厂家', value: '机械表厂家' },
                                            { label: '杭州竞达', value: '杭州竞达' },
                                            { label: '山东科德', value: '山东科德' },
                                            { label: '湖南威铭', value: '湖南威铭' },
                                            { label: '河南新天', value: '河南新天' },
                                          {
                                            label: '宁夏隆基',
                                            value: '宁夏隆基'
                                          },
                                        ]}
                                        disabled={(value && value.watermeterCompany) || isNeedDisable}
                                    />
                                </FormItem>

                                <FormItem label="二维码编号：">
                                    <Input
                                        {...init('qrCode', { rules: [{ pattern: /^\S*$/, message: '不能输入空格' }] })}
                                        style={{ width: 160 }}
                                        onBlur={() => this.checkQRCode()}
                                    />
                                </FormItem>
                            </div>
                        </div>
                    </Form>

                    <div align="center" style={{ marginTop: 10 }}>
                        <CheckboxGroup
                            {...init('isWrite')}
                            dataSource={CheckboxList}
                            className="next-form-text-align"
                            defaultValue={true}
                            disabled
                        />
                    </div>

                    <div align="center" style={{ marginTop: 10 }}>
                        <Button className="button" type="primary" onClick={this.confirmDialog}>
                            换表
                        </Button>
                    </div>
                </IceContainer>

                <Dialog
                    visible={visible}
                    onClose={() => this.onClose()}
                    title="请核对换表信息"
                    footer={
                        <span>
                            <Button
                                onClick={this.secondConfirmation}
                                type="primary"
                                className="button"
                                loading={changeButton}
                            >
                                提交
                            </Button>
                            <Button onClick={() => this.onClose()} className="button" style={{ marginLeft: 20 }}>
                                取消
                            </Button>
                        </span>
                    }
                    footerAlign="center"
                    style={{ width: 500 }}
                >
                    <Form direction="hoz">
                        {tableValue && tableValue.length && tableValue[0].newWatermeterType === 'IC卡表' ? (
                            <Row>
                                <FormItem label="剩余水量（m³）" {...buyItemLayout}>
                                    <Input
                                        value={this.field.getValue('tunnage')}
                                        readOnly
                                        style={styles.confimSpecialInput}
                                    />
                                </FormItem>
                            </Row>
                        ) : (
                            <Row>
                                <FormItem label="剩余金额（元）" {...buyItemLayout}>
                                    <Input
                                        value={this.field.getValue('changeMoney')}
                                        readOnly
                                        style={styles.confimSpecialInput}
                                    />
                                </FormItem>
                            </Row>
                        )}
                        <Row>
                            <FormItem label="剩余处理" {...buyItemLayout}>
                                <Input
                                    value={
                                        tableValue.length > 0
                                            ? tableValue[0].newWatermeterType === 'IC卡表'
                                                ? '剩余水量写入新卡'
                                                : '剩余金额转入余额'
                                            : ''
                                    }
                                    readOnly
                                    style={styles.confimSpecialInput}
                                />
                            </FormItem>
                        </Row>
                        <Row>
                            <FormItem label="用水性质" {...buyItemLayout}>
                                <Select
                                    {...init('newFeeId')}
                                    defaultValue={this.field.getValue('feeId')}
                                    style={styles.confimSpecialInput}
                                >
                                    {this.renderRule()}
                                </Select>
                            </FormItem>
                        </Row>
                        {this.field.getValue('watermeterId') === this.field.getValue('watermeterId1') ? null : (
                            <Row>
                                <FormItem label="旧表状态" {...buyItemLayout}>
                                    <Select
                                        {...init('oldWatermeterStatus')}
                                        defaultValue="3"
                                        style={styles.confimSpecialInput}
                                    >
                                        <Select.Option value="3">废弃</Select.Option>
                                        <Select.Option value="2">返修</Select.Option>
                                    </Select>
                                </FormItem>
                            </Row>
                        )}
                    </Form>
                </Dialog>

                <Dialog
                    visible={isSecondConfirmationVisible}
                    onClose={() => this.setState({ isSecondConfirmationVisible: false })}
                    title="请再次核对用水性质"
                    footer={
                        <span>
                            <Button
                                onClick={this.changeWatermeter}
                                type="primary"
                                className="button"
                                loading={changeButton}
                            >
                                确认提交
                            </Button>
                            <Button
                                className="button"
                                onClick={() => this.setState({ isSecondConfirmationVisible: false })}
                                style={{ marginLeft: 20 }}
                            >
                                返回修改
                            </Button>
                        </span>
                    }
                    footerAlign="center"
                    style={{ width: 600 }}
                >
                    <div
                        style={{
                            fontWeight: 'bold',
                            color: 'red',
                            lineHeight: 2,
                            padding: '12px 0',
                            textAlign: 'center',
                        }}
                    >
                        <div style={{ fontSize: 24 }}>请确认用水性质为</div>
                        <div style={{ fontSize: 36 }}> {this.renderUseWaterType()}</div>
                    </div>
                </Dialog>
            </div>
        );
    }
}
