import React, {Component} from 'react';
import {Balloon, Feedback, Icon, Pagination, Table} from '@icedesign/base';
import ModifyModal from './ModifyModal';
import DeleteModal from './DeleteModal';
import UserModal from './UserModal';
import {getUserList, getUserListInGroup} from '../api';

const Tooltip = Balloon.Tooltip;
export class PageList extends Component {
    state = {
        modifyModal: false,
        deleteModal: false,
        userModal: false,
        userList: [],
        userListInGroup: [],
        dataRecord: {},
    };
    componentDidMount() {
        this.props.getList(this.props.pagination);
    }
    setModifyModal = (boolean) => {
        this.setState({ modifyModal: boolean });
    };
    setDeleteModal = (boolean) => {
        this.setState({ deleteModal: boolean });
    };
    setUserModal = (boolean) => {
        this.setState({ userModal: boolean });
    };
    showModifyRecord = (record) => {
        this.setState({ dataRecord: record });
        this.setModifyModal(true);
    };
    showDeleteRecord = (record) => {
        this.setState({ dataRecord: record });
        this.setDeleteModal(true);
    };
    showUserRecord = (record) => {
        const { id } = record;
        const params = {
            groupId: id,
        };
        getUserList()
            .then((response) => {
                response?.data?.statusCode === 0 && this.setState({ userList: response?.data?.fortasklist });
                this.setState({ dataRecord: record });
            })
            .catch(() => {
                Feedback.toast.error('获取用户列表失败');
            });
        getUserListInGroup(params)
            .then((response) => {
                const {
                    data: { code, datas },
                } = response;
                code === '0' && this.setState({ userListInGroup: datas });
                this.setUserModal(true);
            })
            .catch(() => {
                Feedback.toast.error('获取组内用户列表失败');
            });
    };
    changePage = (current) => {
        const params = {
            ...this.props.pagination,
            page: current,
        };
        this.props.getList(params);
    };
    renderOperation = (action, index, record) => {
        const modifyGroup = (
            <Icon
                type="survey"
                size="small"
                style={{
                    color: '#FFA003',
                    cursor: 'pointer',
                    paddingLeft: '6px',
                    paddingRight: '6px',
                }}
                onClick={() => {
                    this.showModifyRecord(record);
                }}
            />
        );
        const userGroup = (
            <Icon
                type="set"
                size="small"
                style={{
                    color: '#FFA003',
                    cursor: 'pointer',
                    paddingLeft: '6px',
                    paddingRight: '6px',
                }}
                onClick={() => {
                    this.showUserRecord(record);
                }}
            />
        );
        const deleteGroup = (
            <Icon
                type="ashbin"
                size="small"
                style={{
                    color: '#FFA003',
                    cursor: 'pointer',
                    paddingLeft: '6px',
                    paddingRight: '6px',
                }}
                onClick={() => {
                    this.showDeleteRecord(record);
                }}
            />
        );
        return (
            <>
                <Tooltip trigger={modifyGroup} align="t" text="修改分组" />
                <Tooltip trigger={userGroup} align="t" text="设置用户分组" />
                <Tooltip trigger={deleteGroup} align="t" text="删除分组" />
            </>
        );
    };
    render() {
        return (
            <>
                <Table dataSource={this.props.dataSource}>
                    <Table.Column title="分组编号" dataIndex="id" align="center" />
                    <Table.Column title="分组名称" dataIndex="groupName" align="center" />
                    <Table.Column title="创建人名称" dataIndex="createName" align="center" />
                    <Table.Column
                        title="操作"
                        cell={(action, index, record) => this.renderOperation(action, index, record)}
                        align="center"
                    />
                </Table>
                <div className="notice-pagination">
                    <Pagination
                        current={this.props.pagination.page}
                        pageSize={this.props.pagination.pageSize}
                        total={this.props.pagination.totalSize}
                        onChange={(current) => this.changePage(current)}
                    />
                </div>
                <ModifyModal
                    modifyModal={this.state.modifyModal}
                    dataRecord={this.state.dataRecord}
                    getList={this.props.getList}
                    pagination={this.props.pagination}
                    setModifyModal={this.setModifyModal}
                />
                <DeleteModal
                    deleteModal={this.state.deleteModal}
                    dataRecord={this.state.dataRecord}
                    getList={this.props.getList}
                    pagination={this.props.pagination}
                    setDeleteModal={this.setDeleteModal}
                />
                <UserModal
                    userModal={this.state.userModal}
                    dataRecord={this.state.dataRecord}
                    pagination={this.props.pagination}
                    userList={this.state.userList}
                    userListInGroup={this.state.userListInGroup}
                    getList={this.props.getList}
                    setUserModal={this.setUserModal}
                />
            </>
        );
    }
}

export default PageList;
