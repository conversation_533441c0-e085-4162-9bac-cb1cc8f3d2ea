import React, {Component} from 'react';
import {Dialog, Feedback, Field, Transfer} from '@icedesign/base';
import {setUserGroup} from '../api';

export class UserModal extends Component {
    state = {
        chooseArray: null,
    };
    field = new Field(this);
    saveUserGroup = () => {
        const { chooseArray } = this.state;
        const createId = sessionStorage.getItem('stuffId');
        const groupId = this.props.dataRecord?.id;
        if (chooseArray !== null) {
            const array = chooseArray.length
                ? chooseArray.map((item) => {
                      return { userId: item.value, userName: item.label, createId, groupId };
                  })
                : [{ createId, groupId }];
            setUserGroup(array).then((response) => {
                response?.data?.code === '0' && Feedback.toast.success('设置分组成功！');
                response?.data?.code === '1' && Feedback.toast.error(response.data.msg);
            });
            this.props.getList(this.props.pagination);
        }
        this.setState({ chooseArray: null });
        this.props.setUserModal(false);
    };
    handleChange = (value, data, extra) => {
        this.setState({ chooseArray: data });
    };
    handleClose = () => {
        this.setState({ chooseArray: null });
        this.props.setUserModal(false);
    };
    render() {
        const { userModal, userList, userListInGroup } = this.props;
        const defaultValue = userListInGroup.map((item) => item.userId);
        const { init } = this.field;
        return (
            <Dialog
                style={{ width: '1000px' }}
                visible={userModal}
                title="设置用户分组"
                footerAlign="center"
                onOk={() => this.saveUserGroup()}
                onClose={() => this.handleClose()}
                onCancel={() => this.handleClose()}
            >
                <div className="user-group-form-item">
                    <Transfer
                        showSearch
                        {...init('group')}
                        dataSource={userList}
                        defaultValue={defaultValue}
                        onChange={this.handleChange}
                    />
                </div>
            </Dialog>
        );
    }
}

export default UserModal;
