import React, {Component} from 'react';
import {<PERSON>oon, Dialog, Form, Icon, Input, Select} from '@icedesign/base'
import {Feedback, Field} from "@icedesign/base/index"
import axios from "axios/index"
import qs from 'qs'
import {url} from "../../../../components/URL"

const FormItem = Form.Item
const {Option} = Select
const Tooltip = Balloon.Tooltip


export default class UpdateDialog extends Component {

  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      priceTunnages: [],
      formValue: [],
      createId: sessionStorage.getItem("stuffId"),
      createName: sessionStorage.getItem("realName")
    }
    this.field = new Field(this, {autoUnmount: true})
  }

  componentDidMount() {
    this.queryRule()
  }

  /*查询规则*/
  queryRule() {
    const {record} = this.props
    axios({
      method: 'post',
      url: url + 'revenue/fee/get',
      data: qs.stringify({id: record.id})
    }).then((response) => {
      this.setState({
        formValue: response.data.datas
      })
    })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
      })
  }

  /*关闭弹窗*/
  handleCancel() {
    this.setState({visible: false, priceTunnages: [{}]})
  }

  /*弹窗确定回掉函数*/
  handleOk(record) {
    const {createId, createName} = this.state
    this.field.validate((errors, values) => {
      if (errors) {
        return
      } else {
        values.createId = createId
        values.createName = createName
        values.id = record.id
        axios({
          method: 'post',
          url: `${url}revenue/fee/update`,
          data: qs.stringify(values),
        })
          .then((response) => {
            if (response.data.code == "0") {
              Feedback.toast.success("修改成功");
              this.props.queryInspect()
              this.queryRule()
              this.setState({visible: false});
            } else {
              Feedback.toast.error("修改失败:" + response.data.msg);
            }
          })
          .catch((error) => {
            Feedback.toast.error("请求错误：" + error);
          })
      }
    })
  }

  //打开修改弹窗
  onOpen(record) {
    const {formValue} = this.state
    this.field.setValue('name', record.name)
    this.field.setValue('sewageFee', record.sewageFee)
    this.field.setValue('waterSourceFee', record.waterSourceFee)
    this.field.setValue('lateFee', record.lateFee)
    this.field.setValue('derate', record.derate)
    this.field.setValue('critical', record.critical)
    if (formValue.priceTunnages.length > 1) {
      this.field.setValue('ladder', '0')
      this.setState({priceTunnages: formValue.priceTunnages,})
      this.field.setValue('yearStair', record.yearStair)
      this.field.setValue('baseNum', record.baseNum)
      this.field.setValue('delta', record.delta)
    } else {
      this.field.setValue('ladder', '1')
      this.field.setValue('priceTunnages[0].price', formValue.priceTunnages[0].price);
    }
    this.setState({visible: true})
  }

  /*清空阶梯*/
  clearLadder(value) {
    this.field.setValue('ladder', value)
    this.setState({priceTunnages: [{}]})
  }

  /*添加form*/
  addItem() {
    this.state.priceTunnages.push({});
    this.setState({
      priceTunnages: this.state.priceTunnages
    });
  }

  /*删除form*/
  removeItem(index) {
    this.state.priceTunnages.splice(index, 1);
    this.setState({
      priceTunnages: this.state.priceTunnages
    });
  }

  //校验输入框不能输入小数
  checkInput(rule, value, callback) {
    if (value) {
      if (value.indexOf('.') != -1) {
        callback("该项不能输入小数");
      } else {
        callback()
      }
    } else {
      callback()
    }

  }

  render() {
    const {visible, priceTunnages} = this.state
    const {record} = this.props
    const {init} = this.field
    const formItemLayout = {
      labelCol: {fixedSpan: 16},
      wrapperCol: {fixedSpan: 12}
    }
    const formItemLayout1 = {
      labelCol: {fixedSpan: 7},
      wrapperCol: {fixedSpan: 4}
    }
    const update = (<a onClick={() => this.onOpen(record)}>
      <Icon type="survey" size="small" style={{color: "#1DC11D", cursor: 'pointer'}}/>
    </a>)
    return (
      <section>
        <Tooltip trigger={update} align='t' text='修改规则配置'/>

        <Dialog style={{width: 800}} visible={visible} isFullScreen={true}
                onClose={() => this.handleCancel()}
                onCancel={() => this.handleCancel()}
                title='修改规则配置'
                onOk={() => this.handleOk(record)}
        >

          <Form field={this.field}>

            <FormItem {...formItemLayout} label="用水性质：">
              <Input {...init("name", {rules: [{required: true, message: "用水性质必填"}]})}/>
            </FormItem>

            <FormItem {...formItemLayout} label="污水费(元)：">
              <Input {...init("sewageFee", {rules: [{required: true, message: "污水费必填"},]})}
                     htmlType="number"/>
            </FormItem>

            <FormItem {...formItemLayout} label="水资源税(元)：">
              <Input {...init("waterSourceFee", {rules: [{required: true, message: "水资源税必填"}]})} htmlType="number"/>
            </FormItem>

            <FormItem {...formItemLayout} label="滞纳金比例(%)：">
              <Input  {...init("lateFee")} htmlType="number"/>
            </FormItem>

            <FormItem {...formItemLayout} label="减免吨数(m³)）：">
              <Input  {...init("derate",{initValue: record.tunnage},
                {
                  rules: [
                    {validator: (rule, value, callback) => this.checkInput(rule, value, callback)}
                  ]
                })} htmlType="number"/>
            </FormItem>

            <FormItem {...formItemLayout} label="临界吨数(m³)：">
              <Input  {...init("critical",{initValue: record.critical},
                {
                  rules: [
                    {validator: (rule, value, callback) => this.checkInput(rule, value, callback)}
                  ]
                }
              )} htmlType="number"/>
            </FormItem>


            {
              this.field.getValue("ladder") === '0' ?
                <section>
                  <FormItem {...formItemLayout} label="是否是年阶梯：">
                    <Select placeholder="请选择" {...init('yearStair', {rules: [{required: true, message: "扣费按年阶梯必填"}]})}>
                      <Option value="0">是</Option>
                      <Option value="1">否</Option>
                    </Select>
                  </FormItem>

                  <FormItem {...formItemLayout} label="基础人口：">
                    <Input  {...init('baseNum', {initValue: record.baseNum},{
                        rules: [
                          {required: true, message: "请填写基础人口"},
                          {validator: (rule, value, callback) => this.checkInput(rule, value, callback)}
                        ]
                      },
                    )}
                            htmlType="number"/>
                  </FormItem>

                  <FormItem {...formItemLayout} label="阶梯水增量(m³)：">
                    <Input {...init('delta', {initValue: record.delta}, {
                        rules: [{required: true, message: "请填阶梯水增量"},
                          {validator: (rule, value, callback) => this.checkInput(rule, value, callback)}
                        ]
                      },
                    )}
                           htmlType="number"/>
                  </FormItem>

                </section>

                : void (0)
            }

            {
              this.field.getValue("ladder") === "1" ?
                <FormItem {...formItemLayout} label="水价(元)：">
                  <Input  {...init('priceTunnages[0].price', {rules: [{required: true, message: "请填写水价"}]})}
                          htmlType="number"/>
                </FormItem>
                :
                this.field.getValue("ladder") === "0" ?
                  <div style={{marginTop: 5}}>
                    {
                      priceTunnages.map((item, index) => {
                        return (
                          <div key={index} style={{display: 'flex', justifyContent: 'space-evenly'}}>
                            <FormItem label="阶梯：" {...formItemLayout1}>
                              <Input {...init(`priceTunnages[${index}].stairName`, {initValue: index + 1},)}
                                     readOnly={true}
                                     style={{width:50}}
                              />
                            </FormItem>

                            <FormItem label="水价(元)：">
                              <Input {...init(`priceTunnages[${index}].price`, {initValue: item.price}, {
                                rules: [{
                                  required: true,
                                  message: "请填写水价"
                                }]
                              })}
                                     htmlType="number"/>
                            </FormItem>

                            {
                              index + 1 === this.state.priceTunnages.length ? void(0) :
                                <FormItem label="水量(m³)：" style={{marginLeft:20}}>
                                  <Input {...init(`priceTunnages[${index}].tunnage`, {initValue: item.tunnage}, {
                                    rules: [{
                                      required: true,
                                      message: "请填写水量"
                                    }]
                                  })}
                                         htmlType="number"/>
                                </FormItem>
                            }

                          </div>
                        )
                      })
                    }
                  </div>
                  : void(0)
            }

          </Form>

        </Dialog>

      </section>
    )
  }
}
