import React, {Component, Fragment} from 'react';
import IceContainer from '@icedesign/container'
import {
  Balloon,
  Button,
  DatePicker,
  Feedback,
  Field,
  Form,
  Grid,
  Icon,
  Input,
  Select
} from '@icedesign/base'
import axios from "axios/index"
import qs from 'qs'
import {url} from "../../../components/URL"
import UpdateDialog from './components/UpdateDialog'
import PreviewRule from './components/PreviewRule'
import AddDialog from './components/AddDialog'
import {LoginURL} from '../../../components/URL/LoginURL';
import BasicsTable from "../../../common/BasicsTable";
import {formItemLayout} from "../../../common/FormCollocation";

const Tooltip = Balloon.Tooltip;
const { Row, Col } = Grid;
const FormItem = Form.Item;
const { Combobox } = Select;
const { RangePicker } = DatePicker;

export default class Rule extends Component {
  constructor(props) {
    super(props);
    this.state = {
      page: 1,
      pageSize: 10,
      totalSize: 0,
      dataLoading: false,
      dataSource: [],
      title: 1,
      selectRecord: '',
      buttons: [],
      loginURL: LoginURL,
      feeNameList: [], // 用水性质
      createNameList: [], // 操作员
    }
    this.field = new Field(this, { autoUnmount: true })
  }

  componentWillMount() {
    this.getMenuByRole();
    this.queryInspect(1, 10);
    this.querCreateName();
  }


  // 操作员下拉框
  querCreateName() {
    axios({
      method: 'get',
      url: `${url}revenue/user/createName`,
    })
      .then((response) => {
        let createNameList = []
        response.data.datas.map((item) => {
          createNameList.push({ label: item.label, value: item.label })
        })
        this.setState({ createNameList: createNameList })
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
      })
  }

  //时间onchang
  timeOnchange(val, str) {
    this.field.setValue("createTimeStart", str[0]);
    this.field.setValue("createTimeEnd", str[1]);
  }

  //查询用水性质
  queryInspect(page, pageSize) {
    this.setState({ dataLoading: true });
    let values = this.field.getValues();
    values.page = page
    values.pageSize = pageSize
    delete values.queryCreateTime
    axios.post(`${url}revenue/fee/query/`, qs.stringify(values))
      .then((response) => {
        this.setState({
          dataSource: response.data.datas,
          page: response.data.page,
          pageSize: response.data.pageSize,
          total: response.data.totalSize,
          dataLoading: false
        })
      }).catch((error) => {
        Feedback.toast.error("网络错误,请刷新再试")
        this.setState({ dataLoading: false })
      })
  }

  //切换分页
  changePage(page) {
    const { pageSize } = this.state
    this.queryInspect(page, pageSize)
  }

  //改变每页显示条数
  changePageSize = (pageSize) => {
    this.queryInspect(1, pageSize)
  };

  //获取按钮权限
  getMenuByRole() {
    const { loginURL } = this.state;
    axios.get(loginURL + '/getAuthority', { withCredentials: true })
      .then((response) => {
        let jsondata = response.data;
        if (jsondata.statusCode == 0) {
          this.setState({ buttons: jsondata.buttons, });
        } else {
          Feedback.toast.error("权限获取失败")
        }
      }).catch((error) => {
        Feedback.toast.error("网络错误,请刷新再试")
      });
  }

  /*子组件改变父组件的state*/
  onChangeState(stateName) {
    this.setState(stateName)
  }

  render() {
    const { page, pageSize, total, dataSource, dataLoading, title, selectRecord, buttons, feeNameList, createNameList } = this.state;
    const { init } = this.field;
    const columns = [
      {
        title: '用水性质编号',
        dataIndex: 'id',
        key: 1
      },
      {
        title: '用水性质',
        dataIndex: 'name',
        key: 2
      },
      {
        title: '操作员',
        width: 200,
        dataIndex: 'createName',
        key: 3
      },
      {
        title: '日期',
        width: 200,
        dataIndex: 'createTime',
        key: 4
      },
      {
        title: '操作',
        width: 120,
        cell: (value, index, record) => {
          const look = (<Icon type="browse" style={{ color: "#FFA003", cursor: "pointer" }} size="small"
            onClick={() => this.setState({ title: '2', selectRecord: record })} />);
          let ishas = false;
          for (let i = 0; i < buttons.length; i++) {
            if (buttons[i].menuPath === '/System/rule' && buttons[i].buttonCode === 'updateRule') {
              ishas = true;
            }
          }
          return (
            <div style={{ display: 'flex', justifyContent: 'space-around' }}>
              <Tooltip trigger={look} align='t' text='查看规则配置' />
              {
                ishas ? <UpdateDialog record={record} queryInspect={this.queryInspect.bind(this)} /> : void (0)
              }
            </div>
          )
        },
        key: 5
      },
    ];

    return (
      <Fragment>
        <IceContainer title="用水性质查询">
          <Form direction="hoz" field={this.field}>
            <Row justify="space-around">
              <Col span={8}>
                <FormItem label="用水性质名称：" {...formItemLayout}>
                  <Input {...init('name')} placeholder="请输入用水性质名称"
                    style={{ width: 150 }} />
                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem label="创建人：">
                  <Combobox
                    {...init('createName')}
                    placeholder="--请选择--"
                    fillProps="label"
                    hasClear
                    dataSource={createNameList}
                  />
                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem label="创建日期：">
                  <RangePicker
                    {...init('queryCreateTime', { props: { onChange: (val, str) => this.timeOnchange(val, str) } })}
                    placeholder="日期"
                  />
                </FormItem>
              </Col>
            </Row>

          </Form>
          <div align="center">
            <Button type="primary" className="button" onClick={() => this.queryInspect(1, 10)}>
              <Icon type="search" />查询
            </Button>
            <Button type="secondary" className="button" onClick={() => this.field.reset()} style={{ marginLeft: 20 }}>
              <Icon type="refresh" />重置
            </Button>
          </div>
        </IceContainer>

        {
          title === 1 ?
            <IceContainer title="用水性质列表">
              {
                buttons.length > 0 ? buttons.map(item => {
                  if (item.menuPath === '/System/rule' && item.buttonCode
                    === 'addSetting') {
                    return (
                      <AddDialog queryInspect={() => this.queryInspect(1, 10)} />
                    )
                  }
                }) : void (0)
              }
              <BasicsTable columns={columns} dataSource={dataSource}
                total={total} pageSize={pageSize} page={page}
                dataLoading={dataLoading}
                changePage={(value) => this.changePage(value)}
                onPageSizeChange={(value) => this.changePageSize(value)} pageSizeList={[10, 50, 100]} />
            </IceContainer>
            :
            <PreviewRule onClick={this.onChangeState.bind(this)} record={selectRecord} />
        }
      </Fragment>
    )
  }
}
