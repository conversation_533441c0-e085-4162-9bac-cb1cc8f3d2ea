import React, {Component} from 'react';
import {
  Balloon,
  Button,
  Checkbox,
  Dialog,
  Feedback,
  Field,
  Form,
  Icon,
  Input,
  Select
} from '@icedesign/base';
import axios from 'axios';
import qs from 'qs';
import {url} from '../../../../components/URL/index'

const FormItem = Form.Item
const Tooltip = Balloon.Tooltip
const {Option} = Select
const {Group: CheckboxGroup} = Checkbox

export default class EditCommunity extends Component {

  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      buttonLoading: false,
      createId: sessionStorage.getItem("stuffId"),
      createName: sessionStorage.getItem("realName"),
    };
    this.field = new Field(this, {autoUnmount: true})
  }

  //操作弹窗
  handleDialog(visible) {
    const {record} = this.props
    this.setState({visible: visible})
    let list = []
    if (record.watermeterType) {
      list = record.watermeterType.split(',')
    }
    this.field.setValues({...record})
    if (list) {
      this.field.setValue('watermeterType', list)
    }
  }

  //修改事件
  handleOnClick() {
    const {createId, createName} = this.state
    const {record} = this.props
    this.field.validate((errors, values) => {
      if (errors) {
        return
      } else {
        this.setState({buttonLoading: true})
        let watermeterTypes = ''
        for (let i = 0; i < values.watermeterType.length; i++) {
          watermeterTypes += values.watermeterType[i];
          if (i != values.watermeterType.length - 1 && watermeterTypes != '') {
            watermeterTypes += ',';
          }
        }
        values.id = record.id
        values.createId = createId
        values.createName = createName
        values.watermeterType = watermeterTypes
        axios.post(`${url}revenue/region/update`, qs.stringify(values))
          .then((response) => {
            if (response.data.code == "0") {
              this.setState({visible: false})
              Feedback.toast.success("修改成功")
              this.props.queryCommunity()
            } else {
              Feedback.toast.error("修改失败:" + response.data.msg)
            }
            this.setState({buttonLoading: false})
          }).catch((error) => {
          this.setState({buttonLoading: false})
          Feedback.toast.error("程序出错了,请刷新后在试！")
        })
      }
    })
  }

  //抄表员选择
  handleOnchange(value, option, type) {
    const { roleNameList } = this.props;
    roleNameList.map((item) => {
      if (item.value == value) {
        if (type == 'meterReading') { // 抄表员
          this.field.setValue('copyTel', item.mobile)
          this.field.setValue('cid', item.value)
          this.field.setValue('cname', item.label)
        } else if (type == 'icmeterReading') { // 查表员
          this.field.setValue('iccopyTel', item.mobile)
          this.field.setValue('iccid', item.value)
          this.field.setValue('iccname', item.label)
        } else { // 维修员
          this.field.setValue('repairTel', item.mobile)
          this.field.setValue('repairmanId', item.value)
          this.field.setValue('repairmanName', item.label)
        }
      }
    })
  }

  render() {
    const {visible, buttonLoading} = this.state
    const {areaList, list, roleNameList, computerNameList} = this.props
    const {init} = this.field
    const formItemLayout = {
      labelCol: {fixedSpan: 9},
      wrapperCol: {fixedSpan: 12}
    }
    const edit = (<Icon type="survey" size="small" style={{color: "#1DC11D", cursor: 'pointer', marginRight: 15}}
                        onClick={() => this.handleDialog(true)}/>)
    const footer = (
      <div>
        <Button type="primary" loading={buttonLoading} onClick={() => this.handleOnClick()}>
          确定
        </Button>
        <Button onClick={() => this.handleDialog(false)} style={{marginLeft: 20}}>
          取消
        </Button>
      </div>
    );
    return (
      <div>
        <Tooltip trigger={edit} align='t' text='修改'/>

        <Dialog style={{width: 500}} visible={visible} isFullScreen={true}
                footer={footer} onClose={() => this.handleDialog(false)}
                title='修改小区' footerAlign="center">

          <Form field={this.field}>

            <FormItem label="选择片区：" {...formItemLayout}>
              <Select {...init('areaId', {rules: [{required: true, message: '片区名称必填'}]})} placeholder="请选择"
                      dataSource={areaList} style={{width: 240}}/>
            </FormItem>

            <FormItem {...formItemLayout} label="小区名称：">
              <Input  {...init('regionName', {rules: [{required: true, message: '小区名称必填'}]})}
                      placeholder="--请输入--"/>
            </FormItem>

            <FormItem {...formItemLayout} label="区号：">
              <Input  {...init('zoneCode')} placeholder="--请输入--"/>
            </FormItem>

            <FormItem {...formItemLayout} label="抄表周期：">
              <Select {...init('copyPeriod', {rules: [{required: true, message: '抄表日期必填'}]})} style={{width: 240}}>
                <Option value='0'>单月</Option>
                <Option value='1'>双月</Option>
                <Option value='2'>每月</Option>
              </Select>
            </FormItem>

            <FormItem {...formItemLayout} label="抄表类型：">
              <CheckboxGroup {...init('watermeterType', {rules: [{required: true, message: '请选抄表类型'}]})}
                             dataSource={list}/>
            </FormItem>

            <FormItem label="微机员：" {...formItemLayout}>
              <Select placeholder="--请输入--" {...init('computerId')} style={{width: 240}} dataSource={computerNameList}
                      onChange={(value, option) => {
                        this.field.setValue('computerId', value); // 设置value
                        this.field.setValue('computerName', option.label); //设置label
                      }}
              />
            </FormItem>

            <FormItem label="抄表员：" {...formItemLayout}>
              <Select  {...init('cid')} dataSource={roleNameList}
                       style={{width: '240px'}}
                       onChange={(value, option) => this.handleOnchange(value,
                         option, 'meterReading')}>
              </Select>
            </FormItem>

            <FormItem {...formItemLayout} label="抄表员电话：">
              <Input {...init('copyTel')} placeholder="--请输入--"/>
            </FormItem>

            <FormItem label="微机员(IC卡表)：" {...formItemLayout}>
              <Select placeholder="--请选择--" {...init('iccomputerId')}
                      style={{width: 240}} dataSource={computerNameList}
                      onChange={(value, option) => {
                        this.field.setValue('iccomputerId', value); // 设置value
                        this.field.setValue('iccomputerName', option.label); //设置label
                      }}
              />
            </FormItem>

            <FormItem label="查表员(IC卡表)：" {...formItemLayout}>
              <Select  {...init('iccid')} dataSource={roleNameList}
                       style={{width: '240px'}}
                       onChange={(value, option) => this.handleOnchange(value,
                         option, 'icmeterReading')}>
              </Select>
            </FormItem>

            <FormItem {...formItemLayout} label="查表员电话(IC卡表)：">
              <Input {...init('iccopyTel')} placeholder="--请输入--"/>
            </FormItem>


            <FormItem label="维修员：" {...formItemLayout}>
              <Select  {...init('repairmanId')} dataSource={roleNameList} style={{width: '240px'}}
                       onChange={(value,option) => this.handleOnchange(value, option,'repair')}>
              </Select>
            </FormItem>

            <FormItem {...formItemLayout} label="维修员电话：">
              <Input {...init('repairTel')} placeholder="--请输入--"/>
            </FormItem>

            <FormItem label="备&emsp;&emsp;注：" {...formItemLayout} >
              <Input multiple {...init('remarks')} placeholder="--请输入--"/>
            </FormItem>

          </Form>

        </Dialog>

      </div>
    );
  }
}
