import React, {Component} from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog,
  Feedback,
  Icon,
  Pagination,
  Table
} from '@icedesign/base';
import InfoModal from './InfoModal';
import {deleteRecord, getRecordDetails} from '../api';

const Tooltip = Balloon.Tooltip;
export class PageList extends Component {
    state = {
        dataRecord: {},
        infoModal: false,
        deleteModal: false,
        currentRecord: null,
    };
    componentDidMount() {
        this.props.getList(this.props.pagination);
    }
    setInfoModal = (boolean) => {
        this.setState({ infoModal: boolean });
    };
    showInfoRecord = (record) => {
        this.setState({ infoModal: true });
        getRecordDetails(record.id).then((response) => {
            const {
                status,
                data: { code, data },
            } = response;
            if (status === 200 && code === 0) {
                this.setState({ dataRecord: data });
            }
        });
    };
    showDeleteRecord = (record) => {
        this.setState({ deleteModal: true });
        this.setState({ currentRecord: record });
    };
    deleteCurrentRecord = (record) => {
        deleteRecord(record.id).then((response) => {
            const {
                status,
                data: { code, msg },
            } = response;
            if (status === 200 && code === '0') {
                this.props.getList(this.props.pagination);
                Feedback.toast.success('删除审批流成功！');
            } else {
                Feedback.toast.error(msg);
            }
        });
    };
    changePage = (current) => {
        const params = {
            ...this.props.pagination,
            page: current,
        };
        this.props.getList(params);
    };
    renderOperation = (action, index, record) => {
        const infoButton = (
            <Icon
                type="browse"
                size="small"
                style={{
                    color: '#FFA003',
                    cursor: 'pointer',
                    paddingLeft: '6px',
                    paddingRight: '6px',
                }}
                onClick={() => {
                    this.showInfoRecord(record);
                }}
            />
        );
        const deleteButton = (
            <Icon
                type="ashbin"
                size="small"
                style={{
                    color: '#FFA003',
                    cursor: 'pointer',
                    paddingLeft: '6px',
                    paddingRight: '6px',
                }}
                onClick={() => {
                    this.showDeleteRecord(record);
                }}
            />
        );
        return (
            <>
                <Tooltip trigger={infoButton} align="t" text="查看审批流" />
                <Tooltip trigger={deleteButton} align="t" text="删除审批流" />
            </>
        );
    };
    render() {
        const auditTypeEnum = {
            0: '变更手机',
            1: '变更户名',
        };
        return (
            <>
                <Table dataSource={this.props.dataSource}>
                    <Table.Column title="审批流编号" dataIndex="id" align="center" />
                    <Table.Column title="审批流名称" dataIndex="name" align="center" />
                    <Table.Column
                        title="审批流类别"
                        dataIndex="auditType"
                        align="center"
                        cell={(auditType) => auditTypeEnum[auditType]}
                    />
                    <Table.Column title="创建人" dataIndex="createUserName" align="center" />
                    <Table.Column
                        title="操作"
                        cell={(action, index, record) => this.renderOperation(action, index, record)}
                        align="center"
                    />
                </Table>
                <div className="notice-pagination">
                    <Pagination
                        current={this.props.pagination.page}
                        pageSize={this.props.pagination.pageSize}
                        total={this.props.pagination.totalSize}
                        onChange={(current) => this.changePage(current)}
                    />
                </div>
                <InfoModal
                    infoModal={this.state.infoModal}
                    setInfoModal={this.setInfoModal}
                    dataRecord={this.state.dataRecord}
                />
                <Dialog
                    visible={this.state.deleteModal}
                    onOk={() => {
                        this.deleteCurrentRecord(this.state.currentRecord);
                        this.setState({ deleteModal: false });
                    }}
                    onCancel={() => {
                        this.setState({ deleteModal: false });
                    }}
                    onClose={() => {
                        this.setState({ deleteModal: false });
                    }}
                    footerAlign="center"
                    title="删除确认"
                >
                    确认要删除这条数据吗？
                </Dialog>
            </>
        );
    }
}

export default PageList;
