import React, {Component} from 'react';
import {Dialog} from '@icedesign/base';

export class InfoModal extends Component {
    state = {
        imgModal: false,
        imgUrl: '',
    };

    render() {
        const { dataRecord, infoModal, setInfoModal } = this.props;
        const { sysAuditFlowList } = dataRecord;
        return (
            <Dialog
                className="modal-size"
                visible={infoModal}
                title="查看审批流详情"
                footer={false}
                onOk={() => setInfoModal(false)}
                onClose={() => setInfoModal(false)}
                onCancel={() => setInfoModal(false)}
            >
                {sysAuditFlowList && sysAuditFlowList.length ? (
                    sysAuditFlowList.map((item) => {
                        return (
                            <div key={item.id} style={{ display: 'flex', alignItems: 'center' }}>
                                <div style={{ display: 'flex', alignItems: 'center', marginRight: '12px' }}>
                                    <i
                                        style={{
                                            display: 'block',
                                            width: '12px',
                                            height: '12px',
                                            borderRadius: '50%',
                                            border: '2px solid #3080FE',
                                        }}
                                    />
                                </div>
                                <span>{item.roleName}</span>
                            </div>
                        );
                    })
                ) : (
                    <div>未配置审批角色</div>
                )}
            </Dialog>
        );
    }
}

export default InfoModal;
