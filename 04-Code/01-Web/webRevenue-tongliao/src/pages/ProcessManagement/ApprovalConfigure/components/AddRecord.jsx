import React, {Component} from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Form,
  Input,
  Select
} from '@icedesign/base';
import {addRecord, getApprovalRole} from '../api';
import '../index.scss';

export class AddRecord extends Component {
    state = {
        addModal: false,
        approvalRoleList: [],
        RolesNumber: [],
    };
    field = new Field(this);
    showAddRecord = () => {
        this.setState({ addModal: true });
    };
    addApprovalRole = () => {
        if (this.state.approvalRoleList.length) {
            const newRolesNumber = [...this.state.RolesNumber];
            newRolesNumber.push(1);
            this.setState({ RolesNumber: newRolesNumber });
        } else {
            this.getApprovalRoleList().then(() => {
                const newRolesNumber = [...this.state.RolesNumber];
                newRolesNumber.push(1);
                this.setState({ RolesNumber: newRolesNumber });
            });
        }
    };
    getApprovalRoleList = async () => {
        const response = await getApprovalRole();
        const {
            data: {
                obj: { list },
                statusCode,
            },
            status,
        } = response;
        if (status === 200 && statusCode === 0) {
            let newArray = list.map((item) => {
                return { label: item.roleName, value: item.id };
            });
            this.setState({ approvalRoleList: newArray });
        } else {
            Feedback.toast.error('获取审批角色列表失败！');
        }
    };
    renderApprovalRoleFormItem = (i) => {
        return (
            <Form.Item className="add-record-form-item" label="审批流角色：" required>
                <Select
                    className="add-record-input"
                    placeholder="请选择审批角色"
                    {...this.field.init(`roleId${i}`, {
                        rules: [
                            {
                                required: true,
                                message: '请选择审批流角色！',
                                trigger: ['onChange'],
                            },
                        ],
                    })}
                    dataSource={this.state.approvalRoleList}
                />
                {this.field.getError(`roleId${i}`) ? (
                    <span style={{ color: 'red' }}>{this.field.getError(`roleId${i}`).join(',')}</span>
                ) : (
                    ''
                )}
            </Form.Item>
        );
    };
    render() {
        const { init, getError, validate, reset } = this.field;
        const { RolesNumber } = this.state;
        return (
            <div className="add-record">
                <Button
                    type="primary"
                    onClick={() => {
                        this.showAddRecord();
                    }}
                >
                    新增审批流
                </Button>
                <Dialog
                    className="modal-size"
                    visible={this.state.addModal}
                    title="新增审批流"
                    footerAlign="center"
                    onCancel={() => {
                        reset();
                        this.setState({ addModal: false });
                    }}
                    onClose={() => {
                        reset();
                        this.setState({ addModal: false });
                    }}
                    onOk={() => {
                        validate((errors, values) => {
                            const createUid = sessionStorage.getItem('stuffId');
                            const roleIdValues = { ...values };
                            delete roleIdValues.name;
                            delete roleIdValues.auditType;
                            const valuesArray = Object.values(roleIdValues);
                            const sysAuditFlowDTOList = valuesArray.map((item) => {
                                return { roleId: item };
                            });
                            const params = {
                                name: values.name,
                                auditType: values.auditType,
                                sysAuditFlowDTOList,
                                createUid,
                            };
                            if (!errors) {
                                addRecord(params).then((response) => {
                                    const {
                                        status,
                                        data: { code, msg },
                                    } = response;
                                    if (status === 200 && code === 0) {
                                        Feedback.toast.success('添加审批流成功！');
                                        this.props.getList(this.props.pagination);
                                    } else {
                                        Feedback.toast.error(msg);
                                    }
                                    reset();
                                    this.setState({ addModal: false });
                                });
                            }
                        });
                    }}
                >
                    <Form colon className="add-record-form approval-configure">
                        <Form.Item className="add-record-form-item" label="审批流名称：" required>
                            <Input
                                className="add-record-input"
                                {...init('name', {
                                    rules: [
                                        {
                                            required: true,
                                            message: '请输入审批流名称！',
                                        },
                                    ],
                                })}
                            />
                            {getError('name') ? <span style={{ color: 'red' }}>{getError('name')}</span> : ''}
                        </Form.Item>
                        <Form.Item className="add-record-form-item" label="审批流类型：" required>
                            <Select
                                className="add-record-input"
                                placeholder="请选择"
                                {...init('auditType', {
                                    rules: [
                                        {
                                            required: true,
                                            message: '请输入审批流类型！',
                                        },
                                    ],
                                })}
                                dataSource={[
                                    { label: '变更手机', value: 0 },
                                    { label: '变更户名', value: 1 },
                                ]}
                            />
                            {getError('auditType') ? (
                                <span style={{ color: 'red' }}>{getError('auditType').join(',')}</span>
                            ) : (
                                ''
                            )}
                        </Form.Item>
                        {RolesNumber.map((a, i) => {
                            return <div key={i}>{this.renderApprovalRoleFormItem(i)}</div>;
                        })}
                        <Button
                            onClick={() => {
                                this.addApprovalRole();
                            }}
                        >
                            添加审批角色
                        </Button>
                    </Form>
                </Dialog>
            </div>
        );
    }
}

export default AddRecord;
