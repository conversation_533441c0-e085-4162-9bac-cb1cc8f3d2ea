import React, {Component} from 'react';
import IceContainer from '@icedesign/container';
import PageList from './components/PageList';
import AddRecord from './components/AddRecord';
import {getPageList} from './api';

export class Index extends Component {
    state = {
        pagination: {
            page: 1,
            pageSize: 10,
            applyUserId: sessionStorage.getItem('stuffId') ?? null,
        },
        dataSource: [],
    };
    getList = (params) => {
        getPageList(params).then((response) => {
            const {
                status,
                data: { datas, page, pageSize, totalSize },
            } = response;
            if (status === 200) {
                this.setState({
                    dataSource: datas,
                    pagination: { ...this.state.pagination, page, pageSize, totalSize },
                });
            }
        });
    };
    render() {
        return (
            <IceContainer title="审批流列表">
                <AddRecord pagination={this.state.pagination} getList={this.getList} />
                <PageList
                    pagination={this.state.pagination}
                    dataSource={this.state.dataSource}
                    getList={this.getList}
                />
            </IceContainer>
        );
    }
}

export default Index;
