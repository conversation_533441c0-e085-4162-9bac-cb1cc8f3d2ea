import React, {Component} from 'react';
import IceContainer from '@icedesign/container';
import PageList from './components/PageList';
import Search from './components/Search';
import {getPageList} from './api';
import '../index.scss';

export class Index extends Component {
    state = {
        pagination: {
            page: 1,
            pageSize: 10,
            applyUserId: sessionStorage.getItem('stuffId') ?? null,
        },
        dataSource: [],
    };
    getList = (params) => {
        getPageList(params).then((response) => {
            const {
                status,
                data: { datas, page, pageSize, totalSize },
            } = response;
            if (status === 200) {
                const pagination = { ...this.state.pagination, page, pageSize, totalSize };
                this.props.location.pathname === '/ProcessManagement/ApplyForInquiry' && delete pagination.applyUserId;
                this.setState({
                    dataSource: datas,
                    pagination,
                });
            }
        });
    };
    render() {
        return (
            <>
                <IceContainer title="申请查询">
                    <Search
                        pagination={this.state.pagination}
                        dataSource={this.state.dataSource}
                        getList={this.getList}
                        pathname={this.props.location.pathname}
                    />
                </IceContainer>
                <IceContainer title="我的申请">
                    <PageList
                        pagination={this.state.pagination}
                        dataSource={this.state.dataSource}
                        getList={this.getList}
                    />
                </IceContainer>
            </>
        );
    }
}

export default Index;
