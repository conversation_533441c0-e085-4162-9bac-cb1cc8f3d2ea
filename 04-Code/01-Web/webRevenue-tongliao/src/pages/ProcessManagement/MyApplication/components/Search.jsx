import React, {Component} from 'react';
import {
  Button,
  DatePicker,
  Field,
  Form,
  Icon,
  Input,
  Select
} from '@icedesign/base';

const { RangePicker } = DatePicker;
export class ApplySearch extends Component {
    field = new Field(this);
    searchApplication = () => {
        const values = this.field.getValues();
        const params = {
            ...values,
            ...this.props.pagination,
        };
        if (this.props.pathname === '/ProcessManagement/ApplyForInquiry') {
            delete params.applyUserId;
        }
        this.props.getList(params);
    };
    timeOnchange(str) {
        this.field.setValue('applyStartTime', str[0]);
        this.field.setValue('applyEndTime', str[1]);
    }
    reset() {
        this.field.reset();
    }
    render() {
        const { init } = this.field;
        return (
            <Form field={this.field}>
                <div className="search-form-apply">
                    <Form.Item className="search-form-apply-form-item" label="申请编号：">
                        <Input {...init('applyNo')} placeholder="请输入申请编号" />
                    </Form.Item>
                    <Form.Item className="search-form-apply-form-item" label="申请类别：">
                        <Select {...init('applyType')} placeholder="请选择">
                            <li value="0">变更手机</li>
                            <li value="1">变更户名</li>
                        </Select>
                    </Form.Item>
                    <Form.Item className="search-form-apply-form-item" label="申请状态：">
                        <Select {...init('applyStatus')} placeholder="请选择">
                            <li value="0">待审批</li>
                            <li value="1">同意</li>
                            <li value="2">拒绝</li>
                        </Select>
                    </Form.Item>
                </div>
                <div className="search-form-apply">
                    <Form.Item className="search-form-apply-form-item" label="申请时间：">
                        <RangePicker
                            {...init('ApplicationTime', {
                                props: { onChange: (val, str) => this.timeOnchange(str) },
                            })}
                        />
                    </Form.Item>
                </div>
                <Form.Item style={{ justifyContent: 'center' }}>
                    <Button
                        type="primary"
                        className="button"
                        onClick={() => this.searchApplication()}
                        style={{ marginRight: 10 }}
                    >
                        <Icon type="search" />
                        查询
                    </Button>
                    <Button
                        type="secondary"
                        className="button"
                        onClick={() => this.reset()}
                        style={{ marginRight: 10 }}
                    >
                        <Icon type="refresh" />
                        重置
                    </Button>
                </Form.Item>
            </Form>
        );
    }
}

export default ApplySearch;
