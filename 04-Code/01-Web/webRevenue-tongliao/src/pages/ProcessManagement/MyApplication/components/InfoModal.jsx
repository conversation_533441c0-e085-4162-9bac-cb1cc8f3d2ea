import React, {Component} from 'react';
import {Dialog} from '@icedesign/base';
import {url} from '../../../../components/URL';

export class InfoModal extends Component {
    state = {
        imgModal: false,
        imgUrl: '',
    };
    showBigImg = (i) => {
        this.setState({ imgModal: true, imgUrl: i });
    };
    renderingTable = () => {
        const { dataRecord } = this.props;
        const {
            type,
            applyId,
            applyStatus,
            cno,
            createTime,
            oldPhone,
            newPhone,
            oldName,
            newName,
            oldAddress,
            newAddress,
            imgList,
            reviewContent,
        } = dataRecord;
        return (
            <div className="application-info-table">
                <div className="table-col">
                    <div className="table-item">
                        <div className="table-title">申请编号：</div>
                        <div className="table-content">{applyId}</div>
                    </div>
                    <div className="table-item">
                        <div className="table-title">用户编号：</div>
                        <div className="table-content">{cno}</div>
                    </div>
                    <div className="table-item">
                        <div className="table-title">创建时间：</div>
                        <div className="table-content">{createTime}</div>
                    </div>
                </div>
                <div className="table-col">
                    <div className="table-item">
                        <div className="table-title">旧号码：</div>
                        <div className="table-content">{oldPhone}</div>
                    </div>
                    <div className="table-item">
                        <div className="table-title">新号码：</div>
                        <div className="table-content">{newPhone}</div>
                    </div>
                    <div className="table-item">
                        <div className="table-title">审批类型：</div>
                        <div className="table-content">{type === 0 ? '变更手机' : '变更户名'}</div>
                    </div>
                </div>
                <div className="table-col">
                    <div className="table-item">
                        <div className="table-title">旧户名：</div>
                        <div className="table-content">{oldName}</div>
                    </div>
                    <div className="table-item">
                        <div className="table-title">新户名：</div>
                        <div className="table-content">{newName}</div>
                    </div>
                    <div className="table-item">
                        <div className="table-title">审批进度：</div>
                        <div className="table-content">
                            {applyStatus === 0
                                ? '待审批'
                                : dataRecord?.applyStatus === 1
                                ? '同意'
                                : dataRecord?.applyStatus === 2
                                ? '拒绝'
                                : '未知'}
                        </div>
                    </div>
                </div>
                <div className="table-col">
                    <div className="table-item">
                        <div className="table-title">旧地址：</div>
                        <div className="table-content">{oldAddress}</div>
                    </div>
                    <div className="table-item">
                        <div className="table-title">新地址：</div>
                        <div className="table-content">{newAddress}</div>
                    </div>
                    <div className="table-item">
                        <div className="table-title" />
                        <div className="table-content" />
                    </div>
                </div>
                <div className="table-col">
                    <div className="table-item table-item-size-3">
                        <div className="table-title">审批意见：</div>
                        <div className="table-content">{reviewContent}</div>
                    </div>
                </div>
                <div className="table-col">
                    <div className="table-item table-item-size-3">
                        <div className="table-title">图片信息：</div>
                        <div className="table-content">
                            {imgList &&
                                imgList.map((i, index) => {
                                    return (
                                        <div key={index} className="table-content-img-box">
                                            <figure>
                                                <figcaption onClick={() => this.showBigImg(i)}>查看大图</figcaption>
                                                <img src={`${url}${i}`} />
                                            </figure>
                                        </div>
                                    );
                                })}
                        </div>
                    </div>
                </div>
            </div>
        );
    };
    render() {
        const { infoModal, setInfoModal } = this.props;
        return (
            <>
                <Dialog
                    className="modal-size"
                    visible={infoModal}
                    title="查看申请详情"
                    footer={false}
                    onOk={() => setInfoModal(false)}
                    onClose={() => setInfoModal(false)}
                    onCancel={() => setInfoModal(false)}
                >
                    {this.renderingTable()}
                </Dialog>
                <Dialog
                    className="modal-size show-img-modal"
                    visible={this.state.imgModal}
                    footer={false}
                    onClose={() => this.setState({ imgModal: false })}
                >
                    <img src={`${url}${this.state.imgUrl}`} />
                </Dialog>
            </>
        );
    }
}

export default InfoModal;
