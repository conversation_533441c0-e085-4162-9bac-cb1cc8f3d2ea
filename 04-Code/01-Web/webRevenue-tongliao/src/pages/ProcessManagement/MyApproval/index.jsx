import React, {Component} from 'react';
import IceContainer from '@icedesign/container';
import PageList from './components/PageList';
import Search from './components/Search';
import {getHasPageList, getNotPageList} from './api';
import '../index.scss';

export class Index extends Component {
    state = {
        pagination: {
            page: 1,
            pageSize: 10,
            userId: sessionStorage.getItem('stuffId') ?? null,
        },
        tabKey: 'not',
        notDataSource: [],
        hasDataSource: [],
    };
    getNotList = (params = this.state.pagination) => {
        getNotPageList(params).then((response) => {
            const {
                status,
                data: { datas, page, pageSize, totalSize },
            } = response;
            if (status === 200) {
                this.setState({
                    notDataSource: datas,
                    pagination: { ...this.state.pagination, page, pageSize, totalSize },
                });
            }
        });
    };
    getHasList = (params = this.state.pagination) => {
        getHasPageList(params).then((response) => {
            const {
                status,
                data: { datas, page, pageSize, totalSize },
            } = response;
            if (status === 200) {
                this.setState({
                    hasDataSource: datas,
                    pagination: { ...this.state.pagination, page, pageSize, totalSize },
                });
            }
        });
    };
    setTabKey = (params) => {
        this.setState({ tabKey: params });
    };
    render() {
        return (
            <>
                <IceContainer
                    title={
                        this.state.tabKey === 'not'
                            ? '查询未审批'
                            : this.state.tabKey === 'has'
                            ? '查询已审批'
                            : '审批查询'
                    }
                >
                    <Search
                        pagination={this.state.pagination}
                        notDataSource={this.state.notDataSource}
                        hasDataSource={this.state.hasDataSource}
                        getNotList={this.getNotList}
                        getHasList={this.getHasList}
                        tabKey={this.state.tabKey}
                    />
                </IceContainer>
                <IceContainer title="我的审批">
                    <PageList
                        pagination={this.state.pagination}
                        notDataSource={this.state.notDataSource}
                        hasDataSource={this.state.hasDataSource}
                        getNotList={this.getNotList}
                        getHasList={this.getHasList}
                        tabKey={this.state.tabKey}
                        setTabKey={this.setTabKey}
                    />
                </IceContainer>
            </>
        );
    }
}

export default Index;
