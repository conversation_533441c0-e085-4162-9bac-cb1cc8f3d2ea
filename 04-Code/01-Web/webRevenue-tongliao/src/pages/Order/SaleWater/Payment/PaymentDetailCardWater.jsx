import React, {Component} from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Feedback,
  Field,
  Form,
  Grid,
  Input,
  moment,
  Progress,
  Radio,
  Table
} from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import qs from 'qs';
import {url} from '../../../../components/URL';
import AccountMoneyRecord from './AccountMoneyRecord';
import ViewOrderDetail from '../../../../common/ViewOrderDetail';
import cardStatus from '../../../../common/cardStatus';
import sellWateCard from '../../../../common/sellWateCard';
import watermeterKindNo from '../../../../common/watermeterKindNo';
import {Link} from 'react-router-dom';
import {areaCode, systemCode42} from '../../../../components/areaCode/areaCode';
import {LoginURL} from '../../../../components/URL/LoginURL';
import getLodop from '../../../../common/LodopFuncs';
import FoundationSymbol from 'foundation-symbol';
import WaterPrice from '../../../../common/waterPrice';
import BillingRecord from '../../../User/UserSearch/components/billingRecord';
import styles from './PaymentDetailCardWater.module.scss';

const {Row, Col} = Grid;
const FormItem = Form.Item;
const {Group: RadioGroup} = Radio;
let LODOP = getLodop();

let lastTime = 0;

export default class PaymentDetailCardWater extends Component {
    constructor(props) {
        super(props);
        this.state = {
            ladderDataSource: [],
            tableDataSource: [],
            purchase: false, // 缴费按钮状态
            waterByPay: false, // 根据收款计算水量按钮状态
            payByWater: false, // 根据水量计算价格按钮状态
            stuffId: sessionStorage.getItem('stuffId'),
            stuffName: sessionStorage.getItem('realName'),
            account: 0, // 账户余额
            str: '', // 缴费明细
            amount: 0, // 计算金额
            visible: false, // 点击缴费按钮弹出弹出一个Balloon
            noticeVisible: true,
            orderId: '',
            ishas: true,
            loginURL: LoginURL,
            order: [],
            invoiceNo: '',
            invoiceCode: '',
            searchValue: {},
            bills: [],
            count: 0,
            visibleOfCode: false, // 支付码支付
            waitTime: 60,
            code: '',
            paymentId: null,
            setCountdown: null,
            latestInvoice: null,
            latestPrintTime: null
        };
        this.field = new Field(this, { autoUnmount: true });
    }

    componentWillMount() {
        const param = this.props.location.state;
        if (param == undefined) {
            this.props.history.push('/order/SaleWater');
            return;
        }
        const record = param.record;
        this.field.setValues({ ...record, printType: 0, isNote: 1 });

        this.getLatestInvoice();
        this.getMenuByRole();

        let cno = record.cno;
        let values = {};
        values.cno = cno;
        this.setState({
            account: record.account,
            searchValue: param.searchValue,
        });

        axios({
            method: 'post',
            url: `${url}revenue/order/findByCnoAndStatus`,
            data: qs.stringify(values),
        })
            .then((response) => {
                let jsondata = response.data;
                if (jsondata.code == '0') {
                    // 如果没有已付款没刷卡的订单，则判断是否有卡，如果没有卡则不能进入缴费页面
                    try {
                        if (record.watermeterCompany == '深圳华旭') {
                            let result = SZHXMETERCARD_Web.HXCD_4442_DisposeData_Web('1|1|', '').split('|');
                            console.log('hx4442read -> ', result);
                            if (result[0] == 1) {
                                if (result[2] == 0) {
                                    alert('上次缴费未刷表');
                                    this.props.history.push('/order/SaleWater');
                                } else {
                                    this.getTableDataSource();
                                    this.getLadderDataSource();
                                }
                            } else if (result[0] == 9) {
                                this.getTableDataSource();
                                this.getLadderDataSource();
                            } else {
                                alert(result[1]);
                                this.props.history.push('/order/SaleWater');
                            }
                        } else {
                            let readstr = hxdll.user_card();
                            let cardType = readstr.substring(1, 2);
                            if (readstr.length > 5) {
                                // 判断阶梯2/预付费2/预付费42上次购买水量是否刷表 预付费42暂时没有判断
                                if (cardType == '1' || cardType == '2' || cardType == '5') {
                                    let state = readstr.substr(-4);
                                    if (state != '0000') {
                                        alert('上次缴费未刷表');
                                        this.props.history.push('/order/SaleWater');
                                    } else {
                                        this.getTableDataSource();
                                        this.getLadderDataSource();
                                    }
                                } else {
                                    this.getTableDataSource();
                                    this.getLadderDataSource();
                                }
                            } else {
                                alert(cardStatus(readstr));
                                this.props.history.push('/order/SaleWater');
                            }
                        }
                    } catch (error) {
                        alert('浏览器不支持ActiveX控件，请使用IE');
                        this.props.history.push('/order/SaleWater');
                        return;
                    }
                } else {
                    alert(jsondata.msg);
                    this.props.history.push('/order/SaleWater');
                    return;
                }
            })
            .catch((error) => {
                Feedback.toast.error('Axios请求失败' + error);
            });
    }

     // 获取上次发票信息
    getLatestInvoice() {
        const printId = sessionStorage.getItem('stuffId');
        // const printId = "952e75e3f285433abe97a78432224916";
        axios.post(`${url}revenue/invoiceRecord/getLastInvoiceNo?n=1`,
            {printId: printId}).then((response) => {
            this.setState({latestInvoice: response.data.datas?.invoiceNo});
            this.setState({latestPrintTime: response.data.datas?.printTime});
        })
    }

    // 获取权限
    getMenuByRole() {
        const { loginURL } = this.state;
        axios.get(loginURL + '/getAuthority', { withCredentials: true }).then((response) => {
            let jsondata = response.data;
            if (jsondata.statusCode == 0) {
                let ishas = true;
                for (let i = 0; i < jsondata.datas.length; i++) {
                    if (
                        jsondata.datas[i].menuPath == '/order/SaleWater' &&
                        jsondata.datas[i].buttonCode == 'preferentialPrice'
                    ) {
                        ishas = false;
                        break;
                    }
                }
                this.setState({ ishas: ishas });
            }
        });
    }

    // 获取缴费记录列表
    getTableDataSource() {
        let cno = this.field.getValue('cno');
        axios.post(`${url}revenue/user/view`, qs.stringify({ cno: cno })).then((response) => {
            let jsondata = response.data.datas;
            let orders = jsondata.orders;
            let count = 0;
            orders.map((item, key) => {
                if (item.status != '2' && item.status != '4') {
                    count += Number(item.reallyFee);
                }
            });
            this.setState({
                bills: jsondata.bills,
                tableDataSource: orders,
                count: count.toFixed(2),
            });
        });
    }

    // 获取阶梯
    getLadderDataSource() {
        let cno = this.field.getValue('cno');
        axios.post(`${url}revenue/user/findStep`, qs.stringify({ cno: cno })).then((response) => {
            let jsondata = response.data.datas;
            this.setState({ ladderDataSource: jsondata });
        });
    }
    //
    resetMoney(e) {
        this.field.setValue('tunnage',e)
        this.field.reset(['pay','amount','fee']);
       // return Feedback.toast.error('水量发生变化!请重新计算金额');
    }
    // 根据水量计算价格
    computePriceByWater() {
        let tunnage = this.field.getValue('tunnage');
        if (tunnage != undefined && tunnage != 0 && tunnage > 0) {
            let cno = this.field.getValue('cno');
            let param = {};
            param.tunnage = tunnage;
            param.cno = cno;
            this.setState({ payByWater: true });
            axios
                .post(`${url}revenue/fee/calculate`, qs.stringify(param))
                .then((response) => {
                    if (response.data.code == 0) {
                        let jsondata = response.data.datas;
                        this.setState({ str: jsondata.str, amount: jsondata.amount });
                        // 设置阶梯
                        this.field.setValue('stepBalance', jsondata.stepBalance);
                        // 设置计算金额
                        this.field.setValue('amount', jsondata.amount);
                        // 计算应收金额
                        const { account } = this.state;
                        let preferentialPrice1 = this.field.getValue('preferentialPrice');
                        let fee1 = parseFloat(jsondata.amount) - parseFloat(preferentialPrice1) - parseFloat(account);
                        if (fee1 > 0) {
                            this.field.setValue('fee', fee1.toFixed(2));
                        } else {
                            this.field.setValue('fee', 0);
                        }
                        // 判断收款金额是否存在，如果存在则需要计算剩余金额
                        let pay1 = this.field.getValue('pay');
                        if (pay1) {
                            let changeAmount1 = parseFloat(pay1) - parseFloat(fee1);
                            // 设置剩余金额
                            this.field.setValue('changeAmount', changeAmount1.toFixed(2));
                            // 判断结余方式是否存在，如果存在则需要计算实收金额
                            let clearingWay = this.field.getValue('clearingWay');
                            if (clearingWay) {
                                if (clearingWay == 2) {
                                    // 存入账户-----实收金额==收款金额
                                    let reallyFee = pay1;
                                    this.field.setValue('reallyFee', reallyFee);
                                } else {
                                    // 找零-----实收金额==应收金额
                                    let reallyFee = this.field.getValue('fee');
                                    this.field.setValue('reallyFee', reallyFee);
                                }
                            }
                        }
                    } else {
                        alert(response.data.msg);
                    }
                    this.setState({ payByWater: false });
                })
                .catch((error) => {
                    Feedback.toast.error('Axios请求失败' + error);
                    this.setState({ payByWater: false });
                });
        } else {
            Feedback.toast.error('购买水量不能小于0或等于0或为空');
        }
    }

    // 根据收款计算水量
    computerWaterByMoney() {
        const { account } = this.state;
        let money = this.field.getValue('pay');
        if (money != undefined && money != 0 && money > 0) {
            let cno = this.field.getValue('cno');
            let param = {};
            param.money = (parseFloat(money) + parseFloat(account)).toFixed(2);
            param.cno = cno;
            this.setState({ waterByPay: true });
            axios({
                method: 'post',
                url: `${url}revenue/fee/inverse`,
                data: qs.stringify(param),
            })
                .then((response) => {
                    let jsondata = response.data.datas;
                    this.setState({ str: jsondata.str, amount: jsondata.amount });
                    this.field.setValue('amount', jsondata.amount);
                    // 设置购水量
                    this.field.setValue('tunnage', jsondata.tunnage);
                    this.field.setValue('stepBalance', jsondata.stepBalance);
                    // 计算应收金额
                    const { account } = this.state;
                    let preferentialPrice1 = this.field.getValue('preferentialPrice');
                    let fee1 = parseFloat(jsondata.amount) - parseFloat(preferentialPrice1) - parseFloat(account);
                    // 设置应收金额
                    if (fee1 > 0) {
                        this.field.setValue('fee', fee1.toFixed(2));
                    } else {
                        this.field.setValue('fee', 0);
                    }
                    // 设置剩余金额
                    this.field.setValue('changeAmount', parseFloat(jsondata.changeAmount).toFixed(2));
                    if (jsondata.desc != '') {
                        alert(jsondata.desc);
                    }
                    // 设置实收金额
                    // 判断结余方式是否存在，如果存在则需要计算实收金额
                    let clearingWay = this.field.getValue('clearingWay');
                    if (clearingWay) {
                        if (clearingWay == 2) {
                            // 存入账户-----实收金额==收款金额
                            let reallyFee = money;
                            this.field.setValue('reallyFee', reallyFee);
                        } else {
                            // 找零-----实收金额==应收金额
                            let reallyFee = this.field.getValue('fee');
                            this.field.setValue('reallyFee', reallyFee);
                        }
                    }
                    this.setState({ waterByPay: false });
                })
                .catch((error) => {
                    this.setState({ waterByPay: false });
                    Feedback.toast.error('Axios请求失败' + error);
                });
        } else {
            Feedback.toast.error('收款金额不能小于0或等于0或为空');
        }
    }

    // 缴费
    purchase() {
        console.log('purchase');
        this.field.validate((errors, values) => {
            console.log(values.watermeterCompany);
            if (!errors) {
                console.log('tunnage', values.tunnage);
                this.setState({ purchase: true });
                if (values.tunnage != 0) {
                    // 获取卡上卡号
                    let cardNo = '';
                    if (values.watermeterCompany == '深圳华旭') {
                        let result = SZHXMETERCARD_Web.HXCD_4442_DisposeData_Web('1|1|', '').split('|');
                        console.log('hx4442read -> ', result);
                        if (result[0] == 1) {
                            cardNo = '00' + result[26];
                        } else if (result[0] == 9) {
                            cardNo = '00' + result[3];
                        } else {
                            alert('读卡失败:');
                            this.setState({ purchase: false });
                        }
                    } else {
                        let readstr = hxdll.user_card();
                        if (readstr.length > 5) {
                            let type = readstr.substring(1, 2);
                            if (type == '3' || type == '5') {
                                cardNo = readstr.substring(2, 12);
                            } else {
                                cardNo = readstr.substring(2, 10);
                            }
                        } else {
                            alert('读卡失败:' + cardStatus(readstr));
                            this.setState({ purchase: false });
                        }
                    }
                    // 判断卡号
                    let userCardNo = this.field.getValue('cardNo');
                    if (cardNo != userCardNo) {
                        alert('读卡器上卡与用户卡号不匹配');
                        this.setState({ purchase: false });
                        return;
                    }
                    // 增加计算金额
                    const { account, str, stuffId, stuffName } = this.state;
                    values.account = account;
                    values.descr = str;
                    values.createId = stuffId;
                    values.createName = stuffName;
                    values.source = '柜台';
                    values.isTax = 0;
                    values.isNote = 2;
                    if (values.printType === 0) {
                        values.kplx = 3;
                    } else if (values.printType === 1) {
                        values.kplx = 1;
                    }
                    values.createTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
                    axios
                        .post(`${url}revenue/order/create`, qs.stringify(values))
                        .then((response) => {
                            let jsondata = response.data;
                            if (jsondata.code == 0) {
                                // 订单创建成功调用接口查询订单
                                let id = jsondata.datas;
                                let list = [];
                                list.page = 1;
                                list.pageSize = 10;
                                list.id = id;
                                axios
                                    .post(`${url}revenue/order/query`, qs.stringify(list))
                                    .then((response) => {
                                        this.setState({ orderId: jsondata.datas, order: response.data.datas }, () => {
                                            this.submitOrder();
                                        });
                                    })
                                    .catch((error) => {
                                        alert('缴费成功,写卡错误,请前往订单管理页面写卡');
                                        this.props.history.push('/order/OrderSearch');
                                    });
                            } else {
                                alert(jsondata.msg);
                                this.setState({ purchase: false });
                            }
                        })
                        .catch((error) => {
                            this.setState({ purchase: false });
                            Feedback.toast.error('Axios请求失败' + error);
                        });
                } else {
                    this.setState({ dataLoading: false, purchase: false });
                    Feedback.toast.error('购买水量不能为0');
                }
            }
        });
    }
    // 总水费
    printAll() {
        LODOP = getLodop();
        const { stuffId, stuffName, order } = this.state;
        let record = order[0];
        axios({
            method: 'post',
            url: `${url}revenue/printTemplate/get`,
            data: qs.stringify({ templateName: '通辽发票卡表' }),
        })
            .then((response) => {
                let ajaxData = response.data;
                if (ajaxData.code == '0') {
                    // 模板数据
                    let templateCode = ajaxData.datas.templateCode;
                    // 打印记录所需参数
                    let values = this.field.getValues();
                    values.printName = stuffName;
                    values.printId = stuffId;
                    values.createName = record.createName;
                    values.createId = record.createId;
                    // values.printName = '孙亚利'
                    // values.printId = '265a8646f3b641978d2daa4bd10c2e38'
                    // values.createName = '孙亚利'
                    // values.createId = '265a8646f3b641978d2daa4bd10c2e38'
                    values.createTime = moment(record.createTime).format('YYYY-MM-DD HH:mm:ss');
                    values.invoiceType = '0';
                    values.printType = '0';
                    values.orderId = record.id;
                    values.cno = record.cno;
                    values.cname = record.cname;
                    values.waterTotalFee = record.waterTotalFee;
                    values.sewageTotalFee = record.sewageTotalFee;
                    values.waterSourceFee = record.waterSourceFee;
                    values.reallyFee = record.reallyFee;
                    values.tunnage = record.tunnage;
                    values.lastTunnage = record.totalTunnage;
                    values.invoiceNo = this.field.getValue('invoiceNo');
                    values.invoiceCode = '';
                    // 水价明细
                    let waterPrice = WaterPrice(record);
                    let temp = templateCode
                        .replace('日期', moment(new Date()).format('YYYY-MM-DD'))
                        .replace('用户编号', record.cno)
                        .replace('用户名', record.cname)
                        .replace('应收金额1', record.reallyFee)
                        .replace('水量1', record.tunnage)
                        .replace('清水单价', waterPrice)
                        .replace('污水单价', record.sewageTotalFee)
                        .replace('操作员', record.createName + (record.source == '聚合支付' ? '（聚合支付）' : ''))
                        .replace('日期1', moment(new Date()).format('YYYY-MM-DD'))
                        .replace('用户编号1', record.cno)
                        .replace('用户名1', record.cname)
                        .replace('应收金额2', record.reallyFee)
                        .replace('水量2', record.tunnage)
                        .replace('清水单价1', waterPrice)
                        .replace('污水单价1', record.sewageTotalFee)
                        .replace('操作员1', record.createName + (record.source == '聚合支付' ? '（聚合支付）' : ''))
                        .replace('发票代码1', '')
                        .replace('发票号码1', '')
                        .replace('发票代码2', '')
                        .replace('发票号码2', '')
                        .replace('水资源费1', record.sourceTotalFee)
                        .replace('水资源费2', record.sourceTotalFee)
                        .replace('上次结存1', '')
                        .replace('本次结存1', '')
                        .replace('上次结存2', '')
                        .replace('本次结存2', '')
                        .replace('现示数1', record.totalTunnage)
                        .replace('现示数2', record.totalTunnage);
                    eval(temp);
                    let flag = LODOP.PRINT();
                    if (flag) {
                        axios({
                            method: 'post',
                            url: `${url}revenue/invoiceRecord/save`,
                            data: qs.stringify(values),
                        })
                            .then((response) => {
                                this.props.history.push('/order/SaleWater');
                            })
                            .catch((error) => {
                                alert('打印失败,请补打发票');
                                this.props.history.push('/order/OrderSearch');
                            });
                    } else {
                        alert('打印失败,请补打发票');
                        this.props.history.push('/order/OrderSearch');
                    }
                }
            })
            .catch((error) => {
                alert('打印失败,请补打发票');
                this.props.history.push('/order/OrderSearch');
            });
    }
    // 清水费
    printWaterTotalFee() {
        LODOP = getLodop();
        const { stuffId, stuffName, order, invoiceNo, invoiceCode } = this.state;
        let record = order[0];
        axios
            .post(`${url}revenue/printTemplate/get`, qs.stringify({ templateName: '通辽发票卡表' }))
            .then((response) => {
                let ajaxData = response.data;
                if (ajaxData.code == '0') {
                    // 模板数据
                    let templateCode = ajaxData.datas.templateCode;
                    // 打印记录所需参数
                    let values = this.field.getValues();
                    values.printName = stuffName;
                    values.printId = stuffId;
                    values.createName = record.createName;
                    values.createId = record.createId;
                    values.createTime = moment(record.createTime).format('YYYY-MM-DD HH:mm:ss');
                    values.invoiceType = '1';
                    values.printType = '1';
                    values.orderId = record.id;
                    values.cno = record.cno;
                    values.cname = record.cname;
                    values.waterTotalFee = record.waterTotalFee;
                    values.sewageTotalFee = record.sewageTotalFee;
                    values.waterSourceFee = record.waterSourceFee;
                    values.reallyFee = record.reallyFee;
                    values.tunnage = record.tunnage;
                    values.lastTunnage = record.totalTunnage;
                    values.invoiceNo = this.field.getValue('waterInvoiceNo');
                    values.invoiceCode = invoiceCode;
                    // 水价明细
                    let waterPrice = WaterPrice(record);
                    // 水资源费
                    let sourceTotalFee = record.sourceTotalFee ? record.sourceTotalFee : '0';
                    // 应收金额
                    let realFee = (Number(record.waterTotalFee) + Number(sourceTotalFee)).toFixed(2);
                    let temp = templateCode
                        .replace('日期', moment(new Date()).format('YYYY-MM-DD'))
                        .replace('用户编号', record.cno)
                        .replace('用户名', record.cname)
                        .replace('应收金额1', realFee)
                        .replace('水量1', record.tunnage)
                        .replace('清水单价', waterPrice)
                        .replace('污水单价', '')
                        .replace('操作员', record.createName + (record.source == '聚合支付' ? '（聚合支付）' : ''))
                        .replace('日期1', moment(new Date()).format('YYYY-MM-DD'))
                        .replace('用户编号1', record.cno)
                        .replace('用户名1', record.cname)
                        .replace('应收金额2', realFee)
                        .replace('水量2', record.tunnage)
                        .replace('清水单价1', waterPrice)
                        .replace('污水单价1', '')
                        .replace('操作员1', record.createName + (record.source == '聚合支付' ? '（聚合支付）' : ''))
                        .replace('发票代码1', '')
                        .replace('发票号码1', '')
                        .replace('发票代码2', '')
                        .replace('发票号码2', '')
                        .replace('水资源费1', sourceTotalFee)
                        .replace('水资源费2', sourceTotalFee)
                        .replace('上次结存1', '')
                        .replace('本次结存1', '')
                        .replace('上次结存2', '')
                        .replace('本次结存2', '')
                        .replace('现示数1', record.totalTunnage)
                        .replace('现示数2', record.totalTunnage);
                    eval(temp);
                    let flag = LODOP.PRINT();
                    if (flag) {
                        axios({
                            method: 'post',
                            url: `${url}revenue/invoiceRecord/save`,
                            data: qs.stringify(values),
                        })
                            .then((response) => {
                                if (response.data.code == '0') {
                                    this.printSewageTotalFee(templateCode);
                                } else {
                                    alert('添加打印记录失败,请补打发票');
                                    this.props.history.push('/order/OrderSearch');
                                }
                            })
                            .catch((error) => {
                                alert('添加打印记录失败,请补打发票');
                                this.props.history.push('/order/OrderSearch');
                            });
                    } else {
                        alert('打印失败,请补打发票');
                        this.props.history.push('/order/OrderSearch');
                    }
                }
            })
            .catch((error) => {
                alert('打印失败,请补打发票');
                this.props.history.push('/order/OrderSearch');
            });
    }
    // 污水费
    printSewageTotalFee(templateCode) {
        LODOP = getLodop();
        const { stuffId, stuffName, order } = this.state;
        let record = order[0];
        LODOP = getLodop();
        let printId = stuffId;
        // 打印记录所需参数
        let values = this.field.getValues();
        values.printName = stuffName;
        values.printId = printId;
        values.createName = record.createName;
        values.createId = record.createId;
        values.createTime = moment(record.createTime).format('YYYY-MM-DD HH:mm:ss');
        values.invoiceType = '2';
        values.printType = '1';
        values.orderId = record.id;
        values.cno = record.cno;
        values.cname = record.cname;
        values.waterTotalFee = record.waterTotalFee;
        values.sewageTotalFee = record.sewageTotalFee;
        values.waterSourceFee = record.waterSourceFee;
        values.reallyFee = record.reallyFee;
        values.tunnage = record.tunnage;
        values.lastTunnage = record.totalTunnage;
        values.invoiceNo = this.field.getValue('sewageInvoiceNo');;
        values.invoiceCode = '';
        let temp = templateCode
            .replace('日期', moment(new Date()).format('YYYY-MM-DD'))
            .replace('用户编号', record.cno)
            .replace('用户名', record.cname)
            .replace('应收金额1', record.sewageTotalFee)
            .replace('水量1', record.tunnage)
            .replace('清水单价', '')
            .replace('污水单价', record.sewageTotalFee)
            .replace('操作员', record.createName + (record.source == '聚合支付' ? '（聚合支付）' : ''))
            .replace('日期1', moment(new Date()).format('YYYY-MM-DD'))
            .replace('用户编号1', record.cno)
            .replace('用户名1', record.cname)
            .replace('应收金额2', record.sewageTotalFee)
            .replace('水量2', record.tunnage)
            .replace('清水单价1', '')
            .replace('污水单价1', record.sewageTotalFee)
            .replace('操作员1', record.createName + (record.source == '聚合支付' ? '（聚合支付）' : ''))
            .replace('发票代码1', '')
            .replace('发票号码1', '')
            .replace('发票代码2', '')
            .replace('发票号码2', '')
            .replace('水资源费1', '')
            .replace('水资源费2', '')
            .replace('上次结存1', '')
            .replace('本次结存1', '')
            .replace('上次结存2', '')
            .replace('本次结存2', '')
            .replace('现示数1', record.totalTunnage)
            .replace('现示数2', record.totalTunnage);
        eval(temp);
        let flag = LODOP.PRINT();
        if (flag) {
            axios
                .post(`${url}revenue/invoiceRecord/save`, qs.stringify(values))
                .then((response) => {
                    if (response.data.code == '0') {
                        this.props.history.push('/order/SaleWater');
                    } else {
                        alert('添加打印记录失败,请补打发票');
                        this.props.history.push('/order/OrderSearch');
                    }
                })
                .catch((error) => {
                    alert('添加打印记录失败,请补打发票');
                    this.props.history.push('/order/OrderSearch');
                });
        } else {
            alert('打印失败,请补打发票');
            this.props.history.push('/order/OrderSearch');
        }
    }
    // 写卡操作：如果写卡成功，则调用后台修改订单状态为“已完成” 1、如果修改订单状态失败，则调用退卡操作
    submitOrder() {
        console.log('submitOrder');
        this.field.validate((errors, values) => {
            if (!errors) {
                let type = watermeterKindNo(this.field.getValue('watermeterKind'));
                let cardno = this.field.getValue('cardNo');
                let times = Number(this.field.getValue('totalTimes'));
                let totalwater = this.field.getValue('totalTunnage');
                let water = this.field.getValue('tunnage');
                let cno = values.cno;
                try {
                    if (values.watermeterCompany == '深圳华旭') {
                        let watermeterId = this.field.getValue('watermeterId').substring(0, 8);
                        const { stuffId } = this.state;
                        let id = stuffId.substring(0, 4);
                        // 系统码|子表号|电子表号|购买量|关阀报警|囤积限量|购水次数|有效卡标志|IC卡号|用户编码|操作员|总购量|
                        let parameter =
                            systemCode42 +
                            '|1|' +
                            watermeterId +
                            '|' +
                            water +
                            '|3|1500|' +
                            (times + 1) +
                            '|0|' +
                            1 +
                            '|' +
                            cno.substring(2) +
                            '|' +
                            id +
                            '|' +
                            totalwater +
                            '|';
                        console.log('hx4442buy -> ', parameter);
                        let result = SZHXMETERCARD_Web.HXCD_4442_UserCard_Web(parameter).split('|');
                        console.log('hx4442buy -> ', result);
                        if (result[0] > 0) {
                            this.updateOrderStatus();
                        } else {
                            alert('缴费成功，写卡错误：' + result[1] + '\n' + '请前往订单管理页面写卡');
                            // 跳转订单管理页面，写卡
                            this.props.history.push('/order/OrderSearch');
                        }
                    } else {
                        let i = hxdll.sellwater();
                        if (i == 10) {
                            Feedback.toast.error('读卡失败:无卡');
                        } else if (i == 100) {
                            Feedback.toast.error('读卡失败:读卡失败');
                        } else if (i == 101) {
                            Feedback.toast.error('读卡失败:读卡失败');
                        } else if (i == 102) {
                            Feedback.toast.error('读卡失败:非用户卡');
                        } else {
                            // 如果读卡器上有卡或者卡类型正确执行售水
                            let result = hxdll.sellconfirm(
                                cardno,
                                areaCode,
                                times,
                                water,
                                totalwater,
                                0,
                                0,
                                0,
                                0,
                                0,
                                type,
                            );
                            if (result == 0) {
                                this.updateOrderStatus();
                                alert('缴费成功，写卡成功');
                            } else {
                                alert('缴费成功，写卡错误：' + sellWateCard(result) + '\n' + '请前往订单管理页面写卡');
                                // 跳转订单管理页面，写卡
                                this.props.history.push('/order/OrderSearch');
                            }
                        }
                    }
                } catch (error) {
                    alert('缴费成功,浏览器不支持写卡请前往订单管理页面重新写卡');
                    // 跳转订单管理页面，写卡
                    this.props.history.push('/order/OrderSearch');
                }
            }
        });
    }

    // 修改订单状态：1.写卡成功：将缴费状态改为已完成 2.如果成功，则缴费成功，关闭Dialog、清空缴费字段 3如果失败，则执行退卡
    updateOrderStatus() {
        const { orderId } = this.state;
        let value = {};
        value.id = orderId;
        axios
            .post(`${url}revenue/order/complete`, qs.stringify(value))
            .then((response) => {
                let jsondata = response.data;
                if (jsondata.code == 0) {
                    // 判断合打还是分打
                    let printType = this.field.getValue('printType');
                    if (printType === 0) {
                        let isNote = this.field.getValue('isNote');
                        if (isNote == '1') {
                            this.printAll();
                        } else if (isNote == '0') {
                            this.printWaterTotalFee();
                        }
                    } else {
                        // 跳转到缴费列表页面
                        this.props.history.push('/order/SaleWater');
                        alert('缴费成功，写卡成功');
                        this.setState({ visible: false });
                    }
                } else {
                    // 跳转到缴费列表页面
                    this.props.history.push('/order/OrderSearch');
                }
            })
            .catch((error) => {
                this.props.history.push('/order/OrderSearch');
            });
    }

    // 根据优惠金额计算应收金额：1、改变应收金额，2、改变剩余金额，3、改变实收金额
    changeFeeByPreferential(value) {
        // 账户余额
        const { account, amount } = this.state;
        if (value) {
            // 设置优惠金额
            this.field.setValue('preferentialPrice', value);
            // 计算应收金额
            let fee1 = parseFloat(amount) - parseFloat(account) - parseFloat(value);
            if (fee1 > 0) {
                this.field.setValue('fee', fee1.toFixed(2));
            } else {
                this.field.setValue('fee', 0);
            }
            // 计算剩余金额
            let pay1 = this.field.getValue('pay');
            if (pay1) {
                let changeAmount1 = parseFloat(pay1) - parseFloat(fee1);
                this.field.setValue('changeAmount', changeAmount1.toFixed(2));
                // 计算实收金额
                let clearingWay1 = this.field.getValue('clearingWay');
                if (clearingWay1) {
                    if (clearingWay1 == 1) {
                        // 找零
                        let reallyFee1 = this.field.getValue('fee');
                        this.field.setValue('reallyFee', reallyFee1);
                    } else if (clearingWay1 == 2) {
                        // 存入账户
                        let reallyFee1 = pay1;
                        this.field.setValue('reallyFee', reallyFee1.toFixed(2));
                    }
                } else {
                    this.field.reset('reallyFee');
                }
            }
        } else {
            this.field.reset('preferentialPrice');
            let fee1 = parseFloat(amount) - parseFloat(account);
            this.field.setValue('fee', fee1.toFixed(2));
        }
    }

    // 根据收款金额计算剩余金额：1.改变剩余金额  (=收款金额-实收金额),2.改变实收金额
    computeChangeAmount(value) {
        if (value) {
            this.field.setValue('pay', value);
            // 应收金额
            let fee2 = this.field.getValue('fee');

            if (Number(fee2) == 0) {
                const { account, amount } = this.state;
                let preferentialPrice = this.field.getValue('preferentialPrice');
                fee2 = parseFloat(amount) - parseFloat(account) - parseFloat(preferentialPrice);
            }

            if (fee2 != null || fee2 != undefined) {
                let changeAmount1 = parseFloat(value) - parseFloat(fee2);
                // 设置剩余金额parseFloat
                this.field.setValue('changeAmount', changeAmount1.toFixed(2));
                // 计算实收金额
                let clearingWay1 = this.field.getValue('clearingWay');
                if (clearingWay1) {
                    if (clearingWay1 == 1) {
                        // 找零
                        let reallyFee1 = parseFloat(fee2);
                        this.field.setValue('reallyFee', reallyFee1.toFixed(2));
                    } else if (clearingWay1 == 2) {
                        // 存入账户
                        let reallyFee1 = parseFloat(value);
                        this.field.setValue('reallyFee', reallyFee1.toFixed(2));
                    }
                } else {
                    this.field.reset('reallyFee');
                }
            }
        } else {
            this.field.reset('pay');
            this.field.reset('changeAmount');
            this.field.reset('reallyFee');
        }
    }

    // 根据结余方式计算实收金额
    computeReallyFee(value) {
        this.field.setValue('clearingWay', value);
        // 收款金额
        let pay1 = this.field.getValue('pay');
        if (pay1) {
            if (value == 2) {
                // 存入账户
                this.field.setValue('reallyFee', pay1);
            } else if (value == 1) {
                // 找零
                // 剩余金额
                let changeAmount1 = this.field.getValue('changeAmount');
                if (changeAmount1) {
                    let reallyFee1 = parseFloat(pay1) - parseFloat(changeAmount1);
                    this.field.setValue('reallyFee', reallyFee1.toFixed(2));
                }
            }
        }
    }

    // 订单表格操作
    rowOptionRender(record) {
        return <ViewOrderDetail record={record} />;
    }

    countdown = () => {
        this.setState({
            setCountdown: setInterval(() => {
                if (this.state.waitTime > 0) {
                    this.setState({
                        waitTime: this.state.waitTime - 1,
                    });
                } else if (this.state.waitTime == 0) {
                    Feedback.toast.error('已超时');
                    clearInterval(this.state.setCountdown);
                    this.setState(
                        {
                            visibleOfCode: false,
                            waitTime: 60,
                        },
                        () => {
                            document.removeEventListener('keyup', this.keyupEvent);
                        },
                    );
                    return;
                }
            }, 1000),
        });
    };

    clearCode = () => {
        lastTime = 0;
        this.setState({
            code: '',
        });
    };

    translation(s) {
        // 去掉转义字符
        s = s.replace(/[\'\"\\\/\b\f\n\r\t]/g, '');
        // 去掉特殊字符
        s = s.replace(/[\@\#\$\%\^\&\*\(\)\{\}\:\\L\<\>\?\[\]]/);
        return s;
    }

    // 点击缴费按钮，弹出确认框
    confirm() {
        const values = this.field.getValues();
        const isNote = values.isNote;
        const waterInvoiceNo = values.waterInvoiceNo;
        const sewageInvoiceNo = values.sewageInvoiceNo;

        if(isNote === 0 && waterInvoiceNo === sewageInvoiceNo){
            alert('清水发票号码和污水发票号码不能相同！');
        }else{
            axios.post(`${url}revenue/invoiceRecord/checkIfUsed`,
                {invoiceNo: values.invoiceNo, waterInvoiceNo: values.waterInvoiceNo, sewageInvoiceNo: values.sewageInvoiceNo }
            ).then((response) => {
                if (response.data.code === '1') {
                    alert(response.data.msg);
                } else {
                    this.field.validate((errors, values) => {
                        if (errors) {
                            return;
                        } else if (values.payWay == '5') {
                            this.verification();
                        } else {
                            this.setState({visible: true});
                        }
                    });
                }
            });
        }
    }

    keyupEvent = (e) => {
        let sw = true;
        if (sw) {
            let currCode = e.which;
            let currTime = new Date().getTime();
            // console.log('code1', currCode);
            // 判断排除人为误操作按键被插入付款码
            if (e.keyCode >= 48 && e.keyCode <= 57) {
                // 键盘 0 ~ 9 按键取值
                if (lastTime > 0) {
                    if (currTime - lastTime <= 1000) {
                        // 按键时间间隔小于1秒取值
                        this.setState(
                            {
                                code: (this.state.code += String.fromCharCode(currCode)),
                            },
                            () => {
                                // console.log('code2', this.state.code);
                            },
                        );
                    } else {
                        this.clearCode(); // 超时清空
                    }
                } else {
                    // 第一次按键，取支付码第一位的值
                    this.setState({
                        code: String.fromCharCode(currCode),
                    });
                }
            }
            lastTime = currTime;
            if (currCode == 13) {
                sw = false;
                this.countdown();
                this.payment();
                // 回车输入后清空
                this.clearCode();
            }
        }
    };

    // 验证
    verification = () => {
        let value = {};
        value.cno = this.field.getValue('cno');
        value.hno = this.field.getValue('hno');
        value.totalFee = this.field.getValue('reallyFee');
        value.type = this.field.getValue('areaName') === '市区' ? '01' : '02';
        value.createTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
        axios({
            method: 'post',
            url: `${url}revenue/order/unifiedPay`,
            data: value,
        })
            .then((response) => {
                if (response.data.code === '0') {
                    this.setState(
                        {
                            paymentId: response.data.datas,
                            visibleOfCode: true,
                        },
                        () => {
                            this.countdown();
                        },
                    );
                    document.addEventListener('keyup', this.keyupEvent);
                } else {
                    Feedback.toast.error(response.data.msg);
                }
            })
            .catch((error) => {
                Feedback.toast.error('Axios请求失败' + error);
            });
    };

    // 扫码
    payment = () => {
        const { str, stuffName, stuffId } = this.state;
        this.setState({ waitTime: 60 });
        this.field.validate((errors, values) => {
            document.removeEventListener('keyup', this.keyupEvent);
            clearInterval(this.state.setCountdown);
            values.totalFee = this.field.getValue('reallyFee');
            values.type = this.field.getValue('areaName') === '市区' ? '01' : '02';
            values.createTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
            values.payWay = '';
            values.descr = str;
            values.source = '聚合支付';
            values.authCode = this.translation(this.state.code);
            values.id = this.state.paymentId;
            values.createId = stuffId;
            values.createName = stuffName;
            axios({
                method: 'post',
                url: `${url}revenue/order/create`,
                data: qs.stringify(values),
            })
                .then((response) => {
                    if (response.data.code === '0') {
                        Feedback.toast.success('支付成功');
                        let jsondata = response.data;
                        let id = jsondata.datas;
                        let list = [];
                        list.page = 1;
                        list.pageSize = 10;
                        list.id = id;
                        axios({
                            method: 'post',
                            url: `${url}revenue/order/query`,
                            data: qs.stringify(list),
                        })
                            .then((response) => {
                                this.setState({ orderId: jsondata.datas, order: response.data.datas }, () => {
                                    this.submitOrder();
                                });
                            })
                            .catch((error) => {
                                alert('缴费成功,写卡失败,请前往订单管理页面写卡');
                                this.props.history.push('/order/OrderSearch');
                            });
                    } else {
                        Feedback.toast.error('支付失败');
                        this.setState(
                            {
                                visibleOfCode: false,
                            },
                            () => {
                                this.props.history.push('/order/SaleWater');
                            },
                        );
                    }
                })
                .catch((error) => {
                    Feedback.toast.error('Axios请求失败' + error);
                });
        });
    };

    // 检擦购水量
    checkedTunnage(rule, value, callback) {
        if (value) {
            if (value > 999) {
                callback('单次购水量不能超过999立方米');
            } else {
                callback();
            }
        } else {
            callback('必填');
        }
    }

    // 检验购水金额
    checkedPayBiggerFee(rule, value, callback) {
        if (value) {
            let fee = this.field.getValue('fee');
            if (parseFloat(value) < parseFloat(fee)) {
                callback('收款金额必须大于应收金额');
            } else if (value == 0) {
                callback('收款金额不能输入0');
            } else if (value < 0) {
                callback('收款金额不能为负数');
            } else if (isNaN(value)) {
                callback('收款金额必须是数字');
            } else {
                callback();
            }
        } else {
            callback('必填');
        }
    }

    /* 关闭弹窗 */
    onClose() {
        this.setState({ visible: false });
    }

    /* 关闭聚合支付弹窗 */
    onCloseOfCode() {
        this.setState({ visibleOfCode: false, waitTime: 60 }, () => {
            clearInterval(this.state.setCountdown);
        });
    }

    // 如果存入账户，则提示剩余金额不能超过10元
    checkedChangeAmount(rule, value, callback) {
        let clearingWay = this.field.getValue('clearingWay');
        if (clearingWay) {
            if (clearingWay == 2) {
                // 存入账户
                if (value > 10) {
                    callback('剩余金额存入账户不能超过10元');
                } else {
                    callback();
                }
            } else {
                callback();
            }
        }
    }

    // 阶梯渲染
    ladderRender = (value, index) => {
        return '第' + (index + 1) + '阶梯';
    };

    // 渲染订单状态
    renderStatus(value) {
        switch (value) {
            case '0':
                return <span style={{ color: '#ff5711' }}>未完成</span>;
            case '1':
                return <span style={{ color: '#ffa631' }}>已支付未刷卡</span>;
            case '2':
                return <span style={{ color: '#ff0000' }}>退款</span>;
            case '3':
                return <span style={{ color: '#1DC11D' }}>已完成</span>;
            case '4':
                return <span style={{ color: '#ff0000' }}>作废</span>;
            case '5':
                return <span style={{ color: '#ff0000' }}>红冲</span>;
        }
    }

    billingRecord(record) {
        return <BillingRecord record={record} />;
    }

    changePrintType(value) {
        this.field.setValue('printType', value);
        this.field.setValue('isNote', 1);
    }

    copyInvoice = () => {
        const { latestInvoice } = this.state;
        const textArea = document.createElement("textarea");
        textArea.value = latestInvoice;
    
        textArea.style.top = "0";
        textArea.style.left = "0";
        textArea.style.position = "fixed";
    
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
    
        try {
            return document.execCommand('copy');
        } catch (err) {
            return false;
        } finally {
            document.body.removeChild(textArea);
        }
    }

    render() {
        const { init } = this.field;
        let record = this.field.getValues();
        const {
            searchValue,
            ladderDataSource,
            tableDataSource,
            account,
            str,
            visible,
            visibleOfCode,
            bills,
            count,
            ishas,
            payByWater,
            waterByPay,
            purchase,
            waitTime, 
            latestInvoice, 
            latestPrintTime
        } = this.state;
        const buyItemLayout = { labelCol: { fixedSpan: 7 } };
        const footer = (
            <span>
                <Button
                    className="button"
                    onClick={() => this.purchase()}
                    loading={purchase}
                    type="primary"
                    style={{ marginRight: 20 }}
                >
                    确认缴费
                </Button>
                <Button className="button" onClick={() => this.onClose()}>
                    取消
                </Button>
            </span>
        );
        const footerOfKeyUpOfPay = <div />;
        const suffix = <span>{waitTime}s</span>;

        return (
            <>
                <div style={{ marginBottom: 5 }}>
                    <Link to={{ pathname: `/order/SaleWater`, state: { searchValue: searchValue } }}>
                        <Button className="button" type="primary">
                            <FoundationSymbol type="backward" style={{ marginRight: 5 }} size="small" />
                            返回
                        </Button>
                    </Link>
                </div>

                <IceContainer className={styles.basicInformation}>
                    <h5 className={styles.infoColumnTitle}>基本信息</h5>

                    <div className={styles.infoColumn}>
                        <Row wrap className={styles.infoItems}>
                            <Col xxs="24" l="8" className={styles.infoItem}>
                                <span className={styles.infoItemLabel}>用户编号：</span>

                                <span className={styles.infoItemValue}>{record.cno}</span>
                            </Col>

                            <Col xxs="24" l="8" className={styles.infoItem}>
                                <span className={styles.infoItemLabel}>用户名称：</span>

                                <span className={styles.infoItemValue}>{record.cname}</span>
                            </Col>

                            <Col xxs="24" l="8" className={styles.infoItem}>
                                <span className={styles.infoItemLabel}>用水性质：</span>

                                <span className={styles.infoItemValue}>{record.feeName}</span>
                            </Col>

                            <Col xxs="24" l="8" className={styles.infoItem}>
                                <span className={styles.infoItemLabel}>水表种类：</span>

                                <span className={styles.infoItemValue}>{record.watermeterKind}</span>
                            </Col>

                            <Col xxs="24" l="8" className={styles.infoItem}>
                                <span className={styles.infoItemLabel}>水表编号：</span>

                                <span className={styles.infoItemValue}>{record.watermeterId}</span>
                            </Col>

                            <Col xxs="24" l="8" className={styles.infoItem}>
                                <span className={styles.infoItemLabel}>用户卡号：</span>

                                <span className={styles.infoItemValue}>{record.cardNo}</span>
                            </Col>

                            <Col xxs="24" l="8" className={styles.infoItem}>
                                <span className={styles.infoItemLabel}>年购水量(吨)：</span>

                                <span className={styles.infoItemValue}>{record.yearTunnage}</span>
                            </Col>

                            <Col xxs="24" l="8" className={styles.infoItem}>
                                <span className={styles.infoItemLabel}>累计购水量(吨)：</span>

                                <span className={styles.infoItemValue}>{record.totalTunnage}</span>
                            </Col>

                            <Col xxs="24" l="8" className={styles.infoItem}>
                                <span className={styles.infoItemLabel}>累计购水次数：</span>

                                <span className={styles.infoItemValue}>{record.totalTimes}</span>
                            </Col>

                            <Col xxs="24" l="8" className={styles.infoItem}>
                                <span className={styles.infoItemLabel}>用户地址：</span>

                                <span className={styles.infoItemValue}>{record.address}</span>
                            </Col>
                        </Row>
                    </div>
                </IceContainer>

                <IceContainer className={styles.saleWater}>
                    <h5 className={styles.infoColumnTitle}>购水缴费</h5>
                    <Form field={this.field} direction="hoz">
                        <Row>
                            <FormItem label="账户余额：" {...buyItemLayout}>
                                <AccountMoneyRecord value={account + '元'} cno={record.cno} />
                            </FormItem>
                        </Row>

                        <Row>
                            <FormItem label="购买水量（m³）：" {...buyItemLayout}>
                                <Input
                                    {...init('tunnage', {
                                        rules: [
                                            {
                                                validator: (rule, value, callback) =>
                                                    this.checkedTunnage(rule, value, callback),
                                            },
                                        ],
                                    })}
                                    style={style.buyWater}
                                    autoFocus
                                    onChange={(e) => this.resetMoney(e)}
                                />
                                <Button
                                    type="primary"
                                    onClick={() => this.computePriceByWater()}
                                    loading={payByWater}
                                    style={{ borderRadius: '4px', marginLeft: 20, fontSize: 12, height: 24 }}
                                    size="small"
                                >
                                    按水量计算价格
                                </Button>
                            </FormItem>
                            <FormItem label="计算金额（元）：" {...buyItemLayout}>
                                <Input
                                    {...init('amount', { rules: [{ required: true, message: '必填' }] })}
                                    style={style.readOnlyStyle}
                                    readOnly
                                />
                            </FormItem>
                        </Row>

                        <Row>
                            <FormItem label="优惠金额（元）：" {...buyItemLayout}>
                                <Input
                                    {...init('preferentialPrice', {
                                        initValue: 0,
                                        rules: [{ required: true, message: '必填' }],
                                    })}
                                    style={ishas ? style.buyWaterReadonly : style.buyWater}
                                    onChange={(value) => this.changeFeeByPreferential(value)}
                                    readOnly={ishas}
                                />
                            </FormItem>
                            <FormItem label="应收金额（元）：" labelCol={{ fixedSpan: 14 }}>
                                <Input
                                    {...init('fee', { rules: [{ required: true, message: '必填' }] })}
                                    style={style.readOnlyStyle}
                                    readOnly
                                />
                            </FormItem>
                        </Row>

                        <Row>
                            <FormItem label="收款金额（元）：" {...buyItemLayout}>
                                <Input
                                    {...init('pay', {
                                        rules: [
                                            {
                                                validator: (rule, value, callback) =>
                                                    this.checkedPayBiggerFee(rule, value, callback),
                                            },
                                        ],
                                    })}
                                    style={style.buyWater}
                                    htmlType="number"
                                    onChange={(value) => this.computeChangeAmount(value)}
                                />
                                <Button
                                    type="primary"
                                    onClick={() => this.computerWaterByMoney()}
                                    loading={waterByPay}
                                    style={{ borderRadius: '4px', marginLeft: 20, fontSize: 12, height: 24 }}
                                    size="small"
                                >
                                    按收款计算水量
                                </Button>
                            </FormItem>
                            <FormItem label="剩余金额（元）：" {...buyItemLayout}>
                                <Input
                                    {...init('changeAmount', {
                                        rules: [
                                            {
                                                validator: (rule, value, callback) =>
                                                    this.checkedChangeAmount(rule, value, callback),
                                            },
                                        ],
                                    })}
                                    style={style.readOnlyStyle}
                                    readOnly
                                />
                            </FormItem>
                        </Row>

                        <Row>
                            <FormItem label="备&emsp;&emsp;注：" {...buyItemLayout}>
                                <Input {...init('remarks')} style={{ width: 460 }} />
                            </FormItem>
                        </Row>

                        <Row>
                            <FormItem label="付款方式：" {...buyItemLayout}>
                                <RadioGroup
                                    {...init('payWay', { rules: [{ required: true, message: '必填' }] })}
                                    dataSource={[
                                        { value: '1', label: '现金' },
                                        { value: '5', label: '付款码支付' },
                                    ]}
                                    defaultValue="1"
                                />
                            </FormItem>
                        </Row>

                        <Row>
                            <FormItem label="结余方式：" {...buyItemLayout}>
                                <RadioGroup
                                    {...init('clearingWay', { rules: [{ required: true, message: '必填' }] })}
                                    dataSource={[
                                        { value: 1, label: '找零' },
                                        { value: 2, label: '存入账户' },
                                    ]}
                                    onChange={(value) => this.computeReallyFee(value)}
                                    defaultValue={1}
                                />
                            </FormItem>
                        </Row>

                        <Row>
                            <FormItem label="打印方式：" {...buyItemLayout}>
                                <RadioGroup
                                    {...init('printType', { rules: [{ required: true, message: '必填' }] })}
                                    dataSource={[
                                        { value: 0, label: '纸质发票' },
                                        { value: 1, label: '电子发票' },
                                        { value: 2, label: '不打' },
                                    ]}
                                    defaultValue={0}
                                    onChange={(value) => this.changePrintType(value)}
                                />
                            </FormItem>
                        </Row>

                        {this.field.getValue('printType') === 0 ? (
                            <Row>
                                <FormItem label="其他选项：" {...buyItemLayout}>
                                    <RadioGroup
                                        {...init('isNote', { rules: [{ required: true, message: '必填' }] })}
                                        dataSource={[
                                            { value: 1, label: '合并打印' },
                                            { value: 0, label: '清污分离打印' },
                                        ]}
                                        defaultValue={1}
                                    />
                                </FormItem>
                            </Row>
                        ) : (
                            void 0
                        )}

                        {this.field.getValue('printType') === 0 && this.field.getValue('isNote') === 1 ? (
                            <Row>
                                <FormItem label="发票号码：" {...buyItemLayout}>
                                    <Input {...init('invoiceNo', { rules: [{ required: true, message: '必填' }] })} style={{ width: 460 }} />
                                </FormItem>
                            </Row>
                        ) : ('')}
                        
                        {this.field.getValue('printType') === 0 && this.field.getValue('isNote') === 0 ? (
                            <>
                                <Row>
                                    <FormItem label="清水发票号码：" {...buyItemLayout}>
                                        <Input {...init('waterInvoiceNo', { rules: [{ required: true, message: '必填' }] })} style={{ width: 460 }} />
                                    </FormItem>
                                </Row>
                                <Row>
                                    <FormItem label="污水发票号码：" {...buyItemLayout}>
                                        <Input {...init('sewageInvoiceNo', { rules: [{ required: true, message: '必填' }] })} style={{ width: 460 }} />
                                    </FormItem>
                                </Row>
                            </>
                        ) : ('')}

                        <Row>
                            <FormItem label="上次使用发票号码：" {...buyItemLayout} >
                                <Input
                                    value={latestInvoice}
                                    style={style.readOnlyStyle}
                                    readOnly
                                />
                            </FormItem>
                            <Button type="primary" className="button" onClick={() => this.copyInvoice()}>复制发票号码</Button>
                            </Row>

                            <Row>
                            <FormItem label="上次开票时间：" {...buyItemLayout} >
                                <Input
                                    value={latestPrintTime}
                                    style={style.readOnlyStyle}
                                    readOnly
                                />
                            </FormItem>
                        </Row>

                        <Row>
                            <FormItem label="是否计入累计：" {...buyItemLayout}>
                                <RadioGroup
                                    {...init('isCountTunnage', { rules: [{ required: true, message: '必填' }] })}
                                    dataSource={[
                                        { value: 1, label: '是' },
                                        { value: 0, label: '否' },
                                    ]}
                                    defaultValue={1}
                                />
                            </FormItem>
                        </Row>

                        <Row>
                            <FormItem label="实收金额（元）：" {...buyItemLayout}>
                                <Input {...init('reallyFee')} style={style.readOnlyStyle} readOnly />
                            </FormItem>
                        </Row>

                        <Row>
                            <span style={{ marginLeft: 60, color: '#FF0000' }}>缴费明细：{str}</span>
                        </Row>

                        <div align="center" style={{ width: 800, marginTop: 20 }}>
                            <Button type="primary" className="button" onClick={() => this.confirm()}>
                                缴费
                            </Button>
                        </div>
                    </Form>
                </IceContainer>

                <IceContainer className={styles.saleWater}>
                    <h5 className={styles.infoColumnTitle}>阶梯资费</h5>
                    <Table dataSource={ladderDataSource}>
                        <Table.Column title="阶梯" cell={this.ladderRender} align="center" />
                        <Table.Column title="单价（元/m³）" dataIndex="price" align="center" />
                        <Table.Column title="总水量（m³）" dataIndex="total" align="center" />
                        <Table.Column title="使用水量（m³）" dataIndex="used" align="center" />
                        <Table.Column title="剩余水量（m³）" dataIndex="surplus" align="center" />
                    </Table>
                </IceContainer>

                {bills && bills.length > 0 ? (
                    <IceContainer className={styles.saleWater}>
                        <h5 className={styles.infoColumnTitle}>账单记录</h5>
                        <Table dataSource={bills} fixedHeader maxBodyHeight={400}>
                            <Table.Column title="用户编号" dataIndex="cno" align="center" />
                            <Table.Column title="水表编号" dataIndex="watermeterId" align="center" />
                            <Table.Column title="出账日期" dataIndex="createTime" align="center" />
                            <Table.Column title="上期示数" dataIndex="lastNum" align="center" />
                            <Table.Column title="本期示数" dataIndex="thisNum" align="center" />
                            <Table.Column title="结算水量" dataIndex="tunnage" align="center" />
                            <Table.Column title="账单金额" dataIndex="amount" align="center" />
                            <Table.Column title="欠费金额" dataIndex="unpaid" align="center" />
                            <Table.Column
                                title="账单状态"
                                dataIndex="billStatus"
                                align="center"
                                cell={(value) => (value == 0 ? '未结清' : '已结清')}
                            />
                            <Table.Column
                                title="操作"
                                cell={(value, index, record) => this.billingRecord(record)}
                                align="center"
                            />
                        </Table>
                    </IceContainer>
                ) : (
                    void 0
                )}

                <IceContainer className={styles.saleWater}>
                    <h5 className={styles.infoColumnTitle}>订单记录</h5>
                    <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: 10, marginRight: 10 }}>
                        <div>累计金额：{count ? count : 0}元</div>
                    </div>
                    <Table dataSource={tableDataSource} fixedHeader maxBodyHeight={550}>
                        <Table.Column title="用户编号" dataIndex="cno" align="center" />
                        <Table.Column title="水表编号" dataIndex="watermeterId" align="center" />
                        <Table.Column title="水表种类" dataIndex="watermeterKind" align="center" />
                        <Table.Column title="订单来源" dataIndex="source" align="center" />
                        <Table.Column title="用水性质" dataIndex="feeName" align="center" />
                        <Table.Column title="订单水量" dataIndex="tunnage" align="center" />
                        <Table.Column title="实收金额" dataIndex="reallyFee" align="center" />
                        <Table.Column
                            title="订单状态"
                            dataIndex="status"
                            align="center"
                            cell={(value) => this.renderStatus(value)}
                        />
                        <Table.Column title="提交时间" dataIndex="createTime" align="center" />
                        <Table.Column title="操作员" dataIndex="createName" align="center" />
                        <Table.Column
                            title="操作"
                            align="center"
                            cell={(value, index, record) => this.rowOptionRender(record)}
                        />
                    </Table>

                    <div align="center" style={{ marginTop: 20 }}>
                        <Link to={{ pathname: `/order/SaleWater`, state: { searchValue: searchValue } }}>
                            <Button className="button" type="primary">
                                <FoundationSymbol type="backward" style={{ marginRight: 5 }} size="small" />
                                返回
                            </Button>
                        </Link>
                    </div>
                </IceContainer>

                <Dialog
                    visible={visible}
                    onClose={() => this.onClose()}
                    title="缴费"
                    footer={footer}
                    footerAlign="center"
                    style={{ width: 700 }}
                >
                    <div style={{ fontWeight: 600, marginTop: 5, marginBottom: 20 }}>请核对收款信息后，提交订单</div>
                    <Form direction="hoz">
                        <Row>
                            <FormItem label="付款方式：" {...buyItemLayout}>
                                <Radio defaultChecked>
                                    <span className="next-form-text-align">
                                        {this.field.getValue('payWay') == '1' ? '现金' : '刷卡'}
                                    </span>
                                </Radio>
                            </FormItem>
                            <FormItem label="是否计入累计：" {...buyItemLayout}>
                                <Radio defaultChecked>
                                    <span className="next-form-text-align">
                                        {this.field.getValue('isCountTunnage') === 1 ? '是' : '否'}
                                    </span>
                                </Radio>
                            </FormItem>
                            <FormItem label="结余方式：" {...buyItemLayout}>
                                <Radio defaultChecked>
                                    <span style={{ color: 'red' }} className="next-form-text-align">
                                        {this.field.getValue('clearingWay') == 1 ? '找零' : '存入账户'}
                                    </span>
                                </Radio>
                            </FormItem>
                        </Row>
                        <Row>
                            <FormItem label="购买水量（m³）：" {...buyItemLayout} style={{ color: 'red' }}>
                                <Input
                                    value={this.field.getValue('tunnage')}
                                    style={styles.confimSpecialInput}
                                    readOnly
                                />
                            </FormItem>
                            <FormItem label="计算金额（元）：" {...buyItemLayout}>
                                <Input value={this.field.getValue('amount')} style={styles.confimInput} readOnly />
                            </FormItem>
                        </Row>
                        <Row>
                            <FormItem label="优惠金额（元）：" {...buyItemLayout}>
                                <Input
                                    value={this.field.getValue('preferentialPrice')}
                                    style={styles.confimInput}
                                    readOnly
                                />
                            </FormItem>
                            <FormItem label="应收金额（元）：" {...buyItemLayout}>
                                <Input value={this.field.getValue('fee')} style={styles.confimInput} readOnly />
                            </FormItem>
                        </Row>
                        <Row>
                            <FormItem label="收款金额（元）：" {...buyItemLayout} style={{ color: 'red' }}>
                                <Input value={this.field.getValue('pay')} style={styles.confimSpecialInput} readOnly />
                            </FormItem>
                            <FormItem label="剩余金额（元）：" {...buyItemLayout}>
                                <Input
                                    value={this.field.getValue('changeAmount')}
                                    style={styles.confimInput}
                                    readOnly
                                />
                            </FormItem>
                        </Row>
                        <Row>
                            <FormItem label="实收金额（元）：" {...buyItemLayout} style={{ color: 'red' }}>
                                <Input
                                    value={this.field.getValue('reallyFee')}
                                    style={styles.confimSpecialInput}
                                    readOnly
                                />
                            </FormItem>
                        </Row>
                    </Form>
                </Dialog>

                <Dialog
                    visible={visibleOfCode}
                    onClose={() => this.onCloseOfCode()}
                    title="等待用户扫码"
                    footerAlign="center"
                    footer={footerOfKeyUpOfPay}
                    autoFocus={false}
                    style={{ width: '20%' }}
                >
                    <div style={{ display: 'grid', placeItems: 'center', textAlign: 'center' }}>
                        <Progress percent={waitTime} suffix={suffix} shape="circle" animation={false} />
                    </div>
                </Dialog>
            </>
        );
    }
}
const style = {
    inputStyle: {
        width: 160,
        border: 0,
    },
    buyWater: {
        width: 150,
    },
    buyWaterReadonly: {
        width: 150,
        backgroundColor: '#E6E6E6',
    },
    buyRecord: {
        width: 100,
        border: 0,
    },
    readOnlyStyle: {
        width: 150,
        backgroundColor: '#E6E6E6',
    },
    confimInput: {
        width: 120,
        color: 'red',
    },
    confimSpecialInput: {
        width: 120,
        backgroundColor: '#F3DEDF',
    },
};
