import React, {Component} from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog,
  Feedback,
  Field,
  Form,
  Grid,
  Input,
  moment,
  Progress,
  Radio,
  Table
} from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import qs from 'qs';
import {url} from '../../../../components/URL'
import {Link} from 'react-router-dom'
import AccountMoneyRecord from './AccountMoneyRecord';
import PaymentRecords from '../../../User/UserSearch/components/billingRecord';
import {LoginURL} from '../../../../components/URL/LoginURL';
import getLodop from "../../../../common/LodopFuncs";
import ViewOrderDetail from '../../../../common/ViewOrderDetail';
import FoundationSymbol from 'foundation-symbol';
import style from './PaymentDetailCardWater.module.scss'

const {Row, Col} = Grid;
const FormItem = Form.Item;
const { Group: RadioGroup } = Radio;
var LODOP = getLodop();

let lastTime = 0;

let floatObj = function () {
  /*
   * 判断obj是否为一个整数
   */
  function isInteger(obj) {
    return Math.floor(obj) === obj
  }

  /*
   * 将一个浮点数转成整数，返回整数和倍数。如 3.14 >> 314，倍数是 100
   * @param floatNum {number} 小数
   * @return {object}
   *   {times:100, num: 314}
   */
  function toInteger(floatNum) {
    var ret = { times: 1, num: 0 };
    if (isInteger(floatNum)) {
      ret.num = floatNum;
      return ret
    }
    var strfi = floatNum + '';
    var dotPos = strfi.indexOf('.');
    var len = strfi.substr(dotPos + 1).length;
    var times = Math.pow(10, len);
    var intNum = parseInt(floatNum * times + 0.5, 10);
    ret.times = times;
    ret.num = intNum;
    return ret
  }

  /*
   * 核心方法，实现加减乘除运算，确保不丢失精度
   * 思路：把小数放大为整数（乘），进行算术运算，再缩小为小数（除）
   *
   * @param a {number} 运算数1
   * @param b {number} 运算数2
   * @param op {string} 运算类型，有加减乘除（add/subtract/multiply/divide）
   *
   */
  function operation(a, b, op) {
    var o1 = toInteger(a);
    var o2 = toInteger(b);
    var n1 = o1.num;
    var n2 = o2.num;
    var t1 = o1.times;
    var t2 = o2.times;
    var max = t1 > t2 ? t1 : t2;
    var result = null;
    switch (op) {
      case 'add':
        if (t1 === t2) { // 两个小数位数相同
          result = n1 + n2
        } else if (t1 > t2) { // o1 小数位 大于 o2
          result = n1 + n2 * (t1 / t2)
        } else { // o1 小数位 小于 o2
          result = n1 * (t2 / t1) + n2
        }
        return result / max;
      case 'subtract':
        if (t1 === t2) {
          result = n1 - n2
        } else if (t1 > t2) {
          result = n1 - n2 * (t1 / t2)
        } else {
          result = n1 * (t2 / t1) - n2
        }
        return result / max;
      case 'multiply':
        result = (n1 * n2) / (t1 * t2);
        return result;
      case 'divide':
        result = (n1 / n2) * (t2 / t1);
        return result
    }
  }

  // 加减乘除的四个接口
  function add(a, b) {
    return operation(a, b, 'add')
  }

  function subtract(a, b) {
    return operation(a, b, 'subtract')
  }

  function multiply(a, b) {
    return operation(a, b, 'multiply')
  }

  function divide(a, b) {
    return operation(a, b, 'divide')
  }

  // exports
  return {
    add: add,
    subtract: subtract,
    multiply: multiply,
    divide: divide
  }
}();

export default class PaymentDetailMachineryMeterWater extends Component {

  constructor(props) {
    super(props);
    this.state = {
      tableDataSource: [],
      orderTableSource: [],
      purchase: false,
      order: [],
      invoiceNo: '',
      invoiceCode: '',
      stuffId: sessionStorage.getItem("stuffId"),
      stuffName: sessionStorage.getItem("realName"),
      account: 0,// 账户余额
      oweMoney: 0,// 欠费金额
      visible: false,
      billIds: [],
      clearingWayList: [],
      unpaidList: [],
      shouldMoney: 0,
      ishas: true,
      loginURL: LoginURL,
      searchValue: {},
      readOnly: true,
      count: 0,
      visibleOfCode: false, // 支付码支付
      waitTime: 60,
      code: '',
      paymentId: null,
      setCountdown: null,
      latestInvoice: null,
      latestPrintTime: null
    }
    this.field = new Field(this, { autoUnmount: true });
  }

  componentWillMount() {
    const param = this.props.location.state;
    if (param == undefined) {
      this.props.history.push('/order/SaleWater');
      return;
    }
    const record = param.record;
    this.setState({
      account: record.account,
      searchValue: param.searchValue,
    })
    this.field.setValues({ ...record });
    this.field.setValue('printType', 0)
    this.getMenuByRole();
    this.getTableDataSource();
    this.getLatestInvoice();
  }

  // 获取上次发票信息
  getLatestInvoice() {
    const printId = sessionStorage.getItem('stuffId');
    // const printId = "952e75e3f285433abe97a78432224916";
    axios.post(`${url}revenue/invoiceRecord/getLastInvoiceNo`,
        {printId: printId}).then((response) => {
      this.setState({latestInvoice: response.data.datas?.invoiceNo});
      this.setState({latestPrintTime: response.data.datas?.printTime});
    })
  }

  //获取权限
  getMenuByRole() {
    const { loginURL } = this.state;
    axios.get(loginURL + '/getAuthority', { withCredentials: true })
      .then((response) => {
        let jsondata = response.data;
        if (jsondata.statusCode == 0) {
          let ishas = true;
          for (let i = 0; i < jsondata.datas.length; i++) {
            if (jsondata.datas[i].menuPath == '/order/SaleWater' && jsondata.datas[i].buttonCode == 'preferentialPrice') {
              ishas = false;
              break;
            }
          }
          this.setState({ ishas: ishas });
        }
      })
  }

  //获取账单列表
  getTableDataSource() {
    let cno = this.field.getValue('cno');
    let value = {};
    value.cno = cno;
    axios({
      method: 'post',
      url: `${url}revenue/user/view`,
      data: qs.stringify(value)
    })
      .then(response => {
        let jsondata = response.data.datas;
        let orders = jsondata.orders
        let count = 0
        orders.map((item, key) => {
          if (item.status != '2' && item.status != '4') {
            count += Number(item.reallyFee)
          }
        })
        this.setState({
          tableDataSource: jsondata.bills,
          orderTableSource: orders,
          unpaidList: jsondata.unpaidList,
          count: count.toFixed(2)
        })
        //计算欠费金额和统计没有付清的账单ids
        let temp = 0;
        let unpaidBills = [];
        jsondata.bills.map((item) => {
          temp = parseFloat(temp) + parseFloat(item.unpaid);
          if (item.billStatus == 0) {
            unpaidBills.push(item.id);
          }
        });

        let leaveWay = [{ value: 2, label: "存入账户" }];

        if (temp > 0) {
          leaveWay = [
            { value: 2, label: "存入账户" },
            { value: 1, label: "找零" }
          ];
        }
        //设置欠费金额
        this.setState({
          oweMoney: temp.toFixed(2),
          billIds: unpaidBills,
          clearingWayList: leaveWay,
          readOnly: false
        });

        //计算应收金额: 欠费金额-账户余额-优惠金额+滞纳金
        let lateAmount1 = this.field.getValue('lateAmount');
        let { account } = this.state;

        let fee = parseFloat(temp) - parseFloat(account) + parseFloat(lateAmount1);
        //设置应收金额
        if (fee > 0) {
          this.field.setValue('fee', fee.toFixed(2));
        } else {
          this.field.setValue('fee', 0);
        }
        this.setState({
          shouldMoney: fee.toFixed(2),
        })
      })
      .catch(error => {
        Feedback.toast.error('Axios请求失败' + error);
      })
  }

  //缴费成功自动开阀
  openValveMs(msWatermeterList) {
    const { stuffId, stuffName } = this.state;
    let values = {};
    values.valve = "1";
    values.createName = stuffName;
    values.createId = stuffId;
    values.minShengRemoteWatermeters = msWatermeterList;
    axios({
      method: 'post',
      url: `${url}revenue/minsheng/remoteWatermeter/switchValve`,
      data: values
    }).then(response => {
      let jsondata = response.data;
      if (jsondata.code != '0') {
        alert('开阀命令下达失败，请前往远传表管理进行【开阀】')
      }
    })
  }

  //缴费成功自动开阀
  openValve(yzWatermeterList) {
    const { stuffId, stuffName } = this.state;
    let values = {};
    values.valve = "1";
    values.createName = stuffName;
    values.createId = stuffId;
    values.remoteWatermeters = yzWatermeterList;
    axios({
      method: 'post',
      url: `${url}revenue/remoteWatermeter/switchValve`,
      data: values
    }).then(response => {
      let jsondata = response.data;
      if (jsondata.code != '0') {
        alert('开阀命令下达失败，请前往远传表管理进行【开阀】')
      }
    })
  }

  //创建订单后找到同户下所有水表开阀
  autoOpen() {
    axios.get(`${url}revenue/user/findToOpenWatermetersByCno`, { params: { cno: this.field.getValue('cno') } })
      .then((response) => {
        let watermeterList = response.data.datas
        let yzWatermeterList = []
        let msWatermeterList = []
        watermeterList.map((item) => {
          if (item.watermeterCompany == '辽宁民生') {
            msWatermeterList.push(item)
          } else {
            yzWatermeterList.push(item)
          }
        })
        if (msWatermeterList.length == 0 && yzWatermeterList.length != 0) {
          this.openValve(yzWatermeterList);
        } else if (yzWatermeterList.length == 0 && msWatermeterList.length != 0) {
          this.openValveMs(msWatermeterList);
        } else if (yzWatermeterList.length != 0 && msWatermeterList.length != 0) {
          this.openValve(yzWatermeterList);
          this.openValveMs(msWatermeterList);
        }
      }).catch((error) => {
        alert("开阀没有成功请手动开阀！！");
      })
  }

  //交完费判断是否打印
  printPaperInvoice(data) {
    let id = data
    let list = []
    list.page = 1
    list.pageSize = 10
    list.id = id
    axios.post(`${url}revenue/order/query`, qs.stringify(list)).then((response) => {
      this.setState({ order: response.data.datas })
      //查询发票单价
      axios.get(`${url}revenue/price/findPriceAndSewageFeeAndWaterSourceFee`, { params: { cno: this.field.getValue('cno') } })
        .then(response => {
          if (response.data.code == "0") {
            let price = response.data.datas
            //判断合打还是分打
            let isNote = this.field.getValue('isNote')
            if (isNote == '1') {
              this.printAll(price)
            } else if (isNote == '0') {
              this.printWaterTotalFee(price)
            } else {
              //跳转到缴费列表页面
              this.props.history.push('/order/SaleWater');
            }
            alert('缴费成功');
            this.setState({ visible: false, visibleOfCode: false });
          } else {
            //跳转到缴费列表页面
            alert('查询阶梯价格失败请重新缴费')
            this.props.history.push('/order/SaleWater');
            Feedback.toast.error(response.data.msg);
          }
        }).catch(error => {
          //跳转到缴费列表页面
          alert('查询阶梯价格失败请重新缴费')
          this.props.history.push('/order/SaleWater');
        })
    }).catch((error) => {
      alert('缴费成功,打印失败,请重新打印')
      this.props.history.push('/order/OrderSearch');
    })
  }

  //缴费
  purchase() {
    this.field.validate((errors, values) => {
      if (!errors) {
        this.setState({ purchase: true })
        //增加计算金额
        const { account, stuffId, stuffName, oweMoney, billIds } = this.state;
        values.createId = stuffId;
        values.createName = stuffName;
        values.totalArrearage = oweMoney;
        values.account = account;
        values.billIds = billIds;
        values.source = '柜台';
        values.isTax = 0;
        values.isNote = 2;
        if (values.printType === 0) {
          values.kplx = 3;
        } else if (values.printType === 1) {
          values.kplx = 1;
        }
        if (values)
          axios.post(`${url}revenue/order/create`, qs.stringify(values))
            .then(response => {
              let jsondata = response.data
              if (jsondata.code == '0') {
                //缴费成功查询用户下远传表循环自动开阀
                this.autoOpen(jsondata.datas)
                //判断是否打印发票
                if (values.printType === 0) {
                  this.printPaperInvoice(jsondata.datas)
                } else {
                  alert('缴费成功');
                  this.props.history.push('/order/SaleWater');
                }
              } else {
                alert(jsondata.msg)
                this.setState({ purchase: false });
              }
            }).catch(error => {
              this.setState({ purchase: false })
              Feedback.toast.error('网络错误请刷新页面重试')
            })
      }
    })
  }

  //总水费
  printAll(price) {
    LODOP = getLodop();
    const { stuffId, stuffName, order } = this.state;
    let record = order[0];
    axios({
      method: 'post',
      url: `${url}revenue/printTemplate/get`,
      data: qs.stringify({ templateName: '机械表账单发票' }),
    }).then(response => {
      let ajaxData = response.data;
      if (ajaxData.code == "0") {
        //模板数据
        let templateCode = ajaxData.datas.templateCode
        //打印记录所需参数
        let values = this.field.getValues()
        values.printName = stuffName
        values.printId = stuffId
        values.createName = record.createName
        values.createId = record.createId
        values.createTime = moment(record.createTime).format('YYYY-MM-DD HH:mm:ss')
        values.invoiceType = '0'
        values.printType = '0'
        values.orderId = record.id
        values.cno = record.cno
        values.cname = record.cname
        values.waterTotalFee = record.waterTotalFee
        values.sewageTotalFee = record.sewageTotalFee
        values.waterSourceFee = record.waterSourceFee
        values.reallyFee = record.reallyFee
        values.tunnage = record.tunnage
        values.lastTunnage = record.totalTunnage
        values.invoiceNo = this.field.getValue('invoiceNo')
        values.invoiceCode = ''
        let priceInvoice = ''
        if (price.prices.split('\\n').length > 2) {
          priceInvoice = (price.prices.substring(0, price.prices.lastIndexOf('\\n'))).replace('\\n', '-').replace('\\n', '-')
        } else {
          priceInvoice = price.prices.substring(0, price.prices.lastIndexOf('\\n'))
        }
        let temp = templateCode.replace("日期", moment(new Date()).format('YYYY-MM-DD'))
          .replace("用户编号", record.cno)
          .replace("用户名", record.cname)
          .replace("应收金额1", record.reallyFee)
          .replace("现示数1", '')
          .replace("水量1", '')
          .replace("清水单价", priceInvoice)
          .replace("污水单价", record.sewageTotalFee)
          .replace("水资源费1", record.sourceTotalFee)
          .replace("操作员", stuffName)
          .replace("日期1", moment(new Date()).format('YYYY-MM-DD'))
          .replace("用户编号1", record.cno)
          .replace("用户名1", record.cname)
          .replace("应收金额2", record.reallyFee)
          .replace("前示数1", '')
          .replace("现示数2", '')
          .replace("水量2", '')
          .replace("清水单价1", priceInvoice)
          .replace("污水单价1", record.sewageTotalFee)
          .replace("操作员1", stuffName)
          .replace("水资源费2", record.sourceTotalFee)
          .replace("制表人1", '')
        eval(temp);
        let flag = LODOP.PRINT()
        console.log(values);
        if (flag) {
          axios({
            method: 'post',
            url: `${url}revenue/invoiceRecord/save`,
            data: qs.stringify(values),
          }).then(response => {
            if (response.data.code == "0") {
              //返回列表页
              this.props.history.push('/order/SaleWater');
            } else {
              alert('打印失败,请补打发票');
              this.props.history.push('/order/OrderSearch')
            }
          }).catch(error => {
            alert('打印失败,请补打发票');
            this.props.history.push('/order/OrderSearch')
          })
        } else {
          alert('打印失败,请补打发票');
          this.props.history.push('/order/OrderSearch')
        }
      }
    }).catch(error => {
      this.props.history.push('/order/OrderSearch')
      alert('打印失败,请补打发票');
    })
  }

  //清水费
  printWaterTotalFee(price) {
    LODOP = getLodop()
    const { stuffId, stuffName, order } = this.state
    let record = order[0]
    axios({
      method: 'post',
      url: `${url}revenue/printTemplate/get`,
      data: qs.stringify({ templateName: '机械表账单发票' }),
    }).then(response => {
      let ajaxData = response.data;
      if (ajaxData.code == "0") {
        //模板数据
        let templateCode = ajaxData.datas.templateCode
        //打印记录所需参数
        let values = this.field.getValues()
        values.printName = stuffName
        values.printId = stuffId
        values.createName = record.createName
        values.createId = record.createId
        values.createTime = moment(record.createTime).format('YYYY-MM-DD HH:mm:ss')
        values.invoiceType = '1'
        values.printType = '1'
        values.orderId = record.id
        values.cno = record.cno
        values.cname = record.cname
        values.waterTotalFee = record.waterTotalFee
        values.sewageTotalFee = record.sewageTotalFee
        values.waterSourceFee = record.waterSourceFee
        values.reallyFee = record.reallyFee
        values.tunnage = record.tunnage
        values.lastTunnage = record.totalTunnage
        values.invoiceNo = this.field.getValue('waterInvoiceNo')
        values.invoiceCode = ''
        let priceInvoice = ''
        if (price.prices.split('\\n').length > 2) {
          priceInvoice = (price.prices.substring(0, price.prices.lastIndexOf('\\n'))).replace('\\n', '-').replace('\\n', '-')
        } else {
          priceInvoice = price.prices.substring(0, price.prices.lastIndexOf('\\n'))
        }
        let realFee = (Number(record.reallyFee) - Number(record.sewageTotalFee)).toFixed(2)
        let temp = templateCode.replace("日期", moment(new Date()).format('YYYY-MM-DD'))
          .replace("用户编号", record.cno)
          .replace("用户名", record.cname)
          .replace("应收金额1", realFee)
          .replace("现示数1", '')
          .replace("水量1", '')
          .replace("清水单价", priceInvoice)
          .replace("污水单价", '')
          .replace("水资源费1", record.sourceTotalFee)
          .replace("操作员", stuffName)
          .replace("日期1", moment(new Date()).format('YYYY-MM-DD'))
          .replace("用户编号1", record.cno)
          .replace("用户名1", record.cname)
          .replace("应收金额2", realFee)
          .replace("前示数1", '')
          .replace("现示数2", '')
          .replace("水量2", '')
          .replace("清水单价1", priceInvoice)
          .replace("污水单价1", '')
          .replace("操作员1", stuffName)
          .replace("水资源费2", record.sourceTotalFee)
          .replace("制表人1", '')
        eval(temp);
        let flag = LODOP.PRINT()
        if (flag) {
          axios({
            method: 'post',
            url: `${url}revenue/invoiceRecord/save`,
            data: qs.stringify(values),
          }).then(response => {
            console.log('清水', response.data)
            if (response.data.code == "0") {
              this.printSewageTotalFee(priceInvoice, templateCode)
            } else {
              alert('打印失败,请补打发票');
              this.props.history.push('/order/OrderSearch')
            }
          }).catch(error => {
            alert('打印失败,请补打发票');
            this.props.history.push('/order/OrderSearch')
          })
        } else {
          alert('打印失败,请补打发票');
          this.props.history.push('/order/OrderSearch')
        }
      }
    }).catch(error => {
      alert('打印失败,请补打发票');
      this.props.history.push('/order/OrderSearch')
    })
  }

  //污水费
  printSewageTotalFee(priceInvoice, templateCode) {
    const { stuffId, stuffName, order } = this.state
    let record = order[0]
    LODOP = getLodop();
    let printId = stuffId
    //打印记录所需参数
    let values = this.field.getValues()
    values.printName = stuffName
    values.printId = printId
    values.createName = record.createName
    values.createId = record.createId
    values.createTime = moment(record.createTime).format('YYYY-MM-DD HH:mm:ss')
    values.invoiceType = '2'
    values.printType = '1'
    values.orderId = record.id
    values.cno = record.cno
    values.cname = record.cname
    values.waterTotalFee = record.waterTotalFee
    values.sewageTotalFee = record.sewageTotalFee
    values.waterSourceFee = record.waterSourceFee
    values.reallyFee = record.reallyFee
    values.tunnage = record.tunnage
    values.lastTunnage = record.totalTunnage
    values.invoiceNo = this.field.getValue('sewageInvoiceNo')
    values.invoiceCode = ''
    let temp = templateCode.replace("日期", moment(new Date()).format('YYYY-MM-DD'))
      .replace("用户编号", record.cno)
      .replace("用户名", record.cname)
      .replace("应收金额1", Number(record.sewageTotalFee))
      .replace("现示数1", '')
      .replace("水量1", '')
      .replace("清水单价", '')
      .replace("污水单价", record.sewageFee)
      .replace("水资源费1", '')
      .replace("操作员", stuffName)
      .replace("日期1", moment(new Date()).format('YYYY-MM-DD'))
      .replace("用户编号1", record.cno)
      .replace("用户名1", record.cname)
      .replace("应收金额2", record.sewageTotalFee)
      .replace("前示数1", '')
      .replace("现示数2", '')
      .replace("水量2", '')
      .replace("清水单价1", '')
      .replace("污水单价1", record.sewageFee)
      .replace("操作员1", stuffName)
      .replace("水资源费2", '')
      .replace("制表人1", '')
    eval(temp);
    let flag = LODOP.PRINT()
    if (flag) {
      console.log(values);
      axios({
        method: 'post',
        url: `${url}revenue/invoiceRecord/save`,
        data: qs.stringify(values),
      }).then(response => {
        if (response.data.code == "0") {
          //返回列表页
          this.props.history.push('/order/SaleWater');
        } else {
          alert('打印失败,请补打发票');
          this.props.history.push('/order/OrderSearch')
        }
      }).catch(error => {
        alert('打印失败,请补打发票');
        this.props.history.push('/order/OrderSearch')
      })
    } else {
      alert('打印失败,请补打发票');
      this.props.history.push('/order/OrderSearch')
    }
  }

  //根据优惠金额计算应收金额
  changeFeeByPreferential = (value) => {
    //账户余额
    const { account, oweMoney } = this.state;
    if (value) {
      //设置优惠金额
      this.field.setValue('preferentialPrice', value);

      //计算应收金额: 欠费金额-账户余额-优惠金额+滞纳金
      let lateAmount = this.field.getValue('lateAmount');
      let fee1 = parseFloat(oweMoney) - parseFloat(account) - parseFloat(value) + parseFloat(lateAmount);
      if (fee1 > 0) {
        this.field.setValue('fee', fee1.toFixed(2));
      } else {
        this.field.setValue('fee', 0);
      }
      this.setState({
        shouldMoney: fee1.toFixed(2),
      })
      //计算剩余金额
      let pay1 = this.field.getValue('pay');
      if (pay1) {
        let changeAmount1 = parseFloat(pay1) - parseFloat(fee1);
        this.field.setValue('changeAmount', changeAmount1.toFixed(2));
        //计算实收金额
        let clearingWay1 = this.field.getValue('clearingWay');
        if (clearingWay1) {
          if (clearingWay1 == 1) {//找零
            let reallyFee1 = fee1;
            this.field.setValue('reallyFee', reallyFee1.toFixed(2));
          } else if (clearingWay1 == 2) {//存入账户
            let reallyFee1 = parseFloat(pay1);
            this.field.setValue('reallyFee', reallyFee1.toFixed(2));
          }
        } else {
          this.field.reset('reallyFee');
        }
      }
    } else {
      this.field.reset('preferentialPrice');
      let lateAmount1 = this.field.getValue('lateAmount');
      let fee1 = parseFloat(oweMoney) - parseFloat(account) + parseFloat(lateAmount1);
      this.field.setValue('fee', fee1.toFixed(2));
    }
  }

  //根据收款金额计算剩余金额
  computeChangeAmount = (value) => {
    if (value) {
      this.field.setValue('pay', value);
      //应收金额
      const { shouldMoney, unpaidList } = this.state;
      let fee2 = shouldMoney;
      if (fee2 != null || fee2 != undefined) {
        let changeAmount1 = parseFloat(value) - parseFloat(fee2);
        //设置剩余金额parseFloat
        this.field.setValue('changeAmount', changeAmount1.toFixed(2));
        //计算实收金额
        let clearingWay1 = this.field.getValue('clearingWay');
        if (clearingWay1) {
          if (clearingWay1 == 1) {//找零
            let reallyFee1 = parseFloat(fee2);
            this.field.setValue('reallyFee', reallyFee1.toFixed(2));
          } else if (clearingWay1 == 2) {//存入账户
            let reallyFee1 = parseFloat(value);
            this.field.setValue('reallyFee', reallyFee1.toFixed(2));
          }
        } else {
          this.field.reset('reallyFee');
        }
      }
    } else {
      this.field.reset('pay');
      this.field.reset('changeAmount');
      this.field.reset('reallyFee');
    }
  }

  //根据结余方式计算实收金额
  computeReallyFee = (value) => {
    this.field.setValue('clearingWay', value);
    //收款金额
    let pay1 = this.field.getValue('pay');
    if (pay1) {
      if (value == 2) { // 存入账户
        this.field.setValue('reallyFee', pay1);
      } else if (value == 1) { //找零
        //剩余金额
        let changeAmount1 = this.field.getValue('changeAmount');
        if (changeAmount1) {
          let reallyFee1 = parseFloat(pay1) - parseFloat(changeAmount1);
          this.field.setValue('reallyFee', reallyFee1.toFixed(2));
        }
      }
    }
  }

  rowOption = (value, index, record) => {
    return (
      <PaymentRecords record={record} />
    )
  }

  keyupEvent = (e) => {
    let sw = true;
    if (sw) {
      let currCode = e.which;
      let currTime = new Date().getTime();
      console.log('code1', currCode);
      // 判断排除人为误操作按键被插入付款码
      if (e.keyCode >= 48 && e.keyCode <= 57) { // 键盘 0 ~ 9 按键取值
        if (lastTime > 0) {
          if (currTime - lastTime <= 1000) { // 按键时间间隔小于1秒取值
            this.setState({
              code: this.state.code += String.fromCharCode(currCode)
            }, () => {
              console.log('code2', this.state.code);
            })
          } else {
            this.clearCode(); // 超时清空
          }
        } else { // 第一次按键，取支付码第一位的值
          this.setState({
            code: String.fromCharCode(currCode)
          })
        }
      }
      lastTime = currTime;
      if (currCode == 13) {
        sw = false;
        this.countdown();
        this.payment();
        // 回车输入后清空
        this.clearCode();
      }
    }
  }

  countdown = () => {
    this.setState({
      setCountdown: setInterval(() => {
        if (this.state.waitTime > 0) {
          this.setState({
            waitTime: this.state.waitTime - 1
          })
        } else if (this.state.waitTime == 0) {
          Feedback.toast.error('已超时')
          clearInterval(this.state.setCountdown)
          this.setState({
            visibleOfCode: false,
            waitTime: 60
          }, () => {
            document.removeEventListener('keyup', this.keyupEvent)
          })
          return;
        }
      }, 1000)
    })
  }

  clearCode = () => {
    lastTime = 0
    this.setState({
      code: '',
    })
  }

  translation(s) {
    // 去掉转义字符
    s = s.replace(/[\'\"\\\/\b\f\n\r\t]/g, '');
    // 去掉特殊字符
    s = s.replace(/[\@\#\$\%\^\&\*\(\)\{\}\:\\L\<\>\?\[\]]/);
    return s;
  };

  //点击缴费按钮，弹出确认框
  confirm() {
    const values = this.field.getValues();
    const isNote = values.isNote;
    const waterInvoiceNo = values.waterInvoiceNo;
    const sewageInvoiceNo = values.sewageInvoiceNo;

    if(isNote === 0 && waterInvoiceNo === sewageInvoiceNo){
      alert('清水发票号码和污水发票号码不能相同！');
    }else{
      axios.post(`${url}revenue/invoiceRecord/checkIfUsed`,
        {invoiceNo: values.invoiceNo, waterInvoiceNo: values.waterInvoiceNo, sewageInvoiceNo: values.sewageInvoiceNo }
      ).then((response) => {
        if (response.data.code === '1') {
          alert(response.data.msg);
        } else {
          this.field.validate((errors, values) => {
            if (errors) {
              return
            } else {
              let param = {}
              param.cno = values.cno
              param.reallyFee = values.reallyFee
              param.isNote = values.isNote
              
              axios.post(`${url}revenue/order/check`, qs.stringify(param))
                .then((res) => {
                  if (res.data.code === '0') {
                    if (values.payWay == '5') {
                      this.verification();
                    } else {
                          this.setState({visible: true})
                    }
                  } else {
                    let last = floatObj.subtract(Number(values.reallyFee), Number(res.data.datas))
                    alert("输入的收款金额所购吨数不为整吨,应输入金额为：" + last + "元")
                  }
                }).catch(() => {
                  Feedback.toast.error("网络连接错误!请刷新页面重试");
                })
            }
          })
        }
      });
    }
  }

  // 验证
  verification = () => {
    let value = {};
    value.cno = this.field.getValue('cno');
    value.hno = this.field.getValue('hno');
    value.totalFee = this.field.getValue('reallyFee');
    value.type = this.field.getValue('areaName') === '市区' ? '01' : '02';
    value.createTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
    axios({
      method: 'post',
      url: `${url}revenue/order/unifiedPay`,
      data: value
    })
      .then(response => {
        if (response.data.code === '0') {
          this.setState({
            paymentId: response.data.datas,
            visibleOfCode: true
          }, () => {
            this.countdown();
          })
          document.addEventListener('keyup', this.keyupEvent)
        } else {
          Feedback.toast.error(response.data.msg)
        }
      })
      .catch(error => {
        Feedback.toast.error('Axios请求失败' + error);
      })
  }

  // 扫码
  payment = () => {
    this.setState({ waitTime: 60 })
    this.field.validate((errors, values) => {
      clearInterval(this.state.setCountdown)
      document.removeEventListener('keyup', this.keyupEvent)
      const { account, stuffId, stuffName, oweMoney, billIds } = this.state;
      values.createId = stuffId;
      values.createName = stuffName;
      values.totalArrearage = oweMoney;
      values.account = account;
      values.billIds = billIds;
      values.isTax = 0;
      values.isNote = 2;
      if (values.printType === 0) {
        values.kplx = 3;
      } else if (values.printType === 1) {
        values.kplx = 1;
      }
      values.totalFee = this.field.getValue('reallyFee');
      values.type = this.field.getValue('areaName') === '市区' ? '01' : '02';
      values.createTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
      values.payWay = '';
      values.source = '聚合支付';
      values.authCode = this.translation(this.state.code);
      values.id = this.state.paymentId;
      axios({
        method: 'post',
        url: `${url}revenue/order/create`,
        data: qs.stringify(values)
      })
        .then(response => {
          if (response.data.code === '0') {
            Feedback.toast.success('支付成功')
            //判断是否打印发票
            if (values.printType === 0) {
              this.printPaperInvoice(response.data.datas)
            } else {
              alert('缴费成功');
              this.props.history.push('/order/SaleWater');
            }
          } else {
            Feedback.toast.error(response.data.msg)
            this.setState({
              visibleOfCode: false
            }, () => {
              this.props.history.push('/order/SaleWater');
            })
          }
        })
        .catch(error => {
          Feedback.toast.error('Axios请求失败' + error);
        })
    })
  }

  checkedPay = (rule, value, callback) => {
    if (value) {
      let fee = this.state.oweMoney
      if (parseFloat(value) < parseFloat(fee)) {
        callback('收款金额必须大于欠费金额');
      } else if (value > 1000000) {
        callback('单次付款金额不能大于1000,000')
      } else if (value == 0) {
        callback('收款金额不能输入0')
      } else if (value < 0) {
        callback('收款金额不能为负数')
      } else if (isNaN(value)) {
        callback('收款金额必须是数字')
      } else {
        callback();
      }
    } else {
      callback('必填')
    }
  }

  /*关闭弹窗*/
  onClose() {
    this.setState({ visible: false })
  }

  /*关闭聚合支付弹窗*/
  onCloseOfCode() {
    this.setState({ visibleOfCode: false, waitTime: 60 },
      () => {
        clearInterval(this.state.setCountdown)
      })
  }

  statusFm = (value) => {
    if (value == '1') {
      return <span style={{ color: '#1DC11D' }}>已结清</span>;
    } else {
      return <span style={{ color: '#ff0000' }}>未结清</span>;
    }
  }

  /*渲染订单状态*/
  renderStatus = (value) => {
    switch (value) {
      case '0':
        return <span style={{ color: '#ff5711' }}>未完成</span>
      case '1':
        return <span style={{ color: '#ffa631' }}>已支付未刷卡</span>
      case '2':
        return <span style={{ color: '#ff0000' }}>退款</span>
      case '3':
        return <span style={{ color: '#1DC11D' }}>已完成</span>
      case '4':
        return <span style={{ color: '#ff0000' }}>作废</span>
      case '5':
        return <span style={{ color: '#ff0000' }}>红冲</span>
    }
  }

  rowOptionRender = (value, index, record) => {
    return (<ViewOrderDetail record={record} />
    )
  }

  changePrintType = (value) => {
    this.field.setValue('printType', value);
    if( value === 0 ){
      this.field.setValue('isNote', 1);
    }
  }

  copyInvoice = () => {
    const { latestInvoice } = this.state;
    const textArea = document.createElement("textarea");
    textArea.value = latestInvoice;

    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        return document.execCommand('copy');
    } catch (err) {
        return false;
    } finally {
        document.body.removeChild(textArea);
    }
  }

  render() {
    const { init } = this.field;
    let record = this.field.getValues()
    const { searchValue, orderTableSource, tableDataSource, account, oweMoney, visible, visibleOfCode, readOnly, count, ishas, purchase, waitTime, latestInvoice, latestPrintTime } = this.state;
    const buyItemLayout = { labelCol: { fixedSpan: 7 } };
    const footer = (
      <span>
        <Button className="button" onClick={() => this.purchase()} loading={purchase} type="primary"
          style={{ marginRight: 20 }}>
          确认缴费
        </Button>
        <Button className="button" onClick={() => { this.onClose() }}>
          &emsp;取消&emsp;
        </Button>
      </span>
    )
    const footerOfKeyUpOfPay = (
      <div></div>
    )
    const suffix = (
      <span>
        {waitTime}s
      </span>
    )
    return (
      <div className='payment'>
        <div style={{ marginBottom: 5 }}>
          <Link to={{ pathname: `/order/SaleWater`, state: { searchValue: searchValue } }}>
            <Button className="button" type="primary">
              <FoundationSymbol type="backward" style={{ marginRight: 5 }} size="small" />
              返回
            </Button>
          </Link>
        </div>
        <IceContainer className={style.basicInformation}>
          <h5 className={style.infoColumnTitle}>基本信息</h5>
          <div className={style.infoColumn}>
            <Row wrap className={styles.infoItems}>
              <Col xxs="24" l="8" className={style.infoItem}>
                <span className={style.infoItemLabel}>用户编号：</span>
                <span className={style.infoItemValue}>{record.cno}</span>
              </Col>
              <Col xxs="24" l="8" className={style.infoItem}>
                <span className={style.infoItemLabel}>用户名称：</span>
                <span className={style.infoItemValue}>{record.cname}</span>
              </Col>
              <Col xxs="24" l="8" className={style.infoItem}>
                <span className={style.infoItemLabel}>用水性质：</span>
                <span className={style.infoItemValue}>{record.feeName}</span>
              </Col>
              <Col xxs="24" l="8" className={style.infoItem}>
                <span className={style.infoItemLabel}>水表种类：</span>
                <span className={style.infoItemValue}>{record.watermeterKind}</span>
              </Col>
              <Col xxs="24" l="8" className={style.infoItem}>
                <span className={style.infoItemLabel}>水表编号：</span>
                <span className={style.infoItemValue}>{record.watermeterId}</span>
              </Col>
              <Col xxs="24" l="8" className={style.infoItem}>
                <span className={style.infoItemLabel}>户籍人数：</span>
                <span className={style.infoItemValue}>{record.domicileNum}</span>
              </Col>
              <Col xxs="24" l="8" className={style.infoItem}>
                <span className={style.infoItemLabel}>用户地址：</span>
                <span className={style.infoItemValue}>{record.address}</span>
              </Col>
            </Row>
          </div>
        </IceContainer>
        <IceContainer className={style.saleWater}>
          <h5 className={style.infoColumnTitle}>购水缴费</h5>
          <Form field={this.field} direction="hoz">
            <Row>
              <FormItem label="账户余额：" {...buyItemLayout} >
                <AccountMoneyRecord value={account + '元'} cno={record.cno} />
              </FormItem>
              <FormItem label="欠费金额（元）："{...buyItemLayout} >
                <span style={{ color: "#ff0000" }} className="next-form-text-align">{oweMoney + "元"}</span>
              </FormItem>
              <FormItem label="滞纳金（元）：" {...buyItemLayout} className="next-form-text-align">
                <Input style={{ border: 0 }}{...init('lateAmount', { initValue: 0 })} readOnly />
              </FormItem>
            </Row>
            <Row>
              <FormItem label="优惠金额（元）：" {...buyItemLayout} >
                <Input {...init('preferentialPrice', { initValue: 0, rules: [{ required: true, message: '必填' }] })}
                  style={ishas ? styles.buyWaterReadonly : styles.buyWater}
                  onChange={(value) => this.changeFeeByPreferential(value)} readOnly={ishas}
                />
              </FormItem>
            </Row>
            <Row>
              <FormItem label="收款金额（元）：" {...buyItemLayout}>
                <Input {...init('pay', { rules: [{ validator: this.checkedPay }] })}
                  style={styles.buyWater}
                  htmlType="number"
                  onChange={this.computeChangeAmount}
                  autoFocus
                  readOnly={readOnly}
                />
              </FormItem>
              <FormItem label="剩余金额（元）：" {...buyItemLayout}>
                <Input {...init('changeAmount')} style={styles.readOnlyStyle} readOnly />
              </FormItem>
            </Row>
            <Row>
              <FormItem label="备&emsp;&emsp;注：" {...buyItemLayout}>
                <Input {...init('remarks')} style={{...styles.buyWater, width: 460}} />
              </FormItem>
            </Row>
            <Row>
              <FormItem label="付款方式：" {...buyItemLayout}>
                <RadioGroup {...init('payWay', { rules: [{ required: true, message: '必填' }] })}
                  dataSource={[
                    { value: '1', label: "现金" },
                    { value: '5', label: "付款码支付" },
                  ]}
                  defaultValue={'1'}
                />
              </FormItem>
            </Row>
            <Row>
              <FormItem label="结余方式：" {...buyItemLayout}>
                <RadioGroup {...init('clearingWay', { rules: [{ required: true, message: '必填' }] })}
                  dataSource={[
                    { value: 2, label: "存入账户" }
                  ]}
                  onChange={this.computeReallyFee}
                  defaultValue={2}
                />
              </FormItem>
            </Row>
            <Row>
              <FormItem label="打印方式：" {...buyItemLayout}>
                <RadioGroup {...init('printType', { rules: [{ required: true, message: '必填' }] })}
                  dataSource={[
                    { value: 0, label: "纸质发票" },
                    { value: 1, label: "电子发票" },
                    { value: 2, label: "不打" },
                  ]}
                  onChange={(value) => this.changePrintType(value)}
                  defaultValue={0}
                />
              </FormItem>
            </Row>
            {
              this.field.getValue('printType') === 0 ?
                <Row>
                  <FormItem label="其他选项：" {...buyItemLayout}>
                    <RadioGroup {...init('isNote', { rules: [{ required: true, message: '必填' }] })}
                      dataSource={[
                        { value: 1, label: "合并打印" },
                        { value: 0, label: "清污分离打印" },
                      ]}
                      defaultValue={1} />
                  </FormItem>
                </Row> :
                void (0)
            }
           
            {
              this.field.getValue('printType') === 0 && this.field.getValue('isNote') === 1 ?
                <Row>
                  <FormItem label="发票号码：" {...buyItemLayout}>
                    <Input {...init('invoiceNo', { rules: [{ required: true, message: '必填' }] })} 
                      style={{...styles.buyWater, width: 460}} />
                  </FormItem>
                </Row> :
                void (0)
            }
            {
              this.field.getValue('printType') === 0 && this.field.getValue('isNote') === 0 ?
                (
                  <>
                  <Row>
                    <FormItem label="清水发票号码：" {...buyItemLayout}>
                      <Input {...init('waterInvoiceNo', { rules: [{ required: true, message: '必填' }] })} 
                        style={{...styles.buyWater, width: 460}} />
                    </FormItem>
                  </Row>
                  <Row>
                    <FormItem label="污水发票号码：" {...buyItemLayout}>
                      <Input {...init('sewageInvoiceNo', { rules: [{ required: true, message: '必填' }] })} 
                        style={{...styles.buyWater, width: 460}} />
                    </FormItem>
                  </Row>
                  </>
                ) :
                void (0)
            }

            <Row>
              <FormItem label="上次使用发票号码：" {...buyItemLayout} >
                <Input
                  value={latestInvoice}
                  style={styles.buyWaterReadonly}
                  readOnly
                />
              </FormItem>
              <Button type="primary" className="button" onClick={() => this.copyInvoice()}>复制发票号码</Button>
            </Row>

            <Row>
              <FormItem label="上次开票时间：" {...buyItemLayout} >
                <Input
                  value={latestPrintTime}
                  style={styles.buyWaterReadonly}
                  readOnly
                />
              </FormItem>
            </Row>

            <Row>
              <FormItem label="实收金额（元）：" {...buyItemLayout}>
                <Input {...init('reallyFee')} style={styles.readOnlyStyle} readOnly />
              </FormItem>
            </Row>
            <div align="center" style={{ width: 600 }}>
              <Button type="primary" className="button" onClick={() => this.confirm()}>缴费</Button>
            </div>
          </Form>
        </IceContainer>
        <IceContainer className={style.saleWater}>
          <h5 className={style.infoColumnTitle}>账单记录</h5>
          <Table dataSource={tableDataSource} fixedHeader maxBodyHeight={400}>
            <Table.Column title="用户编号" dataIndex="cno" align="center" />
            <Table.Column title="水表编号" dataIndex="watermeterId" align="center" />
            <Table.Column title="出账日期" dataIndex="createTime" align="center" />
            <Table.Column title="上期示数" dataIndex="lastNum" align="center" />
            <Table.Column title="本期示数" dataIndex="thisNum" align="center" />
            <Table.Column title="结算水量" dataIndex="tunnage" align="center" />
            <Table.Column title="账单金额" dataIndex="amount" align="center" />
            <Table.Column title="欠费金额" dataIndex="unpaid" align="center" />
            <Table.Column title="账单状态" dataIndex="billStatus" align="center" cell={this.statusFm} />
            <Table.Column title="操作" cell={this.rowOption} align="center" />
          </Table>
        </IceContainer>
        <IceContainer className={style.saleWater}>
          <h5 className={style.infoColumnTitle}>订单记录</h5>
          <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: 10, marginRight: 10 }}>
            <div>累计金额：{count ? count : 0}元</div>
          </div>
          <Table dataSource={orderTableSource} fixedHeader maxBodyHeight={550}>
            <Table.Column title="用户编号" dataIndex="cno" align="center" />
            <Table.Column title="水表编号" dataIndex="watermeterId" align="center" />
            <Table.Column title="水表种类" dataIndex="watermeterKind" align="center" />
            <Table.Column title="订单来源" dataIndex="source" align="center" />
            <Table.Column title="用水性质" dataIndex="feeName" align="center" />
            <Table.Column title="订单水量" dataIndex="tunnage" align="center" />
            <Table.Column title="实收金额" dataIndex="reallyFee" align="center" />
            <Table.Column title="订单状态" dataIndex="status" align="center" cell={this.renderStatus} />
            <Table.Column title="提交时  间" dataIndex="createTime" align="center" />
            <Table.Column title="操作员" dataIndex="createName" align="center" />
            <Table.Column title="操作" align="center" cell={this.rowOptionRender} />
          </Table>
          <div align="center" style={{ marginTop: 20 }}>
            <Link to={{ pathname: `/order/SaleWater`, state: { searchValue: searchValue } }}>
              <Button className="button" type="primary">
                <FoundationSymbol type="backward" style={{ marginRight: 5 }} size="small" />
                返回
              </Button>
            </Link>
          </div>
        </IceContainer>
        <Dialog
          visible={visible}
          onClose={() => { this.onClose() }}
          title='缴费'
          footer={footer}
          footerAlign="center"
          style={{ width: 700 }}
        >
          <div style={{ fontWeight: 600, marginTop: 5, marginBottom: 20 }}>请核对收款信息后，提交订单</div>
          <Form direction="hoz">
            <Row>
              <FormItem label="付款方式：" {...buyItemLayout} >
                <Radio defaultChecked><span
                  className="next-form-text-align">{this.field.getValue('payWay') == '1' ? '现金' : '刷卡'}</span></Radio>
              </FormItem>
              <div style={{ marginRight: 68 }}></div>
              <FormItem label="结余方式：" {...buyItemLayout} >
                <Radio defaultChecked>
                  <span style={{ color: 'red' }}
                    className="next-form-text-align">{this.field.getValue('clearingWay') == 1 ? '找零' : '存入账户'}</span>
                </Radio>
              </FormItem>
            </Row>
            <Row>
              <FormItem label="欠费金额（元）：" {...buyItemLayout} style={{ color: 'red' }}>
                <Input value={oweMoney} style={styles.confimSpecialInput} readOnly />
              </FormItem>
              <FormItem label="滞纳金（元）：" {...buyItemLayout}>
                <Input value={this.field.getValue('lateAmount')} style={styles.confimInput} readOnly />
              </FormItem>
            </Row>
            <Row>
              <FormItem label="优惠金额（元）：" {...buyItemLayout}>
                <Input value={this.field.getValue('preferentialPrice')} style={styles.confimInput} readOnly />
              </FormItem>
              <FormItem label="应收金额（元）：" {...buyItemLayout}>
                <Input value={this.field.getValue('fee')} style={styles.confimInput} readOnly />
              </FormItem>
            </Row>
            <Row>
              <FormItem label="收款金额（元）：" {...buyItemLayout} style={{ color: 'red' }}>
                <Input value={this.field.getValue('pay')} style={styles.confimSpecialInput} readOnly />
              </FormItem>
              <FormItem label="剩余金额（元）：" {...buyItemLayout} >
                <Input value={this.field.getValue('changeAmount')} style={styles.confimInput} readOnly />
              </FormItem>
            </Row>
            <Row>
              <FormItem label="实收金额（元）：" {...buyItemLayout} style={{ color: 'red' }}>
                <Input value={this.field.getValue('reallyFee')} style={styles.confimSpecialInput} readOnly />
              </FormItem>
            </Row>
          </Form>
        </Dialog>

        <Dialog
          visible={visibleOfCode}
          onClose={() => this.onCloseOfCode()}
          title='等待用户扫码'
          footerAlign="center"
          footer={footerOfKeyUpOfPay}
          autoFocus={false}
          style={{ width: "20%" }}
        >
          <div style={{ display: "grid", placeItems: "center", textAlign: "center" }}>
            <Progress percent={waitTime} suffix={suffix} shape="circle" animation={false} />
          </div>
        </Dialog>
      </div>
    )
  }

}

const styles = {
  inputStyle: {
    width: 160,
    border: 0,
  },
  buyWater: {
    width: 150,
  },
  buyWaterReadonly: {
    width: 150,
    backgroundColor: '#E6E6E6',
  },
  buyRecord: {
    width: 100,
    border: 0,
  },
  readOnlyStyle: {
    width: 150,
    backgroundColor: '#E6E6E6'
  },
  confimInput: {
    width: 120,
  },
  confimSpecialInput: {
    width: 120,
    backgroundColor: '#F3DEDF'
  }
}
