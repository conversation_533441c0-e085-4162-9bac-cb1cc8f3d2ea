import React, {Component} from 'react';
import {
    <PERSON>oon,
    Button,
    DatePicker,
    Dialog,
    Feedback,
    Field,
    Form,
    Icon,
    Input,
    moment,
    Pagination,
    Select,
    Table,
} from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import qs from 'qs';
import HenxCardBreak from './components/HenxCardBreak';
import {url} from '../../../components/URL/index';
import readCard from '../../../common/readCard';
import sellWateCard from '../../../common/sellWateCard';
import ViewOrderDetail from '../../../common/ViewOrderDetail';
import CheckInvoice from './components/CheckInvoice';
import {
    areaCode,
    systemCode28,
    systemCode42
} from '../../../components/areaCode/areaCode';
import PrintInfos
    from '../../InvoiceManage/MakeOutinvoiceRecords/components/Print';
import getLodop from '../../../common/LodopFuncs';
import WaterPrice from '../../../common/waterPrice';
import WaterPriceHX from '../../../common/waterPriceHX';
import Invoicing from './components/Invoicing';
import RedRush from './components/RedRush';

const FormItem = Form.Item;
const {RangePicker} = DatePicker;
const {Combobox} = Select;
const Tooltip = Balloon.Tooltip;
export default class OrderSearch extends Component {
    constructor(props) {
        super(props);
        this.state = {
            areaList: [], // 片区
            regionList: [], // 小区
            feeNameList: [], // 用水性质
            formValue: [], // 表格数据
            roleNameList: [], // 查询操作员
            groupNameList: [], // 查询操作员分组
            unameList: [], // 查询抄表员
            selectedRecord: [], // 选中表格数据
            formwork: [], // 发票模板
            formworkMachinery: [],
            dataLoading: false,
            printStatus: false, // 批量打印状态
            page: 1,
            pageSize: 10,
            totalSize: 0,
            dialogVisible: false,
            reallyTotalFee: 0,
            sewageTotalFee: 0,
            waterTotalFee: 0,
            totalTunnage: 0,
            sourceTotalFee: 0,
            visible: false,
            stuffId: sessionStorage.getItem('stuffId'),
            createName: sessionStorage.getItem('createName'),
            hnoSum: 0,
            modifyUserNameModal: false,
            modifyOrderStatusModal: false,
            currentRecordId: null,
            invoicingRecordModal: false,
            invoicingTimes: [],
            orderTimes: [],
        };
        this.field = new Field(this, { autoUnmount: true });
        this.rowSelection = {
            onChange: (ids, records) => {
                this.setState({ selectedRowKeys: ids, selectedRecord: records });
            },
            getProps: (record) => {
                return {
                    disabled:
                        record.isNote !== '2' || record.isTax === '1' || record.kplx === '1' || record.kplx === '2',
                };
            },
        };
    }

    componentDidMount() {
        let cno = this.props.location.state;
        if (cno === undefined) {
            this.field.setValue('csTime', moment(new Date()).format('YYYY-MM-DD'));
            this.field.setValue('ceTime', moment(new Date()).format('YYYY-MM-DD'));
            this.queryOrder(1, 10);
        } else {
            this.field.setValue('cno', cno.cno);
            this.doSearch(cno);
        }
        this.queryFeeName();
        this.queryArea();
        this.queryGroupNames();
        this.queryRoleName();
        this.getTempIC();
        this.getTemp();
        this.queryUname();
        this.invoicingRecordModalClose();
    }

    // 查询表格数据
    queryOrder(page, pageSize) {
        this.setState({ dataLoading: true, selectedRowKeys: [] });
        let values = this.field.getValues();
        values.page = page;
        values.pageSize = pageSize;
        axios({
            method: 'post',
            url: url + 'revenue/order/query',
            data: qs.stringify(values),
        })
            .then((response) => {
                this.querySumReallyAndWaterAndSewageFee(values);
                this.setState({
                    formValue: response.data.datas,
                    page: response.data.page,
                    pageSize: response.data.pageSize,
                    totalSize: response.data.totalSize,
                    dataLoading: false,
                });
            })
            .catch((error) => {
                Feedback.toast.error('请求错误：' + error);
                this.setState({ dataLoading: false });
            });
    }

    /* 切换分页 */
    changePage(page) {
        const { pageSize } = this.state;
        window.scrollTo(0, 500);
        this.queryOrder(page, pageSize);
        this.setState({ selectedRowKeys: [] }); // 重置批量勾选
    }

    // 改变显示记录数
    changePageSize = (pageSize) => {
        this.queryOrder(1, pageSize);
    };

    // 查询实收总金额，总清水费，总污水费
    querySumReallyAndWaterAndSewageFee(values) {
        axios({
            method: 'post',
            url: url + 'revenue/order/sumReallyAndWaterAndSewageFee',
            data: qs.stringify(values),
        })
            .then((response) => {
                if (response.data.code == '0') {
                    let resultDates = response.data.datas;
                    this.setState({
                        reallyTotalFee: resultDates.reallyTotalFee,
                        sewageTotalFee: resultDates.sewageTotalFee,
                        waterTotalFee: resultDates.waterTotalFee,
                        totalTunnage: resultDates.totalTunnage,
                        sourceTotalFee: resultDates.sourceTotalFee,
                        hnoSum: resultDates.hnoSum,
                    });
                } else {
                    Feedback.toast.error(response.data.msg);
                }
            })
            .catch((error) => {
                Feedback.toast.error('请求错误：' + error);
                this.setState({ dataLoading: false });
            });
    }

    // 调用dll控件写卡,写卡成功后调用接口改变订单状态
    dllSalewater(record) {
        let cardno = record.cardNo;
        let times = Number(record.totalTimes);
        let totalwater = record.totalTunnage;
        let water = 0;
        try {
            let i = hxdll.user_card();
            let type = i.substring(1, 2);
            let cardNo = 0;
            if (type == '3') {
                cardNo = i.substring(2, 12);
            } else {
                cardNo = i.substring(2, 10);
                water = record.tunnage;
            }
            if (i == 10) {
                Feedback.toast.error('读卡失败:无卡');
            } else if (i == 100) {
                Feedback.toast.error('读卡失败:读卡失败');
            } else if (i == 101) {
                Feedback.toast.error('读卡失败:读卡失败');
            } else if (i == 102) {
                Feedback.toast.error('读卡失败:非用户卡');
            } else {
                // 如果读卡器上有卡或者卡类型正确执行售水
                // 判断卡是不是本人的
                if (cardNo != record.cardNo) {
                    alert('读卡器上卡与用户卡号不匹配');
                    return;
                } else {
                    let result = hxdll.sellconfirm(cardno, areaCode, times - 1, water, totalwater, 0, 0, 0, 0, 0, type);
                    if (result == 0) {
                        axios({
                            method: 'post',
                            url: `${url}revenue/order/complete`,
                            data: qs.stringify({ id: record.id }),
                        })
                            .then((response) => {
                                if (response.data.code == '0') {
                                    Feedback.toast.success('写卡成功');
                                    this.queryOrder();
                                } else {
                                    Feedback.toast.error('写卡失败' + response.data.msg);
                                }
                            })
                            .catch((error) => {
                                Feedback.toast.error('请求错误：' + error);
                            });
                    } else {
                        Feedback.toast.error('写卡失败：' + sellWateCard(result) + '请重新尝试');
                    }
                }
            }
        } catch (e) {
            alert('浏览器不支持ActiveX控件，请使用IE');
        }
    }

    // 调用dll控件写卡,写卡成功后调用接口改变订单状态(华旭4428)
    dllSalewaterhuax(record) {
        console.log('record ', record);
        const { stuffId } = this.state;
        axios({
            method: 'post',
            url: `${url}revenue/user/findStep`,
            data: qs.stringify({ cno: record.cno }),
        })
            .then((response) => {
                let ladderDataSource = response.data.datas;
                if (response.data.code == '0') {
                    if (record.watermeterKind == '预付费4442') {
                        const { stuffId } = this.state;
                        let id = stuffId.substring(0, 4);
                        // 系统码|子表号|电子表号|购买量|关阀报警|囤积限量|购水次数|有效卡标志|IC卡号|用户编码|操作员|总购量|
                        let parameter =
                            systemCode42 +
                            '|1|' +
                            record.watermeterId.substring(0, 8) +
                            '|' +
                            record.tunnage +
                            '|3|1500|' +
                            record.totalTimes +
                            '|0|' +
                            1 +
                            '|' +
                            record.cno.substring(2) +
                            '|' +
                            id +
                            '|' +
                            record.totalTunnage +
                            '|';
                        console.log('hx4442write -> ', parameter);
                        let result = SZHXMETERCARD_Web.HXCD_4442_UserCard_Web(parameter).split('|');
                        console.log('hx4442write -> ', result);
                        if (result[0] > 0) {
                            axios({
                                method: 'post',
                                url: `${url}revenue/order/complete`,
                                data: qs.stringify({ id: record.id }),
                            })
                                .then((response) => {
                                    if (response.data.code == '0') {
                                        Feedback.toast.success('写卡成功');
                                        this.queryOrder();
                                    } else {
                                        Feedback.toast.error('写卡失败' + response.data.msg);
                                    }
                                })
                                .catch((error) => {
                                    Feedback.toast.error('请求错误：' + error);
                                });
                        } else {
                            this.setState({ purchase: false });
                            alert('缴费成功,浏览器不支持写卡请前往订单管理页面重新写卡');
                            this.props.history.push('/order/OrderSearch');
                        }
                    } else {
                        axios({
                            method: 'post',
                            url: `${url}revenue/order/findLastOrdersByCnoAndStatus`,
                            data: qs.stringify({ cno: record.cno, resultSize: 1 }),
                        })
                            .then((response) => {
                                let jsondata = response.data;
                                if (jsondata.code == '0') {
                                    let backupData = jsondata.datas[0].backupData;
                                    let cno = record.cno; // 用户编号
                                    let id = stuffId.substring(0, 4); // 操作员编号
                                    let times = record.totalTimes; // 购水次数
                                    let data = moment(new Date()).format('YYYY-MM-DD HH:mm:ss'); // 购水日期
                                    let fee = record.reallyFee; // 购水金额
                                    let totalFee = record.totalFee; // 总购水金额
                                    let waterSourceFee = record.waterSourceFee; // 水资源费
                                    let sewageFee = record.sewageFee; // 污水费
                                    let beforeBackupData = backupData; // 上次购水记录
                                    if (times == 1) {
                                        beforeBackupData = ' ';
                                    }
                                    let price = 0;
                                    let price1 = 0;
                                    let price2 = 0;
                                    let tunnage = 0;
                                    let tunnage1 = 0;
                                    let tunnage2 = 0;
                                    if (ladderDataSource.length > 2) {
                                        price = Number(ladderDataSource[0].price);
                                        price1 = Number(ladderDataSource[1].price);
                                        price2 = Number(ladderDataSource[2].price);
                                        tunnage = Number(ladderDataSource[0].total);
                                        tunnage1 = Number(ladderDataSource[1].total);
                                        tunnage2 = Number(ladderDataSource[2].total);
                                    } else {
                                        price = Number(ladderDataSource[0].price);
                                        price1 = price;
                                        price2 = price;
                                        tunnage = 25;
                                        tunnage1 = 1;
                                        tunnage2 = 1;
                                    }
                                    try {
                                        // 系统码|子表号|用户编号|操作员ID|购水次数|关阀报警|购水日期(yyyy-mm-dd HH:MM:SS)|购水额|累计购水额|囤积限额|限量1|限量2|限量3|单价1|单价2|单价3|单价4|购水类型|前次备份|首购时初始使用量|首购时初始使用额|
                                        let result = SZHXMETERCARD_Web.HXCD_2046_UserCard_Web(
                                            systemCode28 +
                                                '|1|' +
                                                cno +
                                                '|' +
                                                id +
                                                '|' +
                                                times +
                                                '|6|' +
                                                data +
                                                '|' +
                                                fee +
                                                '|' +
                                                totalFee +
                                                '|640|' +
                                                parseInt(tunnage) +
                                                '|' +
                                                parseInt(tunnage + tunnage1) +
                                                '|' +
                                                parseInt(tunnage + tunnage1 + tunnage2) +
                                                '|' +
                                                (price + waterSourceFee + sewageFee) +
                                                '|' +
                                                (price1 + waterSourceFee + sewageFee) +
                                                '|' +
                                                (price2 + waterSourceFee + sewageFee) +
                                                '|' +
                                                (price2 + waterSourceFee + sewageFee) +
                                                '|1|' +
                                                beforeBackupData +
                                                '|0|0|',
                                        ).split('|');
                                        if (result[0] > 0) {
                                            let backupDataNow = result[2];
                                            axios({
                                                method: 'post',
                                                url: `${url}revenue/order/complete`,
                                                data: qs.stringify({ id: record.id, backupData: backupDataNow }),
                                            })
                                                .then((response) => {
                                                    if (response.data.code == '0') {
                                                        Feedback.toast.success('写卡成功');
                                                        this.queryOrder();
                                                    } else {
                                                        Feedback.toast.error('写卡失败' + response.data.msg);
                                                    }
                                                })
                                                .catch((error) => {
                                                    Feedback.toast.error('请求错误：' + error);
                                                });
                                        }
                                    } catch (error) {
                                        this.setState({ purchase: false });
                                        alert('缴费成功,浏览器不支持写卡请前往订单管理页面重新写卡');
                                        this.props.history.push('/order/OrderSearch');
                                    }
                                }
                            })
                            .catch((error) => {
                                Feedback.toast.error('Axios请求失败' + error);
                            });
                    }
                }
            })
            .catch((error) => {
                Feedback.toast.error('Axios请求失败' + error);
            });
    }

    // 写卡按钮
    writeCard(record) {
        let result = 0;
        if (record.watermeterKind === '阶梯4428') {
            try {
                result = SZHXMETERCARD_Web.HXCD_2046_DisposeData_web('1|1|' + systemCode28 + '|', '').split('|');
                if (result[0] == 1) {
                    if (result[3] == 0) {
                        alert('上次缴费未刷表');
                    } else {
                        this.dllSalewaterhuax(record);
                    }
                } else if (result[0] == 6) {
                    this.dllSalewaterhuax(record);
                } else {
                    alert(result[1]);
                }
            } catch (error) {
                alert('浏览器不支持ActiveX控件，请使用IE');
            }
        } else if (record.watermeterKind === '预付费4442') {
            try {
                let result = SZHXMETERCARD_Web.HXCD_4442_DisposeData_Web('1|1|', '').split('|');
                if (result[0] == 1) {
                    if (result[2] == 0) {
                        Feedback.toast.error('上次缴费未刷表');
                    } else {
                        this.dllSalewaterhuax(record);
                    }
                } else if (result[0] == 9) {
                    this.dllSalewaterhuax(record);
                } else {
                    Feedback.toast.error(result[1]);
                }
            } catch (error) {
                Feedback.toast.error('浏览器不支持ActiveX控件，请使用IE');
            }
        } else {
            try {
                let readstr = hxdll.user_card();
                let type = readstr.substring(1, 2);
                if (readstr.length > 5) {
                    if (type == '3') {
                        // 圆卡
                        this.dllSalewater(record);
                    } else if (type == '1') {
                        let state = readstr.substr(-4);
                        if (state == '0000') {
                            // 钥匙卡
                            this.dllSalewater(record);
                        } else {
                            alert('该卡还未在水表上刷过卡无法写卡,请提醒用户回去刷卡。');
                        }
                    }
                } else {
                    Feedback.toast.error('读卡失败:' + readCard(readstr));
                }
            } catch (e) {
                alert('浏览器不支持ActiveX控件，请使用IE');
            }
        }
    }

    download(record) {
        axios
            .post(`${url}revenue/electInvoice/download`, qs.stringify({ id: record.id }))
            .then((response) => {
                if (response.data.code === '0') {
                    window.open(response.data.datas.pdfUrl);
                } else {
                    Feedback.toast.error('下载电子发票失败：' + response.data.msg);
                }
            })
            .catch((error) => {
                Feedback.toast.error('网络错误请刷新页面重试');
            });
    }

    /* 表格操作 */
    renderAction(record) {
        const createId = sessionStorage.getItem('stuffId');
        const createName = sessionStorage.getItem('realName');
        const down = (
            <Icon
                type="download"
                style={{ color: '#3399ff', cursor: 'pointer' }}
                size="small"
                onClick={() => this.download(record)}
            />
        );
        let refundable = true;
        if (record.source === '红冲订单' || record.status == '5') {
            refundable = false;
        } else {
            if (record.watermeterType === 'IC卡表') {
                if (record.source === '微信') {
                    if (record.status != '2') {
                        refundable = true;
                    }
                } else if (record.status != '2') {
                    refundable = true;
                }
            } else if (record.watermeterType === '机械表') {
                if (record.status == '3' || record.status == '0') {
                    refundable = true;
                }
            }
            // else if (record.totalArrearage == 0) {
            //   if (record.status == '3') {
            //     refundable = true
            //   } else {
            //     refundable = false
            //   }
            // } else {
            //   refundable = false
            // }
        }
        return (
            /**
             * 1.卡表
             *   2.1 判断订单来源是否是微信
             *       2.1.1 若是微信，则判断其订单状态是否位“已支付未刷卡”，若是，则可以退
             *       2.1.2 如果是卡表，则需要判断是否是已使用
             * 2.非卡表
             *   2.1判断是否是充值还是账单缴费，若是账单缴费不可退款，否则可以退
             */

            <div style={{ display: 'flex', justifyContent: 'space-around' }}>
                <ViewOrderDetail record={record} type={1} />
                {refundable ? (
                    <HenxCardBreak record={record} queryOrder={this.queryOrder.bind(this)} />
                ) : (
                    <Icon title="不可退" type="history" size="small" style={{ color: '#A9A9A9', cursor: 'pointer' }} />
                )}
                {record.watermeterType === 'IC卡表' &&
                record.status === '3' &&
                (createId === '829a59bde7eb4c1e84d4b2bcde6c6977' || createId
                    === 'a4dcaff9ff46443485e651a234eb8a37' || createId
                    === '39079a7f8d344e06bce818f6fa5e3ac0'|| createId
                        === '2ab196fef13211e8929900163e047834'|| createId
                        === '24939145950740cd93a29776805325f0'
                ) ? (
                    // <BalloonConfirm
                    //     onConfirm={() => this.modifyOrderStatus(record, createId, createName)}
                    //     // onCancel={}
                    //     title="确定要修改订单状态为已支付待刷卡吗"
                    //     style={{ width: 180 }}
                    // >
                    // 	</BalloonConfirm>
                    <Icon
                        title="修改订单状态"
                        type="process"
                        size="small"
                        onClick={() => this.setModifyOrderStatusModal(record)}
                    />
                ) : null}
                {record.status == '1' ? (
                    <Icon
                        title="写卡"
                        type="image-text"
                        size="small"
                        style={{ color: '#FFA003', cursor: 'pointer' }}
                        onClick={() => this.writeCard(record)}
                    />
                ) : (
                    <Icon
                        title="已写卡"
                        type="image-text"
                        size="small"
                        style={{ color: '#A9A9A9', cursor: 'pointer' }}
                    />
                )}
                {record.source !== '平账' && record.isTax === '0' && (record.kplx === '3' || record.kplx === null) ? (
                    <PrintInfos record={record} type="print" queryOrder={() => this.queryOrder(1, 10)} />
                ) : (
                    void 0
                )}
                {(record.status === '1' || record.status === '3') &&
                (record.isTax === '0' || record.isTax === '1' || record.isTax === '2' || record.isTax === '3') &&
                (record.kplx === '1' || record.kplx === '2' || record.kplx === null) ? (
                    <Invoicing record={record} queryOrder={() => this.queryOrder(1, 10)} />
                ) : (
                    void 0
                )}
                {record.status !== '2' && record.isTax === '1' && record.kplx === '1' ? (
                    <RedRush record={record} queryOrder={() => this.queryOrder(1, 10)} />
                ) : (
                    void 0
                )}
                {record.isTax === '1' && record.kplx !== '3' ? (
                    <Tooltip trigger={down} align="t" text="下载电子发票" />
                ) : (
                    void 0
                )}
                {
                    <Icon
                        title="修改用户名称"
                        type="account"
                        size="small"
                        style={{ cursor: 'pointer' }}
                        onClick={() => this.setModifyUserNameModal(record)}
                    />
                }

                {
                    //查看已开发票
                    record && record.isNote != 2 ? <CheckInvoice record={record} /> : null
                }

            </div>
        );
    }

    doSearch = (cno) => {
        if (cno == undefined) {
            return;
        } else {
            this.setState({ dataLoading: true });
            axios({
                method: 'post',
                url: url + 'revenue/order/query?n=' + Math.random(),
                data: qs.stringify(cno),
            })
                .then((response) => {
                    this.querySumReallyAndWaterAndSewageFee(cno);
                    this.setState({
                        formValue: response.data.datas,
                        current: response.data.page,
                        pageSize: response.data.pageSize,
                        totalSize: response.data.totalSize,
                        dataLoading: false,
                    });
                })
                .catch((error) => {
                    Feedback.toast.error('请求错误：' + error);
                    this.setState({ dataLoading: false });
                });
        }
    };

    /* 渲染表格中状态栏 */
    renderPayWay = (value) => {
        if (value == 0) {
            return <span>刷卡</span>;
        } else if (value == 1) {
            return <span>现金</span>;
        } else if (value == 2) {
            return <span>微信</span>;
        } else if (value == 3) {
            return <span>现金-APP</span>;
        } else if (value == 4) {
            return <span>农行</span>;
        } else if (value == 5) {
            return <span>聚合-支付宝</span>;
        } else if (value == 6) {
            return <span>聚合-微信</span>;
        } else if (value == 7) {
            return <span>蒙速办-微信</span>;
        }
        else if (value == 8) {
            return <span>银行支付</span>;
        }
        else if (value == 9) {
            return <span>支付宝支付</span>;
        }
    };

    // 重置
    reset() {
        this.field.reset('id');
        this.field.reset('cno');
        this.field.reset('hno');
        this.field.reset('areaId');
        this.field.reset('regionId');
        this.field.reset('payWays');
        this.field.reset('statusli');
        this.field.reset('cardNo');
        this.field.reset('watermeterType');
        this.field.reset('watermeterKind');
        this.field.reset('createId');
        this.field.reset('startAmount');
        this.field.reset('endAmount');
        this.field.reset('watermeterId');
        this.field.reset('source');
        this.field.reset('feeId');
        this.field.reset('startTunnage');
        this.field.reset('endTunnage');
        this.field.reset('isNote');
        this.field.reset('watermeterCompany');
        this.field.reset('isTax');
        this.field.reset('address');
        this.field.reset('kplx');
        this.field.reset('meterReaderId');
        this.setState({ selectedRowKeys: [] }); // 重置批量勾选
    }

    // 读卡
    doRead() {
        try {
            // 读恒信卡
            let readtype = hxdll.chk_card();
            console.log('readtype -> ', readtype);
            // let readtype = 1
            // 读华旭卡
            let readtypeHuaxu = SZHXMETERCARD_Web.HXCD_2046_DisposeData_web('1|1|' + systemCode28 + '|', '').split('|');
            console.log('readtypeHuaxu -> ', readtypeHuaxu);
            let readtypeHuaxu_4442 = SZHXMETERCARD_Web.HXCD_4442_DisposeData_Web('1|1|', '').split('|');
            console.log('readtypeHuaxu_4442 -> ', readtypeHuaxu_4442);
            if (readtypeHuaxu_4442[0] > -1) {
                if (readtypeHuaxu_4442[0] == 1 || readtypeHuaxu_4442[0] == 9) {
                    if (readtypeHuaxu_4442[0] == 9) {
                        let cno = readtypeHuaxu_4442[3];
                        console.log('cno -> ', cno);
                        this.field.setValue('cno', '00' + cno);
                        this.queryOrder(1, 10);
                    } else {
                        let cno = readtypeHuaxu_4442[26];
                        console.log('cno -> ', cno);
                        this.field.setValue('cno', '00' + cno);
                        this.queryOrder(1, 10);
                    }
                } else {
                    Feedback.toast.error('华旭卡读卡失败：' + readtypeHuaxu_4442[1]);
                }
                return;
            }
            if (readtype > 1 && readtypeHuaxu[0] < 1) {
                if (readtype == '11' || readtype == '25') {
                    let readstr = hxdll.user_card();
                    let type = readstr.substring(1, 2);
                    let cardNo = 0;
                    if (type == '3' || type == '5') {
                        cardNo = readstr.substring(2, 12);
                    } else {
                        cardNo = readstr.substring(2, 10);
                    }
                    if (readstr.length > 5) {
                        this.field.setValue('cardNo', cardNo);
                        this.queryOrder(1, 10);
                    } else {
                        Feedback.toast.error('恒信卡读卡失败：' + readCard(readstr));
                    }
                } else {
                    switch (readtype) {
                        case 0:
                            Feedback.toast.error('恒信卡读卡失败：设备失败');
                            return;
                        case 1:
                            Feedback.toast.error('恒信卡读卡失败：读卡器无卡');
                            return;
                        case 2:
                            Feedback.toast.error('恒信卡读卡失败：不存在的卡型，非恒信卡');
                            return;
                        case 3:
                            Feedback.toast.error('恒信卡读卡失败：读卡失败');
                            return;
                        case 4:
                            Feedback.toast.error('恒信卡读卡失败：坏卡');
                            return;
                        default:
                            Feedback.toast.error('恒信卡读卡失败：非用户卡');
                            return;
                    }
                }
            } else if (readtypeHuaxu[0] > -1 && readtype < 2) {
                if (readtypeHuaxu[0] == 1 || readtypeHuaxu[0] == 9 || readtypeHuaxu[0] == 6) {
                    if (readtypeHuaxu.length > 29 || readtypeHuaxu[0] == 6) {
                        let cardNo = readtypeHuaxu[2];
                        this.field.setValue('cardNo', cardNo);
                        this.queryOrder(1, 10);
                    } else {
                        let cardNo = readtypeHuaxu[26];
                        this.field.setValue('cardNo', cardNo);
                        this.queryOrder(1, 10);
                    }
                } else if (readtypeHuaxu[0] == 0) {
                    Feedback.toast.error('华旭卡读卡失败：非华旭卡');
                } else if (readtypeHuaxu[0] == -1) {
                    Feedback.toast.error('华旭卡读卡失败');
                } else if (readtypeHuaxu[0] == -2) {
                    Feedback.toast.error('华旭卡读卡失败：子表号数据无效');
                } else if (readtypeHuaxu[0] == 7) {
                    Feedback.toast.error('空卡');
                } else {
                    Feedback.toast.error('华旭卡读卡失败：非用户卡');
                }
            } else if (readtypeHuaxu[0] > -1 && readtype > 1) {
                Feedback.toast.error('读卡器上存在多张卡请检查！');
            } else if (readtype == 1 && readtypeHuaxu[1] == '未插卡!') {
                Feedback.toast.error('读卡器无卡');
            } else {
                Feedback.toast.error('设备失败');
            }
        } catch (e) {
            Feedback.toast.error('系统繁忙,请稍后再试');
        }
    }

    /* 渲染订单状态 */
    renderStatus = (value) => {
        switch (value) {
            case '0':
                return <span style={{ color: '#ff5711' }}>未完成</span>;
            case '1':
                return <span style={{ color: '#ffa631' }}>已支付未刷卡</span>;
            case '2':
                return <span style={{ color: '#ff0000' }}>退款</span>;
            case '3':
                return <span style={{ color: '#1DC11D' }}>已完成</span>;
            case '4':
                return <span style={{ color: '#ff0000' }}>作废</span>;
            case '5':
                return <span style={{ color: '#ff0000' }}>红冲</span>;
        }
    };

    /* 格式化日期 */
    formatData(value) {
        const time = value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : void 0;
        return time;
    }

    // 时间onchang
    timeOnchange(val, str) {
        this.field.setValue('csTime', str[0]);
        this.field.setValue('ceTime', str[1]);
    }
    timeOnchange2(val, str) {
        this.field.setValue('refundStartTime', str[0]);
        this.field.setValue('refundEndTime', str[1]);
    }

    /* 关闭弹窗 */
    onClose = () => {
        this.setState({ visible: false });
    };

    // 查询用水性质
    queryFeeName() {
        axios({
            method: 'post',
            url: `${url}revenue/fee/queryList`,
        })
            .then((response) => {
                let feeNameList = [];
                response.data.datas.map((item) => {
                    feeNameList.push({ label: item.name, value: item.id });
                });
                this.setState({ feeNameList: feeNameList });
            })
            .catch((error) => {
                Feedback.toast.error('请求错误：' + error);
            });
    }

    // 查询片区
    queryArea() {
        axios({
            method: 'get',
            url: url + 'revenue/area/getAll',
        })
            .then((response) => {
                let areaList = [];
                response.data.datas.map((item) => {
                    areaList.push({ label: item.name, value: item.id });
                });
                this.setState({ areaList: areaList });
            })
            .catch((error) => {
                Feedback.toast.error('请求错误：' + error);
            });
    }

    // 查询操作员
    queryRoleName() {
        axios({
            method: 'post',
            url: `${url}revenue/order/createName`,
        })
        .then((response) => {
            if (response.data.code == 0) {
                this.setState({
                    roleNameList: response.data.datas,
                });
            }
        })
        .catch((error) => {
            Feedback.toast.error('请求错误：' + error);
        });
    }

    // 查询操作员分组
    queryGroupNames() {
        axios({
            method: 'get',
            url: `${url}revenue/operatorGroup/getGroupListForSelection`,
        })
            .then((response) => {
                if (response.data.code == 0) {
                    this.setState({
                        groupNameList: response.data.datas,
                    });
                }
            })
            .catch((error) => {
                Feedback.toast.error('请求错误：' + error);
            });
    }

    // 字段过长隐藏
    omit(value) {
        const look = (
            <section style={{ whiteSpace: 'nowrap', textOverflow: 'ellipsis', overflow: 'hidden' }}>{value}</section>
        );
        return <Tooltip trigger={look} align="t" text={value} style={{ backgroundColor: '#f9f9f9' }} />;
    }

    // 片区onchange
    onChange(value) {
        this.field.setValue('areaId', value);
        this.field.reset('regionId');
        this.field.reset('createId');
        // 区册查询
        axios({
            method: 'post',
            url: `${url}revenue/region/regionlist`,
            data: qs.stringify({ areaId: value }),
        })
            .then((response) => {
                let regionList = [];
                response.data.datas.map((item) => {
                    regionList.push({ label: item.regionName, value: item.id });
                });
                this.setState({ regionList: regionList });
            })
            .catch((error) => {
                Feedback.toast.error('请求异常', error);
            });
        // 操作员联动
        // 区册查询
        axios({
            method: 'post',
            url: `${url}revenue/order/createName`,
            data: qs.stringify({ areaId: value }),
        })
            .then((response) => {
                if (response.data.code == 0) {
                    this.setState({
                        roleNameList: response.data.datas,
                    });
                }
            })
            .catch((error) => {
                Feedback.toast.error('请求错误：' + error);
            });
    }

    // 打印打开打印弹窗
    printAll() {
        const { selectedRecord } = this.state;
        if (selectedRecord.length > 0) {
            this.print();
        } else {
            Feedback.toast.error('请至少勾选一笔订单');
        }
    }

    // ic卡表模板
    getTempIC() {
        axios({
            method: 'post',
            url: `${url}revenue/printTemplate/get`,
            data: qs.stringify({ templateName: '通辽发票卡表' }),
        })
            .then((response) => {
                let ajaxData = response.data;
                if (ajaxData.code == '0') {
                    this.setState({ formwork: ajaxData.datas });
                }
            })
            .catch((error) => {
                Feedback.toast.error('系统异常请稍后再试', error);
            });
    }

    // 机械表模板
    getTemp() {
        axios({
            method: 'post',
            url: `${url}revenue/printTemplate/get`,
            data: qs.stringify({ templateName: '机械表账单发票' }),
        })
            .then((response) => {
                let ajaxData = response.data;
                if (ajaxData.code == '0') {
                    this.setState({ formworkMachinery: ajaxData.datas });
                }
            })
            .catch((error) => {
                Feedback.toast.error('系统异常请稍后再试', error);
            });
    }

    // 批量打印
    print() {
        this.setState({ printStatus: true });
        let LODOP = getLodop();
        const { formwork, stuffId, createName, selectedRecord, formworkMachinery } = this.state;
        // 模板数据
        let templateCode = formwork.templateCode;
        // 打印记录所需参数
        let values = [];
        values.printName = createName;
        values.printId = stuffId;
        values.invoiceType = '0';
        values.printType = '2';
        values.invoiceCode = this.field.getValue('invoiceCode');
        values.isNote = '1';
        // 循环打印
        selectedRecord.map((item, index) => {
            values.orderId = item.id;
            values.cno = item.cno;
            values.cname = item.cname;
            values.createName = item.createName;
            values.createId = item.createId;
            values.createTime = moment(item.createTime).format('YYYY-MM-DD HH:mm:ss');
            values.waterTotalFee = item.waterTotalFee;
            values.sewageTotalFee = item.sewageTotalFee;
            values.waterSourceFee = item.waterSourceFee;
            values.reallyFee = item.reallyFee;
            values.tunnage = item.tunnage;
            values.lastTunnage = item.totalTunnage;
            let lastSave = '上次结存：' + item.account;
            let thisSave = '本次结存：' + item.changeAmount;
            let flag = '';
            values.invoiceNo = this.field.getValue('invoiceNo');
            // 水资源费
            let sourceTotalFee = item.sourceTotalFee ? item.sourceTotalFee : '0';
            // 判断是什么类型的表
            if (item.watermeterType == 'IC卡表') {
                if (item.watermeterKind == '阶梯4428') {
                    let waterPrice = WaterPriceHX(item);
                    let temp = templateCode
                        .replace('日期', moment(item.createTime).format('YYYY-MM-DD'))
                        .replace('用户编号', item.cno)
                        .replace('用户名', item.cname)
                        .replace('应收金额1', item.reallyFee)
                        .replace('水量1', '')
                        .replace('清水单价', waterPrice)
                        .replace('污水单价', item.sewageFee)
                        .replace('操作员', item.createName + (item.source == '聚合支付' ? '（聚合支付）' : ''))
                        .replace('日期1', moment(item.createTime).format('YYYY-MM-DD'))
                        .replace('用户编号1', item.cno)
                        .replace('用户名1', item.cname)
                        .replace('应收金额2', item.reallyFee)
                        .replace('水量2', '')
                        .replace('清水单价1', waterPrice)
                        .replace('污水单价1', item.sewageFee)
                        .replace('操作员1', item.createName + (item.source == '聚合支付' ? '（聚合支付）' : ''))
                        .replace('发票代码1', '')
                        .replace('发票号码1', '')
                        .replace('发票代码2', '')
                        .replace('发票号码2', '')
                        .replace('水资源费1', item.waterSourceFee)
                        .replace('水资源费2', item.waterSourceFee)
                        .replace('上次结存1', '')
                        .replace('本次结存1', '')
                        .replace('上次结存2', '')
                        .replace('本次结存2', '')
                        .replace('现示数1', '')
                        .replace('现示数2', '');
                    eval(temp);
                    flag = LODOP.PRINT();
                    if (flag == 1) {
                        axios({
                            method: 'post',
                            url: `${url}revenue/invoiceRecord/save`,
                            data: qs.stringify(values),
                        })
                            .then((response) => {
                                if (response.data.code == '0') {
                                    // 打印完刷新页面
                                    if (index == selectedRecord.length - 1) {
                                        this.queryRef();
                                    }
                                }
                            })
                            .catch((error) => {
                                Feedback.toast.error('系统异常请稍后再试' + error);
                            });
                    }
                } else {
                    // 水价明细
                    let waterPrice = WaterPrice(item);
                    let temp = templateCode
                        .replace('日期', moment(item.createTime).format('YYYY-MM-DD'))
                        .replace('用户编号', item.cno)
                        .replace('用户名', item.cname)
                        .replace('应收金额1', item.reallyFee)
                        .replace('水量1', item.tunnage)
                        .replace('清水单价', waterPrice)
                        .replace('污水单价', item.sewageTotalFee)
                        .replace('操作员', item.createName + (item.source == '聚合支付' ? '（聚合支付）' : ''))
                        .replace('日期1', moment(item.createTime).format('YYYY-MM-DD'))
                        .replace('用户编号1', item.cno)
                        .replace('用户名1', item.cname)
                        .replace('应收金额2', item.reallyFee)
                        .replace('水量2', item.tunnage)
                        .replace('清水单价1', waterPrice)
                        .replace('污水单价1', item.sewageTotalFee)
                        .replace('操作员1', item.createName + (item.source == '聚合支付' ? '（聚合支付）' : ''))
                        .replace('发票代码1', '')
                        .replace('发票号码1', '')
                        .replace('发票代码2', '')
                        .replace('发票号码2', '')
                        .replace('水资源费1', sourceTotalFee)
                        .replace('水资源费2', sourceTotalFee)
                        .replace('上次结存1', '')
                        .replace('本次结存1', '')
                        .replace('上次结存2', '')
                        .replace('本次结存2', '')
                        .replace('现示数1', item.totalTunnage)
                        .replace('现示数2', item.totalTunnage);
                    eval(temp);
                    flag = LODOP.PRINT();
                    if (flag == 1) {
                        axios({
                            method: 'post',
                            url: `${url}revenue/invoiceRecord/save`,
                            data: qs.stringify(values),
                        })
                            .then((response) => {
                                if (response.data.code == '0') {
                                    // 打印完刷新页面
                                    if (index == selectedRecord.length - 1) {
                                        this.queryRef();
                                    }
                                }
                            })
                            .catch((error) => {
                                Feedback.toast.error('系统异常请稍后再试' + error);
                            });
                    }
                }
            } else if (item.watermeterType == '远传表') {
                if (item.watermeterCompany == '辽宁民生') {
                    let temp = templateCode
                        .replace('日期', moment(item.createTime).format('YYYY-MM-DD'))
                        .replace('用户编号', item.cno)
                        .replace('用户名', item.cname)
                        .replace('应收金额1', item.reallyFee)
                        .replace('水量1', '')
                        .replace('清水单价', '')
                        .replace('污水单价', item.sewageTotalFee)
                        .replace('操作员', item.createName + (item.source == '聚合支付' ? '（聚合支付）' : ''))
                        .replace('日期1', moment(item.createTime).format('YYYY-MM-DD'))
                        .replace('用户编号1', item.cno)
                        .replace('用户名1', item.cname)
                        .replace('应收金额2', item.reallyFee)
                        .replace('水量2', '')
                        .replace('清水单价1', '')
                        .replace('污水单价1', item.sewageTotalFee)
                        .replace('操作员1', item.createName + (item.source == '聚合支付' ? '（聚合支付）' : ''))
                        .replace('发票代码1', '')
                        .replace('发票号码1', '')
                        .replace('发票代码2', '')
                        .replace('发票号码2', '')
                        .replace('水资源费1', item.sourceTotalFee)
                        .replace('水资源费2', item.sourceTotalFee)
                        .replace('上次结存1', lastSave)
                        .replace('本次结存1', thisSave)
                        .replace('上次结存2', lastSave)
                        .replace('本次结存2', thisSave)
                        .replace('现示数1', '')
                        .replace('现示数2', '');
                    eval(temp);
                    flag = LODOP.PRINT();
                    if (flag == 1) {
                        axios({
                            method: 'post',
                            url: `${url}revenue/invoiceRecord/save`,
                            data: qs.stringify(values),
                        })
                            .then((response) => {
                                if (response.data.code == '0') {
                                    // 打印完刷新页面
                                    if (index == selectedRecord.length - 1) {
                                        this.queryRef();
                                    }
                                }
                            })
                            .catch((error) => {
                                Feedback.toast.error('系统异常请稍后再试' + error);
                            });
                    }
                } else {
                    // 水价明细
                    let waterPrice = WaterPrice(item);
                    let temp = templateCode
                        .replace('日期', moment(item.createTime).format('YYYY-MM-DD'))
                        .replace('用户编号', item.cno)
                        .replace('用户名', item.cname)
                        .replace('应收金额1', item.reallyFee)
                        .replace('水量1', '')
                        .replace('清水单价', waterPrice)
                        .replace('污水单价', item.sewageTotalFee)
                        .replace('操作员', item.createName + (item.source == '聚合支付' ? '（聚合支付）' : ''))
                        .replace('日期1', moment(item.createTime).format('YYYY-MM-DD'))
                        .replace('用户编号1', item.cno)
                        .replace('用户名1', item.cname)
                        .replace('应收金额2', item.reallyFee)
                        .replace('水量2', '')
                        .replace('清水单价1', waterPrice)
                        .replace('污水单价1', item.sewageTotalFee)
                        .replace('操作员1', item.createName + (item.source == '聚合支付' ? '（聚合支付）' : ''))
                        .replace('发票代码1', '')
                        .replace('发票号码1', '')
                        .replace('发票代码2', '')
                        .replace('发票号码2', '')
                        .replace('水资源费1', item.sourceTotalFee)
                        .replace('水资源费2', item.sourceTotalFee)
                        .replace('上次结存1', lastSave)
                        .replace('本次结存1', thisSave)
                        .replace('上次结存2', lastSave)
                        .replace('本次结存2', thisSave)
                        .replace('现示数1', '')
                        .replace('现示数2', '');
                    eval(temp);
                    flag = LODOP.PRINT();
                    if (flag == 1) {
                        axios({
                            method: 'post',
                            url: `${url}revenue/invoiceRecord/save`,
                            data: qs.stringify(values),
                        })
                            .then((response) => {
                                if (response.data.code == '0') {
                                    // 打印完刷新页面
                                    if (index == selectedRecord.length - 1) {
                                        this.queryRef();
                                    }
                                }
                            })
                            .catch((error) => {
                                Feedback.toast.error('系统异常请稍后再试' + error);
                            });
                    }
                }
            } else {
                templateCode = formworkMachinery.templateCode;
                // 水价明细
                let waterPrice = WaterPrice(item);
                let flag = waterPrice.substring(waterPrice.lastIndexOf('*') + 1, waterPrice.length);
                let temp = templateCode
                    .replace('日期', moment(item.createTime).format('YYYY-MM-DD'))
                    .replace('用户编号', item.cno)
                    .replace('用户名', item.cname)
                    .replace('应收金额1', item.reallyFee)
                    .replace('现示数1', '')
                    .replace('水量1', '')
                    .replace('清水单价', flag)
                    .replace('污水单价', item.sewageTotalFee)
                    .replace('水资源费1', item.sourceTotalFee)
                    .replace('操作员', item.createName)
                    .replace('日期1', moment(item.createTime).format('YYYY-MM-DD'))
                    .replace('用户编号1', item.cno)
                    .replace('用户名1', item.cname)
                    .replace('应收金额2', item.reallyFee)
                    .replace('前示数1', '')
                    .replace('现示数2', '')
                    .replace('水量2', '')
                    .replace('清水单价1', flag)
                    .replace('污水单价1', item.sewageTotalFee)
                    .replace('操作员1', item.createName)
                    .replace('水资源费2', item.sourceTotalFee)
                    .replace('制表人1', '');
                eval(temp);
                flag = LODOP.PRINT();
                if (flag == 1) {
                    axios({
                        method: 'post',
                        url: `${url}revenue/invoiceRecord/save`,
                        data: qs.stringify(values),
                    })
                        .then((response) => {
                            if (response.data.code == '0') {
                                // 打印完刷新页面
                                if (index == selectedRecord.length - 1) {
                                    this.queryRef();
                                }
                            }
                        })
                        .catch((error) => {
                            Feedback.toast.error('系统异常请稍后再试' + error);
                        });
                }
            }
        });
        this.setState({ visible: false });
    }

    // 批量打印后刷新剩余的为打印订单
    queryRef() {
        this.setState({ dataLoading: true, selectedRowKeys: [] });
        let values = this.field.getValues();
        values.page = 1;
        values.pageSize = 10;
        axios({
            method: 'post',
            url: url + 'revenue/order/query',
            data: qs.stringify(values),
        })
            .then((response) => {
                this.setState({
                    formValue: response.data.datas,
                    page: response.data.page,
                    pageSize: response.data.pageSize,
                    totalSize: response.data.totalSize,
                    dataLoading: false,
                    printStatus: false,
                    selectedRecord: [],
                });
                Feedback.toast.success('打印成功！');
            })
            .catch((error) => {
                Feedback.toast.error('请求错误：' + error);
                this.setState({ dataLoading: false });
            });
    }

    // 导出
    downloadFile = () => {
        let values = this.field.getValues();
        let url1 = `${url}revenue/orderReport/all?n=1`;
        if (values.id) {
            url1 += '&id=' + values.id;
        }
        if (values.cno) {
            url1 += '&cno=' + values.cno;
        }
        if (values.refundStartTime) {
            url1 += '&refundStartTime=' + values.refundStartTime;
        }
        if (values.refundEndTime) {
            url1 += '&refundEndTime=' + values.refundEndTime;
        }
        if (values.areaId) {
            url1 += '&areaId=' + values.areaId;
        }
        if (values.regionId) {
            url1 += '&regionId=' + values.regionId;
        }
        if (values.statusli) {
            url1 += '&statusli=' + values.statusli;
        }
        if (values.csTime) {
            url1 += '&csTime=' + values.csTime;
        } else {
            return Feedback.toast.error('订单提交时间不能为空！');
        }
        if (values.ceTime) {
            url1 += '&ceTime=' + values.ceTime;
        } else {
            return Feedback.toast.error('订单提交时间不能为空！');
        }
        if (values.isNote) {
            url1 += '&isNote=' + values.isNote;
        }
        if (values.cardNo) {
            url1 += '&cardNo=' + values.cardNo;
        }
        if (values.watermeterTypeList) {
            url1 += '&watermeterTypeList=' + values.watermeterTypeList;
        }
        if (values.watermeterKind) {
            url1 += '&watermeterKind=' + values.watermeterKind;
        }
        if (values.createId) {
            url1 += '&createId=' + values.createId;
        }
        if (values.startAmount) {
            url1 += '&startAmount=' + values.startAmount;
        }
        if (values.endAmount) {
            url1 += '&endAmount=' + values.endAmount;
        }
        if (values.watermeterId) {
            url1 += '&watermeterId=' + values.watermeterId;
        }
        if (values.source) {
            url1 += '&source=' + values.source;
        }
        if (values.feeId) {
            url1 += '&feeId=' + values.feeId;
        }
        if (values.startTunnage) {
            url1 += '&startTunnage=' + Number(values.startTunnage);
        }
        if (values.endTunnage) {
            url1 += '&endTunnage=' + Number(values.endTunnage);
        }
        if (values.payWays) {
            url1 += '&payWays=' + values.payWays;
        }
        if (values.watermeterCompany) {
            url1 += '&watermeterCompany=' + values.watermeterCompany;
        }
        if (values.address) {
            url1 += '&address=' + values.address;
        }
        if (values.hno) {
            url1 += '&hno=' + values.hno;
        }
        if (values.isTax) {
            url1 += '&isTax=' + values.isTax;
        }
        if (values.cid) {
            url1 += '&cid=' + values.cid;
        }
        if (values.cname) {
            url1 += '&cname=' + values.cname;
        }
        if (values.groupId) {
            url1 += '&groupId=' + values.groupId;
        }
        if (values.kplx) {
            url1 += '&kplx=' + values.kplx;
        }
        if (values.terminalId) {
            url1 += '&terminalId=' + values.terminalId;
        }
        window.open(encodeURI(url1), 'about:blank');
    };

    // 导出订单
    downOrder() {
        let values = this.field.getValues();
        let url1 = `${url}revenue/orderReport/OrderDetailCount?n=1`;
        if (values.id) {
            url1 += '&id=' + values.id;
        }
        if (values.cno) {
            url1 += '&cno=' + values.cno;
        }
        if (values.areaId) {
            url1 += '&areaId=' + values.areaId;
        }
        if (values.regionId) {
            url1 += '&regionId=' + values.regionId;
        }
        if (values.statusli) {
            url1 += '&statusli=' + values.statusli;
        }
        if (values.csTime) {
            url1 += '&csTime=' + values.csTime;
        } else {
            return Feedback.toast.error('订单提交时间不能为空！');
        }
        if (values.ceTime) {
            url1 += '&ceTime=' + values.ceTime;
        } else {
            return Feedback.toast.error('订单提交时间不能为空！');
        }
        if (values.isNote) {
            url1 += '&isNote=' + values.isNote;
        }
        if (values.cardNo) {
            url1 += '&cardNo=' + values.cardNo;
        }
        if (values.watermeterTypeList) {
            url1 += '&watermeterTypeList=' + values.watermeterTypeList;
        }
        if (values.watermeterKind) {
            url1 += '&watermeterKind=' + values.watermeterKind;
        }
        if (values.createId) {
            url1 += '&createId=' + values.createId;
        }
        if (values.startAmount) {
            url1 += '&startAmount=' + values.startAmount;
        }
        if (values.endAmount) {
            url1 += '&endAmount=' + values.endAmount;
        }
        if (values.watermeterId) {
            url1 += '&watermeterId=' + values.watermeterId;
        }
        if (values.source) {
            url1 += '&source=' + values.source;
        }
        if (values.feeId) {
            url1 += '&feeId=' + values.feeId;
        }
        if (values.startTunnage) {
            url1 += '&startTunnage=' + Number(values.startTunnage);
        }
        if (values.endTunnage) {
            url1 += '&endTunnage=' + Number(values.endTunnage);
        }
        if (values.payWays) {
            url1 += '&payWays=' + values.payWays;
        }
        if (values.watermeterCompany) {
            url1 += '&watermeterCompany=' + values.watermeterCompany;
        }
        if (values.address) {
            url1 += '&address=' + values.address;
        }
        if (values.hno) {
            url1 += '&hno=' + values.hno;
        }
        if (values.isTax) {
            url1 += '&isTax=' + values.isTax;
        }
        if (values.refundStartTime) {
            url1 += '&refundStartTime=' + values.refundStartTime;
        }
        if (values.refundEndTime) {
            url1 += '&refundEndTime=' + values.refundEndTime;
        }
        if (values.cid) {
            url1 += '&cid=' + values.cid;
        }
        if (values.meterReaderId) {
            url1 += '&meterReaderId=' + values.meterReaderId;
        }
        if (values.cname) {
            url1 += '&cname=' + values.cname;
        }
        if (values.groupId) {
            url1 += '&groupId=' + values.groupId;
        }
        if (values.kplx) {
            url1 += '&kplx=' + values.kplx;
        }
        if (values.terminalId) {
            url1 += '&terminalId=' + values.terminalId;
        }
        window.open(encodeURI(url1), 'about:blank');
    }

    // 渲染是否税票
    renderIsTaxReceipt(value, record) {
        switch (value) {
            case '0':
                return <span style={{ color: '#ff0000' }}>未开税票</span>;
            case '1':
                return <span style={{ color: '#1DC11D' }}>已开税票</span>;
            case '2':
                return <span style={{ color: '#ff0000' }}>开票中</span>;
            case '3':
                return <span style={{ color: '#ff0000' }}>开票失败</span>;
        }
    }

    renderkplx(value) {
        switch (value) {
            case '1':
                return <span style={{ color: '#1DC11D' }}>正票</span>;
            case '2':
                return <span style={{ color: '#ff0000' }}>红冲票</span>;
            case '3':
                return <span style={{ color: '#1C86EE' }}>纸质发票</span>;
        }
    }

    // 查询抄表员
    queryUname() {
        axios({
            method: 'get',
            url: `${url}revenue/staff/getAllCname`,
        })
            .then((response) => {
                if (response.data.code == 0) {
                    let unameList = [];
                    response.data.datas.map((item) => {
                        unameList.push({ label: item.label, value: item.id });
                    });
                    this.setState({ unameList: unameList });
                } else {
                    Feedback.toast.error(response.data.msg);
                }
            })
            .catch((error) => {
                Feedback.toast.error('请求错误：' + error);
            });
    }

    setModifyUserNameModal = (record) => {
        this.setState({ modifyUserNameModal: true, currentRecordId: record.id });
    };
    setModifyOrderStatusModal = (record) => {
        this.setState({ modifyOrderStatusModal: true, currentRecordId: record.id });
    };
    onModifyUserNameModalClose = () => {
        this.setState({ modifyUserNameModal: false, currentRecordId: null });
    };
    onModifyOrderStatusModalClose = () => {
        this.setState({ modifyOrderStatusModal: false, currentRecordId: null });
    };
    modifyOrderStatus = () => {
        const createId = sessionStorage.getItem('stuffId');
        const createName = sessionStorage.getItem('realName');
        axios
            .post(`${url}revenue/order/customizationUpdateOrder`, {
                id: this.state.currentRecordId,
                createName,
                createId,
            })
            .then((response) => {
                const { status, data } = response;
                if (status === 200 && data.code === '0') {
                    Feedback.toast.success('操作成功！');
                    this.queryOrder(1, 10);
                } else {
                    Feedback.toast.error('操作失败！');
                }
                this.onModifyOrderStatusModalClose();
            });
    };
    modifyUserName = () => {
        const cname = this.field.getValue('newUsername');
        const createId = sessionStorage.getItem('stuffId');
        const createName = sessionStorage.getItem('realName');
        axios
            .post(`${url}revenue/order/customizationUpdateCustomerName`, {
                id: this.state.currentRecordId,
                cname,
                createId,
                createName,
            })
            .then((response) => {
                const { status, data } = response;
                if (status === 200 && data.code === '0') {
                    Feedback.toast.success('操作成功！');
                    this.queryOrder(1, 10);
                } else {
                    Feedback.toast.error('操作失败！');
                }
                this.onModifyUserNameModalClose();
            });
    };

    downloadInvoicingRecord = () => {
        this.field.validate((errors) => {
            if (errors) {
                return;
            } else {
                const createId = this.field.getValue('invoiceCreateId') ? this.field.getValue('invoiceCreateId') : null;
                if (this.state.orderTimes.length === 2) {
                    // 开票时间
                    const csTime = this.state.invoicingTimes && this.state.invoicingTimes.length === 2 ? this.state.invoicingTimes[0] : null;
                    const ceTime = this.state.invoicingTimes && this.state.invoicingTimes.length === 2 ? this.state.invoicingTimes[1] : null;
                    // 订单时间
                    const ostime = this.state.orderTimes[0];
                    const oetime = this.state.orderTimes[1];

                    window.open(
                        encodeURI(
                            `${url}revenue/orderReport/exportInvoiceDetail?createId=${createId}&csTime=${csTime}&ceTime=${ceTime}&osTime=${ostime}&oeTime=${oetime}`,
                        ),
                        'about:blank',
                    );
                }
            }
        });
    };
    invoicingRecordModalClose = () => {
        this.setState({ invoicingTimes: [],orderTimes:[], invoicingRecordModal: false });
        this.field.reset('invoiceCreateId');
    };

    render() {
        const {
            hnoSum,
            unameList,
            selectedRowKeys,
            regionList,
            roleNameList,
            groupNameList,
            feeNameList,
            areaList,
            formValue,
            page,
            pageSize,
            totalSize,
            dataLoading,
            reallyTotalFee,
            sewageTotalFee,
            waterTotalFee,
            totalTunnage,
            sourceTotalFee,
        } = this.state;
        const { init } = this.field;
        return (
            <>
                <Dialog
                    visible={this.state.modifyUserNameModal}
                    onOk={this.modifyUserName}
                    onCancel={this.onModifyUserNameModalClose}
                    onClose={this.onModifyUserNameModalClose}
                    title="修改用户名称"
                    footerAlign="center"
                >
                    <Form field={this.field}>
                        <FormItem label="新用户名称：">
                            <Input {...init('newUsername')} placeholder="请输入" />
                        </FormItem>
                    </Form>
                </Dialog>
                <Dialog
                    visible={this.state.modifyOrderStatusModal}
                    onOk={this.modifyOrderStatus}
                    onCancel={this.onModifyOrderStatusModalClose}
                    onClose={this.onModifyOrderStatusModalClose}
                    title="修改订单状态"
                    footerAlign="center"
                >
                    确定要修改订单状态为已支付待刷卡吗
                </Dialog>
                <Dialog
                    visible={this.state.invoicingRecordModal}
                    onCancel={this.invoicingRecordModalClose}
                    onClose={this.invoicingRecordModalClose}
                    title="导出电子票开票记录"
                    footer={
                        <div style={{ textAlign: 'center' }}>
                            <Button type="primary" onClick={this.downloadInvoicingRecord}>
                                导出
                            </Button>
                        </div>
                    }
                >
                    <Form field={this.field} style={{ width: 360 }}>
                        <FormItem label="订单时间">
                            <RangePicker
                                {...init('orderTimes', {
                                    rules: [{ required: true, message: '请选择订单时间' }],
                                })}
                                style={{ width: 240 }}
                                onChange={(val, str) => {
                                    this.field.setValue('orderTimes', str);
                                    this.setState({ orderTimes: str });
                                }}
                            />
                        </FormItem>
                        <FormItem label="&nbsp;&nbsp;&nbsp;开票时间">
                            <RangePicker
                                {...init('invoicingTimes', {
                                    rules: [{ required: false, message: '请选择开票时间' }],
                                })}
                                style={{ width: 240 }}
                                onChange={(val, str) => {
                                    this.field.setValue('invoicingTimes', str);
                                    this.setState({ invoicingTimes: str });
                                }}
                            />
                        </FormItem>
                        <FormItem label="&emsp;&emsp;开票人">
                            <Combobox
                                {...init('invoiceCreateId', {
                                    rules: [{ required: false, message: '请选择开票人' }],
                                })}
                                placeholder="--请选择--"
                                fillProps="label"
                                hasClear
                                style={{ width: 240 }}
                                dataSource={roleNameList}
                            />
                        </FormItem>
                    </Form>
                </Dialog>
                <IceContainer title="订单管理">
                    <Form field={this.field}>
                        <div style={{ display: 'flex', justifyContent: 'space-around' }}>
                            <div style={{ display: 'flex', flexDirection: 'column' }}>
                                <FormItem label="订单编号：">
                                    <Input
                                        {...init('id')}
                                        placeholder="--支持微信单号查询--"
                                        style={styles.searchInputWidth}
                                    />
                                </FormItem>
                                <FormItem label="用户编号：">
                                    <Input {...init('cno')} placeholder="--请输入--" style={styles.searchInputWidth} />
                                </FormItem>
                                <FormItem label="选择片区：">
                                    <Select
                                        placeholder="--请输入--"
                                        {...init('areaId')}
                                        style={{ width: 180 }}
                                        dataSource={areaList}
                                        onChange={(value) => this.onChange(value, 0)}
                                    />
                                </FormItem>
                                <FormItem label="选择小区：">
                                    <Combobox
                                        {...init('regionId')}
                                        placeholder="--请先选择片区--"
                                        fillProps="label"
                                        hasClear
                                        style={{ width: 180 }}
                                        dataSource={regionList}
                                    />
                                </FormItem>

                                <FormItem label="提交时间：">
                                    <RangePicker
                                        onChange={(val, str) => this.timeOnchange(val, str)}
                                        style={{ width: 211 }}
                                        defaultValue={[
                                            moment(new Date()).format('YYYY-MM-DD'),
                                            moment(new Date()).format('YYYY-MM-DD'),
                                        ]}
                                    />
                                </FormItem>
                                <FormItem label="退款时间：">
                                    <RangePicker
                                        onChange={(val, str) => this.timeOnchange2(val, str)}
                                        style={{ width: 211 }}
                                        // defaultValue={[moment(new Date()).format('YYYY-MM-DD'), moment(new Date()).format('YYYY-MM-DD')]}
                                    />
                                </FormItem>
                                <FormItem label="纸票状态：">
                                    <Select
                                        placeholder="请选择"
                                        style={styles.searchInputWidth}
                                        {...init('isNote')}
                                        dataSource={[
                                            { label: '已开纸票', value: '1' },
                                            { label: '未开纸票', value: '2' },
                                        ]}
                                    />
                                </FormItem>
                                <FormItem label="用户户号：">
                                    <Input {...init('hno')} placeholder="--请输入--" style={styles.searchInputWidth} />
                                </FormItem>
                                <FormItem label="订单状态：">
                                    <Select
                                        {...init('statusli')}
                                        style={styles.searchInputWidth}
                                        multiple
                                        dataSource={[
                                            { label: '请选择', value: '' },
                                            { label: '未完成', value: 0 },
                                            { label: '已支付待刷卡', value: 1 },
                                            { label: '退款', value: 2 },
                                            { label: '已完成', value: 3 },
                                            { label: '作废', value: 4 },
                                            { label: '红冲', value: 5 },
                                        ]}
                                        defaultValue={[1, 3, 5]}
                                    />
                                </FormItem>
                            </div>
                            <div style={{ display: 'flex', flexDirection: 'column' }}>
                                <FormItem label="用户卡号：">
                                    <Input
                                        {...init('cardNo')}
                                        placeholder="--请输入--"
                                        style={styles.searchInputWidth}
                                    />
                                </FormItem>
                                <FormItem label="水表类型：">
                                    <Select
                                        placeholder="请选择"
                                        style={styles.searchInputWidth}
                                        {...init('watermeterTypeList')}
                                        multiple
                                        dataSource={[
                                            { label: '请选择', value: '' },
                                            { label: 'IC卡表', value: 'IC卡表' },
                                            { label: '机械表', value: '机械表' },
                                            { label: '远传表', value: '远传表' },
                                        ]}
                                    />
                                </FormItem>
                                <FormItem label="水表种类：">
                                    <Select
                                        placeholder="请选择"
                                        style={styles.searchInputWidth}
                                        {...init('watermeterKind')}
                                        dataSource={[
                                            { label: '请选择', value: '' },
                                            { label: '预付费2', value: '预付费2' },
                                            { label: '预付费5', value: '预付费5' },
                                            { label: '阶梯4428', value: '阶梯4428' },
                                            { label: '预付费4442', value: '预付费4442' },
                                            { label: '机械表', value: '机械表' },
                                            { label: '无线远传', value: '无线远传' },
                                            { label: '有线远传', value: '有线远传' },
                                        ]}
                                    />
                                </FormItem>
                                <FormItem label="&emsp;操作员：">
                                    <Combobox
                                        {...init('createId')}
                                        placeholder="--请选择--"
                                        fillProps="label"
                                        hasClear
                                        style={styles.searchInputWidth}
                                        dataSource={roleNameList}
                                    />
                                </FormItem>
                                <FormItem label="操作员分组：">
                                    <Combobox
                                        {...init('groupId')}
                                        placeholder="--请选择--"
                                        fillProps="label"
                                        hasClear
                                        style={styles.searchInputWidth}
                                        dataSource={groupNameList}
                                    />
                                </FormItem>
                                <FormItem label="金额区间：">
                                    <Input {...init('startAmount')} htmlType="number" style={{ width: 85 }} />
                                    <span>—</span>
                                    <Input {...init('endAmount')} htmlType="number" style={{ width: 85 }} />
                                </FormItem>
                                <FormItem label="水表厂家：">
                                    <Select
                                        {...init('watermeterCompany')}
                                        placeholder="请输入"
                                        style={{ width: '170px' }}
                                        dataSource={[
                                            { label: '请选择', value: '' },
                                            { label: '扬州恒信', value: '扬州恒信' },
                                            { label: '深圳华旭', value: '深圳华旭' },
                                            { label: '辽宁民生', value: '辽宁民生' },
                                            { label: '山科', value: '山科' },
                                            { label: '机械表厂家', value: '机械表厂家' },
                                            { label: '杭州竞达', value: '杭州竞达' },
                                            { label: '山东科德', value: '山东科德' },
                                            { label: '湖南威铭', value: '湖南威铭' },
                                            { label: '河南新天', value: '河南新天' },
                                            {
                                                label: '宁夏隆基',
                                                value: '宁夏隆基'
                                            },
                                            { label: '威傲', value: '威傲' },
                                        ]}
                                    />
                                </FormItem>
                                <FormItem label="税票状态：">
                                    <Select
                                        placeholder="请选择"
                                        style={styles.searchInputWidth}
                                        {...init('isTax')}
                                        dataSource={[
                                            { label: '开票失败', value: '3' },
                                            { label: '开票中', value: '2' },
                                            { label: '已开税票', value: '1' },
                                            { label: '未开税票', value: '0' },
                                        ]}
                                    />
                                </FormItem>

                                <FormItem label="开具类型：">
                                    <Select
                                        placeholder="请选择"
                                        style={styles.searchInputWidth}
                                        {...init('kplx')}
                                        dataSource={[
                                            { label: '正票', value: 1 },
                                            { label: '红冲票', value: 2 },
                                            { label: '纸质发票', value: 3 },
                                        ]}
                                    />
                                </FormItem>
                            </div>

                            <div style={{ display: 'flex', flexDirection: 'column' }}>
                                <FormItem label="水表编号：">
                                    <Input
                                        {...init('watermeterId')}
                                        placeholder="--请输入--"
                                        style={styles.searchInputWidth}
                                    />
                                </FormItem>
                                <FormItem label="订单来源：">
                                    <Select
                                        {...init('source')}
                                        style={styles.searchInputWidth}
                                        dataSource={[
                                            { label: '请选择', value: '' },
                                            { label: '柜台', value: '柜台' },
                                            { label: '聚合支付', value: '聚合支付' },
                                            { label: '微信', value: '微信' },
                                            { label: '平账', value: '平账' },
                                            { label: 'H5', value: 'H5' },
                                            { label: '农行', value: '农行' },
                                            { label: '支付宝', value: '支付宝' },
                                            { label: 'APP', value: 'APP' },
                                            { label: '红冲订单', value: '红冲订单' },
                                            { label: '蒙速办', value: '蒙速办' },
                                            { label: '营业厅-公众号', value: '营业厅-公众号' },
                                            { label: '营业厅-小程序', value: '营业厅-小程序' },
                                            { label: '联社', value: '联社' },
                                            { label: '银联', value: '银联' },
                                            { label: '建行', value: '建行' },
                                            { label: '支付宝生活缴费', value: '支付宝生活缴费' },
                                        ]}
                                    />
                                </FormItem>
                                <FormItem label="用水性质：">
                                    <Select
                                        {...init('feeId')}
                                        placeholder="请选择"
                                        dataSource={feeNameList}
                                        style={{ width: 180 }}
                                    />
                                </FormItem>
                                <FormItem label="水量区间：">
                                    <Input {...init('startTunnage')} htmlType="number" style={{ width: 85 }} />
                                    <span>—</span>
                                    <Input {...init('endTunnage')} htmlType="number" style={{ width: 85 }} />
                                </FormItem>
                                <FormItem label="支付方式：">
                                    <Select
                                        {...init('payWays')}
                                        style={styles.searchInputWidth}
                                        multiple
                                        dataSource={[
                                            { label: '请选择', value: '' },
                                            { label: '现金', value: 1 },
                                            { label: '刷卡', value: 0 },
                                            { label: '微信', value: 2 },
                                            { label: '现金-APP', value: 3 },
                                            { label: '农行', value: 4 },
                                            { label: '聚合-支付宝', value: 5 },
                                            { label: '聚合-微信', value: 6 },
                                            { label: '蒙速办-微信', value: 7 },
                                            { label: '银行支付', value: 8 },
                                            { label: '支付宝支付', value: 9 },
                                        ]}
                                    />
                                </FormItem>
                                <FormItem label="&emsp;抄表员：">
                                    <Combobox
                                        {...init('meterReaderId')}
                                        placeholder="请选择"
                                        dataSource={unameList}
                                        style={{ width: 170 }}
                                        fillProps="label"
                                        hasClear
                                    />
                                </FormItem>
                                <FormItem label="用户名称：">
                                    <Input {...init('cname')} style={{ width: 250 }} placeholder="请输入" />
                                </FormItem>
                                <FormItem label="用户地址：">
                                    <Input {...init('address')} style={{ width: 250 }} placeholder="请输入" />
                                </FormItem>
                                <FormItem label="终端机编号：">
                                    <Input {...init('terminalId')}
                                           style={{width: 250}}
                                           placeholder="请输入"/>
                                </FormItem>
                            </div>
                        </div>
                    </Form>
                    <div style={{ textAlign: 'center' }}>
                        <Button
                            type="primary"
                            className="button"
                            onClick={() => this.queryOrder(1, 10)}
                            style={{ marginRight: 10 }}
                        >
                            <Icon type="search" />
                            查询
                        </Button>
                        <Button
                            type="secondary"
                            className="button"
                            onClick={() => this.reset()}
                            style={{ marginRight: 10 }}
                        >
                            <Icon type="refresh" />
                            重置
                        </Button>
                        <Button type="primary" className="button" onClick={() => this.doRead()}>
                            <Icon type="text" />
                            读卡
                        </Button>
                    </div>
                </IceContainer>
                <IceContainer title="订单列表">
                    <div style={{ marginBottom: 10 }}>
                        <Button type="primary" className="button" onClick={this.downloadFile}>
                            <Icon type="download" />
                            导出订单报表
                        </Button>
                        <Button
                            type="primary"
                            className="button"
                            style={{ marginLeft: 10 }}
                            onClick={() => this.downOrder()}
                        >
                            <Icon type="download" />
                            导出订单
                        </Button>
                        <Button
                            type="primary"
                            className="button"
                            style={{ marginLeft: 10 }}
                            onClick={() => this.printAll()}
                            loading={this.state.printStatus}
                        >
                            <Icon type="print" />
                            批量打印
                        </Button>
                        <Button
                            type="primary"
                            className="button"
                            style={{ marginLeft: 10 }}
                            onClick={() => this.setState({ invoicingRecordModal: true })}
                        >
                            <Icon type="download" />
                            导出电子票开票记录
                        </Button>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: 10 }}>
                        <div>总户数：{hnoSum}户</div>
                        <div style={{ marginLeft: 10 }}>实收总金额：{reallyTotalFee ? reallyTotalFee : 0}元</div>
                        <div style={{ marginLeft: 10 }}>总清水费：{waterTotalFee ? waterTotalFee : 0}元</div>
                        <div style={{ marginLeft: 10 }}>总污水费：{sewageTotalFee ? sewageTotalFee : 0}元</div>
                        <div style={{ marginLeft: 10 }}>总水资源税：{sourceTotalFee ? sourceTotalFee : 0}元</div>
                        <div style={{ marginLeft: 10 }}>总吨数：{totalTunnage ? totalTunnage : 0} 吨</div>
                    </div>

                    <Table
                        dataSource={formValue}
                        isLoading={dataLoading}
                        rowSelection={{ ...this.rowSelection, selectedRowKeys: selectedRowKeys }}
                        primaryKey="id"
                    >
                        <Table.Column title="用户编号" dataIndex="cno" align="center" />
                        <Table.Column title="用户名称" dataIndex="cname" align="center" />
                        <Table.Column title="用户卡号" dataIndex="cardNo" align="center" />
                        <Table.Column
                            title="用户地址"
                            dataIndex="address"
                            align="center"
                            cell={(value) => this.omit(value)}
                        />
                        <Table.Column title="水表编号" dataIndex="watermeterId" align="center" />
                        <Table.Column title="水表种类" dataIndex="watermeterKind" align="center" />
                        <Table.Column title="水表厂家" dataIndex="watermeterCompany" align="center" />
                        <Table.Column title="订单来源" dataIndex="source" align="center" />
                        <Table.Column title="支付方式" dataIndex="payWay" align="center" cell={this.renderPayWay} />
                        <Table.Column title="用水性质" dataIndex="feeName" align="center" />
                        <Table.Column
                            title="订单水量"
                            dataIndex="tunnage"
                            align="center"
                            cell={(value) => (value ? value : 0)}
                        />
                        <Table.Column title="实收金额" dataIndex="reallyFee" align="center" />
                        <Table.Column title="订单状态" dataIndex="status" align="center" cell={this.renderStatus} />
                        <Table.Column
                            title="缴费时间"
                            dataIndex="createTime"
                            align="center"
                            cell={(value) => this.formatData(value)}
                        />
                        <Table.Column
                            title="纸票状态"
                            dataIndex="isNote"
                            align="center"
                            cell={(value, index, record) =>
                                value === '2' || record.source == '红冲订单' ? '未开纸票' : '已开纸票'
                            }
                        />
                        <Table.Column
                            title="税票状态"
                            dataIndex="isTax"
                            align="center"
                            cell={(value, index, record) => this.renderIsTaxReceipt(value, record)}
                        />
                        <Table.Column
                            title="开具类型"
                            dataIndex="kplx"
                            align="center"
                            cell={(value) => this.renderkplx(value)}
                        />
                        <Table.Column title="操作员" dataIndex="createName" align="center" />
                        <Table.Column title="抄表员" dataIndex="meterReadName" align="center" />
                        <Table.Column
                            title="操作"
                            cell={(value, index, record) => this.renderAction(record)}
                            align="center"
                            lock="right"
                            width={150}
                        />
                    </Table>

                    <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
                        <Pagination
                            pageSizeSelector="dropdown"
                            onPageSizeChange={this.changePageSize}
                            pageSizeList={[10, 30, 50, 100]}
                            style={{ marginTop: 15 }}
                            current={page}
                            pageSize={pageSize}
                            total={totalSize}
                            size="small"
                            onChange={(current) => this.changePage(current)}
                        />
                        <div style={{ lineHeight: '53px', marginLeft: 10 }}>共{totalSize}条记录</div>
                    </div>
                </IceContainer>
            </>
        );
    }
}

const styles = {
    searchInputWidth: {
        width: 180,
    },
};
