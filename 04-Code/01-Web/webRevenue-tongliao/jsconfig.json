{
    "compilerOptions": {
        "baseUrl": "./", // 用于解析非相对模块名称的基目录
        // 模块名到基于 baseUrl 的路径映射的列表
        "paths": {
            "@/*": ["src/*"]
        },
        // *其他选项
        "experimentalDecorators": true, // 启用装饰器
        "emitDecoratorMetadata": true // 为装饰器提供元数据的支持
    },
    "include": ["src/**/*", "configs/**/*", "typings/**/*"],
    "exclude": ["node_modules", "build", "dist", "scripts", "jest"]
}
