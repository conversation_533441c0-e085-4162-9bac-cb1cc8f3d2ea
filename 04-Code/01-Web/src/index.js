// import 'core-js/stable';
// import 'regenerator-runtime/runtime';
import $ from 'JQuery';
import React from 'react';
import ReactDOM from 'react-dom';
import App from './App';
import * as serviceWorker from './serviceWorker';
import { LocaleProvider } from 'antd';
import zhCN from 'antd/es/locale-provider/zh_CN';
import { message } from 'antd';
import moment from 'moment';
// 推荐在入口文件全局设置 locale
import 'moment/locale/zh-cn';
moment.locale('zh-cn');
React.$pmn = function (key) {
	const str = localStorage.getItem('pmn');
	return str.indexOf(key) > -1 ? 1 : 0
};
// 消息弹窗配置
message.config({
  top: 200,
  duration: 3,
  maxCount: 3,
});
React.$getScrollTop = () => {
	return $('.app-sider').scrollTop(); // $('.app-sider').height();
};

React.$scrollTo = () => {
	if ($('.app-sider').length > 0) {
		let height = localStorage.getItem('mp') ? parseFloat(localStorage.getItem('mp')) : 0;
		// document.getElementsByClassName('app-sider')[0].scrollTo(0, height);
		// $('.app-sider').scrollTop(height);
		$('.app-sider').animate({scrollTop:height}, 1200)
	}
};

// 存储路由和基础信息
React.$saveWhere = (key, val) => {
	// const type = localStorage.getItem('tags');
	let where = sessionStorage.getItem('where');
	where = where ? JSON.parse(where) : {};
	where = {
		...where,
		[key]: val
	};
	sessionStorage.setItem('where', JSON.stringify(where))
};

// 路由定位
React.$goWhere = (key, props) => {
	// const type = localStorage.getItem('tags');
	let where = sessionStorage.getItem('where');
	where = where ? JSON.parse(where) : null;
	if (where[key]) {
		sessionStorage.setItem('_id', where[key].id);
		props.history.push( where[key].path)
	}
};

// 返回；
React.$backWhere = (props) => {
	let where = sessionStorage.getItem('where') ? JSON.parse(sessionStorage.getItem('where')) : null;
	let id = JSON.parse(sessionStorage.getItem('_id'));
	let prev = '';
	Object.keys(where).forEach((key) => {
		if (where[key].id === id) {
			prev = where[key].prev;
			delete where[key]
		}
	});
	sessionStorage.setItem('where', JSON.stringify(where));
	props.history.push(prev)
};

// console.log(`我们当前的开发环境为： ${process.env.NODE_ENV} !`);
ReactDOM.render(
	<LocaleProvider locale={zhCN}>
		<App />
	</LocaleProvider>,
	document.getElementById('root')
);

// If you want your app to work offline and load faster, you can change
// unregister() to register() below. Note this comes with some pitfalls.
// Learn more about service workers: https://bit.ly/CRA-PWA
serviceWorker.unregister();

