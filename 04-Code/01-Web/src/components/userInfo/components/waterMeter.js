import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Row, Col, Form, Divider, Descriptions } from 'antd';
import { showFields } from '$utils';

import './index.scss';

class Index extends Component {
	render() {
		const { data } = this.props;
		const { no, wellNo, province, city, district, address, status, installTime, stampNumber, lockNumber, installLocationTypeName, installLocationName, wheelPresentNumber, wheelBaseNumber, caliber, kind, type, manufacturer, qrCodeNumber } = data ? data : {};
		return (
			<div className="infoComponent">
				<Descriptions bordered>
					<Descriptions.Item label="水表编号：">{showFields(no)}</Descriptions.Item>
					<Descriptions.Item label="二维码编号：">{showFields(qrCodeNumber)}</Descriptions.Item>
					<Descriptions.Item label="地下表井序号：">{showFields(wellNo)}</Descriptions.Item>
					<Descriptions.Item label="水表厂家：">{showFields(manufacturer)}</Descriptions.Item>
					<Descriptions.Item label="水表分类：">{showFields(type)}</Descriptions.Item>
					<Descriptions.Item label="水表种类：">{showFields(kind)}</Descriptions.Item>
					<Descriptions.Item label="水表口径：">{showFields(caliber)}</Descriptions.Item>
					<Descriptions.Item label="字轮基数：">{showFields(wheelBaseNumber)}</Descriptions.Item>
					<Descriptions.Item label="当前字轮读数：">{showFields(wheelPresentNumber)}</Descriptions.Item>
					<Descriptions.Item label="水表位置：">
						{showFields(installLocationTypeName)}-{showFields(installLocationName)}
					</Descriptions.Item>
					<Descriptions.Item label="水表地址：" span={3}>
						{province}
						{city}
						{district}
						{address}
					</Descriptions.Item>
					<Descriptions.Item label="铅封号：">{showFields(lockNumber)}</Descriptions.Item>
					<Descriptions.Item label="钢印号：">{showFields(stampNumber)}</Descriptions.Item>
					<Descriptions.Item label="安装时间：">{showFields(installTime)}</Descriptions.Item>
					<Descriptions.Item label="水表状态：">{showFields(status)}</Descriptions.Item>
				</Descriptions>
			</div>
		);
	}
}

Index.propTypes = {
	data: PropTypes.object.isRequired // 是否显示
};

Index.defaultProps = {
	data: {} // 默认为空
};

export default Index;
