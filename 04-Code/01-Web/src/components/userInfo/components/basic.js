import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Row, Col, Form, Divider, Descriptions, Tag } from 'antd';
import Img from 'react-zmage';
import { userStatusColorMap} from '@/constants/colorStyle'
import './index.scss';

class Index extends Component {
	render() {
		const { data } = this.props;

		const {
			cno,
			hno,
			name,
			cardNo,
			province,
			city,
			district,
			address,
			waterUseKindType,
			waterUseKindName,
			areaName,
			status,
			meterType,
			apportionType,
			apportionAmount,
			population,
			payWay,
			numberOfHouse,
			customerType,
			accountBalance,
			fixedQuantity,
			fixedQuantityAmount,
			createPersonName,
			createTime,
			customerLevelName,
			remark,
			accumulationAmount,
			totalAmount,
			credits,
			contact,
			contactPhone,
			email,
			idCard,
			houseLadderUse,
			houseLadderSum,
			shareLadder,
		} = data || {};
		return (
			<div className="infoComponent">
				<Row>
					<Descriptions bordered>
						<Descriptions.Item label="用户户号：">{hno}</Descriptions.Item>
						<Descriptions.Item label="用户编号：">{cno}</Descriptions.Item>
						<Descriptions.Item label="用户名称：">{name}</Descriptions.Item>
						<Descriptions.Item label="用户卡号：">{cardNo}</Descriptions.Item>
						<Descriptions.Item label="用户地址：" span={3}>
							{province}
							{city}
							{district}
							{address}
						</Descriptions.Item>
						<Descriptions.Item label="用水分类：">{waterUseKindType}</Descriptions.Item>
						<Descriptions.Item label="用水性质：">{waterUseKindName}</Descriptions.Item>
						<Descriptions.Item label="用户片区：">{areaName}</Descriptions.Item>
						<Descriptions.Item label="缴费方式：">{payWay}</Descriptions.Item>
						<Descriptions.Item label="人口数：">{population}</Descriptions.Item>
						<Descriptions.Item label="户数：">{numberOfHouse}</Descriptions.Item>
						<Descriptions.Item label="用户类型：">{customerType}</Descriptions.Item>
						<Descriptions.Item label="用户等级：">{customerLevelName}</Descriptions.Item>
						<Descriptions.Item label="水表类型：">{meterType}</Descriptions.Item>
						<Descriptions.Item label="分摊类型：">{apportionType}</Descriptions.Item>
						<Descriptions.Item label="分摊量：">{apportionAmount}</Descriptions.Item>
						<Descriptions.Item label="身份证号：">{idCard}</Descriptions.Item>
						<Descriptions.Item label="电子邮箱：">{email}</Descriptions.Item>
						<Descriptions.Item label="联系电话：">{contactPhone}</Descriptions.Item>
						<Descriptions.Item label="联系人：">{contact}</Descriptions.Item>
						<Descriptions.Item label="信用额度：">{credits}</Descriptions.Item>
						<Descriptions.Item label="总用(购)水量：">{totalAmount}</Descriptions.Item>
						<Descriptions.Item label="累计用(购)水量：">{accumulationAmount}</Descriptions.Item>
						<Descriptions.Item label="账户余额：">{accountBalance}</Descriptions.Item>
						<Descriptions.Item label="是否定量：">{fixedQuantity ? '是' : '否'}</Descriptions.Item>
						<Descriptions.Item label="定量值：">{fixedQuantityAmount}</Descriptions.Item>
						<Descriptions.Item label="阶梯用量：">{houseLadderUse}</Descriptions.Item>
						<Descriptions.Item label="阶梯总量：">{houseLadderSum}</Descriptions.Item>
						<Descriptions.Item label="共享阶梯：">{shareLadder ? '是' : '否'}</Descriptions.Item>
						<Descriptions.Item label="用户状态：">
							<Tag color={userStatusColorMap.get(status)}>
								{status}
							</Tag>
						</Descriptions.Item>
						<Descriptions.Item label="开户操作员：">{createPersonName}</Descriptions.Item>
						<Descriptions.Item label="开户时间：">{createTime}</Descriptions.Item>
						<Descriptions.Item label="备注：" span={3}>
							{remark}
						</Descriptions.Item>
					</Descriptions>
				</Row>
			</div>
		);
	}
}

Index.propTypes = {
	data: PropTypes.object.isRequired // 是否显示
};

Index.defaultProps = {
	data: {} // 默认为空
};

export default Index;
