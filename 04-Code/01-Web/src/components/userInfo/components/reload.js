import React, { Component } from 'react';
import { Row, Col, Table, Button } from 'antd';


class Index extends Component {
	constructor(props) {
		super(props);
		this.state = {
			page: 1,
			pageSize: 10,
			columns: [
				{
					title: '序号',
					dataIndex: 'xuhao',
					align: 'center',
					render: (text, record, index) => {
						return index+1
					}
				},
				{
					title: '返单时间',
					dataIndex: 'name',
					key: 'name',
					align: 'center',
				},
				{
					title: '旧表编号',
					dataIndex: 'age',
					key: 'age',
					align: 'center',
				},
				{
					title: '旧表种类',
					dataIndex: 'address',
					key: 'address',
					align: 'center',
				},
				{
					title: '旧表厂家',
					dataIndex: 'address',
					key: 'address',
					align: 'center',
				},
				{
					title: '旧表读数',
					dataIndex: 'address',
					key: 'address',
					align: 'center',
				},
				{
					title: '新表编号',
					dataIndex: 'address',
					key: 'address',
					align: 'center',
				},
				{
					title: '新表种类',
					dataIndex: 'address',
					key: 'address',
					align: 'center',
				},
				{
					title: '新表厂家',
					dataIndex: 'address',
					key: 'address',
					align: 'center',
				},
				{
					title: '新表读数',
					dataIndex: 'address',
					key: 'address',
					align: 'center',
				},
				{
					title: '操作',
					key: 'operation',
					width: 200,
					fixed: 'right',
					align: 'center',
					render: (text, record, index) => {
						return (
							<span>
								<Button title='查看' className='btn' type="primary" size="small" icon="eye"
												onClick={ () => this.handleView(record) }/>
							</span>
						)
					},
				}
			]
		};
	}

	// 查看详情
	handleView = record => {};

	getList = () => {
		const { page, pageSize} = this.state;
		this.props.list(page, pageSize)
	};

	// 列表分页
	handlePageChange = (page) => {
		this.setState({
			page
		}, () => {
			this.getList();
		})
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState({
			page: 1,
			pageSize: size,
		}, () => {
			this.getList();
		})
	};

	render () {
		const { page, pageSize, columns } = this.state;
		const { data, total } = this.props;
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10','20','50','100'],
			total: total,
			showTotal: (total) => {return `共 ${total} 条数据 `},
			onChange : (page) => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		return (
			<div>
				<Table
					size="middle"
					bordered
					scroll={{x: 2300}}
					rowKey={()=>Math.random()}
					columns={columns}
					pagination={paginationProps}
					dataSource={data}
				/>
			</div>
		)
	}
}

export default Index;
