import React, { Component } from 'react';
import { Tabs } from 'antd';

import Order from './order'; //基础信息
import Bill from './bill'; //基础信息
import Special from './special'; //基础信息

const { TabPane } = Tabs;
class Index extends Component{
	constructor(props) {
		super(props);
		this.state = {
			key: '0',
			list: [
				{
					label: '订单信息',
					value: '0',
					render: (data, total, list) => (<Order data={data} total={total} list={list} />)
				},
				{
					label: '账单信息',
					value: '1',
					render: (data, total, list) => (<Bill data={data} total={total} list={list} />)
				},
				{
					label: '特抄收费记录',
					value: '2',
					render: (data, total, list) => (<Special data={data} total={total} list={list} />)
				}
			]
		};
	}

	componentDidMount(){
		this.props.onRef(this)
	}

	onChangeType = type => {
		this.setState({key: type}, () => this.props.setType(parseInt(type)))
	};

	setType = () => {
		this.setState({key: '0'}, () => this.onChangeType('0'))
	};

	render () {
		const { list, key } = this.state;
		const { data, total, getData} = this.props;
		return (
			<div>
				<Tabs activeKey={`${key}`} defaultActiveKey="0" onChange={this.onChangeType} style={{ minHeight: 220 }}>
					{
						list.map((item, index) => {
							return (
								<TabPane tab={item.label} key={item.value}>
									{item.render(data, total, getData)}
								</TabPane>
							)
						})
					}
				</Tabs>
			</div>
		)
	}
}

export default Index;
