import React, { Component } from 'react';
import { Row, Col, Table, Button } from 'antd';


class Index extends Component {
	constructor(props) {
		super(props);
		this.state = {
			page: 1,
			pageSize: 10,
			columns: [
				{
					title: '序号',
					dataIndex: 'xuhao',
					align: 'center',
					render: (text, record, index) => {
						return index+1
					}
				},
				{
					title: '原用水性质',
					dataIndex: 'oldWaterUseKindType',
					key: 'oldWaterUseKindType',
					align: 'center',
				},
				{
					title: '原用水分类',
					dataIndex: 'oldWaterUseKindName',
					key: 'oldWaterUseKindName',
					align: 'center',
				},
				{
					title: '原阶梯类型',
					dataIndex: 'oldLadderType',
					key: 'oldLadderType',
					align: 'center',
				},
				{
					title: '新用水性质',
					dataIndex: 'newWaterUseKindType',
					key: 'newWaterUseKindType',
					align: 'center',
				},
				{
					title: '新用水分类',
					dataIndex: 'newWaterUseKindName',
					key: 'newWaterUseKindName',
					align: 'center',
				},
				{
					title: '新阶梯类型',
					dataIndex: 'newLadderType',
					key: 'newLadderType',
					align: 'center',
				},
				{
					title: '申请状态',
					dataIndex: 'applyStatus',
					key: 'applyStatus',
					align: 'center',
				},
				{
					title: '生效日期',
					dataIndex: 'updateTime',
					key: 'updateTime',
					align: 'center',
				},
				{
					title: '备注',
					dataIndex: 'remark',
					key: 'remark',
					align: 'center',
				}
			]
		};
	}

	getList = () => {
		const { page, pageSize} = this.state;
		this.props.list(page, pageSize)
	};

	// 列表分页
	handlePageChange = (page) => {
		this.setState({
			page
		}, () => {
			this.getList();
		})
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState({
			page: 1,
			pageSize: size,
		}, () => {
			this.getList();
		})
	};

	render () {
		const { page, pageSize, columns } = this.state;
		const { data, total } = this.props;
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10','20','50','100'],
			total: total,
			showTotal: (total) => {return `共 ${total} 条数据 `},
			onChange : (page) => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		return (
			<div>
				<Table
					size="middle"
					bordered
					rowKey={()=>Math.random()}
					columns={columns}
					pagination={paginationProps}
					dataSource={data}
				/>
			</div>
		)
	}
}

export default Index;
