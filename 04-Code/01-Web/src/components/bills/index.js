import './index.scss';

import React, { Component } from 'react';

import http from '$http';
import {
	constants,
	showFields,
} from '$utils';
import {
	Button,
	Col,
	Descriptions,
	Form,
	Icon,
	Input,
	message,
	Modal,
	Radio,
	Row,
	Select,
	Table,
	Tabs,
	Tag,
} from 'antd';
import PropTypes from 'prop-types';

import { billStatusColorMap } from '@/constants/colorStyle';

const FormItem = Form.Item;
const { TabPane } = Tabs;
const Option = Select.Option;

const formItemLayout = {
	labelCol: {
		xs: { span: 6 },
		sm: { span: 6 }
	},
	wrapperCol: {
		xs: { span: 17 },
		sm: { span: 17 }
	}
};
const formItemLayout2 = {
	labelCol: {
		xs: { span: 9 },
		sm: { span: 9 }
	},
	wrapperCol: {
		xs: { span: 10 },
		sm: { span: 10 }
	}
};

// 账单明细
const columnsFor1 = [
	{
		title: '序号',
		dataIndex: 'xuhao',
		width: 80,
		align: 'center',
		render: (text, record, index) => {
			return index + 1;
		}
	},
	{
		title: '月份',
		dataIndex: 'settlementPeriod',
		key: 'settlementPeriod',
		align: 'center'
	},
	{
		title: '用水性质',
		dataIndex: 'waterUseKindType',
		key: 'waterUseKindType',
		align: 'center'
	},
	{
		title: '用水分类',
		dataIndex: 'waterUseKindName',
		key: 'waterUseKindName',
		align: 'center'
	},
	{
		title: '阶梯名称',
		dataIndex: 'ladderName',
		key: 'ladderName',
		align: 'center'
	},
	{
		title: '用水量',
		dataIndex: 'waterAmount',
		key: 'waterAmount',
		align: 'center'
	},
	{
		title: '单价',
		align: 'center',
		render: (text, record, index) => {
			let newWaterAmount = record.waterAmount === 0 ? 1 : record.waterAmount;
			return ((record.cleanWaterFee + record.sewageFee + record.waterResourceFee) / newWaterAmount).toFixed(2);
		}
	},
	{
		title: '清水费',
		dataIndex: 'cleanWaterFee',
		key: 'cleanWaterFee',
		align: 'center'
	},
	{
		title: '污水费',
		dataIndex: 'sewageFee',
		key: 'sewageFee',
		align: 'center'
	},
	{
		title: '水资源税',
		dataIndex: 'waterResourceFee',
		key: 'waterResourceFee',
		align: 'center'
	},
	{
		title: '其他费用',
		dataIndex: 'otherFee',
		key: 'otherFee',
		align: 'center'
	}
];

// 金额来源
const columnsFor2 = [
	{
		title: '序号',
		dataIndex: 'xuhao',
		width: 80,
		align: 'center',
		render: (text, record, index) => {
			return index + 1;
		}
	},
	{
		title: '销账金额',
		dataIndex: 'settleFee',
		key: 'settleFee',
		align: 'center'
	},
	{
		title: '金额来源',
		dataIndex: 'sourceType',
		key: 'sourceType',
		align: 'center'
	},
	{
		title: '用户/订单编号',
		dataIndex: 'sourceNo',
		key: 'sourceNo',
		align: 'center'
	},
	{
		title: '时间',
		dataIndex: 'createTime',
		key: 'createTime',
		align: 'center'
	},
	{
		title: '状态',
		dataIndex: 'status',
		key: 'status',
		align: 'center'
	}
];

// 总分表用量
const columnsFor3 = [
	{
		title: '序号',
		dataIndex: 'xuhao',
		width: 80,
		align: 'center',
		render: (text, record, index) => {
			return index + 1;
		}
	},
	{
		title: '分表用户编号',
		dataIndex: 'partCustomerCno',
		key: 'partCustomerCno',
		align: 'center'
	},
	{
		title: '分表用户名称',
		dataIndex: 'partCustomerName',
		key: 'partCustomerName',
		align: 'center'
	},
	{
		title: '分表水表类型',
		dataIndex: 'meterType',
		key: 'meterType',
		align: 'center'
	},
	{
		title: '分表本次用量',
		dataIndex: 'partWaterAmount',
		key: 'partWaterAmount',
		align: 'center'
	}
];

// 原始账单信息
const columnsFor4 = [
	{
		title: '序号',
		dataIndex: 'xuhao',
		width: 80,
		align: 'center',
		render: (text, record, index) => {
			return index + 1;
		}
	},
	{
		title: '上期示数',
		dataIndex: 'lastWheelNumber',
		key: 'lastWheelNumber',
		align: 'center'
	},
	{
		title: '本期示数',
		dataIndex: 'currentWheelNumber',
		key: 'currentWheelNumber',
		align: 'center'
	},
	{
		title: '抄见水量',
		dataIndex: 'copiedAmount',
		key: 'copiedAmount',
		align: 'center'
	},
	{
		title: '实际用水量',
		dataIndex: 'actualAmount',
		key: 'actualAmount',
		align: 'center'
	},
	{
		title: '结算水量',
		dataIndex: 'settleAmount',
		key: 'settleAmount',
		align: 'center'
	},
	{
		title: '账单金额',
		dataIndex: 'billFee',
		key: 'billFee',
		align: 'center'
	},
	{
		title: '结清金额',
		dataIndex: 'settleFee',
		key: 'settleFee',
		align: 'center'
	},
	{
		title: '账单状态',
		dataIndex: 'billStatus',
		key: 'billStatus',
		align: 'center',
		render: text => {
			return <Tag color={billStatusColorMap.get(text)}>{text}</Tag>;
		}
	}
];

// 原始账单明细
const columnsFor5 = [
	{
		title: '序号',
		dataIndex: 'xuhao',
		width: 80,
		align: 'center',
		render: (text, record, index) => {
			return index + 1;
		}
	},
	{
		title: '月份',
		dataIndex: 'settlementPeriod',
		key: 'settlementPeriod',
		align: 'center'
	},
	{
		title: '用水性质',
		dataIndex: 'waterUseKindType',
		key: 'waterUseKindType',
		align: 'center'
	},
	{
		title: '用水分类',
		dataIndex: 'waterUseKindName',
		key: 'waterUseKindName',
		align: 'center'
	},
	{
		title: '阶梯名称',
		dataIndex: 'ladderName',
		key: 'ladderName',
		align: 'center'
	},
	{
		title: '用水量',
		dataIndex: 'waterAmount',
		key: 'waterAmount',
		align: 'center'
	},
	{
		title: '清水费',
		dataIndex: 'cleanWaterFee',
		key: 'cleanWaterFee',
		align: 'center'
	},
	{
		title: '污水费',
		dataIndex: 'sewageFee',
		key: 'sewageFee',
		align: 'center'
	},
	{
		title: '水资源税',
		dataIndex: 'waterResourceFee',
		key: 'waterResourceFee',
		align: 'center'
	},
	{
		title: '其他费用',
		dataIndex: 'otherFee',
		key: 'otherFee',
		align: 'center'
	}
];

class Index extends Component {
	constructor(props) {
		super(props);
		this.state = {
			radio: 0, // 呆坏账切换
			badReasonId: null, // 呆坏账原因
			badDescription: '', //呆坏账描述
			tab: 0, // 0 - 基础信息 1 - 费用信息 2 - 记录信息
			showUpdateFeeIntervalModal: false, //显示收费区间
			feeInterval: null, //收费区间
			adjust: {
				currentNum: '', // 本期示数
				adjustWater: '', // 调整水量
				retainWater: '', // 保留水量
				adjustAmount: '', // 调整金额
				retainAmount: '' // 保留金额
			},
			cut: {
				cutWater: '', // 核减水量
				retainWater: '', // 保留水量
				cutAmount: '', // 核减金额
				retainAmount: '' // 保留金额
			},
			list: [
				{
					type: null, // 用水性质
					waterUseKindId: null, // 用水分类
					waterAmount: '' // 水量
				}
			],
			waterUseKindMap: new Map(), // 用水分类列表
			waterUseKindList: []
		};
	}

	updateFeeIntervalModal () {
		this.setState({ showUpdateFeeIntervalModal: true });
	}

	handleFeeIntervalSubmit = () => {
		console.log(this.state);
		//修改收费区间
		let param = {
			id: this.props.record.id,
			customerId: this.props.record.customerId,
			feeInterval: this.state.feeInterval
		};
		http.post(`api/bill/updateFeeInterval`, param).then(res => {
			if (res.code === 0) {
				message.success('操作成功');
				this.setState({ showUpdateFeeIntervalModal: false });
				this.props.getDesc();
			}
		});
	};
	handleCancel = () => {
		this.setState({
			showUpdateFeeIntervalModal: false
		});
	};

	// getDesc = () => {
	// 	console.log(this.props);
	// 	http.restGet(`api/bill/getDetail`, this.props.record.id).then(res => {
	// 		if (res.code === 0) {

	// 			// this._renderDetail();
	// 		}
	// 	});
	// };

	componentDidMount () {
		this.getWaterUseKinds();
	}

	componentWillReceiveProps (nextProps) {
		if (nextProps.record) {
			const data = nextProps.record;
			if (data) {
				let newRadio = 0;
				if (data.badMarkStatus === '坏账') {
					newRadio = 1;
				} else if (data.badMarkStatus === '呆账') {
					newRadio = 2;
				} else if (data.badMarkStatus === '延时收费') {
					newRadio = 3;
				}
				this.setState({ radio: newRadio });
			}
		}
	}

	// 获取用水分类
	getWaterUseKinds = () => {
		const { waterUseKindMap } = this.state;
		constants.waterQualityMap.forEach((item, index) => {
			http.restGet(`/api/wuk/waterusekind/getSelect`, item.value).then(res => {
				if (res.code === 0) {
					waterUseKindMap.set(item.value, res.data);
				}
			});
		});
		this.setState({ waterUseKindMap });
	};

	// 添加收费内容列表
	addLine = () => {
		const { list } = this.state;
		list.push({
			type: undefined, // 用水性质
			waterUseKindId: undefined, // 用水分类
			waterAmount: '' // 水量
		});
		this.setState({ list });
	};

	// 删除某一行
	handleDelLine = index => {
		const { list } = this.state;
		list.splice(index, 1);
		this.setState({ list });
	};

	// 切换tabs
	onChangeType = tab => {
		this.setState({ tab });
	};

	// 计算调整功能的值
	handleCalcAmount = () => {
		const { adjust } = this.state;
		const { record } = this.props;
		let billFee = record.billFee; // 账单金额
		if (!adjust.currentNum) {
			message.error('请先输入调整本期示数');
			return false;
		}
		if (Number(adjust.currentNum) < Number(record.lastWheelNumber)) {
			message.error('本期调整示数不能小于上期示数！');
			return false;
		}
		http
			.post(`/api/bill/getAdjustmentFee`, {
				currentWheelNumber: adjust.currentNum,
				billId: record.id
			})
			.then(res => {
				if (res.code === 0) {
					// 保留金额
					let retainAmount = res.data.totalFeeAmount;
					// 调整金额 = 保留金额 - 原始账单金额
					let adjustAmount = (Number(retainAmount * 1) - Number(billFee * 1)).toFixed(2);
					this.setState({
						adjust: {
							...adjust,
							adjustAmount,
							retainAmount
						}
					});
				} else {
					message.error('计算失败，请稍后重试！');
				}
			})
			.catch(err => {
				message.error('计算失败，请稍后重试！');
			});
	};

	// 计算核减功能的值
	handleCalc = () => {
		const { cut, list } = this.state;
		const { record } = this.props;
		let isContinue = true;
		list.forEach(item => {
			if (item.type === undefined || !item.waterUseKindId || !`${item.waterAmount}`) {
				isContinue = false;
			}
		});
		if (!isContinue) {
			message.error('请完善数据后进行计算');
			return false;
		}
		http
			.post(`/api/bill/getProcessFee`, {
				chargeMinusFeeDetailList: list,
				billId: record.id
			})
			.then(res => {
				let cutAmount = 0; // 核减金额
				let retainAmount = 0; // 保留金额
				if (res.code === 0) {
					//保留金额 = 后台计算
					retainAmount = res.data.totalFeeAmount;
					// 核减金额 = 保留金额 - 原始账单金额
					cutAmount = (Number(retainAmount) - Number(record.billFee)).toFixed(2);
					this.setState({
						cut: {
							...cut,
							retainAmount,
							cutAmount
						}
					});
				} else {
					message.error('计算失败，请稍后重试！');
				}
			})
			.catch(err => {
				message.error('计算失败，请稍后重试！' + err);
			});
	};

	// 渲染详情
	_renderDetail = () => {
		const { tab } = this.state;
		const { record } = this.props;
		const meterType = record.meterType;
		return (
			<>
				<Tabs activeKey={`${tab}`} onChange={this.onChangeType} type="card">
					{/* 账单预览 */}
					<TabPane tab="账单总览" key="0">
						<Descriptions bordered>
							<Descriptions.Item label="用户编号：">{showFields(record.cno)}</Descriptions.Item>
							<Descriptions.Item label="用户名称：">{showFields(record.customerName)}</Descriptions.Item>
							<Descriptions.Item label="用户地址：">{showFields(record.customerAddress)}</Descriptions.Item>
							<Descriptions.Item label="账单编号：">{showFields(record.billNo)}</Descriptions.Item>
							<Descriptions.Item label="上期示数：">{showFields(record.lastWheelNumber)}</Descriptions.Item>
							<Descriptions.Item label="本期示数：">{showFields(record.currentWheelNumber)}</Descriptions.Item>
							<Descriptions.Item label="抄见水量：">{showFields(record.copiedAmount)}</Descriptions.Item>
							<Descriptions.Item label="实际水量：">{showFields(record.actualAmount)}</Descriptions.Item>
							<Descriptions.Item label="结算水量：">{showFields(record.settleAmount)}</Descriptions.Item>
							<Descriptions.Item label="应收金额：">{showFields(record.billFee)}</Descriptions.Item>
							<Descriptions.Item label="滞纳金：">{showFields(record.penaltyFee)}</Descriptions.Item>
							<Descriptions.Item label="实际应收金额：">{showFields(record.actualFee)}</Descriptions.Item>
							<Descriptions.Item label="结清金额：">{showFields(record.settleFee)}</Descriptions.Item>
							<Descriptions.Item label="账单状态：">{showFields(<Tag color={billStatusColorMap.get(record.billStatus)}>{record.billStatus}</Tag>)}</Descriptions.Item>
							<Descriptions.Item label="平账时间：">{showFields(record.averageAccountTime)}</Descriptions.Item>
							<Descriptions.Item label="账单类型：">{showFields(<span style={record.billType === '红冲出账' ? { color: 'red' } : void 0}>{record.billType}</span>)}</Descriptions.Item>
							<Descriptions.Item label="出账状态：">{showFields(<span style={record.outStatus === '出账失败' ? { color: 'red' } : void 0}>{record.outStatus}</span>)}</Descriptions.Item>
							<Descriptions.Item label="出账失败原因：">{showFields(record.outFailReason)}</Descriptions.Item>
							<Descriptions.Item label="账单创建人：">{showFields(record.createPersonName)}</Descriptions.Item>
							<Descriptions.Item label="账单创建时间：">{showFields(record.createTime)}</Descriptions.Item>
							<Descriptions.Item label="账单所属账期：">{showFields(record.billPeriod)}</Descriptions.Item>
							<Descriptions.Item label="水表类型：">{showFields(record.meterType)}</Descriptions.Item>
							<Descriptions.Item label="送盘标志：">{showFields(record.sendDiscMark)}</Descriptions.Item>
							<Descriptions.Item label="呆坏账：">{showFields(record.badMarkStatus)}</Descriptions.Item>
							<Descriptions.Item label="呆坏账原因：">{showFields(record.badReasonValue)}</Descriptions.Item>
							<Descriptions.Item label="呆坏账描述：">{showFields(record.badDescription)}</Descriptions.Item>
							<Descriptions.Item label="通讯时间：">{record && showFields(record.correspondenceTime)}</Descriptions.Item>
							<Descriptions.Item label="总表账单编号：">{record.meterType === '虚表' ? showFields(record.sumWaterMeterBillNo) : '--'}</Descriptions.Item>
							<Descriptions.Item label="收费区间：">
								<div style={{ display: 'flex', justifyContent: 'space-between' }}>
									{showFields(record.feeInterval)}
									<Icon type="edit" style={{ cursor: 'pointer', textAlign: 'right', color: 'blue' }} onClick={() => this.updateFeeIntervalModal()} />
								</div>
							</Descriptions.Item>
						</Descriptions>
					</TabPane>
					{/* 账单明细 */}
					<TabPane tab="账单明细" key="1">
						<Table bordered columns={columnsFor1} rowKey={record => record.id} dataSource={record.chargeDetailList || []} pagination={false} />
					</TabPane>
					{/* 金额来源 */}
					<TabPane tab="金额来源" key="2">
						<Table bordered columns={columnsFor2} rowKey={record => record.id} dataSource={record.chargeBillSourceList || []} pagination={false} />
					</TabPane>
					{/* 总分表用量 */}
					{meterType === '总表' || meterType === '虚表' ? (
						<TabPane tab="总分表用量" key="3">
							<Table bordered columns={columnsFor3} rowKey={record => record.id} dataSource={record.chargeBillPartList || []} pagination={false} />
						</TabPane>
					) : null}
				</Tabs>
				<Modal title="修改收费区间" visible={this.state.showUpdateFeeIntervalModal} onCancel={this.handleCancel} footer={null}>
					<Row>
						{record ? (
							<Form {...constants.formItemLayout}>
								<FormItem label="用户编号：">
									<Input disabled value={record.cno} />
								</FormItem>
								<FormItem label="用户名称：">
									<Input disabled value={record.customerName} />
								</FormItem>
								<FormItem label="原收费区间：">
									<Input disabled value={record.feeInterval} />
								</FormItem>
								<FormItem label="新收费区间：">
									<Input
										placeholder="请输入收费区间"
										onChange={v => {
											this.setState({ feeInterval: v.target.value });
										}}
									/>
								</FormItem>
							</Form>
						) : null}
						<Col align="center">
							<Button className="btn" type="primary" onClick={this.handleFeeIntervalSubmit}>
								确定
							</Button>
							<Button className="btn" type="default" onClick={this.handleCancel}>
								取消
							</Button>
						</Col>
					</Row>
				</Modal>
			</>
		);
	};

	// 渲染发票打印
	_renderPrint = () => {
		const { radio } = this.state;
		return (
			<Form {...formItemLayout}>
				<Col className="col">
					<FormItem label="票据类型选择">
						<Radio.Group
							onChange={v => {
								this.setState({
									radio: v.target.value
								});
							}}
							value={radio}
						>
							<Radio value={0}>合打发票</Radio>
							<Radio value={1}>清污分离打印</Radio>
						</Radio.Group>
					</FormItem>
				</Col>
			</Form>
		);
	};

	// 调整
	_renderAdjust = () => {
		const { adjust } = this.state;
		const { record, adjustmentReason } = this.props;
		return (
			<Row>
				<Col>
					<h4>原始账单信息</h4>
					<Table className="tableMb" bordered columns={columnsFor4} rowKey={record => record.id} dataSource={[{ ...record }]} pagination={false} />
				</Col>
				<Col>
					<Form {...formItemLayout2}>
						<FormItem label="调整本期示数:">
							<Col span={17}>
								<Input
									placeholder="请输入调整本期示数"
									value={adjust.currentNum}
									onChange={v => {
										let value = v.target.value;
										// 判断是否有空格，如果有空格，则去掉空格
										if (/\s+/.test(`${value}`)) {
											let val = `${value}`.replace(/\s+/, '');
											v.target.value = val;
										} else {
											if (/^[1-9]\d*$/.test(`${value}`)) {
												v.target.value = `${value}`;
											} else {
												v.target.value = `${value}`.substr(0, `${value}`.length - 1);
											}
											value = v.target.value;
											let lastWheelNumber = record.lastWheelNumber; // 上期示数
											let currentWheelNumber = record.currentWheelNumber; // 本期示数
											// 调整水量 = 调整本期示数 - 原始本期示数
											let adjustWater = parseInt(value) - parseInt(currentWheelNumber);
											// 保留水量 = 调整本期示数 - 原始上期示数
											let retainWater = parseInt(value) - parseInt(lastWheelNumber);
											this.setState({ adjust: { ...adjust, currentNum: value, adjustWater, retainWater } });
										}
									}}
								/>
							</Col>
						</FormItem>
						<FormItem label="调整水量:">
							<Col span={17}>
								<Input disabled placeholder="调整水量" value={adjust.adjustWater} />
							</Col>
						</FormItem>
						<FormItem label="保留水量:">
							<Col span={17}>
								<Input disabled placeholder="保留水量" value={adjust.retainWater} />
							</Col>
						</FormItem>
						<FormItem label="调整金额:">
							<Col span={17}>
								<Input disabled placeholder="调整金额" value={adjust.adjustAmount} />
							</Col>
							<Col span={6}>
								<Button type="link" onClick={this.handleCalcAmount}>
									点击计算金额
								</Button>
							</Col>
						</FormItem>
						<FormItem label="保留金额:">
							<Col span={17}>
								<Input disabled placeholder="保留金额" value={adjust.retainAmount} />
							</Col>
						</FormItem>
						<FormItem label={'调整原因:'}>
							<Col span={17}>
								<Select
									placeholder="请选择"
									onChange={v => {
										adjust.reasonId = v;
									}}
								>
									{adjustmentReason &&
										adjustmentReason.map((item, index) => {
											return (
												<Option key={index} value={item.value}>
													{item.label}
												</Option>
											);
										})}
								</Select>
							</Col>
						</FormItem>
						<FormItem label={'调整备注:'}>
							<Col span={17}>
								<Input
									placeholder="请输入"
									onBlur={v => {
										adjust.description = v.target.value;
									}}
								/>
							</Col>
						</FormItem>
					</Form>
				</Col>
			</Row>
		);
	};

	// 核减
	_renderCut = () => {
		const { list, waterUseKindMap, waterUseKindList, cut } = this.state;
		const { record, minusReason } = this.props;
		return (
			<Row>
				<Col>
					<h4>原始账单信息</h4>
					<Table className="tableMb" bordered columns={columnsFor4} rowKey={record => record.id} dataSource={[{ ...record }]} pagination={false} />
					<h4>原始账单明细</h4>
					<Table className="tableMb" bordered columns={columnsFor5} rowKey={record => record.id} dataSource={record.chargeDetailList || []} pagination={false} />
				</Col>
				<Col className="adjust">
					<h4>核减账单明细</h4>
					<Form {...formItemLayout2}>
						{list.map((item, index) => {
							return (
								<Row key={index}>
									<Col span={7}>
										<FormItem label="用水性质:">
											<Select
												value={item.type}
												placeholder="请选择用水性质"
												onChange={v => {
													item.type = v;
													item.waterUseKindId = null;
													this.setState({ waterUseKindList: waterUseKindMap.get(v) });
												}}
											>
												{constants.waterQualityMap.map((item, index) => {
													return (
														<Option key={index} value={item.value}>
															{item.label}
														</Option>
													);
												})}
											</Select>
										</FormItem>
									</Col>
									<Col span={7}>
										<FormItem label="用水分类">
											<Select
												value={item.waterUseKindId}
												placeholder="请选择用水分类"
												onChange={v => {
													item.waterUseKindId = v;
													const { list } = this.state;
													list[index] = {
														...list[index],
														waterUseKindId: v
													};
													this.setState({ list });
												}}
											>
												{waterUseKindList.map((item, index) => {
													return (
														<Option key={index} value={item.value}>
															{item.label}
														</Option>
													);
												})}
											</Select>
										</FormItem>
									</Col>
									<Col span={7}>
										<FormItem label="输入吨数">
											<Input
												value={item.waterAmount}
												placeholder="请输入吨数"
												onChange={v => {
													const { list } = this.state;
													const { record } = this.props;
													let value = v.target.value;
													if (/\s+/.test(`${value}`)) {
														let val = `${value}`.replace(/\s+/, '');
														v.target.value = val;
													} else {
														if (/^[1-9]\d*$/.test(`${value}`)) {
															v.target.value = `${value}`;
														} else {
															if (`${value}`.length <= 1) {
																v.target.value = 0;
															} else {
																v.target.value = `${value}`.replace(`${value}`.substr(`${value}`.length - 1, 1), '');
															}
														}
														let cutWater = 0; // 核减水量
														let retainWater = 0; // 保留水量

														// 对当前明细进行赋值
														list[index] = {
															...list[index],
															waterAmount: parseInt(v.target.value)
														};

														// 保留水量 = 核减明细 吨数之和
														list.forEach(item => {
															retainWater += parseInt(item.waterAmount);
														});

														// 核减水量 = 保留水量 - 原始账单结算水量
														cutWater = retainWater - parseInt(record.settleAmount);

														this.setState({
															list,
															cut: {
																...cut,
																retainWater,
																cutWater
															}
														});
													}
												}}
											/>
										</FormItem>
									</Col>
									<Col span={3}>{index > 0 ? <Button className="delLine" title="删除当前行" type="danger" icon="minus-circle" onClick={() => this.handleDelLine(index)} /> : null}</Col>
								</Row>
							);
						})}
						<Col className="col" align="center">
							<Button type="primary" onClick={this.addLine}>
								添加收费内容
							</Button>
						</Col>
						<Col span={16} offset={3}>
							<FormItem label="核减水量:">
								<Col span={17}>
									<Input disabled placeholder="核减水量" value={cut.cutWater} />
								</Col>
								<Col span={6}>
									<Button type="link" onClick={this.handleCalc}>
										点击计算
									</Button>
								</Col>
							</FormItem>
							<FormItem label="保留水量:">
								<Col span={17}>
									<Input disabled placeholder="保留水量" value={cut.retainWater} />
								</Col>
							</FormItem>
							<FormItem label="核减金额:">
								<Col span={17}>
									<Input disabled placeholder="核减金额" value={cut.cutAmount} />
								</Col>
							</FormItem>
							<FormItem label="保留金额:">
								<Col span={17}>
									<Input disabled placeholder="保留金额" value={cut.retainAmount} />
								</Col>
							</FormItem>
							<FormItem label={'核减原因:'}>
								<Col span={17}>
									<Select
										placeholder="请选择"
										onChange={v => {
											cut.reasonId = v;
										}}
									>
										{minusReason &&
											minusReason.map((item, index) => {
												return (
													<Option key={index} value={item.value}>
														{item.label}
													</Option>
												);
											})}
									</Select>
								</Col>
							</FormItem>
							<FormItem label={'核减备注:'}>
								<Col span={17}>
									<Input
										placeholder="请输入"
										onBlur={v => {
											cut.description = v.target.value;
										}}
									/>
								</Col>
							</FormItem>
						</Col>
					</Form>
				</Col>
			</Row>
		);
	};

	// 渲染记呆坏账
	_renderBook = () => {
		const { radio } = this.state;
		const { dazeBillReason, badBillReason, record } = this.props;

		return (
			<Form {...formItemLayout}>
				<Col className="col" align="center">
					<Radio.Group
						onChange={v => {
							this.setState({ radio: v.target.value });
						}}
						defaultValue={radio}
					>
						<Radio value={0}>正常</Radio>
						<Radio value={1}>坏账</Radio>
						<Radio value={2}>呆账</Radio>
						<Radio value={3}>延收款</Radio>
					</Radio.Group>
				</Col>
				{radio !== 0 && radio !== 3 ? (
					<Col className="col">
						<FormItem label={radio === 2 ? '呆账原因:' : '坏账原因:'}>
							<Col span={17}>
								<Select
									onChange={v => {
										this.setState({ badReasonId: v });
									}}
									defaultValue={record.badReasonId}
								>
									{radio === 2
										? dazeBillReason.map((item, index) => {
											return (
												<Option key={index} value={item.value}>
													{item.label}
												</Option>
											);
										})
										: badBillReason.map((item, index) => {
											return (
												<Option key={index} value={item.value}>
													{item.label}
												</Option>
											);
										})}
								</Select>
							</Col>
						</FormItem>
						<FormItem label={'备注:'}>
							<Col span={17}>
								<Input
									placeholder="请输入"
									onBlur={v => {
										this.setState({ badDescription: v.target.value });
									}}
									defaultValue={record.badDescription}
								/>
							</Col>
						</FormItem>
					</Col>
				) : radio !== 0 ? (
					<Col className="col">
						<FormItem label={'备注:'}>
							<Col span={17}>
								<Input
									placeholder="请输入"
									onBlur={v => {
										this.setState({ badDescription: v.target.value });
									}}
									defaultValue={record.badDescription}
								/>
							</Col>
						</FormItem>
					</Col>
				) : null}
			</Form>
		);
	};

	// 提交
	onSubmit = () => {
		const { type, onSubmit, record } = this.props;
		const { radio, badReasonId, badDescription, adjust, list } = this.state;
		let params = null;
		switch (type) {
			case 1: // 1-打印发票
				params = { radio };
				break;
			case 2: // 2-调整
				// 判断调整本期示数
				if (!adjust.currentNum) {
					message.error('请先输入调整本期示数！');
					return false;
				} else if (!adjust.reasonId) {
					message.error('请选择调整原因！');
					return false;
				} else if (Number(adjust.currentNum) < Number(record.lastWheelNumber)) {
					message.error('本期调整示数不能小于上期示数！');
					return false;
				}
				params = {
					currentWheelNumber: adjust.currentNum,
					billId: record.id
				};
				break;
			case 3: // 3-核减
				let isContinue = true;
				list.forEach(item => {
					if (item.type === undefined || !item.waterUseKindId || !`${item.waterAmount}`) {
						isContinue = false;
					}
				});
				if (!isContinue) {
					message.error('请先完善明细数据后再提交！');
					return false;
				}
				let chargeMinusFeeDetailList = [];
				list.forEach(item => {
					chargeMinusFeeDetailList.push({
						waterUseKindId: item.waterUseKindId,
						waterAmount: item.waterAmount
					});
				});
				params = {
					billId: record.id,
					chargeMinusFeeDetailList
				};
				break;
			case 4: // 4-记呆坏账
				if (radio === 1 || radio === 2) {
					if (!!!badReasonId) {
						message.warning('请选择呆坏账原因！');
						return false;
					}
				}
				params = {
					id: record.id,
					badMarkStatus: radio,
					badReasonId,
					badDescription
				};
				break;
			default:
				break;
		}
		onSubmit(params);
		this.setState({
			adjust: {
				currentNum: '', // 本期示数
				adjustWater: '', // 调整水量
				retainWater: '', // 保留水量
				adjustAmount: '', // 调整金额
				retainAmount: '' // 保留金额
			},
			cut: {
				cutWater: '', // 核减水量
				retainWater: '', // 保留水量
				cutAmount: '', // 核减金额
				retainAmount: '' // 保留金额
			},
			list: [
				{
					type: null, // 用水性质
					waterUseKindId: null, // 用水分类
					waterAmount: '' // 水量
				}
			],
			waterusekinds: {} // 用水分类列表
		});
	};

	// 取消
	onCancel = () => {
		this.setState(
			{
				adjust: {
					currentNum: '', // 本期示数
					adjustWater: '', // 调整水量
					retainWater: '', // 保留水量
					adjustAmount: '', // 调整金额
					retainAmount: '' // 保留金额
				},
				cut: {
					cutWater: '', // 核减水量
					retainWater: '', // 保留水量
					cutAmount: '', // 核减金额
					retainAmount: '' // 保留金额
				},
				list: [
					{
						type: null, // 用水性质
						waterUseKindId: null, // 用水分类
						waterAmount: '' // 水量
					}
				],
				waterusekinds: {} // 用水分类列表
			},
			() => this.props.onCancel()
		);
	};

	render () {
		const { visible, title, type, record } = this.props;
		return (
			<Modal className={`operationModal ${type === 0 || type === 3 ? 'showDetail' : type === 2 ? 'showAdjust' : ''}`} title={title} visible={visible} onCancel={this.onCancel} footer={null}>
				{/* 0-查看 */}
				{type === 0 ? this._renderDetail() : null}
				{/* 1-打印发票 */}
				{type === 1 ? this._renderPrint() : null}
				{/* 2-调整 */}
				{type === 2 ? this._renderAdjust() : null}
				{/* 3-核减 */}
				{type === 3 ? this._renderCut() : null}
				{/* 4-记呆坏账 */}
				{type === 4 ? this._renderBook() : null}
				<Row className="row">
					<Col align="center">
						{type !== 0 ? (
							<Button className="btn" type="primary" onClick={this.onSubmit}>
								提交
							</Button>
						) : null}
						<Button className="btn" type="default" onClick={this.onCancel}>
							取消
						</Button>
					</Col>
				</Row>
			</Modal>
		);
	}
}

Index.propTypes = {
	visible: PropTypes.bool.isRequired, // 是否显示
	onSubmit: PropTypes.func.isRequired, // 默认获取数据的方法
	onCancel: PropTypes.func.isRequired, // 关闭Modal
	type: PropTypes.number.isRequired, // Modal显示类型
	title: PropTypes.string.isRequired, // Modal标题
	record: PropTypes.object.isRequired // 记录集
};

Index.defaultProps = {
	visible: false, // 默认不显示
	title: '查看详情', // 标题
	type: null, // 0-查看 1-打印发票 2-调整 3-核减 4-记呆坏账
	record: {} // 初始值
};

export default Form.create()(Index);
