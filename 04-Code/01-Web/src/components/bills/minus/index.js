import React, { Component, Fragment } from 'react';
import PropTypes from 'prop-types';
import { billStatusColorMap } from '@/constants/colorStyle';
import { Button, Row, Col, Modal, Table, Descriptions, Tag } from 'antd';
import { showFields } from '$utils';

import './index.scss';

const DescItem = Descriptions.Item;

// 账单明细
const columnsFor1 = [
	{
		title: '序号',
		dataIndex: 'xuhao',
		width: 80,
		align: 'center',
		render: (text, record, index) => {
			return index + 1;
		}
	},
	{
		title: '月份',
		dataIndex: 'settlementPeriod',
		key: 'settlementPeriod',
		align: 'center'
	},
	{
		title: '用水性质',
		dataIndex: 'waterUseKindType',
		key: 'waterUseKindType',
		align: 'center'
	},
	{
		title: '用水分类',
		dataIndex: 'waterUseKindName',
		key: 'waterUseKindName',
		align: 'center'
	},
	{
		title: '阶梯名称',
		dataIndex: 'ladderName',
		key: 'ladderName',
		align: 'center'
	},
	{
		title: '用水量',
		dataIndex: 'waterAmount',
		key: 'waterAmount',
		align: 'center'
	},
	{
		title: '清水费',
		dataIndex: 'cleanWaterFee',
		key: 'cleanWaterFee',
		align: 'center'
	},
	{
		title: '污水费',
		dataIndex: 'sewageFee',
		key: 'sewageFee',
		align: 'center'
	},
	{
		title: '水资源税',
		dataIndex: 'waterResourceFee',
		key: 'waterResourceFee',
		align: 'center'
	},
	{
		title: '其他费用',
		dataIndex: 'otherFee',
		key: 'otherFee',
		align: 'center'
	}
];

// 原始账单信息
const columnsFor2 = [
	{
		title: '上期示数',
		dataIndex: 'lastWheelNumber',
		key: 'lastWheelNumber',
		align: 'center'
	},
	{
		title: '本期示数',
		dataIndex: 'currentWheelNumber',
		key: 'currentWheelNumber',
		align: 'center'
	},
	{
		title: '抄见水量',
		dataIndex: 'copiedAmount',
		key: 'copiedAmount',
		align: 'center'
	},
	{
		title: '实际用水量',
		dataIndex: 'actualAmount',
		key: 'actualAmount',
		align: 'center'
	},
	{
		title: '结算水量',
		dataIndex: 'settleAmount',
		key: 'settleAmount',
		align: 'center'
	},
	{
		title: '账单金额',
		dataIndex: 'billFee',
		key: 'billFee',
		align: 'center'
	},
	{
		title: '结清金额',
		dataIndex: 'settleFee',
		key: 'settleFee',
		align: 'center'
	},
	{
		title: '账单状态',
		dataIndex: 'billStatus',
		key: 'billStatus',
		align: 'center',
		render: text => {
			return (
				<Tag color={billStatusColorMap.get(text)}>
					{text}
				</Tag>
			);
		}
	}
];

// 核减明细
const columnsFor3 = [
	{
		title: '序号',
		dataIndex: 'xuhao',
		width: 80,
		align: 'center',
		render: (text, record, index) => {
			return index + 1;
		}
	},
	{
		title: '用水性质',
		dataIndex: 'waterUserKindType',
		key: 'waterUserKindType',
		align: 'center'
	},
	{
		title: '用水分类',
		dataIndex: 'waterUserKindName',
		key: 'waterUserKindName',
		align: 'center'
	},
	{
		title: '吨数',
		dataIndex: 'waterAmount',
		key: 'waterAmount',
		align: 'center'
	}
];

class Index extends Component {
	constructor(props) {
		super(props);
		this.state = {};
	}

	onCancel = () => {
		this.props.onCancel();
	};

	render() {
		const { visible, record } = this.props;
		return (
			<Modal
				className='minusModal'
				title={'账单核减明细'}
				visible={visible}
				onCancel={this.onCancel}
				footer={null}
			>
				{
					record ?
						<Fragment>
							<Row>
								<Col>
									<h4>原始账单基本信息</h4>
									<Table
										className='mb'
										bordered
										columns={columnsFor2}
										rowKey={(record) => record.id}
										dataSource={[{ ...record.chargeBillOld }]}
										pagination={false}
									/>
									<h4>原始账单明细</h4>
									<Table
										className='mb'
										bordered
										columns={columnsFor1}
										rowKey={(record) => record.id}
										dataSource={record.chargeDetailList || []}
										pagination={false}
									/>
									<h4>核减明细</h4>
									<Table
										className='mb'
										bordered
										columns={columnsFor3}
										rowKey={(record) => record.id}
										dataSource={record.chargeMinusFeeDetailList || []}
										pagination={false}
									/>
								</Col>
							</Row>
							<Row>
								{
									record.status === '作废' ?
										<Descriptions bordered className='mb'>
											<DescItem label="核减水量：">{showFields(record.minusWaterAmount)}</DescItem>
											<DescItem label="保留水量：">{showFields(record.retainWaterAmount)}</DescItem>
											<DescItem label="核减金额：">{showFields(record.minusFee)}</DescItem>
											<DescItem label="保留金额：">{showFields(record.retainFee)}</DescItem>
											<DescItem label="创建时间：">{showFields(record.createTime)}</DescItem>
											<DescItem label="创建人：">{showFields(record.createPersonName)}</DescItem>
											<DescItem label="核减时间：">{showFields(record.minusTime)}</DescItem>
											<DescItem label="作废时间：">{showFields(record.updateTime)}</DescItem>
											<DescItem label="作废人：">{showFields(record.updatePersonName)}</DescItem>
											<DescItem label="原因：">{showFields(record.reasonValue)}</DescItem>
											<DescItem label="备注：">{showFields(record.description)}</DescItem>
										</Descriptions>
										:
										<Descriptions bordered className='mb'>
											<DescItem label="核减水量：">{showFields(record.minusWaterAmount)}</DescItem>
											<DescItem label="保留水量：">{showFields(record.retainWaterAmount)}</DescItem>
											<DescItem label="核减金额：">{showFields(record.minusFee)}</DescItem>
											<DescItem label="保留金额：">{showFields(record.retainFee)}</DescItem>
											<DescItem label="创建时间：">{showFields(record.createTime)}</DescItem>
											<DescItem label="创建人：">{showFields(record.createPersonName)}</DescItem>
											<DescItem label="核减时间：">{showFields(record.minusTime)}</DescItem>
											<DescItem label="原因：">{showFields(record.reasonValue)}</DescItem>
											<DescItem label="备注：">{showFields(record.description)}</DescItem>
										</Descriptions>
								}

							</Row>
						</Fragment>
						: null
				}
				<Row>
					<Col align='center'>
						<Button type='default' onClick={this.onCancel}>取消</Button>
					</Col>
				</Row>
			</Modal>
		);
	}
}

Index.propTypes = {
	visible: PropTypes.bool.isRequired, // 是否显示
	onCancel: PropTypes.func.isRequired, // 关闭Modal
	record: PropTypes.object.isRequired // 记录集
};

Index.defaultProps = {
	visible: false, // 默认不显示
	record: {} // 初始值
};

export default Index;
