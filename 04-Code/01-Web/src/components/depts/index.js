import React, { Component } from 'react';
import PropTypes from 'prop-types';
import http from '$http';
import { getAreaIds } from '$utils'
import { Button, Col, Modal, Row, Tree, Spin } from "antd";

import './index.scss'

const { TreeNode } = Tree;

const api = {
	depts: `/api/sys/department/getTree`,
}

class Index extends Component {
	constructor(props) {
		super(props);
		this.state = {
			loading: false,
			depts: [],
			checkedKeys: [],
			showTree: false, // 是否开始渲染树
		};
	}

	componentWillReceiveProps(nextProps, nextContext) {

		if (nextProps.visible) {
			http.get(api.depts).then(res => {
				if (res.code === 0) {
					this.setState({
						depts: res.data,
						showTree: true
					})
				}
			})

		}
	}

	// 获取当前选项
	onCheck = (checkedKeys, info) => {
		this.setState({checkedKeys})
	};

	// 取消弹窗
	handleCancel = () => {
		this.setState({
			checkedKeys: [],
		}, () => {
			this.props.onCancel()
		})
	};

	// 提交数据
	handleSubmit = () => {
		let _arr = [];
		this.state.checkedKeys.forEach(item => _arr.push(parseInt(item)));
		this.setState({
			checkedKeys: [],
		}, () => {
			this.props.onSubmit(_arr);
		});
	};

	// 渲染片区树
	_renderTreeLoop = depts => {
		return depts.map(item => {
			if (item.children) {
				return (
					<TreeNode key={ item.id } title={ item.name }>
						{ this._renderTreeLoop(item.children) }
					</TreeNode>
				);
			} else {
				return <TreeNode key={item.id} title={item.name} />;
			}
		})
	};

	render () {
		const { depts, checkedKeys, loading, showTree } = this.state;
		const { visible } = this.props;
		return (
			<Modal title="选择所属部门" visible={visible} onCancel={this.handleCancel} footer={null}>
				<Spin spinning={loading}>
					{
						showTree ? <Tree
							checkable
							autoExpandParent
							checkedKeys={checkedKeys}
							onCheck={this.onCheck}
						>
							{
								depts.length > 0 && this._renderTreeLoop(depts)
							}
						</Tree> : null
					}
					<Row>
						<Col  align="center">
							<Button type="primary" onClick={this.handleSubmit}>提交</Button>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<Button type="default" onClick={this.handleCancel}>取消</Button>
						</Col>
					</Row>
				</Spin>
			</Modal>
		)
	}
}

Index.propTypes = {
	visible: PropTypes.bool.isRequired, // 是否显示
	onSubmit: PropTypes.func.isRequired, // 默认获取数据的方法
	onCancel: PropTypes.func.isRequired, // 关闭Modal
};

Index.defaultProps = {
	visible: false, // 默认不显示
};

export default Index;
