import React, { Component, Fragment } from 'react';
import { Form, Row, Col, Divider, Modal, Table, Button } from 'antd';
import { constants } from '$utils';
import './index.scss';

export default class SpecialFeeDetailModal extends React.Component {
	constructor(props) {
		super(props);
	}

	render() {
		const { detail, visible, handleCancel } = this.props;
		const specialFeeDetailList = detail.specialFeeDetailList;
		const chargeDetailList = detail.chargeDetailList;
		const specialFeeDetailColumns = [
			{
				title: '用水性质',
				dataIndex: 'waterUseKindName',
				align: 'center'
			},
			{
				title: '水量',
				dataIndex: 'waterAmount',
				align: 'center'
			},
			{
				title: '是否计入累积',
				align: 'center',
				render: (text, record, index) => {
					if (record.accumulation) {
						return '是';
					} else {
						return '否';
					}
				}
			}
		];
		const chargeDetailColumns = [
			{
				title: '用水性质',
				dataIndex: 'waterUseKindType',
				align: 'center'
			},
			{
				title: '用水分类',
				dataIndex: 'waterUseKindName',
				align: 'center'
			},
			{
				title: '阶梯名称',
				dataIndex: 'ladderName',
				align: 'center'
			},
			{
				title: '阶梯用水量',
				dataIndex: 'waterAmount',
				align: 'center'
			},
			{
				title: '清水费',
				dataIndex: 'cleanWaterFee',
				align: 'center'
			},
			{
				title: '污水费',
				dataIndex: 'sewageFee',
				align: 'center'
			},
			{
				title: '水资源税',
				dataIndex: 'waterResourceFee',
				align: 'center'
			},
			{
				title: '其他费用',
				dataIndex: 'otherFee',
				align: 'center'
			}
		];
		return (
			<Modal className="special-fee-modal" title="特抄收费详情" visible={visible} onCancel={handleCancel} footer={null}>
				<Form {...constants.formItemLayout}>
					<Row>
						<Col>
							<h3>收费内容</h3>
						</Col>
					</Row>
					<Row className="main">
						<Table bordered columns={specialFeeDetailColumns} rowKey={() => Math.random()}
							dataSource={specialFeeDetailList} />
					</Row>
					<Divider dashed={true} />
					<Row>
						<Col>
							<h3>费用明细</h3>
						</Col>
					</Row>
					<Row className="main">
						<Table bordered columns={chargeDetailColumns} rowKey={() => Math.random()} dataSource={chargeDetailList} />
					</Row>
				</Form>
			</Modal>
		);
	}

}
