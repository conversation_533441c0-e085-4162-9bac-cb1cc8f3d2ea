import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { actionCreators } from './store';
import { connect } from 'react-redux';
import { Form, TreeSelect, Button, Row, Col, Modal, Input, Spin, Table, message } from 'antd';
import { constants } from '$utils';
import './index.scss';

const FormItem = Form.Item;
const { TreeNode } = TreeSelect;

class Index extends Component {

	constructor(props) {
		super(props);
		this.state = {
			loading: false,
			page: 1,
			pageSize: 10,
			searchForm: {
				hno: '',
				cno: '',
			  status:'0',
				name: '',
				address: '',
				areaId: ''
			},
			selectedRowKeys: [], // 选中的行数据
			selectedRows: []
		};
	}

	componentDidMount() {
		this.props.getAreaSelect();
	}

	// 获取列表数据
	getList = () => {
		const { page, pageSize, searchForm } = this.state;
		let params = {
			page,
			pageSize,
			...searchForm
		};
		this.props.handleList(params);
	};

	// 搜索
	handleSearch = () => {
		this.setState({
			page: 1
		}, () => this.getList());
	};

	// 提交
	handleSubmit = () => {
		const { onSubmit } = this.props;
		const { selectedRows } = this.state;
		if (selectedRows.length === 0) {
			message.error('请选择关联用户');
			return;
		}
		this.setState({ selectedRowKeys: [], selectedRows: [] });
		onSubmit(selectedRows);
	};

	// 重置
	handleReset = () => {
		this.setState({
			searchForm: {
				hno: '',
				cno: '',
				name: '',
				address: '',
				areaId: ''
			},
			page: 1
		}, () => {
			this.getList();
		});
	};

	// 取消
	handleCancel = () => {
		this.props.onCancel();
	};

	onSelectChange = (selectedRowKeys, selectedRows) => {
		this.setState({ selectedRowKeys, selectedRows });
	};

	// 列表分页
	handlePageChange = (page) => {
		this.setState({
			page
		}, () => {
			this.getList();
		});
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState({
			page: 1,
			pageSize: size
		}, () => {
			this.getList();
		});
	};

	//递归渲染
	_renderTreeNode = treeData => {
		return treeData.map(tree => {
			if (tree.children) {
				return (
					<TreeNode key={tree.id} title={tree.name} value={tree.id}>
						{this._renderTreeNode(tree.children)}
					</TreeNode>
				);
			} else {
				return <TreeNode key={tree.id} title={tree.name} value={tree.id}/>;
			}
		});
	};

	render() {
		const { loading, searchForm, selectedRowKeys, page, pageSize } = this.state;
		const { visible, data, areaSelect } = this.props;
		const rowSelection = {
			fixed: true,
			selectedRowKeys,
			onChange: this.onSelectChange
		};
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: data.total,
			showTotal: (total) => {
				return `共 ${total} 条数据 `;
			},
			onChange: (page) => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		const columns = [
			{
				title: '用户编号',
				dataIndex: 'cno',
				key: 'cno',
				align: 'center',
				render: text => <a>{text}</a>
			},
			{
				title: '户号',
				dataIndex: 'hno',
				key: 'hno',
				align: 'center'
			},
			{
				title: '用户名称',
				dataIndex: 'name',
				key: 'name',
				align: 'center'
			},
			{
				title: '用户地址',
				dataIndex: 'address',
				key: 'address',
				align: 'center'
			},
			{
				title: '水表类型',
				dataIndex: 'waterMeterType',
				key: 'waterMeterType',
				align: 'center'
			},
			{
				title: '水表种类',
				dataIndex: 'waterMeterKindType',
				key: 'waterMeterKindType',
				align: 'center'
			},
			{
				title: '片区',
				dataIndex: 'areaName',
				key: 'areaName',
				align: 'center'
			}
		];
		return (
			<Modal className='associatedUsers' title="关联用户" visible={visible} onCancel={this.handleCancel} footer={null}>
				<Row>
					<Form {...constants.formItemLayout}>

						<Row>
							<Col span={8}>
								<FormItem label='用户编号：'>
									<Input
										placeholder='请输入用户编号'
										value={searchForm.cno}
										onChange={v => {
											const { searchForm } = this.state;
											this.setState({
												searchForm: {
													...searchForm,
													cno: v.target.value
												}
											});
										}}
									/>
								</FormItem>
							</Col>
							<Col span={8}>
								<FormItem label='户号：'>
									<Input
										placeholder='请输入户号'
										value={searchForm.hno}
										onChange={v => {
											const { searchForm } = this.state;
											this.setState({
												searchForm: {
													...searchForm,
													hno: v.target.value
												}
											});
										}}
									/>
								</FormItem>
							</Col>
							<Col span={8}>
								<FormItem label='用户名称：'>
									<Input
										placeholder='请输入用户名称'
										value={searchForm.name}
										onChange={v => {
											const { searchForm } = this.state;
											this.setState({
												searchForm: {
													...searchForm,
													name: v.target.value
												}
											});
										}}
									/>
								</FormItem>
							</Col>
						</Row>

						<Row>
							<Col span={8}>
								<FormItem label='用户地址'>
									<Input
										placeholder='请输入用户地址'
										value={searchForm.address}
										onChange={v => {
											const { searchForm } = this.state;
											this.setState({
												searchForm: {
													...searchForm,
													address: v.target.value
												}
											});
										}}
									/>
								</FormItem>
							</Col>
							<Col span={8}>
								<FormItem label="用户片区：">
									<TreeSelect
										style={{ width: '100%' }}
										dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
										value={searchForm.areaId}
										placeholder="请选择片区"
										allowClear
										treeDefaultExpandedKeys={[100]}
										onChange={(areaId) => {
											this.setState({
												searchForm: {
													...searchForm,
													areaId: areaId
												}
											});
										}}
									>
										{
											areaSelect && this._renderTreeNode(areaSelect)
										}
									</TreeSelect>
								</FormItem>
							</Col>
						</Row>

						<Col span={16} align='right'>
							<Button type='primary' className='searchBtn' onClick={this.handleSearch}>搜索</Button>
							<Button type='default' className='searchBtn' onClick={this.handleReset}>重置</Button>
						</Col>
					</Form>
				</Row>
				<Spin spinning={loading}>
					<Table
						rowKey={record => record.id}
						rowSelection={rowSelection}
						columns={columns}
						dataSource={data.rows}
						pagination={paginationProps}
					/>
				</Spin>
				<Row>
					<Col align='center'>
						<Button type='primary' className='searchBtn' onClick={this.handleSubmit}>确定</Button>
						<Button type='default' className='searchBtn' onClick={this.handleCancel}>取消</Button>
					</Col>
				</Row>
			</Modal>
		);
	}
}

Index.propTypes = {
	visible: PropTypes.bool.isRequired, // 是否显示
	data: PropTypes.array.isRequired, // 列表数据
	handleList: PropTypes.func.isRequired, // 搜索方法
	onSubmit: PropTypes.func.isRequired, // 默认获取数据的方法
	onCancel: PropTypes.func.isRequired // 关闭Modal
};

Index.defaultProps = {
	visible: false, // 默认不显示
	data: {
		total: 0,
		page: 1,
		pageSize: 10,
		rows: []
	} // 默认空数组
};

// export default Index;

const mapStateToProps = state => {
	const data = state.get('associatedUsers');
	return {
		areaSelect: data.areaSelect // 片区选择框
	};
};
const mapDispatchToProps = dispatch => ({
	getAreaSelect: () => dispatch(actionCreators.getAreaSelect()) //获取片区选择框
});
export default connect(
	mapStateToProps,
	mapDispatchToProps
)(Form.create()((Index)));
