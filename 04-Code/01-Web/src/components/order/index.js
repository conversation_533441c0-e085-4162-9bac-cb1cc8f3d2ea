import './index.scss';

import React from 'react';

import http from '$http';
import {
	constants,
	showFields,
} from '$utils';
import {
	Button,
	Col,
	Descriptions,
	Form,
	Icon,
	Input,
	message,
	Modal,
	Row,
	Table,
	Tabs,
	Tag,
} from 'antd';

const FormItem = Form.Item;
const formItemLayout = {
	labelCol: {
		xs: { span: 6 },
		sm: { span: 6 }
	},
	wrapperCol: {
		xs: { span: 17 },
		sm: { span: 17 }
	}
};
const { TabPane } = Tabs;
const chargeDetailColumns = [
	{
		title: '用水性质',
		dataIndex: 'waterUseKindType',
		align: 'center'
	},
	{
		title: '用水分类',
		dataIndex: 'waterUseKindName',
		align: 'center'
	},
	{
		title: '阶梯名称',
		dataIndex: 'ladderName',
		align: 'center'
	},
	{
		title: '阶梯用水量',
		dataIndex: 'waterAmount',
		align: 'center'
	},
	{
		title: '单价',
		align: 'center',
		render: (text, record, index) => {
			let newWaterAmount = record.waterAmount === 0 ? 1 : record.waterAmount;
			return (((record.cleanWaterFee) + (record.sewageFee) + (record.waterResourceFee)) / newWaterAmount).toFixed(2);
		}
	},
	{
		title: '清水费',
		dataIndex: 'cleanWaterFee',
		align: 'center'
	},
	{
		title: '污水费',
		dataIndex: 'sewageFee',
		align: 'center'
	},
	{
		title: '水资源税',
		dataIndex: 'waterResourceFee',
		align: 'center'
	},
	{
		title: '其他费用',
		dataIndex: 'otherFee',
		align: 'center'
	}
];
const chargeOrderDetailColumns = [
	{
		title: '金额用途',
		dataIndex: 'detailType',
		align: 'center'
	},
	{
		title: '金额',
		dataIndex: 'detailAmount',
		align: 'center'
	},
	{
		title: '账单编号/卡号',
		dataIndex: 'targetNo',
		align: 'center'
	}
];
export default class OrderDetailModal extends React.Component {
	constructor(props) {
		super(props);
		this.state = {
			tabKey: 1,
			showUpdatePayIntervalModal: false,
			payInterval: null
		};
	}

	callback = tabKey => {
		this.setState({ tabKey });
	};
	updatePayIntervalModal () {
		this.setState({ showUpdatePayIntervalModal: true });
	}
	handlePayIntervalSubmit = () => {

		console.log(this.props);
		//修改缴费区间
		let param = {
			id: this.props.detail.id,
			customerId: this.props.detail.customerId,
			payInterval: this.state.payInterval
		};

		http.post(`api/charge/order/updatePayInterval`, param).then(res => {
			if (res.code === 0) {
				message.success('操作成功');
				this.setState({ showUpdatePayIntervalModal: false });
				this.props.getDetail(this.props.detail.id);
			}
		});
	};
	handleCancel = () => {
		this.setState({
			showUpdatePayIntervalModal: false
		});
	};

	render () {
		const { detail, visible, handleCancel } = this.props;
		const chargeDetailList = detail && detail.chargeDetailList;
		const chargeOrderDetailList = detail && detail.chargeOrderDetailList;
		// 状态颜色
		const statusColorMap = new Map();
		statusColorMap.set('已完成', '#87d068');
		statusColorMap.set('退款', '#f50');
		statusColorMap.set('红冲', '#f50');
		statusColorMap.set('已支付待刷卡', '#108ee9');
		return (
			detail && <Modal className="order-modal" title="订单详情" visible={visible} onCancel={handleCancel} footer={null}>
				<Tabs defaultActiveKey="1" onChange={this.callback}>
					<TabPane tab="订单信息" key="1">
						<Descriptions bordered>
							<Descriptions.Item label="订单编号：">{detail && detail.orderNo}</Descriptions.Item>
							<Descriptions.Item label="订单来源:">
								{detail && detail.orderSource === '红冲' ? (<span style={{ color: 'red' }}>
									{detail.orderSource}
								</span>) : detail.orderSource
								}
							</Descriptions.Item>
							<Descriptions.Item label="付款方式：">{detail && detail.chargeWay}</Descriptions.Item>
							<Descriptions.Item label="订单金额:">{detail && detail.orderAmount}</Descriptions.Item>
							<Descriptions.Item label="优惠金额：">{detail && detail.discountedAmount}</Descriptions.Item>
							<Descriptions.Item label="实收金额：">{detail && detail.actualAmount}</Descriptions.Item>
							<Descriptions.Item label="上次余额:">{detail && detail.deductionAmount}</Descriptions.Item>
							<Descriptions.Item label="本次余额:">{detail && detail.balanceAmount}</Descriptions.Item>
							<Descriptions.Item label="写卡金额：">{detail && detail.writeCardAmount}</Descriptions.Item>
							<Descriptions.Item label="平帐金额:">{detail && detail.dischargeAmount}</Descriptions.Item>
							<Descriptions.Item label="缴费时间:">{detail && detail.createTime}</Descriptions.Item>
							<Descriptions.Item label="写卡时间：">{detail && detail.writeCardTime}</Descriptions.Item>
							<Descriptions.Item label="账期:">{detail && detail.period}</Descriptions.Item>
							<Descriptions.Item label="小票状态:">{detail && detail.invoice === 1 ? '是' : '否'}</Descriptions.Item>
							<Descriptions.Item label="小票编号：">{detail && detail.receiptNo}</Descriptions.Item>
							<Descriptions.Item label="税票状态:">{detail && detail.taxInvoice}</Descriptions.Item>
							<Descriptions.Item label="订单状态:">
								{detail && (
									<Tag color={statusColorMap.get(detail.status)}>
										{detail.status}
									</Tag>
								)}
							</Descriptions.Item>
							<Descriptions.Item label="收费员：">{detail && detail.createPersonName}</Descriptions.Item>
							<Descriptions.Item label="设备ID：">{detail && detail.terminalName}</Descriptions.Item>
							<Descriptions.Item label="收费部门：">{detail && detail.departmentName}</Descriptions.Item>
							<Descriptions.Item label="凭证流水号：">{detail && detail.wireTransferNo}</Descriptions.Item>
							<Descriptions.Item label="水费票号:">{detail && detail.cleanNo}</Descriptions.Item>
							<Descriptions.Item label="污水票号:">{detail && detail.sewageNo}</Descriptions.Item>
							<Descriptions.Item label="缴费区间：">
								<div style={{ display: 'flex', justifyContent: 'space-between' }}>
									{showFields(detail.payInterval)}
									<Icon type="edit" style={{ cursor: 'pointer', textAlign: 'right', color: 'blue' }} onClick={() => this.updatePayIntervalModal()} />
								</div>
							</Descriptions.Item>
							<Descriptions.Item label="备注：" span={3}>{detail && detail.remark}</Descriptions.Item>
						</Descriptions>
					</TabPane>
					<TabPane tab="订单明细" key="2">
						<Table bordered columns={chargeDetailColumns} rowKey={() => Math.random()} dataSource={chargeDetailList} />
					</TabPane>
					<TabPane tab="金额去向" key="3">
						<Table bordered columns={chargeOrderDetailColumns} rowKey={() => Math.random()}
							dataSource={chargeOrderDetailList} />
					</TabPane>
				</Tabs>
				<Modal title="修改收费区间" visible={this.state.showUpdatePayIntervalModal} onCancel={this.handleCancel} footer={null}>
					<Row>
						{detail ? (
							<Form {...constants.formItemLayout}>
								<FormItem label="用户编号：">
									<Input disabled value={detail.cno} />
								</FormItem>
								<FormItem label="用户名称：">
									<Input disabled value={detail.customerName} />
								</FormItem>
								<FormItem label="原缴费区间：">
									<Input disabled value={detail.payInterval} />
								</FormItem>
								<FormItem label="新缴费区间：">
									<Input
										placeholder="请输入缴费区间"
										onChange={v => {
											this.setState({ payInterval: v.target.value });
										}}
									/>
								</FormItem>
							</Form>
						) : null}
						<Col align="center">
							<Button className="btn" type="primary" onClick={this.handlePayIntervalSubmit}>
								确定
							</Button>
							<Button className="btn" type="default" onClick={this.handleCancel}>
								取消
							</Button>
						</Col>
					</Row>
				</Modal>
			</Modal>

		);
	}
}
