import React, { Component } from 'react';
import { Tooltip, Icon, Avatar, Dropdown, Menu, Badge, Modal, Form, Button, Col, Row, Input } from 'antd';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
import { actionCreators } from './store';
import { routes } from '@/router/routes';
import { constants } from '$utils';
import FullScreen from '@/components/FullScreen';
import Tags from './Tags';
import BasicDrawer from '@/components/BasicDrawer';
import {getInversionCount} from "./store/actionCreators";
import http from '$http';

const FormItem = Form.Item;

class TopHeader extends Component {
	state = { visible: false, modalVisible: false };
	handleLogout = () => {
		this.props.setUserInfo({});
		this.props.emptyTag();
		localStorage.removeItem('token');
		localStorage.removeItem('ISL');
		localStorage.removeItem('usi');
		localStorage.removeItem('pmn');
		localStorage.removeItem('bh');
		localStorage.removeItem('bhl');
		this.props.history.push('/');
		this.props.history.go();
	};
	componentDidMount() {
		let userInfo = localStorage.getItem('usi') && JSON.parse(localStorage.getItem('usi'));
		if (userInfo) {
			this.props.setUserInfo(userInfo);
		} else {
			this.props.setUserInfo({});
			this.props.history.push('/login');
		}

		this.getNews()
		this.props.getInversionCount()
	}

	// TODO: 开发周期暂时取消消息提醒，后期可考虑长连接进行消息实时通知
	getNews = () => {
		// setInterval(() => this.props.unread(), 10000);
	};

	toNews = () => {
		this.handClickTag('/news');
		this.props.history.push('/news');
	};

	handClickTag(path, parent) {
		for (let i = 0; i < routes.length; i++) {
			if (path === routes[i].path) {
				let obj = { path, title: '消息', component: routes[i].component };
				this.props.addTag(parent ? Object.assign({}, obj, { parent: parent.title }) : obj);
			}
		}
	}

	toggle = () => {
		this.props.setCollapse(!this.props.isCollapsed);
	};

	appDownload = () => {
		window.open(`${process.env.UPLOAD_ROOT}/app.png`);
	};

	onLineHelpDownload = () => {
		window.open(`http://help.everqin.com/%E8%BE%BE%E6%8B%89%E7%89%B9%E6%97%97/index/`);
	};
	handleDownload = () => {
		const url = `${process.env.API_ROOT}/api/wm/watermeter/abnormal/getInversionExport`
		http.export(url, {}, (res) => {
			const fileName = '远传表倒装数据_' + new Date().getTime() + '.xlsx';
			const blob = new Blob([res]);
			if (window.navigator && window.navigator.msSaveOrOpenBlob){
				window.navigator.msSaveOrOpenBlob(blob, fileName)
				return
			}
			const downloadElement = document.createElement('a');
			const href = window.URL.createObjectURL(blob);
			downloadElement.href = href;
			downloadElement.download = fileName;
			document.body.appendChild(downloadElement);
			downloadElement.click();
			document.body.removeChild(downloadElement);
			window.URL.revokeObjectURL(href);
		})
	}
	helpDownload = () => {
		window.open(`${process.env.UPLOAD_ROOT}/manual.docx`);
	};

	installDownload = () => {
		window.open(`${process.env.UPLOAD_ROOT}/soft_install.zip`);
	};
	setting = () => {
		this.setState({ visible: true });
	};

	onClose = () => {
		this.setState({ visible: false });
	};

	onChangeTags = checked => {
		this.props.setTags(checked);
		localStorage.setItem('tags', checked);
		this.onClose();
	};

	onChangeBreadCrumb = checked => {
		this.props.setBreadCrumb(checked);
		localStorage.setItem('breadCrumb', checked);
		this.onClose();
	};

	onChangeTheme = checked => {
		this.props.setTheme(checked ? 'dark' : 'light');
		localStorage.setItem('theme', checked ? 'dark' : 'light');
		this.onClose();
	};

	// 修改密码
	handleModifyPassword = () => {
		this.setState({
			modalVisible: true
		});
	};

	// 取消弹窗
	handleCancel = () => {
		this.setState({
			modalVisible: false
		});
	};

	// 修改密码
	handleSubmit = (e) => {
		e.preventDefault();
		this.props.form.validateFields((err, values) => {
			if (!err) {
				this.props.modifyPassword(values)
			}
		});
		this.handleCancel()
	}

	_renderModifyPassword = () => {
		const { modalVisible } = this.state;
		const { form } = this.props;
		const { getFieldDecorator } = form;
		return (
			<Modal title="修改密码" visible={modalVisible} onCancel={this.handleCancel} footer={null}>
				<Form {...constants.formItemLayout}>
					<Row>
						<Col span={24}>
							<FormItem label="原密码：">
								{getFieldDecorator('oldPwd', {
									initialValue: '',
									rules: [
										{
											required: true,
											message: '请输入原密码'
										}
									]
								})(<Input palceholder="请输入原密码" type={'password'} />)}
							</FormItem>
						</Col>
						<Col span={24}>
							<FormItem label="新密码：">
								{getFieldDecorator('newPwd', {
									initialValue: '',
									rules: [
										{
											required: true,
											message: '请输入新密码'
										}
									]
								})(<Input palceholder="请输入新密码" type={'password'} />)}
							</FormItem>
						</Col>
					</Row>
					<Row>
						<Col span={24} align="center">
							<Button type="primary" className="btn" onClick={this.handleSubmit}>
								提交
						</Button>
							<Button type="default" className="btn" onClick={this.handleCancel}>
								取消
						</Button>
						</Col>
					</Row>
				</Form>
			</Modal>
		)
	}

	render() {
		const DropdownList = (
			<Menu className="drop-list">
				<Menu.Item key="user">
					<Icon type="user" />
					{Object.keys(this.props.userInfo).length > 0 && this.props.userInfo.personName}
				</Menu.Item>
				<Menu.Item key="password" onClick={this.handleModifyPassword}>
					<Icon type="profile" />
					修改密码
				</Menu.Item>
				<Menu.Item key="logout" onClick={this.handleLogout}>
					<Icon type="logout" />
					退出登录
				</Menu.Item>
			</Menu>
		);
		const { showTags, count, newsCount, inversionCount } = this.props;
		const personName = this.props.userInfo.personName;
		return (
			<div className="top-header">
				<div className="top-header-inner">
					<Icon className="trigger" type={true ? 'menu-unfold' : 'menu-fold'} onClick={this.toggle} />
					<div className="header-title">智慧水务管理平台</div>
					<div className="header-right">
						<div className="setting">
							<Badge count={inversionCount} overflowCount={999}>
								<Tooltip placement="bottom" title={'水表倒装'}>
										<Icon style={{ fontSize: '21px' }} type="dashboard" onClick={this.handleDownload}/>
								</Tooltip>
							</Badge>
						</div>
						<div className="setting">
							<Tooltip placement="bottom" title={'在线帮助'}>
								<Icon style={{ fontSize: '21px', cursor: 'pointer' }} type="reddit" onClick={this.onLineHelpDownload} />
							</Tooltip>
						</div>
						<div className="setting">
							<Tooltip placement="bottom" title={'app下载'}>
								<Icon style={{ fontSize: '21px', cursor: 'pointer' }} type="qrcode" onClick={this.appDownload} />
							</Tooltip>
						</div>
						<div className="setting">
							<Tooltip placement="bottom" title={'帮助手册下载'}>
								<Icon style={{ fontSize: '21px', cursor: 'pointer' }} type="question-circle" onClick={this.helpDownload} />
							</Tooltip>
						</div>
						<div className="setting">
							<Tooltip placement="bottom" title={'安装文件下载'}>
								<Icon style={{ fontSize: '21px', cursor: 'pointer' }} type="tool" onClick={this.installDownload} />
							</Tooltip>
						</div>

						<div className="full-screen">
							<FullScreen />
						</div>
						<div className="setting">
							<Icon style={{ fontSize: '21px', cursor: 'pointer' }} type="setting" onClick={this.setting} />
						</div>
						<div className="news-wrap">
							<Badge count={count}>
								<Icon style={{ fontSize: '21px', cursor: 'pointer' }} type="bell" onClick={this.toNews} />
							</Badge>
						</div>
						<div id="dropdown-wrap">
							<Dropdown getPopupContainer={() => document.getElementById('dropdown-wrap')} overlay={DropdownList}>
								<Row style={{ width: 120 }}>
									<Col span={10} style={{ paddingTop: 6 }}>
										{personName}
									</Col>
									<Col span={12}>
										<Avatar
											size="large"
											src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACIAAAAiCAYAAAA6RwvCAAADwElEQVRYR7WXTWwTVxDH/7Pe9Qf+ahyaOAmBUKeBAyqXSlUrUM/0A6lHxAWE1PbSG+IIoceKG5e2EqKXimMlWtpzRdWqgkurHiDEJTgkcQJx8Bf+2I9Bb8kG2/H6PRL7XfbwZub9dmbezDzCa6z5fH7EqTQ/AuFDZhwBMAUgvmmiDGCBCP+B8bsWC/46nU6vqZonFcFsdvmYCesCHJwggq6iwwwLGn4zoH+TyYz/IdPpCXJvIX9Qs82rzPyxzFCvfSK65QSMrw5PpR/6yfmCzGWXTjFb3wHkuX43LAC4TKR/MZOZuNHNUFeQ+9nFiwTMMrNS6FQJiYgZmD2Umfy6U2fbQQICzJdVje9IjuhSJ0wbiAgH4PzYb09s+3siBrTTrWHaAhGJSVbjH5WcCGga3kjGEI1GEDReXqKmaaFareFZsQLbcRQcxWXWQ0e9BN4Cmcsu/qJyO2LRCEZHUhAw3ZaAWF0roFKtSWHEbZrJTH4iBF0QUScstm7LNAXE2OgwiHrnMDNjZXVdCUYn/bioM67Fe9ncTWJ82gtEeGDqwJivJzp1hWcWHq1Iw8SEnw9n9p8kUbatcnNJVjGHhxIYTiVlTmvbXy8Usb5R6qkjKrAeD07Q3HzuDAPXZSfs3zeKcCgoE2vbrzeayD1eleoQcJbmsrnrzDgjk54+OAHNJ0H9dB3HwfzDJZlpEOEHuj+fuwPgXZn0IEEA3BUgTwDslYEMMjQAngqQOoCQDGRQybp5bkMZZFDXtxVEKTRCYRAFbRPEDY1Ssnqh62eJb0mHu8rXtzWH+tP0Xll0r69qQZMl82723YKmWuJ3c1AvXbfEU3BCuel5xiLhEEIhA8GgAT0Q2OrEouNato1m00SjYaJWbyixbzU9lTFAtP3UUALJeBS6HlA6wLJsFMtVFDZKEJB+q20MEEJ+g5Fh6Ng39ibEdyfLNC08XnkC8e1c2wYjIdBtVBRNbmoyrewFP1DhnYXFPEQTfLV8RsWXXmkfnkf2DrmzaT+WmGXXnm64psSzwnd49g7znhOaRnjrwPhrt34/aOGN/x8tw3FYkPR+TrTCxPZEZsfTw319YC3n17nyvKb2wPJgStXquWg48q2m0c6ytMM1jsNWtV77MhGNXuvmtZ5/XCgU3onHE9/reuC93eSJZdl/l8ulz1Op1L9+dpRcXyo9/ywcNs4bhv6+9wRRAGPTtP6q180ricSen2TySiCekbVi8e2YYZwMGMZxAj4IaNoQkeZWOGbHth1ng4E/bdO8XTHNmyPJ5AMZgLf/At/j2PAbUsw0AAAAAElFTkSuQmCC"
										/>
									</Col>
									<Col span={2} style={{ paddingTop: 6 }}>
										<Icon style={{ color: 'rgba(0,0,0,.3)', cursor: 'pointer' }} type="caret-down" />
									</Col>
								</Row>
							</Dropdown>
						</div>
					</div>
				</div>
				{showTags ? <Tags /> : null}
				<BasicDrawer title="系统设置" closable onClose={this.onClose} visible={this.state.visible} onChangeTags={this.onChangeTags} onChangeBreadCrumb={this.onChangeBreadCrumb} onChangeTheme={this.onChangeTheme} {...this.props} />
				{this._renderModifyPassword()}
			</div>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('layout');
	return {
		userInfo: data.userInfo,
		isCollapsed: data.isCollapsed,
		showTags: data.showTags,
		type: data.type,
		count: data.count,
		inversionCount: data.inversionCount
	}
};
const mapDispatchToProps = dispatch => ({
	setCollapse: data => {
		dispatch(actionCreators.setCollapse(data));
	},
	setUserInfo: data => {
		dispatch(actionCreators.setUserInfo(data));
	},
	emptyTag: () => {
		dispatch(actionCreators.emptyTag());
	},
	addTag: data => {
		dispatch(actionCreators.addTag(data));
	},
	setBreadCrumb: data => {
		dispatch(actionCreators.setBreadCrumb(data));
	},
	setTags: data => {
		dispatch(actionCreators.setTags(data));
	},
	setTheme: data => {
		dispatch(actionCreators.setTheme(data));
	},
	unread: () => dispatch(actionCreators.unread()),
	modifyPassword: (data) => dispatch(actionCreators.modifyPassword(data)),
	getInversionCount: (data) => dispatch(actionCreators.getInversionCount(data)),
});
export default connect(
	mapStateToProps,
	mapDispatchToProps
)(withRouter(Form.create()(TopHeader)));
