import * as actionTypes from './constants';
const defaultState = {
	fields: null,
  showBreadCrumb: localStorage.getItem('breadCrumb') ? localStorage.getItem('breadCrumb') : false,
  showTags: localStorage.getItem('tags') ? localStorage.getItem('tags') : false,
  type: localStorage.getItem('theme') ? localStorage.getItem('theme') : 'dark', // 页面风格
	isCollapsed: false,
	userInfo: {}, // 当前登录用户信息
	tagList: [], // 标签列表
	loading: false, //
	count: 0,
};

export default (state = defaultState, action) => {
	switch (action.type) {
		case actionTypes.SHOW_LOADING:
			state.loading = action.data;
			return { ...state };
		case actionTypes.SET_USERINFO:
			state.userInfo = action.data;
			return { ...state };
		case actionTypes.SET_BREADCRUMB:
			state.showBreadCrumb = action.data;
			return { ...state };
		case actionTypes.SET_TAGS:
			state.showTags = action.data;
			return { ...state };
		case actionTypes.SET_THEME:
			state.type = action.data;
			return { ...state };
		case actionTypes.SET_COLLAPSE:
			state.isCollapsed = action.data;
			return { ...state };
		case actionTypes.ADD_TAG:
			let arr = null;
			if (JSON.stringify(state.tagList).indexOf(action.data.path) > -1) {
				arr = [...state.tagList];
			} else {
				arr = [...state.tagList, action.data];
			}
			state.tagList = arr;
			return { ...state };
		case actionTypes.REMOVE_TAG:
			state.tagList = state.tagList.filter(item => {
				if (item.path !== action.data) {
					return item;
				}
			});
			return { ...state };
		case actionTypes.EMPTY_TAG:
			state.tagList = [];
			return { ...state };
		case actionTypes.UNREAD_TOTAL:
			state.count = action.data;
			return { ...state };
		case actionTypes.MODIFY_PASSWORD:
			return { ...state };
		default:
			return state;
	}
}
