import { combineReducers } from 'redux-immutable';

// 全局框架
import { reducer as layoutReducer } from '@/layout/store';

// 通用组件
// 关联用户
import { reducer as associatedUsersReducer } from '../components/associatedUsers/store';

// 首页
import { reducer as homeReducer } from '@/views/home/<USER>';

// 数据大屏
import { reducer as bigScreenReducer } from '@/views/bigScreen/store';

// 开户管理
// 预开户
import { reducer as preReducer } from '@/views/account/modules/pre/store';
// 用户开户
import { reducer as accountReducer } from '@/views/account/modules/user/store';
//批量开户
import { reducer as batchReducer } from '@/views/account/modules/batch/store'

// 收费管理
// 柜台缴费
import { reducer as paymentReducer } from '@/views/charge/modules/payment/store';
// 小额借记
import { reducer as pettyDebitReducer } from '@/views/charge/modules/pettyDebit/store';
// 小额借记
import { reducer as withholdReducer } from '@/views/charge/modules/withhold/store';
// 订单管理
import { reducer as orderReducer } from '@/views/charge/modules/order/store';
// 订单退款记录
import { reducer as orderRefundRecordReducer } from '@/views/charge/modules/orderRefundRecord/store';
// 订单发票打印记录
import { reducer as orderInvoiceRecordReducer } from '@/views/charge/modules/orderInvoiceRecord/store';
// 特抄收费
import { reducer as specialReducer } from '@/views/charge/modules/special/store';
// 特抄收费记录
import { reducer as specialRecordReducer } from '@/views/charge/modules/specialRecord/store';
// 特抄收费发票打印记录
import { reducer as specialRecordInvoiceRecordReducer } from '@/views/charge/modules/specialRecordInvoiceRecord/store';
// 打印欠费通知单
import { reducer as oweNoticePrintReducer } from '@/views/charge/modules/oweNoticePrint/store';

// 用户管理
// 用户信息
import { reducer as infosReducer } from '@/views/users/modules/infos/store';
// 产权人信息
import { reducer as ownersReducer } from '@/views/users/modules/owners/store';
// 低保户管理
import { reducer as poorReducer } from '@/views/users/modules/poor/store';
// 特困户管理
import { reducer as subsistenceReducer } from '@/views/users/modules/subsistence/store';
// 小额借记信息
import { reducer as debitReducer } from '@/views/users/modules/debit/store';
// 托收代扣信息
import { reducer as withHoldingReducer } from '@/views/users/modules/withHolding/store';
// 发票信息
import { reducer as invoiceReducer } from '@/views/users/modules/invoice/store';
// 增值税资料管理
import { reducer as userTaxReducer } from '@/views/users/modules/tax/store';
// 用户信息变更记录
import { reducer as infoChangeReducer } from '@/views/users/modules/infoChange/store';
// 同户管理
import { reducer as sameHouseholdReducer } from '@/views/users/modules/sameHousehold/store';
// 换表/拆表/复装
import { reducer as waterMeterCRDReducer } from '@/views/users/modules/waterMeterCRD/store';
// 换表记录
import { reducer as waterMeterChangeReducer } from '@/views/users/modules/waterMeterChange/store';
// 拆表记录
import { reducer as dismantleReducer } from '@/views/users/modules/dismantle/store';
// 复装记录
import { reducer as waterMeterReloadReducer } from '@/views/users/modules/waterMeterReload/store';
// 批量销户
import { reducer as cancelAccountReducer } from '@/views/users/modules/cancelAccount/store';
// 销户记录
import { reducer as cancellationReducer } from '@/views/users/modules/cancellation/store';
// 提现记录
import { reducer as withdrawReducer } from '@/views/users/modules/withdraw/store';
// 人口核增记录
import { reducer as addPopulationRecord } from '@/views/users/modules/addPopulationRecord/store';

// IC卡管理
// 补卡
import { reducer as replacementReducer } from '@/views/ic/modules/replacement/store';
// 补卡记录
import { reducer as replacementRecordReducer } from '@/views/ic/modules/replacementRecord/store';
// 管理卡制作
import { reducer as managementCardReducer } from '@/views/ic/modules/managementCard/store';
// IC卡终端管理
import { reducer as terminalReducer } from '@/views/ic/modules/terminal/store';
// 卡表查表
import { reducer as checkRecordReducer } from '@/views/ic/modules/checkRecord/store';

// 远传表管理
// 远传表用户
import { reducer as remoteUsersReducer } from '@/views/remote/modules/users/store';
// 集中器管理
import { reducer as concentratorReducer } from '@/views/remote/modules/concentrator/store';
// 开关阀记录
import { reducer as onOffRecordReducer } from '@/views/remote/modules/onOffRecord/store';
// 远传表账单
import { reducer as remoteBillReducer } from '@/views/remote/modules/bill/store';
// 远传表数据
import { reducer as datasReducer } from '@/views/remote/modules/datas/store';
// 账单发票打印记录
import { reducer as remoteBillPrintInvoiceRecordReducer } from '@/views/remote/modules/billPrintInvoiceRecord/store';
// 远传表账单核减记录
import { reducer as remoteBillMinusRecordReducer } from '@/views/remote/modules/billMinusRecord/store';
import { reducer as billElectronicRecordReducer } from '@/views/remote/modules/billElectronicRecord/store';


// 机械表管理
// 机械表用户
import { reducer as mechanicalUsersReducer } from '@/views/mechanical/modules/users/store';
// 抄表任务
import { reducer as amrReducer } from '@/views/mechanical/modules/amr/store';
// 机械表账单
import { reducer as mechanicalBillReducer } from '@/views/mechanical/modules/bill/store';
// 账单调整记录
import { reducer as billAdjustmentRecordReducer } from '@/views/mechanical/modules/billAdjustmentRecord/store';
// 账单核减记录
import { reducer as billMinusRecordReducer } from '@/views/mechanical/modules/billMinusRecord/store';
// 账单发票打印记录
import { reducer as mechanicalBillPrintInvoiceRecordReducer } from '@/views/mechanical/modules/billPrintInvoiceRecord/store';
// 抄表轨迹
import { reducer as trackReducer } from '@/views/mechanical/modules/track/store';
import { reducer as mcBillElectronicRecordReducer } from '@/views/mechanical/modules/mcBillElectronicRecord/store';

// 水表管理
// 水表信息
import { reducer as waterMeterReducer } from '@/views/waterMeter/store'

// 系统设置
// 抄表周期配置
import { reducer as transcribeCycleReducer } from '@/views/system/modules/transcribeCycle/store';
// 抄表员管理
import { reducer as transcriberReducer } from '@/views/system/modules/transcriber/store';
// 审批流配置
import { reducer as auditReducer } from '@/views/system/modules/audit/store';
// 审批流角色配置
import { reducer as auditRoleReducer } from '@/views/system/modules/auditRole/store';
// 水司银行账户信息管理
import { reducer as bankInfoReducer } from '@/views/system/modules/bankInfo/store';
// 水司税务信息管理
import { reducer as taxInfoReducer } from '@/views/system/modules/taxInfo/store';
// 片区管理
import { reducer as areasReducer } from '@/views/system/modules/areas/store';
// 员工管理
import { reducer as staffReducer } from '@/views/system/modules/staff/store';
// 角色管理
import { reducer as rollReducer } from '@/views/system/modules/roll/store';
// 部门管理
import { reducer as departReducer } from '@/views/system/modules/depart/store';
// 部门领导管理
import { reducer as departLeaderReducer } from '@/views/system/modules/departLeader/store';
// 用水性质
import { reducer as waterQualityReducer } from '@/views/system/modules/waterQuality/store';
// 数据字典
import { reducer as dictionaryReducer } from '@/views/system/modules/dictionary/store';
// 系统参数
import { reducer as parametersReducer } from '@/views/system/modules/parameters/store';
// 模块管理
import { reducer as modulesReducer } from '@/views/system/modules/modules/store';
// 开户银行管理
import { reducer as banksReducer } from '@/views/system/modules/banks/store';

// 票据管理
// 票据领用
import { reducer as receiptReducer } from '@/views/bill/modules/receipt/store';
// 领用记录
import { reducer as receiptRecordReducer } from '@/views/bill/modules/receiptRecord/store';
// 开具记录
import { reducer as issuingRecordReducer } from '@/views/bill/modules/issuingRecord/store';
// 票据模板
import { reducer as templateReducer } from '@/views/bill/modules/template/store';

import { reducer as electronicRecordReducer } from '@/views/charge/modules/electronicRecord/store';

// 发送短信
import { reducer as sendSmsReducer } from '@/views/message/modules/sendSms/store';
//发送短信记录
import { reducer as sendRecordReducer } from '@/views/message/modules/sendRecord/store';

// 公告消息
// 公告列表 、 发布公告
import { reducer as announcementReducer } from '@/views/announcement/store';
import { reducer as newsReducer } from '@/views/news/store'; // 消息中心

// 流程管理
//统计报表
import { reducer as chargerReportReducer } from '@/views/reportForm/modules/chargeReport/store';
// 我的申请
import { reducer as applyReducer } from '@/views/process/modules/apply/store';
// 我的审批
import { reducer as approvalReducer } from '@/views/process/modules/approval/store';
// 申请查询
import { reducer as applyQueryReducer } from '@/views/process/modules/applyQuery/store';
import OrderRefundRecord from "../views/charge/modules/orderRefundRecord";
// 用水性质变更记录
import { reducer as waterUseKindChange } from '@/views/users/modules/waterUseKindChange/store';

const reducer = combineReducers({
	// 全局框架
	layout: layoutReducer,

	// 通用组件
	associatedUsers: associatedUsersReducer, // 关联用户

	// 首页
	home: homeReducer,
	// 数据大屏
	bigScreen: bigScreenReducer,
	// 开户管理
	pre: preReducer, // 预开户
	account: accountReducer, // 用户开户
	batch: batchReducer,    //批量开户
	// 收费管理
	payment: paymentReducer,	// 柜台缴费
	pettyDebit: pettyDebitReducer, //小额借记
	withhold: withholdReducer, //小额借记
	order: orderReducer, // 订单管理
	orderRefundRecord: orderRefundRecordReducer, // 订单退款记录
	orderInvoiceRecord: orderInvoiceRecordReducer, // 订单发票打印记录
	electronicRecord: electronicRecordReducer, // 电子发票
	special: specialReducer, // 特抄收费
	specialRecord: specialRecordReducer, // 特抄收费记录
	specialRecordInvoiceRecord: specialRecordInvoiceRecordReducer, // 特抄收费发票打印记录
	oweNoticePrint: oweNoticePrintReducer, // 打印欠费通知单
	// 用户管理
	infos: infosReducer, // 用户信息
	owners: ownersReducer, // 产权人信息
	poor: poorReducer, // 低保户管理
	subsistence: subsistenceReducer, // 特困户管理
	debit: debitReducer, // 小额借记信息
	withHolding: withHoldingReducer, // 代扣信息
	userTax: userTaxReducer, // 增值税资料管理
	invoice: invoiceReducer, // 发票信息
	infoChange: infoChangeReducer, // 用户信息变更记录
	sameHousehold: sameHouseholdReducer, // 同户管理
	waterMeterCRD: waterMeterCRDReducer, // 换表/拆表/复装
	waterMeterChange: waterMeterChangeReducer, // 换表记录
	dismantle: dismantleReducer, // 拆表记录
	waterMeterReload: waterMeterReloadReducer, // 复装记录
	cancelAccount: cancelAccountReducer, // 批量销户
	cancellation: cancellationReducer, // 销户记录
	withdraw: withdrawReducer, // 销户记录
	addPopulationRecord: addPopulationRecord, // 人口核增记录
	// IC卡管理
	replacement: replacementReducer, // 补卡
	replacementRecord: replacementRecordReducer, // 补卡记录
	managementCard: managementCardReducer, // 管理卡制作
	terminal: terminalReducer, // IC卡终端管理
	checkRecord: checkRecordReducer, // 卡表查表记录
	// 远传表管理
	remoteUsers: remoteUsersReducer, // 远传表用户
	concentrator: concentratorReducer, // 集中器管理
	onOffRecord: onOffRecordReducer, // 开关阀记录
	remoteBill: remoteBillReducer, // 远传表账单
	remoteDatas: datasReducer, // 远传表数据
	remoteBillPrintInvoiceRecord: remoteBillPrintInvoiceRecordReducer, // 账单发票打印记录
	remoteBillMinusRecord: remoteBillMinusRecordReducer, // 远传表账单核减记录
	billElectronicRecord: billElectronicRecordReducer, // 电子发票
	// 机械表管理
	mechanicalUsers: mechanicalUsersReducer, // 机械表用户
	amr: amrReducer, // 抄表任务
	mechanicalBill: mechanicalBillReducer, // 机械表账单
	billAdjustmentRecord: billAdjustmentRecordReducer, // 账单调整记录
	billMinusRecord: billMinusRecordReducer, // 账单核减记录
	mechanicalBillPrintInvoiceRecord: mechanicalBillPrintInvoiceRecordReducer, // 账单发票打印记录
	track: trackReducer, // 抄表轨迹
	mcBillElectronicRecord: mcBillElectronicRecordReducer, // 电子发票
	// 水表管理
	waterMeter: waterMeterReducer, // 水表信息
	// 系统设置
	transcribeCycle: transcribeCycleReducer, // 抄表周期配置
	transcriber: transcriberReducer, // 抄表员管理
	audit: auditReducer, // 审批流配置
	auditRole: auditRoleReducer, // 审批流角色配置
	bankInfo: bankInfoReducer, // 水司银行账户信息管理
	taxInfo: taxInfoReducer, // 水司税务信息管理
	areas: areasReducer, // 片区管理
	staff: staffReducer, // 员工管理
	roll: rollReducer, // 角色管理
	depart: departReducer, // 部门管理
	departLeader: departLeaderReducer, //部门领导管理
	waterQuality: waterQualityReducer, // 用水性质
	dictionary: dictionaryReducer, // 字典管理
	parameters: parametersReducer, // 系统参数
	modules: modulesReducer, // 模块管理
	banks: banksReducer, // 开户银行管理
	// 票据管理
	receipt: receiptReducer, // 票据领用
	receiptRecord: receiptRecordReducer, // 领用记录
	issuingRecord: issuingRecordReducer, // 开具记录
	template: templateReducer, // 票据模板
	// 消息管理
	sendSms: sendSmsReducer, // 发送短信
	sendRecord: sendRecordReducer, // 发送记录
	news: newsReducer, // 消息中心
	// 公告消息
	announcement: announcementReducer,// 公告列表 、 发布公告
	// 流程管理
	apply: applyReducer, // 我的申请
	approval: approvalReducer, // 我的审批
	applyQuery: applyQueryReducer, // 我的审批
	//统计报表
	chargerReport:chargerReportReducer,    //收费报表
	waterUseKindChange: waterUseKindChange, // 用水性质变更记录
});


export default reducer;
