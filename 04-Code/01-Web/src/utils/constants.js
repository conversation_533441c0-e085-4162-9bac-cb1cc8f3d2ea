import Qinhuangdao from '@/assets/json/qinhuangdao.json'; //秦皇岛
import JingDu from '@/assets/json/jiangdu.json'; // 江都
import <PERSON><PERSON><PERSON><PERSON> from '@/assets/json/ewenkeqi.json'; // 鄂温克旗

const constants = {
	fileUrl: process.env.UPLOAD_ROOT,
	page: 1, // 全局page
	pageSize: 10, // 全局pageSize
	// 全局搜索表单布局
	formItemLayout: {
		labelCol: {
			xs: { span: 6 },
			sm: { span: 6 }
		},
		wrapperCol: {
			xs: { span: 15 },
			sm: { span: 15 }
		}
	},
	// 用水性质
	waterQualityMap: [
		{
			label: '居民用水',
			value: 0
		},
		{
			label: '非居民用水',
			value: 1
		},
		{
			label: '特种行业用水',
			value: 2
		}
	],
	// 阶梯类型
	ladderTypeMap: [
		{
			label: '非阶梯',
			value: 0
		},
		{
			label: '月阶梯',
			value: 1
		},
		{
			label: '年阶梯',
			value: 2
		}
	],
	// 用户状态
	customerStatus: [
		{
			label: '正常',
			value: 0
		},
		{
			label: '异常',
			value: 1
		},
		{
			label: '拆表',
			value: 2
		},
		{
			label: '销户',
			value: 4
		},
		{
			label: '待销户',
			value: 3
		},
			{
					label: '报停',
					value: 5
			},



	],
	// 低保户状态
	subsistenceStatus: [
		{
			label: '全部',
			value: null
		},
		{
			label: '正常',
			value: 0
		},
		{
			label: '未生效',
			value: 1
		},
		{
			label: '已过期',
			value: 2
		}
	],
	// 水表类型
	waterMeterType: [
		{
			label: 'IC卡表',
			value: 0
		},
		{
			label: '机械表',
			value: 1
		},
		{
			label: '远传表',
			value: 2
		}
	],
	// 水表种类
	waterMeterKinds: [
		{
			label: '机械表',
			value: 0
		},
		{
			label: '有线远传',
			value: 1
		},
		{
			label: '无线远传',
			value: 2
		}
	],
	// 水表分类
	meterType: [
		{
			label: '单表',
			value: 0
		},
		{
			label: '总表',
			value: 1
		},
		{
			label: '分表',
			value: 2
		},
		{
			label: '虚表',
			value: 3
		}
	],
	// 付款方式
	payWay: [
		{
			label: '现金',
			value: 0
		},
		{
			label: '小额借记',
			value: 1
		},
		{
			label: '代扣',
			value: 2
		}
	],
	payWayUpdate: [
		{
			label: '现金',
			value: 0
		},
		{
			label: 'POS机',
			value: 2
		},
		{
			label: '电汇',
			value: 3
		}
	],
	// 用户状态
	userStatus: [
		{
			label: '正常',
			value: 0
		},
		{
			label: '异常',
			value: 1
		},
		{
			label: '拆表',
			value: 2
		}
	],
	// 水表厂家
	waterMeterManufacturer: [
		{
			label: '请选择',
			value: null
		},
	  {
			label: '华旭',
			value: 12
	  },
	  {
			label: '建源',
			value: 13
	  },
		{
			label: '扬州恒信',
			value: 0
		},
		{
			label: '济南瑞泉',
			value: 1
		},
		{
			label: '河南新天',
			value: 2
		},
		{
			label: '山东冠翔',
			value: 3
		},
		{
			label: '唐山汇中',
			value: 4
		},
		{
			label: '江西三川',
			value: 5
		},
		{
			label: '海湾',
			value: 6
		},
		{
			label: '其他',
			value: 7
		}
	],
	// 水表位置
	installLocation: [
		{
			label: '厨房',
			value: 0
		},
		{
			label: '卫生间',
			value: 1
		},
		{
			label: '储物间',
			value: 2
		},
		{
			label: '户外',
			value: 3
		},
		{
			label: '阁楼',
			value: 4
		},
		{
			label: '其他',
			value: 5
		}
	],
	// 用户类型
	userType: [
		{
			label: '正常',
			value: 0
		},
		{
			label: '低保',
			value: 1
		},
		{
			label: '特困',
			value: 2
		}
	],
	// 产权人状态
	ownerStatus: [
		{
			label: '全部',
			value: null
		},
		{
			label: '已生效',
			value: 1
		},
		{
			label: '未生效',
			value: 0
		}
	],
	// 增值税状态
	taxStatus: [
		{
			label: '启用',
			value: 0
		},
		{
			label: '停用',
			value: 1
		}
	],
	// 账户状态
	accountStatus: [
		{
			label: '启用',
			value: 0
		},
		{
			label: '禁用',
			value: 1
		}
	],
	// 分摊方式
	apportionType: [
		{
			label: '分摊比例',
			value: 0
		},
		{
			label: '固定量',
			value: 1
		}
	],
	// 是否定量
	fixedQuantity: [
		{
			label: '否',
			value: 0
		},
		{
			label: '是',
			value: 1
		}
	],
	SpecialFeeStatusEnum: [
		{
			label: '请选择',
			value: null
		},
		{
			label: '待收费',
			value: 0
		},
		{
			label: '核减中',
			value: 1
		},
		{
			label: '已完成',
			value: 2
		},
		{
			label: '作废',
			value: 3
		},
		{
			label: '红冲',
			value: 4
		},
		{
			label: '待退款',
			value: 5
		},
	],
	//地图
	map: {
		//地图接口api 协议
		reactAmapProtocol: 'https://',
		// 地图key
		reactAmapKey: '18196a5d5d4865faff8deaf12c969ab2',
		//默认的中点
		center: { longitude: 119.650602, latitude: 39.958058 }
	},
	// 大屏geomap配置
	geomap: {
		// 秦皇岛 开始
		geomapJson: Qinhuangdao,
		center: [119.344626, 39.969297],
		zoom: 1.0,
		top: '28%',
		left: '10%',
		// 字段对应后台接口字段
		geoCoordMap: {
			'营业所': [119.597454, 39.952104],
			'山海关分公司': [119.756148, 40.017132],
			'市开发区分公司': [119.52515, 39.897655],
			'山海关开发区分公司': [119.794433, 39.993158],
			'东山分公司': [119.623366, 39.915839],
			'北部工业区分公司': [119.607161, 39.972461]
		}
		// 秦皇岛 结束

		// 江都 开始
		// geomapJson: JingDu,
		// center: [ 119.549195, 32.427035 ],
		// zoom: 1.0,
		// top: '28%',
		// left: '30%',
		// geoCoordMap: {}
		// 江都 结束

		// 鄂温克旗 开始
		// geomapJson: Ewenkeqi,
		// center: [ 119.736057, 49.159616 ],
		// zoom: 1.0,
		// top: '-28%',
		// left: '30%',
		// geoCoordMap: { '001中鼎半岛': [ 119.723177,48.594738 ] }
		// 鄂温克旗 结束
	}
};

export default constants;
