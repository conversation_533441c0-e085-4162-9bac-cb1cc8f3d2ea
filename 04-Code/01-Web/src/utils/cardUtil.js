import { message } from 'antd';
import http from '$http';
import axios from 'axios';
import api from "../views/charge/modules/order/store/api";
import * as actionTypes from "../views/ic/modules/rfc/store/constants";

const _config = {
	timeout: 600000,
	retry: 4,
	retryDelay: 1000
};

const httpLocal = axios.create(_config);

let hxdll = '';
if (document.getElementById('hxdll')) {
	hxdll = document.getElementById('hxdll');
}
let sunfs = '';
if (document.getElementById('sunfs')) {
	sunfs = document.getElementById('sunfs');
}

/*扬州恒信*/
//区域码
const localCode = process.env.LOCAL_CODE;

//水表种类转换
const waterMeterKindNo = (type) => {
	switch (type) {
		case '预付费2':
			return '1';
		case '预付费4442':
			return '2';
		case '预付费5':
			return '3';
		case '阶梯5':
			return '4';
		case '阶梯2':
			return '5';
	}
};

//补0
const str_pad = (data) => {
	data = data.split('.')[0];
	let zero = '000000';
	let tmp = 6 - data.length;
	return zero.substr(0, tmp) + data;
};

//按钮读取用户卡卡号
export const readCardNo = () => {
	try {
		let userCard = hxdll.user_card();
		let cardType = userCard.substring(1, 2);
		let cardNo = null;
		if (cardType === '3' || cardType === '4') {
			cardNo = userCard.substring(2, 12);
		} else {
			cardNo = userCard.substring(2, 10);
		}
		switch (userCard) {
			case '0':
				message.error('恒信卡读卡失败：设备失败');
				break;
			case '1':
				message.error('恒信卡读卡失败：读卡器无卡');
				break;
			case '2':
				message.error('恒信卡读卡失败：卡类型错误');
				break;
			case '3':
				message.error('恒信卡读卡失败：读卡失败');
				break;
			case '4':
				message.error('恒信卡读卡失败：非用户卡');
				break;
			default:
				return cardNo;
		}
	} catch (e) {
		message.error('浏览器不支持读卡,请使用IE浏览器');
	}
};

//读卡
export const readCard = () => {
	try {
		let userCard = hxdll.user_card();
		console.log('userCard: ', userCard);
		switch (userCard) {
			case '0':
				message.error('恒信卡读卡失败：设备失败');
				break;
			case '1':
				message.error('恒信卡读卡失败：读卡器无卡');
				break;
			case '2':
				message.error('恒信卡读卡失败：卡类型错误');
				break;
			case '3':
				message.error('恒信卡读卡失败：读卡失败');
				break;
			case '4':
				message.error('恒信卡读卡失败：非用户卡');
				break;
			default:
				return userCard;
		}
	} catch (e) {
		message.error('浏览器不支持读卡,请使用IE浏览器');
	}
};

//读取卡状态
export const checkCardStatus = () => {
	try {
		let result = hxdll.chk_card();
		if (result === 6) {
			return '测零卡';
		} else if (result === 8) {
			return '清除卡';
		} else if (result === 9) {
			return '检查卡';
		} else if (result === 10) {
			return '功能卡';
		} else if (result === 11) {
			return '用户卡';
		} else if (result === 12) {
			return '预付费2其他卡';
		} else if (result > 100) {
			return '开关卡';
		} else if (result === 20) {
			return '射频卡读卡失败';
		} else if (result === 21) {
			return '射频卡开关卡';
		} else if (result === 22) {
			return '射频卡清除卡';
		} else if (result === 23) {
			return '射频卡校验卡';
		} else if (result === 24) {
			return '射频卡检查卡';
		} else if (result === 25) {
			return '射频卡用户卡';
		} else if (result === 26) {
			return '射频卡阶梯用户卡';
		} else if (result === 27) {
			return '射频卡其他卡';
		} else if (result === 30) {
			return '射频卡功能卡';
		} else if (result === 31) {
			return '阶梯测零卡';
		} else if (result === 32) {
			return '阶梯开关卡';
		} else if (result === 33) {
			return '阶梯清除卡';
		} else if (result === 34) {
			return '阶梯显示卡';
		} else if (result === 35) {
			return '阶梯检查卡';
		} else if (result === 36) {
			return '阶梯功能卡';
		} else if (result === 37) {
			return '4442清除卡';
		} else if (result === 38) {
			return ' 4442开关卡';
		} else if (result === 39) {
			return '4442显示卡';
		} else if (result === 40) {
			return ' 4442检查卡';
		} else if (result === 41) {
			return '4442功能卡';
		} else if (result === 42) {
			return '4442补卡卡';
		} else if (result === 43) {
			return '4442其他卡';
		} else if (result === 49) {
			return ' 4442用户卡';
		} else if (result === 0) {
			message.error('设备失败');
		} else if (result === 1) {
			message.error('无卡');
		} else if (result === 2) {
			message.error('不存在的卡型，非恒信卡');
		} else if (result === 3) {
			message.error('读卡失败');
		} else if (result === 4) {
			message.error('坏卡');
		} else if (result === 5) {
			message.error('写卡失败');
		}
	} catch (e) {
		message.error('浏览器不支持读卡,请使用IE浏览器');
	}
};

//读取预付费检查卡
export const readYFFCheckCard = () => {
	try {
		let result = hxdll.copyusercx();
		switch (result) {
			case 10:
				message.error('恒信卡读检查卡失败：读卡器无卡');
				break;
			case 11:
				message.error('恒信卡读检查卡失败：设备错误');
				break;
			case 12:
				message.error('恒信卡读检查卡失败：读卡错误');
				break;
			default:
				return result;
		}
	} catch (e) {
		message.error('浏览器不支持读卡,请使用IE浏览器');
	}
};

//读取阶梯检查卡
export const readJTCheckCard = () => {
	//读卡
	try {
		let result = hxdll.copy_card();
		switch (result) {
			case 10:
				message.error('恒信卡读检查卡失败：读卡器无卡');
				break;
			case 11:
				message.error('恒信卡读检查卡失败：设备错误');
				break;
			case 12:
				message.error('恒信卡读检查卡失败：读卡错误');
				break;
			default:
				return result;
		}
	} catch (e) {
		message.error('浏览器不支持读卡,请使用IE浏览器');
	}
};

//开户
export const newUser = (cardNo, waterMeterNo, waterMeterKind, areaCode) => {
	let type = waterMeterKindNo(waterMeterKind);
	let chargeType = 'yff';
	if (type === '4' || type === '5') {
		chargeType = 'jtsj';
	}
	let newAreaCode = localCode;
	console.log('newUser: ');
	console.log('cardNo: ', cardNo);
	console.log('waterMeterNo: ', waterMeterNo);
	console.log('chargeType: ', chargeType);
	console.log('newAreaCode: ', newAreaCode);
	console.log('type: ', type);
	try {
		let result;
		if (type == '1') {
			console.log('yff2')
			result = hxdll.newuser(cardNo, cardNo, chargeType, newAreaCode, type);
		}
		if (type == '3') {
			console.log('yff5')
			result = hxdll.newuser(cardNo, waterMeterNo, chargeType, newAreaCode, type);
		}
		console.log('result', result)
		switch (result) {
			case 0:
				return result;
			case 10:
				message.error('开户写卡失败：水表编号非法,请修改后补开户卡');
				break;
			case 100:
				message.error('开户写卡失败：水表类型不正确,请修改后补开户卡');
				break;
			case 101:
				message.error('开户写卡失败：读卡失败,请补开户卡');
				break;
			case 102:
				message.error('开户写卡失败：蓝色钥匙卡写卡失败,请补开户卡');
				break;
			case 103:
				message.error('开户写卡失败：蓝色钥匙卡读卡失败,请补开户卡');
				break;
			case 104:
				message.error('开户写卡失败：蓝色钥匙卡坏卡或卡号非8位,请补开户卡');
				break;
			case 200:
				message.error('开户写卡失败：灰色钥匙卡初始化失败,请补开户卡');
				break;
			case 201:
				message.error('开户写卡失败：灰色钥匙卡验证失败,请补开户卡');
				break;
			case 202:
				message.error('开户写卡失败：灰色钥匙卡写卡失败,请补开户卡');
				break;
			case 300:
				message.error('开户写卡失败：圆卡读id失败,请补开户卡');
				break;
			case 301:
				message.error('开户写卡失败：圆卡读卡失败,请补开户卡');
				break;
			case 302:
				message.error('开户写卡失败：圆卡写卡失败,请补开户卡');
				break;
			default:
				message.error('开户写卡失败,,请补开户卡');
				break;
		}
	} catch (e) {
		message.error('浏览器不支持读卡,请使用IE浏览器');
	}
};

//售水写卡
export const sellConfirm = async (waterMeterKind, cardNo, frequency, waterQuantity, totalWater, zjsj1, zjsj2, zjsj3, sl1, sl2, areaCode) => {
	let type = waterMeterKindNo(waterMeterKind);
	let price1 = 0, price2 = 0, price3 = 0, quantity1 = 0, quantity2 = 0;
	if (type === '4' || type === '5') {
		price1 = zjsj1;
		price2 = zjsj2;
		price3 = zjsj3;
		quantity1 = sl1;
		quantity2 = sl2;
	}
	let newAreaCode = localCode;
	let newTotalWater = totalWater < 0 ? 0 : totalWater;
	let result = 0;
	try {
		//再次查询水价
		if (cardNo) {
			await http.restGet(`api/cm/customer/getByCardNo`, cardNo).then(res => {
				if (res.code === 0) {
					let orderDetail = res.data;
					let zjsj1 = 0;
					let zjsj2 = 0;
					let zjsj3 = 0;
					let sl1 = 0;
					let sl2 = 0;
					if (orderDetail.ladderType && orderDetail.ladderType === '非阶梯') {
						zjsj1 = orderDetail.price1;
						zjsj2 = orderDetail.price1;
						zjsj3 = orderDetail.price1;
						sl1 = 25;
						sl2 = 0;
					} else {
						zjsj1 = orderDetail.price1 ? orderDetail.price1 : 0;
						zjsj2 = orderDetail.price2 ? orderDetail.price2 : 0;
						zjsj3 = orderDetail.price3 ? orderDetail.price3 : 0;
						sl1 = orderDetail.Ladder1 ? orderDetail.Ladder1 : 0;
						sl2 = orderDetail.Ladder2 ? orderDetail.Ladder2 : 0;
					}
					// if (zjsj1 != price1) {
					// 	message.error('水价1写入错误');
					// 	return;
					// }
					// if (zjsj2 != price2) {
					// 	message.error('水价2写入错误');
					// 	return;
					// }
					// if (zjsj3 != price3) {
					// 	message.error('水价3写入错误');
					// 	return;
					// }
				}
				console.log('sellConfirm: ');
				console.log('cardNo: ', cardNo);
				console.log('newAreaCode: ', newAreaCode);
				console.log('frequency: ', frequency);
				console.log('waterQuantity: ', waterQuantity);
				console.log('newTotalWater: ', newTotalWater);
				console.log('quantity1: ', quantity1);
				console.log('quantity2: ', quantity2);
				console.log('type: ', type);
				result = hxdll.sellconfirm(cardNo, newAreaCode, frequency, waterQuantity, newTotalWater, price1, price2, price3, quantity1, quantity2, type);
				console.log('result', result)
				switch (result) {
					case 0:
						return result;
					case 10:
						message.error('售水写卡失败：设备失败');
						break;
					case 11:
						message.error('售水写卡失败：水表类型不正确');
						break;
					case 12:
						message.error('售水写卡失败：内部水量不为零，无法继续购水');
						break;
					case 13:
						message.error('售水写卡失败：IC卡内部次数与数据库不相符');
						break;
					case 14:
						message.error('售水写卡失败：IC卡换卡');
						break;
					case 15:
						message.error('售水写卡失败：写卡失败');
						break;
					case 16:
						message.error('售水写卡失败：IC卡验证失败');
						break;
				}

			});

		} else {
			console.log('sellConfirm: ');
			console.log('cardNo: ', cardNo);
			console.log('newAreaCode: ', newAreaCode);
			console.log('frequency: ', frequency);
			console.log('waterQuantity: ', waterQuantity);
			console.log('newTotalWater: ', newTotalWater);
			console.log('quantity1: ', quantity1);
			console.log('quantity2: ', quantity2);
			console.log('type: ', type);
			result = hxdll.sellconfirm(cardNo, newAreaCode, frequency, waterQuantity, newTotalWater, price1, price2, price3, quantity1, quantity2, type);
			console.log('result', result)
			switch (result) {
				case 0:
					return result;
				case 10:
					message.error('售水写卡失败：设备失败');
					break;
				case 11:
					message.error('售水写卡失败：水表类型不正确');
					break;
				case 12:
					message.error('售水写卡失败：内部水量不为零，无法继续购水');
					break;
				case 13:
					message.error('售水写卡失败：IC卡内部次数与数据库不相符');
					break;
				case 14:
					message.error('售水写卡失败：IC卡换卡');
					break;
				case 15:
					message.error('售水写卡失败：写卡失败');
					break;
				case 16:
					message.error('售水写卡失败：IC卡验证失败');
					break;
			}

		}

		return result

	} catch (e) {
		message.error('浏览器不支持读卡,请使用IE浏览器' + e);
	}
};

//补卡
export const replacementCard = (waterMeterKind, cardNo, waterMeterNo, frequency, waterQuantity, totalWater, areaCode) => {
	let type = waterMeterKindNo(waterMeterKind);
	let chargeType = 'yff';
	if (type === '4' || type === '5') {
		chargeType = 'jtsj';
	}
	let newAreaCode = localCode;
	console.log('replacementCard: ');
	console.log('cardNo: ', cardNo);
	console.log('waterMeterNo: ', waterMeterNo);
	console.log('frequency: ', frequency);
	console.log('waterQuantity: ', waterQuantity);
	console.log('totalWater: ', totalWater);
	console.log('chargeType: ', chargeType);
	console.log('newAreaCode: ', newAreaCode);
	// console.log(cardNo, waterMeterNo, frequency, waterQuantity, totalWater, chargeType, newAreaCode)
	try {
		let result = hxdll.bfxkconfirm(cardNo, waterMeterNo, frequency, waterQuantity, totalWater, chargeType, newAreaCode);
		console.log('result', result)
		switch (result) {
			case 0:
				return result;
			case 10:
				message.error('补卡写卡失败：水表编号错误');
				break;
			case 11:
				message.error('补卡写卡失败：写卡失败');
				break;
			case 12:
				message.error('补卡写卡失败：设备失败');
				break;
			case 13:
				message.error('补卡写卡失败：读卡失败');
				break;
			case 14:
				message.error('补卡写卡失败：坏卡');
				break;
			default:
				message.error('补卡写卡失败');
				break;
		}
	} catch (e) {
		message.error('浏览器不支持读卡,请使用IE浏览器');
	}
};

//阶梯检查卡补卡
export const copyToUser = (cardNo, waterQuantity, frequency, flag, areaCode) => {
	let newAreaCode = localCode;
	try {
		let result = hxdll.copytouser(newAreaCode, cardNo, waterQuantity, frequency, flag);
		switch (result) {
			case 0:
				return result;
			case 10:
				message.error('检查卡补卡写卡失败：设备失败');
				break;
			case 11:
				message.error('检查卡补卡写卡失败：写卡失败');
				break;
			case 12:
				message.error('检查卡补卡写卡失败：读卡失败');
				break;
			case 13:
				message.error('检查卡补卡写卡失败：坏卡');
				break;
			default:
				message.error('检查卡补卡写卡失败');
				break;
		}
	} catch (e) {
		message.error('浏览器不支持读卡,请使用IE浏览器');
	}

};

//换表
export const changeWaterMeter = (areaCode, waterMeterKind, cardNo, waterMeterNo, frequency, zjsj1, zjsj2, zjsj3, sl1, sl2) => {
	let type = waterMeterKindNo(waterMeterKind);
	let price1 = 0, price2 = 0, price3 = 0, quantity1 = 0, quantity2 = 0;
	if (type === '4' || type === '5') {
		price1 = zjsj1;
		price2 = zjsj2;
		price3 = zjsj3;
		quantity1 = sl1;
		quantity2 = sl2;
	}
	let newAreaCode = localCode;
	console.log('hbconfirm', newAreaCode, cardNo, waterMeterNo, frequency, type, price1, price2, price3, quantity1, quantity2)
	try {
		let result = hxdll.hbconfirm(newAreaCode, cardNo, waterMeterNo, frequency, type, price1, price2, price3, quantity1, quantity2);
		switch (result) {
			case 0:
				return result;
			case 10:
				message.error('换表写卡失败：水表编号错误');
				break;
			case 11:
				message.error('换表写卡失败：写卡失败');
				break;
			case 12:
				message.error('换表写卡失败：设备失败');
				break;
			case 13:
				message.error('换表写卡失败：读卡失败');
				break;
			case 14:
				message.error('换表写卡失败：坏卡');
				break;
			case 15:
				message.error('换表写卡失败：卡初始化失败');
				break;
			case 16:
				message.error('换表写卡失败： IC卡验证失败');
				break;
			case 100:
				message.error('换表写卡失败： IC卡验证失败');
				break;
			default:
				message.error('换表写卡失败');
				break;
		}
	} catch (e) {
		message.error('浏览器不支持读卡,请使用IE浏览器');
	}

};

/*工具卡*/
//错误0或者错误00修正
export const errCard = (number) => {
	try {
		let result = 0;
		if (number === 0) {
			result = hxdll.err0();
		} else {
			result = hxdll.err00();
		}
		switch (result) {
			case 0:
				return result;
			case 10:
				message.error('修正错误：读卡器无卡');
				break;
			case 11:
				message.error('修正错误：非射频卡，无需要此操作');
				break;
			case 12:
				message.error('修正错误：读卡失败');
				break;
			case 13:
				message.error('修正错误：非用户卡');
				break;
			case 14:
				message.error('修正错误：写卡失败');
				break;
		}
	} catch (e) {
		message.error(e);
	}
};

//工具卡制作
export const toolCard = (cardType, areaCode) => {
	let newAreaCode = localCode;
	try {
		let result = hxdll.gl_card(cardType, newAreaCode);
		switch (result) {
			case 0:
				return result;
			case 1:
				message.error('制作管理卡失败：设备失败');
				break;
			case 2:
				message.error('制作管理卡失败：写卡失败');
				break;
			case 3:
				message.error('制作管理卡失败：读卡失败');
				break;
			case 4:
				message.error('制作管理卡失败：坏卡');
				break;
			case 5:
				message.error('制作管理卡失败：密码校验失败不可写');
				break;
		}
	} catch (e) {
		message.error(e);
	}
};

//读补卡卡
export const readSupplementary = () => {
	try {
		let result = hxdll.bkkcx();
		switch (result) {
			case 10:
				message.error('补卡卡读卡：设备错误');
				break;
			case 11:
				message.error('补卡卡读卡：读卡错误');
				break;
			case 12:
				message.error('补卡卡读卡：非补卡卡');
				break;
			default:
				return result;
		}
	} catch (e) {
		message.error(e);
	}
};

//补卡卡补卡
export const supplementary = (cardNo, areaCode, water) => {
	try {
		let result = hxdll.bkkconfirm(cardNo, areaCode, water);
		switch (result) {
			case 0:
				return result;
			case 10:
				message.error('补卡失败：水量不合法');
				break;
			case 11:
				message.error('补卡失败：设备失败');
				break;
			case 12:
				message.error('补卡失败：IC卡验证失败');
				break;
			case 13:
				message.error('补卡失败：写卡失败');
				break;
		}
	} catch (e) {
		message.error(e);
	}
};

/*河南新天*/
//读卡
export const xtRead = () => {
	let openport = sunfs.openport(3, 104);
	console.log('openport: ', openport);
	sleep(300);
	let resultXT = sunfs.readcard();
	console.log('resultXT: ', resultXT);
	if (resultXT === 1) {
		resultXT = sunfs.fsdata;
	}
	sunfs.closeport();
	switch (resultXT) {
		case 2:
			message.error('新天卡读卡失败：串口错误');
			break;
		case 3:
			message.error('新天卡读卡失败：通讯校验错误');
			break;
		case 4:
			message.error('新天卡读卡失败：通讯失败');
			break;
		case 5:
			message.error('新天卡读卡失败：空卡');
			break;
		case 6:
			message.error('新天卡读卡失败：未放卡');
			break;
		case 7:
			message.error('新天卡读卡失败：不知名卡');
			break;
		case 8:
			message.error('新天卡读卡失败：卡校验错误');
			break;
		case 9:
			message.error('新天卡读卡失败：写卡错误');
			break;
		case 10:
			message.error('新天卡读卡失败：非空卡');
			break;
		case 11:
			message.error('新天卡读卡失败：非充值卡');
			break;
		case 12:
			message.error('新天卡写卡失败：非充值卡');
			break;
		default:
			return resultXT;
	}
};

//开户
export const xtWriteOpen = (cardNo) => {
	let data = '1,' + '0A' + ',' + str_pad(Number(cardNo).toString(16)) + ',000000,000000,000000,000000,000000,000000,000000,000000,000000,000000';
	let openport = sunfs.openport(3, 104);
	console.log('openport: ', openport);
	sleep(300);
	sunfs.fsdata = data;
	console.log('sunfs.fsdata: ', sunfs.fsdata);
	let resultXT = sunfs.writecard();
	console.log('resultXT: ', resultXT);
	sunfs.closeport();
	switch (resultXT) {
		case 1:
			return resultXT;
		case 2:
			message.error('新天卡开户失败：串口错误,请补开户卡');
			break;
		case 3:
			message.error('新天卡开户失败：通讯校验错误,请补开户卡');
			break;
		case 4:
			message.error('新天卡开户失败：通讯失败,请补开户卡');
			break;
		case 5:
			message.error('新天卡开户失败：空卡,请补开户卡');
			break;
		case 6:
			message.error('新天卡开户失败：未放卡,请补开户卡');
			break;
		case 7:
			message.error('新天卡开户失败：不知名卡,请补开户卡');
			break;
		case 8:
			message.error('新天卡开户失败：卡校验错误,请补开户卡');
			break;
		case 9:
			message.error('新天卡开户失败：写卡错误,请补开户卡');
			break;
		case 10:
			message.error('新天卡开户失败：非空卡,请补开户卡');
			break;
		case 11:
			message.error('新天卡开户失败：非充值卡,请补开户卡');
			break;
		case 12:
			message.error('新天卡写卡失败：非充值卡');
			break;
	}
};

//缴费写卡
export const xtWriteCard = (cardNo, total, water, times) => {
	let newWater = null;
	if (water === 0) {
		newWater = '000000';
	} else {
		newWater = str_pad((Number(water)).toString(16));
	}
	let newCardNo = str_pad(Number(cardNo).toString(16));
	let newTotal = str_pad((Number(total)).toString(16));
	let data
	if (times == 0) {
		data = '1,' + '0A' + ',' + newCardNo + ',000000,000000,' + newWater + ',' + newTotal + ',000000,000000,000000,000000,000000,000000';
	} else {
		data = '2,' + '0A' + ',' + newCardNo + ',000000,000000,' + newWater + ',' + newTotal + ',000000,000000,000000,000000,000000,000000';
	}
	console.log('04data', data)
	let openport = sunfs.openport(3, 104);
	console.log('openport: ', openport);
	sleep(300);
	sunfs.fsdata = data;
	let resultXT = sunfs.writecard();
	console.log('resultXT: ', resultXT);
	sunfs.closeport();
	switch (resultXT) {
		case 1:
			return resultXT;
		case 2:
			message.error('新天卡写卡失败：串口错误');
			break;
		case 3:
			message.error('新天卡写卡失败：通讯校验错误');
			break;
		case 4:
			message.error('新天卡写卡失败：通讯失败');
			break;
		case 5:
			message.error('新天卡写卡失败：空卡');
			break;
		case 6:
			message.error('新天卡写卡失败：未放卡');
			break;
		case 7:
			message.error('新天卡写卡失败：不知名卡');
			break;
		case 8:
			message.error('新天卡写卡失败：卡校验错误');
			break;
		case 9:
			message.error('新天卡写卡失败：写卡错误');
			break;
		case 10:
			message.error('新天卡写卡失败：非空卡');
			break;
		case 11:
			message.error('新天卡写卡失败：非充值卡');
			break;
		case 12:
			message.error('新天卡写卡失败：分界点、单价参数错误');
			break;
	}
};

//补卡
export const xtReplacementCard = (cardNo, total, water) => {
	let newWater = null;
	if (water === 0) {
		newWater = '000000';
	} else {
		newWater = str_pad((Number(water)).toString(16));
	}
	let newCardNo = str_pad(Number(cardNo).toString(16));
	let newTotal = str_pad((Number(total)).toString(16));
	let data = '1,' + '0A' + ',' + newCardNo + ',000000,000000,' + newWater + ',' + newTotal + ',000000,000000,000000,000000,000000,000000';
	console.log('04data', data);
	let openport = sunfs.openport(3, 104);
	console.log('openport: ', openport);
	sleep(300);
	sunfs.fsdata = data;
	let resultXT = sunfs.writecard();
	console.log('resultXT: ', resultXT);
	sunfs.closeport();
	switch (resultXT) {
		case 1:
			return resultXT;
		case 2:
			message.error('新天卡补卡失败：串口错误');
			break;
		case 3:
			message.error('新天卡补卡失败：通讯校验错误');
			break;
		case 4:
			message.error('新天卡补卡失败：通讯失败');
			break;
		case 5:
			message.error('新天卡补卡失败：空卡');
			break;
		case 6:
			message.error('新天卡补卡失败：未放卡');
			break;
		case 7:
			message.error('新天卡补卡失败：不知名卡');
			break;
		case 8:
			message.error('新天卡补卡失败：卡校验错误');
			break;
		case 9:
			message.error('新天卡补卡失败：写卡错误');
			break;
		case 10:
			message.error('新天卡补卡失败：非空卡');
			break;
		case 11:
			message.error('新天卡补卡失败：非充值卡');
			break;
		case 12:
			message.error('新天卡写卡失败：分界点、单价参数错误');
			break;
	}
};

//换表
export const xtWriteChange = (cardNo, total) => {
		cardNo = str_pad(Number(cardNo).toString(16));
	let newTotal =  str_pad((Number(total)).toString(16));
	let data = '1,' + '0A' + ',' + cardNo + ',000000,000000,' + newTotal + ',' + newTotal + ',000000,000000,000000,000000,000000,000000';
	console.log('xtWriteChange: ', data)
	let openport = sunfs.openport(3, 104);
	console.log('openport: ', openport);
	sleep(300);
	sunfs.fsdata = data;
	let resultXT = sunfs.writecard();
	sunfs.closeport();
	switch (resultXT) {
		case 1:
			return resultXT;
		case 2:
			message.error('新天换表失败：串口错误');
			break;
		case 3:
			message.error('新天换表失败：通讯校验错误');
			break;
		case 4:
			message.error('新天换表失败：通讯失败');
			break;
		case 5:
			message.error('新天换表失败：空卡');
			break;
		case 6:
			message.error('新天换表失败：未放卡');
			break;
		case 7:
			message.error('新天换表失败：不知名卡');
			break;
		case 8:
			message.error('新天换表失败：卡校验错误');
			break;
		case 9:
			message.error('新天换表失败：写卡错误');
			break;
		case 10:
			message.error('新天换表失败：非空卡');
			break;
		case 11:
			message.error('新天换表失败：非充值卡');
			break;
		case 12:
			message.error('新天换表失败：分界点、单价参数错误');
			break;
	}
};

//清空卡
export const xtClearCard = () => {
	let data = '1,5A,000000,000000,000000,000000,000000,000000,000000,000000,000000,000000,000000';
	let openport = sunfs.openport(3, 104);
	console.log('openport: ', openport);
	sleep(300);
	sunfs.fsdata = data;
	let resultXT = sunfs.writecard();
	sunfs.closeport();
	switch (resultXT) {
		case 2:
			message.error('新天卡清空失败：串口错误');
			break;
		case 3:
			message.error('新天卡清空失败：通讯校验错误');
			break;
		case 4:
			message.error('新天卡清空失败：通讯失败');
			break;
		case 5:
			return resultXT;
		case 6:
			message.error('新天卡清空失败：未放卡');
			break;
		case 7:
			message.error('新天卡清空失败：不知名卡');
			break;
		case 8:
			message.error('新天卡清空失败：卡校验错误');
			break;
		case 9:
			message.error('新天卡清空失败：写卡错误');
			break;
		case 10:
			message.error('新天卡清空失败：非空卡');
			break;
		case 11:
			message.error('新天卡清空失败：非充值卡');
		case 12:
			message.error('新天卡清空失败：分界点、单价参数错误');
			break;
	}
};

//检查卡
export const xtClearCheckCard = () => {
	let data = '1,0C,000000,000000,000000,000000,000000,000000,000000,000000,000000,000000,000000';
	let openport = sunfs.openport(3, 104);
	console.log('openport: ', openport);
	sleep(300);
	sunfs.fsdata = data;
	let resultXT = sunfs.writecard();
	sunfs.closeport();
	switch (resultXT) {
		case 1:
			return resultXT;
		case 2:
			message.error('新天制作检查卡失败：串口错误');
			break;
		case 3:
			message.error('新天制作检查卡失败：通讯校验错误');
			break;
		case 4:
			message.error('新天制作检查卡失败：通讯失败');
			break;
		case 5:
			message.error('新天制作检查卡失败：空卡');
			break;
		case 6:
			message.error('新天制作检查卡失败：未放卡');
			break;
		case 7:
			message.error('新天制作检查卡失败：不知名卡');
			break;
		case 8:
			message.error('新天制作检查卡失败：卡校验错误');
			break;
		case 9:
			message.error('新天制作检查卡失败：写卡错误');
			break;
		case 10:
			message.error('新天制作检查卡失败：非空卡');
			break;
		case 11:
			message.error('新天制作检查卡失败：非充值卡');
			break;
		case 12:
			message.error('新天制作检查卡失败：分界点、单价参数错误');
			break;
	}
};

//检查卡
export const xtClearBigCheckCard = () => {
	let data = '1,1B,000000,000000,000000,000000,000000,000000,000000,000000,000000,000000,000000';
	let openport = sunfs.openport(3, 104);
	console.log('openport: ', openport);
	sleep(300);
	sunfs.fsdata = data;
	let resultXT = sunfs.writecard();
	sunfs.closeport();
	switch (resultXT) {
		case 1:
			return resultXT;
		case 2:
			message.error('新天制作检查卡失败：串口错误');
			break;
		case 3:
			message.error('新天制作检查卡失败：通讯校验错误');
			break;
		case 4:
			message.error('新天制作检查卡失败：通讯失败');
			break;
		case 5:
			message.error('新天制作检查卡失败：空卡');
			break;
		case 6:
			message.error('新天制作检查卡失败：未放卡');
			break;
		case 7:
			message.error('新天制作检查卡失败：不知名卡');
			break;
		case 8:
			message.error('新天制作检查卡失败：卡校验错误');
			break;
		case 9:
			message.error('新天制作检查卡失败：写卡错误');
			break;
		case 10:
			message.error('新天制作检查卡失败：非空卡');
			break;
		case 11:
			message.error('新天制作检查卡失败：非充值卡');
			break;
		case 12:
			message.error('新天制作检查卡失败：分界点、单价参数错误');
			break;
	}
};

/*泰安轻松*/
const qs_read_url = 'http://127.0.0.1:54250/yikatong';
const qs_write_url = 'http://127.0.0.1:54250/yikatong';
const qs_sys_code = '136';
const qs_headers = {
	'Content-type': 'text/plain;'
}

// 读卡
export const qsRead = async () => {
	const res = await httpLocal.get(qs_read_url + "?t=" + Date.now())
	console.log('qsRead ', res.data);
	return res.data
}
// 空白卡
export const qsEmptyCard = async () => {
	const res = await httpLocal.delete(qs_write_url)
	console.log('qsEmptyCard ', res.data);
	return res.data
}
// 清除卡
export const qsClearCard = async () => {
	const data = { "cardtype": "2", "syscode": qs_sys_code, "pcode": "05"}
	const res = await httpLocal.put(qs_write_url, data)
	console.log('qsClearCard ', res.data);
	return res.data
}
// 开户卡
export const qsOpenCard = async (cardNo) => {
	const data = { "cardtype": "3", "syscode": qs_sys_code, "usercode": cardNo, "pcode": "05", "data": [{ "isvalid": "1", "buynum": "0", "buytimes": "0" }] }
	const res = await httpLocal.put(qs_write_url, data)
	console.log('qsOpenCard ', res.data);
	return res.data
}
// 写卡
export const qsWriteCard = async (cardNo, buyAmount, buyTimes) => {
	const data = { "cardtype": "4", "syscode": qs_sys_code, "usercode": cardNo, "pcode": "05", "data": [{ "isvalid": "1", "buynum": buyAmount * 10, "buytimes": buyTimes }] }
	const res = await httpLocal.put(qs_write_url, data)
	console.log('qsWriteCard ', res.data);
	return res.data
}
// 退款
export const qsRefundCard = async (cardNo, buyAmount) => {
	const data = { "cardtype": "6", "syscode": qs_sys_code, "usercode": cardNo, "buynum": buyAmount * 10, "pcode": "05" }
	const res = await httpLocal.put(qs_write_url, data)
	console.log('qsRefundCard ', res.data);
	return res.data
}
// 补卡
export const qsReplaceCard = async (cardNo, buyAmount, buyTimes) => {
	const data = { "cardtype": "4", "syscode": qs_sys_code, "usercode": cardNo, "pcode": "05", "data": [{ "cardstatus": "1", "isvalid": "1", "buynum": buyAmount * 10, "buytimes": buyTimes }] }
	const res = await httpLocal.put(qs_write_url, data)
	console.log('qsReplaceCard ', res.data);
	return res.data
}
//乘法
export const accMul = (num1, num2) => {
	let m = 0, s1 = num1.toString(), s2 = num2.toString();
	try {
		m += s1.split('.')[1].length;
	} catch (e) {
	}
	try {
		m += s2.split('.')[1].length;
	} catch (e) {
	}
	return Number(s1.replace('.', '')) * Number(s2.replace('.', '')) / Math.pow(10, m);
};

export const sleep = (ms) => {
	let start = new Date().getTime(), expire = start + ms;
	while (new Date().getTime() < expire) { }
	return;
}

export default (
	readCardNo, readCard, readYFFCheckCard, readJTCheckCard, newUser, sellConfirm,
	replacementCard, changeWaterMeter, copyToUser, errCard, toolCard, checkCardStatus, supplementary,
	readSupplementary, xtRead, xtWriteOpen, xtWriteCard, xtClearCard, xtWriteChange, xtClearCheckCard, xtReplacementCard,
	accMul, xtClearBigCheckCard, qsRead, qsEmptyCard, qsOpenCard, qsWriteCard, qsRefundCard, sleep);
