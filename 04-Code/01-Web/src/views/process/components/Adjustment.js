import React, { Component, Fragment } from 'react';
import { Row, Col, Table, Descriptions } from 'antd';
import { showFields } from '$utils';
import './index.scss';

const DescItem = Descriptions.Item;

// 原始账单信息
const columns = [
	{
		title: '序号',
		dataIndex: 'xuhao',
		width: 80,
		align: 'center',
		render: (text, newDetail, index) => {
			return index + 1;
		}
	},
	{
		title: '上期示数',
		dataIndex: 'lastWheelNumber',
		key: 'lastWheelNumber',
		align: 'center'
	},
	{
		title: '本期示数',
		dataIndex: 'currentWheelNumber',
		key: 'currentWheelNumber',
		align: 'center'
	},
	{
		title: '抄见水量',
		dataIndex: 'copiedAmount',
		key: 'copiedAmount',
		align: 'center'
	},
	{
		title: '实际用水量',
		dataIndex: 'actualAmount',
		key: 'actualAmount',
		align: 'center'
	},
	{
		title: '结算水量',
		dataIndex: 'settleAmount',
		key: 'settleAmount',
		align: 'center'
	},
	{
		title: '账单金额',
		dataIndex: 'billFee',
		key: 'billFee',
		align: 'center'
	},
	{
		title: '结清金额',
		dataIndex: 'settleFee',
		key: 'settleFee',
		align: 'center'
	},
	{
		title: '账单状态',
		dataIndex: 'billStatus',
		key: 'billStatus',
		align: 'center'
	}
];

class Adjustment extends Component {

	constructor(props) {
		super(props);
		this.state = {};
	}


	render() {
		const { detail,customer } = this.props;
		let newDetail = detail ? detail : '';
		return (
			<Fragment>
				<Descriptions title="用户信息" bordered>
					<Descriptions.Item label="用户名称：">{customer && customer.name}</Descriptions.Item>
					<Descriptions.Item label="用户编号：">{customer && customer.cno}</Descriptions.Item>
					<Descriptions.Item label="人口数：">{customer && customer.population}</Descriptions.Item>
					<Descriptions.Item label="联系人：">{customer && customer.contact}</Descriptions.Item>
					<Descriptions.Item label="联系电话：">{customer && customer.contactPhone}</Descriptions.Item>
					<Descriptions.Item label="身份证号：">{customer && customer.idCard}</Descriptions.Item>
					<Descriptions.Item label="片区：">{customer && customer.areaName}</Descriptions.Item>
					<Descriptions.Item label="水表类型：">{customer && customer.meterType}</Descriptions.Item>
					<Descriptions.Item label="客户类型：">{customer && customer.customerType}</Descriptions.Item>
					<Descriptions.Item label="水表编号：">{customer && customer.waterMeterNo}</Descriptions.Item>
					<Descriptions.Item label="水表类型：">{customer && customer.waterMeterType}</Descriptions.Item>
					<Descriptions.Item label="水表种类：">{customer && customer.waterMeterKindType}</Descriptions.Item>
				</Descriptions>
				<br/>
				<Row>
					<Col>
						<h4>原始账单信息</h4>
						<Table className='mb' bordered columns={columns} rowKey={(newDetail) => newDetail.id}
									 dataSource={[{ ...newDetail.chargeBillOld }]} pagination={false}/>
					</Col>
				</Row>
				<br/>
				<Row>
					{
						newDetail.status !== '作废' ?
							<Descriptions bordered className='mb'>
								<DescItem label="调整本期示数：">{showFields(newDetail.currentWheelNumberNew)}</DescItem>
								<DescItem label="调整水量：">{showFields(newDetail.adjustmentWaterAmount)}</DescItem>
								<DescItem label="保留水量：">{showFields(newDetail.retainWaterAmount)}</DescItem>
								<DescItem label="调整金额：">{showFields(newDetail.adjustmentFee)}</DescItem>
								<DescItem label="保留金额：">{showFields(newDetail.retainFee)}</DescItem>
								<DescItem label="创建时间：">{showFields(newDetail.createTime)}</DescItem>
								<DescItem label="创建人：">{showFields(newDetail.createPersonName)}</DescItem>
								<DescItem label="调整时间：">{showFields(newDetail.adjustmentTime)}</DescItem>
							</Descriptions>
							:
							<Descriptions bordered className='mb'>
								<DescItem label="调整本期示数：">{showFields(newDetail.currentWheelNumberNew)}</DescItem>
								<DescItem label="调整水量：">{showFields(newDetail.adjustmentWaterAmount)}</DescItem>
								<DescItem label="保留水量：">{showFields(newDetail.retainWaterAmount)}</DescItem>
								<DescItem label="调整金额：">{showFields(newDetail.adjustmentFee)}</DescItem>
								<DescItem label="保留金额：">{showFields(newDetail.retainFee)}</DescItem>
								<DescItem label="创建时间：">{showFields(newDetail.createTime)}</DescItem>
								<DescItem label="创建人：">{showFields(newDetail.createPersonName)}</DescItem>
								<DescItem label="调整时间：">{showFields(newDetail.adjustmentTime)}</DescItem>
								<DescItem label="作废时间：">{showFields(newDetail.updateTime)}</DescItem>
								<DescItem label="作废人：">{showFields(newDetail.updatePersonName)}</DescItem>
								<Descriptions.Item label="描述：">{showFields(newDetail.reasonValue)}</Descriptions.Item>
							</Descriptions>
					}
				</Row>
			</Fragment>
		);
	}
}

export default Adjustment;
