import React, { Component, Fragment } from 'react';
import {Col, Descriptions} from 'antd';
import { constants } from '$utils';
import './index.scss';
import Img from "react-zmage";
import NoData from '$components/NoData';

class WaterUseKindChange extends Component {
	render() {
		const { detail, customer } = this.props;

		let fileList = [];
		if(detail&&detail.annexList){
			detail.annexList.forEach(item => {
				fileList.push({
					uid: item.id,
					id: item.id,
					createTime: item.createTime,
					updateTime: item.updateTime,
					relatedId: item.relatedId,
					relatedCode: item.relatedCode,
					originName: item.originName,
					newName: item.newName,
					filePath: item.filePath,
					name: item.originName,
					status: 'done',
					url: `${constants.fileUrl}${item.filePath}${item.newName}`
				});
			});
			detail.fileList=fileList;
		}

		return (
			<Fragment>
				<Descriptions title="用户信息" bordered>
					<Descriptions.Item label="用户名称：">{customer && customer.name}</Descriptions.Item>
					<Descriptions.Item label="用户编号：">{customer && customer.cno}</Descriptions.Item>
					<Descriptions.Item label="人口数：">{customer && customer.population}</Descriptions.Item>
					<Descriptions.Item label="联系人：">{customer && customer.contact}</Descriptions.Item>
					<Descriptions.Item label="联系电话：">{customer && customer.contactPhone}</Descriptions.Item>
					<Descriptions.Item label="身份证号：">{customer && customer.idCard}</Descriptions.Item>
					<Descriptions.Item label="片区：">{customer && customer.areaName}</Descriptions.Item>
					<Descriptions.Item label="水表类型：">{customer && customer.meterType}</Descriptions.Item>
					<Descriptions.Item label="客户类型：">{customer && customer.customerType}</Descriptions.Item>
					<Descriptions.Item label="水表编号：">{customer && customer.waterMeterNo}</Descriptions.Item>
					<Descriptions.Item label="水表类型：">{customer && customer.waterMeterType}</Descriptions.Item>
					<Descriptions.Item label="水表种类：">{customer && customer.waterMeterKindType}</Descriptions.Item>
				</Descriptions>
				<Descriptions title="用水分类变更信息" bordered>
					<Descriptions.Item label="原用水性质：">{detail && detail.oldWaterUseKindType}</Descriptions.Item>
					<Descriptions.Item label="原用水分类：">{detail && detail.oldWaterUseKindName}</Descriptions.Item>
					<Descriptions.Item label="原阶梯类型：">{detail && detail.oldLadderType}</Descriptions.Item>
					<Descriptions.Item label="新用水性质：">{detail && detail.newWaterUseKindType}</Descriptions.Item>
					<Descriptions.Item label="新用水分类：">{detail && detail.newWaterUseKindName}</Descriptions.Item>
					<Descriptions.Item label="新阶梯类型：">{detail && detail.newLadderType}</Descriptions.Item>
					<Descriptions.Item label="备注：" span={3}>
						{detail && detail.remark}
					</Descriptions.Item>
				</Descriptions>
				<Descriptions  title="照片" bordered>

				{
					detail &&detail.fileList &&detail.fileList.length > 0 ? (
									detail.fileList.map((item, index) => {
										return (
												<Col key={index} className="photoInfo" span={8} align="center">
													<Img src={item.url} alt="" style={{height:300,weight:300}} />
												</Col>
										);
									})

							) :
							<NoData text="暂无照片信息" />
				}
				</Descriptions>

			</Fragment>
		);
	}
}
export default WaterUseKindChange;
