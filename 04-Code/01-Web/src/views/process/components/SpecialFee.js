import React, { Component } from 'react';
import {Descriptions, Form, Row, Col, Button, Divider, Table } from 'antd';
import './index.scss';

const specialFeeDetailColumns = [
	{
		title: '用水分类',
		dataIndex: 'waterUseKindName',
		align: 'center'
	},
	{
		title: '水量',
		dataIndex: 'waterAmount',
		align: 'center'
	},
	{
		title: '是否计入累积',
		align: 'center',
		render: (text, record, index) => {
			if (record.accumulation) {
				return '是';
			} else {
				return '否';
			}
		}
	}
];

const chargeDetailColumns = [
	{
		title: '用水分类',
		dataIndex: 'waterUseKindType',
		align: 'center'
	},
	{
		title: '用水性质',
		dataIndex: 'waterUseKindName',
		align: 'center'
	},
	{
		title: '阶梯名称',
		dataIndex: 'ladderName',
		align: 'center'
	},
	{
		title: '阶梯用水量',
		dataIndex: 'waterAmount',
		align: 'center'
	},
	{
		title: '清水费',
		dataIndex: 'cleanWaterFee',
		align: 'center'
	},
	{
		title: '污水费',
		dataIndex: 'sewageFee',
		align: 'center'
	},
	{
		title: '水资源税',
		dataIndex: 'waterResourceFee',
		align: 'center'
	},
	{
		title: '其他费用',
		dataIndex: 'otherFee',
		align: 'center'
	},
];

class SpecialFee extends Component {
	render(){
		const { detail, handleCancel } = this.props;
		const { chargeDetailList=[]} = detail|{};
		return (
				<Form>
					<Row>
						<Descriptions bordered>
							<Descriptions.Item label="用户名称：">{detail && detail.customerName}</Descriptions.Item>
							<Descriptions.Item label="用户编号：">{detail && detail.cno}</Descriptions.Item>
							<Descriptions.Item label="特抄收费编号：">{detail && detail.specialFeeNo}</Descriptions.Item>
							<Descriptions.Item label="收费金额：">{detail && detail.feeAmount}</Descriptions.Item>
							<Descriptions.Item label="财务年月：">{detail && detail.period}</Descriptions.Item>
							<Descriptions.Item label="付款方式：">{detail && detail.chargeWay}</Descriptions.Item>
							<Descriptions.Item label="是否收费：">{detail && detail.charge ? '是' : '否' }</Descriptions.Item>
							<Descriptions.Item label="收费时间：">{detail && detail.createTime}</Descriptions.Item>
							<Descriptions.Item label="收费原因：">{detail && detail.feeCause}</Descriptions.Item>
							<Descriptions.Item label="状态：">{detail && detail.status}</Descriptions.Item>
							<Descriptions.Item label="申请状态：">{detail && detail.applyStatus}</Descriptions.Item>
						</Descriptions>
					</Row>
					<Divider dashed orientation="left">收费内容</Divider>
					<Row className="main">
						<Table bordered columns={specialFeeDetailColumns} rowKey={() => Math.random()}
									 dataSource={detail && detail.specialFeeDetailList} />
					</Row>
					<Divider dashed orientation="left">费用明细</Divider>
					<Row className="main">
						<Table bordered columns={chargeDetailColumns} rowKey={() => Math.random()} dataSource={chargeDetailList} />
					</Row>
					<Row>
						<Col span={24} align="center">
							<Button type="default" onClick={() =>handleCancel()}>
								关闭
							</Button>
						</Col>
					</Row>
				</Form>
		);
	}
}

export default SpecialFee;
