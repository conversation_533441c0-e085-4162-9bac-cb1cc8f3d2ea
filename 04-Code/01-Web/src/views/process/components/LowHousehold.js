import React, { Component, Fragment } from 'react';
import { Row, Table, Descriptions, Col, Divider } from 'antd';
import NoData from '$components/NoData';
import Img from 'react-zmage';
import { constants } from '$utils';

class LowHousehold extends Component {

	render() {
		const userColumns = [
			{
				title: '序号',
				dataIndex: 'xuhao',
				width: 80,
				align: 'center',
				render: (text, record, index) => {
					return index + 1;
				}
			},
			{
				title: '用户编号',
				dataIndex: 'cno',
				key: 'cno',
				align: 'center'
			},
			{
				title: '户号',
				dataIndex: 'hno',
				key: 'hno',
				align: 'center'
			},
			{
				title: '用户名称',
				dataIndex: 'name',
				key: 'name',
				align: 'center'
			},
			{
				title: '用户地址',
				dataIndex: 'address',
				key: 'address',
				align: 'center'
			},
			{
				title: '水表类型',
				dataIndex: 'waterMeterType',
				key: 'waterMeterType',
				align: 'center',
				render: (text, record, index) => {
					return text ? text : '--';
				}
			},
			{
				title: '水表种类',
				dataIndex: 'waterMeterKindType',
				key: 'waterMeterKindType',
				align: 'center',
				render: (text, record, index) => {
					return text ? text : '--';
				}
			}
		];
		const { detail } = this.props;
		let fileList = [];
		let newDetail = detail ? detail : "";
		newDetail && newDetail.annexList.length > 0 && newDetail.annexList.forEach(item => {
			fileList.push({
				uid: item.id,
				id: item.id,
				createTime: item.createTime,
				updateTime: item.updateTime,
				relatedId: item.relatedId,
				relatedCode: item.relatedCode,
				originName: item.originName,
				newName: item.newName,
				filePath: item.filePath,
				name: item.originName,
				status: 'done',
				url: `${constants.fileUrl}${item.filePath}${item.newName}`
			});
		});
		return (
			<Row className='detail'>

				<Descriptions bordered>
					<Descriptions.Item label="特困证号：">{newDetail.poorCertificate}</Descriptions.Item>
					<Descriptions.Item label="姓名：">{newDetail.name}</Descriptions.Item>
					<Descriptions.Item label="地址：">{newDetail.address}</Descriptions.Item>
					<Descriptions.Item label="身份证号：：">{newDetail.identityCard}</Descriptions.Item>
					<Descriptions.Item label="生效日期：">{newDetail.effectiveDate}</Descriptions.Item>
					<Descriptions.Item label="失效日期：">{newDetail.expiredDate}</Descriptions.Item>
					<Descriptions.Item label="用水性质：">{newDetail.waterUseKindName}</Descriptions.Item>
					<Descriptions.Item label="创建人：">{newDetail.createPersonName}</Descriptions.Item>
					<Descriptions.Item label="创建时间：">{newDetail.createTime}</Descriptions.Item>
					<Descriptions.Item label="备注：">{newDetail.note}</Descriptions.Item>
				</Descriptions>
				<Divider dashed orientation="left">关联用户</Divider>
				<Table bordered pagination={false} rowKey={() => Math.random()} columns={userColumns}
					dataSource={newDetail.customerList}
				/>
				<Divider dashed orientation="left">照片信息</Divider>
				{
					fileList.length > 0 ? fileList.map((item, index) => {
						return (
							<Col key={index} className='photoInfo' span={8} align='center'><Img src={item.url} alt=""  style={{height:300,weight:300}}/></Col>
						);
					}) : <NoData text='暂无照片信息' />
				}
			</Row>
		);
	}
}

export default LowHousehold;
