import http from '$http';
import * as actionTypes from './constants';
import api from './api';
// 数据回填
const payload = (type, data) => ({ type, data });
// 组件操作redux数据状态
const setState = data => ({ type: actionTypes.SET_STATE, data });
// 获取列表
const list = data => {
	return async dispatch => {
		const res = await http.post(api.list, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.LIST_RECORD, res.data));
		}
	};
};
// 删除申请
const del = (data, list) => {
	return async dispatch => {
		const res = await http.post(api.del + data);
		if (res.code === 0) {
			list();
			dispatch(payload(actionTypes.DELETE, res.data));
		}
	};
};
// 获取审批流详情
const listReview = data => {
	return async dispatch => {
		const res = await http.restGet(api.listReview, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.LIST_REVIEW, res.data));
		}
	};
};
// 获取用户详情
const getCustomer = data => {
	return async dispatch => {
		const res = await http.restGet(api.getCustomer, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_CUSTOEMR, res.data));
		}
	};
};
// 获取过户详情
const getCustomerProperty = data => {
	return async dispatch => {
		const res = await http.restGet(api.getCustomerProperty, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_CUSTOMER_PROPERTY, res.data));
		}
	};
};
// 获取人口核增详情
const getPopulationChange = data => {
	return async dispatch => {
		const res = await http.restGet(api.getPopulationChange, data);
		if (res.code === 0) {
			dispatch(getCustomer(res.data.customerId));
			dispatch(payload(actionTypes.GET_POPULATION_CHANGE, res.data));

		}
	};
};
// 获取用水分类变更详情
const getWaterUseKindChange = data => {
	return async dispatch => {
		const res = await http.restGet(api.getWaterUseKindChange, data);
		if (res.code === 0) {
			dispatch(getCustomer(res.data.customerId));
			dispatch(payload(actionTypes.GET_WATER_USE_KIND_CHANGE, res.data));
		}
	};
};
// 获取提现详情
const getWithdraw = data => {
	return async dispatch => {
		const res = await http.restGet(api.getWithdraw, data);
		if (res.code === 0) {
			dispatch(getCustomer(res.data.customerId));
			dispatch(payload(actionTypes.GET_WITHDRAW, res.data));
		}
	};
};
// 获取特抄收费核减详情
const getSpecialFeeMinus = data => {
	return async dispatch => {
		const res = await http.restGet(api.getSpecialFeeMinus, data);
		if (res.code === 0) {
			dispatch(getCustomer(res.data.customerId));
			dispatch(payload(actionTypes.GET_SPECIAL_FEE_MINUS, res.data));
		}
	};
};
// 获取换表详情
const getWaterMeterChange = data => {
	return async dispatch => {
		const res = await http.restGet(api.getWaterMeterChange, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_WATER_METER_CHANGE, res.data));
		}
	};
};
// 获取拆表详情
const getWaterMeterDismantle = data => {
	return async dispatch => {
		const res = await http.restGet(api.getWaterMeterDismantle, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_WATER_METER_DISMANTLE, res.data));
		}
	};
};
//批量开户详情
const getDetailList = data => {
	return async dispatch => {
		const res = await http.post(api.getDetailList, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_DETAIL_LIST, res.data));
		}
	};
};
//批量开户详情
const getDetail = data => {
	return async dispatch => {
		const res = await http.restGet(api.getDetail, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_DETAIL, res.data));
		}
	};
};
// 获取特困户详情
const getLowHousehold = data => {
	return async dispatch => {
		const res = await http.restGet(api.getLowHousehold,data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_LOW_HOUSEHOLD, res.data));
		}
	};
};
// 低保
const getSubsistence = data => {
	return async dispatch => {
		const res = await http.restGet(api.getSubsistence, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_SUBSISTENCE, res.data))
		}
	}
};
// 获取水费水量详情
const getAdjustment = data => {
	return async dispatch => {
		const res = await http.restGet(api.getAdjustment, data);
		if (res.code === 0) {
			dispatch(getCustomer(res.data.customerId));
			dispatch(payload(actionTypes.GET_ADJUSTMENT, res.data))
		}
	}
};
// 获取订单退款详情
const getOrderDetail = data => {
	return async dispatch => {
		const res = await http.restGet(api.getOrderDetail, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_ORDER_DETAIL, res.data));
		}
	};
};
//获取核减详情
const getMinus=data=>{
	return async dispatch => {
		const res = await http.restGet(api.getMinus, data);
		if (res.code === 0) {
			dispatch(getCustomer(res.data.customerId));
			dispatch(payload(actionTypes.GET_MINUS, res.data));
		}
	};
}
//获取批量销户详情
const getCancelAccount=data=>{
	return async dispatch => {
		const res = await http.post(api.getCancelAccount, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_CANCEL_ACCOUNT, res.data));
		}
	};
}

const getSpecialFee = data => {
	return async dispatch => {
		const res = await http.restGet(api.getSpecialFee, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_SPECIAL_FEE, res.data));
		}
	};
}

const getReissueWaterPresentNumber = data => {
	return async dispatch => {
		const res = await http.restGet(api.getReissueWaterPresentNumber, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_REISSUE_WATERPRESENTNUMBER, res.data));
		}
	};
}

//获取呆坏账信息
const getBadMark = data => {
	return async dispatch => {
		const res = await http.restGet(api.getBadMark, data)
		if (res.code === 0){
			dispatch(payload(actionTypes.GET_BADMARK, res.data));
		}
	}
}

export {
	setState,
	list,
	del,
	listReview,
	getCustomer,
	getCustomerProperty,
	getPopulationChange,
	getWaterUseKindChange,
	getWithdraw,
	getSpecialFeeMinus,
	getWaterMeterChange,
	getWaterMeterDismantle,
	getDetailList,
	getDetail,
	getLowHousehold,
	getSubsistence,
	getAdjustment,
	getOrderDetail,
	getMinus,
	getCancelAccount,
	getSpecialFee,
	getReissueWaterPresentNumber,
	getBadMark
};
