import React, { Component } from 'react';
import { actionCreators } from './store';
import { connect } from 'react-redux';
import { Modal, Table, Form, Select, Row, Col, Input, Button, DatePicker, Timeline, Tag } from 'antd';
import { constants } from '$utils';
import { APPLY_TYPE, APPLY_STATUS } from '@/constants/process';
import CustomerProperty from '../../components/CustomerProperty';
import PopulationChange from '../../components/PopulationChange';
import Withdraw from '../../components/Withdraw';
import SpecialFeeMinus from '../../components/SpecialFeeMinus';
import WaterMeterChange from '../../components/WaterMeterChange';
import WaterMeterDismantle from '../../components/WaterMeterDismantle';
import WaterUseKindChange from '../../components/WaterUseKindChange';
import BatchInfo from '../../components/BatchInfo';
import LowHousehold from '../../components/LowHousehold';
import Subsistence from '../../components/Subsistence';
import Adjustment from '../../components/Adjustment';
import OrderRefund from '../../components/OrderRefund';
import Minus from '../../components/minus';
import CancelAccount from '../../components/cancelAccount';
import SpecialFee from '../../components/SpecialFee';
import UserModifyRecord from '../../../users/modules/infos/components/userModifyRecord'; // 用户信息修改记录
import './index.scss';
import qs from 'qs'
import BadMark from "../../components/badMark";

const FormItem = Form.Item;
const { Option } = Select;
const ButtonGroup = Button.Group;
const { RangePicker } = DatePicker;
const defaultSearchForm = {
	applyNo: '',
	applyType: null,
	applyStatus: null,
	applyStartTime: '',
	applyEndTime: ''
};


class Index extends Component {

	constructor(props) {
		super(props);
		this.state = {
			expand: false, // 搜索项过多的时候，隐藏部分
			page: 1,
			pageSize: 10,
			searchForm: defaultSearchForm,
			applyId: null,
			applyType: '',
			reviewModalVisible: false
		};
	}

	componentDidMount() {
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields && isTags) {
			this.setState({
				...fields.state
			}, () => {
				this.props.form.setFieldsValue({
					...fields.form
				});
			});
		} else {
			this.getList();
		}
	}

	componentWillUnmount() {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			});
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			});
		}
	}

	// 获取列表
	getList = () => {
		const { page, pageSize, searchForm } = this.state;
		this.props.list(Object.assign({ ...searchForm }, { page, pageSize }));
	};

	// 列表分页
	handlePageChange = page => {
		this.setState(
			{
				page
			},
			() => {
				this.getList();
			}
		);
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState(
			{
				page: 1,
				pageSize: size
			},
			() => {
				this.getList();
			}
		);
	};

	// 搜索功能
	handleSearch = () => {
		this.setState(
			{
				page: 1
			},
			() => {
				this.getList();
			}
		);
	};

	// 重置搜索
	handleReset = () => {
		this.props.form.setFieldsValue({
			rangPicker: [undefined, undefined]
		});
		this.setState(
			{
				searchForm: Object.assign({}, defaultSearchForm)
			},
			() => {
				this.getList();
			}
		);
	};

	// 日期查询条件
	getDate = (date, dateString) => {
		if (dateString.length === 2) {
			this.setState({
				searchForm: { ...this.state.searchForm, applyStartTime: dateString[0], applyEndTime: dateString[1] }
			});
		}
	};

	// 渲染搜索
	_renderSearchForm = () => {
		const { getFieldDecorator } = this.props.form;
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					<Col span={8}>
						<FormItem label={'申请编号:'}>
							<Input
								placeholder="请输入申请编号"
								value={this.state.searchForm.applyNo}
								onChange={v => {
									this.setState({
										searchForm: { ...this.state.searchForm, applyNo: v.target.value }
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'申请类别:'}>
							<Select
								placeholder="请选择"
								value={this.state.searchForm.applyType}
								onChange={v => {
									this.setState({
										searchForm: { ...this.state.searchForm, applyType: v }
									});
								}}
							>
								{APPLY_TYPE.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'申请状态:'}>
							<Select
								placeholder="请选择"
								value={this.state.searchForm.applyStatus}
								onChange={v => {
									this.setState({
										searchForm: { ...this.state.searchForm, applyStatus: v }
									});
								}}
							>
								{APPLY_STATUS.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={8}>
						<FormItem label={'申请时间:'}>{getFieldDecorator('rangPicker', { rules: [{ type: 'array' }] })(<RangePicker
							onChange={this.getDate} placeholder={['开始时间', '结束时间']}/>)}</FormItem>
					</Col>
					<Col span={8}/>
				</Row>
				<Row>
					<Col span={16} offset={8} align="right">
						<FormItem>
							<Button type="primary" onClick={this.handleSearch}>
								搜索
							</Button>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<Button type="default" onClick={this.handleReset}>
								重置
							</Button>
						</FormItem>
					</Col>
				</Row>
			</Form>
		);
	};

	// 渲染操作按钮
	_renderOperationButton = () => {
		return <Col/>;
	};

	// 查看
	handleView = record => {
		this.setState({ applyId: record.id, applyType: record.applyType });
		if (record.applyType === '居民过户') {
			this.props.getCustomerProperty(record.id);
		}
		if (record.applyType === '非居民过户') {
			this.props.getCustomerProperty(record.id);
		}
		if (record.applyType === '人口核增') {
			this.props.getPopulationChange(record.id);
		}
		if (record.applyType === '变更用水分类') {
			this.props.getWaterUseKindChange(record.id);
		}
		if (record.applyType === '提现') {
			this.props.getWithdraw(record.id);
		}
		if (record.applyType === '特抄收费核减') {
			this.props.getSpecialFeeMinus(record.id);
		}
		if (record.applyType === '非居民水表更换') {
			this.props.getWaterMeterChange(record.id);
		}
		if (record.applyType === '居民水表更换') {
			this.props.getWaterMeterChange(record.id);
		}
		if (record.applyType === 'DN50以下拆表') {
			this.props.getWaterMeterDismantle(record.id);
		}
		if (record.applyType === 'DN50及以上拆表') {
			this.props.getWaterMeterDismantle(record.id);
		}
		if (record.applyType === '开户/批量开户') {
			this.props.getDetailList({ page: 1, pageSize: 10, applyId: record.id });
			this.props.getDetail(record.id);
		}
		if (record.applyType === '特困户申请') {
			this.props.getSubsistence(record.id);
		}
		if (record.applyType === '低保户申请') {
			this.props.getLowHousehold(record.id);
		}
		if (record.applyType === '当日订单退款') {
			this.props.getOrderDetail(record.id);
		}
		if (record.applyType === '隔日订单退款') {
			this.props.getOrderDetail(record.id);
		}
		if (record.applyType === '未销水量水费调整') {
			this.props.getAdjustment(record.id);
		}
		if (record.applyType === '已销调整非居民户水量、水费审批') {
			this.props.getAdjustment(record.id);
		}
		if (record.applyType === '未销居民水量水费核减审批1-20吨') {
			this.props.getMinus(record.id);
		}
		if (record.applyType === '未销居民水量水费核减审批21-200吨') {
			this.props.getMinus(record.id);
		}
		if (record.applyType === '未销居民水量水费核减审批200吨以上') {
			this.props.getMinus(record.id);
		}
		if (record.applyType === '已销核减非居民户水量、水费审批') {
			this.props.getMinus(record.id);
		}
		if (record.applyType === '特抄收费作废') {
			this.props.getSpecialFee(record.id);
		}
		if (record.applyType === '销户/批量销户'||record.applyType ==='快捷销户/批量销户') {
			let data={page:1,pageSize:10}
			data.applyId=record.id
			this.props.getCancelAccount(data);
		}

		if (record.applyType === '呆坏账') {
			this.props.getBadMark(record.id);
		}
		this.props.setState([{ key: 'visible', value: true }]);
	};

	// 取消弹窗
	handleCancel = () => {
		this.props.setState([{ key: 'visible', value: false }]);
	};

	// 查看审批流
	handleViewAudit = record => {
		this.setState({ reviewModalVisible: true });
		this.props.listReview(record.id);
	};

	// 取消审批流弹窗
	handleCancelAudit = () => {
		this.setState({ reviewModalVisible: false });
	};

	// 删除记录
	handleDel = record => {
		const _this = this;
		Modal.confirm({
			title: '操作确认',
			content: '是否确认撤销该申请？',
			okText: '确定',
			cancelText: '取消',
			onOk() {
				_this.props.del(record.id, _this.getList);
			}
		});
	};

	// 渲染详情
	_renderDetailModal = () => {
		const { applyType,applyId } = this.state;
		const { detail, customer, visible, infoList } = this.props;
		return (
				visible&&<Modal className="my-apply-modal" title={'详情'} visible={visible} onCancel={this.handleCancel} footer={null}>
				{(() => {
					switch (applyType) {
						case '居民过户':
							return <CustomerProperty detail={detail} customer={customer}/>;
						case '非居民过户':
							return <CustomerProperty detail={detail} customer={customer}/>;
						case '人口核增':
							return <PopulationChange detail={detail} customer={customer}/>;
						case '变更用水分类':
							return <WaterUseKindChange detail={detail} customer={customer}/>;
						case '提现':
							return <Withdraw detail={detail} customer={customer}/>;
						case '特抄收费核减':
							return <SpecialFeeMinus detail={detail} customer={customer}/>;
						case '非居民水表更换':
							return <WaterMeterChange detail={detail}/>;
						case '居民水表更换':
							return <WaterMeterChange detail={detail}/>;
						case 'DN50以下拆表':
							return <WaterMeterDismantle detail={detail}/>;
						case 'DN50及以上拆表':
							return <WaterMeterDismantle detail={detail}/>;
						case '开户/批量开户':
							return <BatchInfo detail={detail} infoList={infoList}
																getDetailList={(data) => this.props.getDetailList(data)}/>;
						case '特困户申请':
							return <LowHousehold detail={detail}/>;
						case '低保户申请':
							return <Subsistence detail={detail}/>;
						case '当日订单退款':
							return <OrderRefund detail={detail}/>;
						case '隔日订单退款':
							return <OrderRefund detail={detail}/>;
						case '未销居民水量水费核减审批1-20吨':
							return <Minus detail={detail} customer={customer}/>;
						case '未销居民水量水费核减审批21-200吨':
							return <Minus detail={detail} customer={customer}/>;
						case '未销居民水量水费核减审批200吨以上':
							return <Minus detail={detail} customer={customer}/>;
						case '已销核减非居民户水量、水费审批':
							return <Minus detail={detail} customer={customer}/>;
						case '未销水量水费调整':
							return <Adjustment detail={detail} customer={customer}/>;
						case '已销调整非居民户水量、水费审批':
							return <Adjustment detail={detail} customer={customer}/>;
						case '销户/批量销户':
							return <CancelAccount detail={detail}  applyId={applyId} getCancelAccount={(data)=>this.props.getCancelAccount(data)}/>;
						case '快捷销户/批量销户':
							return <CancelAccount detail={detail}  applyId={applyId} getCancelAccount={(data)=>this.props.getCancelAccount(data)}/>;
						case '特抄收费作废':
							return <SpecialFee detail={detail} handleCancel={this.handleCancel} />;
						case '补发水量':
							return <UserModifyRecord tabIndex={2} isKey={8} applyId={applyId} />;
						case '更改表号':
							return <UserModifyRecord tabIndex={2} isKey={8} applyId={applyId} />;
						case '呆坏账':
							return <BadMark detail={detail}  />;
						default:
							return null;
					}
				})()}
			</Modal>
		);
	};

	// 渲染审批流
	_renderAuditModal = () => {
		const { reviewModalVisible } = this.state;
		const { reviewList } = this.props;
		return (
			<Modal className="my-apply-modal" title={'审批进度'} visible={reviewModalVisible} onCancel={this.handleCancelAudit}
						 footer={null}>
				<Timeline>
					{reviewList &&
					reviewList.map(item => {
						{
							return item.reviewPersonName ?
								<Timeline.Item color="green">已审批：【 {item.reviewPersonName} 】</Timeline.Item> :
								<Timeline.Item color="red">待审批：【 {item.roleName} 】</Timeline.Item>;
						}
					})}
				</Timeline>
			</Modal>
		);
	};

	render() {
		const { page, pageSize } = this.state;
		const { dataList, total } = this.props;
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: total => {
				return `共 ${total} 条数据 `;
			},
			onChange: page => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		// 申请状态颜色
		const applyStatusColorMap = new Map();
		applyStatusColorMap.set('进行中', 'green');
		applyStatusColorMap.set('已拒绝', 'red');
		applyStatusColorMap.set('已完成', 'blue');
		applyStatusColorMap.set('待回填', 'gold');
		const columns = [
			{
				title: '申请编号',
				dataIndex: 'applyNo',
				width: 40,
				align: 'center'
			},
			{
				title: '申请类型',
				dataIndex: 'applyType',
				width: 120,
				align: 'center'
			},
			{
				title: '申请时间',
				dataIndex: 'createTime',
				width: 120,
				align: 'center'
			},
			{
				title: '申请状态',
				dataIndex: 'applyStatus',
				width: 80,
				align: 'center',
				render: text => {
					return (
						<Tag color={applyStatusColorMap.get(text)}>
							{text}
						</Tag>
					);
				}
			},
			{
				title: '操作',
				key: 'operation',
				width: 120,
				align: 'center',
				render: (text, record, index) => {
					return (
						<span>
								<ButtonGroup>
									<Button title="查看详情" className="btn" type="primary" size="small" icon="eye"
													onClick={() => this.handleView(record)}/>
									<Button title="查看审批流" className="btn" type="primary" size="small" icon="audit"
													onClick={() => this.handleViewAudit(record)}/>
								{/*	<Button title="删除" className="btn" type="danger" size="small" icon="delete"
													onClick={() => this.handleDel(record)}/>*/}
								</ButtonGroup>
							</span>
					);
				}
			}
		];
		return (
			<div className="shadow-radius my-apply">
				<Row>
					<Col>
						<h1>我的申请</h1>
					</Col>
				</Row>
				<Row>{this._renderSearchForm()}</Row>
				<Row>{this._renderOperationButton()}</Row>
				<Row className="main">
					<Table bordered columns={columns} rowKey={() => Math.random()} dataSource={dataList}
								 pagination={paginationProps}/>
				</Row>
				{this._renderDetailModal()}
				{this._renderAuditModal()}
			</div>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('apply');
	return {
		fields: data.fields,
		dataList: data.dataList, // 列表数据
		total: data.total, // 总条数
		visible: data.visible, // 是否显示弹窗
		detail: data.detail, // 获取详情
		customer: data.customer, // 用户详情
		reviewList: data.reviewList, // 审批流详情
		infoList: data.infoList       //批量开户详情
	};
};
const mapDispatchToProps = dispatch => ({
	setState: data => dispatch(actionCreators.setState(data)), // 设置redux值
	list: data => dispatch(actionCreators.list(data)), // 获取列表
	del: (data, list) => dispatch(actionCreators.del(data, list)), // 删除记录
	listReview: data => dispatch(actionCreators.listReview(data)), // 获取审批流详情
	getCustomer: data => dispatch(actionCreators.getCustomer(data)), // 获取用户详情
	getCustomerProperty: data => dispatch(actionCreators.getCustomerProperty(data)), // 获取过户详情
	getPopulationChange: data => dispatch(actionCreators.getPopulationChange(data)), // 获取人口核增详情
	getWaterUseKindChange: data => dispatch(actionCreators.getWaterUseKindChange(data)), // 获取用水性质变更详情
	getWithdraw: data => dispatch(actionCreators.getWithdraw(data)), // 获取提现详情
	getSpecialFeeMinus: data => dispatch(actionCreators.getSpecialFeeMinus(data)), // 获取特抄收费核减详情
	getWaterMeterChange: data => dispatch(actionCreators.getWaterMeterChange(data)), // 获取换表详情
	getWaterMeterDismantle: data => dispatch(actionCreators.getWaterMeterDismantle(data)), // 获取拆表详情
	getDetailList: data => dispatch(actionCreators.getDetailList(data)), // 获取批量开户列表详情
	getDetail: data => dispatch(actionCreators.getDetail(data)),         //获取批量开户分页
	getLowHousehold: data => dispatch(actionCreators.getLowHousehold(data)),     //获取特困户详情
	getSubsistence: data => dispatch(actionCreators.getSubsistence(data)), // 获取详情
	getAdjustment: data => dispatch(actionCreators.getAdjustment(data)), // 获取水费水量跳转
	getOrderDetail: data => dispatch(actionCreators.getOrderDetail(data)), // 获取退款订单详情
	getMinus: data => dispatch(actionCreators.getMinus(data)),                     //获取核减详情
	getCancelAccount: data => dispatch(actionCreators.getCancelAccount(data)),               //获取批量销户详情
	getSpecialFee: data => dispatch(actionCreators.getSpecialFee(data)),           //获取特超收费信息
	getReissueWaterPresentNumber: data => dispatch(actionCreators.getReissueWaterPresentNumber(data)),           //获取补发水量信息
	getBadMark: data => dispatch(actionCreators.getBadMark(data)),          //获取特超收费信息

});
export default connect(
	mapStateToProps,
	mapDispatchToProps
)(Form.create()(Index));
