import * as actionTypes from './constants';
import { dataType } from '$utils';

const defaultState = {
	fields: null,
	dataList: [], // 列表数据
	total: 0, // 总条数
	detail: null, // 详情
	customer: null, // 用户详情
	visible: false, // 涉及到交互的弹窗显示控制 默认为false
	applyUserList: [], // 申请人员列表
	reviewList: [], // 审批流详情
	infoList:[],  //批量开户列表
};

// 重要提示： 请做好各代码段的注释！！！
export default (state = defaultState, action) => {
	switch (action.type) {
		// 回填组件传给redux的值
		case actionTypes.SET_STATE:
			let _data = action.data;
			if (dataType(_data) === 'Object') {
				state[_data.key] = _data.value;
			}
			if (dataType(_data) === 'Array') {
				_data.forEach(item => {
					state[item.key] = item.value;
				});
			}
			return { ...state };
		// 回填列表数据
		case actionTypes.LIST_RECORD:
			state.dataList = action.data.rows;
			state.total = action.data.total;
			return { ...state };

	  case actionTypes.GETAPPLYVAT:
			state.detail = action.data;
	   return { ...state };
		// 获取申请人选择框
		case actionTypes.GET_APPLY_USER_SELECT:
			state.applyUserList = action.data;
			return { ...state };
		// 获取审批流详情
		case actionTypes.LIST_REVIEW:
			state.reviewList = action.data;
			return { ...state };
		// 获取用户详情
		case actionTypes.GET_CUSTOEMR:
			state.customer = action.data;
			return { ...state };
		// 获取过户详情
		case actionTypes.GET_CUSTOMER_PROPERTY:
			state.detail = action.data;
			return { ...state };
		// 获取人口核增详情
		case actionTypes.GET_POPULATION_CHANGE:
			state.detail = action.data;
			return { ...state };
		// 获取变更用水性质详情
		case actionTypes.GET_WATER_USE_KIND_CHANGE:
			state.detail = action.data;
			return { ...state };
		// 获取提现详情
		case actionTypes.GET_WITHDRAW:
			state.detail = action.data;
			return { ...state };
		// 获取特抄收费核减详情
		case actionTypes.GET_SPECIAL_FEE_MINUS:
			state.detail = action.data;
			return { ...state };
		// 获取换表详情
		case actionTypes.GET_WATER_METER_CHANGE:
			state.detail = action.data;
			return { ...state };
		// 获取拆表详情
		case actionTypes.GET_WATER_METER_DISMANTLE:
			state.detail = action.data;
			return { ...state };
		// 获取批量开户列表
		case actionTypes.GET_DETAIL_LIST:
			state.infoList = action.data;
			return { ...state };
		//查看列表
		case actionTypes.GET_DETAIL:
			state.detail = action.data;
			return {...state};
		//特困户
		case actionTypes.GET_LOW_HOUSEHOLD:
			state.detail = action.data;
			return {...state};
		//低保
		case actionTypes.GET_SUBSISTENCE:
			state.detail = action.data;
			return {...state};
		//水费水量
		case actionTypes.GET_ADJUSTMENT:
			state.detail = action.data;
			return {...state};
		//订单退款详情
		case actionTypes.GET_ORDER_DETAIL:
			state.detail = action.data;
			return {...state};
		//获取核减详情
		case actionTypes.GET_MINUS:
			state.detail = action.data;
			return {...state};
		//获取批量销户详情
		case actionTypes.GET_CANCEL_ACCOUNT:
			state.detail = action.data;
			return {...state};
		case actionTypes.GET_SPECIAL_FEE:
			state.detail = action.data;
			return {...state};
		case actionTypes.GET_MCMARECORD:
			state.detail = action.data;
			return {...state};
		default:
			return state;
	}
};
