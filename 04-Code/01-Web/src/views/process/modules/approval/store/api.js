export default {
	listToReview: `/api/process/review/my/toReview`,
	listHasReview: `/api/process/review/my/hasReview`,
	listReview: `/api/process/review/list`,
	review: `/api/process/review/review`,
	getCustomer: `/api/cm/customer/get`,
	getCustomerProperty: `/api/cm/customer/property/getByApply`,
	getPopulationChange: `/api/cm/customer/population/change/getByApply`,
	getWaterUseKindChange: `/api/cm/customer/waterUseKind/change/getByApply`,
	getWithdraw: `/api/cm/customer/withdraw/getByApply`,
	getSpecialFeeMinus: `/api/charge/specialFee/getMinusByApply`,
	getWaterMeterChange: `/api/cm/customer/waterMeter/change/getByApply`,
	getWaterMeterDismantle: `/api/cm/customer/waterMeter/dismantle/getByApply`,
	getDetailList:`/api/cm/customer/import/detail/listPage`,
	getDetail:`/api/cm/customer/import`,
	getLowHousehold:`/api/cm/lowHousehold/getByApplyId`,
	getSubsistence: `/api/cm/poorHousehold/getByApplyId`,
	getAdjustment: `/api/bill/change/adjustment/getByApplyId`,
	getOrderDetail: `/api/charge/order/getByApplyId`,
	getMinus:`/api/bill/change/minus/getByApplyId`,
	getCancelAccount:'api/cm/customer/batchCancel/detail/listPage',
	getApplyUserSelect: `/api/process/apply/getApplyUserSelect`,
	getSpecialFee: 'api/charge/specialFee/getByApplyId',
	getBadMark: 'api/bill/getBadMarkDetail',

};
