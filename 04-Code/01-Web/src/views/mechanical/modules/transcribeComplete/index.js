import React, { Component, Fragment } from 'react';
import { actionCreators } from './store';
import { connect } from 'react-redux';
import http from '$http';
import {
	Modal,
	Table,
	Form,
	Select,
	Row,
	Col,
	Input,
	Button,
	TreeSelect,
	Icon,
	Tag,
	DatePicker
} from 'antd';
import { constants } from '$utils';
import './index.scss';
import {modify} from "./store/actionCreators";
import moment from "moment/moment";

const { confirm } = Modal;
const FormItem = Form.Item;
const { Option } = Select;
const ButtonGroup = Button.Group;
const { TreeNode } = TreeSelect;
const InputGroup = Input.Group;

const defaultSearchForm = {
	transcriberIds: [],
	transcribeYear: null,
};

class Index extends Component {

	constructor(props) {
		super(props);
		this.state = {
			expand: false, // 搜索项过多的时候，隐藏部分
			page: 1,
			pageSize: 10,
			searchForm: defaultSearchForm,
			isOpen: false,
			detailVisible: false,
			addVisible: false,
			updateVisible: false
		};
	}

	componentDidMount() {
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields && isTags) {
			this.setState({
				...fields.state
			}, () => {
				this.props.form.setFieldsValue({
					...fields.form
				});
			});
		} else {
			this.getList();
			this.props.transcribersList();
		}
	}

	componentWillUnmount() {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			});
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			});
		}
	}

	// 获取列表
	getList = () => {
		const { page, pageSize, searchForm } = this.state;
		let params = {
			page,
			pageSize,
			...searchForm,
			transcribeYear: searchForm.transcribeYear ? moment(searchForm.transcribeYear).format("YYYY"): searchForm.transcribeYear
		};
		this.props.list(params);
	};

	// 列表分页
	handlePageChange = (page) => {
		this.setState(
			{
				page
			},
			() => {
				this.getList();
			}
		);
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState(
			{
				page: 1,
				pageSize: size
			},
			() => {
				this.getList();
			}
		);
	};

	// 搜索功能
	handleSearch = () => {
		this.setState(
			{
				page: 1
			},
			() => {
				this.getList();
			}
		);
	};

	// 重置搜索
	handleReset = () => {
		this.props.form.setFieldsValue({
			rangPicker: [undefined, undefined]
		});
		this.setState(
			{
				searchForm: defaultSearchForm
			},
			() => {
				this.getList();
			}
		);
	};

	handleAdd = () => {
		this.props.uncompleteTranscribes(() => this.setState({addVisible: true}));
	};
	// 获取详情
	handleView = record => {
		this.props.getDetail(record.id, () => this.setState({detailVisible: true}));
	};
	// 获取详情
	handleModify = record => {
		this.props.getDetail(record.id, () => this.setState({updateVisible: true}));
	};
	// 提交信息
	handleSubmit = e => {
		e.preventDefault();
		this.props.form.validateFields((err, values) => {
			if (!err) {
				this.props.modify(values, this.getList);
				this.props.form.resetFields()
				this.setState({updateVisible: false});
			}
		});
	};
	handleSubmits = e => {
		e.preventDefault();
		this.props.form.validateFields((err, values) => {
			if (!err) {
				if (values.completes && values.completes.length){
					this.props.add(values.completes, this.getList);
				}
				this.setState({addVisible: false});
			}
		});
	};
	// 删除记录
	handleDel = (record) => {
		const _this = this;
		confirm({
			title: '确认操作',
			content: '是否确认删除该记录？',
			okText: '确定',
			okType: 'danger',
			cancelText: '取消',
			onOk() {
				_this.props.del(record.id, _this.getList);
			},
		});
	};

	// 渲染搜索
	_renderSearchForm = () => {
		const { searchForm } = this.state;
		const { transcribers } = this.props;
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					<Col span={8}>
						<FormItem label={'抄表员:'} labelCol={{span:9}}>
							<Select
								mode="multiple"
								showSearch
								placeholder="请选择"
								value={searchForm.transcriberIds}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, transcriberIds: v } });
								}} showArrow>
								{transcribers.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'年份:'}>
							<DatePicker
								open={this.state.isOpen}
								value={searchForm.transcribeYear}
								onOpenChange={status => this.setState({isOpen: status})}
								onPanelChange={value => {
									this.setState({ searchForm: { ...searchForm, transcribeYear: value }, isOpen: false})
								}}
								allowClear
								mode="year"
								format='YYYY'
								placeholder={'请选择年份'}
							/>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={24} align="right">
						<Button type="primary" onClick={this.handleSearch}>
							搜索
						</Button>
						&nbsp;&nbsp;&nbsp;&nbsp;
						<Button type="default" onClick={this.handleReset}>
							重置
						</Button>
					</Col>
				</Row>
			</Form>
		);
	};

	render() {
		const { page, pageSize, detailVisible, addVisible, updateVisible } = this.state;
		const { dataList, total, detail, completes } = this.props;
		const { getFieldDecorator, resetFields } = this.props.form;
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: (total) => {
				return `共 ${total} 条数据 `;
			},
			onChange: (page) => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		const columns = [
			{
				title: '抄表员',
				dataIndex: 'transcriberName',
				key: 'transcriberName',
				align: 'center'
			},
			{
				title: '年份',
				dataIndex: 'transcribeYear',
				key: 'transcribeYear',
				align: 'center'
			},
			{
				title: '第一季度应完成数',
				dataIndex: 'amountFirst',
				key: 'amountFirst',
				align: 'center'
			},
			{
				title: '第二季度应完成数',
				dataIndex: 'amountSecond',
				key: 'amountSecond',
				align: 'center'
			},
			{
				title: '第三季度应完成数',
				dataIndex: 'amountThird',
				key: 'amountThird',
				align: 'center'
			},
			{
				title: '第四季度应完成数',
				dataIndex: 'amountFourth',
				key: 'amountFourth',
				align: 'center'
			},
			{
				title: '第一季度应回收户数',
				dataIndex: 'countFirst',
				key: 'countFirst',
				align: 'center'
			},
			{
				title: '第二季度应回收户数',
				dataIndex: 'countSecond',
				key: 'countSecond',
				align: 'center'
			},
			{
				title: '第三季度应回收户数',
				dataIndex: 'countThird',
				key: 'countThird',
				align: 'center'
			},
			{
				title: '第四季度应回收户数',
				dataIndex: 'countFourth',
				key: 'countFourth',
				align: 'center'
			},
			{
				title: '创建时间',
				dataIndex: 'createTime',
				key: 'createTime',
				align: 'center'
			},
			{
				title: '创建人',
				dataIndex: 'createPersonName',
				key: 'createPersonName',
				align: 'center'
			},
			{
				title: '操作',
				key: 'operation',
				width: 180,
				align: 'center',
				fixed: 'right',
				render: (text, record, index) => {
						return (
							<span>
								<ButtonGroup>
									<Button
										title="查看"
										className="btn"
										type="primary"
										size="small"
										icon="eye"
										onClick={() => this.handleView(record)}
									/>
									<Button
										title="修改"
										className="btn"
										type="primary"
										size="small"
										icon="form"
										onClick={() => this.handleModify(record)}
									/>
									<Button
										title="删除"
										className="btn"
										type="danger"
										size="small"
										icon="delete"
										onClick={() => this.handleDel(record)}
									/>
								</ButtonGroup>
					&nbsp;
							</span>
						);
				}
			}
		]
		return (
			<div className="shadow-radius bill-adjustment">
				<Row>
					<Col>
						<h1>定额收费计划</h1>
					</Col>
				</Row>
				<Row>{this._renderSearchForm()}</Row>
				<Row>
					<Button type="primary" className="btn" onClick={this.handleAdd}>
						新增
					</Button>
				</Row>
				<Row className="main">
					<Table
						bordered
						columns={columns}
						rowKey={record => record.id}
						dataSource={dataList}
						pagination={paginationProps}
					/>
				</Row>

				<Modal title="详情" visible={detailVisible} onCancel={() => this.setState({detailVisible: false, detail: null})} footer={null}>
					<Row>
						{
							detail ?
								<Form {...constants.formItemLayout}>
									<FormItem label='抄表员：'>{detail.transcriberName}</FormItem>
									<FormItem label='年份：'>{detail.transcribeYear}</FormItem>
									<FormItem label='第一季度应完成数：'>{detail.amountFirst}</FormItem>
									<FormItem label='第二季度应完成数：'>{detail.amountSecond}</FormItem>
									<FormItem label='第三季度应完成数：'>{detail.amountThird}</FormItem>
									<FormItem label='第四季度应完成数：'>{detail.amountFourth}</FormItem>
									<FormItem label='第一季度应回收户数：'>{detail.countFirst}</FormItem>
									<FormItem label='第二季度应回收户数：'>{detail.countSecond}</FormItem>
									<FormItem label='第三季度应回收户数：'>{detail.countThird}</FormItem>
									<FormItem label='第四季度应回收户数：'>{detail.countFourth}</FormItem>
									<FormItem label='创建时间：'>{detail.createTime}</FormItem>
									<FormItem label='创建人：'>{detail.createPersonName}</FormItem>
								</Form>
								: null
						}
					</Row>
				</Modal>

				<Modal title="新增" className="water-meter-modal" style={{width: "95vw"}} visible={addVisible} onCancel={() => this.setState({addVisible: false})} footer={null}>
					<Form {...constants.formItemLayout}>
						<Row>
							<Col span={2}></Col>
							<Col span={2}>应完成数(元)</Col>
							<Col span={2}>第一季度</Col>
							<Col span={2}>第二季度</Col>
							<Col span={2}>第三季度</Col>
							<Col span={2}>第四季度</Col>
							<Col span={1}></Col>
							<Col span={2}>应回收户数</Col>
							<Col span={2}>第一季度</Col>
							<Col span={2}>第二季度</Col>
							<Col span={2}>第三季度</Col>
							<Col span={2}>第四季度</Col>
						</Row>
						{/*{
							completes.map(({label, value}, index) => {
								return (<Row>
									<Col span={2}>
										<FormItem label="抄表员：">
											{label} {getFieldDecorator(`completes[${index}].transcriberId`, { initialValue: value })(<Input type='hidden'/>)}
										</FormItem>
									</Col>
									<Col span={2}>
										<FormItem label="年份">
											{new Date().getFullYear()} {getFieldDecorator(`completes[${index}].transcribeYear`, {initialValue: new Date().getFullYear()})(<Input type='hidden'/>)}
										</FormItem>
									</Col>
									<Col span={10}>
										<FormItem label="第一季度应完成数(元)：">
											{getFieldDecorator(`completes[${index}].amountFirst`, {
												initialValue: null,
												rules: [
													{
														required: true,
														message: '请输入应完成数'
													}
												]
											})(<Input style={{width: 80}} palceholder="请输入应完成数"/>)
											}
										</FormItem>
									</Col>
									<Col span={10}>
										<FormItem label="第二季度应完成数(元)：">
											{getFieldDecorator(`completes[${index}].amountSecond`, {
												initialValue: null,
												rules: [
													{
														required: true,
														message: '请输入应完成数'
													}
												]
											})(<Input style={{width: 80}} palceholder="请输入应完成数"/>)
											}
										</FormItem>
									</Col>
									<Col span={10}>
										<FormItem label="第三季度应完成数(元)：">
											{getFieldDecorator(`completes[${index}].amountThird`, {
												initialValue: null,
												rules: [
													{
														required: true,
														message: '请输入应完成数'
													}
												]
											})(<Input style={{width: 80}} palceholder="请输入应完成数"/>)
											}
										</FormItem>
									</Col>
									<Col span={10}>
										<FormItem label="第四季度应完成数(元)：">
											{getFieldDecorator(`completes[${index}].amountFourth`, {
												initialValue: null,
												rules: [
													{
														required: true,
														message: '请输入应完成数'
													}
												]
											})(<Input style={{width: 80}} palceholder="请输入应完成数"/>)
											}
										</FormItem>
									</Col>
									<Col span={10}>
										<FormItem label="第一季度应回收户数：">
												{getFieldDecorator(`completes[${index}].countFirst`, { initialValue: null, rules: [
											{
												required: true,
												message: '请输入应回收户数'
											}
												]
											})(<Input style={{width: 80}} palceholder="请输入应回收户数"/>)
											}
										</FormItem>
									</Col>
									<Col span={10}>
										<FormItem label="第二季度应回收户数：">
											{getFieldDecorator(`completes[${index}].countSecond`, { initialValue: null, rules: [
													{
														required: true,
														message: '请输入应回收户数'
													}
												]
											})(<Input style={{width: 80}} palceholder="请输入应回收户数"/>)
											}
										</FormItem>
									</Col>
									<Col span={10}>
										<FormItem label="第三季度应回收户数：">
											{getFieldDecorator(`completes[${index}].countThird`, { initialValue: null, rules: [
													{
														required: true,
														message: '请输入应回收户数'
													}
												]
											})(<Input style={{width: 80}} palceholder="请输入应回收户数"/>)
											}
										</FormItem>
									</Col>
									<Col span={10}>
										<FormItem label="第四季度应回收户数：">
											{getFieldDecorator(`completes[${index}].countFourth`, { initialValue: null, rules: [
													{
														required: true,
														message: '请输入应回收户数'
													}
												]
											})(<Input style={{width: 80}} palceholder="请输入应回收户数"/>)
											}
										</FormItem>
									</Col>
								</Row>)
							})
						}*/}
						{
							completes.map(({label, value}, index) => {
								return (<Row>
									<Col span={2}>
										<FormItem label="抄表员：">
											{label} {getFieldDecorator(`completes[${index}].transcriberId`, { initialValue: value })(<Input type='hidden'/>)}
										</FormItem>
									</Col>
									<Col span={2}>
										<FormItem label="年份">
											{new Date().getFullYear()} {getFieldDecorator(`completes[${index}].transcribeYear`, {initialValue: new Date().getFullYear()})(<Input type='hidden'/>)}
										</FormItem>
									</Col>
									<Col span={2}>
										<FormItem>
											{getFieldDecorator(`completes[${index}].amountFirst`, {
												initialValue: null,
												rules: [
													{
														required: true,
														message: '请输入应完成数'
													}
												]
											})(<Input style={{width: 80}} palceholder="请输入应完成数"/>)
											}
										</FormItem>
									</Col>
									<Col span={2}>
										<FormItem>
											{getFieldDecorator(`completes[${index}].amountSecond`, {
												initialValue: null,
												rules: [
													{
														required: true,
														message: '请输入应完成数'
													}
												]
											})(<Input style={{width: 80}} palceholder="请输入应完成数"/>)
											}
										</FormItem>
									</Col>
									<Col span={2}>
										<FormItem>
											{getFieldDecorator(`completes[${index}].amountThird`, {
												initialValue: null,
												rules: [
													{
														required: true,
														message: '请输入应完成数'
													}
												]
											})(<Input style={{width: 80}} palceholder="请输入应完成数"/>)
											}
										</FormItem>
									</Col>
									<Col span={2}>
										<FormItem>
											{getFieldDecorator(`completes[${index}].amountFourth`, {
												initialValue: null,
												rules: [
													{
														required: true,
														message: '请输入应完成数'
													}
												]
											})(<Input style={{width: 80}} palceholder="请输入应完成数"/>)
											}
										</FormItem>
									</Col>
									<Col span={3}></Col>
									<Col span={2}>
										<FormItem>
											{getFieldDecorator(`completes[${index}].countFirst`, { initialValue: null, rules: [
													{
														required: true,
														message: '请输入应回收户数'
													}
												]
											})(<Input style={{width: 80}} palceholder="请输入应回收户数"/>)
											}
										</FormItem>
									</Col>
									<Col span={2}>
										<FormItem>
											{getFieldDecorator(`completes[${index}].countSecond`, { initialValue: null, rules: [
													{
														required: true,
														message: '请输入应回收户数'
													}
												]
											})(<Input style={{width: 80}} palceholder="请输入应回收户数"/>)
											}
										</FormItem>
									</Col>
									<Col span={2}>
										<FormItem>
											{getFieldDecorator(`completes[${index}].countThird`, { initialValue: null, rules: [
													{
														required: true,
														message: '请输入应回收户数'
													}
												]
											})(<Input style={{width: 80}} palceholder="请输入应回收户数"/>)
											}
										</FormItem>
									</Col>
									<Col span={2}>
										<FormItem>
											{getFieldDecorator(`completes[${index}].countFourth`, { initialValue: null, rules: [
													{
														required: true,
														message: '请输入应回收户数'
													}
												]
											})(<Input style={{width: 80}} palceholder="请输入应回收户数"/>)
											}
										</FormItem>
									</Col>
								</Row>)
							})
						}
						<Row>
							<Col span={24} align="center">
								<Fragment>
									<Button type="primary" className="btn" onClick={this.handleSubmits}>
										提交
									</Button>
									<Button type="default" className="btn" onClick={() => this.setState({addVisible: false})}>
										取消
									</Button>
								</Fragment>
							</Col>
						</Row>
					</Form>
				</Modal>

				<Modal title="修改" visible={updateVisible} onCancel={() => {
					resetFields()
					this.setState({updateVisible: false}
					)}} footer={null}>
					<Form {...constants.formItemLayout}>
						{detail? (<><Row>
								<Col>
									<FormItem label="抄表员：">
										{detail.transcriberName} {getFieldDecorator(`id`, {initialValue: detail.id})(<Input type='hidden'/>)}
									</FormItem>
								</Col>
							</Row>
							<Row>
								<Col>
									<FormItem label="年份">
										{detail.transcribeYear}
									</FormItem>
								</Col>
							</Row>
							<Row>
							<Col span={24}>
								<FormItem label="第一季度应完成数(元)：">
									{getFieldDecorator(`amountFirst`, {
										initialValue: detail.amountFirst,
										rules: [
											{
												required: true,
												message: '请输入应完成数'
											}
										]
									})(<Input palceholder="请输入应完成数"/>)
									}
								</FormItem>
							</Col>
							</Row>
							<Row>
								<Col span={24}>
									<FormItem label="第二季度应完成数(元)：">
										{getFieldDecorator(`amountSecond`, {
											initialValue: detail.amountSecond,
											rules: [
												{
													required: true,
													message: '请输入应完成数'
												}
											]
										})(<Input palceholder="请输入应完成数"/>)
										}
									</FormItem>
								</Col>
						</Row>
								<Row>
									<Col span={24}>
										<FormItem label="第三季度应完成数(元)：">
											{getFieldDecorator(`amountThird`, {
												initialValue: detail.amountThird,
												rules: [
													{
														required: true,
														message: '请输入应完成数'
													}
												]
											})(<Input palceholder="请输入应完成数"/>)
											}
										</FormItem>
									</Col>
								</Row>
								<Row>
									<Col span={24}>
										<FormItem label="第四季度应完成数(元)：">
											{getFieldDecorator(`amountFourth`, {
												initialValue: detail.amountFourth,
												rules: [
													{
														required: true,
														message: '请输入应完成数'
													}
												]
											})(<Input palceholder="请输入应完成数"/>)
											}
										</FormItem>
									</Col>

									<Col span={24}>
										<FormItem label="第一季度应回收户数：">
											{getFieldDecorator(`countFirst`, { initialValue: detail.countFirst, rules: [
													{
														required: true,
														message: '请输入应回收户数'
													}
												]
											})(<Input palceholder="请输入应回收户数"/>)
											}
										</FormItem>
									</Col>
									<Col span={24}>
										<FormItem label="第二季度应回收户数：">
											{getFieldDecorator(`countSecond`, { initialValue: detail.countSecond, rules: [
													{
														required: true,
														message: '请输入应回收户数'
													}
												]
											})(<Input palceholder="请输入应回收户数"/>)
											}
										</FormItem>
									</Col>
									<Col span={24}>
										<FormItem label="第三季度应回收户数：">
											{getFieldDecorator(`countThird`, { initialValue: detail.countThird, rules: [
													{
														required: true,
														message: '请输入应回收户数'
													}
												]
											})(<Input palceholder="请输入应回收户数"/>)
											}
										</FormItem>
									</Col>
									<Col span={24}>
										<FormItem label="第四季度应回收户数：">
											{getFieldDecorator(`countFourth`, { initialValue: detail.countFourth, rules: [
													{
														required: true,
														message: '请输入应回收户数'
													}
												]
											})(<Input palceholder="请输入应回收户数"/>)
											}
										</FormItem>
									</Col>
								</Row></>
						) : null
						}
						<Row>
							<Col span={24} align="center">
								<Fragment>
									<Button type="primary" className="btn" onClick={this.handleSubmit}>
										提交
									</Button>
									<Button type="default" className="btn" onClick={() => {
										resetFields()
										this.setState({updateVisible: false})
									}}>
										取消
									</Button>
								</Fragment>
							</Col>
						</Row>
					</Form>
				</Modal>
			</div>
		);
	}
}

const mapStateToProps = (state) => {
	const data = state.get('transcribeComplete');
	return {
		fields: data.fields,
		dataList: data.dataList, // 列表数据
		total: data.total, // 总条数
		visible: data.visible, // 是否显示弹窗
		detail: data.detail, // 获取详情
		transcribers: data.transcribers, // 抄表员
		completes: data.completes
	};
};
const mapDispatchToProps = (dispatch) => ({
	setState: (data) => dispatch(actionCreators.setState(data)), // 设置redux值
	list: (data) => dispatch(actionCreators.list(data)), // 获取列表
	add: (data, list) => dispatch(actionCreators.add(data, list)), // 删除记录
	del: (data, list) => dispatch(actionCreators.del(data, list)), // 删除记录
	modify: (data, list) => dispatch(actionCreators.modify(data, list)), // 删除记录
	getDetail: (data, action) => dispatch(actionCreators.detail(data, action)), // 获取详情
	transcribersList: () => dispatch(actionCreators.transcribersList()), // 抄表员
	uncompleteTranscribes: (action) => dispatch(actionCreators.uncompleteTranscribes(action)) // 抄表员
});
export default connect(mapStateToProps, mapDispatchToProps)(Form.create()(Index));
