import React, { Component } from 'react';
import { actionCreators } from './store';
import { connect } from 'react-redux';
import http from '$http';
import { Modal, Table, Form, Select, Row, Col, Input, Button, TreeSelect, Icon, Tag } from 'antd';
import { constants } from '$utils';
import { APPLY_STATUS_BY_OTHER } from '@/constants/order';
import Adjustment from '$components/bills/adjust';
import { statusColorMap,applyStatusColorMap} from '@/constants/colorStyle'
import './index.scss';
import { CHANGE_STATUS } from '../../../remote/modules/billMinusRecord/constants';

const { confirm } = Modal;
const FormItem = Form.Item;
const { Option } = Select;
const ButtonGroup = Button.Group;
const { TreeNode } = TreeSelect;
const InputGroup = Input.Group;

const defaultSearchForm = {
	cno: '',
	areaId: null,
	billNo: '',
	waterAmountStart: '',
	waterAmountEnd: '',
	feeStart: '',
	feeEnd: '',
	applyStatus: null,
	status: null
};

class Index extends Component {

	constructor(props) {
		super(props);
		this.state = {
			expand: false, // 搜索项过多的时候，隐藏部分
			page: 1,
			pageSize: 10,
			searchForm: defaultSearchForm,
		};
	}

	componentDidMount() {
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields && isTags) {
			this.setState({
				...fields.state
			}, () => {
				this.props.form.setFieldsValue({
					...fields.form
				});
			});
		} else {
			this.getList();
			this.props.areaList();
		}
	}

	componentWillUnmount() {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			});
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			});
		}
	}

	// 获取列表
	getList = () => {
		const { page, pageSize, searchForm } = this.state;
		let params = {
			page,
			pageSize,
			...searchForm
		};
		this.props.list(params);
	};

	// 列表分页
	handlePageChange = (page) => {
		this.setState(
			{
				page
			},
			() => {
				this.getList();
			}
		);
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState(
			{
				page: 1,
				pageSize: size
			},
			() => {
				this.getList();
			}
		);
	};

	// 搜索功能
	handleSearch = () => {
		this.setState(
			{
				page: 1
			},
			() => {
				this.getList();
			}
		);
	};

	// 重置搜索
	handleReset = () => {
		this.props.form.setFieldsValue({
			rangPicker: [undefined, undefined]
		});
		this.setState(
			{
				searchForm: defaultSearchForm
			},
			() => {
				this.getList();
			}
		);
	};

	// 获取详情
	handleView = record => {
		this.props.getDetail(record.id);
	};

	// 删除记录
	handleDel = (record) => {
		const _this = this;
		confirm({
			title: '确认操作',
			content: '是否确认作废该记录？',
			okText: '确定',
			okType: 'danger',
			cancelText: '取消',
			onOk() {
				_this.props.del(record.id, _this.getList);
			},
		});
	};

	getDate = (date, dateString) => {
		if (dateString.length === 2) {
			this.setState({
				searchForm: { ...this.state.searchForm, createStartDate: dateString[0], createEndDate: dateString[1] }
			});
		}
	};

	// 取消Modal
	handleCancel = () => {
		this.props.setState([{ key: 'visible', value: false }, { key: 'detail', value: null }]);
	};

	// 片区选择树
	areaOnChange = (value) => {
		this.setState({
			searchForm: {
				...this.state.searchForm,
				areaId: value
			}
		});
	};

	//导出
	export() {
		const { searchForm } = this.state;
		let url = `${process.env.API_ROOT}/api/bill/change/adjustment/exportChargeBillAdjustmentRecord`;
		http.export(url, searchForm, (res) => {
			const blob = new Blob([res]);
			const downloadElement = document.createElement('a');
			const href = window.URL.createObjectURL(blob);
			const fileName = '账单调整记录_' + new Date().getTime() + '.xlsx';
			downloadElement.href = href;
			downloadElement.download = fileName;
			document.body.appendChild(downloadElement);
			downloadElement.click();
			document.body.removeChild(downloadElement);
			window.URL.revokeObjectURL(href);
		});
	}

	// 渲染片区树
	_renderTreeLoop = areas => {
		return areas.map(item => {
			if (item.children) {
				return (
					<TreeNode key={item.id} title={item.name} value={item.id}>
						{this._renderTreeLoop(item.children)}
					</TreeNode>
				);
			} else {
				return <TreeNode key={item.id} title={item.name} value={item.id} />;
			}
		});
	};

	// 渲染搜索
	_renderSearchForm = () => {
		const { searchForm } = this.state;
		const { areas } = this.props;
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					<Col span={8}>
						<FormItem label={'用户编号:'}>
							<Input
								placeholder="请输入用户编号"
								value={searchForm.cno}
								onChange={(v) => {
									this.setState({
										searchForm: { ...searchForm, cno: v.target.value }
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'账单编号:'}>
							<Input
								placeholder="请输入账单编号"
								value={searchForm.billNo}
								onChange={(v) => {
									this.setState({
										searchForm: { ...searchForm, billNo: v.target.value }
									});
								}}
							/>
						</FormItem>
					</Col>
					{/* 片区 */}
					<Col span={8}>
						<FormItem label={'所属片区:'}>
							<TreeSelect
								showSearch
								filterTreeNode={(inputValue, treeNode) => treeNode.props.title.indexOf(inputValue) > -1}
								style={{ width: '100%' }}
								value={searchForm.areaId}
								dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
								placeholder="请选择所属片区"
								allowClear
								treeDefaultExpandedKeys={[100]}
								onChange={this.areaOnChange}
							>
								{
									this._renderTreeLoop(areas)
								}
							</TreeSelect>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={8}>
						<FormItem label={'调整水量:'}>
							<InputGroup compact>
								<Input
									style={{ width: '40%', textAlign: 'center' }}
									placeholder="起始水量"
									value={searchForm.waterAmountStart}
									onChange={(v) => {
										this.setState({
											searchForm: { ...searchForm, waterAmountStart: v.target.value }
										});
									}}
								/>
								<Input
									style={{
										width: '20%',
										borderLeft: 0,
										pointerEvents: 'none',
										backgroundColor: '#fff'
									}}
									placeholder="~"
									disabled
								/>
								<Input
									style={{ width: '40%', textAlign: 'center', borderLeft: 0 }}
									placeholder="结束水量"
									value={searchForm.waterAmountEnd}
									onChange={(v) => {
										this.setState({
											searchForm: { ...searchForm, waterAmountEnd: v.target.value }
										});
									}}
								/>
							</InputGroup>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'调整金额:'}>
							<InputGroup compact>
								<Input
									style={{ width: '40%', textAlign: 'center' }}
									placeholder="起始金额"
									value={searchForm.feeStart}
									onChange={(v) => {
										this.setState({
											searchForm: { ...searchForm, feeStart: v.target.value }
										});
									}}
								/>
								<Input
									style={{
										width: '20%',
										borderLeft: 0,
										pointerEvents: 'none',
										backgroundColor: '#fff'
									}}
									placeholder="~"
									disabled
								/>
								<Input
									style={{ width: '40%', textAlign: 'center', borderLeft: 0 }}
									placeholder="结束金额"
									value={searchForm.feeEnd}
									onChange={(v) => {
										this.setState({
											searchForm: { ...searchForm, feeEnd: v.target.value }
										});
									}}
								/>
							</InputGroup>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'申请状态:'}>
							<Select
								placeholder="请选择"
								value={searchForm.applyStatus}
								onChange={(v) => {
									this.setState({
										searchForm: { ...searchForm, applyStatus: v }
									});
								}}
							>
								{APPLY_STATUS_BY_OTHER.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={8}>
						<FormItem label={'状态:'}>
							<Select
								placeholder="请选择"
								value={this.state.searchForm.status}
								onChange={(v) => {
									this.setState({
										searchForm: { ...this.state.searchForm, status: v }
									})
								}}
							>
								{CHANGE_STATUS.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									)
								})}
							</Select>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={24} align="right">
						<Button type="primary" onClick={this.handleSearch}>
							搜索
						</Button>
						&nbsp;&nbsp;&nbsp;&nbsp;
						<Button type="default" onClick={this.handleReset}>
							重置
						</Button>
					</Col>
				</Row>
			</Form>
		);
	};

	render() {
		const { page, pageSize } = this.state;
		const { dataList, total, visible, detail } = this.props;
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: (total) => {
				return `共 ${total} 条数据 `;
			},
			onChange: (page) => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		const columns = [
			{
				title: '序号',
				dataIndex: 'xuhao',
				width: 80,
				align: 'center',
				render: (text, record, index) => {
					return index + 1;
				}
			},
			{
				title: '用户编号',
				dataIndex: 'cno',
				key: 'cno',
				align: 'center'
			},
			{
				title: '账单编号',
				dataIndex: 'billNo',
				key: 'billNo',
				align: 'center'
			},
			{
				title: '用户名称',
				dataIndex: 'name',
				key: 'name',
				align: 'center'
			},
			{
				title: '所属片区',
				dataIndex: 'areaName',
				key: 'areaName',
				align: 'center'
			},
			{
				title: '用户地址',
				dataIndex: 'address',
				key: 'address',
				align: 'center'
			},
			{
				title: '出账时间',
				dataIndex: 'outBillTime',
				key: 'outBillTime',
				align: 'center'
			},
			{
				title: '上期示数',
				dataIndex: 'lastWheelNumber',
				key: 'lastWheelNumber',
				align: 'center'
			},
			{
				title: '本期示数',
				dataIndex: 'currentWheelNumber',
				key: 'currentWheelNumber',
				align: 'center'
			},
			{
				title: '账单水量',
				dataIndex: 'billWaterAmount',
				key: 'billWaterAmount',
				align: 'center'
			},
			{
				title: '账单金额',
				dataIndex: 'billFee',
				key: 'billFee',
				align: 'center'
			},
			{
				title: '新本期示数',
				dataIndex: 'currentWheelNumberNew',
				key: 'currentWheelNumberNew',
				align: 'center'
			},
			{
				title: '调整水量',
				dataIndex: 'adjustmentWaterAmount',
				key: 'adjustmentWaterAmount',
				align: 'center'
			},
			{
				title: '保留水量',
				dataIndex: 'retainWaterAmount',
				key: 'retainWaterAmount',
				align: 'center'
			},
			{
				title: '调整金额',
				dataIndex: 'adjustmentFee',
				key: 'adjustmentFee',
				align: 'center'
			},
			{
				title: '保留金额',
				dataIndex: 'retainFee',
				key: 'retainFee',
				align: 'center'
			},
			{
				title: '调整时间',
				dataIndex: 'adjustmentTime',
				key: 'adjustmentTime',
				align: 'center'
			},

			{
				title: '创建时间',
				dataIndex: 'createTime',
				key: 'createTime',
				align: 'center'
			},
			{
				title: '创建人',
				dataIndex: 'createPersonName',
				key: 'createPersonName',
				align: 'center'
			},
			{
				title: '状态',
				dataIndex: 'status',
				key: 'status',
				align: 'center',
				width: 80,
				fixed: 'right',
				render: text => {
					return (
						<Tag color={statusColorMap.get(text)}>
							{text}
						</Tag>
					);
				}
			},
			{
				title: '申请状态',
				dataIndex: 'applyStatus',
				key: 'applyStatus',
				align: 'center',
				width: 80,
				fixed: 'right',
				render: text => {
					return (
						<Tag color={applyStatusColorMap.get(text)}>
							{text}
						</Tag>
					);
				}
			},
			{
				title: '操作',
				key: 'operation',
				width: 120,
				align: 'center',
				fixed: 'right',
				render: (text, record, index) => {
					if (record.applyStatus === '已完成') {
						return (
							<span>
								<ButtonGroup>
									<Button
										permission={React.$pmn('REVENUE:MECHANICAL_WATER_METER_MANAGEMENT:BILL_ADJUSTMENT_RECORD:VIEW')}
										title="查看"
										className="btn"
										type="primary"
										size="small"
										icon="eye"
										onClick={() => this.handleView(record)}
									/>
									<Button
										permission={React.$pmn('REVENUE:MECHANICAL_WATER_METER_MANAGEMENT:BILL_ADJUSTMENT_RECORD:INVALID')}
										title="作废"
										className="btn"
										type="danger"
										size="small"
										icon="delete"
										onClick={() => this.handleDel(record)}
									/>
								</ButtonGroup>
					&nbsp;
							</span>
						);
					} else {
						return (
							<span>
								<ButtonGroup>
									<Button
										title="查看"
										className="btn"
										type="primary"
										size="small"
										icon="eye"
										onClick={() => this.handleView(record)}
									/>
								</ButtonGroup>
							</span>
						);
					}
				}
			}
		]
		return (
			<div className="shadow-radius bill-adjustment">
				<Row>
					<Col>
						<h1>账单调整记录</h1>
					</Col>
				</Row>
				<Row>{this._renderSearchForm()}</Row>
				<Row>
					<Button className="searchBtn" type="primary" onClick={() => this.export()}><Icon type="download" />导出账单调整记录</Button>
				</Row>
				<Row className="main">
					<Table
						bordered
						columns={columns}
						rowKey={() => Math.random()}
						scroll={{ x: 3000 }}
						dataSource={dataList}
						pagination={paginationProps}
					/>
				</Row>
				{
					detail ? <Adjustment visible={visible} record={detail} onCancel={this.handleCancel} /> : null
				}

			</div>
		);
	}
}

const mapStateToProps = (state) => {
	const data = state.get('billAdjustmentRecord');
	return {
		fields: data.fields,
		dataList: data.dataList, // 列表数据
		total: data.total, // 总条数
		visible: data.visible, // 是否显示弹窗
		detail: data.detail, // 获取详情
		areas: data.areas // 片区树
	};
};
const mapDispatchToProps = (dispatch) => ({
	setState: (data) => dispatch(actionCreators.setState(data)), // 设置redux值
	list: (data) => dispatch(actionCreators.list(data)), // 获取列表
	del: (data, list) => dispatch(actionCreators.del(data, list)), // 删除记录
	getDetail: data => dispatch(actionCreators.detail(data)), // 获取详情
	areaList: () => dispatch(actionCreators.areaList()) // 获取片区
});
export default connect(mapStateToProps, mapDispatchToProps)(Form.create()(Index));
