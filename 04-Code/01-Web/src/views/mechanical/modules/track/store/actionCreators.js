import http from '$http';
import * as actionTypes from './constants';
import { message } from 'antd';
import api from './api';

// 数据回填
const payload = (type, data) => ({ type, data });

// 组件操作redux数据状态
const setState = data => ({ type: actionTypes.SET_STATE, data });

// 获取抄表员选择框
const getTranscriberSelect = () => {
	return async dispatch => {
		const res = await http.get(api.getTranscriberSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_TRANSCRIBER_SELECT, res.data))
		}
	}
};

// 获取位置列表
const listLocation = data => {
	return async dispatch => {
		const res = await http.post(api.listLocation, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.LIST_LOCATION, res.data))
		}
	}
};

export { setState, getTranscriberSelect, listLocation };
