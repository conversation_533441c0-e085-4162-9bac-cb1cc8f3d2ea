import * as actionTypes from "./constants";
import { dataType } from '$utils'

const defaultState = {
	fields: null,
	transcriberSelect: [], // 
	paths: [],
	markers: [],
};

// 重要提示： 请做好各代码段的注释！！！
export default (state = defaultState, action) => {
	switch (action.type) {

		// 回填组件传给redux的值
		case actionTypes.SET_STATE:
			let _data = action.data;
			if (dataType(_data) === 'Object') {
				state[_data.key] = _data.value;
			}
			if (dataType(_data) === 'Array') {
				_data.forEach(item => {
					state[item.key] = item.value;
				})
			}
			return { ...state };

		// 
		case actionTypes.GET_TRANSCRIBER_SELECT:
			state.transcriberSelect = action.data;
			return { ...state };

		// 
		case actionTypes.LIST_LOCATION:
			let locationData = action.data;
			let paths = []
			let markers = []
			locationData && locationData.length > 0 && locationData.forEach(item => {
				let o = {
					longitude: item.lon,
					latitude: item.lat
				}
				let m = { position: o }
				paths.push(o)
				markers.push(m)

			})
			state.paths = paths
			state.markers = markers
			return { ...state };

		default:
			return state;
	}
}
