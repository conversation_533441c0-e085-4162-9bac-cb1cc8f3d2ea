import React, { Component } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>, Markers } from 'react-amap';
import { actionCreators } from './store';
import { connect } from 'react-redux';
import { Button, Col, DatePicker, Form, Input, Row, Select, Table } from 'antd';

const { Option } = Select;

class Index extends Component {
	constructor(props) {
		super(props);
		this.state = {
			transcriberId: null,
			transcribeDay: '',
		};
		// 管网图事件
		this.lineEvents = {
			created: (ins) => {
				console.log(ins);
			},
			show: () => {
				console.log('line show');
			},
			hide: () => {
				console.log('line hide');
			},
			click: () => {
				console.log('line clicked');
			}
		};
	}

	componentDidMount() {
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields && isTags) {
			this.setState(
				{
					...fields.state
				},
				() => {
					this.props.form.setFieldsValue({
						...fields.form
					});
				}
			);
		} else {
			this.props.getTranscriberSelect()
		}
	}

	componentWillUnmount() {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			});
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			});
		}
	}

	// 获取定位
	listLocation = () => {
		const { transcriberId, transcribeDay } = this.state;
		if (transcriberId && transcribeDay) {
			let params = {
				transcriberId,
				transcribeDay
			}
			this.props.listLocation(params)
		}
	}

	render() {
		const { transcriberSelect, paths, markers } = this.props;
		console.log('paths',paths)
		console.log('markers',markers)
		return (
			<div className="shadow-radius" style={{ width: '100%', height: '100vh' }}>
				<Map amapkey={'18196a5d5d4865faff8deaf12c969ab2'} zoom={14} center={[119.61063, 39.9345]}>
					<Polyline
						path={paths}
						events={this.lineEvents}
						visible={true}
						style={{ strokeWeight: 2, strokeColor: '#EA5151' }}
					/>
					<Markers markers={markers} visible={true} />
					<div
						style={{
							position: 'absolute',
							minHeight: '18px',
							width: '28%',
							top: '4%',
							left: '70%',
							backgroundColor: '#fff',
							padding: '10px'
						}}
					>
						<Row>
							<Col span={12}>
								<Select
									showSearch
									placeholder="请选择抄表员"
									style={{ width: 180 }}
									value={this.state.transcriberId}
									onChange={v => {
										this.setState({
											transcriberId: v
										}, () => {
											this.listLocation()
										});
									}}>
									<Option value={null}>请选择抄表员</Option>
									{transcriberSelect.map((item, index) => {
										return (
											<Option key={index} value={item.value}>
												{item.label}
											</Option>
										);
									})}
								</Select>
							</Col>
							<Col span={12}>
								<DatePicker
									format={'YYYY-MM-DD'}
									value={this.state.transcribeDay}
									onChange={v => {
										this.setState({ transcribeDay: v }, () => {
											this.listLocation()
										});
									}} />
							</Col>
						</Row>
					</div>
				</Map>
			</div>
		);
	}
}

const mapStateToProps = (state) => {
	const data = state.get('track');
	return {
		fields: data.fields,
		transcriberSelect: data.transcriberSelect,
		paths: data.paths,
		markers: data.markers
	};
};
const mapDispatchToProps = (dispatch) => ({
	setState: (data) => dispatch(actionCreators.setState(data)), // 设置redux值
	getTranscriberSelect: () => dispatch(actionCreators.getTranscriberSelect()),
	listLocation: (data) => dispatch(actionCreators.listLocation(data)), // 获取
});
export default connect(mapStateToProps, mapDispatchToProps)(Form.create()(Index));
