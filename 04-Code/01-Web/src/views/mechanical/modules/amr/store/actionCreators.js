import http from '$http';
import { message } from 'antd';

import api from './api';
import * as actionTypes from './constants';

// 数据回填
const payload = (type, data) => ({ type, data });

// 组件操作redux数据状态
const setState = data => ({ type: actionTypes.SET_STATE, data });

// 获取列表
const list = data => {
	return async dispatch => {
		const res = await http.post(api.list, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.LIST_RECORD, res.data));
		}
	};
};

// 获取详情
const getDetail = (data, transcribeVisible) => {
	return async dispatch => {
		const res = await http.restGet(api.detail, data);
		if (res.code === 0) {
			transcribeVisible();
			dispatch(payload(actionTypes.DETAIL_RECORD, res.data));
		}
	};
};

// 获取抄表员选择框
const getTranscriberSelect = () => {
	return async dispatch => {
		const res = await http.get(api.getTranscriberSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_TRANSCRIBE_SELECT, res.data));
		}
	};
};

//获取片区
const getAreaSelect = () => {
	return async dispatch => {
		const res = await http.get(api.getAreaSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_AREA_SELECT, res.data));
		}
	};
};

// 作废
const invalidTask = (data, list) => {
	return async dispatch => {
		const res = await http.post(api.invalidTask + data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.INVALID_TASK, res.data));
		}
		list();
	};
};

// 下载抄表任务
const download = data => {
	return async dispatch => {
		const res = await http.post(api.download, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.DOWNLOAD, res.data));
		}
	};
};

const calculateFee = data => {
	return async dispatch => {
		const res = await http.post(api.calculateFee, data);
		if (res.code === 0) {
			if (data.isInit) {
				dispatch(payload(actionTypes.CALCULATE_FEE_1, res.data));
			} else {
				dispatch(payload(actionTypes.CALCULATE_FEE, res.data));
			}
		}
	};
};

//获取抄表原因
const getTranscribeResult = (data, detail) => {
	let newData = { currentWheelNumber: data.currentWheelNumber, id: detail.id };
	return async dispatch => {
		const res = await http.post(api.getTranscribeResult, newData);
		if (res.code === 0) {
			let newData = {};
			newData.result = res.data;
			newData.detail = detail;
			newData.data = data;
			dispatch(payload(actionTypes._GET_TRANSCRIBE_RESULT, newData));
		}
	};
};

//获取抄表原因
const getRanscribeMeter = (data, list, handleCancel, loading) => {
	return async dispatch => {
		const res = await http.post(api.getRanscribeMeter, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_RANSCRI_BEMETER, null));
			handleCancel();
			list();
		}
		setTimeout(() => {
			loading();
		}, 1500);
	};
};

//导入抄表文件结清账单
const importChargeBill = (data, list, close) => {
	return async dispatch => {
		const res = await http.post(api.importChargeBill, data);
		if (res.code === 0) {
			message.success('自动结清账单成功！');
			list();
			close();
		}
	};
};

export {
	calculateFee,
	download,
	getAreaSelect,
	getDetail,
	getRanscribeMeter,
	getTranscribeResult,
	getTranscriberSelect,
	importChargeBill,
	invalidTask,
	list,
	setState,
};
