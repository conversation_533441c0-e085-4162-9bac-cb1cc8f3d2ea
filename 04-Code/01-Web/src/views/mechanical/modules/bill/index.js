import './index.scss';

import React, { Component } from 'react';

import Operation from '$components/bills';
import http from '$http';
import { constants, excelExport } from '$utils';
import { Button, Col, DatePicker, Form, Icon, Input, InputNumber, message, Modal, Row, Select, Table, Tag, TreeSelect } from 'antd';
import moment from 'moment';
import { connect } from 'react-redux';

import { PRINT_TYPE } from '@/constants/bill';
import { billStatusColorMap } from '@/constants/colorStyle';
import { MECHANICAL_WATER_METER_KIND } from '@/constants/waterMeter';
import { WATER_USE_KIND_TYPE } from '@/constants/waterUseKind';

import { BAD_MARK_STATUS, BILL_STATUS, BILL_TYPE, OUT_FAIL_REASON, OUT_STATUS, SEND_DISC_MARK } from './constants';
import { actionCreators } from './store';

const FormItem = Form.Item;
const { Option } = Select;
const InputGroup = Input.Group;
const { RangePicker, MonthPicker } = DatePicker;
const { TreeNode } = TreeSelect;
const now = new Date();
const dateFormat = 'YYYY-MM-DD';

const defaultSearchForm = {
	billKind: 0,
	billNo: '', // 账单编号
	cno: '', // 客户编号
	hno: undefined,
	areaStart: null,
	areaEnd: null,
	customerName: '', // 用户姓名
	areaId: null, // 账单区域ID
	customerAreaId: null, // 用户区域ID
	waterUseKindIdList: [], // 用水性质ID
	chargeBillSourceList: [], // 用水性质ID
	waterMeterNo: '', // 水表编号
	transcribeTaskId: '', // 抄表任务id
	billType: null, // 账单类型：手工出账、抄表出账、自动出账、换表出账、销户出账、红冲出账
	billTypeList: null, // 账单类型：手工出账、抄表出账、自动出账、换表出账、销户出账、红冲出账
	settleAmountStart: '', // 开始结算水量
	settleAmountEnd: '', // 结束结算水量
	billPeriod: '', // 账期
	billStatus: null, // 账单状态：未结清、结清、部分结清
	outStatus: null, // 出账状态: 出账成功、出账失败
	outFailReason: null, // 出账失败原因
	sendDiscMark: null, // /送盘标志
	badMarkStatus: null, // 呆账类型:正常,呆账,坏账
	createTimeStart: moment(now.getFullYear() + '-01-01', 'YYYY-MM-DD').format(dateFormat), // 开始出账日期
	createTimeEnd: moment(now.getFullYear() + '-12-31', 'YYYY-MM-DD').format(dateFormat), // 结束出账日期
	averageAccountTimeStart: '', // 开始平账日期
	averageAccountTimeEnd: '', // 结束平账日期
	createUidList: null, // 客户id
	address: null, //地址
	payWay: null, //付款方式
	waterUseKindType: null, //用水分类
	taxInvoice: null,
	generalTaxpayer: null,
	customerLevelId: null, // 用户等级
	transcriber: null,
	fixedQuantity: null,
	kind: null // 水表种类
};

class Index extends Component {
	constructor(props) {
		super(props);
		this.state = {
			checkedKeys: [],
			expand: false, // 搜索项过多的时候，隐藏部分
			page: 1,
			pageSize: 10,
			searchForm: defaultSearchForm,
			selectedRowKeys: [], //列表选中
			show: false,
			type: null,
			title: '',
			record: null
		};
	}

	componentDidMount() {
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields && isTags) {
			this.setState(
				{
					...fields.state
				},
				() => {
					this.props.form.setFieldsValue({
						...fields.form
					});
				}
			);
		} else {
			this.props.waterUseKindList();
			this.props.areaList();
			this.props.customerList();
			this.props.customerLevelList();
			this.props.getTranscriberList(); // 获取抄表员列表
		}
		document.addEventListener('keypress', this.handleEnterKey);
	}

	componentWillUnmount() {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			});
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			});
		}
		document.removeEventListener('keypress', this.handleEnterKey);
	}

	// 回车事件
	handleEnterKey = e => {
		if (e.keyCode === 13) {
			this.getList();
		}
	};

	// 显示高级搜索
	showMoreSearch = () => {
		this.setState({
			expand: !this.state.expand
		});
	};

	// 获取列表
	getList = () => {
		const { page, pageSize, searchForm } = this.state;
		let params = { page, pageSize, ...searchForm };
		this.props.list(params);
		this.props.getTotal(params);
	};

	// 列表分页
	handlePageChange = page => {
		this.setState({ page }, () => this.getList());
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState({ page: 1, pageSize: size }, () => this.getList());
	};

	// 搜索功能
	handleSearch = () => {
		this.setState({ page: 1 }, () => {
			this.getList();
			this.setState({ selectedRowKeys: [] });
		});
	};

	// 重置搜索
	handleReset = () => {
		this.props.form.setFieldsValue({
			billPeriodPicker: undefined
		});
		this.resetQueryDate();
		this.props.form.setFieldsValue({
			averageDatePicker: [undefined, undefined]
		});
		this.setState({
			page: 1,
			searchForm: defaultSearchForm,
			selectedRowKeys: []
		});
	};

	// 设置搜索日期
	resetQueryDate = () => {
		this.props.form.setFieldsValue({ createDatePicker: [moment(now.getFullYear() + '-01-01', dateFormat), moment(now, dateFormat)] });
	};

	// 查看详情
	handleView = record => {
		http.restGet(`api/bill/getDetail`, record.id).then(res => {
			if (res.code === 0) {
				this.setState({ title: '查看详情', type: 0, record: res.data }, () => {
					this.props.setState({ key: 'visible', value: true });
				});
			}
		});
	};

	// 打印发票
	handlePrint = record => {
		this.setState(
			{
				title: '打印发票',
				type: 1,
				record
			},
			() => {
				this.props.setState({ key: 'visible', value: true });
			}
		);
	};

	// 调整
	handleAdjust = record => {
		http.restGet(`api/bill/getDetail`, record.id).then(res => {
			if (res.code === 0) {
				this.props.getAdjustmentReason();
				this.setState({ title: '调整', type: 2, record: res.data }, () => {
					this.props.setState({ key: 'visible', value: true });
				});
			}
		});
	};

	// 核减
	handleCut = record => {
		http.restGet(`api/bill/getDetail`, record.id).then(res => {
			if (res.code === 0) {
				this.props.getMinusReason();
				this.setState(
					{
						title: '核减',
						type: 3,
						record: res.data
					},
					() => {
						this.props.setState({ key: 'visible', value: true });
					}
				);
			}
		});
	};

	// 记呆坏账
	handleBook = record => {
		this.props.getDazeBillReason();
		this.props.getBadBillReason();
		this.setState(
			{
				title: '记呆坏账',
				type: 4,
				record: record
			},
			() => {
				this.props.setState({ key: 'visible', value: true });
			}
		);
	};

	// 作废
	handleDel = record => {
		Modal.confirm({
			title: '操作确认',
			content: '是否确认作废该记录？',
			okText: '确认',
			cancelText: '取消',
			onOk: () => {
				this.props.invalid(record.id, this.getList);
			}
		});
	};

	// 修改账单状态未结清
	handleUpdateStatusToUnSettled = record => {
		Modal.confirm({
			title: '操作确认',
			content: '是否修改账单状态为"未结清"？',
			okText: '确认',
			cancelText: '取消',
			onOk: () => {
				this.props.updateStatusToUnSettled(record.id, this.getList);
			}
		});
	};

	// 取消弹窗
	handleModalCancel = () => {
		this.props.setState([
			{ key: 'visible', value: false },
			{ key: 'detail', value: null }
		]);
	};

	// 弹窗提交
	handleModalSubmit = params => {
		const { type } = this.state;
		this.setState(
			{
				type: null
			},
			() => {
				// 1-打印发票
				if (type === 1) {
				}
				// 2-调整
				if (type === 2) {
					this.props.adjust(params);
				}
				// 3-核减
				if (type === 3) {
					this.props.cut(params);
				}
				//  4-记呆坏账
				if (type === 4) {
					this.props.badMarkUpdate(params, this.getList);
				}
			}
		);
	};

	// 用户片区选择树
	customerAreaOnChange = value => {
		this.setState({ searchForm: { ...this.state.searchForm, customerAreaId: value } });
	};

	// 账单片区选择树
	areaOnChange = value => {
		this.setState({ searchForm: { ...this.state.searchForm, areaId: value } });
	};

	// 渲染片区树
	_renderTreeLoop = areas => {
		return areas.map(item => {
			if (item.children) {
				return (
					<TreeNode key={item.id} title={item.name} value={item.id}>
						{this._renderTreeLoop(item.children)}
					</TreeNode>
				);
			} else {
				return <TreeNode key={item.id} title={item.name} value={item.id} />;
			}
		});
	};

	// 账期选择框
	getPeriod = (date, dateString) => {
		this.setState({
			searchForm: {
				...this.state.searchForm,
				billPeriod: dateString.replace('-', '')
			}
		});
	};

	//出账日期选择框
	getCreateDate = (date, dateString) => {
		this.setState({
			searchForm: {
				...this.state.searchForm,
				createTimeStart: dateString[0],
				createTimeEnd: dateString[1]
			}
		});
	};

	//平账日期选择框
	getAverageDate = (date, dateString) => {
		this.setState({
			searchForm: {
				...this.state.searchForm,
				averageAccountTimeStart: dateString[0],
				averageAccountTimeEnd: dateString[1]
			}
		});
	};

	// 渲染搜索
	_renderSearchForm = () => {
		const { searchForm, expand } = this.state;
		const { waterUseKinds, areas, customers, customerLevels, transcriberList } = this.props;
		const { getFieldDecorator } = this.props.form;
		const { kind } = searchForm;
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					{/* 账单编号 */}
					<Col span={8}>
						<FormItem label={'账单编号:'}>
							<Input
								placeholder="请输入账单编号"
								value={searchForm.billNo}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, billNo: v.target.value }
									});
								}}
							/>
						</FormItem>
					</Col>
					{/* 用户编号 */}
					<Col span={8}>
						<FormItem label={'用户编号:'}>
							<Input
								placeholder="请输入用户编号"
								value={searchForm.cno}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, cno: v.target.value }
									});
								}}
							/>
						</FormItem>
					</Col>
					{/* 用户户号 */}
					<Col span={8}>
						<FormItem label={'用户户号:'}>
							<Input
								placeholder="请输入用户户号"
								value={searchForm.hno}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, hno: v.target.value } });
								}}
							/>
						</FormItem>
					</Col>
				</Row>
				<Row>
					{/* 用户名称 */}
					<Col span={8}>
						<FormItem label={'用户名称:'}>
							<Input
								placeholder="请输入用户名称"
								value={searchForm.customerName}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, customerName: v.target.value }
									});
								}}
							/>
						</FormItem>
					</Col>
					{/* 用户片区 */}
					<Col span={8}>
						<FormItem label={'用户片区:'}>
							<TreeSelect showSearch filterTreeNode={(inputValue, treeNode) => treeNode.props.title.indexOf(inputValue) > -1} style={{ width: '100%' }} value={searchForm.customerAreaId} dropdownStyle={{ maxHeight: 400, overflow: 'auto' }} placeholder="请选择所属片区" allowClear treeDefaultExpandedKeys={[100]} onChange={this.customerAreaOnChange}>
								{this._renderTreeLoop(areas)}
							</TreeSelect>
						</FormItem>
					</Col>
					{/* 账单片区 */}
					<Col span={8}>
						<FormItem label={'账单片区:'}>
							<TreeSelect showSearch filterTreeNode={(inputValue, treeNode) => treeNode.props.title.indexOf(inputValue) > -1} style={{ width: '100%' }} value={searchForm.areaId} dropdownStyle={{ maxHeight: 400, overflow: 'auto' }} placeholder="请选择所属片区" allowClear treeDefaultExpandedKeys={[100]} onChange={this.areaOnChange}>
								{this._renderTreeLoop(areas)}
							</TreeSelect>
						</FormItem>
					</Col>
					{/* 水表编号 */}
					<Col span={8}>
						<FormItem label={'水表编号:'}>
							<Input
								placeholder="请输入水表编号"
								value={searchForm.waterMeterNo}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, waterMeterNo: v.target.value }
									});
								}}
							/>
						</FormItem>
					</Col>
					{/* 用水分类 */}
					<Col span={8}>
						<FormItem label={'用水分类:'}>
							<Select
								placeholder="请选择"
								value={searchForm.waterUseKindType}
								onChange={v => {
									this.setState(
										{
											searchForm: { ...searchForm, waterUseKindType: v }
										},
										() => {
											this.props.waterUseKindList(v);
										}
									);
								}}
							>
								{WATER_USE_KIND_TYPE.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					{/* 用水性质 */}
					<Col span={8}>
						<FormItem label={'用水性质:'}>
							<Select
								mode="multiple"
								placeholder="请选择"
								value={searchForm.waterUseKindIdList}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, waterUseKindIdList: v } });
								}}
							>
								{waterUseKinds.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					{/* 抄表任务编号 */}
					<Col span={8}>
						<FormItem label={'抄表任务编号:'}>
							<Input
								placeholder="请输入抄表任务编号"
								value={searchForm.transcribeTaskNo}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, transcribeTaskNo: v.target.value }
									});
								}}
							/>
						</FormItem>
					</Col>
					{/* 水表种类 */}
					<Col span={8}>
						<FormItem label={'水表种类:'}>
							<Select
								showSearch
								placeholder="请选择"
								value={kind}
								onChange={v => {
									this.setState({
										searchForm: {
											...searchForm,
											kind: v
										}
									});
								}}
							>
								<Option value={null}>全部</Option>
								{MECHANICAL_WATER_METER_KIND.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					{/* 结算水量 */}
					<Col span={8} style={{ display: expand ? 'block' : 'none' }}>
						<FormItem label={'结算水量:'}>
							<InputGroup compact>
								<Input
									style={{ width: '40%', textAlign: 'center' }}
									placeholder="起始水量"
									value={searchForm.settleAmountStart}
									onChange={v => {
										this.setState({
											searchForm: { ...searchForm, settleAmountStart: v.target.value }
										});
									}}
								/>
								<Input
									style={{
										width: '20%',
										borderLeft: 0,
										pointerEvents: 'none',
										backgroundColor: '#fff'
									}}
									placeholder="~"
									disabled
								/>
								<Input
									style={{ width: '40%', textAlign: 'center', borderLeft: 0 }}
									placeholder="结束水量"
									value={searchForm.settleAmountEnd}
									onChange={v => {
										this.setState({
											searchForm: { ...searchForm, settleAmountEnd: v.target.value }
										});
									}}
								/>
							</InputGroup>
						</FormItem>
					</Col>
					{/* 账期 */}
					<Col span={8} style={{ display: expand ? 'block' : 'none' }}>
						<FormItem label={'账期:'}>{getFieldDecorator('billPeriodPicker')(<MonthPicker onChange={this.getPeriod} placeholder="请选择账期" />)}</FormItem>
					</Col>
					{/* 账单状态 */}
					<Col span={8} style={{ display: expand ? 'block' : 'none' }}>
						<FormItem label={'账单状态:'}>
							<Select
								placeholder="请选择"
								value={searchForm.billStatus}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, billStatus: v }
									});
								}}
							>
								{BILL_STATUS.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					{/* 出账状态 */}
					<Col span={8} style={{ display: expand ? 'block' : 'none' }}>
						<FormItem label={'出账状态:'}>
							<Select
								placeholder="请选择"
								value={searchForm.outStatus}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, outStatus: v }
									});
								}}
							>
								{OUT_STATUS.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					{/* 出账失败原因 */}
					<Col span={8} style={{ display: expand ? 'block' : 'none' }}>
						<FormItem label={'出账失败原因:'}>
							<Select
								placeholder="请输入出账失败原因"
								value={searchForm.outFailReason}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, outFailReason: v }
									});
								}}
							>
								{OUT_FAIL_REASON.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					{/* 特殊标志 */}
					<Col span={8} style={{ display: expand ? 'block' : 'none' }}>
						<FormItem label={'特殊标志:'}>
							<Select
								placeholder="请选择"
								value={searchForm.sendDiscMark}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, sendDiscMark: v }
									});
								}}
							>
								{SEND_DISC_MARK.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					{/* 呆坏账标志 */}
					<Col span={8} style={{ display: expand ? 'block' : 'none' }}>
						<FormItem label={'呆坏账标志:'}>
							<Select
								placeholder="请选择"
								value={searchForm.badMarkStatus}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, badMarkStatus: v }
									});
								}}
							>
								{BAD_MARK_STATUS.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					{/* 出账日期 */}
					<Col span={8} style={{ display: expand ? 'block' : 'none' }}>
						<FormItem label={'出账日期:'}>
							{getFieldDecorator('createDatePicker', {
								initialValue: [moment(now.getFullYear() + '-01-01', dateFormat), moment(now.getFullYear() + '-12-31', dateFormat)],
								rules: [{ type: 'array' }]
							})(<RangePicker onChange={this.getCreateDate} placeholder={['开始日期', '结束日期']} />)}
						</FormItem>
					</Col>
					{/* 平账时间 */}
					<Col span={8} style={{ display: expand ? 'block' : 'none' }}>
						<FormItem label={'平账日期:'}>{getFieldDecorator('averageDatePicker')(<RangePicker onChange={this.getAverageDate} placeholder={['开始日期', '结束日期']} />)}</FormItem>
					</Col>
					{/* 账单创建人 */}
					<Col span={8} style={{ display: expand ? 'block' : 'none' }}>
						<FormItem label={'账单创建人:'}>
							<Select
								mode="multiple"
								placeholder="请选择"
								value={searchForm.createUidList}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, createUidList: v }
									});
								}}
							>
								{customers.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					{/* 账单类型 */}
					<Col span={8} style={{ display: expand ? 'block' : 'none' }}>
						<FormItem label={'账单类型:'}>
							<Select
								mode={'multiple'}
								placeholder="请选择"
								value={searchForm.billTypeList}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, billTypeList: v } });
								}}
							>
								{BILL_TYPE.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					{/* 申请状态 */}
					{/* <Col span={8} style={{ display: expand ? 'block' : 'none' }}>
						<FormItem label={'申请状态:'}>
							<Select
								placeholder="请选择" value={searchForm.applyStatus}
								onChange={(v) => {
									this.setState({ searchForm: { ...searchForm, applyStatus: v } });
								}}>
								{
									APPLY_STATUS_BY_OTHER.map((item, index) => {
										return <Option key={index} value={item.value}>{item.label}</Option>;
									})
								}
							</Select>
						</FormItem>
					</Col> */}
					{/* 用户地址 */}
					<Col span={8} style={{ display: expand ? 'block' : 'none' }}>
						<FormItem label={'用户地址:'}>
							<Input
								placeholder="请输入用户地址"
								value={searchForm.address}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, address: v.target.value }
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8} style={{ display: expand ? 'block' : 'none' }}>
						<FormItem label={'付款方式:'}>
							<Select
								placeholder="请选择"
								value={searchForm.payWay}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, payWay: v }
									});
								}}
							>
								{constants.payWay.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8} style={{ display: expand ? 'block' : 'none' }}>
						<FormItem label={'片区范围:'}>
							<InputGroup compact>
								<Input
									style={{ width: '40%', textAlign: 'center' }}
									placeholder="起始片区号"
									value={searchForm.areaStart}
									onChange={v => {
										this.setState({ searchForm: { ...searchForm, areaStart: v.target.value } });
									}}
								/>
								<Input style={{ width: '10%', borderLeft: 0, pointerEvents: 'none', backgroundColor: '#fff' }} placeholder="~" disabled />
								<Input
									style={{ width: '40%', textAlign: 'center', borderLeft: 0 }}
									placeholder="结束片区号"
									value={searchForm.areaEnd}
									onChange={v => {
										this.setState({ searchForm: { ...searchForm, areaEnd: v.target.value } });
									}}
								/>
							</InputGroup>
						</FormItem>
					</Col>
					<Col span={8} style={{ display: expand ? 'block' : 'none' }}>
						<FormItem label={'是否开税票:'}>
							<Select
								placeholder="请选择"
								value={searchForm.taxInvoice}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, taxInvoice: v } });
								}}
							>
								{PRINT_TYPE.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8} style={{ display: expand ? 'block' : 'none' }}>
						<FormItem label={'一般纳税人:'}>
							<Select
								allowClear
								placeholder="请选择"
								value={searchForm.generalTaxpayer}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, generalTaxpayer: v } });
								}}
							>
								<Option value={0}>否</Option>
								<Option value={1}>是</Option>
							</Select>
						</FormItem>
					</Col>
					<Col span={8} style={{ display: expand ? 'block' : 'none' }}>
						<FormItem label="用户等级：">
							<Select
								allowClear
								value={searchForm.customerLevelId}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, customerLevelId: v } });
								}}
							>
								<Option key={0} value={null}>
									{'请选择'}
								</Option>
								{customerLevels.map((item, index) => {
									return (
										<Option key={index + 1} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>

					<Col span={8} style={{ display: expand ? 'block' : 'none' }}>
						<FormItem label={'使用预存水量:'}>
							<InputGroup>
								<InputNumber
									className="noBorderRight"
									style={{ width: '40%' }}
									value={this.state.searchForm.preStoreWaterAmountDidStart}
									onChange={value => {
										this.setState({ searchForm: { ...searchForm, preStoreWaterAmountDidStart: value } });
									}}
								/>
								<InputNumber className="midIpt noBorderLeft noBorderRight" style={{ width: '20%' }} placeholder="~" disabled />
								<InputNumber
									className="noBorderLeft"
									style={{ width: '40%' }}
									value={this.state.searchForm.preStoreWaterAmountDidEnd}
									onChange={value => {
										this.setState({
											searchForm: {
												...searchForm,
												preStoreWaterAmountDidEnd: value
											}
										});
									}}
								/>
							</InputGroup>
						</FormItem>
					</Col>
					<Col span={8} style={{ display: expand ? 'block' : 'none' }}>
						<FormItem label={'抄表员:'}>
							<TreeSelect
								showSearch
								placeholder="请选择"
								allowClear
								dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
								value={this.state.searchForm.transcriber}
								filterTreeNode={(inputValue, treeNode) => treeNode.props.title.indexOf(inputValue) > -1}
								onChange={v => {
									this.setState({
										searchForm: {
											...searchForm,
											transcriber: v
										}
									});
								}}
							>
								{transcriberList.map((item, index) => {
									return <TreeNode key={index} title={item.label} value={item.value} />;
								})}
							</TreeSelect>
						</FormItem>
					</Col>
					<Col span={8} style={{ display: expand ? 'block' : 'none' }}>
						<FormItem label="是否定量">
							<Select
								placeholder="请选择"
								value={searchForm.fixedQuantity}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, fixedQuantity: v } });
								}} showArrow

							>
								{constants.fixedQuantity.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					{/* 定量值 */}
					<Col span={8} style={{ display: expand ? 'block' : 'none' }}>
						<FormItem label={'定量值:'}>
							<Select
								mode="multiple"
								placeholder="请选择"
								value={searchForm.fixedQuantityAmountList}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, fixedQuantityAmountList: v } });
								}}
							>
								{constants.fixedQuantitySelect.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
				</Row>
				<Col span={24} align="right">
					<Button type="primary" className="searchBtn" onClick={this.handleSearch}>
						搜索
					</Button>
					<Button type="default" className="searchBtn" onClick={this.handleReset}>
						重置
					</Button>
					<Button type="link" className="searchBtn" onClick={this.showMoreSearch}>
						{expand ? '关闭高级搜索' : '高级搜索'}
					</Button>
				</Col>
			</Form>
		);
	};

	//导出账单
	exportChargeBill() {
		const { searchForm } = this.state;
		excelExport('/api/bill/exportChargeBill', '账单明细', searchForm);
		// let url = `${process.env.API_ROOT}/api/bill/exportChargeBill`;
		// http.export(url, searchForm, (res) => {
		// 	const blob = new Blob([res]);
		// 	const fileName = '账单明细_' + new Date().getTime() + '.xlsx';
		// 	if (window.navigator && window.navigator.msSaveOrOpenBlob){
		// 			window.navigator.msSaveOrOpenBlob(blob, fileName)
		// 			return
		// 	}
		// 	const downloadElement = document.createElement('a');
		// 	const href = window.URL.createObjectURL(blob);
		// 	downloadElement.href = href;
		// 	downloadElement.download = fileName;
		// 	document.body.appendChild(downloadElement);
		// 	downloadElement.click();
		// 	document.body.removeChild(downloadElement);
		// 	window.URL.revokeObjectURL(href);
		// });
	}

	//选中事件
	onSelectChange = selectedRowKeys => {
		this.setState({ selectedRowKeys });
	};

	// 批量打印税票
	batchPrintTax = () => {
		const { selectedRowKeys } = this.state;
		if (selectedRowKeys.length > 0) {
			this.props.batchPrintTax(selectedRowKeys, this.getList);
			this.setState({ selectedRowKeys: [] });
		} else {
			this.getList();
			setTimeout(() => this.openModal(), 2000);
		}
	};

	// 批量作废账单
	batchInvalidBill = () => {
		const { selectedRowKeys } = this.state;
		if (selectedRowKeys.length > 0) {
			this.props.batchInvalidBill(selectedRowKeys, this.getList);
			this.setState({ selectedRowKeys: [] });
		}
	};

	openModal() {
		if (this.props.total) {
			Modal.confirm({
				title: '提示',
				content: `未勾选指定用户时,根据查询条件来批量打印.一共${this.props.total}户，确认要打印吗？`,
				okText: '确认',
				cancelText: '取消',
				onOk: () => {
					const { searchForm } = this.state;
					this.props.batchPrintTaxByCondition(searchForm);
				}
			});
		} else {
			message.error('未查询到用户');
		}
	}

	// 批量打印税票
	batchPrintTax = () => {
		const { selectedRowKeys } = this.state;
		if (selectedRowKeys.length > 0) {
			this.props.batchPrintTax(selectedRowKeys, this.getList);
			this.setState({ selectedRowKeys: [] });
		} else {
			this.getList();
			setTimeout(() => this.openModal(), 2000);
		}
	};

	// 批量打印电子发票
	batchPrintETax = () => {
		const { selectedRowKeys } = this.state;
		if (selectedRowKeys.length > 0) {
			this.props.batchPrintETax(selectedRowKeys, this.getList);
			this.setState({ selectedRowKeys: [] });
		} else {
			this.getList();
			setTimeout(() => this.openETaxModal(), 2000);
		}
	};

	openETaxModal() {
		if (this.props.total) {
			Modal.confirm({
				title: '提示',
				content: `未勾选指定用户时,根据查询条件来批量打印.一共${this.props.total}户，确认要打印吗？`,
				okText: '确认',
				cancelText: '取消',
				onOk: () => {
					const { searchForm } = this.state;
					this.props.batchPrintETaxByCondition(searchForm);
				}
			});
		} else {
			message.error('未查询到用户');
		}
	}

	render() {
		const { page, pageSize, title, type, record, selectedRowKeys } = this.state;
		const { dataList, total, visible, orderTotal, adjustmentReason, minusReason, dazeBillReason, badBillReason } = this.props;
		const columns = [
			{
				title: '账单编号',
				dataIndex: 'billNo',
				key: 'billNo',
				align: 'center'
			},
			{
				title: '用户编号',
				dataIndex: 'cno',
				key: 'cno',
				align: 'center'
			},
			{
				title: '用户户号',
				dataIndex: 'hno',
				key: 'hno',
				align: 'center'
			},
			{
				title: '用户名称',
				dataIndex: 'customerName',
				key: 'customerName',
				align: 'center'
			},
			{
				title: '用户地址',
				dataIndex: 'customerAddress',
				key: 'customerAddress',
				align: 'center'
			},
			{
				title: '水表编号',
				dataIndex: 'waterMeterNo',
				key: 'waterMeterNo',
				align: 'center'
			},
			{
				title: '账单类型',
				dataIndex: 'billType',
				key: 'billType',
				align: 'center',
				render: text => {
					return <span style={text === '红冲出账' ? { color: 'red' } : void 0}>{text}</span>;
				}
			},
			{
				title: '抄表任务编号',
				dataIndex: 'transcribeTaskNo',
				key: 'transcribeTaskNo',
				align: 'center'
			},
			{
				title: '结算水量',
				dataIndex: 'settleAmount',
				key: 'settleAmount',
				align: 'center'
			},
			{
				title: '应收金额',
				dataIndex: 'billFee',
				key: 'billFee',
				align: 'center'
			},
			{
				title: '已结金额',
				dataIndex: 'settleFee',
				key: 'settleFee',
				align: 'center'
			},
			{
				title: '付款方式',
				dataIndex: 'payWay',
				key: 'payWay',
				align: 'center'
			},
			// {
			// 	title: '账期',
			// 	dataIndex: 'billPeriod',
			// 	key: 'billPeriod',
			// 	align: 'center'
			// },
			{
				title: '出账失败原因',
				dataIndex: 'outFailReason',
				key: 'outFailReason',
				align: 'center'
			},
			{
				title: '是否开税票',
				dataIndex: 'taxInvoice',
				key: 'taxInvoice',
				align: 'center'
			},
			{
				title: '特殊标志',
				dataIndex: 'sendDiscMark',
				key: 'sendDiscMark',
				align: 'center'
			},
			{
				title: '呆坏账标志',
				dataIndex: 'badMarkStatus',
				key: 'badMarkStatus',
				align: 'center'
			},
			{
				title: '出账日期',
				dataIndex: 'createTime',
				key: 'createTime',
				align: 'center'
			},
			{
				title: '平账日期',
				dataIndex: 'averageAccountTime',
				key: 'averageAccountTime',
				align: 'center'
			},
			{
				title: '部门',
				dataIndex: 'departmentName',
				key: 'departmentName',
				align: 'center'
			},
			{
				title: '账单创建人',
				dataIndex: 'createPersonName',
				key: 'createPersonName',
				align: 'center'
			},
			{
				title: '是否定量',
				dataIndex: 'customerFixedQuantity',
				key: 'customerFixedQuantity',
				align: 'center',
				render: text => {
					return <span>{text ? '是' : '否'}</span>;
				}
			},
			{
				title: '定量值',
				dataIndex: 'customerFixedQuantityAmount',
				key: 'customerFixedQuantityAmount',
				align: 'center'
			},
			{
				title: '账单状态',
				dataIndex: 'billStatus',
				key: 'billStatus',
				align: 'center',
				width: 80,
				fixed: 'right',
				render: text => {
					return <Tag color={billStatusColorMap.get(text)}>{text}</Tag>;
				}
			},
			{
				title: '出账状态',
				dataIndex: 'outStatus',
				key: 'outStatus',
				align: 'center',
				width: 80,
				fixed: 'right',
				render: text => {
					return <span style={text === '出账失败' ? { color: 'red' } : void 0}>{text}</span>;
				}
			},
			{
				title: '操作',
				key: 'operation',
				width: 200,
				align: 'center',
				fixed: 'right',
				render: (text, record, index) => {
					return record.billStatus === '红冲' || record.billStatus === '作废' || record.billType === '红冲出账' || record.outStatus === '出账失败' ? (
						<Button permission={React.$pmn('REVENUE:MECHANICAL_WATER_METER_MANAGEMENT:BILL:VIEW')} title="查看" className="btn" type="primary" size="small" icon="eye" onClick={() => this.handleView(record)} />
					) : (
						<span>
							<Button permission={React.$pmn('REVENUE:MECHANICAL_WATER_METER_MANAGEMENT:BILL:VIEW')} title="查看" className="btn" type="primary" size="small" icon="eye" onClick={() => this.handleView(record)} />
							{/*<Button
									permission={React.$pmn('REVENUE:MECHANICAL_WATER_METER_MANAGEMENT:BILL:PRINT')}
									title="打印发票"
									className="btn"
									type="primary"
									size="small"
									icon="printer"
									onClick={() => this.handlePrint(record)} />*/}
							<Button permission={React.$pmn('REVENUE:MECHANICAL_WATER_METER_MANAGEMENT:BILL:ADJUST')} title="调整" className="btn" type="primary" size="small" icon="control" onClick={() => this.handleAdjust(record)} disabled={record.sendDiscMark != '正常' ? true : false} />
							<Button permission={React.$pmn('REVENUE:MECHANICAL_WATER_METER_MANAGEMENT:BILL:MINUS')} title="核减" className="btn" type="primary" size="small" icon="switcher" onClick={() => this.handleCut(record)} disabled={record.sendDiscMark != '正常' ? true : false} />
							<Button permission={React.$pmn('REVENUE:MECHANICAL_WATER_METER_MANAGEMENT:BILL:REMARK')} title="记呆坏账" className="btn" type="primary" size="small" icon="schedule" onClick={() => this.handleBook(record)} />
							<Button permission={React.$pmn('REVENUE:MECHANICAL_WATER_METER_MANAGEMENT:BILL:INVALID')} title="作废" className="btn" type="danger" size="small" icon="delete" onClick={() => this.handleDel(record)} disabled={record.sendDiscMark != '正常' ? true : false} />
							&nbsp;
							<Button permission={React.$pmn('REVENUE:MECHANICAL_WATER_METER_MANAGEMENT:BILL:UP_STATUS')} title="修改账单状态" className="btn" type="danger" size="small" icon="control" onClick={() => this.handleUpdateStatusToUnSettled(record)} disabled={record.chargeBillSourceList.length !== 0 || record.billStatus !== '结清'} />
							&nbsp;
						</span>
					);
				}
			}
		];
		const rowSelection = {
			fixed: true,
			selectedRowKeys,
			onChange: this.onSelectChange
		};
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: total => {
				return `共 ${total} 条数据 `;
			},
			onChange: page => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};

		return (
			<div className="shadow-radius bill">
				<h1>机械表账单</h1>

				{this._renderSearchForm()}

				<Row span={24}>
					{/*<Button className="searchBtn" type="primary" onClick={() => this.batchPrintTax()}><Icon*/}
					{/*	type="printer" />批量打印税票</Button>*/}
					{/*<Button className="searchBtn" type="primary" onClick={this.batchPrintETax}>*/}
					{/*	<Icon type="trademark" />批量推送电子发票</Button>*/}

					<Button className="searchBtn" type="primary" onClick={this.batchInvalidBill}>
						<Icon type="trademark" />
						批量作废账单
					</Button>
					<Button className="searchBtn" type="primary" onClick={() => this.exportChargeBill()}>
						<Icon type="download" />
						导出账单
					</Button>
				</Row>

				<Row className="main">
					<Col style={{ marginBottom: 5 }}>
						总金额: {orderTotal.orderAmount}元 &emsp;&emsp;&emsp; 总污水费： {orderTotal.sewageFee}元 &emsp;&emsp;&emsp; 总清水费： {orderTotal.cleanWaterFee}元 &emsp;&emsp;&emsp; 总用水量： {orderTotal.waterAmount}吨 &emsp;&emsp;&emsp; 已结金额: {orderTotal.settleFee}元 &emsp;&emsp;&emsp; 欠费金额: {orderTotal.oweFee}元 &emsp;&emsp;&emsp;
					</Col>
					<Col style={{ marginBottom: 5 }}>
						实际总金额: {orderTotal.actuallyOrderAmount}元 &emsp;&emsp;&emsp; 实际已结金额: {orderTotal.actuallySettleFee}元 &emsp;&emsp;&emsp; 实际欠费金额: {orderTotal.actuallyOweFee}元 &emsp;&emsp;&emsp;
					</Col>
					<Table bordered columns={columns} rowSelection={rowSelection} scroll={{ x: 3000 }} rowKey={record => record.id} dataSource={dataList} pagination={paginationProps} />
				</Row>

				<Operation title={title} visible={visible} type={type} record={record} adjustmentReason={adjustmentReason} minusReason={minusReason} dazeBillReason={dazeBillReason} badBillReason={badBillReason} onCancel={this.handleModalCancel} onSubmit={this.handleModalSubmit} />
			</div>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('mechanicalBill');
	return {
		fields: data.fields,
		dataList: data.dataList, // 列表数据
		total: data.total, // 总条数
		visible: data.visible, // 是否显示弹窗
		detail: data.detail, // 获取详情
		waterUseKinds: data.waterUseKinds, // 用水分类列表
		areas: data.areas, // 片区树
		customers: data.customers, // 客户列表
		orderTotal: data.orderTotal, // 统计
		adjustmentReason: data.adjustmentReason, //获取调整原因
		minusReason: data.minusReason, //获取核减原因
		dazeBillReason: data.dazeBillReason, //呆账原因
		badBillReason: data.badBillReason, //坏账原因,
		customerLevels: data.customerLevels, // 用户等级
		transcriberList: data.transcriberList // 抄表员
	};
};
const mapDispatchToProps = dispatch => ({
	setState: data => dispatch(actionCreators.setState(data)), // 设置redux值
	list: data => dispatch(actionCreators.list(data)), // 获取列表
	getDetail: data => dispatch(actionCreators.detail(data)), // 获取详情
	add: data => dispatch(actionCreators.add(data)), // 新增记录
	del: (data, list) => dispatch(actionCreators.del(data, list)), // 删除记录
	modify: data => dispatch(actionCreators.modify(data)), // 修改记录
	waterUseKindList: type => dispatch(actionCreators.waterUseKindList(type)), // 获取用水分类
	areaList: () => dispatch(actionCreators.areaList()), // 获取片区
	customerList: () => dispatch(actionCreators.customerList()), // 获取客户列表
	badMarkUpdate: (data, list) => dispatch(actionCreators.badMarkUpdate(data, list)), // 获取客户列表
	adjust: data => dispatch(actionCreators.adjust(data)), // 调整
	cut: data => dispatch(actionCreators.cut(data)), // 核减
	invalid: (data, list) => dispatch(actionCreators.invalid(data, list)), // 核减
	updateStatusToUnSettled: (data, list) => dispatch(actionCreators.updateStatusToUnSettled(data, list)), // 修改为未结清
	getTotal: data => dispatch(actionCreators.getTotal(data)), // 统计
	getAdjustmentReason: () => dispatch(actionCreators.getAdjustmentReason()), //获取调整原因
	getMinusReason: () => dispatch(actionCreators.getMinusReason()), //获取核减原因
	getDazeBillReason: () => dispatch(actionCreators.getDazeBillReason()), //获呆账减原因
	getBadBillReason: () => dispatch(actionCreators.getBadBillReason()), //获取坏账原因
	batchPrintTax: (data, list) => dispatch(actionCreators.batchPrintTax(data, list)), // 批量打印税票
	batchInvalidBill: (data, list) => dispatch(actionCreators.batchInvalidBill(data, list)), // 批量打印税票
	batchPrintTaxByCondition: data => dispatch(actionCreators.batchPrintTaxByCondition(data)),
	batchPrintETax: (data, list) => dispatch(actionCreators.batchPrintETax(data, list)), // 批量打印电子发票
	batchPrintETaxByCondition: data => dispatch(actionCreators.batchPrintETaxByCondition(data)), // 根据查询条件批量打印电子发票
	customerLevelList: () => dispatch(actionCreators.customerLevelList()), // 用户等级
	getTranscriberList: () => dispatch(actionCreators.getTranscriberList()) // 抄表员
});
export default connect(mapStateToProps, mapDispatchToProps)(Form.create()(Index));
