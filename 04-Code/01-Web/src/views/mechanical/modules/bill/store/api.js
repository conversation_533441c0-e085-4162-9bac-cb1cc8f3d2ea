export default {
	list: `/api/bill/listPageMechanical`,
	waterUseKindList: `/api/wuk/waterusekind/getSelect`,
	area: `/api/sys/area/getTree`,
	customer: `/api/bill/getCreateUidSelect`,
	badMarkUpdate: `/api/bill/badMarkUpdate`,
	adjust: `/api/bill/adjustmentChargeBill`,
	cut: `/api/bill/processChargeBill `,
	invalid: `api/bill/billInvalid`,
	getTotal: `api/bill/billTotal`,
	getAdjustmentReason: `/api/sys/dictionary/getSelectByCode/ADJUSTMENT_REASON`, //获取调整原因
	getMinusReason: `/api/sys/dictionary/getSelectByCode/ADJUSTMENT_REASON`, //获取核减原因
	getDazeBillReason: `/api/sys/dictionary/getSelectByCode/DAZE_BILL_REASON`, //获呆账减原因
	getBadBillReason: `/api/sys/dictionary/getSelectByCode/BAD_BILL_REASON`, //获取坏账原因
	batchPrintTax: `/api/bill/batchPrintTax`,	               // 批量打印税票
	batchPrintTaxByCondition:`api/bill/batchPrintTaxByCondition`,                  //按条件查询批量打印税票
	batchPrintETax: `/api/bill/batchPrintInvoice`,	               // 批量打印电子发票
	batchPrintETaxByCondition:`/api/bill/batchPrintInvoiceByCondition`,                    //按条件查询批量打印电子发票
	getDetailByCustomer: `api/cm/vat/getByCustomer`,
	submitInvoice: `api/electronicInvoice/open`
};
