import './index.scss';

import React, { Component } from 'react';

import http from '$http';
import {
	constants,
	debounce,
} from '$utils';
import {
	Button,
	Col,
	Form,
	Input,
	InputNumber,
	message,
	Modal,
	Row,
	Select,
	Table,
	Tag,
	TreeSelect,
} from 'antd';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';

import { userStatusColorMap } from '@/constants/colorStyle';
import {
	FIXED_QUANTITY,
	PAY_WAY,
} from '@/constants/customer';
import { WATER_USE_KIND_TYPE } from '@/constants/waterUseKind';

import { actionCreators } from './store';

const FormItem = Form.Item;
const { Option } = Select;
const ButtonGroup = Button.Group;
const { confirm } = Modal;
const InputGroup = Input.Group;
const { TreeNode } = TreeSelect;

const defaultSearchForm = {
	hno: '', // 用户户号
	cno: '', // 用户编号
	waterMeterNo: '', // 水表编号
	meterType: null, // 水表类型：正常/总表/分表/虚表
	waterMeterType: 1, // 水表分类
	waterMeterManufacturer: null, // 水表厂家
	installLocationId: null, // 安装位置 关联字典
	areaIdList: [], // 片区ID
	waterUseKindIdList: [], // 用水性质ID
	payWay: null, // 付款方式：小额借记/代扣/其他
	name: '', // 用户名称
	address: '', // 详细地址
	status: 0, // 状态
	customerType: null, // 客户类型：正常/低保/特困
	oweAmountStart: null, // 开始欠费金额
	oweAmountEnd: null, // 结束欠费金额
	accountBalanceStart: '', // 开始余额
	accountBalanceEnd: '', // 结束余额
	fixedQuantity: null, //是否定量
	waterUseKindType: null //用水性质
};

class Index extends Component {
	constructor(props) {
		super(props);
		this.state = {
			expand: false, // 搜索项过多的时候，隐藏部分
			title: '',
			page: 1,
			pageSize: 10,
			searchForm: defaultSearchForm,
			waterQuality: null, // 用水性质
			userId: '', // 用户id
			currentWheelNumber: '',
			selectedRowKeys: [], //列表选中
			selectedRows: [],
			contractType: null
		};
	}

	componentDidMount () {
		const { fields } = this.props;
		// const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		let loadParam = (this.props.location.state && this.props.location.state.fromUrl) == '/users/detail'
		if (fields && loadParam) {
			this.setState(
				{
					...fields.state
				},
				() => {
					this.props.form.setFieldsValue({
						...fields.form
					});
					this.getList();
				}
			);
		} else {
			this.getInstallPosition();
			this.props.getAreaSelect();
			this.props.getTranscriberSelect();
			// this.props.waterUseKindList();
		}
		document.addEventListener('keypress', this.handleEnterKey);
	}

	componentWillUnmount () {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			});
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			});
		}
		document.removeEventListener('keypress', this.handleEnterKey);
	}

	// 回车事件
	handleEnterKey = e => {
		if (e.keyCode === 13) {
			this.getList();
		}
	};

	// 获取列表
	getList = () => {
		const { page, pageSize, searchForm } = this.state;
		let params = {
			page,
			pageSize,
			...searchForm
		};
		this.props.list(params);
	};

	// 搜索
	handleSearch = () => {
		this.setState(
			{
				page: 1
			},
			() => {
				this.getList();
				this.setState({ selectedRowKeys: [] });
			}
		);
	};

	// 重置搜索
	handleReset = () => {
		this.setState(
			{
				page: 1,
				searchForm: Object.assign({}, defaultSearchForm)
			},
			() => {
				// this.getList();
				this.setState({ selectedRowKeys: [] });
			}
		);
	};

	// 安装位置分类改变
	handleChangeInstallLocationType = v => {
		this.props.reallyPosition(v);
		this.props.form.resetFields(['installLocationId']);
	};

	// 列表分页
	handlePageChange = page => {
		this.setState(
			{
				page
			},
			() => {
				this.getList();
			}
		);
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState(
			{
				page: 1,
				pageSize: size
			},
			() => {
				this.getList();
			}
		);
	};

	// 查看详情
	handleView = record => {
		React.$saveWhere('mechanical', {
			prev: this.props.location.pathname,
			path: '/users/detail',
			id: record.id
		});
		sessionStorage.setItem('_id', record.id);
		this.props.history.push('/users/detail');
	};

	// 手工出账
	handleManualBilling = record => {
		this.getCalculateFee(1, record.id, true);
		this.props.getDetail(record.id);
	};
	//显示修改缴费类型
	handleContractType = record => {
		this.props.setState([
			{ key: 'detail', value: record },
			{ key: 'showContractTypeModal', value: true }
		]);

		console.log(this.props);
	};
	//修改缴费类型

	handleContractTypeSubmit = () => {
		let param = {
			customerId: this.props.detail.id,
			contractType: this.state.contractType
		};
		this.props.updateContractType(param, () => {
			this.props.setState([{ key: 'showContractTypeModal', value: false }]);
			this.getList();
		});
	};

	// 派发抄表任务
	dispatchTask = record => {
		confirm({
			title: '操作确认',
			content: '是否确认手动派发抄表任务？',
			okText: '确认',
			cancelText: '取消',
			onOk: () => {
				this.props.transcribe(record.id);
			}
		});
	};

	// 获取水表安装位置
	getInstallPosition = () => {
		this.props.getInstallPosition();
	};

	// 显示高级搜索
	showMoreSearch = () => {
		this.setState({
			expand: !this.state.expand
		});
	};

	// 取消弹窗
	handleCancel = () => {
		this.setState(
			{
				currentWheelNumber: ''
			},
			() => {
				this.props.setState([
					{ key: 'visible', value: false },
					{ key: 'detail', value: null },
					{ key: 'showContractTypeModal', value: false }
				]);
			}
		);
	};
	calculateFee = v => {
		// const { detail } = this.props;
		// let waterAmount = v.target.value;
		// if (waterAmount && Number(waterAmount) > 0) {
		// 	if (/^-?\d+\.\d+$/.test(waterAmount)) {
		// 		message.error('购买水量不能为小数');
		// 	} else {
		// 		this.getCalculateFee(waterAmount, detail.id);
		// 	}
		// }
	};
	getCalculateFee = debounce((waterAmount, customerId, isInit) => {
		if (waterAmount && waterAmount > 0) {
			let param = {
				customerId: customerId,
				waterAmount: waterAmount,
				isInit: isInit
			};
			this.props.calculateFee(param);
		}
	}, 500);
	// 手动出账
	handleSubmit = () => {
		const { currentWheelNumber } = this.state;
		const { detail } = this.props;
		if (!currentWheelNumber) {
			message.error('请先输入本期示数！');
			return false;
		}
		if (parseInt(currentWheelNumber) < detail.wheelPresentNumber) {
			message.error('本期示数必须不小于上期示数！');
			return false;
		}

		this.props.form.validateFields((err, values) => {
			let params = {
				currentWheelNumber: parseInt(currentWheelNumber),
				// feeInterval: values.feeInterval,
				customerId: detail.id
			};
			this.props.handWorkOutBill(params, this.getList);
			this.handleCancel();
		});
	};

	onChange = (value, selectedOptions) => {
		console.log(value, selectedOptions);
	};

	loadData = selectedOptions => {
		// const targetOption = selectedOptions[selectedOptions.length - 1];
		// targetOption.loading = true;
		//
		// // load options lazily
		// setTimeout(() => {
		// 	targetOption.loading = false;
		// 	targetOption.children = [
		// 		{
		// 			label: `${targetOption.label} Dynamic 1`,
		// 			value: 'dynamic1',
		// 		},
		// 		{
		// 			label: `${targetOption.label} Dynamic 2`,
		// 			value: 'dynamic2',
		// 		},
		// 	];
		// 	this.setState({
		// 		options: [...this.state.options],
		// 	});
		// }, 1000);
	};

	// 手动出账 - 本期示数
	changeWheelNum = v => {
		let value = v.target.value;
		// 判断是否有空格，如果有空格，则去掉空格
		if (/\s+/.test(`${value}`)) {
			let val = `${value}`.replace(/\s+/, '');
			v.target.value = val;
		} else {
			if (/^[0-9]\d*$/.test(`${value}`)) {
				v.target.value = `${value}`;
			} else {
				v.target.value = `${value}`.substr(0, `${value}`.length - 1);
			}
			value = v.target.value;
			this.setState({
				currentWheelNumber: value
			});

			let amount = Number(value || 0) - Number(this.props.detail.wheelPresentNumber || 0);
			this.getCalculateFee(amount, this.props.detail.id, false);
		}
	};

	// 导出用户
	handleDownload = () => {
		const { searchForm } = this.state;
		const url = `${process.env.API_ROOT}/api/cm/customer/exportMechanicalExcel`;
		http.export(url, searchForm, res => {
			const blob = new Blob([res]);
			const downloadElement = document.createElement('a');
			const href = window.URL.createObjectURL(blob);
			const fileName = '机械表表用户信息_' + new Date().getTime() + '.xlsx';
			downloadElement.href = href;
			downloadElement.download = fileName;
			document.body.appendChild(downloadElement);
			downloadElement.click();
			document.body.removeChild(downloadElement);
			window.URL.revokeObjectURL(href);
		});
	};
	handleMechanicalUserBill = () => {
		const { searchForm, selectedRowKeys } = this.state;
		const url = `${process.env.API_ROOT}/api/statistics/batchExportMechanicalUserBill`;
		let param = { customerIdList: [] };
		if (selectedRowKeys.length > 0) {
			selectedRowKeys.map(item => {
				param.customerIdList.push(item);
			});
		} else {
			param = searchForm;
		}
		http.export(url, param, res => {
			const blob = new Blob([res]);
			const downloadElement = document.createElement('a');
			const href = window.URL.createObjectURL(blob);
			const fileName = '机械表账页_' + new Date().getTime() + '.zip';
			downloadElement.href = href;
			downloadElement.download = fileName;
			document.body.appendChild(downloadElement);
			downloadElement.click();
			document.body.removeChild(downloadElement);
			window.URL.revokeObjectURL(href);
		});
	};
	//递归渲染
	_renderTreeNode = treeData => {
		return treeData.map(tree => {
			if (tree.children) {
				return (
					<TreeNode key={tree.id} title={tree.name} value={tree.id}>
						{this._renderTreeNode(tree.children)}
					</TreeNode>
				);
			} else {
				return <TreeNode key={tree.id} title={tree.name} value={tree.id} />;
			}
		});
	};

	// 渲染搜索
	_renderSearchForm = () => {
		const { expand, searchForm, waterQuality } = this.state;
		const { installPosition, waterUseKinds, waterUseKindList, areaSelect, installLocationSelect, transcriberSelect } = this.props;
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					{/* 用户编号 */}
					<Col span={8}>
						<FormItem label={'用户编号:'}>
							<Input
								placeholder="请输入用户编号"
								value={searchForm.cno}
								onChange={v => {
									const { searchForm } = this.state;
									this.setState({
										searchForm: {
											...searchForm,
											cno: v.target.value
										}
									});
								}}
							/>
						</FormItem>
					</Col>
					{/* 用户户号 */}
					<Col span={8}>
						<FormItem label={'用户户号:'}>
							<Input
								placeholder="请输入用户户号"
								value={searchForm.hno}
								onChange={v => {
									this.setState({
										searchForm: {
											...searchForm,
											hno: v.target.value
										}
									});
								}}
							/>
						</FormItem>
					</Col>
					{/* 用户名称 */}
					<Col span={8}>
						<FormItem label={'用户名称:'}>
							<Input
								placeholder="请输入用户名称"
								value={searchForm.name}
								onChange={v => {
									this.setState({
										searchForm: {
											...searchForm,
											name: v.target.value
										}
									});
								}}
							/>
						</FormItem>
					</Col>
				</Row>
				<Row>
					{/* 用户地址 */}
					<Col span={8}>
						<FormItem label={'用户地址:'}>
							<Input
								placeholder="请输入用户地址"
								value={searchForm.address}
								onChange={v => {
									const { searchForm } = this.state;
									this.setState({
										searchForm: {
											...searchForm,
											address: v.target.value
										}
									});
								}}
							/>
						</FormItem>
					</Col>
					{/* 用户状态*/}
					<Col span={8}>
						<FormItem label={'用户状态:'}>
							<Select
								placeholder="请选择"
								value={searchForm.status}
								onChange={v => {
									const { searchForm } = this.state;
									this.setState({
										searchForm: {
											...searchForm,
											status: v
										}
									});
								}}
							>
								<Option value={null}>全部</Option>
								{constants.customerStatus.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					{/* 用户类型 */}
					<Col span={8}>
						<FormItem label={'用户类型:'}>
							<Select
								placeholder="请选择"
								value={searchForm.customerType}
								onChange={v => {
									const { searchForm } = this.state;
									this.setState({
										searchForm: {
											...searchForm,
											customerType: v
										}
									});
								}}
							>
								<Option value={null}>全部</Option>
								{constants.userType.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
				</Row>
				<Row>
					{/* 水表编号 */}
					<Col span={8}>
						<FormItem label={'水表编号:'}>
							<Input
								placeholder="请输入水表编号"
								value={searchForm.waterMeterNo}
								onChange={v => {
									const { searchForm } = this.state;
									this.setState({
										searchForm: {
											...searchForm,
											waterMeterNo: v.target.value
										}
									});
								}}
							/>
						</FormItem>
					</Col>
					{/* 水表分类 */}
					<Col span={8}>
						<FormItem label={'水表分类:'}>
							<Select
								placeholder="请选择水表分类"
								value={searchForm.meterType}
								onChange={v => {
									const { searchForm } = this.state;
									this.setState({
										searchForm: {
											...searchForm,
											meterType: v
										}
									});
								}}
							>
								<Option value={null}>全部</Option>
								{constants.meterType.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					{/* 水表厂家 */}
					<Col span={8}>
						<FormItem label={'水表厂家:'}>
							<Select
								placeholder="请选择"
								value={searchForm.waterMeterManufacturer}
								onChange={v => {
									const { searchForm } = this.state;
									this.setState({
										searchForm: {
											...searchForm,
											waterMeterManufacturer: v
										}
									});
								}}
							>
								<Option value={null}>全部</Option>
								{constants.waterMeterManufacturer.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'抄表员:'}>
							<Select
								placeholder="请选择"
								value={this.state.searchForm.transcriberId}
								onChange={v => {
									this.setState({
										searchForm: { ...this.state.searchForm, transcriberId: v }
									});
								}}
							>
								{transcriberSelect.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
				</Row>
				<Row>
					{/* 水表位置分类 */}
					<Col span={8} style={{ display: expand ? 'block' : 'none' }}>
						<FormItem label={'水表位置分类:'}>
							<Select
								placeholder="请选择"
								value={searchForm.installLocationTypeId}
								onChange={v => {
									this.handleChangeInstallLocationType(v);
									this.setState({
										searchForm: {
											...searchForm,
											installLocationTypeId: v,
											installLocationId: ''
										}
									});
								}}
							>
								{installPosition.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					{/* 水表位置 */}
					<Col span={8} style={{ display: expand ? 'block' : 'none' }}>
						<FormItem label={'水表位置:'}>
							<Select
								placeholder="请选择"
								value={searchForm.installLocationId}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, installLocationId: v } });
								}}
							>
								{installLocationSelect.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					{/* 所属片区 */}
					<Col span={8} style={{ display: expand ? 'block' : 'none' }}>
						<FormItem label={'所属片区:'}>
							<TreeSelect
								style={{ width: '100%' }}
								dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
								value={this.state.searchForm.areaIdList}
								placeholder="请选择片区"
								allowClear
								showSearch
								multiple
								filterTreeNode={(inputValue, treeNode) => treeNode.props.title.indexOf(inputValue) > -1}
								treeDefaultExpandedKeys={[100]}
								onChange={areaIdList => {
									this.setState({ searchForm: { ...this.state.searchForm, areaIdList: areaIdList } });
								}}
							>
								{areaSelect && this._renderTreeNode(areaSelect)}
							</TreeSelect>
						</FormItem>
					</Col>
					{/* 用水性质 */}
					<Col span={8} style={{ display: expand ? 'block' : 'none' }}>
						<FormItem label={'用水性质:'}>
							<Select
								placeholder="请选择"
								value={searchForm.waterUseKindType}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, waterUseKindType: v, waterUseKindIdList: [] } }, () => {
										this.props.waterUseKindList(v);
									});
								}}
							>
								{WATER_USE_KIND_TYPE.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8} style={{ display: expand ? 'block' : 'none' }}>
						<FormItem label={'缴费类型:'}>
							<Select
								placeholder="请选择"
								allowClear
								value={searchForm.contractType}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, contractType: v } });
								}}
							>
								<Option value={'包年'}>{'包年'}</Option>
								<Option value={'单月抄'}>{'单月抄'}</Option>
								<Option value={'抄表'}>{'抄表'}</Option>
								<Option value={'包月每月10吨'}>{'包月每月10吨'}</Option>
								<Option value={'磁卡表'}>{'磁卡表'}</Option>
								<Option value={'空户'}>{'空户'}</Option>
								<Option value={'其他'}>{'其他'}</Option>
							</Select>
						</FormItem>
					</Col>
				</Row>
				<Row>
					{/* 用水分类 */}
					<Col span={8} style={{ display: expand ? 'block' : 'none' }}>
						<FormItem label={'用水分类:'}>
							<Select
								placeholder="请选择"
								mode="multiple"
								value={searchForm.waterUseKindIdList}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, waterUseKindIdList: v } });
								}}
							>
								{waterUseKinds.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					{/* 付款方式 */}
					<Col span={8} style={{ display: expand ? 'block' : 'none' }}>
						<FormItem label={'付款方式:'}>
							<Select
								showSearch
								placeholder="请选择"
								value={this.state.searchForm.payWay}
								onChange={v => {
									this.setState({
										searchForm: {
											...searchForm,
											payWay: v
										}
									});
								}}
							>
								{PAY_WAY.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8} style={{ display: expand ? 'block' : 'none' }}>
						<FormItem label={'是否定量:'}>
							<Select
								showSearch
								placeholder="请选择"
								value={this.state.searchForm.fixedQuantity}
								onChange={v => {
									this.setState({
										searchForm: {
											...searchForm,
											fixedQuantity: v
										}
									});
								}}
							>
								{FIXED_QUANTITY.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8} style={{ display: expand ? 'block' : 'none' }}>
						<FormItem label={'用户余额:'}>
							<InputGroup>
								<InputNumber
									className="noBorderRight"
									style={{ width: '40%' }}
									value={this.state.searchForm.accountBalanceStart}
									onChange={value => {
										this.setState({ searchForm: { ...searchForm, accountBalanceStart: value } });
									}}
								/>
								<InputNumber className="midIpt noBorderLeft noBorderRight" style={{ width: '20%' }} placeholder="~" disabled />
								<InputNumber
									className="noBorderLeft"
									style={{ width: '40%' }}
									value={this.state.searchForm.accountBalanceEnd}
									onChange={value => {
										this.setState({
											searchForm: {
												...searchForm,
												accountBalanceEnd: value
											}
										});
									}}
								/>
							</InputGroup>
						</FormItem>
					</Col>
					<Col span={8} style={{ display: expand ? 'block' : 'none' }}>
						<FormItem label={'欠费金额:'}>
							<InputGroup>
								<InputNumber
									className="noBorderRight"
									style={{ width: '40%' }}
									value={this.state.searchForm.oweAmountStart}
									onChange={value => {
										this.setState({
											searchForm: {
												...searchForm,
												oweAmountStart: value
											}
										});
									}}
								/>
								<InputNumber className="midIpt noBorderLeft noBorderRight" style={{ width: '20%' }} placeholder="~" disabled />
								<InputNumber
									className="noBorderLeft"
									style={{ width: '40%' }}
									value={this.state.searchForm.oweAmountEnd}
									onChange={value => {
										this.setState({
											searchForm: {
												...searchForm,
												oweAmountEnd: value
											}
										});
									}}
								/>
							</InputGroup>
						</FormItem>
					</Col>
					{/*<Col span={8} style={{ display: expand ? 'block' : 'none' }}>*/}
					{/*	<FormItem label={'最后出账日期:'}>*/}
					{/*		{getFieldDecorator('khsj', { initialValue: '' })(<Select showSearch placeholder="请选择" />)}*/}
					{/*	</FormItem>*/}
					{/*</Col>*/}
					{/*<Col span={8} style={{ display: expand ? 'block' : 'none' }}>*/}
					{/*	<FormItem label={'账户余额:'}>*/}
					{/*		{getFieldDecorator('khsj', { initialValue: '' })(<Select showSearch placeholder="请选择" />)}*/}
					{/*	</FormItem>*/}
					{/*</Col>*/}
				</Row>
				<Row>
					<Col span={24} align="right">
						<Button className="searchBtn" type="primary" onClick={this.handleSearch}>
							搜索
						</Button>
						<Button className="searchBtn" type="default" onClick={this.handleReset}>
							重置
						</Button>
						<Button type="link" onClick={this.showMoreSearch}>
							{this.state.expand ? '关闭高级搜索' : '高级搜索'}
						</Button>
					</Col>
				</Row>
			</Form>
		);
	};

	//列表复选框
	onSelectChange = (selectedRowKeys, selectedRows) => {
		this.setState({ selectedRowKeys, selectedRows });
	};

	//批量派发抄表任务
	batchDispatchTask = () => {
		const { selectedRowKeys } = this.state;
		if (selectedRowKeys && selectedRowKeys.length > 0) {
			this.props.batchTranscribe({ customerIdList: selectedRowKeys });
			this.setState({ selectedRowKeys: [] });
		} else {
			this.getList();
			setTimeout(() => this.openModal(), 1000);
		}
	};

	openModal () {
		if (this.props.total) {
			Modal.confirm({
				title: '提示',
				content: `未勾选指定用户时,根据查询条件来批量修改.一共${this.props.total}户，确认要修改吗？`,
				okText: '确认',
				cancelText: '取消',
				onOk: () => {
					const { searchForm } = this.state;
					this.props.batchTranscribeByCondition(searchForm, this.getList());
				}
			});
		} else {
			message.error('未查询到用户');
		}
	}

	render () {
		const { page, pageSize, currentWheelNumber, selectedRowKeys } = this.state;
		const { datalist, total, statistic, detail, visible, showContractTypeModal, calculateFee1, calculateFee2, form } = this.props;
		const { getFieldDecorator, setFieldsValue, getFieldValue } = form;
		const columns = [
			{
				title: '用户名称',
				dataIndex: 'name',
				key: 'name',
				align: 'center'
			},
			{
				title: '片区',
				dataIndex: 'areaName',
				key: 'areaName',
				align: 'center'
			},
			{
				title: '用户地址',
				dataIndex: 'address',
				key: 'address',
				align: 'center'
			},
			{
				title: '账户余额（元）',
				dataIndex: 'accountBalance',
				key: 'accountBalance',
				align: 'center'
			},
			{
				title: '欠费金额（元）',
				dataIndex: 'oweFee',
				key: 'oweFee',
				align: 'center',
				render: text => {
					return text ? text : 0;
				}
			},
			{
				title: '用户编号',
				dataIndex: 'cno',
				key: 'cno',
				align: 'center'
			},
			{
				title: '用户户号',
				dataIndex: 'hno',
				key: 'hno',
				align: 'center'
			},
			{
				title: '水表编号',
				dataIndex: 'waterMeterNo',
				key: 'waterMeterNo',
				align: 'center'
			},
			{
				title: '水表分类',
				dataIndex: 'meterType',
				key: 'meterType',
				align: 'center'
			},
			{
				title: '水表种类',
				dataIndex: 'waterMeterType',
				key: 'waterMeterType',
				align: 'center'
			},
			{
				title: '水表厂家',
				dataIndex: 'waterMeterManufacturer',
				key: 'waterMeterManufacturer',
				align: 'center'
			},
			{
				title: '水表位置分类',
				dataIndex: 'installLocation',
				key: 'installLocation',
				align: 'center'
			},
			{
				title: '用水分类',
				dataIndex: 'waterUseKindName',
				key: 'waterUseKindName',
				align: 'center'
			},
			{
				title: '是否定量',
				dataIndex: 'fixedQuantity',
				key: 'fixedQuantity',
				align: 'center',
				render: (text, record, index) => {
					return text ? '是' : '否';
				}
			},
			{
				title: '付款方式',
				dataIndex: 'payWay',
				key: 'payWay',
				align: 'center'
			},
			{
				title: '用户类型',
				dataIndex: 'customerType',
				key: 'customerType',
				align: 'center'
			},
			// {
			// 	title: '抄表员',
			// 	dataIndex: 'transcriberName',
			// 	key: 'transcriberName',
			// 	align: 'center'
			// },
			{
				title: '机表读数',
				dataIndex: 'wheelPresentNumber',
				key: 'wheelPresentNumber',
				align: 'center'
			},
			{
				title: '缴费类型',
				dataIndex: 'contractType',
				key: 'contractType',
				align: 'center'
			},
			{
				title: '开户时间',
				dataIndex: 'createTime',
				key: 'createTime',
				align: 'center'
			},
			{
				title: '开户操作员',
				dataIndex: 'createPersonName',
				key: 'createPersonName',
				align: 'center'
			},
			{
				title: '用户状态',
				dataIndex: 'status',
				key: 'status',
				align: 'center',
				width: 80,
				fixed: 'right',
				render: text => {
					return <Tag color={userStatusColorMap.get(text)}>{text}</Tag>;
				}
			},
			{
				title: '操作',
				key: 'operation',
				width: 300,
				align: 'center',
				fixed: 'right',
				render: (text, record, index) => {
					return (
						<span>
							<ButtonGroup>
								<Button title="查看" permission={React.$pmn('REVENUE:MECHANICAL_WATER_METER_MANAGEMENT:USERS:VIEW')} className="btn" type="primary" size="small" icon="eye" onClick={() => this.handleView(record)} />

								<Button title="手工出账" permission={React.$pmn('REVENUE:MECHANICAL_WATER_METER_MANAGEMENT:USERS:MANUALBILLING')} className="btn" type="primary" size="small" icon="money-collect" onClick={() => this.handleManualBilling(record)} disabled={record.meterType === '虚表' ? true : false} />

								<Button title="派发抄表任务" permission={React.$pmn('REVENUE:MECHANICAL_WATER_METER_MANAGEMENT:USERS:TASK')} className="btn" type="primary" size="small" icon="profile" onClick={() => this.dispatchTask(record)} disabled={record.meterType === '虚表' ? true : false} />

								<Button title="修改缴费类型" permission={React.$pmn('REVENUE:MECHANICAL_WATER_METER_MANAGEMENT:USERS:UPDATEPAYTYPE')} className="btn" type="primary" size="small" icon="form" onClick={() => this.handleContractType(record)} />
							</ButtonGroup>
						</span>
					);
				}
			}
		];
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: total => {
				return `户数:${statistic.hno} 户  共 ${total} 条数据 `;
			},
			onChange: page => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		const rowSelection = {
			fixed: true,
			selectedRowKeys,
			onChange: this.onSelectChange
		};
		return (
			<div className="shadow-radius">
				<h1>机械表用户</h1>
				<Row>{this._renderSearchForm()}</Row>
				<Row className="search-button buttonGroup" style={{ marginBottom: 20 }}>
					<Button type="primary" className="btn" onClick={this.handleDownload}>
						导出机械表用户
					</Button>
					<Button type="primary" className="btn" onClick={this.batchDispatchTask}>
						批量派发抄表任务
					</Button>
					<Button type="primary" className="btn" onClick={this.handleMechanicalUserBill}>
						导出账页
					</Button>
				</Row>

				<Table bordered columns={columns} scroll={{ x: 3200 }} rowKey={data => data.id} dataSource={datalist} pagination={paginationProps} rowSelection={rowSelection} />

				<Modal title="手工出账" visible={visible} onCancel={this.handleCancel} footer={null}>
					<Row>
						{detail ? (
							<Form {...constants.formItemLayout}>
								<FormItem label="用户编号：">
									<Input disabled value={detail.cno} />
								</FormItem>
								<FormItem label="用户名称：">
									<Input disabled value={detail.name} />
								</FormItem>
								<FormItem label="用户地址：">
									<Input disabled value={detail.address} />
								</FormItem>
								<FormItem label="用水性质：">
									<Input disabled value={detail.waterUseKindType} />
								</FormItem>
								<FormItem label="用水分类：">
									<Input disabled value={detail.waterUseKindName} />
								</FormItem>
								<FormItem label="缴费类型：">
									<Input disabled value={detail.contractType || ''} />
								</FormItem>
								<FormItem label="上期示数：">
									<Input disabled value={detail.wheelPresentNumber} />
								</FormItem>
								<FormItem label="本期示数：">
									<view style={{ display: 'flex', justifyContent: 'space-between' }}>
										<Input value={currentWheelNumber} onChange={this.changeWheelNum} placeholder="请输入本期示数" />
									</view>
								</FormItem>
								{/*<FormItem label="收费区间：">*/}
								{/*	{getFieldDecorator(`feeInterval`, {*/}
								{/*		initialValue: '',*/}
								{/*		rules: []*/}
								{/*	})(<Input placeholder="收费区间" />)}*/}
								{/*</FormItem>*/}
								<FormItem label="单价：">
									<Input disabled value={calculateFee2} />
								</FormItem>
								{currentWheelNumber && calculateFee1 && currentWheelNumber > detail.wheelPresentNumber ? (
									<>
										<FormItem label="吨数：">
											<Input disabled value={Number(currentWheelNumber || 0) - Number(this.props.detail.wheelPresentNumber || 0)} />
										</FormItem>
										<FormItem label="金额：">
											<Input disabled value={calculateFee1} />
										</FormItem>
									</>
								) : null}
							</Form>
						) : null}
						<Col align="center">
							<Button className="btn" type="primary" onClick={this.handleSubmit}>
								确定
							</Button>
							<Button className="btn" type="default" onClick={this.handleCancel}>
								取消
							</Button>
						</Col>
					</Row>
				</Modal>

				<Modal title="修改缴费类型" visible={showContractTypeModal} onCancel={this.handleCancel} footer={null}>
					<Row>
						{detail ? (
							<Form {...constants.formItemLayout}>
								<FormItem label="用户编号：">
									<Input disabled value={detail.cno} />
								</FormItem>
								<FormItem label="用户名称：">
									<Input disabled value={detail.name} />
								</FormItem>
								<FormItem label="原缴费类型：">
									<Input disabled value={detail.contractType} />
								</FormItem>
								<FormItem label="新缴费类型：">
									<Select
										placeholder="请选择"
										onChange={v => {
											this.setState({ contractType: v });
										}}
									>
										<Option value={'包年'}>{'包年'}</Option>
										<Option value={'单月抄'}>{'单月抄'}</Option>
										<Option value={'抄表'}>{'抄表'}</Option>
										<Option value={'包月每月10吨'}>{'包月每月10吨'}</Option>
										<Option value={'磁卡表'}>{'磁卡表'}</Option>
										<Option value={'空户'}>{'空户'}</Option>
										<Option value={'其他'}>{'其他'}</Option>
									</Select>
								</FormItem>
							</Form>
						) : null}
						<Col align="center">
							<Button className="btn" type="primary" onClick={this.handleContractTypeSubmit}>
								确定
							</Button>
							<Button className="btn" type="default" onClick={this.handleCancel}>
								取消
							</Button>
						</Col>
					</Row>
				</Modal>
			</div>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('mechanicalUsers');
	return {
		fields: data.fields,
		datalist: data.datalist, // 列表数据
		total: data.total, // 总条数
		visible: data.visible, // 是否显示弹窗
		detail: data.detail, // 获取详情
		installPosition: data.installPosition, // 水表安装位置
		installLocationSelect: data.installLocationSelect,
		transcriberSelect: data.transcriberSelect, //抄表员选择框
		waterUseKinds: data.waterUseKinds, // 用水分类
		areaSelect: data.areaSelect, //区域
		statistic: data.statistic,
		calculateFee1: data.calculateFee1, //计算金额
		calculateFee2: data.calculateFee2, //单价
		showContractTypeModal: data.showContractTypeModal
	};
};
const mapDispatchToProps = dispatch => ({
	setState: data => dispatch(actionCreators.setState(data)), // 设置redux值
	list: data => dispatch(actionCreators.list(data)), // 获取列表
	getDetail: data => dispatch(actionCreators.detail(data)), // 获取详情
	add: data => dispatch(actionCreators.add(data)), // 新增记录
	del: data => dispatch(actionCreators.del(data)), // 删除记录
	modify: data => dispatch(actionCreators.modify(data)), // 修改记录
	getInstallPosition: () => dispatch(actionCreators.installPosition()), // 水表安装位置
	reallyPosition: type => dispatch(actionCreators.reallyPosition(type)), // 水表实际安装位置
	waterUseKindList: type => dispatch(actionCreators.waterUseKindList(type)), // 用水分类
	handWorkOutBill: (data, list) => dispatch(actionCreators.handWorkOutBill(data, list)), // 手动出账
	transcribe: data => dispatch(actionCreators.transcribe(data)), // 手动派发抄表任务
	getAreaSelect: () => dispatch(actionCreators.getAreaSelect()), //获取片区选择框
	batchTranscribe: data => dispatch(actionCreators.batchTranscribe(data)), //更具勾选派发抄表任务
	batchTranscribeByCondition: (data, refurbish) => dispatch(actionCreators.batchTranscribeByCondition(data, refurbish)), //更具条件派发抄表任务
	getTranscriberSelect: data => dispatch(actionCreators.getTranscriberSelect(data)), // 获取抄表员选择框
	calculateFee: data => dispatch(actionCreators.calculateFee(data)), //计算金额
	updateContractType: (data, refurbish) => dispatch(actionCreators.updateContractType(data, refurbish)) //修改收费区间
});
export default connect(mapStateToProps, mapDispatchToProps)(Form.create()(withRouter(Index)));
