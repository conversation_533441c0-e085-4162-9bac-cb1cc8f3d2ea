import http from '$http';
import * as actionTypes from './constants';
import { message } from 'antd';
import api from './api';

// 数据回填
const payload = (type, data) => ({ type, data });

// 组件操作redux数据状态
const setState = (data) => ({ type: actionTypes.SET_STATE, data });

// 获取列表
const list = data => {
	return async dispatch => {
		const res = await http.post(api.list, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.LIST_RECORD, res.data));
		}
		const statisticRes = await http.post(api.statistic, data);
		if (statisticRes.code === 0) {
			dispatch(payload(actionTypes.STATISTIC, statisticRes.data));
		}
	}
};

//获取所有抄表员
const getAllTranscriber = data => {
		return http.post(api.getAllTranscriber, data);
}

// 获取详情
const detail = (data,ops)=> {
	return async dispatch => {
		const res = await http.restGet(api.detail, data);
		if (res.code === 0) {
				debugger
				if(ops){
						ops();
				}
			dispatch(payload(actionTypes.DETAIL_RECORD, res.data));
		}
	};
};
// 新增
const add = (data, list) => {
	return async dispatch => {
		const res = await http.post(api.add, data);
		if (res.code === 0) {
			message.success('新增成功！');
			list();
		}
	};
};
// 删除
const del = (data, list) => {
	return async dispatch => {
		const res = await http.del(api.del, data);
		if (res.code === 0) {
			message.success('删除成功！');
			list();
		}
	};
};
// 修改
const modify = (data, list) => {
	return async dispatch => {
		const res = await http.post(api.modify, data);
		if (res.code === 0) {
			message.success('修改成功！');
			list();
		}
	};
};
// 水表安装位置
const installPosition = () => {
	return async dispatch => {
		const res = await http.get(api.installPosition);
		if (res.code === 0) {
			dispatch(payload(actionTypes.INSTALL_POSITION_RECORD, res.data));
		}
	};
};
// 水表实际安装位置
const reallyPosition = (type) => {
	return async dispatch => {
		const res = await http.restGet(api.reallyPosition, type);
		if (res.code === 0) {
			dispatch(payload(actionTypes.REALLY_POSITION_RECORD, res.data));
		}
	};
};
// 用水性质列表
const waterUseKindList = (type) => {
	return async dispatch => {
		const res = await http.restGet(api.waterUseKindList, type);
		if (res.code === 0) {
			dispatch(payload(actionTypes.WATER_USE_KIND_RECORD, res.data));
		}
	};
};
// 手动出账
const handWorkOutBill = (data, list) => {
	return async dispatch => {
		const res = await http.post(api.handWorkOutBill, data);
		if (res.code === 0) {
			list();
			message.success('出账成功！');
		}
	};
};
// 手动出账
const fixedQuantityOutBillByPopulartion = (data, list) => {
		return async dispatch => {
				const res = await http.post(api.fixedQuantityOutBillByPopulartion, data);
				if (res.code === 0) {
						list();
						message.success('出账成功！');
				}
		};
};

const transcribe = data => {
	return async dispatch => {
		const res = await http.restPost(api.transcribe, data);
		if (res.code === 0) {
			message.success('派发任务成功！');
		}
	};
};
//获取片区
const getAreaSelect = () => {
	return async dispatch => {
		const res = await http.get(api.getAreaSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_AREA_SELECT, res.data));
		}
	};
};
//根据勾选派发抄表任务
const batchTranscribe = (data) => {
	return async dispatch => {
		const res = await http.post(api.batchTranscribe, data);
		if (res.code === 0) {
			message.success('批量派发抄表任务成功！');
			dispatch(payload(actionTypes.BATCH_TRANSCRIBE, res.data));
		}
	};
};
//根据条件派发抄表任务
const batchTranscribeByCondition = (data, refurbish) => {
	return async dispatch => {
		const res = await http.post(api.batchTranscribeByCondition, data);
		if (res.code === 0) {
			message.success('批量派发抄表任务成功！');
			refurbish();
		}
	};
};
//获取用户等级
const customerLevelList = () => {
	return async dispatch => {
		const res = await http.get(api.customerLevelList);
		if (res.code === 0) {
			dispatch(payload(actionTypes.CUSTOMER_LEVEL_RECORD, res.data));
		}
	};
};
export {
	setState,
	list,
	detail,
	add,
	modify,
	del,
	installPosition,
	reallyPosition,
	waterUseKindList,
	handWorkOutBill,
	transcribe,
	getAreaSelect,
	batchTranscribe,
	batchTranscribeByCondition,
 fixedQuantityOutBillByPopulartion,
	customerLevelList,
	getAllTranscriber
};
