import * as actionTypes from "./constants";
import { dataType } from '$utils'

const defaultState = {
	fields: null,
	datalist: [], // 列表数据
	total: 0, // 总条数
	detail: null, // 详情
	visible: false, // 涉及到交互的弹窗显示控制 默认为false
	installPosition: [], // 安装位置
	installLocationSelect: [],
	waterUseKinds: [], // 用水性质
	areaSelect: [], // 片区
	statistic: {},
	customerLevels: [], // 用户等级
};

// 重要提示： 请做好各代码段的注释！！！
export default (state = defaultState, action) => {
	switch (action.type) {

		// 回填组件传给redux的值
		case actionTypes.SET_STATE:
			let _data = action.data;
			if (dataType(_data) === 'Object') {
				state[_data.key] = _data.value;
			}
			if (dataType(_data) === 'Array') {
				_data.forEach(item => {
					state[item.key] = item.value;
				})
			}
			return { ...state };

		// 回填列表数据
		case actionTypes.LIST_RECORD:
			state.datalist = action.data.rows;
			state.total = action.data.total;
			return { ...state };

		// 回填详情信息
		case actionTypes.DETAIL_RECORD:
			// ... do something
			state.detail = action.data;
			return { ...state };
		// 新增记录集
		case actionTypes.ADD_RECORD:
			// ... do something
			return { ...state };
		// 删除记录集
		case actionTypes.DEL_RECORD:
			// ... do something
			return { ...state };
		// 修改记录集
		case actionTypes.MODIFY_RECORD:
			// ... do something
			return { ...state };
		// 水表安装位置
		case actionTypes.INSTALL_POSITION_RECORD:
			// ... do something
			state.installPosition = action.data;
			return { ...state };
		// 水表实际安装位置
		case actionTypes.REALLY_POSITION_RECORD:
			// ... do something
			state.installLocationSelect = action.data;
			return { ...state };
		// 获取用水性质
		case actionTypes.WATER_USE_KIND_RECORD:
			state.waterUseKinds = action.data;
			return { ...state };
		// 获取片区选择框
		case actionTypes.GET_AREA_SELECT:
			state.areaSelect = action.data;
			return { ...state };
		// 获取统计详情
		case actionTypes.STATISTIC:
			state.statistic = action.data;
			return { ...state };
		//用户等级
		case actionTypes.CUSTOMER_LEVEL_RECORD:
			state.customerLevels = action.data;
			return { ...state };
		default:
			return state;
	}
}
