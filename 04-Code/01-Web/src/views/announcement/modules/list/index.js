import React, { Component } from 'react';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import { actionCreators } from '../../store';
import { Modal, Table, Form, Row, Col, Input, Button } from 'antd';
import './index.scss';

const { confirm } = Modal;
const FormItem = Form.Item;

class Index extends Component {
	constructor(props) {
		super(props);
		this.state = {
			visible: false, // 是否显示详情
			pageSize: 10, // 页条数
			page: 1, // 页码
			title: '', // 搜索条件 - 标题
		};
	}

	componentDidMount() {
		// const { fields } = this.props;
		// const isTags = localStorage.getItem('tags');
		// // 获取当前 reducer fields的值，
		// // 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		// if (fields && isTags) {
		// 	this.setState({
		// 		...fields.state
		// 	}, () => {
		// 		this.props.form && this.props.form.setFieldsValue({
		// 			...fields.form
		// 		})
		// 	})
		// } else {
		// 	this.getList();
		// }
		this.getList();
	}

	componentWillUnmount() {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			})
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			})
		}
	}

	// 获取数据
	getList = () => {
		const { pageSize, page, title } = this.state;
		this.props.getList({ pageSize, page, title });
	};

	// 搜索功能
	handleSearch = () => {
		this.setState({
			page: 1
		}, () => {
			this.getList();
		})
	};

	// 重置搜索条件
	handleReset = () => {
		this.setState({ title: '' }, () => {
			this.getList();
		})
	};

	// 搜索框 值回填
	changeTitle = v => {
		this.setState({
			title: v.target.value
		})
	};

	// 编辑
	handleEdit = (row) => {
		this.props.history.push({ pathname: "/announcementMessage/modify", state: { row } })
	}

	// 删除
	handleDel = (row) => {
		const _this = this;
		confirm({
			title: '操作确认',
			content: '确定要删除当前记录吗？',
			okText: '确定',
			cancelText: '取消',
			onOk() {
				_this.props.del(row.id);
			},
			onCancel() { }
		});
	}

	// 查看详情
	handleView = row => {
		this.setState({ visible: true }, () => {
			this.props.getDetail(row.id)
		});
	};

	//
	handleOk = () => {
		this.setState({ visible: false });
	};

	//
	handleCancel = () => {
		this.setState({ visible: false });
	};

	// 列表分页
	handlePageChange = (page) => {
		this.setState({
			page
		}, () => {
			this.getList();
		})
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState({
			page: 1,
			pageSize: size,
		}, () => {
			this.getList();
		})
	};


	render() {
		const { detail, total } = this.props;
		const { pageSize, page } = this.state;
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: [10, 20, 50, 100],
			total: total,
			showTotal: (total) => { return `共 ${total} 条数据 ` },
			onChange: (page) => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		const columns = [
			{
				title: '标题',
				dataIndex: 'title',
				width: 250,
				align: 'center'
			},
			{
				title: '发布人',
				dataIndex: 'createPersonName',
				width: 150,
				align: 'center'
			},
			{
				title: '更新日期',
				dataIndex: 'updateTime',
				width: 150,
				align: 'center'
			},
			{
				title: '创建日期',
				dataIndex: 'createTime',
				width: 150,
				align: 'center'
			},
			{
				title: '操作',
				key: 'operation',
				width: 150,
				align: 'center',
				render: (text, record, index) => {
					return (
						<span>
							<Button permission={React.$pmn('REVENUE:NOTICE_MESSAGE:LIST:VIEW')} title='查看' className='btn' type="default" size="small" icon="eye" onClick={() => this.handleView(record)} />
							<Button permission={React.$pmn('REVENUE:NOTICE_MESSAGE:LIST:EDIT')} title='编辑' className='btn' type="primary" size="small" icon="form" onClick={() => this.handleEdit(record)} />
							<Button permission={React.$pmn('REVENUE:NOTICE_MESSAGE:LIST:DEL')} title='删除' className='btn' type="danger" size="small" icon="delete" onClick={() => this.handleDel(record)} />
							&nbsp;
						</span>
					)
				},
			},
		];
		return (
			<div className="shadow-radius announcement">
				{/* 页面标题 - 搜索条件 */}
				<Row>
					<Col span={6}><h1>公告列表</h1></Col>
					<Col span={18}>
						<Form layout="inline">
							<Col span={20} align={'right'}>
								<FormItem>
									<Input placeholder="请输入标题" value={this.state.title} onChange={this.changeTitle} />
								</FormItem>
							</Col>
							<Col span={4}>
								<FormItem>
									&nbsp;
									<Button type="primary" onClick={this.handleSearch}>
										搜索
									</Button>
								</FormItem>
							</Col>
						</Form>
					</Col>
				</Row>
				{/* 表格渲染 */}
				<Row className='main'>
					<Table
						bordered
						columns={columns}
						rowKey={(record) => record.id}
						dataSource={this.props.datalist}
						pagination={paginationProps}
					/>
				</Row>
				{/* 查看详情 */}
				<Modal title="公告详情" visible={this.state.visible} onOk={this.handleOk} onCancel={this.handleCancel} footer={null}>
					{
						detail && (
							<div className='currentRow'>
								<Row>
									<Col span={5} align='right'><strong>公告标题:</strong>&nbsp;&nbsp;</Col>
									<Col span={19}>{detail.title}</Col>
								</Row>
								<Row>
									<Col span={5} align='right'><strong>发布人:</strong>&nbsp;&nbsp;</Col>
									<Col span={19}>{detail.createPersonName}</Col>
								</Row>
								<Row>
									<Col span={5} align='right'><strong>发布时间:</strong>&nbsp;&nbsp;</Col>
									<Col span={19}>{detail.createTime}</Col>
								</Row>
								<Row>
									<Col span={5} align='right'><strong>公告正文:</strong>&nbsp;&nbsp;</Col>
									<Col span={19} className='currentContent' dangerouslySetInnerHTML={{ __html: detail.content }} />
								</Row>
							</div>
						)
					}

				</Modal>
			</div>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('announcement');
	return {
		fields: data.fields,
		datalist: data.datalist,
		detail: data.detail,
		total: data.total,
	}
};
const mapDispatchToProps = dispatch => ({
	setState: data => dispatch(actionCreators.setState(data)),
	getList: data => {
		dispatch(actionCreators.list(data))
	},
	del: data => {
		dispatch(actionCreators.del(data))
	},
	getDetail: data => {
		dispatch(actionCreators.detail(data))
	},
});
export default connect(
	mapStateToProps,
	mapDispatchToProps
)(withRouter(Index));
