import http from '$http';
import * as actionTypes from './constants';
import { message } from 'antd';
import api from './api';

// 数据回填
const payload = (type, data) => ({type, data});

// 组件操作redux数据状态
const setState = data => ({ type: actionTypes.SET_STATE, data });

// 发布新增公告
const publish = (data, props) => {
	http.post(api.publish, data).then(res => {
		if (res.code === 0) {
			message.success('发布成功！');
			setTimeout(() => props.push('/announcementMessage/list'), 300)

		}
	})
};

// 修改公告
const modify = (data, props) => {
	return async () => {
		const res = await http.post(api.modify, data);
		if (res.code === 0) {
			message.success('修改成功！');
			setTimeout(() => props.push('/announcementMessage/list'), 300)
		} else {
			message.error(res.msg);
		}
	}
};

// 删除公告
const del = (data) => {
	return async dispatch => {
		const res = await http.del(api.del, data);
		if (res.code === 0) {
			message.success('删除成功！');
			dispatch(payload(actionTypes.DEL_RECORD, data));
			return true;
		}
	}
};

// 获取公告列表
const list = data => {
	return async dispatch => {
		const res = await http.post(api.list, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.LIST_RECORD, res.data));
			return true;
		}
	}
};

// 获取公告详情
const detail = data => {
	return async dispatch => {
		const res = await http.restGet(api.detail, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.DETAIL_RECORD, res.data))
		}
	}
};

export { publish, modify, del, list, detail, setState };
