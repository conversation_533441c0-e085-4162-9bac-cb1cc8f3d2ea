	import React, { Component } from 'react';
import Particles from 'react-particles-js';
import { Form, Icon, Input, Button } from 'antd';
import { connect } from 'react-redux';
import { actionCreators } from '@/layout/store';
import '@/assets/css/login';


const FormItem = Form.Item;
class Login extends Component {
	state = { clientHeight: document.documentElement.clientHeight || document.body.clientHeight };
	constructor(props) {
		super(props);
		this.onResize = this.onResize.bind(this);
	}
	login = e => {
		e.preventDefault();
		this.props.form.validateFields((err, values) => {
			if (!err) {
				this.props.userLogin(values, this.props.history);
			} else {
				console.log(err);
			}
		});
	};

	componentWillMount() {
		localStorage.removeItem('bh');
		localStorage.removeItem('bhl');
		//防止页面后退
		window.history.pushState(null, null, document.URL);
		window.addEventListener('popstate', function () {
			window.history.pushState(null, null, document.URL);
		});
	}

	componentDidMount() {
		window.addEventListener('resize', this.onResize);
	}
	componentWillUnmount() {
		window.addEventListener('resize', this.onResize);
		// componentWillMount进行异步操作时且在callback中进行了setState操作时，需要在组件卸载时清除state
		this.setState = () => {
			return;
		};
	}
	onResize() {
		this.setState({ clientHeight: document.documentElement.clientHeight || document.body.clientHeight });
	}
	render() {
		const { getFieldDecorator } = this.props.form;
		return (
			<div className="container">
				<Particles
          style={{visibility: 'hidden'}}
					height={this.state.clientHeight - 5 + 'px'}
					params={{
						number: { value: 50 },
						ize: { value: 1 },
						interactivity: {
							events: {
								onhover: { enable: true, mode: 'repulse' }
							}
						}
					}}
				/>
				<div className="content">
					<div className="title">智慧水务管理平台</div>
					<Form>
						<FormItem>
							{getFieldDecorator('userName', {
								initialValue: '',
								rules: [{ required: true, message: '请填写用户名！' }]
							})(<Input prefix={<Icon type="user" style={{ color: 'rgba(0,0,0,.25)' }} />} placeholder="用户名" />)}
						</FormItem>
						<FormItem>
							{getFieldDecorator('password', {
								initialValue: '',
								rules: [{ required: true, message: '请填写密码！' }]
							})(<Input.Password prefix={<Icon type="lock" style={{ color: 'rgba(0,0,0,.25)' }} />} placeholder="密码" />)}
						</FormItem>
						<FormItem>
							<Button type="primary" htmlType="submit" block onClick={this.login}>
								登录
							</Button>
							{/* <div style={{ color: '#999',paddingTop:'10px',textAlign:'center' }}>Tips : 开发阶段，账号密码暂时固定，可修改。</div> */}
						</FormItem>
					</Form>
				</div>
			</div>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('layout');
	return {
		state: data
	}
};
const mapDispatchToProps = dispatch => ({
	setUserInfo: data => {
		dispatch(actionCreators.setUserInfo(data));
	},
	userLogin: (data, props) => {
		dispatch(actionCreators.userLogin(data, props));
	},
});
export default connect(
	mapStateToProps,
	mapDispatchToProps
)(Form.create()(Login));
