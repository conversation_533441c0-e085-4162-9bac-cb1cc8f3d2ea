import React, { Component } from 'react';
import { actionCreators } from './store';
import { connect } from 'react-redux';
import { Button, Col, Form, Input, Row, Select, Table, DatePicker, Modal, Upload, message } from 'antd';
import { constants } from '$utils';
import { WATER_METER_TYPE, WATER_METER_KIND } from '@/constants/waterMeter';
import './index.scss';
import { batchCloseValve } from './store/actionCreators';

const FormItem = Form.Item;
const { Option } = Select;
const ButtonGroup = Button.Group;
const { confirm } = Modal;
const { RangePicker } = DatePicker;

const defaultSearchForm = {
	no: '',
	address: '',
	qrCodeNumber: '',
	type: null,
	kind: null,
	status: 1
};

class Index extends Component {
	constructor(props) {
		super(props);
		this.state = {
			page: constants.page,
			pageSize: constants.pageSize,
			searchForm: {
				no: '',
				address: '',
				qrCodeNumber: '',
				type: null,
				kind: null,
				status: 1
			},
			columns: [
				{
					title: '水表编号',
					dataIndex: 'no',
					key: 'no',
					align: 'center'
				},
				{
					title: '水表厂家',
					dataIndex: 'manufacturer',
					key: 'manufacturer',
					align: 'center'
				},
				{
					title: '水表类型',
					dataIndex: 'type',
					key: 'type',
					align: 'center'
				},
				{
					title: '水表种类',
					dataIndex: 'kind',
					key: 'kind',
					align: 'center'
				},
				{
					title: '阀门状态',
					dataIndex: 'valveStatus',
					key: 'valveStatus',
					align: 'center'
				},
				{
					title: '区域',
					dataIndex: 'address',
					key: 'address',
					align: 'center'
				},
				{
					title: '详细地址',
					dataIndex: 'installTime',
					key: 'installTime',
					align: 'center'
				},
				{
					title: '安装位置分类',
					dataIndex: 'installTime',
					key: 'installTime',
					align: 'center'
				},
				{
					title: '安装位置',
					dataIndex: 'installTime',
					key: 'installTime',
					align: 'center'
				},
				{
					title: '开户状态',
					dataIndex: 'installTime',
					key: 'installTime',
					align: 'center'
				},
				{
					title: '开户时间',
					dataIndex: 'installTime',
					key: 'installTime',
					align: 'center'
				},
				{
					title: '导入时间',
					dataIndex: 'installTime',
					key: 'installTime',
					align: 'center'
				},
				{
					title: '导入人',
					dataIndex: 'installTime',
					key: 'installTime',
					align: 'center'
				},
				{
					title: '操作',
					width: 100,
					align: 'center',
					render: (text, record, index) => {
						return (
							<Button
								title='查看'
								className='btn'
								type="primary"
								size="small"
								icon="pay-circle"
								onClick={() => this.handleView(record)}
							>
								缴费
							</Button>
						)
					}
				},
			],
			selectedRowKeys: []
		};
	}

	componentDidMount() {
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields&&isTags) {
			this.setState({
				...fields.state
			}, () => {
				this.props.form.setFieldsValue({
					...fields.form
				})
			})
		} else {
			this.getList();
		}
	}

	componentWillUnmount() {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			})
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			})
		}
	}

	// 获取列表
	getList = () => {
		const { page, pageSize, searchForm } = this.state;
		this.props.list(Object.assign({ ...searchForm }, { page, pageSize }));
	};

	// 搜索功能
	handleSearch = () => {
		this.setState(
			{
				page: 1
			},
			() => {
				this.getList();
			}
		);
	};

	// 重置搜索
	handleReset = () => {
		this.setState(
			{
				searchForm: Object.assign({}, defaultSearchForm)
			},
			() => {
				this.getList();
			}
		);
	};

	// 列表分页
	handlePageChange = page => {
		this.setState(
			{
				page
			},
			() => {
				this.getList();
			}
		);
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState(
			{
				page: 1,
				pageSize: size
			},
			() => {
				this.getList();
			}
		);
	};

	// 查看详情
	handleView = record => { };

	// 编辑记录
	handleEdit = record => { };

	// 删除记录
	handleDel = record => { };

	// 批量关阀
	handleBatchCloseValve = () => {
		let param = {
			waterMeterIdList: this.state.selectedRowKeys
		};
		this.props.batchCloseValve(param);
		this.setState({ selectedRowKeys: [] });
	};

	// 选中事件
	onSelectChange = selectedRowKeys => {
		this.setState({ selectedRowKeys });
	};

	// 开户时间选择
	getOpenDate = (date, dateString) => {
		if (dateString.length === 2) {
			this.setState({
				searchForm: { ...this.state.searchForm, createStartDate: dateString[0], createEndDate: dateString[1] }
			})
		}
	}

	// 导入时间选择
	getImportDate = (date, dateString) => {
		if (dateString.length === 2) {
			this.setState({
				searchForm: { ...this.state.searchForm, createStartDate: dateString[0], createEndDate: dateString[1] }
			})
		}
	}
	// 渲染搜索
	_renderSearchForm = () => {
		const { searchForm } = this.state;
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					<Col span={8}>
						<FormItem label={'水表编号:'}>
							<Input
								placeholder="请输入"
								value={searchForm.no}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, no: v.target.value }
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'水表厂家:'}>
							<Select
								placeholder="请选择"
								value={searchForm.manufacturer}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, manufacturer: v }
									});
								}}
							>
								{WATER_METER_TYPE.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'水表分类:'}>
							<Select
								placeholder="请选择"
								value={searchForm.type}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, type: v }
									});
								}}
							>
								{WATER_METER_TYPE.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'水表种类:'}>
							<Select
								placeholder="请选择"
								value={searchForm.kind}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, kind: v }
									});
								}}
							>
								{WATER_METER_KIND.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'详细位置:'}>
							<Input
								placeholder="请输入"
								value={searchForm.address}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, address: v.target.value }
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'安装位置分类:'}>
							<Select
								placeholder="请选择"
								value={searchForm.installLocationTypeId}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, installLocationTypeId: v }
									});
								}}
							>
								{WATER_METER_KIND.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'安装位置:'}>
							<Select
								placeholder="请选择"
								value={searchForm.installLocationName}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, installLocationName: v }
									});
								}}
							>
								{WATER_METER_KIND.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'阀门状态:'}>
							<Select
								placeholder="请选择"
								value={searchForm.valveStatus}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, valveStatus: v }
									});
								}}
							>
								{WATER_METER_KIND.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'开户状态:'}>
							<Select
								placeholder="请选择"
								value={searchForm.openStatus}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, openStatus: v }
									});
								}}
							>
								{WATER_METER_KIND.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'开户时间:'}>
							<RangePicker onChange={this.getDate} />
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'导入时间:'}>
							<RangePicker />
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'导入人:'}>
							<Select
								placeholder="请选择"
								value={searchForm.manufacturer}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, manufacturer: v }
									});
								}}
							>
								{WATER_METER_TYPE.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={16} offset={8} align="right">
						<FormItem>
							<Button type="primary" onClick={this.handleSearch}>
								搜索
							</Button>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<Button type="default" onClick={this.handleReset}>
								重置
							</Button>
						</FormItem>
					</Col>
				</Row>
			</Form>
		);
	};

	// 渲染操作按钮
	_renderOperationButton = () => {
		return (
			<Row className="search-button buttonGroup">
				<Col style={{ float: 'left' }}>
					<Button type="primary" size={'small'} onClick={this.handleBatchCloseValve}>
						批量关阀
					</Button>
					<Button type="primary" size={'small'} style={{ marginLeft: '20px' }}>
						导出列表
					</Button>
				</Col>
				<Col style={{ float: 'right' }}>
					<Button
						type="primary"
						size={'small'}
						onClick={() => {
							window.open(`${process.env.UPLOAD_ROOT}/templates/pre_opening_import_template.xlsx`);
						}}
					>
						下载模板
					</Button>
					<Upload
						headers={{
							token: localStorage.getItem('token')
						}}
						accept={`.xls,.xlsx`}
						action={`${process.env.API_ROOT}/api/wm/watermeter/preOpening`}
						onChange={info => {
							if (info.file.status === 'done') {
								if (info.file.response.code === 0) {
									message.success(info.file.response.msg);
								} else {
									message.error(info.file.response.msg);
								}
							} else if (info.file.status === 'error') {
								message.error(info.file.response.msg);
							}
						}}
					>
						<Button type="primary" size={'small'} style={{ marginLeft: '20px' }}>
							导入数据
						</Button>
					</Upload>
				</Col>
			</Row>
		);
	};

	render() {
		const { page, pageSize, columns, selectedRowKeys } = this.state;
		const { dataList, total } = this.props;

		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: total => {
				return `共 ${total} 条数据 `;
			},
			onChange: page => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};

		const rowSelection = {
			fixed: true,
			selectedRowKeys,
			onChange: this.onSelectChange
		};
		return (
			<div className="shadow-radius pre">
				<Row>
					<Col>
						<h1>预开户列表</h1>
					</Col>
				</Row>
				<Row>{this._renderSearchForm()}</Row>
				<Row>{this._renderOperationButton()}</Row>
				<Row className="main">
					<Table
						rowSelection={rowSelection}
						bordered columns={columns}
						rowKey={data => data.id}
						dataSource={dataList}
						pagination={paginationProps}
					/>
				</Row>
			</div>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('pre');
	return {
		fields: data.fields,
		dataList: data.dataList, // 列表数据
		total: data.total, // 总条数
		visible: data.visible, // 是否显示弹窗
		detail: data.detail // 获取详情
	};
};
const mapDispatchToProps = dispatch => ({
	setState: data => dispatch(actionCreators.setState(data)), // 设置redux值
	list: data => dispatch(actionCreators.list(data)), // 获取列表
	getDetail: data => dispatch(actionCreators.detail(data)), // 获取详情
	add: data => dispatch(actionCreators.add(data)), // 新增记录
	del: data => dispatch(actionCreators.del(data)), // 删除记录
	modify: data => dispatch(actionCreators.modify(data)), // 修改记录
	batchCloseValve: data => dispatch(actionCreators.batchCloseValve(data)) // 批量关阀
});
export default connect(
	mapStateToProps,
	mapDispatchToProps
)(Form.create()(Index));
