import http from '$http';
import * as actionTypes from './constants';
import { message } from 'antd';
import api from './api';
import { newUser, changeWaterMeter, xtWriteOpen, xtClearCard, qsEmptyCard, qsOpenCard, sleep } from '@/utils/cardUtil';
// 数据回填
const payload = (type, data) => ({ type, data });

// 组件操作redux数据状态
const setState = data => ({ type: actionTypes.SET_STATE, data });

// 新增
const add = (data, props) => {
	return async dispatch => {
		const res = await http.post(api.add, data);
		if (res.code === 0) {
			//开户成功后写卡
			let record = res.data;
			console.log('record: ', record);
			if (record.waterMeterType === 'IC卡表') {
				if (record.waterMeterManufacturer === '扬州恒信') {
					let cardNo = '';
					if (record.waterMeterKindType === '预付费2' || record.waterMeterKindType === '预付费4442' || record.waterMeterKindType === '阶梯2') {
						cardNo = record.cardNo;
					} else {
						cardNo = record.waterMeterNo;
					}
					let result = -1;
					if (data.accumulationAmount !== '0') {
						result = changeWaterMeter(data.areaCode, record.waterMeterKindType, cardNo, record.waterMeterNo, data.accumulationAmount, 0, 0, 0, 0, 0);
					} else {
						result = newUser(cardNo, record.waterMeterNo, record.waterMeterKindType, data.areaCode);
					}
					if (result === 0) {
						message.success('开户成功！');
						setTimeout(() => props.push('/chargeManagement/payment'), 300);
					}
				} else if (record.waterMeterManufacturer === '河南新天') {
					xtClearCard()
					message.success('开户成功！用户编号： ' + record.cno);
					// setTimeout(() => props.push('/chargeManagement/payment'), 300);
					// sleep(300)
					// let result = xtWriteOpen(record.cardNo);
					// if (result === 1) {
					// 	message.success('开户成功！');
					// 	setTimeout(() => props.push('/chargeManagement/payment'), 300);
					// }
				} else if (record.waterMeterManufacturer === '泰安') {
					qsEmptyCard().then(res => {
						if (res.errcode == 0) {
							qsOpenCard(record.cardNo).then(res1 => {
								if (res1.errcode === 0) {
									message.success('开户成功！');
									setTimeout(() => props.push('/chargeManagement/payment'), 300);
								} else {
									message.error(res1.errmsg);
								}
							});
						} else {
							message.error(res.errmsg);
						}
					});
				}
			} else {
				message.success('开户成功！');
				setTimeout(() => props.push('/chargeManagement/payment'), 300);
			}
		}
	};
};

// 用水性质列表
const waterUseKindList = data => {
	return async dispatch => {
		const res = await http.restGet(api.waterUseKindList, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.WATER_USE_KIND_RECORD, res.data));
		}
	};
};

// 用水性质列表
const customerLevelList = () => {
	return async dispatch => {
		const res = await http.get(api.customerLevelList);
		if (res.code === 0) {
			dispatch(payload(actionTypes.CUSTOMER_LEVEL_RECORD, res.data));
		}
	};
};

// 用水性质列表
const waterMeterList = data => {
	return async dispatch => {
		const res = await http.restGet(api.waterMeterList, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.WATER_METER_RECORD, res.data));
		}
	};
};

const waterMeterInfo = (data, ops) => {
	return async dispatch => {
		const res = await http.post(api.waterMeterInfo, data);
		if (res.code === 0) {
			if (!!!res.data) {
				message.error('当前水表不存在或不为库存状态');
			} else {
				dispatch(payload(actionTypes.WATER_METER_INFO_RECORD, res.data));
				if (ops) {
					ops();
				}
			}
		}
	};
};

// 获取片区选择框
const getAreaSelect = () => {
	return async dispatch => {
		const res = await http.get(api.getAreaSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_AREA_SELECT, res.data));
		}
	};
};

// 获取片区码
const getAreaCode = data => {
	return async dispatch => {
		const res = await http.restGet(api.getAreaCode, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_AREA_CODE, res.data));
		}
	};
};
export {
	setState,
	add,
	waterUseKindList,
	customerLevelList,
	waterMeterList,
	waterMeterInfo,
	getAreaSelect,
	getAreaCode
};
