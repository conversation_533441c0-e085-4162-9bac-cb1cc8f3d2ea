import React, { Component, Fragment } from 'react';
import { withRouter } from 'react-router-dom';
import { actionCreators } from './store';
import { connect } from 'react-redux';
import { constants } from '$utils';
import { getTranscriberList } from '@/utils/common';

import { WATER_METER_MANUFACTURER } from '@/constants/waterMeter';
import { Button, Col, Form, Input, Row, Radio, Select, Icon, TreeSelect, message, Tooltip } from 'antd';
import './index.scss';

const FormItem = Form.Item;
const { TextArea } = Input;
const { Option } = Select;
const Group = Radio.Group;
const RadioButton = Radio.Button;
const { TreeNode } = TreeSelect;

const formItemLayout = {
	labelCol: { span: 8 },
	wrapperCol: { span: 15 }
};

let apportionType = null; // 0-分摊比例 1-固定量

class Index extends Component {

	constructor(props) {
		super(props);
		this.state = {
			transcriberList: [],
			isCard: 'yes', // yes: 卡表  no: 费卡表
			fixedQuantity: 0, // 是否定量 - 默认为：否
			meterType: null // 0-单表 1-总表 2-分表 3-虚表
		};
	}

	componentDidMount() {
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields && isTags) {
			this.setState({
				...fields.state
			}, () => {
				this.props.form.setFieldsValue({
					...fields.form
				});
			});
		} else {
			// 获取用户等级
			this.props.customerLevelList();
			// 获取片区选择框
			this.props.getAreaSelect();
		}
	}

	componentWillUnmount() {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			});
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			});
		}
	}

	radioOnChange = e => {
		this.setState({ isCard: e.target.value },
			() => {
				this.props.form.resetFields();
				this.props.setState([{ key: 'waterMeters', value: [] }, { key: 'waterMeter', value: null }]);
			}
		);
	};

	onSubmit = e => {
		e.preventDefault();
		this.props.form.validateFields((err, values) => {
			if (!err) {
				let params = null;
				const { isCard } = this.state;
				const { waterMeter, areaCode } = this.props;
				if (isCard === 'no') {
					params = {
						name: values.name, // 用户名称
						numberOfHouse: values.numberOfHouse, // 户数
						population: values.population, // 人口数
						contact: values.contact, // 联系人
						contactPhone: values.contactPhone, // 联系电话
						idCard: values.idCard, // 身份证
						email: values.email, // 邮箱
						district: values.district, // 区
						address: values.address, // 详细地址
						meterType: values.meterType, // 水表类型：正常/总表/分表/虚表
						sumCno: values.sumCno, // 总表cno
						apportionType: values.apportionType, // 分摊类型：分摊比例/固定量
						apportionAmount: values.apportionAmount, // 分摊量
						customerLevelId: values.customerLevelId, // 用户等级
						waterMeterId: waterMeter && waterMeter.id, // 水表ID
						areaId: values.areaId, // 区域ID
						waterUseKindId: values.waterUseKindId, // 用水分类ID
						credits: values.credits, // 信用额度
						totalAmount: values.totalAmount, // 总购水量/总用水量
						accumulationAmount: values.accumulationAmount, // 累积购水量/累积用水量：记录当前表用量，换表时清空
						yearUseAmount: values.yearUseAmount, // 年度用水量
						yearBuyAmount: values.yearBuyAmount, // 年度购水量
						fixedQuantity: values.fixedQuantity, // 是否定量用户
						transcriberId: values.transcriberId,
						fixedQuantityAmount: values.fixedQuantityAmount, // 定量值
						accountBalance: values.accountBalance, // 余额
						remark: values.remark // 备注
					};
				} else {
					params = {
						name: values.name, // 用户名称
						numberOfHouse: values.numberOfHouse, // 户数
						population: values.population, // 人口数
						contact: values.contact, // 联系人
						contactPhone: values.contactPhone, // 联系电话
						idCard: values.idCard, // 身份证
						email: values.email, // 邮箱
						district: values.district, // 区
						address: values.address, // 详细地址
						meterType: 0, // 水表类型：卡表只能为单表
						fixedQuantity: 0, // 是否定量用户： 卡表不能为定量用户
						customerLevelId: values.customerLevelId, // 用户等级
						areaId: values.areaId, // 区域ID
						waterMeterId: waterMeter.id, // 水表ID
						waterUseKindId: values.waterUseKindId, // 用水分类ID
						credits: values.credits, // 信用额度
						totalAmount: values.totalAmount, // 总购水量/总用水量
						transcriberId: values.transcriberId,
						accumulationAmount: values.accumulationAmount, // 累积购水量/累积用水量：记录当前表用量，换表时清空
						yearBuyAmount: values.yearBuyAmount, // 年度购水量
						remark: values.remark, // 备注
						areaCode: areaCode
					};
				}
				console.log('params: ', params);
				this.props.add(params, this.props.history);
				this.onReset();
			}
		});
	};

	onReset = () => {
		this.props.form.resetFields();
	};

	//获取片区码
	getAreaCode(value) {
		this.props.getAreaCode(value);
	}

	//递归渲染
	_renderTreeNode = treeData => {
		return treeData.map(tree => {
			if (tree.children) {
				return (
					<TreeNode key={tree.id} title={tree.name} value={tree.id} disabled={true}>
						{this._renderTreeNode(tree.children)}
					</TreeNode>
				);
			} else {
				return <TreeNode key={tree.id} title={tree.name} value={tree.id} />;
			}
		});
	};

	renderForm = () => {
		const { isCard } = this.state;
		return (
			/* 标识符: all-卡表、非卡表 no-非卡表 yes-卡表*/
			isCard === 'yes' ?
				this.renderICForm() :
				this.renderNOICForm()
		);
	};

	renderICForm() {
		const { meterType, transcriberList } = this.state;
		const { form, waterUseKinds, customerLevels, waterMeter, areaSelect } = this.props;
		const { getFieldDecorator, setFieldsValue, getFieldValue } = form;
		return <Form {...formItemLayout}>
			<Row>
				{/* 用户名称 - all */}
				<Col span={8}>
					<FormItem label="用户名称：">
						{getFieldDecorator(`name`, {
							initialValue: '',
							rules: [
								{
									required: true,
									message: '用户名称必填'
								}
							]
						})(<Input placeholder="请输入用户名称" />)}
					</FormItem>
				</Col>
				{/* 用户地址（区） - all */}
				<Col span={8}>
					<FormItem label="用户地址（区）：">
						{getFieldDecorator(`district`, {
							initialValue: '',
							rules: [
								{
									required: true,
									message: '用户地址必填'
								}
							]
						})(<Input placeholder="请输入用户地址" />)}
					</FormItem>
				</Col>
				{/* 详细地址 - all */}
				<Col span={8}>
					<FormItem label="详细地址：">
						{getFieldDecorator(`address`, {
							initialValue: '',
							rules: [
								{
									required: true,
									message: '详细地址必填'
								}
							]
						})(<Input placeholder="请输入详细地址" />)}
					</FormItem>
				</Col>
			</Row>

			<Row>
				{/* 用水分类 - all */}
				<Col span={8}>
					<FormItem label="用水分类：">
						{getFieldDecorator(`waterUseKind`, {
							rules: [
								{
									required: true,
									message: '用水分类必选'
								}
							]
						})(
							<Select placeholder="请选择用水分类" onChange={v => this.props.waterUseKindList(v)}>
								{constants.waterQualityMap.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						)}
					</FormItem>
				</Col>
				{/* 用水性质 - all */}
				<Col span={8}>
					<FormItem label="用水性质：">
						{getFieldDecorator(`waterUseKindId`, {
							rules: [
								{
									required: true,
									message: '用水性质必选'
								}
							]
						})(
							<Select placeholder="请选择用水性质">
								{waterUseKinds.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						)}
					</FormItem>
				</Col>
				{/* 用户片区 - all */}
				<Col span={8}>
					<FormItem label="用户片区：">
						{getFieldDecorator(`areaId`, {
							initialValue: null,
							rules: [
								{
									required: true,
									message: '用户片区必填'
								}
							]
						})(
							<TreeSelect style={{ width: '100%' }} dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
								showSearch
								filterTreeNode={(inputValue, treeNode) => treeNode.props.title.indexOf(inputValue) > -1}
								placeholder="请选择片区" allowClear treeDefaultExpandedKeys={[100]}
								onChange={(v) => this.getAreaCode(v)}>
								{this._renderTreeNode(areaSelect)}
							</TreeSelect>
						)}
					</FormItem>
				</Col>
			</Row>

			<Row>
				{/* 人口数 - all */}
				<Col span={8}>
					<FormItem label="人口数：">
						{getFieldDecorator(`population`, {
							initialValue: 3,
							rules: [
								{
									required: true,
									message: '人口数必填'
								},
							]
						})(<Input placeholder="请输入人口数" suffix="人"/>)}
						{/*<Tooltip placement="top" title="人口数默认3人，不足3人按3人计算。如需要增加人口数，请通过用户管理中的人口核增功能增加。">
							<Icon type="question-circle-o" style={{ marginLeft: 5 }} />
						</Tooltip>*/}
					</FormItem>
				</Col>
				{/* 户数 - all */}
				<Col span={8}>
					<FormItem label="户数：">
						{getFieldDecorator(`numberOfHouse`, {
							initialValue: '1',
							rules: [
								{
									required: true,
									message: '户数必填'
								},
								{
									validator: function (rule, value, callback) {
										if (/\s+/.test(value)) {
											setFieldsValue({
												numberOfHouse: value.replace(/\s+/, '')
											});
											callback();
										} else {
											if (/^\d+$/.test(value)) {
												callback();
											} else {
												callback();
											}
										}
									}
								}
							]
						})(<Input placeholder="请输入户数" />)}
					</FormItem>
				</Col>
				{/* 信用额度 - all */}
				<Col span={8}>
					<FormItem label="信用额度：">
						{getFieldDecorator(`credits`, {
							initialValue: '0',
							rules: [
								{
									required: true,
									message: '信用额度必填'
								}
							]
						})(<Input placeholder="请输入信用额度" />)}
					</FormItem>
				</Col>
			</Row>

			<Row>
				{/* 联系人 - all */}
				<Col span={8}>
					<FormItem label="联系人：">
						{getFieldDecorator(`contact`, {
							initialValue: '',
							rules: []
						})(<Input placeholder="请输入联系人" />)}
					</FormItem>
				</Col>
				{/* 联系电话 - all */}
				<Col span={8}>
					<FormItem label="联系电话：">
						{getFieldDecorator(`contactPhone`, {
							initialValue: '',
							rules: [
								{
									validator: function (rule, value, callback) {
										if (value) {
											if (/\s+/.test(value)) {
												callback('不允许空格');
											}
											if (!/^1[3456789]\d{9}$/.test(value)) {
												callback('手机号码必须为11位数字');
											} else {
												callback();
											}
										} else {
											callback();
										}
									}
								}
							]
						})(<Input placeholder="请输入联系电话" />)}
					</FormItem>
				</Col>
				{/* 身份证号 - all */}
				<Col span={8}>
					<FormItem label="身份证号：">
						{getFieldDecorator(`idCard`, {
							initialValue: '',
							rules: []
						})(<Input placeholder="请输入身份证号" />)}
					</FormItem>
				</Col>
			</Row>

			<Row>
				{/* 用户等级 - all */}
				<Col span={8}>
					<FormItem label="用户等级：">
						{getFieldDecorator(`customerLevelId`, {
							rules: []
						})(
							<Select placeholder="请选择用户等级">
								{customerLevels.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						)}
					</FormItem>
				</Col>
				{/* 电子邮箱 - all */}
				<Col span={8}>
					<FormItem label="电子邮箱：">
						{getFieldDecorator(`email`, {
							initialValue: '',
							rules: [
								{
									type: 'email',
									message: '请输入正确的邮箱格式'
								}
							]
						})(<Input placeholder="请输入电子邮箱" />)}
					</FormItem>
				</Col>
				{/* 总用（购）水量 - all */}
				<Col span={8}>
					<FormItem label="总用（购）水量：">
						{getFieldDecorator(`totalAmount`, {
							initialValue: '0',
							rules: [{ required: true, message: '总用（购）水量必填' }]
						})(<Input placeholder="请输入总用（购）水量" />)}
					</FormItem>
				</Col>
			</Row>

			<Row>
				{/* 累计购水量 - yes */}
				<Col span={8}>
					<FormItem label="累计购水量：">
						{getFieldDecorator(`accumulationAmount`, {
							initialValue: '0',
							rules: [{ required: true, message: '累计购水量必填' }]
						})(<Input placeholder="请输入累计购水量" />)}
					</FormItem>
				</Col>
				{/* 年度购水量 - all */}
				<Col span={8}>
					<FormItem label="年度购水量：">
						{getFieldDecorator(`yearBuyAmount`, {
							initialValue: '0',
							rules: [
								{
									required: true,
									message: '年度购水量必填'
								}
							]
						})(<Input placeholder="请输入年度购水量" />)}
					</FormItem>
				</Col>
				{/* 水表厂家 - all */}
				{meterType !== 3 ? (
					<Col span={8}>
						<FormItem label="水表厂家：">
							{getFieldDecorator(`manufacturer`, {
								rules: [
									{
										required: true,
										message: '水表厂家必选'
									}
								]
							})(
								<Select
									showSearch
									placeholder="请选择水表厂家"
									onChange={v => {
										let waterMeterId = getFieldValue('manufacturer');
										if (waterMeterId) {
											this.props.waterMeterInfo({
												no: waterMeterId,
												manufacturer: v
											});
										}
									}}
								>
									{WATER_METER_MANUFACTURER.map((item, index) => {
										return (
											<Option key={index} value={item.value}>
												{item.label}
											</Option>
										);
									})}
								</Select>
							)}
						</FormItem>
					</Col>
				) : void (0)
				}
			</Row>

			{/* 水表信息 - all */}
			{meterType !== 3 ? (
				<Fragment>
					<Row>
						<Col span={8}>
							<FormItem label="水表编号：">
								{getFieldDecorator(`waterMeterId`, {
									rules: [
										{
											required: meterType !== 3,
											message: '水表编号必填'
										}
									]
								})(
									<Input
										placeholder="请输入水表编号"
										onBlur={v => {
											let manufacturer = getFieldValue('manufacturer');
											setFieldsValue({
												waterMeterId: v.target.value
											});
											if (manufacturer !== undefined) {
												if (v.target.value) {
													this.props.waterMeterInfo({
														no: v.target.value,
														manufacturer: manufacturer
													}, () => {
														const { waterMeter } = this.props;
														if (waterMeter) {
															setFieldsValue({ address: waterMeter.address })
															let type;
															switch (waterMeter.type) {
																case "IC卡表":
																	type = 0
																	break;
																//查询发票单价
																case "机械表":
																	type = 1
																	break;
																//用户等级
																case "远传表":
																	type = 2
																	break;
															}
															getTranscriberList(type).then(data => {
																this.setState({
																	transcriberList: data
																});
															});


														}
													});
												}
											}
										}}
										onChange={v => {
											let manufacturer = getFieldValue('manufacturer');
											if (manufacturer === undefined) {
												message.error('请先选择水表厂家！');
												v.target.value = '';
												setFieldsValue({
													waterMeterId: ''
												});
											}
										}}
									/>
								)}
							</FormItem>
						</Col>
						<Col span={8}>
							<FormItem label="水表类型：">
								<Input disabled value={waterMeter && waterMeter.type} placeholder="请先输入水表编号" />
							</FormItem>
						</Col>
						<Col span={8}>
							<FormItem label="水表种类：">
								<Input disabled value={waterMeter && waterMeter.kind} placeholder="请先输入水表编号" />
							</FormItem>
						</Col>
					</Row>
					<Row>
						<Col span={8}>
							<FormItem label="新表底码：">
								<Input disabled value={waterMeter && waterMeter.wheelBaseNumber} placeholder="请先输入水表编号" />
							</FormItem>
						</Col>
						<Col span={8}>
							<FormItem label="水表口径：">
								<Input disabled value={waterMeter && waterMeter.caliber} placeholder="请先输入水表编号" />
							</FormItem>
						</Col>
						<Col span={8}>
							<FormItem label="水表地址：">
								<Input disabled value={waterMeter && waterMeter.address} placeholder="请先输入水表地址" />
							</FormItem>
						</Col>
					</Row>
				</Fragment>
			) : void (0)}
			{/* 备注 - all */}
			{/* 供水合同编号 - all */}
			<Col span={8}>
				<FormItem label="供水合同编号：">
					{getFieldDecorator(`contractNumber`, {})(<Input placeholder="供水合同编号" />)}
				</FormItem>
			</Col>
			<Col span={8}>
				<FormItem label="备注：">
					{getFieldDecorator(`remark`, {
						initialValue: ''
					})(<TextArea placeholder="请输入备注信息" />)}
				</FormItem>
			</Col>
		</Form>;
	}

	renderNOICForm() {
		const { fixedQuantity, meterType } = this.state;
		const { form, waterUseKinds, customerLevels, waterMeter, areaSelect } = this.props;
		const { getFieldDecorator, setFieldsValue, getFieldValue } = form;
		return <Form {...formItemLayout}>
			<Row>
				{/* 用户名称 - all */}
				<Col span={8}>
					<FormItem label="用户名称：">
						{getFieldDecorator(`name`, {
							initialValue: '',
							rules: [
								{
									required: true,
									message: '用户名称必填'
								}
							]
						})(<Input placeholder="请输入用户名称" />)}
					</FormItem>
				</Col>
				{/* 用户地址（区） - all */}
				<Col span={8}>
					<FormItem label="用户地址（区）：">
						{getFieldDecorator(`district`, {
							initialValue: '',
							rules: [
								{
									required: true,
									message: '用户地址必填'
								}
							]
						})(<Input placeholder="请输入用户地址" />)}
					</FormItem>
				</Col>
				{/* 详细地址 - all */}
				<Col span={8}>
					<FormItem label="详细地址：">
						{getFieldDecorator(`address`, {
							initialValue: '',
							rules: [
								{
									required: true,
									message: '详细地址必填'
								}
							]
						})(<Input placeholder="请输入详细地址" />)}
					</FormItem>
				</Col>
			</Row>

			<Row>
				{/* 用水分类 - all */}
				<Col span={8}>
					<FormItem label="用水分类：">
						{getFieldDecorator(`waterUseKind`, {
							rules: [
								{
									required: true,
									message: '用水分类必选'
								}
							]
						})(
							<Select placeholder="请选择用水分类" onChange={v => this.props.waterUseKindList(v)}>
								{constants.waterQualityMap.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						)}
					</FormItem>
				</Col>
				{/* 用水性质 - all */}
				<Col span={8}>
					<FormItem label="用水性质：">
						{getFieldDecorator(`waterUseKindId`, {
							rules: [
								{
									required: true,
									message: '用水性质必选'
								}
							]
						})(
							<Select placeholder="请选择用水性质">
								{waterUseKinds.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						)}
					</FormItem>
				</Col>
				{/* 用户片区 - all */}
				<Col span={8}>
					<FormItem label="用户片区：">
						{getFieldDecorator(`areaId`, {
							initialValue: null,
							rules: [
								{
									required: true,
									message: '用户片区必填'
								}
							]
						})(
							<TreeSelect style={{ width: '100%' }} dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
								showSearch
								filterTreeNode={(inputValue, treeNode) => treeNode.props.title.indexOf(inputValue) > -1}
								placeholder="请选择片区" allowClear treeDefaultExpandedKeys={[100]}
								onChange={(v) => this.getAreaCode(v)}>
								{this._renderTreeNode(areaSelect)}
							</TreeSelect>
						)}
					</FormItem>
				</Col>
			</Row>

			<Row>
				{/* 人口数 - all */}
				<Col span={8}>
					<FormItem label="人口数：">
						{getFieldDecorator(`population`, {
							initialValue: 3,
							rules: [
								{
									required: true,
									message: '人口数必填'
								},
							]
						})(<Input placeholder="请输入人口数" suffix="人"/>)}
						{/*<Tooltip placement="top" title="人口数默认3人，不足3人按3人计算。如需要增加人口数，请通过用户管理中的人口核增功能增加。">
							<Icon type="question-circle-o" style={{ marginLeft: 5 }} />
						</Tooltip>*/}
					</FormItem>
				</Col>
				{/* 户数 - all */}
				<Col span={8}>
					<FormItem label="户数：">
						{getFieldDecorator(`numberOfHouse`, {
							initialValue: '1',
							rules: [
								{
									required: true,
									message: '户数必填'
								},
								{
									validator: function (rule, value, callback) {
										if (/\s+/.test(value)) {
											setFieldsValue({
												numberOfHouse: value.replace(/\s+/, '')
											});
											callback();
										} else {
											if (/^\d+$/.test(value)) {
												callback();
											} else {
												callback();
											}
										}
									}
								}
							]
						})(<Input placeholder="请输入户数" />)}
					</FormItem>
				</Col>
				{/* 信用额度 - all */}
				<Col span={8}>
					<FormItem label="信用额度：">
						{getFieldDecorator(`credits`, {
							initialValue: '0',
							rules: [
								{
									required: true,
									message: '信用额度必填'
								}
							]
						})(<Input placeholder="请输入信用额度" />)}
					</FormItem>
				</Col>
			</Row>

			<Row>
				{/* 联系人 - all */}
				<Col span={8}>
					<FormItem label="联系人：">
						{getFieldDecorator(`contact`, {
							initialValue: '',
							rules: []
						})(<Input placeholder="请输入联系人" />)}
					</FormItem>
				</Col>
				{/* 联系电话 - all */}
				<Col span={8}>
					<FormItem label="联系电话：">
						{getFieldDecorator(`contactPhone`, {
							initialValue: '',
							rules: [
								{
									validator: function (rule, value, callback) {
										if (value) {
											if (/\s+/.test(value)) {
												callback('不允许空格');
											}
											if (!/^1[3456789]\d{9}$/.test(value)) {
												callback('手机号码必须为11位数字');
											} else {
												callback();
											}
										} else {
											callback();
										}
									}
								}
							]
						})(<Input placeholder="请输入联系电话" />)}
					</FormItem>
				</Col>
				{/* 身份证号 - all */}
				<Col span={8}>
					<FormItem label="身份证号：">
						{getFieldDecorator(`idCard`, {
							initialValue: '',
							rules: []
						})(<Input placeholder="请输入身份证号" />)}
					</FormItem>
				</Col>
			</Row>

			<Row>
				{/* 用户等级 - all */}
				<Col span={8}>
					<FormItem label="用户等级：">
						{getFieldDecorator(`customerLevelId`, {
							rules: [
								{
									required: true,
									message: '用户等级必选'
								}
							]
						})(
							<Select placeholder="请选择用户等级">
								{customerLevels.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						)}
					</FormItem>
				</Col>
				{/* 电子邮箱 - all */}
				<Col span={8}>
					<FormItem label="电子邮箱：">
						{getFieldDecorator(`email`, {
							initialValue: '',
							rules: [
								{
									type: 'email',
									message: '请输入正确的邮箱格式'
								}
							]
						})(<Input placeholder="请输入电子邮箱" />)}
					</FormItem>
				</Col>
				{/* 总用（购）水量 - all */}
				<Col span={8}>
					<FormItem label="总用（购）水量：">
						{getFieldDecorator(`totalAmount`, {
							initialValue: '0',
							rules: [
								{
									required: true,
									message: '总用（购）水量必填'
								}
							]
						})(<Input placeholder="请输入总用（购）水量" />)}
					</FormItem>
				</Col>
			</Row>

			<Row>
				{/* 账户余额 - no */}
				<Col span={8}>
					<FormItem label="账户余额：">
						{getFieldDecorator(`accountBalance`, {
							initialValue: '0',
							rules: [{ required: true, message: '账户余额必填' }]
						})
							(<Input placeholder="请输入账户余额" />)}
					</FormItem>
				</Col>
				{/* 是否定量 - no */}
				<Col span={8}>
					<FormItem label="是否定量：">
						{getFieldDecorator(`fixedQuantity`, {
							initialValue: fixedQuantity,
							rules: [{ required: meterType !== 3, message: '是否定量必选' }]
						})
							(<Select placeholder="请选择是否定量"
								onChange={v => {
									this.setState({ fixedQuantity: v }, () => {
										setFieldsValue({ meterType: undefined, waterMeterType: undefined });
									});
								}}>
								{constants.fixedQuantity.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
							)}
					</FormItem>
				</Col>
				{/* 定量值 */}
				<Col span={8}>
					<FormItem label="定量值：">
						{getFieldDecorator(`fixedQuantityAmount`, {
							initialValue: '',
							rules: [
								{
									required: meterType !== 3 && fixedQuantity === 1,
									message: '定量值必填'
								}
							]
						})(
							<Select>
								{constants.fixedQuantitySelect.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>

						)}
					</FormItem>
				</Col>
			</Row>

			<Row>
				<Col span={8}>
					<FormItem label="水表类型：">
						{getFieldDecorator(`meterType`, {
							rules: [
								{
									required: true,
									message: '水表类型必选'
								}
							]
						})(
							<Select
								placeholder="请选择水表类型"
								onChange={v => {
									this.setState({
										meterType: v
									});
								}}
							>
								{constants.meterType.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						)}
					</FormItem>
				</Col>
				<Col span={8}>
					<FormItem label="年度用水量：">
						{getFieldDecorator(`yearUseAmount`, {
							initialValue: '0',
							rules: [
								{
									required: true,
									message: '年度用水量必填'
								}
							]
						})(<Input placeholder="请输入年度用水量" />)}
					</FormItem>
				</Col>
				{/* 年度购水量 - all */}
				<Col span={8}>
					<FormItem label="年度购水量：">
						{getFieldDecorator(`yearBuyAmount`, {
							initialValue: '0',
							rules: [
								{
									required: true,
									message: '年度购水量必填'
								}
							]
						})(<Input placeholder="请输入年度购水量" />)}
					</FormItem>
				</Col>
			</Row>

			<Row>
				{/* 水表厂家 - all */}
				{meterType !== 3 ? (
					<Col span={8}>
						<FormItem label="水表厂家：">
							{getFieldDecorator(`manufacturer`, {
								rules: [
									{
										required: true,
										message: '水表厂家必选'
									}
								]
							})(
								<Select
									showSearch
									placeholder="请选择水表厂家"
									onChange={v => {
										let waterMeterId = getFieldValue('manufacturer');
										if (waterMeterId) {
											this.props.waterMeterInfo({
												no: waterMeterId,
												manufacturer: v
											});
										}
									}}
								>
									{WATER_METER_MANUFACTURER.map((item, index) => {
										return (
											<Option key={index} value={item.value}>
												{item.label}
											</Option>
										);
									})}
								</Select>
							)}
						</FormItem>
					</Col>
				) : void (0)}
				{/* 总表编号 - no */}
				<Col span={8}>
					<FormItem label="总表用户编号：">
						{getFieldDecorator(`sumCno`, {
							initialValue: '',
							rules: [{ required: meterType === 2 || meterType === 3 ? true : false, message: '请输入总表用户编号' }]
						})(<Input placeholder="请输入总表用户编号" disabled={meterType === 2 || meterType === 3 ? false : true} />)}
					</FormItem>
				</Col>
				{/* 供水合同编号 - all */}
				<Col span={8}>
					<FormItem label="供水合同编号：">
						{getFieldDecorator(`contractNumber`, {})(<Input placeholder="供水合同编号" />)}
					</FormItem>
				</Col>
			</Row>

			{/* 水表信息 - all */}
			{meterType !== 3 ? (
				<Fragment>
					<Row>
						<Col span={8}>
							<FormItem label="水表编号：">
								{getFieldDecorator(`waterMeterId`, {
									rules: [
										{
											required: meterType !== 3,
											message: '水表编号必填'
										}
									]
								})(
									<Input
										placeholder="请输入水表编号"
										onBlur={v => {
											let manufacturer = getFieldValue('manufacturer');
											if (manufacturer !== undefined) {
												if (v.target.value) {
													this.props.waterMeterInfo({
														no: v.target.value,
														manufacturer: manufacturer
													}, () => {
														const { waterMeter } = this.props;
														if (waterMeter) {
															if (waterMeter.address) {
																setFieldsValue({ address: waterMeter.address })
															}
															let type;
															switch (waterMeter.type) {
																case "IC卡表":
																	type = 0
																	break;
																//查询发票单价
																case "机械表":
																	type = 1
																	break;
																//用户等级
																case "远传表":
																	type = 2
																	break;
															}
															getTranscriberList(type).then(data => {
																this.setState({
																	transcriberList: data
																});
															});


														}
													});
												}
											}
										}}
										onChange={v => {
											let manufacturer = getFieldValue('manufacturer');
											if (manufacturer === undefined) {
												message.error('请先选择水表厂家！');
												v.target.value = '';
												setFieldsValue({
													waterMeterId: ''
												});
											}
										}}
									/>
								)}
							</FormItem>




						</Col>
						<Col span={8}>
							<FormItem label="水表分类：">
								<Input disabled value={waterMeter && waterMeter.type} placeholder="请先输入水表编号" />
							</FormItem>
						</Col>
						<Col span={8}>
							<FormItem label="水表种类：">
								<Input disabled value={waterMeter && waterMeter.kind} placeholder="请先输入水表编号" />
							</FormItem>
						</Col>
					</Row>
					<Row>
						<Col span={8}>
							<FormItem label="新表底码：">
								<Input disabled value={waterMeter && waterMeter.wheelBaseNumber} placeholder="请先输入水表编号" />
							</FormItem>
						</Col>
						<Col span={8}>
							<FormItem label="水表口径：">
								<Input disabled value={waterMeter && waterMeter.caliber} placeholder="请先输入水表编号" />
							</FormItem>
						</Col>
						<Col span={8}>
							<FormItem label="水表地址：">
								<Input disabled value={waterMeter && waterMeter.address} placeholder="请先输入水表地址" />
							</FormItem>
						</Col>
					</Row>
				</Fragment>
			) : void (0)}

			{/* 分摊方式 - no */}
			{meterType === 3 ? (
				<Col span={8}>
					<FormItem label="分摊方式：">
						{getFieldDecorator(`apportionType`, {
							rules: [
								{
									required: true,
									message: '水表类型必选'
								}
							]
						})(
							<Select
								placeholder="请选择分摊方式"
								onChange={v => {
									apportionType = v;
									setFieldsValue({
										apportionAmount: ''
									});
								}}
							>
								{constants.apportionType.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						)}
					</FormItem>
				</Col>
			) : void (0)}

			{/* 分摊值 - no */}
			{meterType === 3 ?
				<Col span={8}>
					<FormItem label="分摊值：">
						{getFieldDecorator(`apportionAmount`, {
							initialValue: '',
							rules: [
								{
									required: true,
									message: '分摊值必填'
								},
								{
									validator: function (rule, value, callback) {
										if (/\s+/.test(value)) {
											setFieldsValue({
												apportionAmount: value.replace(/\s+/, '')
											});
											callback();
										} else {
											// 分摊比例
											if (apportionType === 0) {
												if (/^0\.?\d{0,2}$/.test(value)) {
													if (`${value}` === `00` || `${value}` === `0.00`) {
														setFieldsValue({
															apportionAmount: '0.'
														});
													}
													callback();
												} else {
													setFieldsValue({
														apportionAmount: `${value}`.substr(0, `${value}`.length - 1)
													});
													callback();
												}
											} else {
												// 固定量
												if (/^\d+$/.test(value)) {
													callback();
												} else {
													setFieldsValue({
														apportionAmount: `${value}`.substr(0, `${value}`.length - 1)
													});
													callback('必须输入整数');
												}
											}
											callback();
										}
									}
								}
							]
						})(<Input disabled={!apportionType && !(apportionType === 0)}
							placeholder={!apportionType && !(apportionType === 0) ? '请先选择分摊方式' : '请输入分摊值'}
							style={{ width: '90%' }} />)}
						<Tooltip placement="top" title="选择分摊方式为分摊比例时，只能输入格式为[0.xx]的数据。">
							<Icon type="question-circle-o" style={{ marginLeft: 5 }} />
						</Tooltip>
					</FormItem>
				</Col> : void (0)}
			{/* 备注 - all */}
			<Col span={8}>
				<FormItem label="备注：">
					{getFieldDecorator(`remark`, {
						initialValue: '',
						rules: []
					})(<TextArea placeholder="请输入备注信息" />)}
				</FormItem>
			</Col>
		</Form>;
	}

	render() {
		const { isCard } = this.state;
		return (
			<div className="shadow-radius user">
				<h1>用户开户</h1>
				<Row>
					<Col align="center">
						<Group className="radioGroup" defaultValue={isCard} value={isCard} buttonStyle="solid"
							onChange={this.radioOnChange}>
							<RadioButton value="yes">卡表</RadioButton>
							<RadioButton value="no">非卡表</RadioButton>
						</Group>
					</Col>
				</Row>
				<Row className="account">{this.renderForm()}</Row>
				<Row>
					<Col align="center">
						<Button className="searchBtn" type="primary" onClick={this.onSubmit}>
							提交
						</Button>
						<Button className="searchBtn" type="default" onClick={this.onReset}>
							重置
						</Button>
					</Col>
				</Row>
			</div>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('account');
	return {
		datalist: data.datalist, // 列表数据
		total: data.total, // 总条数
		visible: data.visible, // 是否显示弹窗
		detail: data.detail, // 获取详情
		waterUseKinds: data.waterUseKinds, // 用水性质列表
		customerLevels: data.customerLevels, // 用户等级
		waterMeters: data.waterMeters, // 水表列表
		waterMeter: data.waterMeter, // 水表信息
		areaSelect: data.areaSelect, // 片区选择框
		fields: data.fields, //
		areaCode: data.areaCode   //片区码
	};
};
const mapDispatchToProps = dispatch => ({
	setState: data => dispatch(actionCreators.setState(data)), // 设置redux值
	add: (data, props) => dispatch(actionCreators.add(data, props)), // 新增记录
	waterUseKindList: data => dispatch(actionCreators.waterUseKindList(data)), // 用水性质列表
	customerLevelList: () => dispatch(actionCreators.customerLevelList()), // 用户等级
	waterMeterList: data => dispatch(actionCreators.waterMeterList(data)), // 水表列表
	waterMeterInfo: (data, ops) => dispatch(actionCreators.waterMeterInfo(data, ops)), // 水表信息
	getAreaSelect: () => dispatch(actionCreators.getAreaSelect()), //获取片区选择框
	getAreaCode: data => dispatch(actionCreators.getAreaCode(data))  //获取片区码
});
export default connect(
	mapStateToProps,
	mapDispatchToProps
)(Form.create()(withRouter(Index)));
