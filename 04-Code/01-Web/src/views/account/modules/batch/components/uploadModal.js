import React, { Component, Fragment } from 'react';
import { actionCreators } from '../store';
import { connect } from 'react-redux';
import { Button, Col, Form, Input, Row, Icon, Upload, Modal } from 'antd';
import { constants } from '$utils';

const FormItem = Form.Item;

class UploadModal extends Component {

	constructor(props) {
		super(props);
		this.state = {
			file: null,
			count: 0
		};
	};

	//上传导入
	handleClick() {
		const { file } = this.state;
		const { form, list } = this.props;
		form.validateFields((err, values) => {
			if (!err) {
				this.props.setState({ key: 'loading', value: true });
				let formData = new FormData();
				formData.append('file', file);
				formData.append('projectNo', values.projectNo);
				formData.append('reason', values.reason);
				//file以外的对象拼接
				this.props.batchImport(formData, list);
			}
		});

	}

	//编辑弹窗
	editModal(visible) {
		this.props.setState({ key: 'visible', value: visible });
		this.setState({ count: 0 });
	}

	// 拦截文件上传
	beforeUploadHandle = (file) => {
		const { count } = this.state;
		let newCount = count + 1;
		if (newCount < 2) {
			this.setState({ file: file, count: newCount });
			return false;
		} else {
			Modal.error({ title: '上传的文件不能超过1个' });
		}
	};

	render() {
		const { form, visible, loading } = this.props;
		const { getFieldDecorator } = form;
		const footer = (
			<Fragment>
				<Button key="submit" type="primary" loading={loading} onClick={() => this.handleClick()}>提交</Button>
				<Button key="back" onClick={() => this.editModal(false)}>取消</Button>
			</Fragment>
		);
		return (
			<Fragment>
				<Button type="primary" size='middle' onClick={() => this.editModal(true)}>批量开户导入</Button>

				<Modal title="批量开户" destroyOnClose={true} maskClosable={true} visible={visible}
							 onCancel={() => this.editModal(false)}
							 footer={footer}>
					<Form {...constants.formItemLayout}>
						<Row>
							<Col>
								<FormItem label={'工程项目编号:'}>
									{getFieldDecorator('projectNo', { rules: [{ required: true, message: '请输入工程项目编号' }] })
									(<Input placeholder="请输入"/>)}
								</FormItem>
							</Col>
							<Col>
								<FormItem label={'立户原因:'}>
									{getFieldDecorator('reason', { rules: [{ required: true, message: '请输入立户原因' }] })
									(<Input placeholder="请输入"/>)}
								</FormItem>
							</Col>
							<Col>
								<FormItem label={'上传文件:'}>
									{getFieldDecorator('file', { rules: [{ required: true, message: '请选择上传文件' }] })(
										<Upload name="files" accept={`.xlsx`} beforeUpload={this.beforeUploadHandle}>
											<Button><Icon type="upload"/> 点击上传文件</Button>
										</Upload>
									)}
								</FormItem>
							</Col>
						</Row>
					</Form>
				</Modal>
			</Fragment>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('batch');
	return {
		visible: data.visible, // 是否显示弹窗
		loading: data.loading // 加载
	};
};
const mapDispatchToProps = dispatch => ({
	setState: data => dispatch(actionCreators.setState(data)), // 设置redux值
	batchImport: (data, list) => dispatch(actionCreators.batchImport(data, list)) // 批量开户
});
export default connect(mapStateToProps, mapDispatchToProps)(Form.create()(UploadModal));
