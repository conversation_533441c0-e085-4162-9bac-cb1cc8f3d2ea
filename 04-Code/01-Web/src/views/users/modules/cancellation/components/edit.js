import React, { Component } from 'react';
import { actionCreators } from '../store';
import { connect } from 'react-redux';
import { InputNumber, Descriptions, Spin, Modal, Table, Form, Select, Row, Col, Input, Button, DatePicker, Icon } from 'antd'
import qs from 'qs';
import moment from 'moment';

const FormItem = Form.Item
const { Option } = Select
const ButtonGroup = Button.Group
const { RangePicker } = DatePicker
const { TextArea } = Input;
const { Column, ColumnGroup } = Table;


class ViewDialog extends Component {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      loading: false,
    };
  }




  // 打开弹窗
  handleOpen = () => {
    const { record } = this.props;
    this.props.getUserInfo(record.customerId);
    this.setState({
      visible: true,
    })
  }


  // 取消弹窗
  handleCancel = () => {
    this.setState({
      visible: false,
    })
  };


  //设置加载状态
  setLoading = (type) => {
    if (type == 0) {
      this.setState({
        loading: true
      })
    }
    else if (type == 1) {
      this.setState({
        loading: false
      })
    }
  }

  // 提交
  handleSubmit = () => {
    const { record } = this.props;
    this.props.form.validateFields((err, values) => {
      if (!err) {
        // this.setLoading(0);
        values.dismantleTime = moment(values._d).format('YYYY-MM-DD');
        values.id = record.id;
        this.props.backFill(values, this.handleCancel, () => { this.setLoading(1)},()=>{this.props.handleSearch()});
      }
    });
  }


  render() {
    const { visible, loading } = this.state;
    const { form, userInfo, record } = this.props;
    const { getFieldDecorator, setFieldsValue } = form;
    const formItemLayout = {
      labelCol: {
        xs: { span: 6 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 12 },
      },
    };
    return (
      <>
        <Button
          title='回填'
          className='btn'
          type="primary"
          size="small"
          icon="form"
          onClick={this.handleOpen}
        />
        <Modal
          className="payModal"
          title="销户回填"
          centered={true}
          visible={visible}
          onCancel={this.handleCancel}
          footer={null}
          destroyOnClose={true}
        >
          <Spin spinning={loading} tip="Loading...">
            <Form {...formItemLayout}>
              <Row>
                <Descriptions title="用户信息" bordered>
                  <Descriptions.Item label="用户名称">{userInfo && userInfo.name}</Descriptions.Item>
                  <Descriptions.Item label="用户编号">{userInfo && userInfo.cno}</Descriptions.Item>
                  <Descriptions.Item label="人口数">{userInfo && userInfo.population}</Descriptions.Item>
                  <Descriptions.Item label="联系人">{userInfo && userInfo.contact}</Descriptions.Item>
                  <Descriptions.Item label="联系电话">{userInfo && userInfo.contactPhone}</Descriptions.Item>
                  <Descriptions.Item label="身份证号">{userInfo && userInfo.idCard}</Descriptions.Item>
                  <Descriptions.Item label="片区">{userInfo && userInfo.areaName}</Descriptions.Item>
                  <Descriptions.Item label="水表类型">{userInfo && userInfo.meterType}</Descriptions.Item>
                  <Descriptions.Item label="客户类型">{userInfo && userInfo.customerType}</Descriptions.Item>
                  <Descriptions.Item label="水表编号">{userInfo && userInfo.waterMeterNo}</Descriptions.Item>
                  <Descriptions.Item label="水表分类">{userInfo && userInfo.waterMeterType}</Descriptions.Item>
                  <Descriptions.Item label="水表种类">{userInfo && userInfo.waterMeterKindType}</Descriptions.Item>
                  <Descriptions.Item label="安装位置">{userInfo && userInfo.installLocation}</Descriptions.Item>
                  <Descriptions.Item label="水表厂家">{userInfo && userInfo.waterMeterManufacturer}</Descriptions.Item>
                  <Descriptions.Item label="当前字轮读数">{userInfo && userInfo.wheelPresentNumber}</Descriptions.Item>
                </Descriptions>,
            </Row>
              <Row>
                <Col span={12}>
                  <FormItem label="最新读数:">
                    {
                      getFieldDecorator('currentMeterNumber', {
                        rules: [{
                          required: true,
                          message: '必填',
                        }]
                      })(<InputNumber
                        min={0}
                        style={{ width: 200 }}
                      />)
                    }
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem label="拆表时间:">
                    {
                      getFieldDecorator('dismantleTime', {
                        rules: [{
                          required: true,
                          message: '必填',
                        }]
                      })(
                        <DatePicker
                          style={{ width: 200 }}
                        />
                      )
                    }
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem label="拆表员:">
                    {
                      getFieldDecorator('maintenancePersonName', {
                        rules: [{
                          required: true,
                          message: '必填',
                        }]
                      })(
                        <Input style={{ width: 200 }} />
                      )
                    }
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem label="旧表状态:">
                    {
                      getFieldDecorator('oldMeterStatus', {
                        rules: [{
                          required: true,
                          message: '必填',
                        }]
                      })(
                        <Select style={{ width: 200 }}>
                          <Option key={3} value={3}>
                            返修
                        </Option>
                          <Option key={4} value={4}>
                            废弃
                        </Option>
                          <Option key={5} value={5}>
                            失窃
                        </Option>
                        </Select>
                      )
                    }
                  </FormItem>
                </Col>
                <Col span={24} pull={3}>
                  <FormItem label="备注:">
                    {
                      getFieldDecorator('remark')(
                        <TextArea row={5} />
                      )
                    }
                  </FormItem>
                </Col>
              </Row>
            </Form>
            <Row style={{ marginTop: 10 }}>
              <Col span={24} align="center">
                <Button type="primary" className="btn" onClick={this.handleSubmit}>提交</Button>
                <Button className="btn" onClick={this.handleCancel}>关闭</Button>
              </Col>
            </Row>
          </Spin>
        </Modal>

      </>
    );
  }
}

const mapStateToProps = state => {
  const data = state.get('cancellation');
  return {
    userInfo: data.userInfo, // 详情
  }
};
const mapDispatchToProps = dispatch => ({
  getUserInfo: (data) => dispatch(actionCreators.getUserInfo(data)), // 查看详情
  backFill: (data, cancel, setLoad,getList) => dispatch(actionCreators.backFill(data, cancel, setLoad,getList)), // 回填
});
export default connect(
  mapStateToProps,
  mapDispatchToProps
)((Form.create())(ViewDialog));
