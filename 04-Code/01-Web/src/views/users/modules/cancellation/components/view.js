import React, { Component } from 'react';
import { actionCreators } from '../store';
import { connect } from 'react-redux';
import { Spin, Modal, Table, Form, Select, Row, Col, Input, Button, DatePicker, Icon } from 'antd'
import qs from 'qs';

const FormItem = Form.Item
const { Option } = Select
const ButtonGroup = Button.Group
const { RangePicker } = DatePicker
const { TextArea } = Input;
const { Column, ColumnGroup } = Table;


class ViewDialog extends Component {
  constructor(props) {
    super(props);
    this.state = {
      expand: false, // 搜索项过多的时候，隐藏部分
      page: 1,
      pageSize: 10,
      visible: false,
      customerList: [],
      columns: [
        {
          title: '序号',
          dataIndex: 'xuhao',
          key: 'xuhao',
          align: 'center',
          render: (text, record, index) => {
            return index + 1
          }
        },
        {
          title: '用户编号',
          dataIndex: 'cno',
          key: 'cno',
          align: 'center',
        },
        {
          title: '用户名称',
          dataIndex: 'name',
          key: 'name',
          align: 'center',
        },

        {
          title: '用户片区',
          dataIndex: 'areaName',
          key: 'areaName',
          align: 'center',
        },
        {
          title: '用户地址',
          dataIndex: 'address',
          key: 'address',
          align: 'center',
        },
        {
          title: '用水性质',
          dataIndex: 'waterUseKindName',
          key: 'waterUseKindName',
          align: 'center',
        },
        {
          title: '水表编号',
          dataIndex: 'waterMeterNo',
          key: 'waterMeterNo',
          align: 'center',
        },
        {
          title: '水表类型',
          dataIndex: 'waterMeterType',
          key: 'waterMeterType',
          align: 'center',
        },
        {
          title: '水表种类',
          dataIndex: 'waterMeterKindType',
          key: 'waterMeterKindType',
          align: 'center',
        },
        {
          title: '用户状态',
          dataIndex: 'status',
          key: 'status',
          align: 'center',
        },
        {
          title: '用户余额',
          dataIndex: 'accountBalance',
          key: 'accountBalance',
          align: 'center',
        },
        {
          title: '销户原因',
          dataIndex: 'reason',
          key: 'reason',
          align: 'center',
        },
      ],
    };
  }


  // 打开弹窗
  handleOpen = () => {
    const { record } = this.props;
    this.props.viewDetail(record.id);
    this.setState({ visible: true })
  }


  // 取消弹窗
  handleCancel = () => {
    this.setState({ visible: false })
  };


  // 列表分页
  handlePageChange = (page) => {
    this.setState(
      {
        page
      },
      () => {
        this.getList()
      }
    )
  }

  // 改变分页条数
  pageSizeChange = (current, size) => {
    this.setState(
      {
        page: 1,
        pageSize: size
      },
      () => {
        this.getList()
      }
    )
  }



  render() {
    const { visible } = this.state;
    const { form, canDetail } = this.props;
    const { getFieldDecorator } = form;
    const formItemLayout = {
      labelCol: {
        xs: { span: 6 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 12 },
      },
    };
    return (
      <>
        <Button
          title='查看'
          className='btn'
          type="primary"
          size="small"
          icon="eye"
          onClick={this.handleOpen}
        />
        <Modal
          className="payModal"
          title="销户详情"
          centered={true}
          visible={visible}
          onCancel={this.handleCancel}
          footer={null}
          destroyOnClose={true}
        >
          <Form {...formItemLayout}>
            <Row>
              <Col span={8}>
                <FormItem label="当前字轮读数:">
                  <Input disabled value={canDetail && canDetail.wheelPresentNumber}/>
                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem label="最新读数:">
                  <Input disabled  value={canDetail && canDetail.currentMeterNumber}/>
                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem label="拆表员:">
                  <Input disabled value={canDetail && canDetail.maintenancePersonName}/>
                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem label="拆表时间:">
                  <Input disabled  value={canDetail && canDetail.dismantleTime}/>
                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem label="旧表状态:">
                  <Input disabled  value={canDetail && canDetail.oldMeterStatus}/>
                </FormItem>
              </Col>
              <Col span={24} pull={4}>
                <FormItem label="备注:">
                  <Input disabled  value={canDetail && canDetail.remark}/>
                </FormItem>
              </Col>
              <Col span={24} pull={4}>
                <FormItem label="附件:">

                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem label="退款金额:">
                  <Input disabled  value={canDetail && canDetail.refundMoney}/>
                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem label="退款人:">
                  <Input disabled  value={canDetail && canDetail.refundUid}/>
                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem label="退款时间:">
                  <Input disabled  value={canDetail && canDetail.refundTime}/>
                </FormItem>
              </Col>
            </Row>
          </Form>
          <Row style={{ marginTop: 10 }}>
            <Col span={24} align="center">
              <Button className="btn" onClick={this.handleCancel}>关闭</Button>
            </Col>
          </Row>
        </Modal>

      </>
    );
  }
}

const mapStateToProps = state => {
  const data = state.get('cancellation');
  return {
    canDetail: data.canDetail, // 详情
  }
};
const mapDispatchToProps = dispatch => ({
  viewDetail: (data) => dispatch(actionCreators.viewDetail(data)), // 查看详情
});
export default connect(
  mapStateToProps,
  mapDispatchToProps
)((Form.create())(ViewDialog));
