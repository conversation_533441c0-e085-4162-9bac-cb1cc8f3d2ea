import React, { Component } from 'react';
import { actionCreators } from './store';
import { connect } from 'react-redux';
import http from '$http';
import { InputNumber, TreeSelect, Modal, Table, Form, Select, Row, Col, Input, Button, DatePicker, Icon, Tag } from 'antd';
import { constants } from '$utils';
import View from './components/view';
import Print from './components/print';
import Edit from './components/edit';
import Comfirm from './components/comfirm';
import './index.scss';
import { cancellationStatusColorMap, applyStatusColorMap } from '@/constants/colorStyle';
import { APPLY_STATUS } from '@/constants/process';

const FormItem = Form.Item;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TreeNode } = TreeSelect;

const defaultSearchForm = {};

class Index extends Component {

	constructor(props) {
		super(props);
		this.state = {
			expand: false, // 搜索项过多的时候，隐藏部分
			page: 1,
			pageSize: 10,
			searchForm: defaultSearchForm,
			startTime: null,
			endTime: null
		};
	}

	componentDidMount() {
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields && isTags) {
			this.setState({
				...fields.state
			}, () => {
				this.props.form.setFieldsValue({
					...fields.form
				});
			});
		} else {
			this.getList();
			this.props.getAreaSelect();
			this.props.getWaterUseKindSelect();
		}
	}

	componentWillUnmount() {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			});
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			});
		}
	}

	// 获取列表
	getList = () => {
		const { page, pageSize, startTime, endTime } = this.state;
		const { form } = this.props;
		const { getFieldsValue } = form;
		let values = getFieldsValue();
		values.page = page;
		values.pageSize = pageSize;
		values.startTime = startTime;
		values.endTime = endTime;
		delete values.rangPicker;
		this.props.list(values);
	};

	// 列表分页
	handlePageChange = (page) => {
		this.setState({ page }, () => {
			this.getList();
		});
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState({ page: 1, pageSize: size }, () => {
			this.getList();
		});
	};

	// 搜索功能
	handleSearch = () => {
		this.setState({ page: 1 }, () => {
			this.getList();
		});
	};

	// 重置搜索
	handleReset = () => {
		const { form } = this.props;
		const { resetFields } = form;
		resetFields();
		this.getList();
	};

	// 显示高级搜索
	showMoreSearch = () => {
		this.setState({
			expand: !this.state.expand
		});
	};

	getOrderDate = (date, dateString) => {
		if (dateString.length === 2) {
			this.setState({
				startTime: dateString[0], endTime: dateString[1]
			});
		}
	};

	//导出
	export() {
		const { page, pageSize, startTime, endTime } = this.state;
		const { form } = this.props;
		const { getFieldsValue } = form;
		let values = getFieldsValue();
		values.page = page;
		values.pageSize = pageSize;
		values.startTime = startTime;
		values.endTime = endTime;
		delete values.rangPicker;
		let url = `${process.env.API_ROOT}/api/cm/customer/cancelDetail/exportCustomerBatchCancelDetail`;
		http.export(url, values, (res) => {
			const blob = new Blob([res]);
			const downloadElement = document.createElement('a');
			const href = window.URL.createObjectURL(blob);
			const fileName = '销户记录_' + new Date().getTime() + '.xlsx';
			downloadElement.href = href;
			downloadElement.download = fileName;
			document.body.appendChild(downloadElement);
			downloadElement.click();
			document.body.removeChild(downloadElement);
			window.URL.revokeObjectURL(href);
		});
	}

	// 渲染搜索
	_renderSearchForm = () => {
		const { form, areaSelect, waterUseKindSelect } = this.props;
		const { getFieldDecorator } = form;
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					<Col span={8}>
						<FormItem label="用户编号:">
							{
								getFieldDecorator('cno')
									(<Input />)
							}
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label="用户名称:">
							{
								getFieldDecorator('name')
									(<Input />)
							}
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label="片区:">
							{
								getFieldDecorator('areaId')
									(<TreeSelect
										style={{ width: '100%' }}
										dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
										placeholder="请选择片区"
										allowClear
										treeDefaultExpandedKeys={[100]}
									>
										{
											areaSelect && this._renderTreeNode(areaSelect)
										}
									</TreeSelect>)
							}
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={8}>
						<FormItem label="用户地址:">
							{
								getFieldDecorator('address')
									(<Input />)
							}
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label="用水性质:">
							{
								getFieldDecorator('type')
									(
										<Select>
											{
												constants.waterQualityMap.map((item, index) => {
													return (
														<Option key={index} value={item.value}>
															{item.label}
														</Option>
													);
												})
											}
										</Select>
									)
							}
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label="用水分类:">
							{
								getFieldDecorator('waterUseKindType')
									(
										<Select>
											{waterUseKindSelect.map((item, index) => {
												return (
													<Option key={index} value={item.value}>
														{item.label}
													</Option>
												);
											})}
										</Select>
									)
							}
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={8}>
						<FormItem label="水表厂家:">
							{
								getFieldDecorator('manufacturer')
									(
										<Select>
											{constants.waterMeterManufacturer.map((item, index) => {
												return (
													<Option key={index} value={item.value}>
														{item.label}
													</Option>
												);
											})}
										</Select>
									)
							}
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label="水表编号:">
							{
								getFieldDecorator('no')
									(<Input />)
							}
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label="水表分类:">
							{
								getFieldDecorator('waterType')
									(
										<Select>
											{constants.waterMeterType.map((item, index) => {
												return (
													<Option key={index} value={item.value}>
														{item.label}
													</Option>
												);
											})}
										</Select>
									)
							}
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={8}>
						<FormItem label="水表种类:">
							{
								getFieldDecorator('kind')
									(
										<Select>
											{constants.waterMeterKinds.map((item, index) => {
												return (
													<Option key={index} value={item.value}>
														{item.label}
													</Option>
												);
											})}
										</Select>
									)
							}
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label="状态:">
							{
								getFieldDecorator('status')
									(
										<Select>
											<Option key={0} value={0}>
												待销户
										</Option>
											<Option key={1} value={1}>
												销户
										</Option>
										</Select>
									)
							}
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label="申请状态:">
							{
								getFieldDecorator('applyStatus')
									(
										<Select>
											{
												APPLY_STATUS.map((item, index) => {
													return <Option key={index} value={item.value}>{item.label}</Option>;
												})
											}
										</Select>
									)
							}
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={8}>
						<FormItem label={'用户余额:'}>
							{
								getFieldDecorator('accountBalanceStart')
									(<InputNumber
										step={1}
									/>)
							}
							<span>&emsp;~&emsp;</span>
							{
								getFieldDecorator('accountBalanceEnd')
									(<InputNumber
										step={1}
									/>)
							}
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label="销户原因:">
							{
								getFieldDecorator('reason')
									(<Input />)
							}
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'申请时间:'}>
							{getFieldDecorator('rangPicker', { rules: [{ type: 'array' }] })(
								<RangePicker onChange={this.getOrderDate} placeholder={['开始时间', '结束时间']} />
							)}
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={24} align="right">
						<Button type="primary" onClick={this.handleSearch}>
							搜索
						</Button>
						&nbsp;&nbsp;&nbsp;&nbsp;
						<Button type="default" onClick={this.handleReset}>
							重置
						</Button>
					</Col>
				</Row>
			</Form>
		);
	};

	//递归渲染片区选项
	_renderTreeNode = treeData => {
		return treeData.map(tree => {
			if (tree.children) {
				return (
					<TreeNode key={tree.id} title={tree.name} value={tree.id}>
						{this._renderTreeNode(tree.children)}
					</TreeNode>
				);
			} else {
				return <TreeNode key={tree.id} title={tree.name} value={tree.id} />;
			}
		});
	};

	render() {
		const { page, pageSize } = this.state;
		const { form, dataList, total, areaSelect, waterUseKindSelect } = this.props;
		const columns = [
			{
				title: '序号',
				dataIndex: 'xuhao',
				width: 80,
				align: 'center',
				render: (text, record, index) => {
					return index + 1;
				}
			},
			{
				title: '用户编号',
				dataIndex: 'cno',
				key: 'cno',
				align: 'center'
			},
			{
				title: '用户名称',
				dataIndex: 'name',
				key: 'name',
				align: 'center'
			},
			{
				title: '片区',
				dataIndex: 'areaName',
				key: 'areaName',
				align: 'center'
			},
			{
				title: '用户地址',
				dataIndex: 'address',
				key: 'address',
				align: 'center'
			},
			{
				title: '用水性质',
				dataIndex: 'type',
				key: 'type',
				align: 'center'
			},
			{
				title: '用水分类',
				dataIndex: 'waterUseKindType',
				key: 'waterUseKindType',
				align: 'center'
			},
			{
				title: '水表编号',
				dataIndex: 'no',
				key: 'no',
				align: 'center'
			},
			{
				title: '水表厂家',
				dataIndex: 'manufacturer',
				key: 'no',
				align: 'center'
			},
			{
				title: '水表类型',
				dataIndex: 'waterType',
				key: 'waterType',
				align: 'center'
			},
			{
				title: '水表种类',
				dataIndex: 'kind',
				key: 'kind',
				align: 'center'
			},
			{
				title: '用户余额',
				dataIndex: 'accountBalance',
				key: 'accountBalance',
				align: 'center'
			},
			{
				title: '销户原因',
				dataIndex: 'reason',
				key: 'reason',
				align: 'center'
			},
			{
				title: '申请时间',
				dataIndex: 'createTime',
				key: 'reason',
				align: 'center'
			},
			{
				title: '状态',
				dataIndex: 'customerStatus',
				key: 'customerStatus',
				width: 80,
				align: 'center',
				fixed: 'right',
				render: text => {
					return (
						<Tag color={cancellationStatusColorMap.get(text)}>
							{text}
						</Tag>
					);
				}
			},
			{
				title: '申请状态',
				dataIndex: 'applyStatus',
				key: 'applyStatus',
				width: 80,
				align: 'center',
				fixed: 'right',
				render: text => {
					return (
						<Tag color={applyStatusColorMap.get(text)}>
							{text}
						</Tag>
					);
				}
			},
			{
				title: '操作',
				key: 'operation',
				width: 200,
				align: 'center',
				fixed: 'right',
				render: (text, record, index) => {
					return (
						<span>
							<View record={record} />
							{
								record.applyStatus === '待回填' ?
									<Print record={record} />
									:
									<Button title='打印' className='btn' type="primary" size="small" icon="printer" disabled />
							}

							{
								record.customerStatus === '待销户' && record.applyStatus === '待回填' ?
									<Edit record={record} handleSearch={this.getList} />
									:
									<Button title='回填' className='btn' type="primary" size="small" icon="form" disabled />
							}
							{
								record.customerStatus === '待销户' && record.applyStatus === '已完成' ?
									<Comfirm record={record} handleSearch={this.getList} />
									:
									<Button title='确认销户' className='btn' type="primary" size="small" icon="check-circle" disabled />
							}
						</span>
					);
				}
			}
		];
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: (total) => {
				return `共 ${total} 条数据 `;
			},
			onChange: (page) => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		return (
			<div className="shadow-radius cancellation">
				<h1>销户记录</h1>
				{this._renderSearchForm()}
				<Button className="searchBtn" type="primary" onClick={() => this.export()}><Icon
					type="download" />导出销户记录</Button>
				<Row className='main'>
					<Table
						bordered
						columns={columns}
						rowKey={(record) => record.id}
						dataSource={dataList}
						pagination={paginationProps}
						scroll={{ x: 2800 }} />
				</Row>
			</div>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('cancellation');
	return {
		fields: data.fields,
		dataList: data.dataList, // 列表数据
		total: data.total, // 总条数
		areaSelect: data.areaSelect, // 是否显示弹窗
		detail: data.detail, // 获取详情
		waterUseKindSelect: data.waterUseKindSelect // 用水分类选择框
	};
};
const mapDispatchToProps = dispatch => ({
	setState: data => dispatch(actionCreators.setState(data)), // 设置redux值
	list: data => dispatch(actionCreators.list(data)), // 获取列表
	getDetail: data => dispatch(actionCreators.detail(data)), // 获取详情
	getAreaSelect: data => dispatch(actionCreators.getAreaSelect(data)), // 获取详情
	getWaterUseKindSelect: () => dispatch(actionCreators.getWaterUseKindSelect()) //获取用水分类选择框
});
export default connect(
	mapStateToProps,
	mapDispatchToProps
)((Form.create())(Index));
