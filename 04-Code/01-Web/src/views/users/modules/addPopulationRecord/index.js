import React, { Component } from 'react';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import { actionCreators } from './store';
import http from '$http';
import { Table, Form, Select, Row, Col, Input, Button, DatePicker, Icon, Tag } from 'antd';
import './index.scss';
import { constants } from '$utils';
import {message, Modal} from "antd/lib/index";
import { APPLY_STATUS } from '@/constants/process';
import { applyStatusColorMap } from '@/constants/colorStyle'
import {TreeSelect} from "antd/lib/index";
const FormItem = Form.Item;
const { Option } = Select;
const { RangePicker } = DatePicker;

const { TreeNode } = TreeSelect;

const defaultSearchForm = {
	cno: '', // 用户编号
	applyUid: null, // 申请人
	applyTimeStart: null, // 申请时间开始
	applyTimeEnd: null, // 申请时间结束
	applyStatus: [],  // 申请状态
	effectiveTimeStart: null,  // 生效时间开始
	effectiveTimeEnd: null, // 生效时间结束
	areaId: null,
};

class Index extends Component {

	constructor(props) {
		super(props);
		this.state = {
			pageSize: 10, // 页条数
			page: 1, // 页码
			selectedRowKeys: [], // 已选择
			searchForm: defaultSearchForm,
			applyRangeStart: null, 
			applyRangeEnd: null, 
			effectiveRangeStart: null, 
			effectiveRangeEnd: null
		};
	}

	componentDidMount() {
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields && isTags) {
			this.setState({
				...fields.state
			}, () => {
				this.props.form.setFieldsValue({
					...fields.form
				});
			});
		} else {
			this.getList();
			this.props.areaList();
			this.props.listApplyUserSelect();
		}
	}

	componentWillUnmount() {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			});
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			});
		}
	}

	// 获取数据
	getList = () => {
		const { page, pageSize, searchForm } = this.state;
		let params = {
			page,
			pageSize,
			...searchForm
		};
		this.props.list(params);
	};

	// 搜索功能
	handleSearch = () => {
		this.setState({
			page: 1
		}, () => {
			this.getList();
			this.setState({ selectedRowKeys: [] });
		});
	};

	// 重置搜索条件
	handleReset = () => {
		this.setState(
			{
				page: 1,
				searchForm: Object.assign({}, defaultSearchForm),
				applyRangeStart: null, 
				applyRangeEnd: null, 
				effectiveRangeStart: null, 
				effectiveRangeEnd: null
			},
			() => {
				// this.getList();
				this.setState({ selectedRowKeys: [] });

			}
		);
	};

	// 列表分页
	handlePageChange = (page) => {
		this.setState({
			page
		}, () => {
			this.getList();
		});
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState({
			page: 1,
			pageSize: size
		}, () => {
			this.getList();
		});
	};

	// 时间查询条件
	// 日期选择
	getDate = (date, dateString, startName, endName) => {
		if (dateString.length === 2) {
			var object = {}
			object[startName] = dateString[0]
			object[endName] = dateString[1]
			this.setState({
				searchForm: { ...this.state.searchForm, ...object }
			});

			if(startName === 'applyTimeStart'){
				this.setState({ applyRangeStart: date[0], applyRangeEnd: date[1] })
			}else if(startName === 'effectiveTimeStart'){
				this.setState({ effectiveRangeStart: date[0], effectiveRangeEnd: date[1] })
			}
		}
	};

	// 渲染片区树
	_renderTreeLoop = (areas, isShow) => {
		return areas.map(item => {
			if (item.children) {
				return (
					<TreeNode key={item.id} title={item.name} value={item.id} disabled={isShow}>
						{this._renderTreeLoop(item.children, isShow)}
					</TreeNode>
				);
			} else {
				return <TreeNode key={item.id} title={item.name} value={item.id} />;
			}
		});
	};

  // 渲染搜索
	_renderSearchForm = () => {
		const { searchForm, applyRangeStart, applyRangeEnd, effectiveRangeStart, effectiveRangeEnd } = this.state;
		const { areaId } = searchForm;
		const { applyUsers, areas } = this.props;
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					{/* 用户编号 */}
					<Col span={8}>
						<FormItem label={'用户编号:'}>
							<Input
								placeholder="请输入用户编号"
								value={searchForm.cno}
								onChange={v => {
									const { searchForm } = this.state;
									this.setState({
										searchForm: {
											...searchForm,
											cno: v.target.value
										}
									});
								}}
							/>
						</FormItem>
					</Col>
					{/* 申请人 */}
					<Col span={8}>
						<FormItem label={'申请人:'}>
							<Select
								placeholder="请选择"
								value={searchForm.applyUid}
								onChange={v => {
									const { searchForm } = this.state;
									this.setState({
										searchForm: {
											...searchForm,
											applyUid: v
										}
									});
								}}
							>
								<Option value={null}>全部</Option>
								{
									applyUsers.map((item, index) => {
										return <Option key={index} value={item.value}>{item.label}</Option>;
									})
								}
							</Select>
						</FormItem>
					</Col>
					{/* 申请时间 */}
					<Col span={8}>
						<FormItem label={'申请时间:'}>
							<RangePicker value={[ applyRangeStart, applyRangeEnd ]} onChange={(d, ds) => {this.getDate(d, ds, 'applyTimeStart', 'applyTimeEnd')}} />
						</FormItem>
					</Col>
				</Row>
				<Row>
					{/* 申请状态 */}
					<Col span={8}>
						<FormItem label={'申请状态:'}>
							<Select
								mode="multiple"
								showSearch
								placeholder="请选择"
								value={searchForm.applyStatus}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, applyStatus: v } });
								}} showArrow>
								{APPLY_STATUS.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					{/* 生效时间 */}
					<Col span={8}>
						<FormItem label={'生效时间:'}>
							<RangePicker value={[ effectiveRangeStart, effectiveRangeEnd ]} onChange={(d, ds) => {this.getDate(d, ds, 'effectiveTimeStart', 'effectiveTimeEnd')}} />
						</FormItem>
					</Col>
					{/* 片区 */}
					<Col span={8}>
						<FormItem label={'所属片区:'}>
							<TreeSelect
								style={{ width: '100%' }}
								value={areaId}
								showSearch
								filterTreeNode={(inputValue, treeNode) => treeNode.props.title.indexOf(inputValue) > -1}
								dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
								placeholder="请选择所属片区"
								allowClear
								treeDefaultExpandedKeys={[100]}
								onChange={value => {
									this.setState({
										searchForm: {
											...searchForm,
											areaId: value
										}
									});
								}}
							>
								{this._renderTreeLoop(areas, false)}
							</TreeSelect>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={24} align="right">
						<Button className="searchBtn" type="primary" onClick={this.handleSearch}>
							搜索
						</Button>
						<Button className="searchBtn" type="default" onClick={this.handleReset}>
							重置
						</Button>
					</Col>
				</Row>
			</Form>
		);
	};

  //列表复选框
	onSelectChange = (selectedRowKeys, selectedRows) => {
		this.setState({ selectedRowKeys, selectedRows });
	};

  //导出
	handleDownload = () => {
		const { searchForm, selectedRowKeys } = this.state;
		let param = { ids: [] };
		if (selectedRowKeys.length > 0) {
			selectedRowKeys.map(item => {
				param.ids.push(item);
			});
		} else {
			param = searchForm;
		}
		const url = `${process.env.API_ROOT}/api/cm/customer/population/change/exportExcel`;

		http.export(url, param, (res) => {
			const blob = new Blob([res]);
			const downloadElement = document.createElement('a');
			const href = window.URL.createObjectURL(blob);
			const fileName = '人口核增记录_' + new Date().getTime() + '.xlsx';
			downloadElement.href = href;
			downloadElement.download = fileName;
			document.body.appendChild(downloadElement);
			downloadElement.click();
			document.body.removeChild(downloadElement);
			window.URL.revokeObjectURL(href);
		});
	};

	render() {
		const { page, pageSize, selectedRowKeys } = this.state;

		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: this.props.total,
			showTotal: (total) => {
				return `共 ${total} 条数据 `;
			},
			onChange: (page) => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		const columns = [
			{
				title: '用户编号',
				dataIndex: 'cno',
				width: 100,
				align: 'center'
			},
			{
				title: '用户名称',
				dataIndex: 'customerName',
				width: 100,
				align: 'center'
			},
			{
				title: '用户地址',
				dataIndex: 'address',
				width: 200,
				align: 'center'
			},
			{
				title: '片区',
				dataIndex: 'areaName',
				width: 100,
				align: 'center'
			},
			{
				title: '原人口',
				dataIndex: 'oldPopulation',
				width: 100,
				align: 'center'
			},
			{
				title: '现人口',
				dataIndex: 'newPopulation',
				width: 100,
				align: 'center',
			},
			{
				title: '申请人',
				dataIndex: 'applyUserName',
				width: 100,
				align: 'center'
			},
			{
				title: '申请时间',
				dataIndex: 'applyTime',
				width: 150,
				align: 'center'
			},
			{
				title: '生效时间',
				dataIndex: 'effectiveTime',
				width: 150,
				align: 'center',
			},
			{
				title: '申请状态',
				dataIndex: 'applyStatus',
				width: 100,
				fixed: 'right',
				align: 'center',
				render: text => {
					return (
						<Tag color={applyStatusColorMap.get(text)}>
							{text}
						</Tag>
					);
				}
			}
		];
		const rowSelection = {
			fixed: true,
			selectedRowKeys,
			onChange: this.onSelectChange
		};
		return (
			<div className="shadow-radius message">
				{/* 页面标题 - 搜索条件 */}
				<h1>人口核增记录</h1>
				<Row>{this._renderSearchForm()}</Row>
				<Row className="search-button buttonGroup" style={{ marginBottom: 10 }}>
					<Button type="primary" className="btn" onClick={this.handleDownload}>
						导出
					</Button>
				</Row>
				<Row className='main'>
					<Table
						bordered
						columns={columns}
						rowKey={(record) => record.id}
						dataSource={this.props.datalist}
						pagination={paginationProps}
						rowSelection={rowSelection}
					/>
				</Row>
			</div>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('addPopulationRecord');
	return {
		fields: data.fields,
		datalist: data.datalist,
		total: data.total,
		applyUsers: data.applyUsers, // 历史申请人
		areas: data.areas, // 片区
	};
};
const mapDispatchToProps = dispatch => ({
	setState: (data) => dispatch(actionCreators.setState(data)), // 设置redux值
	list: data => dispatch(actionCreators.list(data)),
	listApplyUserSelect: data => dispatch(actionCreators.listApplyUserSelect(data)), // 历史创建人
	areaList: data => dispatch(actionCreators.areaList()), // 获取片区树
});
export default connect(
	mapStateToProps,
	mapDispatchToProps
)((Form.create())(withRouter(Index)));
