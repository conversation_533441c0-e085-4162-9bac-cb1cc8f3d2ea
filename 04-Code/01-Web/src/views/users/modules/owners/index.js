import React, {Component, Fragment} from 'react';
import { actionCreators } from './store';
import { connect } from 'react-redux';
import http from '$http';
import moment from 'moment';
import { Spin, Upload, Icon, Tag, Button, Col, DatePicker, Form, Input, Row, Select, Table, Modal, Divider, message } from 'antd';
import Img from 'react-zmage';
import { constants, getBase64,unique } from '$utils';
import { APPLY_STATUS_BY_OTHER } from '@/constants/order';
import AssociatedUsers from '$components/associatedUsers';
import { applyStatusColorMap, ownStatusColorMap } from '@/constants/colorStyle';
import NoData from '$components/NoData';

import './index.scss';
import {TreeSelect} from "antd/lib/index";
const { TreeNode } = TreeSelect;
const FormItem = Form.Item;
const { Option } = Select;
const { RangePicker } = DatePicker;
let isEdit = false;
let isDone = false;

const formItemLayout = {
	labelCol: { span: 6 },
	wrapperCol: { span: 15 }
};

class Index extends Component {
	constructor(props) {
		super(props);
		this.state = {
			show: false,
			previewVisible: false, // 查看大图控制器
			previewImage: '',
			fileList: [],
			searchForm: {
				idCard: '',
				personName: '',
				phone: '',
				status: null,
				cno: '',
				applyStatus: null,
				areaId: null
			},
			columns: [
				{
					title: '产权人姓名',
					dataIndex: 'personName',
					key: 'personName',
					align: 'center'
				},
				{
					title: '产权人电话',
					dataIndex: 'phone',
					key: 'phone',
					align: 'center'
				},
				{
					title: '产权人身份证号',
					dataIndex: 'idCard',
					key: 'idCard',
					align: 'center'
				},
				{
					title: '申请状态',
					dataIndex: 'applyStatus',
					key: 'applyStatus',
					align: 'center',
					render: text => {
						return <Tag color={applyStatusColorMap.get(text)}>{text}</Tag>;
					}
				},

				{
					title: '创建时间',
					dataIndex: 'createTime',
					key: 'createTime',
					align: 'center'
				},
				{
					title: '操作员',
					dataIndex: 'createPersonName',
					key: 'createPersonName',
					align: 'center'
				},

				{
					title: '状态',
					dataIndex: 'status',
					key: 'status',
					align: 'center',
					fixed:'right',
					width:80,
					render: text => {
						return <Tag color={ownStatusColorMap.get(text)}>{text}</Tag>;
					}
				},
				{
					title: '操作',
					key: 'operation',
					width: 200,
					align: 'center',
					fixed: 'right',
					render: (text, record, index) => {
						return (
							<span>
								<Button title="查看" className="btn" type="primary" size="small" icon="eye" onClick={() => this.handleView(record)} />
								<Button title="编辑" className="btn" type="primary" size="small" icon="form" disabled={record.applyStatus !== '已保存' && record.applyStatus !== '已拒绝' && record.applyStatus !== '已完成'} onClick={() => this.handleEdit(record)} />
								<Button title="删除" className="btn" type="primary" size="small" icon="delete"  disabled={record.applyStatus !== '已完成'} onClick={() => this.handleDel(record)} />
							</span>
						);
					}
				}
			],
			page: 1,
			pageSize: 10,
			userColumns: [
				{
					title: '用户编号',
					dataIndex: 'cno',
					key: 'cno',
					align: 'center'
				},
				{
					title: '户号',
					dataIndex: 'hno',
					key: 'hno',
					align: 'center'
				},
				{
					title: '用户名称',
					dataIndex: 'name',
					key: 'name',
					align: 'center'
				},
				{
					title: '用户地址',
					dataIndex: 'address',
					align: 'center',
					render: (text, record, index) => {
						return `${record.province}${record.city}${record.district}${record.address}`;
					}
				},
				{
					title: '水表类型',
					dataIndex: 'waterMeterType',
					key: 'waterMeterType',
					align: 'center'
				},
				{
					title: '水表种类',
					dataIndex: 'waterMeterKindType',
					key: 'waterMeterKindType',
					align: 'center'
				}
			], // 关联用户table
			customerList: [], // 关联用户列表
			customerId: null // 关联用户ID
		};
	}

	componentDidMount() {
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields && isTags) {
			this.setState(
				{
					...fields.state
				},
				() => {
					this.props.form.setFieldsValue({
						...fields.form
					});
				}
			);
		} else {
			this.props.areaList(); // 获取片区树
			this.props.waterUseKind();
			this.getList();
		}
	}

	componentWillUnmount() {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			});
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			});
		}
	}

	componentWillReceiveProps(nextProps, nextContext) {
		if (nextProps.detail && !isDone) {
			isDone = true;
			let fileList = [];
			const data = nextProps.detail;
			if (isEdit) {
				this.props.form.setFieldsValue({
					personName: data.personName,
					phone: data.phone,
					idCard: data.idCard,
					status: data.status
				});
			}
			data.annexList.forEach(item => {
				fileList.push({
					uid: item.id,
					id: item.id,
					createTime: item.createTime,
					updateTime: item.updateTime,
					relatedId: item.relatedId,
					relatedCode: item.relatedCode,
					originName: item.originName,
					newName: item.newName,
					filePath: item.filePath,
					name: item.originName,
					status: 'done',
					url: `${constants.fileUrl}${item.filePath}${item.newName}`
				});
			});
			this.setState({
				fileList
			});
		}
	}

	// 获取列表
	getList = () => {
		const { page, pageSize, searchForm } = this.state;
		this.props.list(Object.assign(searchForm, { page, pageSize }));
	};

	// 搜索
	handleSearch = () => {
		this.setState(
			{
				page: 1
			},
			() => this.getList()
		);
	};

	// 重置
	handleReset = () => {
		this.setState(
			{
				page: 1,
				searchForm: {
					idCard: '',
					personName: '',
					phone: '',
					status: null,
					cno: '',
					applyStatus: '',
					areaId: null,
				}
			},
			() => {
				this.getList();
			}
		);
	};

	// 列表分页
	handlePageChange = page => {
		this.setState(
			{
				page
			},
			() => {
				this.getList();
			}
		);
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState(
			{
				page: 1,
				pageSize: size
			},
			() => {
				this.getList();
			}
		);
	};
	// 新增
	handleAdd = () => {
		isEdit = false;
		this.props.setState({ key: 'visible1', value: true });
	};
	// 查看详情
	handleView = record => {
		isEdit = false;
		isDone = false;
		this.props.getDetail(record.id,this.setTalbeData);
		this.props.setState({ key: 'visible', value: true });
	};
	// 编辑记录
	handleEdit = record => {
		isEdit = true;
		isDone = false;
		this.props.getDetail(record.id,this.setTalbeData);
		this.props.setState({ key: 'visible1', value: true });
	};

	// 删除记录
	handleDel = record => {
		const _this = this;
		Modal.confirm({
			title: '操作确认',
			content: '是否确认删除该记录？',
			okText: '确定',
			cancelText: '取消',
			onOk() {
				_this.props.del(record.id, _this.getList);
			}
		});
	};
	// 提交新增资料
	handleSubmit = e => {
		e.preventDefault();
		this.props.form.validateFields((err, values) => {
			if (!err) {
				const { fileList, customerList } = this.state;
				const { personName, phone, idCard } = values;
				let uploadList = fileList.map(item => {
					if (item.response) {
						return item.response.data;
					} else {
						return item;
					}
				});
				let params = {
					personName,
					phone,
					idCard,
					uploadList,
					customerList: customerList,
					operationType: 1
				};
				if (isEdit) {
					params = {
						...params,
						id: this.props.detail.id
					};
					this.props.modify(params, this.getList);
					isEdit = false;
				} else {
					if (!customerList) {
						message.error('请选择关联用户');
						return false;
					}
					this.props.add(params, this.getList);
				}
				this.props.form.resetFields();
				this.setState({
					fileList: [],
					customerList: []
				});
			}
		});
	};
	// 关闭弹窗
	handleCancel = () => {
		this.setState({
			fileList: [],
			customerList: []
		});
		this.props.setState([
			{ key: 'visible', value: false },
			{ key: 'detail', value: null }
		]);
	};
	// 关闭新增弹窗
	handleCancelModal = () => {
		this.setState({
			fileList: [],
			customerList: []
		});
		this.props.setState([
			{ key: 'visible1', value: false },
			{ key: 'detail', value: null }
		]);
	};
	// 获取用户列表
	userList = params => {
		this.props.userList(params);
	};
	// 选择关联用户
	handleChoseUser = () => {
		this.userList({
			hno: '',
			cno: '',
			name: '',
			address: '',
			page: 1,
			pageSize: 10
		});
		this.setState({
			show: true
		});
	};
	// 关闭关联用户
	handleClose = () => {
		this.setState({
			show: false
		});
	};
	// 查看大图
	handlePreviewFile = async file => {
		if (!file.url && !file.preview) {
			file.preview = await getBase64(file.originFileObj);
		}
		this.setState({
			previewImage: file.url || file.preview,
			previewVisible: true
		});
	};
	// 变更图片
	handleChangeFile = ({ file, fileList, event }) => {
		this.setState({ fileList });
		// 如果上传文件失败 或者 错误， 则删除该上传文件
		if (file.status === 'error') {
			message.error('上传失败，请稍后重试');
			this.setState({ fileList: fileList.slice(0, -1) });
		}
	};
	// 取消查看大图
	handleCancelPreview = e => {
		e.preventDefault();
		this.setState({ previewVisible: false });
	};
		// 获取关联用户数据
		getAssociatedUser = record => {
				const { customerList } = this.state;
				let cnoList=[];
			for (let item of customerList) {
				if(record.findIndex(o => o.id == item.id)!=-1){
					cnoList.push(item.cno);
					record.splice(record.findIndex(o => o.id == item.id),1)
				}
			}
			if(cnoList.length!=0){
				message.error(cnoList+'已在列表中');
			}

			this.setState({
						show: false,
						customerList: [ ...customerList,...record ],
				});
		};

	// table 行选择回调
	onSelectChange = (selectedRowKeys, selectedRows) => {
		this.setState({ selectedRowKeys, selectedRows });
	};

		// table 行选择回调
		setTalbeData = (data) => {
				const { customerList } = this.state;
				const a=[...customerList,...data.customerList];

				this.setState({ customerList: a});
		};

	handleDelUser=() => {
			const { selectedRowKeys, customerList } = this.state;
			let newCustomerList = customerList.filter(item => !selectedRowKeys.includes(item.id));
			this.setState({ customerList: newCustomerList, selectedRowKeys: [] });
	};
	//导出
	export() {
		const { searchForm } = this.state;
		let url = `${process.env.API_ROOT}/api/cm/customer/property/exportCustomerProperty`;
		http.export(url, searchForm, res => {
			const blob = new Blob([res]);
			const downloadElement = document.createElement('a');
			const href = window.URL.createObjectURL(blob);
			const fileName = '产权人信息_' + new Date().getTime() + '.xlsx';
			downloadElement.href = href;
			downloadElement.download = fileName;
			document.body.appendChild(downloadElement);
			downloadElement.click();
			document.body.removeChild(downloadElement);
			window.URL.revokeObjectURL(href);
		});
	}
	// 渲染片区树
	_renderTreeLoop = (areas, isShow) => {
		return areas.map(item => {
			if (item.children) {
				return (
					<TreeNode key={item.id} title={item.name} value={item.id} disabled={isShow}>
						{this._renderTreeLoop(item.children, isShow)}
					</TreeNode>
				);
			} else {
				return <TreeNode key={item.id} title={item.name} value={item.id} />;
			}
		});
	};
	// 渲染搜索
	_renderSearchForm = () => {
		const { searchForm } = this.state;
		const { personName, phone, idCard, cno, status, applyStatus, areaId } = searchForm;
		const { areas } = this.props;
		const { getFieldDecorator } = this.props.form;
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					<Col span={8}>
						<FormItem label="产权人姓名">
							<Input
								value={personName}
								placeholder="请输入"
								onChange={val => {
									const { searchForm } = this.state;
									searchForm.personName = val.target.value;
									this.setState({
										...searchForm
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label="产权人电话">
							<Input
								value={phone}
								placeholder="请输入"
								onChange={val => {
									const { searchForm } = this.state;
									searchForm.phone = val.target.value;
									this.setState({
										...searchForm
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label="产权人身份证号">
							<Input
								value={idCard}
								placeholder="请输入"
								onChange={val => {
									const { searchForm } = this.state;
									searchForm.idCard = val.target.value;
									this.setState({
										...searchForm
									});
								}}
							/>
						</FormItem>
					</Col>
				</Row>

				<Row>
					<Col span={8}>
						<FormItem label="用户编号">
							<Input
								value={cno}
								placeholder="请输入"
								onChange={val => {
									const { searchForm } = this.state;
									searchForm.cno = val.target.value;
									this.setState({
										...searchForm
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label="状态">
							<Select
								placeholder="请选择"
								value={searchForm.status}
								onChange={val => {
									const { searchForm } = this.state;
									searchForm.status = val;
									this.setState({
										...searchForm
									});
								}}
							>
								{constants.ownerStatus.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label="申请状态">
							<Select
								placeholder="请选择"
								value={searchForm.applyStatus}
								onChange={val => {
									const { searchForm } = this.state;
									searchForm.applyStatus = val;
									this.setState({
										...searchForm
									});
								}}
							>
								{APPLY_STATUS_BY_OTHER.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={8}>
						<FormItem label={'所属片区:'}>
							<TreeSelect
								style={{ width: '100%' }}
								value={areaId}
								showSearch
								filterTreeNode={(inputValue, treeNode) => treeNode.props.title.indexOf(inputValue) > -1}
								dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
								placeholder="请选择所属片区"
								allowClear
								treeDefaultExpandedKeys={[100]}
								onChange={value => {
									this.setState({
										searchForm: {
											...searchForm,
											areaId: value
										}
									});
								}}
							>
								{this._renderTreeLoop(areas, false)}
							</TreeSelect>
						</FormItem>
					</Col>
				</Row>
				<Col span={16} align="right">
					<Button className="searchBtn" type="primary" onClick={this.handleSearch}>
						搜索
					</Button>
					<Button className="searchBtn" type="default" onClick={this.handleReset}>
						重置
					</Button>
				</Col>
			</Form>
		);
	};

	_renderOperateButton = () => {
		return (
			<Col>
				<Button type="primary" onClick={this.handleAdd}>
					新增
				</Button>
				<Button className="searchBtn" type="primary" onClick={() => this.export()}>
					<Icon type="download" />
					导出产权人信息
				</Button>
			</Col>
		);
	};



	render() {
		const { page, pageSize, columns, previewVisible, previewImage, fileList, show, userColumns, customerList ,selectedRowKeys} = this.state;
		const { datalist, total, visible, visible1, form, waterUseKinds, detail, users } = this.props;
		const { getFieldDecorator, getFieldValue } = form;

		const rowSelection = {
			fixed: true,
			selectedRowKeys,
			onChange: this.onSelectChange
		};

		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: total => {
				return `共 ${total} 条数据 `;
			},
			onChange: page => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};

		const uploadButton = (
			<div>
				<Icon type="plus" />
				<div className="ant-upload-text">上传照片</div>
			</div>
		);

		return (
			<div className="shadow-radius poor">
				{/* 页面标题 */}
				<Row>
					<Col>
						<h1>产权人信息</h1>
					</Col>
				</Row>
				<Row>{this._renderSearchForm()}</Row>
				{/*功能入口*/}
				<Row>{this._renderOperateButton()}</Row>
				<Row className="main">
					<Table bordered scroll={{ x: 2300 }} rowKey={() => Math.random()} columns={columns} dataSource={datalist} pagination={paginationProps} />
				</Row>
				{/* 查看详情 */}
				{!isEdit && detail ? (
					<Modal className="poorDetail" title="产权人详情" destroyOnClose={true} visible={visible} onCancel={this.handleCancel} footer={null}>
						<Row className="detail">
							<Col span={8}>
								<b>姓名：</b>
								{detail.personName}
							</Col>
							<Col span={8}>
								<b>联系电话：</b>
								{detail.phone}
							</Col>
							<Col span={8}>
								<b>身份证号：</b>
								{detail.idCard}
							</Col>
							<Col span={8}>
								<b>状态：</b>
								{detail.status}
							</Col>
							<Divider dashed orientation="left">
								照片信息
							</Divider>
							{detail && fileList.length > 0 ? (
								fileList.map((item, index) => {
									return (
										<Col key={index} className="photoInfo" span={8} align="center">
											<Img src={item.url} alt="" />
										</Col>
									);
								})
							) : (
								<NoData text="暂无照片信息" />
							)}
						</Row>
						<Divider dashed orientation="left">
							关联用户
						</Divider>
						<Table bordered
									 pagination={false} rowKey={() => Math.random()} columns={userColumns} dataSource={detail.customerList} size={'small'} />
						<Row>
							<Col align="center">
								<Button type="default" onClick={this.handleCancel}>
									关闭
								</Button>
							</Col>
						</Row>
					</Modal>
				) : null}

				<Modal className="poorDetail" title={`${isEdit ? '编辑产权人信息' : '新增产权人'}`} destroyOnClose={true} visible={visible1} onCancel={this.handleCancelModal} footer={null} destroyOnClose={true}>
					<Spin spinning={isEdit && !detail}>
						<Form {...formItemLayout}>
							{/*姓名*/}
							<Col span={8}>
								<FormItem label="姓名：">
									{getFieldDecorator('personName', {
										rules: [
											{
												required: true,
												message: '姓名必填'
											}
										]
									})(<Input disabled={detail && detail.applyStatus !== '已保存' && detail.applyStatus !== '已拒绝'} placeholder="请输入姓名" />)}
								</FormItem>
							</Col>
							{/*地址*/}
							<Col span={8}>
								<FormItem label="联系电话：">
									{getFieldDecorator('phone', {
										rules: [
											{
												required: true,
												message: '联系电话必填'
											}
										]
									})(<Input disabled={detail && detail.applyStatus !== '已保存' && detail.applyStatus !== '已拒绝' && detail.applyStatus !== '已完成'} placeholder="请输入联系电话" maxLength={15} />)}
								</FormItem>
							</Col>
							{/*身份证号*/}
							<Col span={8}>
								<FormItem label="身份证号：">
									{getFieldDecorator('idCard', {
										rules: [
											{
												required: true,
												message: '身份证号必填'
											}
										]
									})(<Input disabled={detail && detail.applyStatus !== '已保存' && detail.applyStatus !== '已拒绝'} placeholder="请输入身份证号" maxLength={18} />)}
								</FormItem>
							</Col>
							<Divider dashed orientation="left">
								附件
							</Divider>
							{/*附件*/}
							<Upload
								headers={{
									token: localStorage.getItem('token')
								}}
								accept={`.jpg,.png,.jpeg,.bmp,.gif`}
								action={`${process.env.API_ROOT}/api/upload/file/customerProperty`}
								listType="picture-card"
								fileList={fileList}
								onPreview={this.handlePreviewFile}
								onChange={this.handleChangeFile}
							>
								{fileList.length >= 5 ? null : uploadButton}
							</Upload>
							<Divider dashed orientation="left">
								关联用户
							</Divider>
							{detail && detail.applyStatus !== '已保存' && detail.applyStatus !== '已拒绝' ? (<div>
								<Button type='primary' className='addUserBtn' onClick={this.handleChoseUser}>新增</Button>
								<Button type='primary' className='addUserBtn' onClick={this.handleDelUser}>批量删除</Button>
							</div>) : (
								<Button type="primary" className="addUserBtn" onClick={this.handleChoseUser}>
									选择关联用户
								</Button>
							)}
							{/*关联用户*/}

							<Table bordered pagination={false}  	rowKey={(record) => record.id}
										 rowSelection={rowSelection} columns={userColumns} dataSource={	customerList} size={'small'} />
							<Col span={24} align="center">
								{detail ? (
									detail.applyStatus === '已保存' || detail.applyStatus === '已拒绝' || detail.applyStatus === '已完成' ? (
										<Button type="danger" onClick={this.handleSubmit}>
											提交
										</Button>
									) : null
								) : (
									<Button type="danger" onClick={this.handleSubmit}>
										提交
									</Button>
								)}

								<Button type="default" onClick={this.handleCancelModal}>
									取消
								</Button>
							</Col>
						</Form>
					</Spin>
				</Modal>
				<Modal visible={previewVisible} footer={null} onCancel={this.handleCancelPreview}>
					<img alt="example" style={{ width: '100%' }} src={previewImage} />
				</Modal>
				{show&&<AssociatedUsers radio={true} visible={show} data={users} onCancel={this.handleClose} onSubmit={this.getAssociatedUser} handleList={this.userList} />}
			</div>
		);
	}
}



const mapStateToProps = state => {
	const data = state.get('owners');
	return {
		fields: data.fields,
		datalist: data.datalist, // 列表数据
		total: data.total, // 总条数
		visible: data.visible, // 是否显示弹窗
		visible1: data.visible1, // 是否显示弹窗
		detail: data.detail, // 获取详情
		waterUseKinds: data.waterUseKind,
		users: data.users, // 用户列表
		areas: data.areas,  // 片区树
	};
};
const mapDispatchToProps = dispatch => ({
	setState: data => dispatch(actionCreators.setState(data)), // 设置redux值
	list: data => dispatch(actionCreators.list(data)), // 获取列表
	getDetail: (data,setTalbeData) => dispatch(actionCreators.detail(data,setTalbeData)), // 获取详情
	add: (data, list) => dispatch(actionCreators.add(data, list)), // 新增记录
	del: (data, list) => dispatch(actionCreators.del(data, list)), // 删除记录
	modify: (data, list) => dispatch(actionCreators.modify(data, list)), // 修改记录
	waterUseKind: () => dispatch(actionCreators.waterUseKind()), // 获取用水性质
	userList: data => dispatch(actionCreators.userList(data)), // 获取用户列表
	areaList: data => dispatch(actionCreators.areaList()), // 获取片区树
});
export default connect(mapStateToProps, mapDispatchToProps)(Form.create()(Index));
