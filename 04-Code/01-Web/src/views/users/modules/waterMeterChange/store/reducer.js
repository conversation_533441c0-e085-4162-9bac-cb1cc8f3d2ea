import * as actionTypes from './constants';
import { dataType } from '$utils';

const defaultState = {
	fields: null,
	dataList: [], // 列表数据
	total: 0, // 总条数
	detail: null, // 详情
	customer: null, // 当前用户
	waterMeter: {}, // 水表
	callbackResult: {}, // 换表回填结果
	visible: false, // 涉及到交互的弹窗显示控制 默认为false
	areaNo: '',   //获取区域码
	cardNo: '',
	createList: [],
	areaSelect:[],
};
// 重要提示： 请做好各代码段的注释！！！
export default (state = defaultState, action) => {
	switch (action.type) {

		// 回填组件传给redux的值
		case actionTypes.SET_STATE:
			let _data = action.data;
			if (dataType(_data) === 'Object') {
				state[_data.key] = _data.value;
			}
			if (dataType(_data) === 'Array') {
				_data.forEach(item => {
					state[item.key] = item.value;
				});
			}
			return { ...state };

		// 回填列表数据
		case actionTypes.LIST_RECORD:
			state.dataList = action.data.rows;
			state.total = action.data.total;
			return { ...state };

		// 回填详情信息
		case actionTypes.DETAIL_RECORD:
			state.detail = action.data;
			return { ...state };

		// 回填用户详情信息
		case actionTypes.GET_CUSTOEMR:
			state.customer = action.data;
			return { ...state };

		// 获取水表
		case actionTypes.GET_WATER_METER:
			state.waterMeter = action.data;
			return { ...state };

		// 换表回填
		case actionTypes.CALLBACK:
			state.callbackResult = action.data;
			return { ...state };

		//获取区域码
		case actionTypes.GET_AREA_NO_BY_ID:
			state.areaNo = action.data;
			return { ...state };

		case actionTypes.QUERY_LIST_SELECT:
			state.createList = action.data;
			return { ...state };

		// 复装工单数据
		case actionTypes.GET_WORK_ORDER:
			state.detail = action.data;
			return { ...state };

		// 更新当前字轮读数
		case actionTypes.UPDATE_WHEEL_PRESENT_NUMBER:
			return { ...state };

		// 获取片区选择框
		case actionTypes.GET_AREA_SELECT:
			state.areaSelect = action.data;
			return { ...state };

		default:
			return state;
	}
};
