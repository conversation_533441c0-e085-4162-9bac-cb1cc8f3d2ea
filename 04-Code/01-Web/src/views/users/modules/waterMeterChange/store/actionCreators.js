import http from '$http';
import * as actionTypes from './constants';
import { message } from 'antd';
import api from './api';
import { changeWaterMeter,newXTMF1Card,huaxuReplacementCard, xtWriteChange } from '@/utils/cardUtil';
import {
	replacementCard,
	sellConfirm
} from '@/utils/cardUtil';

// 数据回填
const payload = (type, data) => ({ type, data });

// 组件操作redux数据状态
const setState = data => ({ type: actionTypes.SET_STATE, data });

// 获取列表
const list = data => {
	return async dispatch => {
		const res = await http.post(api.list, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.LIST_RECORD, res.data));
		}
	};
};

// 获取详情
const detail = data => {
	return async dispatch => {
		const res = await http.restGet(api.detail, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.DETAIL_RECORD, res.data));
		}
	};
};

// 获取用户详情
const getCustomer = data => {
	return async dispatch => {
		const res = await http.restGet(api.getCustomer, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_CUSTOEMR, res.data));
		}
	};
};

//修改用户姓名
const updateName = (data) => {
	return http.post(api.updateName, data);
};

//修改用户电话
const updateContactPhone = (data) => {
	return http.post(api.updateContactPhone, data);
};

// 获取水表
const getWaterMeter = data => {
	return async dispatch => {
		const res = await http.post(api.getWaterMeter, data);
		if (res.code === 0) {
			if (!!!res.data) {
				message.error('新表不存在或不为库存状态，请在[表务管理]中确认！');
			}
			dispatch(payload(actionTypes.GET_WATER_METER, res.data));
		}
	};
};

// 换表回填
const callback = (data, list, customerId) => {
	return async dispatch => {
		const res = await http.post(api.callback, data);
		if (res.code === 0) {
			dispatch(callbackGetAreaNoById(customerId, data, list));
			dispatch(payload(actionTypes.CALLBACK, res.data));
		}
	};
};

//换表成功后获取区域码
const callbackGetAreaNoById = (customerId, data, list) => {
	return async dispatch => {
		const res = await http.restGet(api.getAreaNoById, customerId);
		if (res.code === 0) {
			dispatch(callbackGetCustomer(res.data, customerId, data, list));
		}
	};
};

//换表成功后直接写卡
const callbackGetCustomer = (areaNo, customerId, data, list) => {
	return async dispatch => {

		const res = await http.restGet(api.getCustomer, customerId);
		if (res.code === 0) {
			let total = 0;
			let zjsj1 = 0;
			let zjsj2 = 0;
			let zjsj3 = 0;
			let sl1 = 0;
			let sl2 = 0;
			let customer = res.data;
			if (customer.waterMeterType === 'IC卡表') {
				if (customer.waterMeterManufacturer === '扬州恒信') {
					const SYS_NAME = process.env.SYS_NAME;
					if (SYS_NAME === 'qhd') {
						if (customer.ladderType === '非阶梯') {
							zjsj1 = customer.price1;
							zjsj2 = customer.price1;
							zjsj3 = customer.price1;
							sl1 = 25;
							sl2 = 0;
						} else {
							zjsj1 = customer.price1 ? customer.price1 : 0;
							zjsj2 = customer.price2 ? customer.price2 : 0;
							zjsj3 = customer.price3 ? customer.price3 : 0;
							sl1 = customer.ladder1 ? customer.ladder1 : 0;
							sl2 = customer.ladder2 ? customer.ladder2 : 0;
						}
						if (customer.waterMeterKindType === '预付费5') {
							total = customer.accumulationAmount;
						} else {
							total = customer.accumulationBuyAmount;
						}
						let result = changeWaterMeter(areaNo, customer.waterMeterKindType, customer.cardNo, customer.waterMeterNo, total, zjsj1, zjsj2, zjsj3, sl1, sl2);
						if (result === 0) {
							dispatch(updateWriteCard(data, list));
						} else {
							list();
							alert('换表成功！写卡失败请重新写卡');
						}
					} else if (SYS_NAME === 'ewkq') {
						let waterMeterNo = customer.waterMeterNo;
						let total = customer.accumulationBuyAmount;
						let zjsj1 = 0;
						let zjsj2 = 0;
						let zjsj3 = 0;
						let sl1 = 0;
						let sl2 = 0;
						if (customer.ladderType === '非阶梯') {
							zjsj1 = detail.price1;
							zjsj2 = detail.price1;
							zjsj3 = detail.price1;
							sl1 = 25;
							sl2 = 0;
						} else {
							zjsj1 = customer.price1 ? customer.price1 : 0;
							zjsj2 = customer.price2 ? customer.price2 : 0;
							zjsj3 = customer.price3 ? customer.price3 : 0;
							sl1 = customer.ladder1 ? customer.ladder1 : 0;
							sl2 = customer.ladder2 ? customer.ladder2 : 0;
						}
						if (customer.waterMeterKindType === '阶梯2') {
							waterMeterNo = '0000000000';
						}
						let result = changeWaterMeter(areaNo, customer.waterMeterKindType, customer.cardNo, waterMeterNo, total, zjsj1, zjsj2, zjsj3, sl1, sl2);
						if (result === 0) {
							list();
							message.success('换表成功');
						}
					}
				}
				else if (customer.waterMeterManufacturer === '深圳华旭') {
					let resultHuaxu = huaxuReplacementCard(customer.cardNo, customer.waterMeterNo, customer.accumulationAmount, customer.accumulationBuyCount, 0)
					console.log('water meter change callbackGetCustomer resultHuaxu: ', resultHuaxu);
					if (resultHuaxu[0] == 1) {
						dispatch(updateWriteCard(data, list));
					} else {
						list();
						alert('换表成功！写卡失败请重新写卡');
					}
				}
				else if (customer.waterMeterManufacturer === '河南新天') {
					let result = -1;
					if (customer.waterMeterKindType === '阶梯57') {
						if (customer.ladderType === '非阶梯') {
							zjsj1 = customer.price1;
							zjsj2 = customer.price1;
							zjsj3 = customer.price1;
							sl1 = customer.ladder1;
							sl2 = customer.ladder1;
						} else {
							zjsj1 = customer.price1 ? customer.price1 : 0;
							zjsj2 = customer.price2 ? customer.price2 : 0;
							zjsj3 = customer.price3 ? customer.price3 : 0;
							sl1 = customer.ladder1 ? customer.ladder1 : 0;
							sl2 = customer.ladder2 ? customer.ladder2 : 0;
						}
						result = xtWriteChange(customer.cardNo, sl1, sl2, zjsj1, zjsj2, zjsj3, customer.accumulationBuyAmount);
					}else if(customer.waterMeterKindType === 'MF1'){
						//换表， 表号，总购买量，本次购买量（剩下多少写多少）
						result = newXTMF1Card(customer.cardNo,customer.accumulationAmount, customer.accumulationAmount);
					}

					if (result === 1 ||result === true) {
						dispatch(updateWriteCard(data, list));
					} else {
						list();
						alert('换表成功！写卡失败请重新写卡');
					}
				}
			} else {
				list();
				message.success('换表成功');
			}
		}
	};
};

//获取区域码
const getAreaNoById = data => {
	return async dispatch => {
		const res = await http.restGet(api.getAreaNoById, data.customerId);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_AREA_NO_BY_ID, res.data));
		}
	};
};

//修改换表记录
const updateWriteCard = (data, getList) => {
	return async dispatch => {
		const res = await http.post(api.updateWriteCard + `/${data.id}`);
		if (res.code === 0) {
			message.success('换表写卡成功！');
			getList();
		}
	};
};

//获取操作员
const queryListSelect = () => {
	return async dispatch => {
		const res = await http.get(api.queryListSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.QUERY_LIST_SELECT, res.data));
		}
	};
};

// 换表工单数据
const getWorkOrder = (data, print) => {
	return async dispatch => {
		const res = await http.restGet(api.getWorkOrder, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_WORK_ORDER, res.data));
			print();
		}
	};
};

// 更新当前读数
const updateWheelPresentNumber = (data, action = () => { }) => {
	return async dispatch => {
		const res = await http.post(api.updateWheelPresentNumber, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.UPDATE_WHEEL_PRESENT_NUMBER, res.data));
			dispatch(getCustomer(data.id));
			action()
			message.success('当前字轮读数修改成功！');
		}
	};
};

// 获取片区选择框
const getAreaSelect = () => {
	return async dispatch => {
		const res = await http.get(api.getAreaSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_AREA_SELECT, res.data));
		}
	};
};

// 获取片区选择框
const invalidWaterChange = (data, areaNo, list) => {
	let id = data.id;
	return async dispatch => {
		const res = await http.post(api.invalidWaterChange + '/' + id);
		if (res.code === 0) {
			let detail = res.data;
			if (data.applyStatus === '已完成') {
				if (detail.waterMeterType === 'IC卡表') {
					let zjsj1 = 0;
					let zjsj2 = 0;
					let zjsj3 = 0;
					let sl1 = 0;
					let sl2 = 0;
					if (detail.waterMeterKindType === '阶梯5') {
						let accumulationBuyCount = Number(detail.accumulationBuyCount) - 1; //次数
						let total = detail.accumulationBuyAmount;
						if (detail.ladderType === '非阶梯') {
							zjsj1 = detail.price1;
							zjsj2 = detail.price1;
							zjsj3 = detail.price1;
							sl1 = 25;
							sl2 = 0;
						}
						else {
							zjsj1 = detail.price1 ? detail.price1 : 0;
							zjsj2 = detail.price2 ? detail.price2 : 0;
							zjsj3 = detail.price3 ? detail.price3 : 0;
							sl1 = detail.ladder1 ? detail.ladder1 : 0;
							sl2 = detail.ladder2 ? detail.ladder2 : 0;
						}
						let result = replacementCard(detail.waterMeterKindType, detail.cardNo, detail.waterMeterNo, detail.accumulationBuyCount, 0, total, areaNo);
						if (result === 0) {
							let result1 = sellConfirm(detail.waterMeterKindType, detail.cardNo, accumulationBuyCount, 0, total, zjsj1, zjsj2, zjsj3, sl1, sl2, areaNo);
							if (result1 === 0) {
								message.success('作废成功!');
							} else {
								message.error('作废成功,写卡失败,请去ic卡管理中补卡');
							}
						}
					} else {
						let newReissueAmount = 0;                   //本次
						let newAllReissueAmount = 0;                //累计
						if (detail.waterMeterKindType === '预付费2') {
							newReissueAmount = 0;
							newAllReissueAmount = detail.accumulationAmount;
						} else if (detail.waterMeterKindType === '预付费5') {
							newAllReissueAmount = detail.accumulationAmount;
						}
						let result = replacementCard(detail.waterMeterKindType, detail.cardNo, detail.waterMeterNo, detail.accumulationBuyCount, newReissueAmount, newAllReissueAmount, areaNo);
						if (result === 0) {
							message.success('作废成功!');
						} else {
							message.error('作废成功,写卡失败,请去ic卡管理中补卡');
						}
					}
				}
			}
			else {
				message.success('作废成功!');
			}
			list();
		}
	};
};

// 获取用水分类
const getWaterUseKindSelect = data => {
	return async dispatch => {
		const res = await http.restGet(api.getWaterUseKindSelect, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_WATER_USE_KIND_SELECT, res.data));
		}
	};
};


export {
	setState, list, detail, getCustomer, getWaterMeter,
	callback, getAreaNoById, updateWriteCard, queryListSelect, getWorkOrder, updateWheelPresentNumber, getAreaSelect
	, invalidWaterChange, updateName, updateContactPhone,getWaterUseKindSelect
};
