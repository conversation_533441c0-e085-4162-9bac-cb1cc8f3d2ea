import './index.scss';

import React, { Fragment } from 'react';

import { constants } from '$utils';
import {
	Button,
	Col,
	DatePicker,
	Descriptions,
	Form,
	Icon,
	Input,
	InputNumber,
	Modal,
	Row,
	Select,
} from 'antd';

import {
	CHANGE_METER_STATUS,
	WATER_METER_MANUFACTURER,
} from '@/constants/waterMeter';

import { actionCreators } from '../store';

const FormItem = Form.Item;
const { Option } = Select;
class ChangeCallback extends React.Component {
	constructor(props) {
		super(props);
		this.state = {
			changeTime: null,
			visible: false,
			visibleName: false,
			visiblePhone: false,
			surplus: 0
		};
	}

	getChangeTime = (date, dateString) => {
		this.setState({ changeTime: dateString });
		this.props.setChangeTime(dateString);
	};

	handleInputWaterMeterNo = (params) => {
		this.props.getWaterMeter(params);
	}

	//修改读数
	updateWheelPresentNumberModal () {
		this.setState({ visible: true });
	}

	//编辑修改弹窗
	editModal (visible) {
		this.setState({ visible: visible });
	}

	//编辑修改弹窗
	editModalName (visibleName) {
		this.setState({ visibleName: visibleName });
	}

	//编辑修改弹窗
	editModalPhone (visiblePhone) {
		this.setState({ visiblePhone: visiblePhone });
	}

	//提交修改字轮读数
	handleClick () {
		const { customer, form } = this.props
		let param = { id: customer.id };
		param.wheelPresentNumber = form.getFieldValue('wheelPresentNumber');
		this.props.updateWheelPresentNumber(param, () => this.editModal(false));
	}

	//修改提交
	handleClickName () {
		const { customer } = this.props
		let param = { customerId: customer.id };
		param.name = this.props.form.getFieldValue('name');
		actionCreators.updateName(param).then(res => {
			if (res.code === 0) {
				this.setState({ visibleName: false });
				this.props.setState([{ key: 'customer', value: { ...customer, name: param.name } }]);
			}
		})
	}

	//修改提交
	handleClickPhone () {
		const { customer } = this.props
		let param = { customerId: customer.id };
		param.contactPhone = this.props.form.getFieldValue('contactPhone');
		actionCreators.updateContactPhone(param).then(res => {
			if (res.code === 0) {
				this.setState({ visiblePhone: false });
				this.props.setState([{ key: 'customer', value: { ...customer, contactPhone: param.contactPhone } }]);
			}
		})
	}

	//渲染修改弹窗
	renderUpdateModal () {
		const { form, customer } = this.props;
		const { visible } = this.state;
		const { getFieldDecorator } = form;
		const footer = (
			<Fragment>
				<Button key="submit" type="primary" onClick={() => this.handleClick()}>提交</Button>
				<Button key="back" onClick={() => this.editModal(false)}>取消</Button>
			</Fragment>
		);
		return (
			<Modal title={'原表购买量'} visible={visible}
				onCancel={() => this.editModal(false)}
				footer={footer}>
				<Form {...constants.formItemLayout}>
					<Row>
						<Col>
							<FormItem label={'原表购买量:'}>
								{getFieldDecorator('wheelPresentNumber', {
									initialValue: customer ? customer.wheelPresentNumber : null
								})
									(<Input placeholder="请输入" />)}
							</FormItem>
						</Col>
					</Row>
				</Form>
			</Modal>
		);
	}

	//渲染修改名称弹窗
	_renderUpdateName () {
		const { form, customer } = this.props;
		const { visibleName } = this.state;
		const { getFieldDecorator } = form;
		const footer = (
			<Fragment>
				<Button key="submit" type="primary" onClick={() => this.handleClickName()}>提交</Button>
				<Button key="back" onClick={() => this.editModalName(false)}>取消</Button>
			</Fragment>
		);
		return (
			<Modal title={'修改用户名称'} visible={visibleName}
				onCancel={() => this.setState({ visibleName: false })}
				footer={footer}>
				<Form {...constants.formItemLayout}>
					<Row>
						<Col>
							<FormItem label={'用户名称:'}>
								{getFieldDecorator('name', {
									initialValue: customer ? customer.name : null
								})
									(<Input placeholder="请输入" />)}
							</FormItem>
						</Col>
					</Row>
				</Form>
			</Modal>
		);
	}

	//渲染修改名称弹窗
	_renderUpdatePhone () {
		const { form, customer } = this.props;
		const { visiblePhone } = this.state;
		const { getFieldDecorator } = form;
		const footer = (
			<Fragment>
				<Button key="submit" type="primary" onClick={() => this.handleClickPhone()}>提交</Button>
				<Button key="back" onClick={() => this.editModalPhone(false)}>取消</Button>
			</Fragment>
		);
		return (
			<Modal title={'修改用户电话'} visible={visiblePhone}
				onCancel={() => this.setState({ visiblePhone: false })}
				footer={footer}>
				<Form {...constants.formItemLayout}>
					<Row>
						<Col>
							<FormItem label={'用户电话:'}>
								{getFieldDecorator('contactPhone', {
									initialValue: customer ? customer.contactPhone : null
								})
									(<Input placeholder="请输入" />)}
							</FormItem>
						</Col>
					</Row>
				</Form>
			</Modal>
		);
	}

	//计算剩余水量
	calc (v) {
		const { customer } = this.props
		let surplus = Number(customer.wheelPresentNumber) - Number(v)
		this.setState({ surplus })
	}

	//修改名字
	updateNameModal (userInfo) {
		this.setState({ visibleName: true, detail: userInfo });
	}

	//修改电话
	updatePhoneModal (userInfo) {
		this.setState({ visiblePhone: true, detail: userInfo });
	}

	render () {
		const { handleSubmitCallback, handleCancelCallback, form, customer, waterMeter } = this.props;
		const { getFieldDecorator, getFieldValue } = form;
		const { surplus } = this.state
		return (
			<Fragment>
				<Descriptions title="用户信息" bordered>
					<Descriptions.Item label="用户名称:">
						<div style={{ display: 'flex', justifyContent: 'space-between' }}>
							{customer && customer.name}
							<Icon type="edit" style={{ cursor: 'pointer', textAlign: 'right', color: 'blue' }}
								onClick={() => this.updateNameModal(customer)} />
						</div>
					</Descriptions.Item>
					<Descriptions.Item label="用户编号：">{customer && customer.cno}</Descriptions.Item>
					<Descriptions.Item label="人口数：">{customer && customer.population}</Descriptions.Item>
					<Descriptions.Item label="用户地址：">{customer && customer.address}</Descriptions.Item>
					<Descriptions.Item label="用户电话:">
						<div style={{ display: 'flex', justifyContent: 'space-between' }}>
							{customer && customer.contactPhone}
							<Icon type="edit" style={{ cursor: 'pointer', textAlign: 'right', color: 'blue' }}
								onClick={() => this.updatePhoneModal(customer)} />
						</div>
					</Descriptions.Item>
					<Descriptions.Item label="身份证号：">{customer && customer.idCard}</Descriptions.Item>
					<Descriptions.Item label="片区：">{customer && customer.areaName}</Descriptions.Item>
					<Descriptions.Item label="水表类型：">{customer && customer.meterType}</Descriptions.Item>
					<Descriptions.Item label="客户类型：">{customer && customer.customerType}</Descriptions.Item>
					<Descriptions.Item label="水表编号：">{customer && customer.waterMeterNo}</Descriptions.Item>
					<Descriptions.Item label="水表类型：">{customer && customer.waterMeterType}</Descriptions.Item>
					<Descriptions.Item label="水表种类：">{customer && customer.waterMeterKindType}</Descriptions.Item>
					<Descriptions.Item label="安装位置：">{customer && customer.installLocation}</Descriptions.Item>
					<Descriptions.Item label="水表厂家：">{customer && customer.waterMeterManufacturer}</Descriptions.Item>
					<Descriptions.Item label="原表购买量：">
						<div style={{ display: 'flex', justifyContent: 'space-between' }}>
							{customer && customer.wheelPresentNumber}
							<Icon type="edit" style={{ cursor: 'pointer', textAlign: 'right', color: 'blue' }}
								onClick={() => this.updateWheelPresentNumberModal(customer)} />
						</div>
					</Descriptions.Item>
				</Descriptions>
				<Form {...constants.formItemLayout} style={{ marginTop: 20 }}>
					<Row>
						<Col span={12}>
							<FormItem label="最新读数：">
								{getFieldDecorator('oldMeterNumber', {
									initialValue: '',
									rules: [{ required: true, message: '请输入最新读数' }]
								})(<InputNumber palceholder="请输入最新读数" style={{ width: '100%' }} onChange={(v) => this.calc(v)} />)}
							</FormItem>
						</Col>
						<Col span={12}>
							<FormItem label="剩余水量：">
								<Input disabled value={surplus || 0} placeholder="请先输入最新读数" />
							</FormItem>
						</Col>
					</Row>

					<Row>
						<Col span={12}>
							<FormItem label="换表时间：">
								{getFieldDecorator('changeTime', {
									initialValue: null,
									rules: [
										{
											required: true,
											message: '请选择换表时间'
										}
									]
								})(<DatePicker format={'YYYY-MM-DD'} onChange={this.getChangeTime} placeholder={'选择换表时间'} style={{ width: '100%' }} />)}
							</FormItem>
						</Col>
						<Col span={12}>
							<FormItem label="换表员：">
								{getFieldDecorator('changePersonName', {
									initialValue: '',
									rules: [
										{
											required: true,
											message: '请输入换表员'
										}
									]
								})(<Input palceholder="请输入换表员" />)}
							</FormItem>
						</Col>
					</Row>

					<Row>
						<Col span={12}>
							<FormItem label={'旧表状态:'}>
								{getFieldDecorator('oldMeterStatus', {
									initialValue: null,
									rules: [
										{
											required: true,
											message: '请选择旧表状态'
										}
									]
								})(
									<Select placeholder="请选择旧表状态">
										{CHANGE_METER_STATUS.map((item, index) => {
											return (
												<Option key={index} value={item.value}>
													{item.label}
												</Option>
											);
										})}
									</Select>
								)}
							</FormItem>
						</Col>
						<Col span={12}>
							<FormItem label={'选择新表厂家:'}>
								{getFieldDecorator('newMeterManufacturer', {
									initialValue: null,
									rules: [{ required: true, message: '请输入选择新表厂家' }]
								})(
									<Select placeholder="选择新表厂家"
										onBlur={v => {
											const no = getFieldValue('newMeterNo');
											if (no) {
												let params = {
													type: getFieldValue('newMeterType'),
													manufacturer: v,
													no
												}
												this.handleInputWaterMeterNo(params)
											}

										}}

									>
										{WATER_METER_MANUFACTURER.map((item, index) => {
											return (
												<Option key={index} value={item.value}>
													{item.label}
												</Option>
											);
										})}
									</Select>
								)}
							</FormItem>
						</Col>
					</Row>

					<Row>
						<Col span={12}>
							<FormItem label="新表编号：">
								{getFieldDecorator('newMeterNo', {
									initialValue: '',
									rules: [
										{
											required: true,
											message: '请输入新表编号'
										}
									]
								})(
									<Input
										placeholder="请输入新表编号"
										onBlur={v => {
											const manufacturer = getFieldValue('newMeterManufacturer')
											if (typeof manufacturer === "number") {
												let params = {
													type: getFieldValue('newMeterType'),
													manufacturer: getFieldValue('newMeterManufacturer'),
													no: v.target.value
												}
												this.handleInputWaterMeterNo(params)
											}

										}}></Input>
								)}
							</FormItem>
						</Col>
						<Col span={12}>
							<FormItem label={'新表类型:'}>
								<Input disabled value={waterMeter && waterMeter.type} placeholder="请先输入新表编号" />
							</FormItem>
						</Col>
					</Row>

					<Row>
						<Col span={12}>
							<FormItem label="基础读数：">
								<Input disabled value={waterMeter && waterMeter.wheelBaseNumber} placeholder="请先输入新表编号" />
							</FormItem>
						</Col>
						<Col span={12}>
							<Col span={12}>
								<FormItem label="铅封号：">
									{getFieldDecorator('lockNumber', {
										initialValue: '',
										rules: [
											{
												required: true,
												message: '请输入换表员'
											}
										]
									})(<Input palceholder="请输入铅封号" />)}
								</FormItem>
							</Col>
						</Col>
					</Row>

					<Row>
						<Col span={24} align="center">
							<Button type="primary" className="btn" onClick={handleSubmitCallback}>
								提交
							</Button>
							<Button type="default" className="btn" onClick={handleCancelCallback}>
								取消
							</Button>
						</Col>
					</Row>
				</Form>
				{this.renderUpdateModal()}
				{this._renderUpdateName()}
				{this._renderUpdatePhone()}
			</Fragment>
		);
	}
}
export default Form.create()(ChangeCallback);
