import * as actionTypes from './constants';
import { dataType, constants } from '$utils';

const defaultState = {
	fields: null,
	datalist: [], // 列表数据
	total: 0, // 总条数
	statistics: {},// 统计信息
	detail: null, // 详情
	visible: false, // 涉及到交互的弹窗显示控制 默认为false
	editLoading: false,  //加载
	waterusekind: [],
	accountBalance: 0.0, // 账户余额
	customers: [], // 用户列表
	areas: [], // 片区树
	customerLevels: [], // 用户等级
	customerList: [],  //同户信息
	loading: false,
	modalDetail: null, // 弹窗详情
	formwork: null,    //发票模板
	lifeCycle: [],     //生命周期树
	price: 0,
	priceNow: 0,
	userBillData: null,
	userList: [],     //调换表号用户列表
	installPosition: [], // 安装位置
	installLocationSelect: [], // 安装位置选择框
	orderStatistics: null,	// 订单统计信息
	billStatistics: null,	// 账单统计信息
	specialFeeStatistics: null,	// 特抄收费统计信息
	cardMeterCopyRecords: null,	//卡表查表信息
	recordTotal: 0,	//卡表总数
	statistic: {},
	waterUseKindPriceList: [],
};

// 重要提示： 请做好各代码段的注释！！！
export default (state = defaultState, action) => {
	switch (action.type) {
		// 回填组件传给redux的值
		case actionTypes.SET_STATE:
			let _data = action.data;
			if (dataType(_data) === 'Object') {
				state[_data.key] = _data.value;
			}
			if (dataType(_data) === 'Array') {
				_data.forEach(item => {
					state[item.key] = item.value;
				});
			}
			return { ...state };

		// 回填列表数据
		case actionTypes.LIST_RECORD:
			state.datalist = action.data.rows;
			state.total = action.data.total;
			return { ...state };

		// 回填统计数据
		case actionTypes.GET_STATISTICS:
			state.statistics = action.data;
			return { ...state };

		// 回填用户详情信息
		case actionTypes.DETAIL_RECORD:
			// ... do something
			let fileList = [];
			const data = action.data;
			if (data && data.total !== undefined) {
				state.total = data.total;
				state.datalist = data.rows;
			} else {
				if (dataType(data) === 'Object') {
					// 如果附件存在，则处理附件列表
					if (data.annexList && data.annexList.length > 0) {
						data.annexList.forEach(item => {
							fileList.push({
								uid: item.id,
								id: item.id,
								createTime: item.createTime,
								updateTime: item.updateTime,
								relatedId: item.relatedId,
								relatedCode: item.relatedCode,
								originName: item.originName,
								newName: item.newName,
								filePath: item.filePath,
								name: item.originName,
								status: 'done',
								url: `${constants.fileUrl}${item.filePath}${item.newName}`
							});
						});
					}
					data.annexList = fileList;
					state.detail = data;
				}
				if (dataType(data) === 'Array') {
					state.detail = { data };
				}
			}
			return { ...state };

		// 新增记录集
		case actionTypes.ADD_RECORD:
			// ... do something
			return { ...state };

		// 删除记录集
		case actionTypes.DEL_RECORD:
			// ... do something
			return { ...state };

		// 修改记录集
		case actionTypes.MODIFY_RECORD:
			// ... do something
			return { ...state };

		case actionTypes.WATER_USE_KIND:
			state.waterusekind = action.data;
			return { ...state };

		// 人口核增
		case actionTypes.POPULATION_CHANGE:
			return { ...state };

		// 变更用水分类
		case actionTypes.WATER_USE_KIND_CHANGE:
			return { ...state };

		// 获取账户余额
		case actionTypes.GET_ACCOUNT_BALANCE:
			state.accountBalance = action.data;
			return { ...state };

		// 提现
		case actionTypes.WITHDRAW:
			return { ...state };

		case actionTypes.GET_CUSTOMER_RECORD:
			state.customers = [{ value: null, label: '全部' }, ...action.data];
			return { ...state };

		// 片区树
		case actionTypes.GET_AREAS_RECORD:
			state.areas = action.data;
			return { ...state };

		//用户等级
		case actionTypes.CUSTOMER_LEVEL_RECORD:
			state.customerLevels = action.data;
			return { ...state };

		//获取同户信息
		case actionTypes.LIST_SAME_HNO_BY_HNO:
			state.customerList = action.data;
			return { ...state };

		//修改户号
		case actionTypes.UPDATE_HNO:
			state.loading = action.data.loading;
			return { ...state };

		//修改用户详情
		case actionTypes.MODIFY:
			return { ...state };

		//修改用户详情
		case actionTypes.EDIT_DETAIL:
			state.detail = action.data
			return { ...state };

		// 弹窗详情
		case actionTypes.GET_MODAL_DETAIL:
			state.modalDetail = action.data;
			return { ...state };

		//获取发票模板
		case actionTypes.GET_BY_TYPE:
			state.formwork = action.data;
			return { ...state };

		//生命周期树
		case actionTypes.GET_CUSTOMER_LIFE_CYCLE:
			state.lifeCycle = action.data;
			return { ...state };

		case actionTypes.GET_UNIT_PRICE:
			state.price = action.data;
			return { ...state };

		case actionTypes.GET_UNIT_PRICE_NOW:
			state.priceNow = action.data;
			return { ...state };

		case actionTypes.WATER_RUSH_FEE:
			state.userBillData = action.data;
			return { ...state };

		//调换表号表格数据
		case actionTypes.USER_LIST:
			state.userList = action.data.rows;
			return { ...state };

		// 水表安装位置
		case actionTypes.INSTALL_POSITION_RECORD:
			state.installPosition = action.data;
			return { ...state };

		// 获取安装位置选择框
		case actionTypes.GET_INSTALL_LOCATION_SELECT:
			state.installLocationSelect = action.data;
			return { ...state };

		// 获取订单统计信息
		case actionTypes.GET_ORDER_STATISTICS:
			state.orderStatistics = action.data;
			return { ...state };

		// 获取账单统计信息
		case actionTypes.GET_BILL_STATISTICS:
			state.billStatistics = action.data;
			return { ...state };

		// 获取特抄收费统计信息
		case actionTypes.GET_SPECIAL_FEE_STATISTICS:
			state.specialFeeStatistics = action.data;
			return { ...state };

		// 获取卡表查表信息
		case actionTypes.GET_CARD_METER_COPY_RECORDS:
			state.cardMeterCopyRecords = action.data.rows;
			state.recordTotal = action.data.total;
			return { ...state };

		// 获取特抄收费统计信息
		case actionTypes.STATISTIC:
			state.statistic = action.data;
			return { ...state };

		// 获取用水分类单价
		case actionTypes.GET_WATER_USE_KIND_PRICE_LIST:
			state.waterUseKindPriceList = action.data;
			return { ...state };

		//修改表号
		case actionTypes.CHANGE_WATER_METER_NO:
			return { ...state };
		default:
			return state;
	}
};
