export default {
	list: `/api/cm/customer/listPage`,
	modify: `api/cm/customer/update`, //修改用户详情
	detail: `/api/cm/customer/detail`,
	waterusekind: `/api/wuk/waterusekind/getSelect`,
	populationChange: `/api/cm/customer/population/change/save`,
	waterUseKindChange: `api/cm/customer/waterUseKind/change/save`,
	getAccountBalance: `/api/cm/customer/getAccountBalance`,
	withdraw: `/api/cm/customer/withdraw/save`,
	customer: `/api/cm/customer/getCreateUidSelect`,
	area: `/api/sys/area/getTree`,
	customerLevelList: `/api/sys/dictionary/getSelectByCode/CUSTOMER_LEVEL`, //查询用户等级
	listSameHnoByHno: `/api/cm/customer/listSameHnoByHno`, //查询同户
	updateHno: `/api/cm/customer/updateHno`, //修改户号
	AdjustmentDetail: `/api/bill/change/adjustment/getDetail`, // 账单调整弹窗
	reductionDetail: `/api/bill/change/minus/getDetail`, // 账单核减弹窗
	getStatistics: `api/cm/customer/totalHnoAndFee`, // 获取统计信息
	editDetail: `/api/cm/customer/get`, //获取用户详情(修改用)
	getByType: `api/invoice/template/getByType`, //获取发票模板
	getCustomerLifeCycle: `api/cm/customer/getCustomerLifeCycle`, //查询生命周期树
	getUnitPrice: `api/wuk/waterusekind/getUnitPrice`,
	waterRushFee: `api/cm/customer/waterRushFee`, //查询打印数据
	updateCostomerList: `api/cm/customer/updateCostomerList`, //更具条件修改片区
	updateCostomer: `api/cm/customer/updateCostomer`, //更具勾选修改片区
	userList: `/api/cm/customer/listPage`,
	exchangeWaterMeterId: `api/cm/customer/waterMeter/change/exchangeWaterMeterId`,
	waterCard: `api/cm/customer/list`,
	waterRushFeeByCondition: `api/cm/customer/waterRushFeeByCondition`, //根据条件批量打印欠费,停水通知单
	installPosition: `/api/sys/dictionary/getClassSelect/INSTALL_LOCATION`,
	getInstallLocationSelect: `/api/sys/dictionary/getSelectByClass/`,
	getOrderStatistics: `/api/charge/order/orderTotal`,
	getBillStatistics: `/api/bill/billTotal`,
	getSpecialFeeStatistics: `/api/charge/specialFee/statistics`,
	getCardMeterCopyRecords: `/api/cm/customer/card/cardMeterCopyRecord/listPage`,
	getStatistic: `/api/cm/customer/getStatistic`,
	getWaterUseKindPriceList: `api/wuk/waterusekind/getAllPriceSelect`,
	updateWheelPresentNumber: `/api/cm/customer/updateWheelPresentNumberWithAudit`, //带有审批流的修改补发水量
	updateAccountBalance: `/api/cm/customer/updateAccountBalance`, //带有审批流的修改补发水量
	updateAddress: `/api/cm/customer/updateAddress`, //修改地址
	updateContactPhone: `/api/cm/customer/updateContactPhone`, //修改手机号码
	changeAccountBalance: `/api/cm/customer/updateBalance`,
	changeWaterNo:`/api/cm/customer/changeWaterMeterNo`,
	restore: `api/cm/customer/restore`,
	listCustomerByAddress: `api/cm/customer/listCustomerByAddress`,
};
