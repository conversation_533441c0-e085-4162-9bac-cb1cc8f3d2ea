import React, { Component, Fragment } from 'react';
import { withRouter } from 'react-router-dom';
import { actionCreators } from "../store";
import { connect } from "react-redux";
import { Button, Col, Row, Tabs } from "antd";
import Basic from '../components/basicInfo';
import Cost from '../components/cost';
import Record from '../components/record';
import TimeLine from '../components/timeLine'
import './index.scss';

const { TabPane } = Tabs;

class Index extends Component {

	constructor(props) {
		super(props);
		this.state = {
			type: 0, // 0 - 基础信息 1 - 费用信息 2 - 记录信息
			key: 0, // 对应关系的第 0 个
			page: 1,
			pageSize: 10,
		};
	}

	componentDidMount() {
		this.getDetail();
	}

	// 子组件分页获取数据
	getData = (page, pageSize) => {
		this.setState({
			page,
			pageSize
		}, () => {
			this.getDetail()
		})
	};

	// 获取详情
	getDetail = () => {
		const customerId = sessionStorage.getItem('_id');
		const { type, key, page, pageSize } = this.state;
		let params = {
			customerId: parseInt(customerId),
			tabIndex: parseInt(type),
			key: parseInt(key),
			hnoAndCno:1,
			page,
			pageSize
		};
		this.props.getDetail(params)
		this.props.getOrderStatistics(params)
		this.props.getBillStatistics(params)
		this.props.getSpecialFeeStatistics(params)
	};

	onChangeType = (type) => {
		this.setState({ type }, () => {
			if (type === '0') {
				this.basic.setType();
			}
			if (type === '1') {
				this.cost.setType();
			}
			if (type === '2') {
				this.record.setType();
			}
		})
	};

	changeSubType = (type) => {
		this.props.setState({ key: 'detail', value: null });
		this.setState({ key: type }, () => {
			this.getDetail()
		})
	};

	backPage = () => {
		this.props.setState([{ key: 'detail', value: null }]);
		React.$backWhere(this.props)
	};

	// ref
	onRef0 = ref => this.basic = ref;
	onRef1 = ref => this.cost = ref;
	onRef2 = ref => this.record = ref;

	render() {
		const { type } = this.state;
		const { datalist, detail, total, orderStatistics, billStatistics, specialFeeStatistics } = this.props;
		return (
			<div className="shadow-radius infoView">
				{/* 页面标题 */}
				<Row>
					<Col span={22}><h1>用户信息详情</h1></Col>
					<Col span={2}><Button onClick={this.backPage}>返回</Button></Col>
				</Row>
				<Row>
					<Tabs activeKey={`${type}`} onChange={this.onChangeType} type="card">
						<TabPane tab="基础信息" key="0">
							<Basic onRef={this.onRef0} setType={this.changeSubType} data={detail} />
						</TabPane>
						<TabPane tab="费用信息" key="1">
							<Cost onRef={this.onRef1} setType={this.changeSubType} data={datalist} total={total} orderStatistics={orderStatistics} billStatistics={billStatistics} specialFeeStatistics={specialFeeStatistics} getData={this.getData} />
						</TabPane>
						<TabPane tab="记录信息" key="2">
							<Record onRef={this.onRef2} setType={this.changeSubType} data={datalist} total={total} getData={this.getData} />
						</TabPane>
						<TabPane tab="用户信息树" key="3">
							<TimeLine data={datalist} />
						</TabPane>
					</Tabs>
				</Row>
			</div>
		)
	}
}

const mapStateToProps = state => {
	const data = state.get('infos');
	return {
		fields: data.fields,
		datalist: data.datalist, // 列表
		detail: data.detail, // 获取详情
		total: data.total,
		orderStatistics: data.orderStatistics,
		billStatistics: data.billStatistics,
		specialFeeStatistics: data.specialFeeStatistics
	}
};
const mapDispatchToProps = dispatch => ({
	setState: data => dispatch(actionCreators.setState(data)), // 设置redux值
	getDetail: data => dispatch(actionCreators.detail(data)), // 获取详情
	getOrderStatistics: data => dispatch(actionCreators.getOrderStatistics(data)),
	getBillStatistics: data => dispatch(actionCreators.getBillStatistics(data)),
	getSpecialFeeStatistics: data => dispatch(actionCreators.getSpecialFeeStatistics(data)),
});
export default connect(
	mapStateToProps,
	mapDispatchToProps
)(withRouter(Index));
