import React, { Component } from 'react';
import { Tabs } from 'antd';
import Balance from './balance'; // 余额变更记录
import WaterMeterReplacement from './waterMeterReplacement'; // 换标记录
import PatchCard from './patchCard'; // 换标记录
import WaterMeterRemoval from './waterMeterRemoval'; // 拆表记录
import Reload from './reload'; // 复装记录
import Withdraw from './withdraw'; // 提现记录
import Population from './population'; // 人口核增记录
import ChangeWaterUseKind from './changeWaterUseKind'; // 人口核增记录
import UserModifyRecord from './userModifyRecord'; // 用户信息修改记录
import BillAdjustment from './billAdjustment'; // 账单调整记录
import BillReduction from './billReduction'; // 账单核减记录

const { TabPane } = Tabs;
class Index extends Component{
	constructor(props) {
		super(props);
		this.state = {
			page: 1,
			pageSize: 10,
			key: '0',
			list: [
				{
					label: '余额变更记录',
					value: '0',
					render: (data, total, list) => (<Balance data={data} total={total} list={list} />)
				},
				{
					label: '补卡记录',
					value: '1',
					render: (data, total, list) => (<PatchCard data={data} total={total} list={list} />)
				},
				{
					label: '换表记录',
					value: '2',
					render: (data, total, list) => (<WaterMeterReplacement data={data} total={total} list={list} />)
				},
				{
					label: '拆表记录',
					value: '3',
					render: (data, total, list) => (<WaterMeterRemoval data={data} total={total} list={list} />)
				},
				{
					label: '复装记录',
					value: '4',
					render: (data, total, list) => (<Reload data={data} total={total} list={list} />)
				},
				{
					label: '提现记录',
					value: '5',
					render: (data, total, list) => (<Withdraw data={data} total={total} list={list} />)
				},
				{
					label: '人口核增记录',
					value: '6',
					render: (data, total, list) => (<Population data={data} total={total} list={list} />)
				},
				{
					label: '用水性质变更记录',
					value: '7',
					render: (data, total, list) => (<ChangeWaterUseKind data={data} total={total} list={list} />)
				},
				{
					label: '用户信息修改记录',
					value: '8',
					render: (data) => {
						return <UserModifyRecord tabIndex={2} isKey={8} />
					}
				},
				{
					label: '账单调整记录',
					value: '9',
					render: (data) => {
						return <BillAdjustment tabIndex={2} isKey={9} />
					}
				},
				{
					label: '账单核减记录',
					value: '10',
					render: (data) => {
						return <BillReduction tabIndex={2} isKey={10} />
					}
				},
			]
		};
	}
	componentDidMount(){
		this.props.onRef(this)
	}

	onChangeType = type => {
		this.setState({key: type}, () => this.props.setType(parseInt(type)))
	};

	setType = () => {
		this.setState({key: '0'}, () => this.onChangeType('0'))
	};

	render () {
		const { list, key } = this.state;
		const { data, total, getData } = this.props;
		return (
			<div>
				<Tabs activeKey={`${key}`} defaultActiveKey="0" onChange={this.onChangeType}  style={{ minHeight: 220 }}>
					{
						list.map((item, index) => {
							return (
								<TabPane tab={item.label} key={item.value}>
									{item.render(data, total, getData)}
								</TabPane>
							)
						})
					}
				</Tabs>
			</div>
		)
	}
}

export default Index;
