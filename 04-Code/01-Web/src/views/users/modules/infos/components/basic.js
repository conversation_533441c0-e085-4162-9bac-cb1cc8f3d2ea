import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Row, Col, Form, Divider, Descriptions, Tag } from 'antd';
import Img from 'react-zmage';
import './index.scss';
import { userStatusColorMap} from '@/constants/colorStyle'
import Decimal from "decimal.js";


class Index extends Component {

  realBalance = (balance, realTimeFee) => {
    if (balance == null){
      return null
    }
    if (realTimeFee == null){
      return balance
    }
    return new Decimal(balance).sub(new Decimal(realTimeFee)).toNumber()
  }

	render() {
		const { data } = this.props;
		const {
			cno,
			hno,
			name,
			cardNo,
			province,
			city,
			district,
			address,
			waterUseKindType,
			waterUseKindName,
			areaName,
			status,
			meterType,
			apportionType,
			apportionAmount,
			population,
			payWay,
			numberOfHouse,
			customerType,
			accountBalance,
			fixedQuantity,
			fixedQuantityAmount,
			createPersonName,
			createTime,
			customerLevelName,
			remark,
			accumulationAmount,
			totalAmount,
			credits,
			contact,
			contactPhone,
			email,
			idCard,
			houseLadderUse,
			houseLadderSum,
			shareLadder,
			price,
			contractNumber,
			oweFee,
			wheelPresentNumber,
				waterMeterKindType,
				accumulationBuyAmount,
				housePreStoreWaterAmount,
				outBillRatio,
				preStoreWaterAmount,
			// 卡表表内剩余金额
			accountBalanceIc,
			// 最近查表/抄表日期
			lastTranscribeInterval,
			// 抄表员名
			transcribePersonName,
			// 实时水费
			realTimeWaterFee
		} = data || {};
			return (
			<div className="infoComponent">
				<Row>
					<Descriptions bordered>
						<Descriptions.Item label="用户片区：">{areaName}</Descriptions.Item>
						<Descriptions.Item label="用户户号：">{hno}</Descriptions.Item>
						<Descriptions.Item label="用户编号：">{cno}</Descriptions.Item>
						<Descriptions.Item label="用户卡号：">{cardNo}</Descriptions.Item>
						<Descriptions.Item label="用户名称：" span={2}>{name}</Descriptions.Item>
						<Descriptions.Item label="用户地址：" span={2}>
							{province}
							{city}
							{district}
							{address}
						</Descriptions.Item>
						<Descriptions.Item label="联系人：">{contact}</Descriptions.Item>
						<Descriptions.Item label="身份证号：">{idCard}</Descriptions.Item>
						<Descriptions.Item label="联系电话：">{contactPhone}</Descriptions.Item>
						<Descriptions.Item label="电子邮箱：">{email}</Descriptions.Item>
						<Descriptions.Item label="用水分类：">{waterUseKindType}</Descriptions.Item>
						<Descriptions.Item label="用水性质：">{waterUseKindName}</Descriptions.Item>
						<Descriptions.Item label="水费单价：">{price}</Descriptions.Item>
						<Descriptions.Item label="缴费方式：">{payWay}</Descriptions.Item>
						<Descriptions.Item label="供水合同号：">{contractNumber}</Descriptions.Item>
						<Descriptions.Item label="人口数：">{population}</Descriptions.Item>
						<Descriptions.Item label="户数：">{numberOfHouse}</Descriptions.Item>
						<Descriptions.Item label="用户等级：">{customerLevelName}</Descriptions.Item>
						<Descriptions.Item label="信用额度：">{credits}</Descriptions.Item>
						<Descriptions.Item label="用户类型：">{customerType}</Descriptions.Item>
						<Descriptions.Item label="水表分类：">{meterType}</Descriptions.Item>
						<Descriptions.Item label="分摊方式：">{apportionType}</Descriptions.Item>
						<Descriptions.Item label="分摊量：">{apportionAmount}</Descriptions.Item>
						<Descriptions.Item label="是否定量：">{fixedQuantity ? '是' : '否'}</Descriptions.Item>
						<Descriptions.Item label="定量值：">{fixedQuantityAmount}</Descriptions.Item>
						<Descriptions.Item label="总用(购)水量：">{totalAmount}</Descriptions.Item>
							{waterMeterKindType&&waterMeterKindType.indexOf('预付费')!=-1 ?
									<Descriptions.Item label="累计用(购)水量：">{accumulationAmount}</Descriptions.Item> : ''}
							{waterMeterKindType&&waterMeterKindType.indexOf('阶梯')!=-1 ?
									<Descriptions.Item label="累计购水金额：">{accumulationBuyAmount}</Descriptions.Item>: ''}
							<Descriptions.Item label="出账比例(%)：">{outBillRatio}</Descriptions.Item>

							<Descriptions.Item label="结账示数：">{wheelPresentNumber}</Descriptions.Item>
						<Descriptions.Item label="阶梯用量：">{houseLadderUse}</Descriptions.Item>
						<Descriptions.Item label="阶梯总量：">{houseLadderSum}</Descriptions.Item>
						<Descriptions.Item label="共享阶梯：">{shareLadder ? '是' : '否'}</Descriptions.Item>
						<Descriptions.Item label="用户状态：">
							<Tag color={userStatusColorMap.get(status)}>
								{status}
							</Tag>
						</Descriptions.Item>
						<Descriptions.Item label="欠费金额:">{oweFee?oweFee:0}</Descriptions.Item>
						<Descriptions.Item label="账户余额：">{this.realBalance(accountBalance, realTimeWaterFee)}</Descriptions.Item>
							<Descriptions.Item label="预存水量：">{preStoreWaterAmount}</Descriptions.Item>
							<Descriptions.Item label="卡表表内余额：">{accountBalanceIc}</Descriptions.Item>
						<Descriptions.Item label="最近查表/抄表日期：">{lastTranscribeInterval}</Descriptions.Item>
						<Descriptions.Item label="开户操作员：">{createPersonName}</Descriptions.Item>
						<Descriptions.Item label="开户时间：">{createTime}</Descriptions.Item>
						<Descriptions.Item label="抄表员：">{transcribePersonName}</Descriptions.Item>
						<Descriptions.Item label="备注：" span={2}>
							{remark}
						</Descriptions.Item>
					</Descriptions>
				</Row>
			</div>
		);
	}
}

Index.propTypes = {
	data: PropTypes.object.isRequired // 是否显示
};

Index.defaultProps = {
	data: {} // 默认为空
};

export default Index;
