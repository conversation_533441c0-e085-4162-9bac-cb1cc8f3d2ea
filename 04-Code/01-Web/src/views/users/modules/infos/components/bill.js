import React, { Component } from 'react';
import http from '$http';
import { Row, Col, Table, Button, Tag } from 'antd';
import Bill from '$components/bills';
import { billStatusColorMap } from '@/constants/colorStyle';
class Index extends Component {
	constructor(props) {
		super(props);
		this.state = {
			visible: false,
			title: '',
			type: '',
			record: null,
			page: 1,
			pageSize: 10,
		};
	}

	// 查看详情
	handleView = record => {
		http.restGet(`api/bill/getDetail`, record.id).then(res => {
			if (res.code === 0) {
				this.setState({
					title: '账单详情',
					type: 0,
					record: res.data
				}, () => {
					this.setState({ visible: true })
				})
			}
		})
	};

	getList = () => {
		const { page, pageSize } = this.state;
		this.props.list(page, pageSize)
	};

	// 列表分页
	handlePageChange = (page) => {
		this.setState({
			page
		}, () => {
			this.getList();
		})
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState({
			page: 1,
			pageSize: size,
		}, () => {
			this.getList();
		})
	};

	// 取消弹窗
	handleModalCancel = () => {
		this.setState({
			visible: false
		})
	};

	// 弹窗提交
	handleModalSubmit = () => { };

	render() {
		const { page, pageSize, visible, title, type, record } = this.state;
		const { data, total, statistics } = this.props;
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: (total) => {
				return `共 ${total} 条数据 `
			},
			onChange: (page) => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		const columns = [
			{
				title: '账单编号',
				dataIndex: 'billNo',
				key: 'billNo',
				align: 'center',
			},
			{
				title: '水表编号',
				dataIndex: 'waterMeterNo',
				key: 'waterMeterNo',
				align: 'center',
			},
			{
				title: '水表种类',
				dataIndex: 'meterType',
				key: 'meterType',
				align: 'center',
			},
			{
				title: '账单来源',
				dataIndex: 'billType',
				key: 'billType',
				align: 'center',
			},
			{
				title: '上期示数',
				dataIndex: 'lastWheelNumber',
				key: 'lastWheelNumber',
				align: 'center',
			},
			{
				title: '本期示数',
				dataIndex: 'currentWheelNumber',
				key: 'currentWheelNumber',
				align: 'center',
			},
			{
				title: '结算水量',
				dataIndex: 'settleAmount',
				key: 'settleAmount',
				align: 'center',
			},
			{
				title: '账单金额',
				dataIndex: 'billFee',
				key: 'billFee',
				align: 'center',
			},
			{
				title: '已结金额',
				dataIndex: 'settleFee',
				key: 'settleFee',
				align: 'center',
			},
			{
				title: '出账时间',
				dataIndex: 'createTime',
				key: 'createTime',
				align: 'center',
			},
			{
				title: '平账时间',
				dataIndex: 'averageAccountTime',
				key: 'averageAccountTime',
				align: 'center',
			},
			{
				title: '账单创建人',
				dataIndex: 'createPersonName',
				key: 'createPersonName',
				align: 'center',
			},
			{
				title: '特殊标志',
				dataIndex: 'sendDiscMark',
				key: 'sendDiscMark',
				align: 'center',
			},
			{
				title: '账单状态',
				dataIndex: 'billStatus',
				key: 'billStatus',
				align: 'center',
				width: 80,
				fixed: 'right',
				render: text => {
					return <Tag color={billStatusColorMap.get(text)}>{text}</Tag>;
				}
			},
			{
				title: '操作',
				key: 'operation',
				width: 200,
				fixed: 'right',
				align: 'center',
				render: (text, record, index) => {
					return (
						<span>
							<Button title='查看' className='btn' type="primary" size="small" icon="eye"
								onClick={() => this.handleView(record)} />
						</span>
					)
				},
			}
		];
		return (
			<div>
				<Row>
					<Col style={{ marginBottom: 5 }}>
						总金额: {statistics ? statistics.orderAmount : ''}元 &emsp;&emsp;&emsp;
						总吨数：{statistics ? statistics.waterAmount : ''}吨 &emsp;&emsp;&emsp;
					</Col>
					<Table
						size="middle"
						bordered
						scroll={{ x: 2300 }}
						rowKey={() => Math.random()}
						columns={columns}
						pagination={paginationProps}
						dataSource={data}
					/>
					<Bill
						title={title}
						visible={visible}
						type={type}
						record={record}
						onCancel={this.handleModalCancel}
						onSubmit={this.handleModalSubmit}
					/>
				</Row>
			</div>
		)
	}
}

export default Index;
