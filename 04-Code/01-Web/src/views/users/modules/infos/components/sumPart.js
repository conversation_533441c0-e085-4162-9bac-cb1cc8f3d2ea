import React, { Component } from 'react';
import { Row, Col, Table, Button, Form } from 'antd';
import { constants } from '$utils';

class Index extends Component {
	constructor(props) {
		super(props);
		this.state = {
			page: 1,
      pageSize: 10,
      visible:false,
      detail:null,
		};
	}

	getList = () => {
		const { page, pageSize } = this.state;
		this.props.list(page, pageSize);
	};

	// 列表分页
	handlePageChange = page => {
		this.setState({ page }, () => {this.getList();});
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState(
			{ page: 1, pageSize: size }, () => {this.getList();});
	};

	render() {
		const { page, pageSize } = this.state;
		const { data, total } = this.props;
		/*const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: total => {
				return `共 ${total} 条数据 `;
			},
			onChange: page => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};*/
		const columns = [
			{
				title: '序号',
				dataIndex: 'xuHao',
				align: 'center',
				fixed:'left',
				render: (text, record, index) => {
					return index+1
				}
			},
			{
				title: '总表用户编号',
				dataIndex: 'sumCno',
        align: 'center',
        width:240,
        fixed:'left'
			},
			{
				title: '分表用户编号',
				dataIndex: 'partCno',
				align: 'center'
			},
			{
				title: '分摊类型',
				dataIndex: 'apportionType',
				align: 'center'
			},
			{
				title: '分摊量',
				dataIndex: 'apportionAmount',
				align: 'center'
			},
			{
				title: '当前用量',
				dataIndex: 'currentAmount',
        align: 'center',
        width:100
			}
		];
		return (
			<Form {...constants.formItemLayout}>
				<div>
					<Table size="middle" bordered scroll={{ x: 1400 }} rowKey={() => Math.random()} columns={columns}
								 //pagination={paginationProps}
								 pagination={false}
								 dataSource={data} />
				</div>
			</Form>
		);
	}
}

export default Index;
