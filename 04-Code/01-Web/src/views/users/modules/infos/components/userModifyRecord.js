import React, { Component } from 'react';
import {
	Tabs,
	TreeSelect,
	InputNumber,
	Radio,
	Modal,
	Button,
	Col,
	DatePicker,
	Form,
	Input,
	Row,
	Select,
	Table,
	Icon, Tag, Divider
} from 'antd';
import { withRouter } from 'react-router-dom';
import NoData from '$components/NoData';
import { actionCreators } from '../store';
import { connect } from 'react-redux';
import './index.scss';
import { constants } from '$utils';
import moment from "moment";
import Img from "react-zmage";

const FormItem = Form.Item;
const formItemLayout = {
	labelCol: {
		xs: { span: 24 },
		sm: { span: 8 }
	},
	wrapperCol: {
		xs: { span: 24 },
		sm: { span: 16 }
	}
};
class Index extends Component {

	constructor(props) {
		super(props);
		this.state = {
			page: 1,
			pageSize: 10
		};
	}

	componentDidMount() {
		this.getList();
	}

	getList = () => {
		const customerId = sessionStorage.getItem('_id');
		const { page, pageSize } = this.state;
		const { tabIndex, isKey,applyId } = this.props;
		let params = {
			customerId: parseInt(customerId),
			applyId: parseInt(applyId),
			tabIndex: parseInt(tabIndex),
			key: parseInt(isKey),
			page,
			pageSize
		};

		this.props.getDetail(params);
	};


	componentWillReceiveProps(nextProps, nextContext) {
		if (nextProps.datalist&&nextProps.datalist.length>0) {
			const datalist=	nextProps.datalist;
			datalist.forEach(data => {
				let fileList = [];
				if(data.annexList){
					data.annexList.forEach(item => {
						fileList.push({
							uid: item.id,
							id: item.id,
							createTime: item.createTime,
							updateTime: item.updateTime,
							relatedId: item.relatedId,
							relatedCode: item.relatedCode,
							originName: item.originName,
							newName: item.newName,
							filePath: item.filePath,
							name: item.originName,
							status: 'done',
							url: `${constants.fileUrl}${item.filePath}${item.newName}`
						});
					});

				}
				data.fileList=fileList;
			});
		}
	}

	// 列表分页
	handlePageChange = page => {
		this.setState({ page }, () => {
			this.getList();
		});
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState(
			{
				page: 1,
				pageSize: size
			},
			() => {
				this.getList();
			}
		);
	};

	render() {
		const { page, pageSize } = this.state;
		const { datalist, total,applyId } = this.props;
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: (total) => {
				return `共 ${total} 条数据 `;
			},
			onChange: (page) => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		const columns = [
			{
				title: '序号',
				dataIndex: 'xuhao',
				align: 'center',
				render: (text, record, index) => {
					return index + 1;
				},
			},
			{
				title: '修改数据项',
				dataIndex: 'dataItem',
				key: 'dataItem',
				align: 'center',
			},
			{
				title: '原值',
				dataIndex: 'oldValue',
				key: 'oldValue',
				align: 'center',
			},
			{
				title: '新值',
				dataIndex: 'newValue',
				key: 'newValue',
				align: 'center',
			},
			{
				title: '修改时间',
				dataIndex: 'changeTime',
				key: 'changeTime',
				align: 'center',
			},
			{
				title: '修改人',
				dataIndex: 'changePersonName',
				key: 'changePersonName',
				align: 'center'
			},
			{
				title: '备注',
				dataIndex: 'remark',
				key: 'remark',
				align: 'center'
			}


		];
		if(applyId){
			columns.push({
				title: '用户编号',
				dataIndex: 'cno',
				key: 'cno',
				align: 'center'
			})
			columns.push({
				title: '用户名',
				dataIndex: 'name',
				key: 'name',
				align: 'center'
			})
			columns.push({
				title: '照片信息',
				key: 'photoInformation',
				align: 'center',
				render:  (text, record, index) => {
					return record &&record.fileList &&record.fileList.length > 0 ? (
							record.fileList.map((item, index) => {
								return (
										<Col key={index} className="photoInfo" span={8} align="center">
											<Img src={item.url} alt=""  style={{height:300,weight:300}}/>
										</Col>
								);
							})
					) : (
							<NoData text="暂无照片信息" />
					)}
			})

		}
		return (
				<Table  bordered columns={columns} rowKey={() => Math.random()} dataSource={datalist}
							 pagination={paginationProps}/>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('infos');
	return {
		fields: data.fields,
		datalist: data.datalist, // 列表
		detail: data.detail, // 获取详情
		total: data.total
	};
};
const mapDispatchToProps = dispatch => ({
	getDetail: data => dispatch(actionCreators.detail(data)) // 获取详情
});
export default connect(
	mapStateToProps,
	mapDispatchToProps
)(withRouter(Index));
