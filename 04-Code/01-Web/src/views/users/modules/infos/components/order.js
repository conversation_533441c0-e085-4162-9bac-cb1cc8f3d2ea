import React, { Component } from 'react';

import http from '$http';
import {
	Button,
	Col,
	Row,
	Table,
	Tag,
} from 'antd';

import OrderDetailModal from '@/components/order';
import { orderStatusColorMap } from '@/constants/colorStyle';

class Index extends Component {
	constructor(props) {
		super(props);
		this.state = {
			detailModalVisible: false,
			detail: null,
			page: 1,
			pageSize: 10,
		};
	}

	// 查看详情
	handleView = record => {
		http.restGet(`/api/charge/order/getDetailById`, record.id).then(res => {
			if (res.code === 0) {
				this.setState({
					detailModalVisible: true,
					detail: res.data
				})
			}
		})
	};

	getList = () => {
		const { page, pageSize } = this.state;
		this.props.list(page, pageSize)
	};

	// 列表分页
	handlePageChange = (page) => {
		this.setState({
			page
		}, () => {
			this.getList();
		})
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState({
			page: 1,
			pageSize: size,
		}, () => {
			this.getList();
		})
	};

	// 取消详情弹窗
	handleCancel = () => {
		this.setState({ detailModalVisible: false })
	};


	render () {
		const { page, pageSize, detailModalVisible, detail } = this.state;
		const { data, total, statistics } = this.props;
		const columns = [
			{
				title: '订单编号',
				dataIndex: 'orderNo',
				key: 'orderNo',
				align: 'center',
			},
			{
				title: '水表编号',
				dataIndex: 'waterMeterNo',
				key: 'waterMeterNo',
				align: 'center',
			},
			{
				title: '水表种类',
				dataIndex: 'waterMeterKind',
				key: 'waterMeterKind',
				align: 'center',
			},
			{
				title: '订单来源',
				dataIndex: 'orderSource',
				key: 'orderSource',
				align: 'center',
			},
			{
				title: '支付方式',
				dataIndex: 'chargeWay',
				key: 'chargeWay',
				align: 'center',
			},
			{
				title: '用水分类',
				dataIndex: 'waterUseKindName',
				key: 'waterUseKindName',
				align: 'center',
			},
			{
				title: '订单水量',
				dataIndex: 'waterAmount',
				key: 'waterAmount',
				align: 'center',
			},
			{
				title: '订单金额',
				dataIndex: 'orderAmount',
				key: 'orderAmount',
				align: 'center',
			},
			{
				title: '缴费时间',
				dataIndex: 'createTime',
				key: 'createTime',
				align: 'center',
			},
			{
				title: '收费部门',
				dataIndex: 'departmentName',
				key: 'departmentName',
				align: 'center',
			},
			{
				title: '缴费区间',
				dataIndex: 'payInterval',
				key: 'payInterval',
				align: 'center',
			},
			{
				title: '收费员',
				dataIndex: 'createPersonName',
				key: 'createPersonName',
				align: 'center',
			},
			{
				title: '订单状态',
				dataIndex: 'status',
				key: 'status',
				align: 'center',
				width: 80,
				fixed: 'right',
				render: text => {
					return <Tag color={orderStatusColorMap.get(text)}>{text}</Tag>;
				}
			},
			{
				title: '操作',
				key: 'operation',
				width: 200,
				fixed: 'right',
				align: 'center',
				render: (text, record, index) => {
					return (
						<span>
							<Button title='查看' className='btn' type="primary" size="small" icon="eye"
								onClick={() => this.handleView(record)} />
						</span>
					)
				},
			}
		];
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: (total) => { return `共 ${total} 条数据 ` },
			onChange: (page) => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		return (
			<div>
				<Row>
					<Col style={{ marginBottom: 5 }}>
						总金额: {statistics ? statistics.orderAmount : ''}元 &emsp;&emsp;&emsp;
						总吨数：{statistics ? statistics.waterAmount : ''}吨 &emsp;&emsp;&emsp;
						退款金额：{statistics ? statistics.refundFee : ''}元 &emsp;&emsp;&emsp;
						退款水量： {statistics ? statistics.refundAmount : ''}吨
					</Col>
					<Table
						size="middle"
						bordered
						scroll={{ x: 2300 }}
						rowKey={() => Math.random()}
						columns={columns}
						pagination={paginationProps}
						dataSource={data}
					/>
					<OrderDetailModal
						visible={detailModalVisible}
						detail={detail}
						handleCancel={this.handleCancel}
					/>
				</Row>
			</div>
		)
	}
}

export default Index;
