import * as actionTypes from "./constants";
import { dataType } from '$utils'

const defaultState = {
	fields: null,
	datalist: [], // 列表数据
	total: 0, // 总条数
	detail: null, // 详情
	visible1: false, // 新增弹窗开关
	visible: false, // 涉及到交互的弹窗显示控制 默认为false
	waterUseKind: [], // 用水分类列表
	users: [], // 用户列表
};

// 重要提示： 请做好各代码段的注释！！！
export default (state = defaultState, action) => {
	switch (action.type) {

		// 回填组件传给redux的值
		case actionTypes.SET_STATE:
			let _data = action.data;
			if (dataType(_data) === 'Object') {
				state[_data.key] = _data.value;
			}
			if (dataType(_data) === 'Array') {
				_data.forEach(item => {
					state[item.key] = item.value;
				})
			}
			return {...state};

		// 回填列表数据
		case actionTypes.LIST_RECORD:
			state.datalist = action.data.rows;
			state.total = action.data.total;
			return {...state};

		// 回填详情信息
		case actionTypes.DETAIL_RECORD:
			state.detail = action.data;
			return {...state};

		// 新增记录集
		case actionTypes.ADD_RECORD:
			// ... do something
			state.visible1 = false;
			state.detail = null;
			return {...state};

		// 删除记录集
		case actionTypes.DEL_RECORD:
			// ... do something
			return {...state};

		// 修改记录集
		case actionTypes.MODIFY_RECORD:
			// ... do something
			state.visible1 = false;
			state.detail = null;
			return {...state};

		// 用水分类
		case actionTypes.WATER_USE_KIND:
			state.waterUseKind = action.data;
			return {...state};

		// 获取用户列表
		case actionTypes.GET_USER_RECORD:
			state.users = action.data;
			return {...state};

		// 删除用户列表
		case actionTypes.DEL_USER_RECORD:
			state.detail = null;
			return {...state};

		default:
			return state;
	}
}
