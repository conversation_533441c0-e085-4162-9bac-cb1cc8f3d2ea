import React, { Component, Fragment } from 'react';
import { actionCreators } from './store';
import { connect } from 'react-redux';
import http from '$http';
import moment from 'moment';
import { Spin, Upload, Icon, Tag, Button, Col, DatePicker, Form, Input, Row, Select, Table, Modal, Divider, message, TreeSelect } from 'antd';
import Img from 'react-zmage';
import { constants, getBase64 ,getTime} from '$utils';
import AssociatedUsers from '$components/associatedUsers';
import NoData from '$components/NoData';
import { APPLY_STATUS_BY_OTHER } from '@/constants/order';
import { applyStatusColorMap, lowStatusColorMap } from '@/constants/colorStyle';
import './index.scss';

moment.locale('zh-cn');

const { TreeNode } = TreeSelect;
const FormItem = Form.Item;
const { Option } = Select;
let isEdit = false;
let isDone = false;

const formItemLayout = {
	labelCol: { span: 6 },
	wrapperCol: { span: 15 }
};

class Index extends Component {

	constructor(props) {
		super(props);
		this.state = {
			show: false,
			previewVisible: false, // 查看大图控制器
			previewImage: '',
			fileList: [],
			searchForm: {
				cno: '',
				name: '',
				address: '',
				lowCertificate: '',
				status: null,
				applyStatus: null,
				areaId: null,
			},
			page: 1,
			pageSize: 10,
			customerList: [], // 关联用户列表
			customerIdList: '', // 关联用户ID
			selectedRowKeys: [],
			selectedRows: []
		};
	}

	componentDidMount() {
		this.props.areaList(); // 获取片区树
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields && isTags) {
			this.setState(
				{
					...fields.state
				},
				() => {
					this.props.form.setFieldsValue({
						...fields.form
					});
				}
			);
		} else {
			this.props.waterUseKind();
			this.getList();
		}
	}

	componentWillUnmount() {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			});
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			});
		}
	}

	componentWillReceiveProps(nextProps, nextContext) {
		if (nextProps.detail && !isDone) {
			isDone = true;
			let fileList = [];
			let customerIdList=[];
			const data = nextProps.detail;
			if (isEdit) {
				this.props.form.setFieldsValue({
					lowCertificate: data.lowCertificate,
					name: data.name,
					address: data.address,
					identityCard: data.identityCard,
					effectiveDate: moment(data.effectiveDate, 'YYYY-MM-DD'),
					expiredDate: moment(data.expiredDate, 'YYYY-MM-DD'),
					waterUseKindId: data.waterUseKindId,
					note: data.note
				});

				if(data.customerList){
					data.customerList.forEach(item => {
						customerIdList.push(item.id)
					})
				}
			}
			data.annexList.forEach(item => {
				fileList.push({
					uid: item.id,
					id: item.id,
					createTime: item.createTime,
					updateTime: item.updateTime,
					relatedId: item.relatedId,
					relatedCode: item.relatedCode,
					originName: item.originName,
					newName: item.newName,
					filePath: item.filePath,
					name: item.originName,
					status: 'done',
					url: `${constants.fileUrl}${item.filePath}${item.newName}`
				});
			});



			this.setState({
				customerIdList,
				fileList,
				customerList: data.customerList
			});
		}
	}

	// 获取列表
	getList = () => {
		const { page, pageSize, searchForm } = this.state;
		this.props.list(Object.assign(searchForm, { page, pageSize }));
	};

	// 获取用户列表
	userList = params => {
		this.props.userList(params);
	};

	// 搜索
	handleSearch = () => {
		this.setState(
			{
				page: 1
			},
			() => this.getList()
		);
	};

	// 重置
	handleReset = () => {
		this.setState(
			{
				page: 1,
				searchForm: {
					cno: '',
					name: '',
					address: '',
					lowCertificate: '',
					status: null,
					applyStatus: null,
					areaId: null,
				}
			},
			() => {
				this.getList();
			}
		);
	};

	// 列表分页
	handlePageChange = page => {
		this.setState(
			{
				page
			},
			() => {
				this.getList();
			}
		);
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState(
			{
				page: 1,
				pageSize: size
			},
			() => {
				this.getList();
			}
		);
	};

	// 新增
	handleAdd = () => {
		isEdit = false;
		this.props.setState({ key: 'visible1', value: true });
	};

	// 查看详情
	handleView = record => {
		isEdit = false;
		isDone = false;
		this.props.getDetail(record.id);
		this.props.setState({ key: 'visible', value: true });
	};

	// 编辑记录
	handleEdit = record => {
		isEdit = true;
		isDone = false;
		this.props.getDetail(record.id);
		this.props.setState({ key: 'visible1', value: true });
	};

	// 删除记录
	handleDel = record => {
		const _this = this;
		Modal.confirm({
			title: '操作确认',
			content: '是否确认删除该记录？',
			okText: '确定',
			cancelText: '取消',
			onOk() {
				_this.props.del(record.id, _this.getList);
			}
		});
	};

	// 批量删除用户
	handleDelUser = () => {
		const { selectedRowKeys } = this.state;
		const { detail, delUser, getDetail } = this.props;
		let params = {
			id:detail.id,
			customerIdList: selectedRowKeys
		};
		// 操作确认
		Modal.confirm({
			title: '批量删除确认',
			content: '是否确认批量删除记录？',
			okText: '确认',
			cancelText: '取消',
			onOk: () => {
				isDone = false;
				delUser(params, this.getList, getDetail, detail.id);
			}
		});
	};

	// 提交新增资料
	handleSubmit = () => {
		this.props.form.validateFields((err, values) => {
			if (!err) {
				const { fileList, customerIdList } = this.state;
				const { lowCertificate, name, address, identityCard, effectiveDate, expiredDate, waterUseKindId, note } = values;
				if (customerIdList === '') {
					message.error('请先选择关联用户');
					return;
				}
				let photos = fileList.map(item => {
					if (item.response) {
						return item.response.data;
					} else {
						return item;
					}
				});
				let params = {
					lowCertificate, // 低保证号
					customerIdList,
					name,
					address,
					identityCard,
					effectiveDate: moment(effectiveDate, 'YYYY-MM-DD').format('YYYY-MM-DD'),
					expiredDate: moment(expiredDate, 'YYYY-MM-DD').format('YYYY-MM-DD'),
					waterUseKindId,
					note,
					photos
				};
				if (isEdit) {
					params = {
						...params,
						id: this.props.detail.id
					};
					this.props.modify(params, this.getList,()=>{
						isEdit = false;
						this.props.form.resetFields();
						this.setState({
							fileList: [],
							customerList: []
						});
					});
				} else {
					this.props.add(params, this.getList);
				}

			}
		});
	};

	// table 行选择回调
	onSelectChange = (selectedRowKeys, selectedRows) => {
		this.setState({ selectedRowKeys, selectedRows });
	};

	// 关闭弹窗
	handleCancel = () => {
		this.setState({
			fileList: [],
			customerList: []
		});
		this.props.setState([
			{ key: 'visible', value: false },
			{ key: 'detail', value: null }
		]);
	};

	// 关闭新增弹窗
	handleCancelModal = () => {
		this.setState({
			fileList: [],
			customerList: []
		});
		this.props.setState([
			{ key: 'visible1', value: false },
			{ key: 'detail', value: null }
		]);
	};

	// 选择关联用户
	handleChoseUser = () => {
		this.userList({
			hno: '',
			cno: '',
			name: '',
			address: '',
			page: 1,
			pageSize: 10
		});
		this.setState({
			show: true
		});
	};

	// 关闭关联用户
	handleClose = () => {
		this.setState({ show: false });
	};

	// 查看大图
	handlePreviewFile = async file => {
		if (!file.url && !file.preview) {
			file.preview = await getBase64(file.originFileObj);
		}

		this.setState({
			previewImage: file.url || file.preview,
			previewVisible: true
		});
	};

	// 变更图片
	handleChangeFile = ({ file, fileList, event }) => {
		this.setState({ fileList });
		// 如果上传文件失败 或者 错误， 则删除该上传文件
		if (file.status === 'error') {
			message.error('上传失败，请稍后重试');
			this.setState({ fileList: fileList.slice(0, -1) });
		}
	};


	getExpireTime = ({time}) => {
		let month=time.getMonth() + 1 //获取月份
    if(month<=6){
		   return	getTime(6)
		}
		if(month>6&&month<=12){
			return	getTime(12)
		}


	};
	// 取消查看大图
	handleCancelPreview = e => {
		e.preventDefault();
		this.setState({ previewVisible: false });
	};

	// 获取关联用户数据
	getAssociatedUser = record => {

		const { detail, addUser, getDetail } = this.props;
		let customerIdList = [];
		record.forEach(item => {
			customerIdList.push(item.id)
		});
		if (isEdit) {
			let params = {
				id: detail.id,
				customerIdList
			}
			addUser(params, getDetail, detail.id,()=>{
				this.setState({
					show: false,
					selectedRowKeys: [],
					selectedRows: [],
				})

			})
		}else{
			this.setState({
				show: false,
				customerList: [...record],
				customerIdList
			})
		}

		isDone = false
	};

	//导出
	export() {
		const { searchForm } = this.state;
		let url = `${process.env.API_ROOT}/api/cm/lowHousehold/exportLowHousehold`;
		http.export(url, searchForm, res => {
			const blob = new Blob([res]);
			const downloadElement = document.createElement('a');
			const href = window.URL.createObjectURL(blob);
			const fileName = '低保户_' + new Date().getTime() + '.xlsx';
			downloadElement.href = href;
			downloadElement.download = fileName;
			document.body.appendChild(downloadElement);
			downloadElement.click();
			document.body.removeChild(downloadElement);
			window.URL.revokeObjectURL(href);
		});
	}

  // 渲染片区树
	_renderTreeLoop = (areas, isShow) => {
		return areas.map(item => {
			if (item.children) {
				return (
					<TreeNode key={item.id} title={item.name} value={item.id} disabled={isShow}>
						{this._renderTreeLoop(item.children, isShow)}
					</TreeNode>
				);
			} else {
				return <TreeNode key={item.id} title={item.name} value={item.id} />;
			}
		});
	};

	// 渲染搜索
	_renderSearchForm = () => {
		const { searchForm } = this.state;
		const { lowCertificate, name, address, cno, status, areaId } = searchForm;
		const { areas } = this.props
		const formItemLayout = {
			labelCol: {
				xs: { span: 6 },
				sm: { span: 6 }
			},
			wrapperCol: {
				xs: { span: 15 },
				sm: { span: 15 }
			}
		};
		return (
			<Form className="ant-advanced-search-form" {...formItemLayout}>
				<Row>
					<Col span={8}>
						<FormItem label={'低保证号:'}>
							<Input
								value={lowCertificate}
								placeholder="请输入低保证号"
								onChange={val => {
									const { searchForm } = this.state;
									searchForm.lowCertificate = val.target.value;
									this.setState({
										...searchForm
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'姓名:'}>
							<Input
								value={name}
								placeholder="请输入姓名"
								onChange={val => {
									const { searchForm } = this.state;
									searchForm.name = val.target.value;
									this.setState({
										...searchForm
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'地址:'}>
							<Input
								value={address}
								placeholder="请输入地址"
								onChange={val => {
									const { searchForm } = this.state;
									searchForm.address = val.target.value;
									this.setState({
										...searchForm
									});
								}}
							/>
						</FormItem>
					</Col>
				</Row>

				<Row>
					<Col span={8}>
						<FormItem label={'用户编号:'}>
							<Input
								value={cno}
								placeholder="请输入用户编号"
								onChange={val => {
									const { searchForm } = this.state;
									searchForm.cno = val.target.value;
									this.setState({
										...searchForm
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'状态:'}>
							<Select
								value={searchForm.status}
								placeholder="请选择状态"
								onChange={val => {
									const { searchForm } = this.state;
									searchForm.status = val;
									this.setState({
										...searchForm
									});
								}}
							>
								{constants.subsistenceStatus.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label="申请状态">
							<Select
								placeholder="请选择"
								value={searchForm.applyStatus}
								onChange={val => {
									const { searchForm } = this.state;
									searchForm.applyStatus = val;
									this.setState({
										...searchForm
									});
								}}
							>
								{APPLY_STATUS_BY_OTHER.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={8}>
						<FormItem label={'所属片区:'}>
							<TreeSelect
								style={{ width: '100%' }}
								value={areaId}
								showSearch
								filterTreeNode={(inputValue, treeNode) => treeNode.props.title.indexOf(inputValue) > -1}
								dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
								placeholder="请选择所属片区"
								allowClear
								treeDefaultExpandedKeys={[100]}
								onChange={value => {
									this.setState({
										searchForm: {
											...searchForm,
											areaId: value
										}
									});
								}}
							>
								{this._renderTreeLoop(areas, false)}
							</TreeSelect>
						</FormItem>
					</Col>
				</Row>
				<Col span={24} align="right">
					<Button className="searchBtn" type="primary" onClick={this.handleSearch}>
						搜索
					</Button>
					<Button className="searchBtn" type="default" onClick={this.handleReset}>
						重置
					</Button>
				</Col>
			</Form>
		);
	};

	_renderOperateButton = () => {
		return (
			<Col>
				<Button type="primary" onClick={this.handleAdd}>
					新增
				</Button>
				<Button className="searchBtn" type="primary" onClick={() => this.export()}>
					<Icon type="download" />
					导出低保户信息
				</Button>
			</Col>
		);
	};

	//添加删除用户
	deleteUser = () => {
		const { selectedRowKeys, customerList } = this.state;
		let newCustomerList = customerList.filter(item => !selectedRowKeys.includes(item.id));
		let newCmId = [];
		newCustomerList.forEach(item => {
			newCmId.push({ customerId: item.id });
		});
		this.setState({ customerList: newCustomerList, selectedRowKeys: [], cmId: newCmId });
	};

	render() {
		const { page, pageSize, previewVisible, previewImage, fileList, show, customerList, loading, selectedRowKeys } = this.state;
		const { datalist, total, visible, visible1, form, waterUseKinds, detail, users } = this.props;
		const { getFieldDecorator, getFieldValue } = form;
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: total => {
				return `共 ${total} 条数据 `;
			},
			onChange: page => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};

		const userColumns = [
			{
				title: '序号',
				dataIndex: 'xuhao',
				width: 80,
				align: 'center',
				render: (text, record, index) => {
					return index + 1;
				}
			},
			{
				title: '用户编号',
				dataIndex: 'cno',
				key: 'cno',
				align: 'center'
			},
			{
				title: '户号',
				dataIndex: 'hno',
				key: 'hno',
				align: 'center'
			},
			{
				title: '用户名称',
				dataIndex: 'name',
				key: 'name',
				align: 'center'
			},
			{
				title: '用户地址',
				dataIndex: 'address',
				key: 'address',
				align: 'center'
			},
			{
				title: '水表类型',
				dataIndex: 'waterMeterType',
				key: 'waterMeterType',
				align: 'center',
				render: (text, record, index) => {
					return text ? text : '--';
				}
			},
			{
				title: '水表种类',
				dataIndex: 'waterMeterKindType',
				key: 'waterMeterKindType',
				align: 'center',
				render: (text, record, index) => {
					return text ? text : '--';
				}
			}
		];
		const columns = [
			{
				title: '序号',
				dataIndex: 'xuhao',
				width: 80,
				align: 'center',
				render: (text, record, index) => {
					return index + 1;
				}
			},
			{
				title: '低保证号',
				dataIndex: 'lowCertificate',
				key: 'lowCertificate',
				align: 'center'
			},
			{
				title: '姓名',
				dataIndex: 'name',
				key: 'name',
				align: 'center'
			},
			{
				title: '地址',
				dataIndex: 'address',
				key: 'address',
				align: 'center'
			},
			{
				title: '身份证号',
				dataIndex: 'identityCard',
				key: 'identityCard',
				align: 'center'
			},
			{
				title: '生效日期',
				dataIndex: 'effectiveDate',
				key: 'effectiveDate',
				align: 'center'
			},
			{
				title: '失效日期',
				dataIndex: 'expiredDate',
				key: 'expiredDate',
				align: 'center'
			},
			{
				title: '用水分类',
				dataIndex: 'waterUseKindName',
				key: 'waterUseKindName',
				align: 'center'
			},

			{
				title: '申请状态',
				dataIndex: 'applyStatus',
				key: 'applyStatus',
				align: 'center',
				render: text => {
					return <Tag color={applyStatusColorMap.get(text)}>{text}</Tag>;
				}
			},
			{
				title: '创建人',
				dataIndex: 'createPersonName',
				key: 'createPersonName',
				align: 'center'
			},
			{
				title: '创建时间',
				dataIndex: 'createTime',
				key: 'createTime',
				align: 'center'
			},
			{
				title: '状态',
				dataIndex: 'status',
				key: 'status',
				align: 'center',
				fixed: 'right',
				width: 80,
				render: text => {
					return <Tag color={lowStatusColorMap.get(text)}>{text}</Tag>;
				}
			},
			{
				title: '操作',
				key: 'operation',
				width: 200,
				align: 'center',
				fixed: 'right',
				render: (text, record, index) => {
					return (
						<span>
							<Button permission={React.$pmn('REVENUE:USER_MANAGEMENT:SUBSISTENCE_MANAGEMENT:VIEW')} title="查看"
								className="btn" type="primary" size="small" icon="eye" onClick={() => this.handleView(record)} />
							<Button permission={React.$pmn('REVENUE:USER_MANAGEMENT:SUBSISTENCE_MANAGEMENT:EDIT')} title="编辑"
								className="btn" type="primary" size="small" icon="form"
								onClick={() => this.handleEdit(record)} />
							<Button permission={React.$pmn('REVENUE:USER_MANAGEMENT:SUBSISTENCE_MANAGEMENT:DEL')} title="删除"
								className="btn" type="primary" size="small" icon="delete"
								onClick={() => this.handleDel(record)} />
							&nbsp;
						</span>
					);
				}
			}
		];
		const uploadButton = (
			<div>
				<Icon type="plus" />
				<div className="ant-upload-text">上传照片</div>
			</div>
		);
		const rowSelection = {
			fixed: true,
			selectedRowKeys,
			onChange: this.onSelectChange
		};
		return (
			<div className="shadow-radius poor">
				<h1>低保户管理</h1>
				<Row>{this._renderSearchForm()}</Row>
				{/*功能入口*/}
				<Row>{this._renderOperateButton()}</Row>
				<Row className="main">
					<Table bordered scroll={{ x: 2300 }} rowKey={() => Math.random()} columns={columns} dataSource={datalist}
						pagination={paginationProps} />
				</Row>
				{/* 查看详情 */}
				{!isEdit && detail ? (
					<Modal className="poorDetail" title="低保户详情" destroyOnClose={true} visible={visible}
						onCancel={this.handleCancel} footer={null}>
						<Row className="detail">
							<Col span={8}>
								<b>低保证号：</b>
								{detail.lowCertificate}
							</Col>
							<Col span={8}>
								<b>姓名：</b>
								{detail.name}
							</Col>
							<Col span={8}>
								<b>地址：</b>
								{detail.address}
							</Col>
							<Col span={8}>
								<b>身份证号：</b>
								{detail.identityCard}
							</Col>
							<Col span={8}>
								<b>生效日期：</b>
								{detail.effectiveDate}
							</Col>
							<Col span={8}>
								<b>失效日期：</b>
								{detail.expiredDate}
							</Col>
							<Col span={8}>
								<b>用水性质：</b>
								{detail.waterUseKindName}
							</Col>
							<Col span={8}>
								<b>创建人：</b>
								{detail.createPersonName}
							</Col>
							<Col span={8}>
								<b>创建时间：</b>
								{detail.createTime}
							</Col>
							<Col span={24}>
								<b>备注：</b>
								{detail.note}
							</Col>
							<Col span={24}>
								<b>一阶梯限制水量：</b>
								{detail.firstLadderLimitWaterAmount}
							</Col>
							<Divider dashed orientation="left">
								关联用户
							</Divider>
							<Table bordered pagination={false} rowKey={() => Math.random()} columns={userColumns}
								dataSource={detail.customerList} />
							<Divider dashed orientation="left">
								照片信息
							</Divider>
							{detail && fileList.length > 0 ? (
								fileList.map((item, index) => {
									return (
										<Col key={index} className="photoInfo" span={8} align="center">
											<Img src={item.url} alt="" />
										</Col>
									);
								})
							) : (
									<NoData text="暂无照片信息" />
								)}
						</Row>
						<Row>
							<Col align="center">
								<Button type="default" onClick={this.handleCancel}>
									关闭
								</Button>
							</Col>
						</Row>
					</Modal>
				) : null}

				<Modal className="poorDetail" title={`${isEdit ? '编辑低保户信息' : '新增低保户'}`} destroyOnClose={true} visible={visible1}
					onCancel={this.handleCancelModal} footer={null} destroyOnClose={true}>
					<Spin spinning={isEdit && !detail}>
						<Form {...formItemLayout}>
							{/*低保证号*/}
							<Col span={8}>
								<FormItem label="低保证号：">
									{getFieldDecorator('lowCertificate', {
										rules: [
											{
												required: true,
												message: '低保证号必填'
											}
										]
									})(<Input placeholder="请输入低保证号" />)}
								</FormItem>
							</Col>
							{/*姓名*/}
							<Col span={8}>
								<FormItem label="姓名：">
									{getFieldDecorator('name', {
										rules: [
											{
												required: true,
												message: '姓名必填'
											}
										]
									})(<Input placeholder="请输入姓名" />)}
								</FormItem>
							</Col>
							{/*地址*/}
							<Col span={8}>
								<FormItem label="地址：">
									{getFieldDecorator('address', {
										rules: [
											{
												required: true,
												message: '地址必填'
											}
										]
									})(<Input placeholder="请输入地址" />)}
								</FormItem>
							</Col>
							{/*身份证号*/}
							<Col span={8}>
								<FormItem label="身份证号：">
									{getFieldDecorator('identityCard', {
										rules: [
											{
												required: true,
												message: '身份证号必填'
											}
										]
									})(<Input placeholder="请输入身份证号" />)}
								</FormItem>
							</Col>
							{/*生效日期*/}
							<Col span={8}>
								<FormItem label="生效日期：">
									{getFieldDecorator('effectiveDate', {
										initialValue: moment(),
										rules: [
											{
												required: true,
												message: '生效日期必选'
											},
											{
												validator: (rule, value, callback) => {
													callback();
												}
											}
										]
									})(<DatePicker format={'YYYY-MM-DD'} placeholder="请选择生效日期"  disabled  />)}
								</FormItem>
							</Col>
							{/*失效日期*/}
							<Col span={8}>
								<FormItem label="失效日期：">
									{getFieldDecorator('expiredDate', {
										initialValue: moment(this.getExpireTime({time:new Date()})),
										rules: [
											{
												required: true,
												message: '失效日期必选'
											},
											{
												validator: (rule, value, callback) => {
													const start = getFieldValue('effectiveDate');
													if (!start) {
														callback('请先选择生效日期');
													}
													let st = new Date(start).getTime();
													let ed = new Date(value).getTime();
													if (st >= ed) {
														callback('失效日期必须大于生效日期');
													} else {
														callback();
													}
												}
											}
										]
									})(<DatePicker format={'YYYY-MM-DD'} placeholder="请选择失效日期"  disabled={isEdit?false:true}  />)}
								</FormItem>
							</Col>
							{/*用水性质*/}
							<Col span={8}>
								<FormItem label="用水分类：">
									{getFieldDecorator(
										'waterUseKindId',
										detail ? { rules: [{ required: true, message: '用水性质必选' }] }
											: { rules: [{ required: true, message: '用水性质必选' }], initialValue: 676218895941632 }
									)(
										<Select placeholder="请选择用水性质">
											{waterUseKinds.map((item, index) => {
												return (
													<Option key={index} value={item.value}>
														{item.label}
													</Option>
												);
											})}
										</Select>
									)}
								</FormItem>
							</Col>
							{/*备注*/}
							<Col span={8}>
								<FormItem label="备注：">
									{getFieldDecorator('note', {
										rules: []
									})(<Input placeholder="请输入备注信息" />)}
								</FormItem>
							</Col>
							<Divider dashed orientation="left">
								附件
								<p>{isEdit?'':'需要上传低保证原件以及持证人身份证原件'}</p>
							</Divider>
							{/*附件*/}
							<Upload
								headers={{
									token: localStorage.getItem('token')
								}}
								accept={`.jpg,.png,.jpeg,.bmp,.gif`}
								action={`${process.env.API_ROOT}/api/upload/file/low`}
								listType="picture-card"
								fileList={fileList}
								onPreview={this.handlePreviewFile}
								onChange={this.handleChangeFile}
							>
								{fileList.length >= 5 ? null : uploadButton}
							</Upload>
							<Divider dashed orientation="left">
								关联用户
							</Divider>
							{
								isEdit ?
									<Fragment>
										<Button type='primary' className='addUserBtn' onClick={this.handleChoseUser}>新增</Button>
										<Button type='primary' className='addUserBtn' onClick={this.handleDelUser}>批量删除</Button>
									</Fragment> :
									<Fragment>
										<Button type='primary' className='addUserBtn' onClick={this.handleChoseUser}>选择关联用户</Button>
										<Button type='primary' className='addUserBtn' onClick={this.deleteUser}>批量删除</Button>
									</Fragment>
							}
							{/*关联用户*/}
							<Table
								bordered
								columns={userColumns}
								rowKey={(record) => record.id}
								rowSelection={rowSelection}
								dataSource={customerList} />
							<Col span={24} align="center">
								<Button type="primary" onClick={this.handleSubmit}>
									确定
								</Button>
								<Button type="default" onClick={this.handleCancelModal}>
									取消
								</Button>
							</Col>
						</Form>
					</Spin>
				</Modal>
				<Modal visible={previewVisible} footer={null} onCancel={this.handleCancelPreview}>
					<img alt="example" style={{ width: '100%' }} src={previewImage} />
				</Modal>
				<AssociatedUsers radio={true} visible={show} data={users} onCancel={this.handleClose}
					onSubmit={this.getAssociatedUser} handleList={this.userList} />
			</div>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('subsistence');
	return {
		fields: data.fields,
		datalist: data.datalist, // 列表数据
		total: data.total, // 总条数
		visible: data.visible, // 是否显示弹窗
		visible1: data.visible1, // 是否显示弹窗
		detail: data.detail, // 获取详情
		waterUseKinds: data.waterUseKind,
		users: data.users, // 用户列表
		areas: data.areas, // 片区树
	};
};
const mapDispatchToProps = dispatch => ({
	setState: data => dispatch(actionCreators.setState(data)), // 设置redux值
	list: data => dispatch(actionCreators.list(data)), // 获取列表
	getDetail: data => dispatch(actionCreators.detail(data)), // 获取详情
	add: (data, list) => dispatch(actionCreators.add(data, list)), // 新增记录
	del: (data, list) => dispatch(actionCreators.del(data, list)), // 删除记录
	modify: (data, list,operation) => dispatch(actionCreators.modify(data, list,operation)), // 修改记录
	waterUseKind: () => dispatch(actionCreators.waterUseKind()), // 获取用水性质
	userList: data => dispatch(actionCreators.userList(data)), // 获取用户列表
	delUser: (data, list, callback, id) => dispatch(actionCreators.delUser(data, list, callback, id)), // 批量删除用户
	addUser: (data, callback, id,operation) => dispatch(actionCreators.addUser(data, callback, id,operation)), // 批量删除用户
	areaList: data => dispatch(actionCreators.areaList()), // 获取片区
});
export default connect(mapStateToProps, mapDispatchToProps)(Form.create()(Index));
