import http from '$http';
import * as actionTypes from './constants';
import { message } from 'antd';
import api from './api';

// 数据回填
const payload = (type, data) => ({ type, data });

// 组件操作redux数据状态
const setState = data => ({ type: actionTypes.SET_STATE, data });

// 获取列表
const list = data => {
	return async dispatch => {
		const res = await http.post(api.list, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.LIST_RECORD, res.data))
		}
	}
};

// 获取详情
const detail = data => {
	return async dispatch => {
		const res = await http.restGet(api.detail, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.DETAIL_RECORD, res.data))
		}
	}
};

// 新增
const add = (data, list) => {
	return async dispatch => {
		const res = await http.post(api.add, data);
		if (res.code === 0) {
			message.success('添加成功');
			list();
			dispatch(payload(actionTypes.ADD_RECORD))
		}
	}
};

// 删除
const del = (data, list) => {
	return async dispatch => {
		const res = await http.del(api.del, data);
		if (res.code === 0) {
			message.success('修改成功');
			list();
			dispatch(payload(actionTypes.DEL_RECORD))
		}
	}
};

// 修改
const modify = (data, list,operation) => {
	return async dispatch => {
		const res = await http.post(api.modify, data);
		if (res.code === 0) {
			message.success('修改成功');
			list();
			operation();
			dispatch(payload(actionTypes.MODIFY_RECORD))
		}
	}
};

const waterUseKind = () => {
	return async dispatch => {
		const res = await http.get(api.waterusekind);
		if (res.code === 0) {
			dispatch(payload(actionTypes.WATER_USE_KIND, res.data))
		}
	}
};

// 客户列表
const userList = data => {
	return async dispatch => {
		const res = await http.post(api.users, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_USER_RECORD, res.data));
		}
	}
};

// 添加客户
const addUser = (data, callback, id,operation) => {
	return async dispatch => {
		const res = await http.post(api.addUser, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.ADD_USER_RECORD));
			callback(id);
			operation();
		}
	}
};

// 删除/批量删除客户
const delUser = (data, list, callback, id) => {
	return async dispatch => {
		const res = await http.post(api.delUser, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.DEL_USER_RECORD));
			callback(id);
			list();
		}
	}
};

// 获取片区
const areaList = () => {
	return async dispatch => {
		const res = await http.get(api.area);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_AREAS_RECORD, res.data));
		}
	};
};

export { setState, list, detail, add, modify, del, waterUseKind, userList, addUser, delUser, areaList };
