import React, { Component, Fragment } from 'react';
import { Button, Form, Table, Modal, Tag } from 'antd';
import { actionCreators } from '../store';
import { connect } from 'react-redux';
import {userStatusColorMap} from '@/constants/colorStyle';
class SameHouseList extends Component {

	constructor(props) {
		super(props);
		this.state = {
			visible: false,
		};
	}

	//打开弹窗并查询同户下所有数据
	handleOpenModal(hno) {
		this.props.listSameHnoByHno(hno);
		this.openModal(true);
	}

	//打开弹窗
	openModal(visible) {
		this.setState({ visible: visible });
	};

	render() {
		const { hno, customerList } = this.props;
		const { visible } = this.state;
		const columns = [
			{
				title: '用户编号',
				dataIndex: 'cno',
				align: 'center'
			},
			{
				title: '用户名称',
				dataIndex: 'name',
				align: 'center'
			},
			{
				title: '所属片区',
				dataIndex: 'city',
				align: 'center'
			},
			{
				title: '用户地址',
				dataIndex: 'address',
				align: 'center'
			},
			{
				title: '用水性质',
				dataIndex: 'waterUseKindName',
				align: 'center'
			},
			{
				title: '水表编号',
				dataIndex: 'waterMeterNo',
				align: 'center'
			},
			{
				title: '水表厂家',
				dataIndex: 'waterMeterManufacturer',
				align: 'center'
			},
			{
				title: '水表类型',
				dataIndex: 'waterMeterType',
				align: 'center'
			},
			{
				title: '用户状态',
				dataIndex: 'status',
				align: 'center',
				render: text => {
					return (
						<Tag color={userStatusColorMap.get(text)}>
							{text}
						</Tag>
					);
				}
			},
			{
				title: '用户余额',
				dataIndex: 'accountBalance',
				align: 'center'
			},
			{
				title: '是否共享阶梯',
				dataIndex: 'shareLadder',
				key: 'shareLadder',
				render: (text) => {
					return text ? '是' : '否'
				}
			},
		];
		return (
			<Fragment>

				<Button title="查看同户详情" className="btn" type="primary" size="small" icon="eye"
					onClick={() => this.handleOpenModal(hno)} />

				<Modal width={'85%'} title="同户列表" visible={visible} onCancel={() => this.openModal(false)} footer={null}>
					<Table rowKey={() => Math.random()} bordered columns={columns} dataSource={customerList} pagination={false} />
				</Modal>

			</Fragment>

		);
	}
}

const mapStateToProps = state => {
	const data = state.get('sameHousehold');
	return {
		customerList: data.customerList // 同户列表数据
	};
};
const mapDispatchToProps = dispatch => ({
	listSameHnoByHno: data => dispatch(actionCreators.listSameHnoByHno(data)) //获取用户
});
export default connect(
	mapStateToProps,
	mapDispatchToProps
)(Form.create()(SameHouseList));
