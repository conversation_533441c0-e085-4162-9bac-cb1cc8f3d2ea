import React, { Component, Fragment} from 'react';
import { withRouter } from 'react-router-dom';
import { actionCreators } from './store';
import { connect } from 'react-redux';
import http from '$http';
import {
	TreeSelect,
	InputNumber,
	Button,
	Col,
	DatePicker,
	Form,
	Input,
	Row,
	Select,
	Table,
	Modal,
	Cascader,
	message,
	Descriptions,
	Tag
} from 'antd';
import { constants } from '$utils';
import { APPLY_STATUS_BY_OTHER } from '@/constants/order';
import { WATER_USE_KIND_CHANGE_TYPE } from '@/constants/customer'
import './index.scss';
import { applyStatusColorMap } from '@/constants/colorStyle';


const FormItem = Form.Item;
const { Option } = Select;
const ButtonGroup = Button.Group;
const { RangePicker } = DatePicker;
const { TreeNode } = TreeSelect;
const InputGroup = Input.Group;
const { confirm } = Modal;
const {TextArea } = Input
const defaultSearchForm = {
	cno : null, // 用户编号
  updateUid : null, // 修改人
  updateTimeStart : null, // 开始修改时间
  updateTimeEnd : null,// 结束修改时间
  applyStatus : null,// 申请状态
  changeType : null, // 变更类型(高变低、底边高、价格不变)
	areaId: null, // 片区
};

class Index extends Component {
	constructor(props) {
		super(props);
		this.state = {
			title: '',
			page: 1,
			pageSize: 10,
			searchForm: defaultSearchForm,
			expand: false,
			rangePickerStart: null,
			rangePickerEnd: null,
		};
	}

	componentDidMount() {
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields && isTags) {
			this.setState({
				...fields.state
			}, () => {
				this.props.form.setFieldsValue({
					...fields.form
				});
				// React.$goWhere('remote', this.props);
			});
		} else {
			this.props.areaList(); // 获取片区树
			this.props.getUpdateUserSelect();
		}
		document.addEventListener('keypress', this.handleEnterKey)
	}

	componentWillUnmount() {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			});
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			});
		}
		document.removeEventListener("keypress", this.handleEnterKey)
	}
	// 回车事件
	handleEnterKey = (e) => {
		if (e.keyCode === 13) {
			this.getList()
		}
	}
	// 获取列表
	getList = () => {
		const { page, pageSize, searchForm } = this.state;
		let params = {
			page,
			pageSize,
			...searchForm
		};
		this.props.list(params);
	};

	// 搜索
	handleSearch = () => {
		this.setState({
			page: 1
		}, () => {
			this.getList();

		});
	};

	// 重置搜索
	handleReset = () => {
		this.setState({
			page: 1,
			searchForm: defaultSearchForm,
			rangePickerStart: null,
			rangePickerEnd: null
		},()=>{
		});
	};

	// 列表分页
	handlePageChange = (page) => {
		this.setState(
			{
				page
			},
			() => {
				this.getList();
			}
		);
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState(
			{
				page: 1,
				pageSize: size
			},
			() => {
				this.getList();
			}
		);
	};

	// 日期选择
	getDate = (date, dateString, startName, endName) => {
		if (dateString.length === 2) {
			var object = {}
			object[startName] = dateString[0]
			object[endName] = dateString[1]
			this.setState({
				rangePickerStart: date[0],
				rangePickerEnd: date[1],
				searchForm: { ...this.state.searchForm, ...object }
			});
		}
	};

	// 渲染片区树
	_renderTreeLoop = (areas, isShow) => {
		return areas.map(item => {
			if (item.children) {
				return (
					<TreeNode key={item.id} title={item.name} value={item.id} disabled={isShow}>
						{this._renderTreeLoop(item.children, isShow)}
					</TreeNode>
				);
			} else {
				return <TreeNode key={item.id} title={item.name} value={item.id} />;
			}
		});
	};

	// 渲染搜索
	_renderSearchForm = () => {
		const { searchForm, expand, rangePickerStart, rangePickerEnd } = this.state;
		const { areaId } = searchForm;
		const { updateUsers, areas } = this.props;
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					<Col span={8}>
						<FormItem label={'用户编号:'}>
							<Input
								placeholder="请输入"
								value={searchForm.cno}
								onChange={v => {
									const { searchForm } = this.state;
									this.setState({
										searchForm: {
											...searchForm,
											cno: v.target.value
										}
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label="修改人：">
							<Select
								allowClear
								value={searchForm.updateUid}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, updateUid: v } })
								}}
							>
								<Option value={null}>请选择</Option>
								{ updateUsers.map((item, index) => {
									return (<Option value={item.value}>{item.label}</Option>);
								})}</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'修改时间:'}>
							<RangePicker value={[ rangePickerStart, rangePickerEnd ]} onChange={(d, ds) => {this.getDate(d, ds, 'updateTimeStart', 'updateTimeEnd')}}
													 placeholder={['开始时间', '结束时间']}/>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={8}>
						<FormItem label="申请状态：">
							<Select
								allowClear
								value={searchForm.applyStatus}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, applyStatus: v } })
								}}
							>
								{APPLY_STATUS_BY_OTHER.map((item, index) => {
									return (<Option key={index} value={item.value}>{item.label}</Option>);
								})}</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'变更类型:'}>
							<Select
								placeholder="请选择"
								value={searchForm.changeType}
								onChange={v => {
									const { searchForm } = this.state;
									this.setState({
										searchForm: {
											...searchForm,
											changeType: v
										}
									});
								}}
							>
								{
									WATER_USE_KIND_CHANGE_TYPE.map((item, index) => {
										return <Option key={index} value={item.value}>{item.label}</Option>;
									})
								}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'所属片区:'}>
							<TreeSelect
								style={{ width: '100%' }}
								value={areaId}
								showSearch
								filterTreeNode={(inputValue, treeNode) => treeNode.props.title.indexOf(inputValue) > -1}
								dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
								placeholder="请选择所属片区"
								allowClear
								treeDefaultExpandedKeys={[100]}
								onChange={value => {
									this.setState({
										searchForm: {
											...searchForm,
											areaId: value
										}
									});
								}}
							>
								{this._renderTreeLoop(areas, false)}
							</TreeSelect>
						</FormItem>
					</Col>
				</Row>

				<Row>
					<Col span={24} align="right" >
						<Button className="searchBtn" type="primary" onClick={this.handleSearch}>
							搜索
						</Button>
						<Button className="searchBtn" type="default" onClick={this.handleReset}>
							重置
						</Button>
						<Button type="link" onClick={this.showMoreSearch}>
								{this.state.expand ? '关闭高级搜索' : '高级搜索'}
						</Button>
					</Col>
				</Row>
			</Form>
		);
	};

	// 显示高级搜索
	showMoreSearch = () => {
			this.setState({
					expand: !this.state.expand
			});
	};

	render() {
		const { page, pageSize} = this.state;
		const { dataList, total, form} = this.props;
		const { getFieldDecorator } = form;
		const columns = [
			{
				title: '用户编号',
				dataIndex: 'cno',
				key: 'cno',
				align: 'center'
			},
			{
				title: '用户名称',
				dataIndex: 'customerName',
				key: 'customerName',
				align: 'center'
			},
			{
				title: '片区',
				dataIndex: 'areaName',
				key: 'areaName',
				align: 'center'
			},
			{
				title: '用户地址',
				dataIndex: 'address',
				key: 'address',
				align: 'center',
				//sorter: (a, b) => a.address && a.address.localeCompare(b.address)
			},
			{
				title: '原用水分类',
				dataIndex: 'oldWaterUseKindName',
				key: 'oldWaterUseKindName',
				align: 'center'
			},
			{
				title: '原用水分类单价',
				dataIndex: 'oldUnitPrice',
				key: 'oldUnitPrice',
				align: 'center'
			},
			{
				title: '新用水分类',
				dataIndex: 'newWaterUseKindName',
				key: 'newWaterUseKindName',
				align: 'center'
			},
			{
				title: '新用水分类单价',
				dataIndex: 'newUnitPrice',
				key: 'newUnitPrice',
				align: 'center'
			},
			{
				title: '申请人',
				dataIndex: 'applyUserName',
				key: 'applyUserName',
				align: 'center'
			},
			{
				title: '申请时间',
				dataIndex: 'applyTime',
				key: 'applyTime',
				align: 'center'
			},
			{
				title: '申请状态',
				dataIndex: 'applyStatus',
				key: 'applyStatus',
				align: 'center',
				width: 80,
				fixed: 'right',
				render: text => {
					return (
						<Tag color={applyStatusColorMap.get(text)}>
							{text}
						</Tag>
					);
				}
			}
		];
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: (total) => {
				return`共 ${total} 条数据 `;
			},
			onChange: (page) => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		return (
			<div className="shadow-radius">
				{/* 页面标题 */}
				<Row>
					<Col>
						<h1>用水分类变更记录</h1>
					</Col>
				</Row>
				<Row>{this._renderSearchForm()}</Row>
				<Row className="search-button buttonGroup">
				</Row>
				<Row className="main">
					<Table
						rowKey={data => data.id}
						bordered
						columns={columns}
						scroll={{ x: 2800 }}
						dataSource={dataList}
						pagination={paginationProps}
					/>
				</Row>
			</div>
		);
	}
}

const mapStateToProps = (state) => {
	const data = state.get('waterUseKindChange');
	return {
		fields: data.fields,
		dataList: data.dataList, // 列表数据
		total: data.total, // 总条数
		updateUsers: data.updateUsers, // 历史处理人
		areas: data.areas,  // 片区树
	};
};
const mapDispatchToProps = (dispatch) => ({
	setState: (data) => dispatch(actionCreators.setState(data)), // 设置redux值
	list: (data) => dispatch(actionCreators.list(data)), // 获取列表
	getUpdateUserSelect:() => dispatch(actionCreators.getUpdateUserSelect()),// 获取历史更新人
	areaList: data => dispatch(actionCreators.getAreaSelect()), // 获取片区树
});
export default connect(mapStateToProps, mapDispatchToProps)(Form.create()(withRouter(Index)));
