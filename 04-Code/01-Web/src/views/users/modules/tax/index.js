import React, { Component, Fragment } from 'react';
import { actionCreators } from './store';
import { connect } from 'react-redux';
import http from '$http';
import { Button, Col, Divider, Form, Icon, Input, message, Modal, Radio, Row, Select, Table, Tag } from 'antd';
import AssociatedUsers from '$components/associatedUsers';
import { constants } from '$utils';
import { taxStatusColorMap } from '@/constants/colorStyle';

import './index.scss';
import { TreeSelect } from 'antd/lib/index';

const { TreeNode } = TreeSelect;
const FormItem = Form.Item;
const { Option } = Select;

let isDone = false;

const defaultSearchForm = {
	vatNumber: null,
	name: null,
	address: null,
	bankAccount: null,
	cno: null,
	status: null,
	generalTaxpayer: null,
	hno: null,
	areaId: null
};

class Index extends Component {

	constructor(props) {
		super(props);
		this.state = {
			searchForm: defaultSearchForm,
			page: 1,
			pageSize: 10,
			show: false, // 显示关联用户弹窗
			modalType: 'add',
			title: '新增增值税资料',
			customerList: [], // 关联用户列表
			cmId: [], // 关联用户ID
			selectedRowKeys: [],
			newCustomerList: [],
			selectedRows: [],
			visibleEmail: false,
			record: null
		};
	}


	componentDidMount() {
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields && isTags) {
			this.setState({
				...fields.state
			}, () => {
				this.props.form.setFieldsValue({
					...fields.form
				});
			});
		} else {
			this.props.areaList(); // 获取片区树
			this.getList();
		}
	}

	componentWillUnmount() {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			});
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			});
		}
	}

	getList = () => {
		const { page, pageSize, searchForm } = this.state;
		let params = {
			page,
			pageSize,
			...searchForm
		};
		this.props.list(params);
	};

	// 获取增值税用户列表
	userList = params => {
		this.props.userList(params);
	};

	// table 行选择回调
	onSelectChange = (selectedRowKeys, selectedRows) => {
		this.setState({ selectedRowKeys, selectedRows });
	};

	// 获取关联用户数据
	getAssociatedUser = record => {
		const { modalType, customerList, newCustomerList } = this.state;
		const { detail, addUser, getDetail } = this.props;
		let cmId = [];
		const all = [...customerList, ...record];
		let flag = true;

		all.forEach(item => {
			if (cmId.indexOf(item.id) === -1) {
				newCustomerList.forEach(customer => {
					if (customer.id === item.id) {
						flag = false;
					}
				});
				if (flag) {
					cmId.push(item.id);
					newCustomerList.push(item);
				}
				flag = true;
			}
		});
		this.setState({
			show: false,
			customerList: all,
			newCustomerList,
			cmId
		}, () => {
			if (modalType === 'edit') {
				// cmId.forEach(item => {
				// 	item.vatId = detail.id;
				// });


				// addUser(cmId, getDetail, detail.id);
			}
		});


	};

	// 搜索
	handleSearch = () => {
		this.setState({
			page: 1
		}, () => this.getList());
	};

	// 重置
	handleReset = () => {
		this.setState({ searchForm: Object.assign({}, defaultSearchForm) },
			() => {
				this.getList();
			});
	};

	// 列表分页
	handlePageChange = (page) => {
		this.setState({
			page
		}, () => {
			this.getList();
		});
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState({
			page: 1,
			pageSize: size
		}, () => {
			this.getList();
		});
	};

	// 新增
	handleAdd = () => {
		this.props.form.resetFields();
		this.setState({
			modalType: 'add',
			title: '新增增值税资料',
			customerList: [], // 关联用户列表
			cmId: [], // 关联用户ID
			selectedRowKeys: [],
			selectedRows: []
		}, () => {
			// 设置redux visible 为true 显示弹窗
			this.props.setState([{ key: 'visible', value: true }, { key: 'detail', value: null }]);
		});
	};

	// 查看
	handleView = record => {
		this.setState({
			modalType: 'view',
			title: '查看详情',
			customerList: [], // 关联用户列表
			newCustomerList: [],
			cmId: [], // 关联用户ID
			selectedRowKeys: [],
			selectedRows: []
		}, () => {
			// 设置redux visible 为true 显示弹窗
			this.props.setState([{ key: 'visible', value: true }, { key: 'detail', value: null }]);
			// 获取详情
			this.props.getDetail(record.id, () => {
				const { detail } = this.props;
				this.setState({ newCustomerList: detail.customerList });
			});
		});
	};

	// 编辑
	handleEdit = record => {
		this.props.form.resetFields();
		this.setState({
			modalType: 'edit',
			title: '编辑增值税资料',
			customerList: [], // 关联用户列表
			newCustomerList: [],
			cmId: [], // 关联用户ID
			selectedRowKeys: [],
			selectedRows: []
		}, () => {
			// 设置redux visible 为true 显示弹窗
			this.props.setState([{ key: 'visible', value: true }, { key: 'detail', value: null }]);
			// 获取详情
			this.props.getDetail(record.id, () => {
				const { detail } = this.props;
				this.setState({ newCustomerList: detail.customerList });
			});
		});
	};

	// 删除
	handleDel = record => {
		Modal.confirm({
			title: '删除确认',
			content: '是否确认删除该记录？',
			okText: '确认',
			cancelText: '取消',
			onOk: () => {
				this.props.del(record.id, this.getList);
			}
		});
	};

	// 取消弹窗
	handleCancel = () => {
		this.props.setState([{ key: 'visible', value: false }, { key: 'detail', value: null }]);
		this.setState({ newCustomerList: [] });
	};

	// 选择关联用户
	handleChoseUser = () => {
		this.userList({
			hno: '',
			cno: '',
			name: '',
			address: '',
			page: 1,
			pageSize: 10
		});
		this.setState({
			show: true
		});
	};

	// 关闭关联用户
	handleClose = () => {
		this.setState({
			show: false
		});
	};

	// 批量删除用户
	handleDelUser = () => {
		const { selectedRows, newCustomerList } = this.state;
		let cmId = [];
		let obj = [];

		newCustomerList.forEach(item => {
			if (cmId.indexOf(item.id) == -1) {
				cmId.push(item.id);
				obj.push(item);
			}
		});
		// 操作确认
		Modal.confirm({
			title: '批量删除确认',
			content: '是否确认批量删除记录？',
			okText: '确认',
			cancelText: '取消',
			onOk: () => {
				isDone = false;
				let left = [];
				for (let x in obj) {
					if (!this.arrInclude(selectedRows, obj[x])) {
						left.push(obj[x]);
					}
				}
				this.setState({ newCustomerList: left, selectedRows: [], selectedRowKeys: [], cmId: [] });
			}
		});
	};

	arrInclude = (a, b) => {
		let include = false;
		a.forEach(item => {
			if (item.id == b.id) {
				include = true;
			}
		});
		return include;
	};

	// 提交
	handleSubmit = e => {
		e.preventDefault();
		this.props.form.validateFields((err, values) => {
			if (!err) {
				const { modalType, cmId, newCustomerList } = this.state;
				if (!cmId) {
					message.error('请先选择关联用户');
					return false;
				}
				let params = null;
				let vatCustomers = [];
				newCustomerList.forEach(item => {
					vatCustomers.push({ customerId: item.id });
				});
				if (modalType === 'add') {
					params = {
						vatNumber: values.vatNumber, // 增值税号
						name: values.name, // 姓名
						address: values.address, // 地址
						openBank: values.openBank, // 开户行
						bankAccount: values.bankAccount, // 银行账号
						phone: values.phone, // 电话

						status: values.status === '启用' ? 0 : 1,
						generalTaxpayer: values.generalTaxpayer
					};
					params.vatCustomers = vatCustomers;

					this.props.add(params, this.getList);
				} else {
					params = {
						id: this.props.detail.id, // id
						vatNumber: values.vatNumber, // 增值税号
						name: values.name, // 姓名
						address: values.address, // 地址
						openBank: values.openBank, // 开户行
						bankAccount: values.bankAccount, // 银行账号
						phone: values.phone, // 电话
						status: values.status === '启用' ? 0 : 1,
						generalTaxpayer: values.generalTaxpayer
					};
					params.vatCustomers = vatCustomers;
					this.props.modify(params, this.getList);
				}
				this.setState({
					customerList: [], // 关联用户列表
					newCustomerList: [],
					cmId: [], // 关联用户ID
					selectedRowKeys: [],
					selectedRows: []
				});
			}
		});
	};

	// 修改邮箱弹窗
	updateEmail = record => {
		this.setState({ visibleEmail: true, record: record});
	};

	//渲染修改邮箱弹窗
	_renderUpdateEmail = () => {
		const { form } = this.props;
		const { visibleEmail, record } = this.state;
		const { getFieldDecorator } = form;
		const footer = (
			<Fragment>
				<Button key="submit" type="primary" onClick={() => this.submitUpdateEmail(record)}>提交</Button>
				<Button key="back" onClick={() => this.editModalEmail(false)}>取消</Button>
			</Fragment>
		);
		return (
			<Modal title={'修改用户邮箱'} visible={visibleEmail}
						 onCancel={() => this.setState({visibleEmail:false})}
						 footer={footer}>
				<Form {...constants.formItemLayout}>
					<Row>
						<Col>
							<FormItem label={'用户邮箱:'}>
								{getFieldDecorator('email', {
									initialValue: record && record.email ? record.email : null
								})
								(<Input placeholder="请输入" />)}
							</FormItem>
						</Col>
					</Row>
				</Form>
			</Modal>
		);
	}

	// 修改邮箱提交
	submitUpdateEmail() {
		const { record, newCustomerList } = this.state;
		let param = { id: record.id };
		param.email = this.props.form.getFieldValue('email');
		const all = [...newCustomerList];
		all.forEach(item => {
			if (item.id === record.id) {
				item.email = param.email;
			}
		});
		actionCreators.updateEmail(param).then(res =>{
			if(res.code === 0){
				this.setState({visibleEmail : false, newCustomerList: all});
				this.props.form.resetFields('email');
			}
		})
	}

	//编辑修改弹窗
	editModalEmail(visibleEmail) {
		this.setState({ visibleEmail: visibleEmail });
	}

	//导出
	export() {
		const { searchForm } = this.state;
		let url = `${process.env.API_ROOT}/api/cm/vat/exportVat`;
		http.export(url, searchForm, (res) => {
			const blob = new Blob([res]);
			const downloadElement = document.createElement('a');
			const href = window.URL.createObjectURL(blob);
			const fileName = '开票资料_' + new Date().getTime() + '.xlsx';
			downloadElement.href = href;
			downloadElement.download = fileName;
			document.body.appendChild(downloadElement);
			downloadElement.click();
			document.body.removeChild(downloadElement);
			window.URL.revokeObjectURL(href);
		});
	}

	// 渲染操作按钮
	_renderOperateButton = () => {
		return (
			<Col>
				<Button type='primary' onClick={this.handleAdd}>新增</Button>
				<Button className="searchBtn" type="primary" onClick={() => this.export()}><Icon
					type="download"/>导出开票资料</Button>
			</Col>
		);
	};

	// 渲染片区树
	_renderTreeLoop = (areas, isShow) => {
		return areas.map(item => {
			if (item.children) {
				return (
					<TreeNode key={item.id} title={item.name} value={item.id} disabled={isShow}>
						{this._renderTreeLoop(item.children, isShow)}
					</TreeNode>
				);
			} else {
				return <TreeNode key={item.id} title={item.name} value={item.id}/>;
			}
		});
	};

	// 渲染搜索
	_renderSearchForm = () => {
		const { searchForm } = this.state;
		const { areaId } = searchForm;
		const { areas } = this.props;
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					<Col span={8}>
						<FormItem label={'税号:'}>
							<Input
								value={searchForm.vatNumber}
								placeholder="请输入税号"
								onChange={val => {
									searchForm.vatNumber = val.target.value;
									this.setState({
										...searchForm
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'用户名称:'}>
							<Input
								value={searchForm.name}
								placeholder="请输入姓名"
								onChange={(v) => {
									this.setState({
										searchForm: { ...searchForm, name: v.target.value }
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'地址:'}>
							<Input
								value={searchForm.address}
								placeholder="请输入地址"
								onChange={val => {
									searchForm.address = val.target.value;
									this.setState({
										...searchForm
									});
								}}
							/>
						</FormItem>
					</Col>
				</Row>

				<Row>
					<Col span={8}>
						<FormItem label={'银行账号:'}>
							<Input
								value={searchForm.bankAccount}
								placeholder="请输入银行账号"
								onChange={val => {
									searchForm.bankAccount = val.target.value;
									this.setState({
										...searchForm
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'用户编号:'}>
							<Input
								value={searchForm.cno}
								placeholder="请输入用户编号"
								onChange={val => {
									searchForm.cno = val.target.value;
									this.setState({
										...searchForm
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'状态:'}>
							<Select
								placeholder='请选择状态'
								value={searchForm.status}
								onChange={val => {
									searchForm.status = val;
									this.setState({
										...searchForm
									});
								}}
							>
								{
									constants.taxStatus.map((item, index) => {
										return (
											<Option key={index} value={item.value}>{item.label}</Option>
										);
									})
								}
							</Select>
						</FormItem>
					</Col>
				</Row>

				<Row>
					<Col span={8}>
						<FormItem label={'一般纳税人:'}>
							<Select
								placeholder='请选择状态'
								value={searchForm.generalTaxpayer}
								onChange={val => {
									searchForm.generalTaxpayer = val;
									this.setState({
										...searchForm
									});
								}}
							>
								<Option value={null}>全部</Option>
								<Option value={true}>是</Option>
								<Option value={false}>否</Option>
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'户号:'}>
							<Input value={searchForm.hno} placeholder="请输入户号"
										 onChange={val => {
											 this.setState({ searchForm: { ...searchForm, hno: val.target.value } });
										 }}/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'所属片区:'}>
							<TreeSelect
								style={{ width: '100%' }}
								value={areaId}
								showSearch
								filterTreeNode={(inputValue, treeNode) => treeNode.props.title.indexOf(inputValue) > -1}
								dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
								placeholder="请选择所属片区"
								allowClear
								treeDefaultExpandedKeys={[100]}
								onChange={value => {
									this.setState({
										searchForm: {
											...searchForm,
											areaId: value
										}
									});
								}}
							>
								{this._renderTreeLoop(areas, false)}
							</TreeSelect>
						</FormItem>
					</Col>
				</Row>

				<Col span={24} align="right">
					<Button className="searchBtn" type="primary" onClick={this.handleSearch}>搜索</Button>
					<Button className="searchBtn" type="default" onClick={this.handleReset}>重置</Button>
				</Col>
			</Form>
		);
	};

	//添加删除用户
	deleteUser = () => {
		const { selectedRowKeys, customerList } = this.state;
		let newCustomerList = customerList.filter(item => !selectedRowKeys.includes(item.id));
		let newCmId = [];
		newCustomerList.forEach(item => {
			newCmId.push({ customerId: item.id });
		});
		this.setState({ customerList: newCustomerList, selectedRowKeys: [], cmId: newCmId });
	};

	render() {
		const { page, pageSize, title, modalType, customerList, show, selectedRowKeys, newCustomerList } = this.state;
		const { form, datalist, total, visible, detail, users } = this.props;

		const { getFieldDecorator } = form;
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: (total) => {
				return `共 ${total} 条数据 `;
			},
			onChange: (page) => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		const rowSelection = {
			fixed: true,
			selectedRowKeys,
			onChange: this.onSelectChange
		};
		const columns = [
			{
				title: '税号',
				dataIndex: 'vatNumber',
				key: 'vatNumber',
				align: 'center'
			},
			{
				title: '用户名称',
				dataIndex: 'name',
				key: 'name',
				align: 'center'
			},
			{
				title: '地址',
				dataIndex: 'address',
				key: 'address',
				align: 'center'
			},
			{
				title: '开户银行',
				dataIndex: 'openBank',
				key: 'openBank',
				align: 'center'
			},
			{
				title: '银行账号',
				dataIndex: 'bankAccount',
				key: 'bankAccount',
				align: 'center'
			},
			{
				title: '电话',
				dataIndex: 'phone',
				key: 'phone',
				align: 'center'
			},
			{
				title: '一般纳税人',
				dataIndex: 'generalTaxpayer',
				key: 'generalTaxpayer',
				align: 'center',
				render: (text) => {
					return text ? '是' : '否';
				}
			},
			{
				title: '创建人',
				dataIndex: 'createPersonName',
				key: 'createPersonName',
				align: 'center'
			},
			{
				title: '创建时间',
				dataIndex: 'createTime',
				key: 'createTime',
				align: 'center'
			},
			{
				title: '修改状态',
				dataIndex: 'modifyStatus',
				key: 'modifyStatus',
				align: 'center'
			},
			{
				title: '状态',
				dataIndex: 'status',
				key: 'status',
				align: 'center',
				width: 80,
				fixed: 'right',
				render: text => {
					return (
						<Tag color={taxStatusColorMap.get(text)}>
							{text}
						</Tag>
					);
				}
			},
			{
				title: '操作',
				key: 'operation',
				width: 200,
				align: 'center',
				fixed: 'right',
				render: (text, record, index) => {
					return (
						<span>
							<Button permission={React.$pmn('REVENUE:USER_MANAGEMENT:VAT_MANAGEMENT:VIEW')} title='查看' className='btn'
											type="primary" size="small" icon="eye"
											onClick={() => this.handleView(record)}/>
							<Button permission={React.$pmn('REVENUE:USER_MANAGEMENT:VAT_MANAGEMENT:EDIT')} title='编辑' className='btn'
											type="primary" size="small" icon="form"
											onClick={() => this.handleEdit(record)}/>
							<Button permission={React.$pmn('REVENUE:USER_MANAGEMENT:VAT_MANAGEMENT:DEL')} title='删除' className='btn'
											type="primary" size="small" icon="delete"
											onClick={() => this.handleDel(record)}/>
							&nbsp;
						</span>
					);
				}
			}
		];
		const userColumns = [
			{
				title: '用户编号',
				dataIndex: 'cno',
				key: 'cno',
				align: 'center'
			},
			{
				title: '户号',
				dataIndex: 'hno',
				key: 'hno',
				align: 'center'
			},
			{
				title: '用户名称',
				dataIndex: 'name',
				key: 'name',
				align: 'center'
			},
			{
				title: '用户地址',
				dataIndex: 'address',
				key: 'address',
				align: 'center'
			},
			{
				title: '用户邮箱',
				dataIndex: 'email',
				key: 'email',
				align: 'center'
			},
			{
				title: '水表类型',
				dataIndex: 'waterMeterType',
				key: 'waterMeterType',
				align: 'center'
			},
			{
				title: '水表种类',
				dataIndex: 'waterMeterKindType',
				key: 'waterMeterKindType',
				align: 'center'
			}
		]; // 关联用户table

		const userColumnsForEditEmail = [
			{
				title: '用户编号',
				dataIndex: 'cno',
				key: 'cno',
				align: 'center'
			},
			{
				title: '户号',
				dataIndex: 'hno',
				key: 'hno',
				align: 'center'
			},
			{
				title: '用户名称',
				dataIndex: 'name',
				key: 'name',
				align: 'center'
			},
			{
				title: '用户地址',
				dataIndex: 'address',
				key: 'address',
				align: 'center'
			},
			{
				title: '用户邮箱',
				dataIndex: 'email',
				key: 'email',
				align: 'center',
				render: (text, record) => {
					return (
						<div>
							{record && record.email}
							<Icon type="edit" style={{ cursor: 'pointer', textAlign: 'left', color: 'blue' }}
										onClick={() => this.updateEmail(record)} />
						</div>
					);
				}
			},
			{
				title: '水表类型',
				dataIndex: 'waterMeterType',
				key: 'waterMeterType',
				align: 'center'
			},
			{
				title: '水表种类',
				dataIndex: 'waterMeterKindType',
				key: 'waterMeterKindType',
				align: 'center'
			}
		]; // 关联用户table，可修改邮箱

		return (
			<div className="shadow-radius reload">
				<h1>开票资料管理</h1>
				<Row>{this._renderSearchForm()}</Row>
				<Row>{this._renderOperateButton()}</Row>
				<Row>{this._renderUpdateEmail()}</Row>
				<Row className='main'>
					<Table
						bordered
						scroll={{ x: 2300 }}
						rowKey={() => Math.random()}
						columns={columns}
						dataSource={datalist}
						pagination={paginationProps}
					/>
				</Row>
				<Modal className='taxModal' title={title} visible={visible} onCancel={this.handleCancel} footer={null}>
					<Row>
						<Form {...constants.formItemLayout}>
							<Col span={8}>
								<FormItem label='税号：'>
									{
										modalType === 'view' ? detail && detail.vatNumber : getFieldDecorator('vatNumber', {
											initialValue: modalType === 'edit' ? detail && detail.vatNumber : ''
										})(<Input/>)
									}
								</FormItem>
							</Col>
							<Col span={8}>
								<FormItem label='用户名称：'>
									{
										modalType === 'view' ? detail && detail.name : getFieldDecorator('name', {
											initialValue: modalType === 'edit' ? detail && detail.name : ''
										})(<Input/>)
									}
								</FormItem>
							</Col>
							<Col span={8}>
								<FormItem label='地址：'>
									{
										modalType === 'view' ? detail && detail.address : getFieldDecorator('address', {
											initialValue: modalType === 'edit' ? detail && detail.address : ''
										})(<Input/>)
									}
								</FormItem>
							</Col>
							<Col span={8}>
								<FormItem label='开户银行：'>
									{
										modalType === 'view' ? detail && detail.openBank : getFieldDecorator('openBank', {
											initialValue: modalType === 'edit' ? detail && detail.openBank : ''
										})(<Input/>)
									}
								</FormItem>
							</Col>
							<Col span={8}>
								<FormItem label='银行账号：'>
									{
										modalType === 'view' ? detail && detail.bankAccount : getFieldDecorator('bankAccount', {
											initialValue: modalType === 'edit' ? detail && detail.bankAccount : ''
										})(<Input/>)
									}
								</FormItem>
							</Col>
							<Col span={8}>
								<FormItem label='电话：'>
									{
										modalType === 'view' ? detail && detail.phone : getFieldDecorator('phone', {
											initialValue: modalType === 'edit' ? detail && detail.phone : ''
										})(<Input/>)
									}
								</FormItem>
							</Col>
							<Col span={8}>
								<FormItem label='一般纳税人：'>
									{
										modalType === 'view' ? detail && detail.generalTaxpayer ? '是' : '否' : getFieldDecorator('generalTaxpayer', {
											initialValue: modalType === 'edit' ? detail && detail.generalTaxpayer : false
										})(
											<Radio.Group>
												<Radio value={true}>是</Radio>
												<Radio value={false}>否</Radio>
											</Radio.Group>
										)
									}
								</FormItem>
							</Col>
							<Col span={8}>
								<FormItem label='启用：'>
									{
										modalType === 'view' ? detail && detail.status : getFieldDecorator('status', {
											initialValue: modalType === 'edit' ? detail && detail.status : '启用'
										})(
											<Radio.Group>
												<Radio value="启用">是</Radio>
												<Radio value="禁用">否</Radio>
											</Radio.Group>
										)
									}
								</FormItem>
							</Col>
						</Form>
						<Divider dashed orientation="left">关联用户</Divider>
						{
							modalType === 'view' ? null :
								modalType === 'add' ?
									<Fragment>
										<Button type='primary' className='addUserBtn' onClick={this.handleChoseUser}>选择关联用户</Button>
										<Button type='primary' className='addUserBtn' onClick={this.handleDelUser}>批量删除</Button>
									</Fragment> :
									<Fragment>
										<Button type='primary' className='addUserBtn' onClick={this.handleChoseUser}>新增</Button>
										<Button type='primary' className='addUserBtn' onClick={this.handleDelUser}>批量删除</Button>
									</Fragment>
						}
						{/*关联用户*/}
						{
							modalType === 'view' ?
								<Table
									bordered
									pagination={false}
									rowKey={(record) => record.id}
									rowSelection={rowSelection}
									columns={userColumns}
									dataSource={newCustomerList}
									size={'small'}
								/>
								:
								modalType === 'add' ?
									<Table
										bordered
										pagination={false}
										rowKey={(record) => record.id}
										rowSelection={rowSelection}
										columns={userColumnsForEditEmail}
										dataSource={newCustomerList}
										size={'small'}
									/>
									:
									<Table
										bordered
										pagination={false}
										rowKey={(record) => record.id}
										rowSelection={rowSelection}
										columns={userColumnsForEditEmail}
										dataSource={newCustomerList}
										size={'small'}
									/>
						}
						<Row>
							<Col align='center'>
								{
									modalType === 'view' ? null :
										<Button type='primary' className='btn' onClick={this.handleSubmit}>确认</Button>
								}
								<Button type='default' className='btn' onClick={this.handleCancel}>取消</Button>
							</Col>
						</Row>
					</Row>
				</Modal>
				<AssociatedUsers visible={show} data={users} handleList={this.userList} onCancel={this.handleClose}
												 onSubmit={this.getAssociatedUser}/>
			</div>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('userTax');
	return {
		fields: data.fields,
		datalist: data.dataList, // 列表数据
		total: data.total, // 总条数
		visible: data.visible, // 是否显示弹窗
		detail: data.detail, // 获取详情
		users: data.users, // 增值税用户列表
		areas: data.areas,  // 片区树
		newCustomerList: data.newCustomerList
	};
};
const mapDispatchToProps = dispatch => ({
	setState: data => dispatch(actionCreators.setState(data)), // 设置redux值
	list: data => dispatch(actionCreators.list(data)), // 获取列表
	add: (data, list) => dispatch(actionCreators.add(data, list)), // 新增记录
	modify: (data, list) => dispatch(actionCreators.modify(data, list)), // 修改记录 基础信息
	del: (data, list) => dispatch(actionCreators.del(data, list)), // 删除记录
	getDetail: (data, ops) => dispatch(actionCreators.detail(data, ops)), // 获取详情
	userList: data => dispatch(actionCreators.userList(data)), // 获取用户列表
	delUser: (data, list, callback, id) => dispatch(actionCreators.delUser(data, list, callback, id)), // 批量删除用户
	addUser: (data, callback, id) => dispatch(actionCreators.addUser(data, callback, id)), // 批量删除用户
	areaList: data => dispatch(actionCreators.areaList()), // 获取片区树
	updateEmail: data => dispatch(actionCreators.updateEmail(data)), // 修改用户邮箱
});
export default connect(
	mapStateToProps,
	mapDispatchToProps
)((Form.create())(Index));
