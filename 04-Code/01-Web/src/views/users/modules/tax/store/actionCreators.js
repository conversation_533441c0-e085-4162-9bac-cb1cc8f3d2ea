import http from '$http';
import * as actionTypes from './constants';
import { message } from 'antd';
import api from './api';

// 数据回填
const payload = (type, data) => ({ type, data });

// 组件操作redux数据状态
const setState = data => ({ type: actionTypes.SET_STATE, data });

// 列表
const list = data => {
	return async dispatch => {
		const res = await http.post(api.list, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.LIST_RECORD, res.data));
		}
	};
};

// 获取详情
const detail = (data,ops) => {
	return async dispatch => {
		const res = await http.restGet(api.detail, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.DETAIL_RECORD, res.data));
			ops();
		}
	};
};


// 新增
const add = (data, list) => {
	return async dispatch => {
		const res = await http.post(api.add, data);
		if (res.code === 0) {
			message.success('新增成功！');
			dispatch(setState({key: 'visible', value: false}));
			list()
		}
	}
};

// 删除
const del = (data, list) => {
	return async dispatch => {
		const res = await http.del(api.del, data);
		if (res.code === 0) {
			message.success('删除成功');
			list();
		}
	}
};

// 修改
const modify = (data, list) => {
	return async dispatch => {
		const res = await http.post(api.modify, data);
		if (res.code === 0) {
			message.success('修改成功');
			dispatch(payload(actionTypes.MODIFY_RECORD));
			list();
		}
	}
};

// 添加增值税客户
const addUser = (data, callback, id) => {
	return async dispatch => {
		const res = await http.post(api.addUser, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.ADD_USER_RECORD));
			callback(id);
		}
	}
};

// 删除/批量删除增值税客户
const delUser = (data, list, callback, id) => {
	return async dispatch => {
		const res = await http.post(api.delUser, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.DEL_USER_RECORD));
			callback(id);
			list();
		}
	}
};

// 增值税用户列表
const userList = data => {
	return async dispatch => {
		const res = await http.post(api.users, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_USER_RECORD, res.data));
		}
	}
};

// 获取片区列表
const areaList = () => {
	return async dispatch => {
		const res = await http.get(api.area);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_AREAS_RECORD, res.data));
		}
	};
};

// 修改用户邮箱
const updateEmail = data => {
	return http.post(api.updateEmail, data);
};

export {
	setState,
	list,
	detail,
	add,
	del,
	modify,
	addUser,
	delUser,
	userList,
	areaList,
	updateEmail
};
