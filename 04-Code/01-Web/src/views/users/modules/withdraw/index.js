import React, { Component } from 'react';
import { actionCreators } from './store';
import { connect } from 'react-redux';
import http from '$http';
import { Modal, Table, Form, Select, Row, Col, Input, Button, DatePicker, Icon, Tag } from 'antd';
import { constants } from '$utils';
import { WITHDRAW_STATUS } from './constants';
import './index.scss';
import { withdrawStatusColorMap } from '@/constants/colorStyle';
const FormItem = Form.Item;
const { Option } = Select;
const { RangePicker } = DatePicker;

const defaultSearchForm = {
	cno: '',
	status: null,
	withdrawStartTime: null,
	withdrawEndTime: null,
	startTime: null,
	endTime: null
};

class Index extends Component {

	constructor(props) {
		super(props);
		this.state = {
			expand: false, // 搜索项过多的时候，隐藏部分
			page: 1,
			pageSize: 10,
			searchForm: defaultSearchForm,
		};
	}

	componentDidMount() {
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields && isTags) {
			this.setState({
				...fields.state
			}, () => {
				this.props.form.setFieldsValue({
					...fields.form
				})
			})
		} else {
			this.getList();
		}
	}

	componentWillUnmount() {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			})
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			})
		}
	}

	// 获取列表
	getList = () => {
		const { page, pageSize, searchForm } = this.state;
		this.props.list(Object.assign({ ...searchForm }, { page, pageSize }));
	};

	// 列表分页
	handlePageChange = page => {
		this.setState(
			{
				page
			},
			() => {
				this.getList();
			}
		);
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState(
			{
				page: 1,
				pageSize: size
			},
			() => {
				this.getList();
			}
		);
	};

	// 搜索功能
	handleSearch = () => {
		this.setState(
			{
				page: 1
			},
			() => {
				this.getList();
			}
		);
	};

	// 重置搜索
	handleReset = () => {
		this.props.form.setFieldsValue({ rangPicker: [undefined, undefined] })
		this.props.form.setFieldsValue({ withdrawPicker: [undefined, undefined] })
		this.setState(
			{
				searchForm: Object.assign({}, defaultSearchForm)
			},
			() => {
				this.getList();
			}
		);
	};

	// 确认提现
	handleWithdraw = record => {
		const _this = this;
		Modal.confirm({
			title: '操作确认',
			content: '是否确认该用户已提现？',
			okText: '确定',
			cancelText: '取消',
			onOk() {
				_this.props.withdraw(record.id, _this.getList);
			}
		});
	};

	//申请时间
	getOrderDate = (date, dateString) => {
		if (dateString.length === 2) {
			this.setState({
				searchForm: { ...this.state.searchForm, startTime: dateString[0], endTime: dateString[1] }
			});
		}
	};

	//提现时间
	getWithdrawDate = (date, dateString) => {
		if (dateString.length === 2) {
			this.setState({
				searchForm: { ...this.state.searchForm, withdrawStartTime: dateString[0], withdrawEndTime: dateString[1] }
			});
		}
	};

	//导出
	export() {
		const { searchForm } = this.state;
		let url = `${process.env.API_ROOT}/api/cm/customer/withdraw/exportCustomerWithdraw`;
		http.export(url, searchForm, (res) => {
			const blob = new Blob([res]);
			const downloadElement = document.createElement('a');
			const href = window.URL.createObjectURL(blob);
			const fileName = '提现记录_' + new Date().getTime() + '.xlsx';
			downloadElement.href = href;
			downloadElement.download = fileName;
			document.body.appendChild(downloadElement);
			downloadElement.click();
			document.body.removeChild(downloadElement);
			window.URL.revokeObjectURL(href);
		});
	}

	// 渲染搜索
	_renderSearchForm = () => {
		const { getFieldDecorator } = this.props.form;
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					<Col span={8}>
						<FormItem label={'客户编号:'}>
							<Input
								placeholder="请输入客户编号"
								value={this.state.searchForm.cno}
								onChange={v => {
									this.setState({
										searchForm: { ...this.state.searchForm, cno: v.target.value }
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'提现状态:'}>
							<Select
								placeholder="请选择"
								value={this.state.searchForm.status}
								onChange={v => {
									this.setState({
										searchForm: { ...this.state.searchForm, status: v }
									});
								}}
							>
								{WITHDRAW_STATUS.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'提现时间:'}>
							{getFieldDecorator('withdrawPicker', { rules: [{ type: 'array' }] })(
								<RangePicker onChange={this.getWithdrawDate} placeholder={['开始时间', '结束时间']} />
							)}
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={8}>
						<FormItem label={'申请时间:'}>
							{getFieldDecorator('rangPicker', { rules: [{ type: 'array' }] })(
								<RangePicker onChange={this.getOrderDate} placeholder={['开始时间', '结束时间']} />
							)}
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={16} offset={8} align="right">
						<FormItem>
							<Button type="primary" onClick={this.handleSearch}>
								搜索
							</Button>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<Button type="default" onClick={this.handleReset}>
								重置
							</Button>
						</FormItem>
					</Col>
				</Row>
			</Form>
		);
	};

	// 渲染操作按钮
	_renderOperationButton = () => {
		return <Col>
			<Button className="searchBtn" type="primary" onClick={() => this.export()}><Icon type="download" />导出提现记录</Button>
		</Col>;
	};

	render() {
		const { page, pageSize } = this.state;
		const { dataList, total } = this.props;
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: total => {
				return `共 ${total} 条数据 `;
			},
			onChange: page => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		const columns = [
			{
				title: '用户编号',
				dataIndex: 'cno',
				align: 'center'
			},
			{
				title: '用户名称',
				dataIndex: 'customerName',
				align: 'center'
			},
			{
				title: '用户地址',
				dataIndex: 'address',
				align: 'center'
			},
			{
				title: '申请时间',
				dataIndex: 'createTime',
				align: 'center'
			},
			{
				title: '原余额',
				dataIndex: 'oldBalance',
				align: 'center'
			},
			{
				title: '提现金额',
				dataIndex: 'withdrawBalance',
				align: 'center'
			},
			{
				title: '提现方式',
				dataIndex: 'withdrawChargeWay',
				align: 'center',
			},
			{
				title: '备注',
				dataIndex: 'remark',
				align: 'center'
			},
			{
				title: '提现时间',
				dataIndex: 'withdrawTime',
				align: 'center'
			},
			{
				title: '提现状态',
				dataIndex: 'status',
				align: 'center',
				render: text => {
					return (
						<Tag color={withdrawStatusColorMap.get(text)}>
							{text}
						</Tag>
					);
				}
			},
			{
				title: '操作',
				key: 'operation',
				align: 'center',
				render: (text, record, index) => {
					return <span>{record.status === '待提现' ? <Button title="提现" className="btn" type="primary" size="small" icon="check" onClick={() => this.handleWithdraw(record)} /> : null}</span>;
				}
			}
		]
		return (
			<div className="shadow-radius withdraw">
				<Row>
					<Col>
						<h1>提现记录</h1>
					</Col>
				</Row>
				<Row>{this._renderSearchForm()}</Row>
				<Row>{this._renderOperationButton()}</Row>
				<Row className="main">
					<Table bordered columns={columns} rowKey={() => Math.random()} dataSource={dataList} pagination={paginationProps} />
				</Row>
			</div>
		);
	}
}
const mapStateToProps = state => {
	const data = state.get('withdraw');
	return {
		fields: data.fields,
		dataList: data.dataList, // 列表数据
		total: data.total, // 总条数
		visible: data.visible, // 是否显示弹窗
		detail: data.detail, // 获取详情
		applyUserList: data.applyUserList // 申请人员列表
	};
};
const mapDispatchToProps = dispatch => ({
	setState: data => dispatch(actionCreators.setState(data)), // 设置redux值
	list: data => dispatch(actionCreators.list(data)), // 获取列表
	withdraw: (data, list) => dispatch(actionCreators.withdraw(data, list)) // 提现
});
export default connect(
	mapStateToProps,
	mapDispatchToProps
)(Form.create()(Index));
