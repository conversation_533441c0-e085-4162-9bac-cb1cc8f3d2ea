import React, { Component } from 'react';
import { actionCreators } from './store';
import { connect } from 'react-redux';
import http from '$http';
import { Modal, Table, Form, Select, Row, Col, Input, Button, DatePicker, Icon } from 'antd';
import { constants } from '$utils';
import WaterMeterReload from './components/WaterMeterReload';
import './index.scss';
import { WATER_METER_MANUFACTURER, WATER_METER_KIND, WATER_METER_CALIBER } from '@/constants/waterMeter';
import { changeWaterMeter, newXTMF1Card, xtWriteChange } from '@/utils/cardUtil';

const FormItem = Form.Item;
const { Option } = Select;
const defaultSearchForm = {
	cno: '',
	waterMeterNo: null, // 水表编号
	waterMeterType: null, // 水表分类
	manufacturer: null, // 厂家
	waterMeterKind: null, // 水表种类
	newWaterMeterNo: null, // 新水表编号
	newWaterMeterType: null, // 新水表分类
	newManufacturer: null, // 新厂家
	newWaterMeterKind: null, // 水表种类
	hasChange: null, // 是否换表
	reloadTime: null, // 复装时间
	createUid: null //操作员
};
const { confirm } = Modal;

class Index extends Component {
	constructor(props) {
		super(props);
		this.state = {
			expand: false, // 搜索项过多的时候，隐藏部分
			page: 1,
			pageSize: 10,
			searchForm: defaultSearchForm,
			detailModalVisible: false
		};
	}

	componentDidMount() {
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields && isTags) {
			this.setState(
				{
					...fields.state
				},
				() => {
					this.props.form.setFieldsValue({
						...fields.form
					});
				}
			);
		} else {
			this.getList();
		}
	}

	componentWillUnmount() {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			});
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			});
		}
	}

	// 获取列表
	getList = () => {
		const { page, pageSize, searchForm } = this.state;
		this.props.list(Object.assign({ ...searchForm }, { page, pageSize }));
	};

	// 列表分页
	handlePageChange = page => {
		this.setState(
			{
				page
			},
			() => {
				this.getList();
			}
		);
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState(
			{
				page: 1,
				pageSize: size
			},
			() => {
				this.getList();
			}
		);
	};

	// 搜索功能
	handleSearch = () => {
		this.setState(
			{
				page: 1
			},
			() => {
				this.getList();
			}
		);
	};

	// 重置搜索
	handleReset = () => {
		this.setState(
			{
				searchForm: Object.assign({}, defaultSearchForm)
			},
			() => {
				this.getList();
			}
		);
	};

	// 查看详情
	handleView = record => {
		this.props.getDetail(record.id);
		this.setState({ detailModalVisible: true });
	};

	handleCancel = () => {
		this.setState({ detailModalVisible: false });
	};

	//导出
	export() {
		const { searchForm } = this.state;
		let url = `${process.env.API_ROOT}/api/cm/customer/waterMeter/reload/exportCustomerWaterMeterReload`;
		http.export(url, searchForm, res => {
			const blob = new Blob([res]);
			const downloadElement = document.createElement('a');
			const href = window.URL.createObjectURL(blob);
			const fileName = '复装记录_' + new Date().getTime() + '.xlsx';
			downloadElement.href = href;
			downloadElement.download = fileName;
			document.body.appendChild(downloadElement);
			downloadElement.click();
			document.body.removeChild(downloadElement);
			window.URL.revokeObjectURL(href);
		});
	}

	// 渲染搜索
	_renderSearchForm = () => {
		const { form, createList } = this.props;
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					<Col span={8}>
						<FormItem label={'用户编号:'}>
							<Input
								placeholder="请输入用户编号"
								value={this.state.searchForm.cno}
								onChange={v => {
									this.setState({
										searchForm: { ...this.state.searchForm, cno: v.target.value }
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'新表编号:'}>
							<Input
								placeholder="请输入水表编号"
								value={this.state.searchForm.waterMeterNo}
								onChange={v => {
									this.setState({ searchForm: { ...this.state.searchForm, waterMeterNo: v.target.value } });
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'新表厂家:'}>
							<Select
								placeholder="请选择"
								value={this.state.searchForm.newManufacturer}
								onChange={v => {
									this.setState({ searchForm: { ...this.state.searchForm, newManufacturer: v } });
								}}
							>
								{WATER_METER_MANUFACTURER.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
				</Row>

				<Row>
					<Col span={8}>
						<FormItem label={'新表分类:'}>
							<Select
								placeholder="请选择"
								value={this.state.searchForm.newWaterMeterType}
								onChange={v => {
									this.setState({ searchForm: { ...this.state.searchForm, newWaterMeterType: v } });
								}}
							>
								{constants.waterMeterType.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'新表种类:'}>
							<Select
								placeholder="请选择"
								value={this.state.searchForm.newWaterMeterKind}
								onChange={v => {
									this.setState({ searchForm: { ...this.state.searchForm, newWaterMeterKind: v } });
								}}
							>
								{WATER_METER_KIND.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'复装时间:'}>
							<DatePicker
								format={'YYYY-MM-DD'}
								value={this.state.searchForm.reloadTime}
								onChange={v => {
									this.setState({ searchForm: { ...this.state.searchForm, reloadTime: v } });
								}}
							/>
						</FormItem>
					</Col>
				</Row>

				<Row>
					<Col span={8}>
						<FormItem label={'是否换表:'}>
							<Select
								placeholder="请选择"
								value={this.state.searchForm.hasChange}
								onChange={v => {
									this.setState({ searchForm: { ...this.state.searchForm, hasChange: v } });
								}}
							>
								<Option value={0}>否</Option>
								<Option value={1}>是</Option>
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'操作员:'}>
							<Select
								placeholder="请选择操作员"
								value={this.state.searchForm.createUid}
								onChange={v => {
									this.setState({ searchForm: { ...this.state.searchForm, createUid: v } });
								}}
							>
								{createList.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'水表口径:'}>
							<Select
								placeholder="请选择"
								value={this.state.searchForm.caliber}
								onChange={v => {
									this.setState({ searchForm: { ...this.state.searchForm, caliber: v } });
								}}
							>
								{WATER_METER_CALIBER.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
				</Row>

				<Col span={24} align="right">
					<FormItem>
						<Button type="primary" onClick={this.handleSearch}>
							搜索
						</Button>
						&nbsp;&nbsp;&nbsp;&nbsp;
						<Button type="default" onClick={this.handleReset}>
							重置
						</Button>
					</FormItem>
				</Col>
			</Form>
		);
	};

	// 渲染详情
	_renderDetailModal = () => {
		const { detailModalVisible } = this.state;
		const { detail } = this.props;
		return (
			<Modal className="reload-modal" title={'复装详情'} visible={detailModalVisible} onCancel={this.handleCancel} footer={null}>
				<WaterMeterReload detail={detail} />
			</Modal>
		);
	};

	//复装写卡
	handleChangeWater(record) {
		const _this = this;
		this.props.getAreaNoById(record);
		this.props.getDetail(record.id);
		this.props.getUserInfo(record.customerId);
		confirm({
			title: '确认操作',
			content: '是否确认复装写卡？',
			okText: '确定',
			cancelText: '取消',
			onOk() {
				let detail = _this.props.detail;
				let total = 0;
				let zjsj1 = 0;
				let zjsj2 = 0;
				let zjsj3 = 0;
				let sl1 = 0;
				let sl2 = 0;
				// let customer = _this.props.customer;
				let customer = _this.props.userInfo;
				if (detail.newManufacturer === '扬州恒信') {
					const SYS_NAME = process.env.SYS_NAME;
					if (SYS_NAME === 'qhd') {
						let areaNo = _this.props.areaNo;
						if (detail.ladderType === '非阶梯') {
							zjsj1 = detail.price1;
							zjsj2 = detail.price1;
							zjsj3 = detail.price1;
							sl1 = 25;
							sl2 = 0;
						} else {
							zjsj1 = detail.price1 ? detail.price1 : 0;
							zjsj2 = detail.price2 ? detail.price2 : 0;
							zjsj3 = detail.price3 ? detail.price3 : 0;
							sl1 = detail.ladder1 ? detail.ladder1 : 0;
							sl2 = detail.ladder2 ? detail.ladder2 : 0;
						}
						if (customer.waterMeterKindType === '预付费5') {
							total = customer.accumulationAmount;
						} else {
							total = customer.accumulationBuyAmount;
						}
						let result = changeWaterMeter(areaNo, detail.newWaterMeterKind, customer.cardNo, detail.newWaterMeterNo, total, zjsj1, zjsj2, zjsj3, sl1, sl2);
						if (result === 0) {
							_this.props.updateWriteCard(detail, _this.getList);
						}
					} else if (SYS_NAME === 'ewkq') {
						let areaNo = _this.props.areaNo;
						let customer = _this.props.customer;
						let waterMeterNo = detail.newWaterMeterNo;
						let total = customer.accumulationBuyAmount;
						let zjsj1 = 0;
						let zjsj2 = 0;
						let zjsj3 = 0;
						let sl1 = 0;
						let sl2 = 0;
						if (detail.ladderType === '非阶梯') {
							zjsj1 = detail.price1;
							zjsj2 = detail.price1;
							zjsj3 = detail.price1;
							sl1 = 25;
							sl2 = 0;
						} else {
							zjsj1 = detail.price1 ? detail.price1 : 0;
							zjsj2 = detail.price2 ? detail.price2 : 0;
							zjsj3 = detail.price3 ? detail.price3 : 0;
							sl1 = detail.ladder1 ? detail.ladder1 : 0;
							sl2 = detail.ladder2 ? detail.ladder2 : 0;
						}
						if (customer.waterMeterKindType === '阶梯2') {
							waterMeterNo = '0000000000';
						}
						let result = changeWaterMeter(areaNo, detail.newWaterMeterKind, customer.cardNo, waterMeterNo, total, zjsj1, zjsj2, zjsj3, sl1, sl2);
						if (result === 0) {
							_this.props.updateWriteCard(detail, _this.getList);
						}
					}
				} else if (detail.newManufacturer === '河南新天') {
					let result = -1;
					if (customer.waterMeterKindType === '阶梯57') {
						if (detail.ladderType === '非阶梯') {
							zjsj1 = detail.price1;
							zjsj2 = detail.price1;
							zjsj3 = detail.price1;
							sl1 = detail.ladder1;
							sl2 = detail.ladder1;
						} else {
							zjsj1 = detail.price1 ? detail.price1 : 0;
							zjsj2 = detail.price2 ? detail.price2 : 0;
							zjsj3 = detail.price3 ? detail.price3 : 0;
							sl1 = detail.ladder1 ? detail.ladder1 : 0;
							sl2 = detail.ladder2 ? detail.ladder2 : 0;
						}
						result = xtWriteChange(customer.cardNo, sl1, sl2, zjsj1, zjsj2, zjsj3, customer.accumulationBuyAmount);
					} else if (customer.waterMeterKindType === 'MF1') {
						//换表， 表号，总购买量，本次购买量（剩下多少写多少）
						result = newXTMF1Card(customer.cardNo, customer.accumulationAmount, customer.accumulationAmount);
					}
					if (result === 1 || result === true) {
						_this.props.updateWriteCard(detail, _this.getList);
					}
				}
			}
		});
	}

	render() {
		const { page, pageSize } = this.state;
		const { dataList, total } = this.props;
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: total => {
				return `共 ${total} 条数据 `;
			},
			onChange: page => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		const columns = [
			{
				title: '用户编号',
				dataIndex: 'cno',
				align: 'center'
			},
			{
				title: '用户名称',
				dataIndex: 'customerName',
				align: 'center'
			},
			{
				title: '新表编号',
				dataIndex: 'newWaterMeterNo',
				align: 'center'
			},
			{
				title: '新表种类',
				dataIndex: 'newWaterMeterKind',
				align: 'center'
			},
			{
				title: '新表分类',
				dataIndex: 'newWaterMeterType',
				align: 'center'
			},
			{
				title: '新表口径',
				dataIndex: 'caliberNew',
				align: 'center'
			},
			{
				title: '新表厂家',
				dataIndex: 'newManufacturer',
				align: 'center'
			},
			{
				title: '是否换表',
				align: 'center',
				render: (text, record, index) => {
					if (record.change) {
						return '是';
					} else {
						return '否';
					}
				}
			},
			{
				title: '复装时间',
				dataIndex: 'reloadTime',
				align: 'center'
			},
			{
				title: '换表员',
				dataIndex: 'changePersonName',
				align: 'center'
			},
			{
				title: '操作',
				key: 'operation',
				width: 150,
				align: 'center',
				fixed: 'right',
				render: (text, record, index) => {
					return (
						<span>
							<Button title="查看" className="btn" type="primary" size="small" icon="eye" onClick={() => this.handleView(record)} />
							<Button title="复装写卡" className="btn" type="primary" size="small" icon="table" onClick={() => this.handleChangeWater(record)} />
						</span>
					);
				}
			}
		];
		return (
			<div className="shadow-radius reload">
				<h1>复装记录</h1>
				{this._renderSearchForm()}
				<Row>
					<Button className="searchBtn" type="primary" onClick={() => this.export()}>
						<Icon type="download" />
						导出复装记录
					</Button>
				</Row>
				<Row className="main">
					<Table bordered scroll={{ x: 2300 }} rowKey={() => Math.random()} columns={columns} dataSource={dataList} pagination={paginationProps} />
				</Row>
				{this._renderDetailModal()}
			</div>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('waterMeterReload');
	return {
		fields: data.fields,
		dataList: data.dataList, //列表数据
		total: data.total, //总条数
		visible: data.visible, //是否显示弹窗
		detail: data.detail, //获取详情
		areaNo: data.areaNo, //区域码
		userInfo: data.userInfo, //卡号
		createList: data.createList
	};
};

const mapDispatchToProps = dispatch => ({
	setState: data => dispatch(actionCreators.setState(data)), // 设置redux值
	list: data => dispatch(actionCreators.list(data)), // 获取列表
	getDetail: data => dispatch(actionCreators.detail(data)), // 获取详情
	getAreaNoById: data => dispatch(actionCreators.getAreaNoById(data)), //获取区域码
	getUserInfo: data => dispatch(actionCreators.getUserInfo(data)), //获取用户信息
	updateWriteCard: (data, getList) => dispatch(actionCreators.updateWriteCard(data, getList)), //修改换表状态
	queryListSelect: () => dispatch(actionCreators.queryListSelect()) //获取抄表员
});
export default connect(mapStateToProps, mapDispatchToProps)(Form.create()(Index));
