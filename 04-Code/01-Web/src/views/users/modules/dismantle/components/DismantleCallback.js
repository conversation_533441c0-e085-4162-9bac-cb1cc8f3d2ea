import React, { Component, Fragment } from 'react';
import { Modal, Table, Form, Select, Row, Col, Input, Button, DatePicker, Descriptions, InputNumber } from 'antd';
import { constants } from '$utils';
import { CHANGE_METER_STATUS } from '@/constants/waterMeter';
import './index.scss';
const FormItem = Form.Item;
const { Option } = Select;
class DismantleCallback extends React.Component {
	constructor(props) {
		super(props);
		this.state = {
			dismantleTime: null
		};
	}
	getDismantleTime = (date, dateString) => {
		this.setState({ dismantleTime: dateString });
		this.props.setDismantleTime(dateString);
	};
	render() {
		const { handleSubmitCallback, handleCancelCallback, form, customer } = this.props;
		const { getFieldDecorator } = form;
		return (
			<Fragment>
				<Descriptions title="用户信息" bordered>
					<Descriptions.Item label="用户名称：">{customer && customer.name}</Descriptions.Item>
					<Descriptions.Item label="用户编号：">{customer && customer.cno}</Descriptions.Item>
					<Descriptions.Item label="人口数：">{customer && customer.population}</Descriptions.Item>
					<Descriptions.Item label="用户地址：">{customer && customer.address}</Descriptions.Item>
					<Descriptions.Item label="联系电话：">{customer && customer.contactPhone}</Descriptions.Item>
					<Descriptions.Item label="身份证号：">{customer && customer.idCard}</Descriptions.Item>
					<Descriptions.Item label="片区：">{customer && customer.areaName}</Descriptions.Item>
					<Descriptions.Item label="水表类型：">{customer && customer.meterType}</Descriptions.Item>
					<Descriptions.Item label="客户类型：">{customer && customer.customerType}</Descriptions.Item>
					<Descriptions.Item label="水表编号：">{customer && customer.waterMeterNo}</Descriptions.Item>
					<Descriptions.Item label="水表类型：">{customer && customer.waterMeterType}</Descriptions.Item>
					<Descriptions.Item label="水表种类：">{customer && customer.waterMeterKindType}</Descriptions.Item>
					<Descriptions.Item label="安装位置：">{customer && customer.installLocation}</Descriptions.Item>
					<Descriptions.Item label="水表厂家：">{customer && customer.waterMeterManufacturer}</Descriptions.Item>
					<Descriptions.Item label="当前字轮读数：">{customer && customer.wheelPresentNumber}</Descriptions.Item>
				</Descriptions>
				<Form {...constants.formItemLayout} style={{ marginTop: 20 }}>
					<Row>
						<Col span={12}>
							<FormItem label="最新读数：">
								{getFieldDecorator('oldMeterNumber', {
									initialValue: '',
									rules: [{ required: true, message: '请输入最新读数' },
										{
											validator: (rule, value, callback) => {
												if (/\s+/.test(value)) {
													this.props.form.setFieldsValue({
														oldMeterNumber: value.replace(/\s+/, '')
													});
													callback();
												}else if (value<0){
													callback("不能为负数");
												}else if (value===null){
													callback("必填");
												}
												else {
													if (/^\d+\.?\d{0,2}$/.test(value)) {
														callback();
													} else {
														this.props.form.setFieldsValue({
															oldMeterNumber: `${value}`.substr(`${value}`, `${value}`.length - 1)
														});
														callback();
													}
												}
											}
										}]
								})(<InputNumber palceholder="请输入最新读数" style={{ width: '100%' }} />)}
							</FormItem>
						</Col>
						<Col span={12}>
							<FormItem label="拆表时间：">
								{getFieldDecorator('dismantleTime', {
									initialValue: null,
									rules: [
										{
											required: true,
											message: '请选择拆表时间'
										}
									]
								})(<DatePicker format={'YYYY-MM-DD'} onChange={this.getDismantleTime} placeholder={'选择拆表时间'} style={{ width: '100%' }} />)}
							</FormItem>
						</Col>
					</Row>
					<Row>
						<Col span={12}>
							<FormItem label="维修员：">
								{getFieldDecorator('maintenancePersonName', {
									initialValue: '',
									rules: [
										{
											required: true,
											message: '请输入维修员'
										}
									]
								})(<Input palceholder="请输入维修员" />)}
							</FormItem>
						</Col>
					</Row>
					<Row>
						<Col span={24} align="center">
							<Button type="primary" className="btn" onClick={handleSubmitCallback}>
								提交
							</Button>
							<Button type="default" className="btn" onClick={handleCancelCallback}>
								取消
							</Button>
						</Col>
					</Row>
				</Form>
			</Fragment>
		);
	}
}
export default Form.create()(DismantleCallback);
