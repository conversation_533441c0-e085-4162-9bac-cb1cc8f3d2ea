import React, { Component, Fragment } from 'react';
import { Descriptions, Tag } from 'antd';
class WaterMeterDismantle extends React.Component {
	render() {
		const { detail } = this.props;
		// 状态颜色
		const statusColorMap = new Map();
		statusColorMap.set('库存', 'green');
		statusColorMap.set('预开户', 'lime');
		statusColorMap.set('使用中', 'blue');
		statusColorMap.set('返修', 'red');
		statusColorMap.set('失窃', 'red');
		statusColorMap.set('废弃', 'red');
		return (
			<Descriptions bordered>
				<Descriptions.Item label="用户名称：">{detail && detail.customerName}</Descriptions.Item>
				<Descriptions.Item label="用户编号：">{detail && detail.cno}</Descriptions.Item>
				<Descriptions.Item label="用户户号：">{detail && detail.hno}</Descriptions.Item>
				<Descriptions.Item label="用户余额：">{detail && detail.accountBalance}</Descriptions.Item>
				<Descriptions.Item label="用户地址：" span={2}>
					{detail && detail.address}
				</Descriptions.Item>
				<Descriptions.Item label="旧水表编号：">{detail && detail.waterMeterNo}</Descriptions.Item>
				<Descriptions.Item label="旧水表类型：">{detail && detail.waterMeterType}</Descriptions.Item>
				<Descriptions.Item label="旧水表厂家：">{detail && detail.manufacturer}</Descriptions.Item>
				<Descriptions.Item label="旧表状态：" >
					<Tag color={statusColorMap.get(detail && detail.oldMeterStatus)}>
						{detail && detail.oldMeterStatus}
					</Tag>
				</Descriptions.Item>
				<Descriptions.Item label="旧表已购字轮读数：">{detail && detail.oldMeterBuyNumber}</Descriptions.Item>
				<Descriptions.Item label="旧表字轮读数：">{detail && detail.oldMeterNumber}</Descriptions.Item>
				<Descriptions.Item label="卡表剩余水量：">{detail && detail.cardLeftWaterAmount}</Descriptions.Item>
				<Descriptions.Item label="卡表剩余金额：">{detail && detail.cardLeftAmount}</Descriptions.Item>
				<Descriptions.Item></Descriptions.Item>
				<Descriptions.Item label="拆表类型：">{detail && detail.dismantleType}</Descriptions.Item>
				<Descriptions.Item label="维修员：">{detail && detail.maintenancePersonName}</Descriptions.Item>
				<Descriptions.Item label="换拆表时间：">{detail && detail.dismantleTime}</Descriptions.Item>
				<Descriptions.Item label="备注：" span={3}>
					{detail && detail.remark}
				</Descriptions.Item>
			</Descriptions>
		);
	}
}
export default WaterMeterDismantle;
