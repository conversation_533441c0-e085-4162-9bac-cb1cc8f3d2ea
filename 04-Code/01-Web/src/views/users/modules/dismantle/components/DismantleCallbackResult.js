import React, { Component, Fragment } from 'react';
import { Modal, Table, Form, Select, Row, Col, Input, Button, DatePicker, Descriptions, InputNumber } from 'antd';
import './index.scss';
class ChangeCallbackResult extends React.Component {
	render() {
		const { callbackResult } = this.props;
		return (
			<Fragment>
				<Descriptions title="拆表结果" bordered>
					<Descriptions.Item label="卡表剩余水量：">{callbackResult && callbackResult.cardLeftWaterAmount}</Descriptions.Item>
					<Descriptions.Item label="卡表剩余金额：">{callbackResult && callbackResult.cardLeftAmount}</Descriptions.Item>
				</Descriptions>
			</Fragment>
		);
	}
}
export default ChangeCallbackResult;
