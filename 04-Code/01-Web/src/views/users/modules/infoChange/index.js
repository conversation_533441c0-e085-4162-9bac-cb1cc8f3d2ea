import React, { Component, Fragment } from 'react';
import { actionCreators } from './store';
import { connect } from 'react-redux';
import http from '$http';
import { Modal, Table, Form, Select, Row, Col, Input, Button, DatePicker, Icon } from 'antd';
import { constants } from '$utils';
import { DATA_ITEM } from './constants';
import './index.scss';

const { confirm } = Modal;
const FormItem = Form.Item;
const { Option } = Select;
const ButtonGroup = Button.Group;
const { RangePicker } = DatePicker;

const defaultSearchForm = {
	cno: '',
	dataItem: null,
	startTime: null,
	endColumn: null,
	changeUid: ''
};

class Index extends Component {

	constructor(props) {
		super(props);
		this.state = {
			expand: false, // 搜索项过多的时候，隐藏部分
			page: 1,
			pageSize: 10,
			searchForm: defaultSearchForm
		};
	}

	componentDidMount() {
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields && isTags) {
			this.setState({
				...fields.state
			}, () => {
				this.props.form.setFieldsValue({
					...fields.form
				});
			});
		} else {
			this.getList();
			this.props.getChangePerson()
		}
	}

	componentWillUnmount() {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			});
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			});
		}
	}

	// 获取列表
	getList = () => {
		const { page, pageSize, searchForm } = this.state;
		this.props.list(Object.assign({ ...searchForm }, { page, pageSize }));
	};

	// 列表分页
	handlePageChange = (page) => {
		this.setState(
			{
				page
			},
			() => {
				this.getList();
			}
		);
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState(
			{
				page: 1,
				pageSize: size
			},
			() => {
				this.getList();
			}
		);
	};

	// 搜索功能
	handleSearch = () => {
		this.setState(
			{
				page: 1
			},
			() => {
				this.getList();
			}
		);
	};

	// 重置搜索
	handleReset = () => {
		this.props.form.setFieldsValue({ rangPicker: [undefined, undefined] })
		this.setState(
			{
				searchForm: Object.assign({}, defaultSearchForm)
			},
			() => {
				this.getList();
			}
		);
	};

	// 查看详情
	handleView = record => {
	};

	// 编辑记录
	handleEdit = record => {
	};

	// 删除记录
	handleDel = record => {
	};

	getOrderDate = (date, dateString) => {
		if (dateString.length === 2) {
			this.setState({
				searchForm: { ...this.state.searchForm, startTime: dateString[0], endTime: dateString[1] }
			});
		}
	};

	//导出
	export() {
		const { searchForm } = this.state;
		let url = `${process.env.API_ROOT}/api/cm/customer/data/change/exportCustomerChange`;
		http.export(url, searchForm, (res) => {
			const blob = new Blob([res]);
			const downloadElement = document.createElement('a');
			const href = window.URL.createObjectURL(blob);
			const fileName = '用户信息变更记录_' + new Date().getTime() + '.xlsx';
			downloadElement.href = href;
			downloadElement.download = fileName;
			document.body.appendChild(downloadElement);
			downloadElement.click();
			document.body.removeChild(downloadElement);
			window.URL.revokeObjectURL(href);
		});
	}

	// 渲染搜索
	_renderSearchForm = () => {
		const { searchForm } = this.state;
		const { form, changePersonList } = this.props
		const { getFieldDecorator } = form;

		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					<Col span={8}>
						<FormItem label={'用户编号:'}>
							<Input
								placeholder="请输入用户编号"
								value={searchForm.cno}
								onChange={(v) => {
									this.setState({
										searchForm: { ...searchForm, cno: v.target.value }
									});
								}}
							/>
						</FormItem>
					</Col>

					<Col span={8}>
						<FormItem label={'修改数据项:'}>
							<Select placeholder="请选修改数据项"
								value={searchForm.dataItem}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, dataItem: v } });
								}}>
								{DATA_ITEM && DATA_ITEM.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>

					<Col span={8}>
						<FormItem label={'修改时间:'}>
							{getFieldDecorator('rangPicker', { rules: [{ type: 'array' }] })(
								<RangePicker onChange={this.getOrderDate} placeholder={['开始时间', '结束时间']} />
							)}
						</FormItem>
					</Col>

				</Row>
				<Row>
					<Col span={8}>
						<FormItem label={'修改人:'}>
							<Select placeholder="请选修改人"
								value={searchForm.changeUid}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, changeUid: v } });
								}}>
								{changePersonList && changePersonList.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={16} offset={8} align="right">
						<FormItem>
							<Button type="primary" onClick={this.handleSearch}>
								搜索
							</Button>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<Button type="default" onClick={this.handleReset}>
								重置
							</Button>
						</FormItem>
					</Col>
				</Row>
			</Form>
		);
	};

	render() {
		const { page, pageSize } = this.state;
		const { dataList, total } = this.props;
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: (total) => {
				return `共 ${total} 条数据 `;
			},
			onChange: (page) => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		const columns = [
			{
				title: '用户编号',
				dataIndex: 'cno',
				align: 'center'
			},
			{
				title: '修改数据项',
				dataIndex: 'dataItem',
				align: 'center'
			},
			{
				title: '原值',
				dataIndex: 'oldValue',
				align: 'center'
			},
			{
				title: '新值',
				dataIndex: 'newValue',
				align: 'center'
			},
			{
				title: '修改时间',
				dataIndex: 'changeTime',
				align: 'center'
			},
			{
				title: '修改人',
				dataIndex: 'changePersonName',
				align: 'center'
			},
			{
				title: '备注',
				dataIndex: 'remark',
				width: 100,
				align: 'center'
			}
		];
		return (
			<div className="shadow-radius info-change">
				<Row>
					<Col><h1>用户信息变更记录</h1></Col>
				</Row>
				<Row>
					{this._renderSearchForm()}
				</Row>
				<Row>
					<Button className="searchBtn" type="primary" onClick={() => this.export()}><Icon type="download" />导出用户信息变更记录</Button>
				</Row>
				<Row className='main'>
					<Table
						bordered
						rowKey={() => Math.random()}
						columns={columns}
						dataSource={dataList}
						pagination={paginationProps}
					/>
				</Row>
			</div>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('infoChange');
	return {
		fields: data.fields,
		dataList: data.dataList, // 列表数据
		total: data.total, // 总条数
		visible: data.visible, // 是否显示弹窗
		detail: data.detail, // 获取详情
		changePersonList: data.changePersonList
	};
};
const mapDispatchToProps = dispatch => ({
	setState: data => dispatch(actionCreators.setState(data)), // 设置redux值
	list: data => dispatch(actionCreators.list(data)), // 获取列表
	getDetail: data => dispatch(actionCreators.detail(data)), // 获取详情
	add: data => dispatch(actionCreators.add(data)), // 新增记录
	del: data => dispatch(actionCreators.del(data)), // 删除记录
	modify: data => dispatch(actionCreators.modify(data)),// 修改记录
	getChangePerson: () => dispatch(actionCreators.getChangePerson()) //查询修改人
});
export default connect(
	mapStateToProps,
	mapDispatchToProps
)((Form.create())(Index));
