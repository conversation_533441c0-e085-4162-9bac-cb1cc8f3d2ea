import React, { Component, Fragment } from 'react';
import { actionCreators } from '../store';
import { connect } from 'react-redux';
import { Spin, Modal, Table, Form, Row, Col, Input, Button, Radio } from 'antd';
import AssociatedUsers from '$components/associatedUsers';

const FormItem = Form.Item;

class AddDialog extends Component {

	constructor(props) {
		super(props);
		this.state = {
			expand: false, // 搜索项过多的时候，隐藏部分
			page: 1,
			pageSize: 10,
			loading: false,
			visible: false,
			cancelVisible: false,
			userVisible: false,
			customerList: [],
			reasonList: []
		};
	}

	// 设置加载状态
	setLoading = (type) => {
		if (type === 0) {
			this.setState({ loading: true });
		} else if (type === 1) {
			this.setState({ loading: false });
		}
	};

	// 提交
	handleSubmit = () => {
		this.props.form.validateFields((err, values) => {
			if (!err) {
				this.setState({ cancelVisible: true });
			}
		});
	};

	handleSubmitOk() {

		this.props.form.validateFields((err, values) => {
			this.setLoading(0);
			const { customerList, reasonList } = this.state;
			let arr = [];
			for (let i = 0; i < customerList.length; i++) {
				arr.push({ customerId: customerList[i].id, reason: null });
			}
			reasonList.filter(n => n);
			for (let i = 0; i < arr.length; i++) {
				for (let k = 0; k < reasonList.length; k++) {
					if (reasonList[k]) {
						if (arr[i].customerId == reasonList[k].id) {
							arr[i].reason = reasonList[k].reason;
						}
					}
				}
			}
			let params = {};
			params.cancelNo = values.cancelNo;
			params.customerBatchCancelDetails = arr;

			if (this.props.form.getFieldValue('type') === 0) {
				this.props.batchCancel(params, () => {this.setLoading(1);}, this.handleCancel, this.props.refresh);
			} else {
				this.props.batchCancelRapid(params, () => {this.setLoading(1);}, ()=>this.cancelVisible(), this.props.refresh,this.handleCancel);
			}
		})
		this.setState({ cancelVisible: false });
	}

	// 打开弹窗
	handleOpen = () => {
		this.setState({ visible: true });
	};

	// 取消弹窗
	handleCancel = () => {
		this.setState({ visible: false, customerList: [], loading: false });
	};

	// 打开选择关联用户
	handleUserOpen = () => {
		this.userList({ page: 1, pageSize: 10 });
		this.setState({
			userVisible: true
		});
	};

	// 关闭选择用户
	handleUserCancel = () => {
		this.setState({
			userVisible: false
		});
	};

	// 获取关联用户数据
	getAssociatedUser = record => {
		this.setState({
			userVisible: false,
			customerList: record
		});
	};

	// 获取用户列表
	userList = (params) => {
		this.props.userList(params);
	};

	//打开销户弹窗
	cancelVisible() {
		this.setState({ cancelVisible: false});
	}

	render() {
		const { visible, loading, customerList, cancelVisible } = this.state;
		const { form, users } = this.props;
		const { getFieldDecorator } = form;
		const formItemLayout = {
			labelCol: {
				xs: { span: 6 },
				sm: { span: 6 }
			},
			wrapperCol: {
				xs: { span: 12 },
				sm: { span: 12 }
			}
		};
		const { reasonList } = this.state;
		const columns = [
			{
				title: '序号',
				dataIndex: 'xuhao',
				key: 'xuhao',
				align: 'center',
				render: (text, record, index) => {
					return index + 1;
				}
			},
			{
				title: '用户编号',
				dataIndex: 'cno',
				key: 'cno',
				align: 'center'
			},
			{
				title: '用户名称',
				dataIndex: 'name',
				key: 'name',
				align: 'center'
			},

			{
				title: '用户片区',
				dataIndex: 'areaName',
				key: 'areaName',
				align: 'center'
			},
			{
				title: '用户地址',
				dataIndex: 'address',
				key: 'address',
				align: 'center'
			},
			{
				title: '用水性质',
				dataIndex: 'waterUseKindName',
				key: 'waterUseKindName',
				align: 'center'
			},
			{
				title: '水表编号',
				dataIndex: 'waterMeterNo',
				key: 'waterMeterNo',
				align: 'center'
			},
			{
				title: '水表类型',
				dataIndex: 'waterMeterType',
				key: 'waterMeterType',
				align: 'center'
			},
			{
				title: '水表种类',
				dataIndex: 'waterMeterKindType',
				key: 'waterMeterKindType',
				align: 'center'
			},
			{
				title: '用户状态',
				dataIndex: 'status',
				key: 'status',
				align: 'center'
			},
			{
				title: '用户余额',
				dataIndex: 'accountBalance',
				key: 'accountBalance',
				align: 'center'
			},
			{
				title: '销户原因',
				width: 200,
				fixed: 'right',
				align: 'center',
				render: (text, record, index) => {
					return (
						<Input name={'reason' + index} onChange={v => {
							reasonList[index] = {
								id: record.id,
								reason: v.target.value
							};
						}}/>
					);
				}
			}
		];
		return (
			<Fragment>

				<Button type="primary" onClick={this.handleOpen}>新增</Button>

				<Modal className="payModal" title="批量销户" centered={true} visible={visible}
							 onCancel={this.handleCancel} footer={null} destroyOnClose={true}>
					<Spin spinning={loading} tip="Loading...">
						<Form {...formItemLayout}>
							<Row>
								<Col span={12}>
									<FormItem label="申请销户编号:">
										{
											getFieldDecorator('cancelNo', {
												rules: [{
													required: true,
													message: '必选'
												}]
											})
											(<Input/>)
										}
									</FormItem>
								</Col>
							</Row>
						</Form>
						<Row type="flex" justify="space-between">
							<Button
								type='primary'
								onClick={this.handleUserOpen}
								style={{ marginBottom: 10 }}
							>
								选择关联用户
							</Button>
							<span>
                销户总数： {this.state.customerList.length}户
              </span>
						</Row>
						<Row>
							<Table
								bordered
								rowKey={() => Math.random()}
								columns={columns}
								dataSource={customerList}
								pagination={false}
								scroll={{ x: 2300 }}
							/>
						</Row>
						<Row style={{ marginTop: 10 }}>
							<Col span={24} align="center">
								<Button type="primary" className="btn" onClick={this.handleSubmit}>提交</Button>
								<Button className="btn" onClick={this.handleCancel}>取消</Button>
							</Col>
						</Row>
					</Spin>
				</Modal>

				<AssociatedUsers
					radio={false}
					visible={this.state.userVisible}
					data={users}
					onCancel={this.handleUserCancel}
					onSubmit={this.getAssociatedUser}
					handleList={this.userList}/>

				<Modal
					title="确认销户"
					centered={true}
					visible={cancelVisible}
					onCancel={() => this.cancelVisible()}
					footer={null}
					destroyOnClose={true}>
					<Form {...formItemLayout}>
						<div style={{ color: '#ff0000', textAlign: 'center' }}>
							<div>提示：普通销户（需回填水表示数）</div>
							<div>快捷销户:（不涉及金额进出，针对于建档错误或者重复等情况）</div>
						</div>
						<Row>
							<Col>
								<FormItem label="销户类型：">
									{getFieldDecorator('type', {
										initialValue: 0
									})(
										<Radio.Group>
											<Radio value={0}>普通销户</Radio>
											<Radio value={1}>快捷销户</Radio>
										</Radio.Group>
									)}
								</FormItem>
							</Col>
						</Row>
					</Form>
					<Row style={{ marginTop: 10 }}>
						<Col span={24} align="center">
							<Button type="primary" className="btn" onClick={() => this.handleSubmitOk()}>提交</Button>
							<Button className="btn" onClick={() => this.cancelVisible()}>取消</Button>
						</Col>
					</Row>
				</Modal>
			</Fragment>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('cancelAccount');
	return {
		users: data.users
	};
};
const mapDispatchToProps = dispatch => ({
	// add: data => dispatch(actionCreators.add(data)), // 新增记录
	userList: data => dispatch(actionCreators.userList(data)), // 获取用户列表
	batchCancel: (data, setLoading, cancel, refresh) => dispatch(actionCreators.batchCancel(data, setLoading, cancel, refresh)),// 批量销户
	batchCancelRapid: (data, setLoading, cancel, refresh,close) => dispatch(actionCreators.batchCancelRapid(data, setLoading, cancel, refresh,close)) // 批量销户
});
export default connect(
	mapStateToProps,
	mapDispatchToProps
)((Form.create())(AddDialog));
