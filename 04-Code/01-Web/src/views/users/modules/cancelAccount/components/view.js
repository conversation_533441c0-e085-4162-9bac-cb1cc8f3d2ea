import React, { Component } from 'react';

import { Button, Col, DatePicker, Form, Input, Modal, Row, Select, Spin, Table } from 'antd';
import { connect } from 'react-redux';

import { actionCreators } from '../store';

const FormItem = Form.Item;
const { Option } = Select;
const ButtonGroup = Button.Group;
const { RangePicker } = DatePicker;
const { TextArea } = Input;
const { Column, ColumnGroup } = Table;

class ViewDialog extends Component {
	constructor(props) {
		super(props);
		this.state = {
			expand: false, // 搜索项过多的时候，隐藏部分
			page: 1,
			pageSize: 10,
			loading: false,
			visible: false,
			userVisible: false,
			customerList: [],
			columns: [
				{
					title: '序号',
					dataIndex: 'xuhao',
					key: 'xuhao',
					align: 'center',
					render: (text, record, index) => {
						return index + 1;
					}
				},
				{
					title: '用户编号',
					dataIndex: 'cno',
					key: 'cno',
					align: 'center'
				},
				{
					title: '用户名称',
					dataIndex: 'name',
					key: 'name',
					align: 'center'
				},

				{
					title: '用户片区',
					dataIndex: 'areaName',
					key: 'areaName',
					align: 'center'
				},
				{
					title: '用户地址',
					dataIndex: 'address',
					key: 'address',
					align: 'center'
				},
				{
					title: '用水分类',
					dataIndex: 'waterUseKindTypeName',
					key: 'waterUseKindTypeName',
					align: 'center'
				},
				{
					title: '水表编号',
					dataIndex: 'no',
					key: 'no',
					align: 'center'
				},
				{
					title: '水表类型',
					dataIndex: 'waterType',
					key: 'waterType',
					align: 'center'
				},
				{
					title: '水表种类',
					dataIndex: 'kind',
					key: 'kind',
					align: 'center'
				},
				{
					title: '用户状态',
					dataIndex: 'status',
					key: 'status',
					align: 'center'
				},
				{
					title: '用户余额',
					dataIndex: 'accountBalance',
					key: 'accountBalance',
					align: 'center'
				},
				{
					title: '销户原因',
					dataIndex: 'reason',
					key: 'reason',
					align: 'center'
				}
			]
		};
	}

	// 查询
	getList = () => {
		const { record } = this.props;
		const { page, pageSize } = this.state;
		let params = {};
		params.applyId = record.applyId;
		params.id = record.id;
		console.log('applyId', this.props.record.applyId);
		params.page = page;
		params.pageSize = pageSize;
		this.props.viewDetail(params);
	};

	// 打开弹窗
	handleOpen = () => {
		// const { record } = this.props;
		// let params = {};
		// params.applyId = record.applyId
		// params.page = 1;
		// params.pageSize = 10;
		// this.props.viewDetail(qs.stringify(params));
		this.getList();
		this.setState({ visible: true });
	};

	// 取消弹窗
	handleCancel = () => {
		this.setState({
			visible: false,
			customerList: [],
			loading: false
		});
	};

	// 列表分页
	handlePageChange = page => {
		this.setState(
			{
				page
			},
			() => {
				this.getList();
			}
		);
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState(
			{
				page: 1,
				pageSize: size
			},
			() => {
				this.getList();
			}
		);
	};

	render() {
		const { visible, loading, columns, page, pageSize } = this.state;
		const { form, record, detail } = this.props;
		console.info(detail);
		const { getFieldDecorator } = form;
		const formItemLayout = {
			labelCol: {
				xs: { span: 6 },
				sm: { span: 6 }
			},
			wrapperCol: {
				xs: { span: 12 },
				sm: { span: 12 }
			}
		};
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: detail.total,
			showTotal: total => {
				return `共 ${detail.total} 条数据 `;
			},
			onChange: page => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		return (
			<div>
				<Button title="查看" className="btn" type="primary" size="small" icon="eye" onClick={this.handleOpen}>
					查看
				</Button>
				<Modal className="payModal" title="查看详情" centered={true} visible={visible} onCancel={this.handleCancel} footer={null} destroyOnClose={true}>
					<Spin spinning={loading} tip="Loading...">
						<Form {...formItemLayout}>
							<Row>
								<Col span={12}>
									<FormItem label="申请销户编号:">
										<Input value={record.cancelNo} disabled />
									</FormItem>
								</Col>
							</Row>
						</Form>
						<Row type="flex" justify="space-between">
							<span>&emsp;</span>
							<span>销户总数: {detail.total} 户</span>
						</Row>
						<Row>
							<Table bordered rowKey={() => Math.random()} columns={columns} dataSource={detail.rows} scroll={{ x: 2300 }} pagination={paginationProps} />
						</Row>
						<Row style={{ marginTop: 10 }}>
							<Col span={24} align="center">
								<Button className="btn" onClick={this.handleCancel}>
									关闭
								</Button>
							</Col>
						</Row>
					</Spin>
				</Modal>
			</div>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('cancelAccount');
	return {
		detail: data.detail // 详情
	};
};
const mapDispatchToProps = dispatch => ({
	viewDetail: data => dispatch(actionCreators.viewDetail(data)) // 批量销户
});
export default connect(mapStateToProps, mapDispatchToProps)(Form.create()(ViewDialog));
