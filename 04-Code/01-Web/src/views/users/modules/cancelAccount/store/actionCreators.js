import http from '$http';
import { message } from 'antd';

import api from './api';
import * as actionTypes from './constants';

// 数据回填
const payload = (type, data) => ({ type, data });

// 组件操作redux数据状态
const setState = data => ({ type: actionTypes.SET_STATE, data });

// 获取列表
const list = data => {
	return async dispatch => {
		const res = await http.post(api.list, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.LIST_RECORD, res.data));
		}
	};
};

// 获取申请人
const getPerson = data => {
	return async dispatch => {
		const res = await http.get(api.gerPER);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_PERSON, res.data));
		}
	};
};

// 获取申请部门
const getDepartment = data => {
	return async dispatch => {
		const res = await http.get(api.getDPT);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_DEPARTMENT, res.data));
		}
	};
};

// 新增
const add = (data, list) => {
	return async dispatch => {
		const res = await http.post(api.add, data);
		if (res.code === 0) {
			message.success('新增成功！');
			list();
		}
	};
};

// 关联申请人列表
const userList = data => {
	return async dispatch => {
		const res = await http.post(api.users, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_USER_RECORD, res.data));
		}
	};
};

// 批量销户
const batchCancel = (data, setLoading, cancel, refresh) => {
	return async dispatch => {
		const res = await http.post(api.batchCancel, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.BATCH_CANCEL, res.data));
			cancel();
			refresh();
			message.success(res.msg);
		}
		setLoading();
	};
};

// 批量快捷销户
const batchCancelRapid = (data, setLoading, cancel, refresh, close) => {
	return async dispatch => {
		const res = await http.post(api.batchCancelRapid, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.BATCH_CANCEL_RAPID, res.data));
			cancel();
			refresh();
			close();
			message.success('快捷销户成功！' + '共销户' + res.data + '户');
		}
		setLoading();
	};
};

// 销户详情查看
const viewDetail = data => {
	return async dispatch => {
		// const head = await http.get(api.viewDetailHeader + `/${data.applyId}`);
		// if (head.code === 0) {
		// 	dispatch(payload(actionTypes.VIEW_DETAIL, head.data));
		// }
		const body = await http.post(api.viewDetailTable, data);
		if (body.code === 0) {
			dispatch(payload(actionTypes.VIEW_DETAIL, body.data));
		}
	};
};

export { add, batchCancel, batchCancelRapid, getDepartment, getPerson, list, setState, userList, viewDetail };
