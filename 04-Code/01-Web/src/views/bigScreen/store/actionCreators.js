import http from '$http';
import * as actionTypes from './constants';
import { message } from 'antd';
import api from './api';

// 数据回填
const payload = (type, data) => ({ type, data });

// 组件操作redux数据状态
const setState = (data) => ({ type: actionTypes.SET_STATE, data });

// 获取总用户数量
const listSumHno = (data) => {
	return async (dispatch) => {
		const res = await http.get(api.getSumHno, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_SUM_HNO, res.data));
		}
	};
};

// 获取总表数
const listWaterMeterTypeCount = (data) => {
	return async (dispatch) => {
		const res = await http.get(api.selectWaterMeterTypeCount, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.SELECT_WATER_METER_TYPE_COUNT, res.data));
		}
	};
};

// 获取本月总售水量
const listSellWaterStatistics = (data) => {
	return async (dispatch) => {
		const res = await http.get(api.selectSellWaterStatistics, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.SELECT_SELL_WATER_STATISTICS, res.data));
		}
	};
};

// 获取各分公司售水比重
const listCompanyWaterFee = (data) => {
	return async (dispatch) => {
		const res = await http.get(api.selectCompanyWaterFee, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.SELECT_COMPANY_WATER_FEE, res.data));
		}
	};
};

// 获取用水分类
const listWaterUseKindStatisticsDTO = (data) => {
	return async (dispatch) => {
		const res = await http.get(api.selectWaterUseKindStatisticsDTO, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.SELECT_WATER_USE_KIND_STATISTICS_DTO, res.data));
		}
	};
};

// 年订单总金额/欠费
const listBillFeeStatistics = (data) => {
	return async (dispatch) => {
		const res = await http.get(api.selectBillFeeStatistics, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.SELECT_BILL_FEE_STATISTICS, res.data));
		}
	};
};

// 订单缴费方式占比 和 缴费方式金额
const listChargeWayStatistics = (data) => {
	return async (dispatch) => {
		const res = await http.get(api.selectChargeWayStatistics, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.CHARGE_WAY_STATISTICS, res.data));
		}
	};
};

// 中间地图
const listCompanyStatistics = (data) => {
	return async (dispatch) => {
		const res = await http.get(api.selectCompanyStatistics, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.SELECT_COMPANY_STATISTICS, res.data));
		}
	};
};

// 本日售水金额
const listStatisticsByDay = (data) => {
	return async (dispatch) => {
		const res = await http.get(api.selectChargeWayStatisticsByDay, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.CHARGE_WAY_STATISTICS_BY_DAY, res.data));
		}
	};
};

// 新增
const add = (data) => {};

// 删除
const del = (data) => {};

// 修改
const modify = (data) => {};

export {
	setState,
	add,
	modify,
	del,
	listSumHno,
  listWaterMeterTypeCount,
  listSellWaterStatistics,
	listCompanyWaterFee,
	listWaterUseKindStatisticsDTO,
  listBillFeeStatistics,
  listChargeWayStatistics,
  listCompanyStatistics,
  listStatisticsByDay
};
