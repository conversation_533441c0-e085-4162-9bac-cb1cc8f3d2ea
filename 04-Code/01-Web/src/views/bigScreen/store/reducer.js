import * as actionTypes from './constants';
import { dataType } from '$utils';

const defaultState = {
	datalist: [], // 列表数据
	total: 0, // 总条数
	detail: null, // 详情
	visible: false, // 涉及到交互的弹窗显示控制 默认为false
	sumHnolist: [], // 总用户数量
	WaterMeterTypeCountlist: [], // 总表数
	sellWaterStatisticslist: [], // 本月总售水量
	companyWaterFeelist: [], // 各分公司售水比重
	waterUseKindStatisticsDTOlist: [], // 用水分类
	billFeeStatisticslist: [], // 年订单总金额/欠费
	chargeWayStatisticslist: [], // 订单缴费方式占比 和 缴费方式金额
	companyStatisticslist: [] // 中间地图
};

// 重要提示： 请做好各代码段的注释！！！
export default (state = defaultState, action) => {
	switch (action.type) {
		// 回填组件传给redux的值
		case actionTypes.SET_STATE:
			let _data = action.data;
			if (dataType(_data) === 'Object') {
				state[_data.key] = _data.value;
			}
			if (dataType(_data) === 'Array') {
				_data.forEach((item) => {
					state[item.key] = item.value;
				});
			}
			return { ...state };

		// 回填列表数据
		case actionTypes.LIST_RECORD:
			state.datalist = action.data.rows;
			state.total = action.data.total;
			return { ...state };

		// 回填总用户数量
		case actionTypes.GET_SUM_HNO:
			state.sumHnolist = action.data;
			return { ...state };

		// 回填总表数
		case actionTypes.SELECT_WATER_METER_TYPE_COUNT:
			state.WaterMeterTypeCountlist = action.data;
			return { ...state };

		// 回填本月总售水量
		case actionTypes.SELECT_SELL_WATER_STATISTICS:
			state.sellWaterStatisticslist = action.data;
			return { ...state };

		// 回填各分公司售水比重
		case actionTypes.SELECT_COMPANY_WATER_FEE:
			state.companyWaterFeelist = action.data;
			return { ...state };

		// 回填用水分类
		case actionTypes.SELECT_WATER_USE_KIND_STATISTICS_DTO:
			state.waterUseKindStatisticsDTOlist = action.data;
			return { ...state };

		// 回填年订单总金额/欠费
		case actionTypes.SELECT_BILL_FEE_STATISTICS:
			state.billFeeStatisticslist = action.data;
			return { ...state };

		// 订单缴费方式占比 和 缴费方式金额
		case actionTypes.CHARGE_WAY_STATISTICS:
			state.chargeWayStatisticslist = action.data;
			return { ...state };

		// 中间地图
		case actionTypes.SELECT_COMPANY_STATISTICS:
			state.companyStatisticslist = action.data;
			return { ...state };

		// 本日售水金额
		case actionTypes.CHARGE_WAY_STATISTICS_BY_DAY:
			state.statisticsByDaylist = action.data;
			return { ...state };

		// 新增记录集
		case actionTypes.ADD_RECORD:
			// ... do something
			return { ...state };

		// 删除记录集
		case actionTypes.DEL_RECORD:
			// ... do something
			return { ...state };

		// 修改记录集
		case actionTypes.MODIFY_RECORD:
			// ... do something
			return { ...state };

		default:
			return state;
	}
};
