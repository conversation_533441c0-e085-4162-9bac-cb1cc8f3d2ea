import React, { Component } from 'react';
import echarts from 'echarts';
import { loopShowTooltip } from './echarts-tooltip-carousel';

class Index extends Component {
	constructor() {
		super();
		this.state = {
			ProportionDatas: [],
			datas: {}
		};
	}

	componentDidMount() {
		this.pieChart = echarts.init(document.getElementById('totalWaterMeter'));
	}

	componentWillReceiveProps(nextProps, nextContent) {
		if (nextProps.datas !== this.state.datas) {
			this.setState({ datas: nextProps.datas });
			let ProportionDatas = [];
			for (let i = 0; i < nextProps.datas.length; i++) {
				const data = nextProps.datas[i];
				ProportionDatas.push({
					name: data.type,
					value: data.number
				});
			}
			this.setState({ ProportionDatas }, () => this.renderPieChart());
		}
	}

	renderPieChart = () => {
		let option = {
			legend: {
				show: false
			},
			tooltip: {
				textStyle: {
					fontSize: 18
				}
			},
			series: [
				{
					type: 'pie',
					color: [ '#D35270', '#F6A66B', '#98D5D6' ],
					radius: [ '25%', '55%' ],
					center: [ '50%', '50%' ],
					label: {
						textStyle: {
							fontSize: 12
						}
					},
					labelLine: {
						show: true,
						length: 6,
						length2: 8
					},
					data: this.state.ProportionDatas
				}
			]
		};

		if (option && typeof option === 'object') {
			this.pieChart.setOption(option, true);
		}
		loopShowTooltip(this.pieChart, option, {
			loopSeries: true
		});
	};

	render() {
		return <div id="totalWaterMeter" ref={(el) => (this.el = el)} style={{ height: '24vh' }} />;
	}
}

export default Index;
