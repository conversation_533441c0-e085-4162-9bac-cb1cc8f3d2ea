import React, { Component } from 'react';
import { actionCreators } from './store';
import { connect } from 'react-redux';
import { Row, Col } from 'antd';
import '@/assets/css/bigScreen';
import TotalWaterMeter from './eCharts/totalWaterMeter'; // 总表数
import WaterProportionOfBranchCompany from './eCharts/waterProportionOfBranchCompany'; // 各分公司用水比重
import TotalAnnualOrderAmount from './eCharts/totalAnnualOrderAmount'; // 年订单总金额/欠费
import NumberOfUsers from './eCharts/numberOfUsers'; // 用户数
import NumberOfSellingWater from './eCharts/numberOfSellingWater'; // 售水数
import OrderClassificationOfProportion from './eCharts/orderClassificationOfProportion'; // 订单缴费方式占比
import OrderClassificationOfMoney from './eCharts/orderClassificationOfMoney'; // 缴费方式金额
import Geomap from './eCharts/geomap'; // 地图

import { formDate } from '$utils'; // 格式化时间

// 时间转换
class BigScreen extends Component {
	constructor() {
		super();
		this.state = {
			now: formDate(new Date(), 'yyyy-MM-dd HH:mm:ss')
		};
	}

	componentDidMount() {
    // 总用户数
		this.props.listSumHno();
		// 总表数
		this.props.listWaterMeterTypeCount();
		// 本月总售水量
		this.props.listSellWaterStatistics();
		// 各分公司售水比重
		this.props.listCompanyWaterFee();
		// 用水分类
		this.props.listWaterUseKindStatisticsDTO();
		// 年订单总金额/欠费
		this.props.listBillFeeStatistics();
		// // 订单缴费方式占比 和 缴费方式金额
		this.props.listChargeWayStatistics();
		// 中间地图
		this.props.listCompanyStatistics();
		// 本日售水金额
		this.props.listStatisticsByDay();
		// 日期定时器
		this.timerDate = setInterval(this.getTick, 1000);
		// 定时器
		this.timerDateOfStatisticsByDay = setInterval(this.dataChange, 3600000);
	}

	getTick = () => {
		this.setState({
			now: formDate(new Date(), 'yyyy-MM-dd HH:mm:ss')
		});
	};

	dataChange = () => {
		// 总用户数
		this.props.listSumHno();
		// 总表数
		this.props.listWaterMeterTypeCount();
		// 本月总售水量
		this.props.listSellWaterStatistics();
		// 各分公司售水比重
		this.props.listCompanyWaterFee();
		// 用水分类
		this.props.listWaterUseKindStatisticsDTO();
		// 年订单总金额/欠费
		this.props.listBillFeeStatistics();
		// // 订单缴费方式占比 和 缴费方式金额
		this.props.listChargeWayStatistics();
		// 中间地图
		this.props.listCompanyStatistics();
		// 本日售水金额
		this.props.listStatisticsByDay();
	};

	componentWillUnmount() {
		clearInterval(this.timerDate);
	}

	render() {
		const {
			sumHnolist,
			WaterMeterTypeCountlist,
			sellWaterStatisticslist,
			companyWaterFeelist,
			waterUseKindStatisticsDTOlist,
			billFeeStatisticslist,
			chargeWayStatisticslist,
			companyStatisticslist,
			statisticsByDaylist
		} = this.props;


		const waterTotal=WaterMeterTypeCountlist.reduce((sum,o)=>sum+o.number,0);
		const { now } = this.state;
		return (
			<div
				className="shadow-radius"
				style={{ margin: 0, padding: 0, height: '100vh', overflow: 'hidden', fontFamily: 'Microsoft YaHei' }}
			>
				<div className="bigScreenContainer">
					<Row>
						<Col span={24} className="bigScreenTop">
							<div className="bigScreenTime">{now}</div>
							<div className="bigScreenTitle">营收大数据</div>
							<div className="bigScreenMoney">
								<p>
									本日售水金额：{statisticsByDaylist && statisticsByDaylist.fee ? (
										statisticsByDaylist.fee
									) : (
										0
									)}元
								</p>
								<p>本日售水次数：{statisticsByDaylist ? statisticsByDaylist.number : 0}次</p>
							</div>
						</Col>
					</Row>
					<Row>
						<Col span={6}>
							{/* 用户总量 开始 */}
							<div className="BigScreenUser">
								<div className="userIcon">
									<div className="BigScreenIcon iconUser" />
									<div className="iconText"> 总用户数量</div>
								</div>
								<div className="userMun">
									<Row>
										<Col span={4}>{sumHnolist ? sumHnolist.slice(0, 1) : 0}</Col>
										<Col span={4}>{sumHnolist ? sumHnolist.slice(1, 2) : 0}</Col>
										<Col span={4}>{sumHnolist ? sumHnolist.slice(2, 3) : 0}</Col>
										<Col span={4}>{sumHnolist ? sumHnolist.slice(3, 4) : 0}</Col>
										<Col span={4}>{sumHnolist ? sumHnolist.slice(4, 5) : 0}</Col>
										<Col span={4}>{sumHnolist ? sumHnolist.slice(5, 6) : 0}</Col>
									</Row>
								</div>
							</div>
							{/* 用户总量 结束 */}

							{/* 总表数 开始 */}
							<div className="flow">
								<div style={{ padding: '3% 0 0 5%' }}>
									<div className="BigScreenIcon iconFlow" />
									<div className="iconText"> 总表数 {waterTotal} </div>
								</div>
								<div className="flowContainer" style={{ width: '100%', margin: '0 0 0 0' }}>
									<TotalWaterMeter datas={WaterMeterTypeCountlist} />
								</div>
							</div>
							{/* 总表数 结束 */}

							{/* 本月总售水量 开始 */}
							<div style={{ textAlign: 'center' }} className="equipment">
								<div style={{ padding: '3% 0 0 5%' }}>
									<div className="BigScreenIcon iconEquipment" />
									<div className="iconText"> 本年售水量</div>
								</div>
								<Row style={{ color: '#fff', fontSize: '1.5rem', marginTop: '2%' }}>
									<Col span={24}>
										本年售水量：{sellWaterStatisticslist ? sellWaterStatisticslist.waterAmount : 0}m³
									</Col>
								</Row>
								<Row style={{ color: '#fff', fontSize: '1.5rem', marginTop: '2%' }}>
									<Col span={24}>
										本年计划：{sellWaterStatisticslist ? sellWaterStatisticslist.targetWaterAmount : 0}m³
									</Col>
								</Row>
								<Row style={{ fontSize: '1.25rem', marginTop: '1%' }}>
									<Col span={12}>
										目标进度：{sellWaterStatisticslist ? sellWaterStatisticslist.target : 0}%
									</Col>
									<Col span={12}>
										同期比：{sellWaterStatisticslist ? sellWaterStatisticslist.compared : 0}m³
									</Col>
								</Row>
							</div>
							{/* 本月总售水量 结束 */}

							{/* 各分公司售水比重 开始 */}
							<div className="DMA">
								<div style={{ padding: '3% 0 0 5%' }}>
									<div className="BigScreenIcon iconDMA" />
									<div className="iconText"> 各分公司售水比重(水量)</div>
								</div>
								<div className="DMAContainer" style={{ width: '80%', margin: '-2% 0 0 10%' }}>
									<WaterProportionOfBranchCompany datas={companyWaterFeelist} />
								</div>
							</div>
							{/* 各分公司售水比重 结束 */}
						</Col>
						<Col span={12}>
							{/* 数据地图 开始 */}
							<div className="mapContainer">
								<Geomap id="geomap" datas={companyStatisticslist} />
								<Row className="mapBoxLeft" />
							</div>
							{/* 数据地图 结束 */}

							{/* 用水分类 开始 */}
							<div style={{ textAlign: 'center', width: '100%', margin: '0' }}>
								<Row>
									<Col span={12}>
										<NumberOfUsers datas={waterUseKindStatisticsDTOlist} />
									</Col>
									<Col span={12}>
										<NumberOfSellingWater datas={waterUseKindStatisticsDTOlist} />
									</Col>
								</Row>
							</div>
							{/* 用水分类 结束 */}
						</Col>
						<Col span={6}>
							{/* 年订单总金额/欠费 开始 */}
							<div className="contrast">
								<div style={{ padding: '3% 0 0 5%' }}>
									<div className="BigScreenIcon iconContrast" />
									<div className="iconText"> 年订单总金额/欠费</div>
								</div>
								<div className="contrastContainer" style={{ width: '80%', margin: '0 0 0 8%' }}>
									<TotalAnnualOrderAmount datas={billFeeStatisticslist} />
								</div>
							</div>
							{/* 年订单总金额/欠费 结束 */}

							{/* 订单缴费方式占比 开始  */}
							<div className="analysis">
								<div style={{ padding: '3% 0 0 5%' }}>
									<div className="BigScreenIcon iconAnalysis" />
									<div className="iconText"> {formDate(new Date(), 'yyyy')}年订单缴费方式占比</div>
								</div>
								<div>
									<OrderClassificationOfProportion datas={chargeWayStatisticslist} />
								</div>
							</div>
							{/* 订单缴费方式占比 结束 */}

							{/* 缴费方式金额 开始 */}
							<div className="monitor">
								<div style={{ padding: '3% 0 0 5%' }}>
									<div className="BigScreenIcon iconMonitor" />
									<div className="iconText"> {formDate(new Date(), 'yyyy')}年缴费方式金额</div>
								</div>
								<div>
									<OrderClassificationOfMoney datas={chargeWayStatisticslist} />
								</div>
							</div>
							{/* 缴费方式金额 结束 */}
						</Col>
					</Row>
				</div>
			</div>
		);
	}
}
const mapStateToProps = (state) => {
	const data = state.get('bigScreen');
	return {
		sumHnolist: data.sumHnolist, // 总用户数量
		WaterMeterTypeCountlist: data.WaterMeterTypeCountlist, // 总表数
		sellWaterStatisticslist: data.sellWaterStatisticslist, // 本月总售水量
		companyWaterFeelist: data.companyWaterFeelist, // 各分公司售水比重
		waterUseKindStatisticsDTOlist: data.waterUseKindStatisticsDTOlist, // 用水分类
		billFeeStatisticslist: data.billFeeStatisticslist, // 年订单总金额/欠费
		chargeWayStatisticslist: data.chargeWayStatisticslist, // 订单缴费方式占比 和 缴费方式金额
		companyStatisticslist: data.companyStatisticslist, // 中间地图
		statisticsByDaylist: data.statisticsByDaylist // 本日售水金额
	};
};

const mapDispatchToProps = (dispatch) => ({
	setState: (data) => dispatch(actionCreators.setState(data)), // 设置redux值
	listSumHno: (data) => dispatch(actionCreators.listSumHno(data)), // 总用户数量
	listWaterMeterTypeCount: (data) => dispatch(actionCreators.listWaterMeterTypeCount(data)), // 总表数
	listSellWaterStatistics: (data) => dispatch(actionCreators.listSellWaterStatistics(data)), // 本月总售水量
	listCompanyWaterFee: (data) => dispatch(actionCreators.listCompanyWaterFee(data)), // 各分公司售水比重
	listWaterUseKindStatisticsDTO: (data) => dispatch(actionCreators.listWaterUseKindStatisticsDTO(data)), // 用水分类
	listBillFeeStatistics: (data) => dispatch(actionCreators.listBillFeeStatistics(data)), // 年订单总金额/欠费
	listChargeWayStatistics: (data) => dispatch(actionCreators.listChargeWayStatistics(data)), // 订单缴费方式占比 和 缴费方式金额
	listCompanyStatistics: (data) => dispatch(actionCreators.listCompanyStatistics(data)), // 中间地图
	listStatisticsByDay: (data) => dispatch(actionCreators.listStatisticsByDay(data)) // 本日售水金额
});

export default connect(mapStateToProps, mapDispatchToProps)(BigScreen);
