import * as actionTypes from './constants';
import { dataType } from '$utils';
const defaultState = {
	fields: null,
	total: 0,
	datalist: [],
	detail: null,
};

export default (state = defaultState, action) => {
	switch(action.type) {
			// 回填组件传给redux的值
		case actionTypes.SET_STATE:
			let _data = action.data;
			if (dataType(_data) === 'Object') {
				state[_data.key] = _data.value;
			}
			if (dataType(_data) === 'Array') {
				_data.forEach(item => {
					state[item.key] = item.value;
				});
			}
			return { ...state };

		case actionTypes.LIST_RECORD:
			const data = action.data;
			// 赋值列表
			state.datalist = data.rows;
			// 赋值总条数
			state.total = data.total;
			// 更新state
			return {...state};
		case actionTypes.DEL_RECORD:
			// 删除前，获取当前删除对象所在列表的index
			let idx = state.datalist.findIndex(item => item.id === action.data );
			// 根据当前列表的index，删除对应项
			state.datalist.splice(idx, 1);
			state.datalist = [...state.datalist];
			// 更新state
			return {...state};
		case actionTypes.DETAIL_RECORD:
			// 赋值详情
			state.detail = action.data;
			// 更新state
			return {...state};
		default:
			return state;
	}
}
