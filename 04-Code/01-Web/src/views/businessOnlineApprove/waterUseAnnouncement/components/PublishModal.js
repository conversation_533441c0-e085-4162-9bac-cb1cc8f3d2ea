import React, { Component } from 'react';
import { connect } from 'react-redux';
import { actionCreators } from '../store';
import { Form, Input, Button, Row, Col, Modal } from 'antd';
import { Editor } from 'react-draft-wysiwyg';
import 'react-draft-wysiwyg/dist/react-draft-wysiwyg.css';
import draftToHtml from 'draftjs-to-html';
import '../index.scss';
import WangEditor from '../../../../components/WangEditor';

const rawContentState = {
	entityMap: { '0': { type: 'IMAGE', mutability: 'MUTABLE', data: { src: 'http://i.imgur.com/aMtBIep.png', height: 'auto', width: '100%' } } },
	blocks: [{ key: '9unl6', text: '', type: 'unstyled', depth: 0, inlineStyleRanges: [], entityRanges: [], data: {} }, { key: '95kn', text: ' ', type: 'atomic', depth: 0, inlineStyleRanges: [], entityRanges: [{ offset: 0, length: 1, key: 0 }], data: {} }, { key: '7rjes', text: '', type: 'unstyled', depth: 0, inlineStyleRanges: [], entityRanges: [], data: {} }]
};

class PublishModal extends Component {
	constructor(props) {
		super(props);
		this.state = {
			editorContent: undefined,
			contentState: rawContentState,
			editorState: '', // 公告正文
		};
	}

	// // 重置表单
	// handleReset = () => {
	// 	this.props.form.resetFields();
	// 	this.setState({
	// 		editorState: '',
	// 		editorContent: undefined
	// 	});
	// }

	onEditorChange = editorContent => {
		this.setState({
			editorContent
		}, () => this.props.form.setFieldsValue({ 'content': editorContent }));
	};

	onEditorStateChange = editorState => {
		this.setState({
			editorState
		});
	};
	setHtmlContent = (newHtml) => {
		this.setState({ htmlContent: newHtml });
		this.props.form.setFieldsValue({ 'content': newHtml });
	};

	// 重置表单
	handleReset = () => {
		this.props.form.resetFields();
		this.setState({
			htmlContent: ''
		});
		// 清空编辑器内容
		this.clearEditorContent();
	}

	// 清空编辑器内容
	clearEditorContent = () => {
		if (this.editorRef && this.editorRef.current) {
			this.editorRef.current.clearContent();
		}
	}
	handlePublish = e => {
		e.preventDefault();
		this.props.form.validateFields((err, values) => {
			if (!err) {
				let content = this.state.htmlContent;
				let title = values.title;
				let type = 2;
				let params = {
					title,
					content,
					type
				};
				this.props.publish(params, () => {
					this.handleReset();
					this.props.onCancel();
					this.props.onSuccess(); // 调用父组件传入的刷新方法
				});
			}
		});
	};

	render() {
		const { getFieldDecorator } = this.props.form;
		const { editorState } = this.state;
		const { visible, onCancel } = this.props;
		const formItemLayout = {
			labelCol: { span: 4 },
			wrapperCol: { span: 20 }
		};

		return (
				<Modal
						title="发布公告"
						visible={visible}
						onCancel={onCancel}
						width={1200}
						footer={[
							<Button key="cancel" onClick={onCancel}>取消</Button>,
							<Button key="submit" type="primary" onClick={this.handlePublish}>提交</Button>
						]}
				>
					<Form>
						<Form.Item label="公告标题" {...formItemLayout}>
							{getFieldDecorator('title', {
								rules: [{ required: true, message: '公告标题必填' }]
							})(<Input />)}
						</Form.Item>
						<Form.Item label="公告正文" {...formItemLayout}>
							{getFieldDecorator('content', {
								rules: [{ required: true, message: '公告正文必填' }]
							})(
									<div className="shadow-radius">
										<div className="gutter-example button-demo editor-demo">
											<Row gutter={16} style={{ padding: '0 5px' }}>
												<Col className="gutter-row" md={24}>
													<div className="gutter-box">
														<Form.Item className="add-record-form-item-content" label="公告内容：" required>
															<WangEditor setHtmlContent={this.setHtmlContent} />
														</Form.Item>
													</div>
												</Col>
											</Row>
										</div>
									</div>
							)}
						</Form.Item>
					</Form>
				</Modal>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('pressureAnnouncement');
	return {
		fields: data.fields,
		datalist: data.datalist
	}
};

const mapDispatchToProps = dispatch => ({
	publish: (data, callback) => {
		dispatch(actionCreators.publish(data, callback))
	}
});

export default connect(
		mapStateToProps,
		mapDispatchToProps
)((Form.create())(PublishModal));
