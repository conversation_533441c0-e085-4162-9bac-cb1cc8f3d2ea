import React from 'react';
import { constants } from '$utils';
import { <PERSON><PERSON>, Col, Descriptions, Divider, Form, Input, message, Modal, Popconfirm, Row, Upload } from 'antd';

const searchItemLayout = {
  labelCol: {
    xs: { span: 6 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 15 },
    sm: { span: 15 },
  },
};

class ReviewModalComponent extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isPreviewVisible: false,
      previewImage: undefined,
    };
  }

  onCancel = () => {
    this.props.setIsReviewModalShow(false);
    this.props.setDetail(undefined);
    this.props.form.resetFields();
  };

  onResolve = () => {
    this.props.form.validateFields((error, values) => {
      if (!error) {
        this.props
          .setReview({
            ...values,
            applyId: this.props.detail.applyId,
            reviewStatus: 1,
            auditType: this.props.detail.auditType,
          })
          .then((response) => {
            if (response.code === 0) {
              this.onCancel();
              this.props.onTabsChange('1');
              message.success('审批完成');
            } else {
              message.error('审批失败');
            }
          });
      }
    });
  };

  onReject = () => {
    this.props.form.validateFields((error, values) => {
      if (!error) {
        this.props
          .setReview({
            ...values,
            applyId: this.props.detail.applyId,
            reviewStatus: 2,
            auditType: this.props.detail.auditType,
          })
          .then((response) => {
            if (response.code === 0) {
              this.onCancel();
              this.props.onTabsChange('1');
              message.success('审批完成');
            } else {
              message.error('审批失败');
            }
          });
      }
    });
  };

  render() {
    const { isReviewModalShow, detail } = this.props;
    const { getFieldDecorator } = this.props.form;

    return (
      <>
        <Modal
          title="审批"
          width={1200}
          visible={isReviewModalShow}
          onCancel={this.onCancel}
          footer={
            <Row>
              <Popconfirm title="是否确认同意？" onConfirm={this.onResolve}>
                <Button
                  permission={React.$pmn('REVENUE:BUSINESS_ONLINE_APPROVE:CHANGE_POPULATION:APPROVE')}
                  type="primary"
                >
                  同意
                </Button>
              </Popconfirm>
              <Popconfirm title="是否确认拒绝？" onConfirm={this.onReject}>
                <Button
                  permission={React.$pmn('REVENUE:BUSINESS_ONLINE_APPROVE:CHANGE_POPULATION:APPROVE')}
                  type="danger"
                >
                  拒绝
                </Button>
              </Popconfirm>
              <Button onClick={this.onCancel}>取消</Button>
            </Row>
          }
        >
          {detail ? (
            <>
              <Divider>申请信息</Divider>
              <Descriptions bordered>
                <Descriptions.Item label="用户名称">{detail.customer.name}</Descriptions.Item>
                <Descriptions.Item label="联系电话">{detail.customer.contactPhone}</Descriptions.Item>
                <Descriptions.Item label="用户编号">{detail.customer.cno}</Descriptions.Item>
                <Descriptions.Item label="人口数">{detail.customer.population}</Descriptions.Item>
                <Descriptions.Item label="用户地址">{detail.customer.address}</Descriptions.Item>
                <Descriptions.Item label="身份证号">{detail.customer.idCard}</Descriptions.Item>
                <Descriptions.Item label="用户片区">{detail.customer.areaName}</Descriptions.Item>
                <Descriptions.Item label="水表分类">{detail.customer.meterType}</Descriptions.Item>
                <Descriptions.Item label="用水性质">{detail.customer.waterUseKindType}</Descriptions.Item>
                <Descriptions.Item label="用水分类">{detail.customer.waterUseKindName}</Descriptions.Item>
                <Descriptions.Item label="申请类型">{detail.auditType}</Descriptions.Item>
                <Descriptions.Item label="申请时间" span={3}>
                  {detail.createTime}
                </Descriptions.Item>
                <Descriptions.Item label="照片信息">
                  <Upload
                    disabled
                    listType="picture-card"
                    fileList={detail.annexList.map((list) => ({
                      uid: list.id,
                      name: list.newName,
                      status: 'done',
                      url: constants.fileUrl + list.filePath + list.newName,
                    }))}
                    onPreview={(file) => {
                      this.setState({ isPreviewVisible: true, previewImage: file.url });
                    }}
                  />
                </Descriptions.Item>
              </Descriptions>
              <Divider>变更信息</Divider>
              <Descriptions bordered>
                <Descriptions.Item label="旧人口数" span={1}>
                  {detail.oldDomicileNum}
                </Descriptions.Item>
                <Descriptions.Item label="新人口数" span={2}>
                  {detail.newDomicileNum}
                </Descriptions.Item>
              </Descriptions>
              <Divider>审批信息</Divider>
              <Form className="ant-advanced-search-form" {...searchItemLayout}>
                <Row>
                  <Col span={24}>
                    <Form.Item label="审批意见">
                      {getFieldDecorator('reviewContent', {
                        rules: [
                          {
                            required: true,
                            message: '请输入审批意见',
                          },
                        ],
                      })(<Input.TextArea placeholder="请输入审批意见" rows={4} />)}
                    </Form.Item>
                  </Col>
                </Row>
              </Form>
            </>
          ) : null}
        </Modal>
        <Modal
          zIndex={1200}
          visible={this.state.isPreviewVisible}
          footer={null}
          onCancel={() => {
            this.setState({ isPreviewVisible: false, previewImage: undefined });
          }}
        >
          <img alt="previewImage" style={{ width: '100%' }} src={this.state.previewImage} />
        </Modal>
      </>
    );
  }
}
export const ReviewModal = Form.create()(ReviewModalComponent);
