import React, { useState } from 'react';
import { constants } from '$utils';
import { Descriptions, Divider, Modal, Upload } from 'antd';

export const InfoModal = ({ isViewModalShow, setIsViewModalShow, detail, setDetail }) => {
  const [preview, setPreview] = useState({ isPreviewVisible: false, previewImage: undefined });

  const onOk = () => {
    onCancel();
  };
  const onCancel = () => {
    setIsViewModalShow(false);
    setDetail(undefined);
  };

  return (
    <>
      <Modal title="详情" width={1200} visible={isViewModalShow} onOk={onOk} onCancel={onCancel}>
        {detail ? (
          <>
            <Divider>申请信息</Divider>
            <Descriptions bordered>
              <Descriptions.Item label="用户名称">{detail.customer.name}</Descriptions.Item>
              <Descriptions.Item label="联系电话">{detail.customer.contactPhone}</Descriptions.Item>
              <Descriptions.Item label="用户编号">{detail.customer.cno}</Descriptions.Item>
              <Descriptions.Item label="用户类型">{detail.customer.customerType}</Descriptions.Item>
              <Descriptions.Item label="人口数">{detail.customer.population}</Descriptions.Item>
              <Descriptions.Item label="用户地址">{detail.customer.address}</Descriptions.Item>
              <Descriptions.Item label="身份证号">{detail.customer.idCard}</Descriptions.Item>
              <Descriptions.Item label="用户片区">{detail.customer.areaName}</Descriptions.Item>
              <Descriptions.Item label="水表分类">{detail.customer.meterType}</Descriptions.Item>
              <Descriptions.Item label="用水性质">{detail.customer.waterUseKindType}</Descriptions.Item>
              <Descriptions.Item label="用水分类">{detail.customer.waterUseKindName}</Descriptions.Item>
              <Descriptions.Item label="申请类型">{detail.auditType}</Descriptions.Item>
              <Descriptions.Item label="申请时间" span={3}>
                {detail.createTime}
              </Descriptions.Item>
              <Descriptions.Item label="照片信息">
                <Upload
                  disabled
                  listType="picture-card"
                  fileList={detail.annexList.map((list) => ({
                    uid: list.id,
                    name: list.newName,
                    status: 'done',
                    url: constants.fileUrl + list.filePath + list.newName,
                  }))}
                  onPreview={(file) => {
                    setPreview({ isPreviewVisible: true, previewImage: file.url });
                  }}
                />
              </Descriptions.Item>
            </Descriptions>
            <Divider>变更信息</Divider>
            <Descriptions bordered>
              <Descriptions.Item label="原用户名" span={1}>
                {detail.oldName}
              </Descriptions.Item>
              <Descriptions.Item label="新用户名" span={2}>
                {detail.newName}
              </Descriptions.Item>
              <Descriptions.Item label="原联系电话" span={1}>
                {detail.oldPhone}
              </Descriptions.Item>
              <Descriptions.Item label="新联系电话" span={2}>
                {detail.newPhone}
              </Descriptions.Item>
              <Descriptions.Item label="原用户地址" span={1}>
                {detail.oldAddress}
              </Descriptions.Item>
              <Descriptions.Item label="新用户地址" span={2}>
                {detail.newAddress}
              </Descriptions.Item>
              <Descriptions.Item label="原身份证号码" span={1}>
                {detail.customer.idCard}
              </Descriptions.Item>
              <Descriptions.Item label="新身份证号码" span={2}>
                {detail.idCard}
              </Descriptions.Item>
            </Descriptions>
            {detail.applyStatus !== '已受理' ? (
              <>
                <Divider>审批信息</Divider>
                <Descriptions bordered>
                  <Descriptions.Item label="审批人" span={3}>
                    {detail.reviewUserName}
                  </Descriptions.Item>
                  <Descriptions.Item label="审批结果" span={3}>
                    {detail.reviewStatus}
                  </Descriptions.Item>
                  <Descriptions.Item label="审批时间" span={3}>
                    {detail.updateTime}
                  </Descriptions.Item>
                  <Descriptions.Item label="审批意见" span={3}>
                    {detail.reviewContent}
                  </Descriptions.Item>
                </Descriptions>
              </>
            ) : null}
          </>
        ) : null}
      </Modal>
      <Modal
        zIndex={1200}
        visible={preview.isPreviewVisible}
        footer={null}
        onCancel={() => {
          setPreview({ isPreviewVisible: false, previewImage: undefined });
        }}
      >
        <img alt="previewImage" style={{ width: '100%' }} src={preview.previewImage} />
      </Modal>
    </>
  );
};
