import React, { useCallback, useEffect, useState } from 'react';
import { constants } from '$utils';
import { Button, Table, Tabs } from 'antd';
import { InfoModal } from './InfoModal';
import { ReviewModal } from './ReviewModal';

export const List = ({
  list,
  getList,
  detail,
  getDetail,
  setDetail,
  paginationProps,
  setPaginationProps,
  setReview,
  activeKey,
  setActiveKey,
}) => {
  const [isViewModalShow, setIsViewModalShow] = useState(false);
  const [isReviewModalShow, setIsReviewModalShow] = useState(false);

  useEffect(() => {
    onTabsChange('1');
  }, [onTabsChange]);

  const onTabsChange = useCallback((activeKey) => {
    setActiveKey(activeKey);
    if (activeKey === '1') {
      getList({ pageSize: paginationProps.pageSize, page: 1, review: 0 });
    } else {
      getList({ pageSize: paginationProps.pageSize, page: 1, review: 1 });
    }
  });

  const onView = (record) => {
    if (record.applyId) {
      getDetail(record.applyId).then((response) => {
        if (response.code === 0) {
          setDetail(response.data);
          setIsViewModalShow(true);
        }
      });
    }
  };
  const onReview = (record) => {
    if (record.applyId) {
      getDetail(record.applyId).then((response) => {
        if (response.code === 0) {
          setDetail(response.data);
          setIsReviewModalShow(true);
        }
      });
    }
  };

  const columns = [
    {
      title: '申请编号',
      dataIndex: 'applyNo',
      align: 'center',
    },
    {
      title: '用户编号',
      dataIndex: 'cno',
      align: 'center',
    },
    {
      title: '申请时间',
      dataIndex: 'createTime',
      align: 'center',
    },
    {
      title: '片区',
      dataIndex: 'areaName',
      align: 'center',
    },
    {
      title: '用户名称',
      dataIndex: 'name',
      align: 'center',
    },
    {
      title: '联系电话',
      dataIndex: 'contactPhone',
      align: 'center',
    },
    {
      title: '用户地址',
      dataIndex: 'address',
      align: 'center',
    },
    {
      title: '申请状态',
      dataIndex: 'applyStatus',
      align: 'center',
    },
    {
      title: '操作',
      key: 'operation',
      width: 240,
      align: 'center',
      render: (text, record, index) => {
        return (
          <Button.Group>
            <Button
              title="详情"
              className="btn"
              type="primary"
              size="small"
              icon="eye"
              onClick={() => onView(record)}
            ></Button>
            {activeKey === '2' ? null : (
              <Button
                permission={React.$pmn('REVENUE:BUSINESS_ONLINE_APPROVE:CHANGE_USER_INFO:APPROVE')}
                title="审批"
                className="btn"
                type="primary"
                size="small"
                icon="audit"
                onClick={() => onReview(record)}
              ></Button>
            )}
          </Button.Group>
        );
      },
    },
  ];

  return (
    <>
      <Tabs activeKey={activeKey} onChange={onTabsChange} destroyInactiveTabPane>
        <Tabs.TabPane tab="未审批" key="1">
          <Table
            bordered
            rowKey="applyId"
            columns={columns}
            dataSource={list}
            pagination={{
              ...paginationProps,
              showSizeChanger: true,
              showQuickJumper: true,
              pageSizeOptions: ['10', '20', '50', '100'],
              showTotal: (total) => `共 ${total} 条数据 `,
              onChange: (page, pageSize) => getList({ page, pageSize, review: 0 }),
              onShowSizeChange: (page, pageSize) => getList({ page, pageSize, review: 0 }),
            }}
          ></Table>
        </Tabs.TabPane>
        <Tabs.TabPane tab="已审批" key="2">
          <Table
            bordered
            rowKey="applyId"
            columns={columns}
            dataSource={list}
            pagination={{
              ...paginationProps,
              showSizeChanger: true,
              showQuickJumper: true,
              pageSizeOptions: ['10', '20', '50', '100'],
              showTotal: (total) => `共 ${total} 条数据 `,
              onChange: (page, pageSize) => getList({ page, pageSize, review: 1 }),
              onShowSizeChange: (page, pageSize) => getList({ page, pageSize, review: 1 }),
            }}
          ></Table>
        </Tabs.TabPane>
      </Tabs>
      <InfoModal
        isViewModalShow={isViewModalShow}
        setIsViewModalShow={setIsViewModalShow}
        detail={detail}
        setDetail={setDetail}
      ></InfoModal>
      <ReviewModal
        isReviewModalShow={isReviewModalShow}
        setIsReviewModalShow={setIsReviewModalShow}
        detail={detail}
        setDetail={setDetail}
        setReview={setReview}
        onTabsChange={onTabsChange}
      ></ReviewModal>
    </>
  );
};
