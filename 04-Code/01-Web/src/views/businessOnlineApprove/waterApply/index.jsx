import React, { useState } from 'react';
import http from '$http';
import { List } from './components/List';
import { Search } from './components/Search';
import {excelExport} from '$utils';

const Index = () => {
  const [list, setList] = useState([]);
  const [detail, setDetail] = useState();
  const [activeKey, setActiveKey] = useState('1');
  const [paginationProps, setPaginationProps] = useState({
    page: 1,
    pageSize: 10,
    total: 0,
  });

  const getList = async (params) => {
    const res = await http.post('/revenue/wx/waterApply/listPageWeb', params);
    if (res.code === 0) {
      setList(res.data.rows);
      setPaginationProps({ page: params.page, pageSize: res.data.pageSize, total: res.data.total });
    }
  };

  const getDetail = (id) => {
    return http.get('/revenue/wx/waterApply/getDetail/' + id);
  };


  const accept = (params) => {
    return http.post('/revenue/wx/waterApply/accepted', params);
  };
  const complete = (params) => {
    return http.post('/revenue/wx/waterApply/completed', params);
  };

  const getExportData = (params) => {
    // http.export('/revenue/wx/waterApply/export', params, (res) => {
    //   const blob = new Blob([res]);
    //   const downloadElement = document.createElement('a');
    //   const href = window.URL.createObjectURL(blob);
    //   const fileName = '供水报装导出' + new Date().getTime() + '.xlsx';
    //   downloadElement.href = href;
    //   downloadElement.download = fileName;
    //   document.body.appendChild(downloadElement);
    //   downloadElement.click();
    //   document.body.removeChild(downloadElement);
    //   window.URL.revokeObjectURL(href);
    // });
    const { searchForm } = this.state;
    excelExport("/revenue/wx/waterApply/export", "供水报装导出"+ new Date().getTime(), searchForm);
  };

  return (
    <div className="shadow-radius">
      <Search setList={setList} getList={getList} activeKey={activeKey} getExportData={getExportData}></Search>
      <List
        list={list}
        getList={getList}
        setList={setList}
        detail={detail}
        getDetail={getDetail}
        setDetail={setDetail}
        paginationProps={paginationProps}
        setPaginationProps={setPaginationProps}
        accept={accept}
        complete={complete}
        activeKey={activeKey}
        setActiveKey={setActiveKey}
      ></List>
    </div>
  );
};

export default Index;
