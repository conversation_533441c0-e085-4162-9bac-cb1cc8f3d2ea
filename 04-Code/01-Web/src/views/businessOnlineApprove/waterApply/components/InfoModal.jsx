import React, { useState } from 'react';
import { constants } from '$utils';
import { Descriptions, Divider, Modal, Upload } from 'antd';

export const InfoModal = ({ isViewModalShow, setIsViewModalShow, detail, setDetail }) => {
  const [preview, setPreview] = useState({ isPreviewVisible: false, previewImage: undefined });

  const onOk = () => {
    onCancel();
  };
  const onCancel = () => {
    setIsViewModalShow(false);
    setDetail(undefined);
  };

  return (
    <>
      <Modal title="详情" width={1200} visible={isViewModalShow} onOk={onOk} onCancel={onCancel}>
        {detail ? (
          <>
            <Divider>申请信息</Divider>
              <Descriptions bordered>
                  <Descriptions.Item
                          label="建设单位">{detail.constructionUnit}</Descriptions.Item>
                  <Descriptions.Item
                          label="项目名称">{detail.projectName}</Descriptions.Item>
                  <Descriptions.Item
                          label="项目地址">{detail.projectAddress}</Descriptions.Item>
                  <Descriptions.Item
                          label="法人名称">{detail.frName}</Descriptions.Item>
                  <Descriptions.Item
                          label="法人电话">{detail.frPhone}</Descriptions.Item>
                  <Descriptions.Item
                          label="经办人名称">{detail.handleName}</Descriptions.Item>
                  <Descriptions.Item
                          label="经办人电话">{detail.handlePhone}</Descriptions.Item>
                  <Descriptions.Item
                          label="预估日用水量">{detail.dailyWaterVolume}</Descriptions.Item>
                  <Descriptions.Item
                          label="用水类别">{detail.waterType === 0 ? '临时' : '永久'}</Descriptions.Item>
                  <Descriptions.Item
                          label="用水性质">{detail.waterUseKindName}</Descriptions.Item>
                  <Descriptions.Item
                          label="预计装表数量">{detail.meterNumber}</Descriptions.Item>
                  <Descriptions.Item
                          label="申请状态">{detail.waterApplyStatus}</Descriptions.Item>
                  <Descriptions.Item label="申请时间" span={3}>{detail.createTime}</Descriptions.Item>
                  <Descriptions.Item
                          label="受理人">{detail.acceptedName}</Descriptions.Item>
                  <Descriptions.Item label="受理时间" span={3}>{detail.acceptedTime}</Descriptions.Item>
                  <Descriptions.Item
                          label="完成人">{detail.completedName}</Descriptions.Item>
                  <Descriptions.Item label="完成时间" span={3}>{detail.completedTime}</Descriptions.Item>
                  <Descriptions.Item label="照片信息">
                      <Upload
                              disabled
                              listType="picture-card"
                              fileList={detail.sysAnnexList.map((list) => ({
                                  uid: list.id,
                                  name: list.newName,
                                  status: 'done',
                                  url: constants.fileUrl + list.filePath + list.newName,
                              }))}
                              onPreview={(file) => {
                                  setPreview({ isPreviewVisible: true, previewImage: file.url });
                              }}
                      />
                  </Descriptions.Item>
              </Descriptions>
          </>
        ) : null}
      </Modal>
      <Modal
        zIndex={1200}
        visible={preview.isPreviewVisible}
        footer={null}
        onCancel={() => {
          setPreview({ isPreviewVisible: false, previewImage: undefined });
        }}
      >
        <img alt="previewImage" style={{ width: '100%' }} src={preview.previewImage} />
      </Modal>
    </>
  );
};
