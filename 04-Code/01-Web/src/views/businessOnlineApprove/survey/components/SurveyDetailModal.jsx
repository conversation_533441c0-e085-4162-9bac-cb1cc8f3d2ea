import React, { Component } from 'react';
import { Modal, Descriptions, Divider, Button, Row, Col, Spin } from 'antd';
import '../index.scss';

class SurveyDetailModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false
    };
  }

  renderMultipleChoice = (options) => {
    // 如果选项不存在或为空数组，返回未作答
    if (!options || !options.length) return '未作答';

    // 客户信息选项映射
    const customerInfoMappings = {
      '0': '男',
      '1': '女',
      '2': '20岁以下',
      '3': '21-40岁',
      '4': '41-60岁',
      '5': '61岁以上',
      // 兼容字符串选项
      'male': '男',
      'female': '女',
      'under20': '20岁以下',
      '21-40': '21-40岁',
      '41-60': '41-60岁',
      'over60': '61岁以上'
    };

    // 将每个选项值映射到显示文本
    const mappedOptions = options.map(option => {
      const optionStr = String(option);
      return customerInfoMappings[optionStr] || optionStr;
    });

    // 用顿号连接选项
    return mappedOptions.join('、');
  }

  renderSingleChoice = (option, questionType) => {
    debugger
    // 如果选项不存在，返回未作答
    if (option === undefined || option === null) return '未作答';

    // 根据问题类型和选项值映射到显示文本
    const optionMappings = {
      // 问题2 - 总体服务满意度
      'question2': {
        '0': '非常满意',
        '1': '满意',
        '2': '不满意'
      },

      // 问题3 - 交费方式便利性
      'question3': {
        '0': '非常方便',
        '1': '方便',
        '2': '不太方便'
      },

      // 问题4 - 水费负担
      'question4': {
        '0': '无足轻重',
        '1': '负担较小',
        '2': '负担沉重'
      },

      // 问题5-7, 9-10 - 满意度评价
      'satisfaction': {
        '0': '非常满意',
        '1': '满意',
        '2': '不满意'
      },

      // 问题8 - 供水稳定性
      'question8': {
        '0': '非常稳定',
        '1': '稳定'
      },

      // 字符串选项映射（兼容旧数据）
      'string': {
        'verySatisfied': '非常满意',
        'satisfied': '满意',
        'notSatisfied': '不满意',
        'veryConvenient': '非常方便',
        'convenient': '方便',
        'notConvenient': '不太方便',
        'negligible': '无足轻重',
        'smallBurden': '负担较小',
        'heavyBurden': '负担沉重',
        'veryGood': '非常满意',
        'good': '满意',
        'bad': '不满意',
        'veryStable': '非常稳定',
        'stable': '稳定'
      }
    };

    // 将选项转为字符串以便查找映射
    const optionStr = String(option);

    // 先尝试根据问题类型查找映射
    if (questionType && optionMappings[questionType] && optionMappings[questionType][optionStr]) {
      return optionMappings[questionType][optionStr];
    }

    // 如果是字符串类型的选项值，尝试从字符串映射中查找
    if (typeof option === 'string' && optionMappings.string[option]) {
      return optionMappings.string[option];
    }

    // 如果都没找到，返回原始值
    return optionStr;
  }

  render() {
    const { visible, onCancel, detail } = this.props;
    const { loading } = this.state;

    if (!detail) return null;

    return (
      <Modal
        title="问卷调查详情"
        visible={visible}
        onCancel={onCancel}
        width="80vw"
        footer={null}
        destroyOnClose={true}
      >
        <Spin spinning={loading}>
          <div className="survey-detail">
            <Descriptions bordered column={1}>
              <Descriptions.Item label="填写人">{detail.name || '未知'}</Descriptions.Item>
              <Descriptions.Item label="填写时间">{detail.createTime || '未知'}</Descriptions.Item>

              <Descriptions.Item label="1、客户信息（多选）">
                {this.renderMultipleChoice(detail.question1)}
              </Descriptions.Item>

              <Descriptions.Item label="2、您对目前东源水务公司的总体服务是否感到满意？">
                {this.renderSingleChoice(detail.question2,'question2')}
              </Descriptions.Item>

              <Descriptions.Item label="3、您认为目前东源水务公司提供的交费方式是否方便？">
                {this.renderSingleChoice(detail.question3,'question3')}
              </Descriptions.Item>

              <Descriptions.Item label="4、目前您的每月水费支出在生活费用支出中是否可以承受？">
                {this.renderSingleChoice(detail.question4,'question4')}
              </Descriptions.Item>

              <Descriptions.Item label="5、请问您对供水服务热线的服务态度如何评价？">
                {this.renderSingleChoice(detail.question5,'satisfaction')}
              </Descriptions.Item>

              <Descriptions.Item label="6、您对供水公司营业大厅的服务态度是否满意？">
                {this.renderSingleChoice(detail.question6,'satisfaction')}
              </Descriptions.Item>

              <Descriptions.Item label="7、您对目前自来水的水质是否感到满意？">
                {this.renderSingleChoice(detail.question7,'satisfaction')}
              </Descriptions.Item>

              <Descriptions.Item label="8、您家的供水稳定性（24小时连续供水）是否满意？">
                {this.renderSingleChoice(detail.question8,'question8')}
              </Descriptions.Item>

              <Descriptions.Item label="9、您对东源水务公司的抢修及时率是否满意？">
                {this.renderSingleChoice(detail.question9,'satisfaction')}
              </Descriptions.Item>

              <Descriptions.Item label="10、您对东源水务公司因突发事件影响到供水服务时的应急措施是否满意？">
                {this.renderSingleChoice(detail.question10,'satisfaction')}
              </Descriptions.Item>

              <Descriptions.Item label="11、其他建议、意见：">
                {detail.question11 || '无'}
              </Descriptions.Item>
            </Descriptions>

            <Row style={{ marginTop: '20px' }}>
              <Col align="center">
                <Button type="default" onClick={onCancel}>
                  关闭
                </Button>
              </Col>
            </Row>
          </div>
        </Spin>
      </Modal>
    );
  }
}

export default SurveyDetailModal;
