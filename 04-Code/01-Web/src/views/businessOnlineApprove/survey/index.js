import React, {Component} from 'react';
import {actionCreators} from './store';
import {connect} from 'react-redux';
import {
    Button,
    Col,
    DatePicker,
    Descriptions,
    Divider,
    Form,
    Icon,
    Input,
    Modal,
    Row,
    Select,
    Table
} from 'antd';
import {constants} from '$utils';
import './index.scss';
import SurveyDetailModal from './components/SurveyDetailModal';


const {confirm} = Modal;
const FormItem = Form.Item;
const {Option} = Select;
const ButtonGroup = Button.Group;
const {RangePicker} = DatePicker;
const TYPE = [{
    label: '全部', value: null
}, {
    label: '建议', value: 2
}, {
    label: '投诉', value: 3
}, {
    label: '反映问题', value: 4
}];
const defaultSearchForm = {
    name: undefined,
    type: undefined,
    phone: undefined
};

class Index extends Component {

    constructor(props) {
        super(props);
        this.state = {
            expand: false, // 搜索项过多的时候，隐藏部分
            page: 1,
            pageSize: 10,
            searchForm: defaultSearchForm,
            visible: false,
        };
    }

    componentDidMount() {
        const {fields} = this.props;
        const isTags = localStorage.getItem('tags');
        // 获取当前 reducer fields的值，
        // 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
        if (fields && isTags) {
            this.setState({
                ...fields.state
            }, () => {
                this.props.form.setFieldsValue({
                    ...fields.form
                });
            });
        } else {
            this.getList();
        }
    }

    componentWillUnmount() {
        const {form, setState} = this.props;
        // 判断当前页面是否存在 antd form 的控件
        // 如果有控件 则存储 form 控件的值和state的值
        if (form) {
            setState({
                key: 'fields', value: {
                    form: form.getFieldsValue(), state: this.state
                }
            });
        } else {
            // 如果没有form 控件的值，则只存储state的值
            setState({
                key: 'fields', value: {
                    state: this.state
                }
            });
        }
    }

    // 获取列表
    getList = () => {
        const {page, pageSize, searchForm} = this.state;
        this.props.list(Object.assign({...searchForm}, {page, pageSize}));
    };

    // 列表分页
    handlePageChange = (page) => {
        this.setState({
            page
        }, () => {
            this.getList();
        });
    };

    // 改变分页条数
    pageSizeChange = (current, size) => {
        this.setState({
            page: 1, pageSize: size
        }, () => {
            this.getList();
        });
    };

    // 搜索功能
    handleSearch = () => {
        this.setState({
            page: 1
        }, () => {
            this.getList();
        });
    };

    // 重置搜索
    handleReset = () => {
        this.props.form.setFieldsValue({rangPicker: [undefined, undefined]})
        this.setState({
            searchForm: Object.assign({}, defaultSearchForm)
        }, () => {
            this.getList();
        });
    };

    // 查看详情
    handleView = record => {
        this.props.getDetail(record.id)
        this.setState({visible: true})
    };

    handleCancel = () => {
        this.setState({visible: false})
    }
    // 编辑记录
    handleEdit = record => {
    };

    // 删除记录
    handleDel = record => {
    };

    getOrderDate = (date, dateString) => {
        if (dateString.length === 2) {
            this.setState({
                searchForm: {
                    ...this.state.searchForm,
                    createTimeStart: dateString[0],
                    createTimeEnd: dateString[1]
                }
            });
        }
    };

    // 渲染搜索
    _renderSearchForm = () => {
        const {searchForm} = this.state;
        const {form} = this.props
        const {getFieldDecorator} = form;

        return (
                <Form className="ant-advanced-search-form" {...constants.formItemLayout}>
                    {/*<Row>*/}
                    {/*    <Col span={8}>*/}
                    {/*        <FormItem label={'投诉人:'}>*/}
                    {/*            <Input*/}
                    {/*                    placeholder="请输入投诉人"*/}
                    {/*                    value={searchForm.name}*/}
                    {/*                    onChange={(v) => {*/}
                    {/*                        this.setState({*/}
                    {/*                            searchForm: {*/}
                    {/*                                ...searchForm,*/}
                    {/*                                name: v.target.value*/}
                    {/*                            }*/}
                    {/*                        });*/}
                    {/*                    }}*/}
                    {/*            />*/}
                    {/*        </FormItem>*/}
                    {/*    </Col>*/}

                    {/*    <Col span={8}>*/}
                    {/*        <FormItem label={'联系电话:'}>*/}
                    {/*            <Input*/}
                    {/*                    placeholder="请输入联系电话"*/}
                    {/*                    value={searchForm.phone}*/}
                    {/*                    onChange={(v) => {*/}
                    {/*                        this.setState({*/}
                    {/*                            searchForm: {*/}
                    {/*                                ...searchForm,*/}
                    {/*                                phone: v.target.value*/}
                    {/*                            }*/}
                    {/*                        });*/}
                    {/*                    }}*/}
                    {/*            />*/}
                    {/*        </FormItem>*/}
                    {/*    </Col>*/}

                    {/*    <Col span={8}>*/}
                    {/*        <FormItem label={'类别:'}>*/}
                    {/*            <Select placeholder="请选择类别"*/}
                    {/*                    value={searchForm.type}*/}
                    {/*                    onChange={v => {*/}
                    {/*                        this.setState({*/}
                    {/*                            searchForm: {*/}
                    {/*                                ...searchForm, type: v*/}
                    {/*                            }*/}
                    {/*                        });*/}
                    {/*                    }}>*/}
                    {/*                {TYPE.map((item, index) => {*/}
                    {/*                    return (<Option key={index}*/}
                    {/*                                    value={item.value}>*/}
                    {/*                                {item.label}*/}
                    {/*                            </Option>);*/}
                    {/*                })}*/}
                    {/*            </Select>*/}
                    {/*        </FormItem>*/}
                    {/*    </Col>*/}

                    {/*    <Col span={8}>*/}
                    {/*        <FormItem label={'投诉时间:'}>*/}
                    {/*            {getFieldDecorator('rangPicker',*/}
                    {/*                    {rules: [{type: 'array'}]})(<RangePicker*/}
                    {/*                    onChange={this.getOrderDate}*/}
                    {/*                    placeholder={['开始时间',*/}
                    {/*                        '结束时间']}/>)}*/}
                    {/*        </FormItem>*/}
                    {/*    </Col>*/}

                    {/*</Row>*/}
                    {/*<Row>*/}
                    {/*    <Col span={16} offset={8} align="right">*/}
                    {/*        <FormItem>*/}
                    {/*            <Button type="primary"*/}
                    {/*                    onClick={this.handleSearch}>*/}
                    {/*                搜索*/}
                    {/*            </Button>*/}
                    {/*            &nbsp;&nbsp;&nbsp;&nbsp;*/}
                    {/*            <Button type="default"*/}
                    {/*                    onClick={this.handleReset}>*/}
                    {/*                重置*/}
                    {/*            </Button>*/}
                    {/*        </FormItem>*/}
                    {/*    </Col>*/}
                    {/*</Row>*/}
                </Form>);
    };

  _renderModal = () => {
    const {visible} = this.state;
    const {detail} = this.props;
    return (
      <SurveyDetailModal
        visible={visible}
        onCancel={this.handleCancel}
        detail={detail}
      />
    );
  }
    render() {
        const {page, pageSize} = this.state;
        const {dataList, total} = this.props;
        const paginationProps = {
            page,
            pageSize,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ['10', '20', '50', '100'],
            total: total,
            showTotal: (total) => {
                return `共 ${total} 条数据 `;
            },
            onChange: (page) => this.handlePageChange(page),
            onShowSizeChange: (current, size) => this.pageSizeChange(current,
                    size)
        };
        const columns = [{
            title: '填写人', dataIndex: 'name', align: 'center'
        }, {
            title: '填写时间', dataIndex: 'createTime', align: 'center'
        },  {
            title: '操作',
            key: 'operation',
            align: 'center',
            fixed: 'right',
            width: 80,
            render: (text, record, index) => {
                return (<span>
										<Button title="查看" className="btn"
                                                type="primary" size="small"
                                                icon="eye"
                                                onClick={() => this.handleView(
                                                        record)}/>
									</span>)
            }
        }];
        return (<div className="shadow-radius info-change">
                    <Row>
                        <Col><h1>问卷调查</h1></Col>
                    </Row>
                    <Row>
                        {this._renderSearchForm()}
                    </Row>
                    <Row className='main'>
                        <Table
                                bordered
                                rowKey={(record) => record.id}
                                columns={columns}
                                dataSource={dataList}
                                pagination={paginationProps}
                        />
                    </Row>
                    {this._renderModal()}
                </div>);
    }
}

const mapStateToProps = state => {
    const data = state.get('survey');
    return {
        fields: data.fields, dataList: data.dataList, // 列表数据
        total: data.total, // 总条数
        visible: data.visible, // 是否显示弹窗
        detail: data.detail, // 获取详情
        changePersonList: data.changePersonList
    };
};
const mapDispatchToProps = dispatch => ({
    setState: data => dispatch(actionCreators.setState(data)), // 设置redux值
    list: data => dispatch(actionCreators.list(data)), // 获取列表
    getDetail: data => dispatch(actionCreators.detail(data)), // 获取详情
});
export default connect(mapStateToProps, mapDispatchToProps)(
        (Form.create())(Index));
