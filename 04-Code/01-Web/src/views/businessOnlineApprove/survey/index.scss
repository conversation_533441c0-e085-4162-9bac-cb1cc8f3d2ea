.reload{
	.ant-advanced-search-form .ant-form-item {
		width: 80%;
		display: flex;
	}

	.ant-advanced-search-form .ant-form-item-control-wrapper {
		flex: 1;
	}
	.main{
		margin-top: 20px;
	}
	.ant-tag-blue {
		color: #1890ff;
		background: #fff;
		border-color: #fff;
	}
	.ant-tag-magenta{
		color: #ff0000;
		background: #fff;
		border-color: #fff;
	}
	.btn{
		margin-right: 5px;
		margin-bottom: 5px;
	}
}
.reload-modal {
  width: 70% !important;
  .ant-input-disabled {
  }
  .ant-input[disabled] {
    color: rgba(0, 0, 0, 0.65);
    background-color: #fff;
    cursor: not-allowed;
    opacity: 1;
    border: 0;
    cursor: default;
  }
  .btn {
    margin: 0 10px;
  }
	.survey-detail {
		max-height: 70vh;
		overflow-y: auto;

		.ant-descriptions-item-label {
			width: 40%;
			font-weight: 500;
		}

		.ant-descriptions-item-content {
			width: 60%;
		}
	}
}
