.bank-info {
	.search {
		display: flex;
		justify-content: space-evenly;
	}

	.search-button {
		margin-top: 15px;
		display: flex;
		justify-content: center;

		Button {
			width: 80px;
			height: 30px;
			border-radius: 5px;
			margin-left: 10px;
		}

	}
}
.editor-demo {
	.home-editor {
		min-height: 400px !important; // 增加编辑器最小高度
		border: 1px solid #F1F1F1;
		padding: 5px;
		border-radius: 2px;
	}

	.home-wrapper {
		border: 1px solid #ccc;
		margin-bottom: 20px;
	}

	.home-toolbar {
		border: 1px solid #ddd;
	}

	.rdw-editor-main {
		min-height: 500px; // 确保编辑区域也足够高
		max-height: calc(100vh - 300px); // 防止超出屏幕
		overflow-y: auto;
	}
}

// 调整模态框样式
.ant-modal-wrap {
	.ant-modal {
		padding-bottom: 0;

		.ant-modal-body {
			padding: 24px;
			max-height: calc(100vh - 180px);
			overflow-y: auto;
		}

		.ant-form-item {
			margin-bottom: 16px;
		}
	}
}

// 确保编辑器工具栏固定在顶部
.rdw-editor-wrapper {
	.rdw-editor-toolbar {
		position: sticky;
		top: 0;
		z-index: 100;
		background: white;
	}
}
