import React from 'react';
import http from '$http';
import { Button, Col, DatePicker, Form, Input, Row, Select, TreeSelect } from 'antd';
import moment from "moment";

const searchItemLayout = {
  labelCol: {
    xs: { span: 6 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 15 },
    sm: { span: 15 },
  },
};
const STOP_APPLY_TYPE = {
  报停: 0,
  销户: 1,
};

const ONLINE_APPLY_STATUS = {
  已受理: 1,
  已通过: 4,
  已拒绝: 2,
};

class SearchComponent extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      list: [],
      areaTree: [],
    };
  }

  componentDidMount = () => {
    this.getAreaTree();
  };

  getAreaTree = async () => {
    const res = await http.get('/api/sys/area/getTree');
    if (res.code === 0) {
      this.setState({ areaTree: res.data });
    }
  };

  // 渲染片区
  _renderTreeLoop = (areas, isShow) => {
    return areas.map((item) => {
      if (item.children) {
        return (
          <TreeSelect.TreeNode key={item.id} title={item.name} value={item.id} disabled={isShow}>
            {this._renderTreeLoop(item.children, isShow)}
          </TreeSelect.TreeNode>
        );
      } else {
        return <TreeSelect.TreeNode key={item.id} title={item.name} value={item.id} />;
      }
    });
  };

  handleSearch = () => {
    this.props.form.validateFields((error, values) => {
      if (!error) {
        if (this.props.activeKey === '1') {
          this.props.getList({ ...values, page: 1, pageSize: 10, review: 0,applyStartTime:values.applyTime[0].format('YYYY-MM-DD'),applyEndTime:values.applyTime[1].format('YYYY-MM-DD')  });
        } else {
          this.props.getList({ ...values, page: 1, pageSize: 10, review: 1,applyStartTime:values.applyTime[0].format('YYYY-MM-DD'),applyEndTime:values.applyTime[1].format('YYYY-MM-DD')  });
        }
      }
    });
  };

  handleReset = () => {
    this.props.form.resetFields();
    if (this.props.activeKey === '1') {
      this.props.getList({ page: 1, pageSize: 10, review: 0 });
    } else {
      this.props.getList({ page: 1, pageSize: 10, review: 1 });
    }
  };

  onExportDataClick = () => {
    this.props.form.validateFields((error, values) => {
      if (!error) {
        if (this.props.activeKey === '1') {
          this.props.getExportData({ ...values, review: 0 });
        } else {
          this.props.getExportData({ ...values, review: 1 });
        }
      }
    });
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    return (
      <>
        <h1>水价变更审批</h1>
        <Form className="ant-advanced-search-form" {...searchItemLayout}>
          <Row>
            <Col span={8}>
              <Form.Item label="用户编号">{getFieldDecorator('cno')(<Input placeholder="请输入用户编号" />)}</Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="用户名称">
                {getFieldDecorator('name')(<Input placeholder="请输入用户名称" />)}
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="片区">
                {getFieldDecorator('areaId')(
                  <TreeSelect
                    style={{ width: '100%' }}
                    dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                    placeholder="请选择片区"
                    allowClear
                  >
                    {this._renderTreeLoop(this.state.areaTree, true)}
                  </TreeSelect>,
                )}
              </Form.Item>
            </Col>
            {/*<Col span={8}>*/}
            {/*  <Form.Item label="申请类型">*/}
            {/*    {getFieldDecorator('stopApplyType')(*/}
            {/*      <Select placeholder="请选择申请类型">*/}
            {/*        <Select.Option value={STOP_APPLY_TYPE.报停}>报停</Select.Option>*/}
            {/*        <Select.Option value={STOP_APPLY_TYPE.销户}>销户</Select.Option>*/}
            {/*      </Select>,*/}
            {/*    )}*/}
            {/*  </Form.Item>*/}
            {/*</Col>*/}
            <Col span={8}>
              <Form.Item label="申请时间">
                {getFieldDecorator('applyTime')(<DatePicker.RangePicker style={{ width: '100%' }} />)}
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="申请状态">
                {getFieldDecorator('onlineApplyStatus')(
                  <Select placeholder="请选择申请状态">
                    <Select.Option value={ONLINE_APPLY_STATUS.已受理}>已受理</Select.Option>
                    <Select.Option value={ONLINE_APPLY_STATUS.已通过}>已通过</Select.Option>
                    <Select.Option value={ONLINE_APPLY_STATUS.已拒绝}>已拒绝</Select.Option>
                  </Select>
                )}
              </Form.Item>
            </Col>
            <Col span={24} align="right">
              <Button type="primary" onClick={this.handleSearch} style={{ marginRight: 24 }}>
                搜索
              </Button>
              <Button type="default" onClick={this.handleReset} style={{ marginRight: 24 }}>
                重置
              </Button>
              <Button type="primary" onClick={this.onExportDataClick}>
                导出
              </Button>
            </Col>
          </Row>
        </Form>
      </>
    );
  }
}

export const Search = Form.create()(SearchComponent);
