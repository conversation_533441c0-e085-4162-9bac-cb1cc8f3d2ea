import React, { Component, Fragment } from 'react';
import { actionCreators } from './store';
import { connect } from 'react-redux';
import http from '$http';
import {
		Upload,
		message,
		Modal,
		Table,
		Form,
		Select,
		Row,
		Col,
		Input,
		Button,
		DatePicker,
		Icon,
		Tag,
		InputNumber
} from 'antd';
import moment from 'moment';
import { constants } from '$utils';
import {
	WATER_METER_STATUS,
	WATER_METER_TYPE,
	WATER_METER_KIND,
	WATER_METER_MANUFACTURER,
	WATER_METER_CALIBER,
	VALVE_STATUE,

} from '@/constants/waterMeter';
import './index.scss';
import WaterMeterDetail from './components/WaterMeterDetail';
import { waterMeterStatusColorMap, valveStatusColorMap } from '@/constants/colorStyle';

const { confirm } = Modal;
const FormItem = Form.Item;
const { Option } = Select;
const { TextArea } = Input;
const { RangePicker, MonthPicker } = DatePicker;
const InputGroup = Input.Group;


const defaultSearchForm = {
	no: '',
	qrCodeNumber: '',
	status: null,
	type: null,
	kind: null,
	manufacturer: null,
};

class Index extends Component {

	constructor(props) {
		super(props);
		this.state = {
			expand: false, // 搜索项过多的时候，隐藏部分
			page: 1,
			pageSize: 10,
			searchForm: defaultSearchForm,
			modalType: 'view',
			modalTitle: '',
			searchWaterMeterType: null,
			waterMeterType: null,
			installTime: '',
			appearanceDate: '',
			detailModalVisible: false,
			installRangeStart: null, 
			installRangeEnd: null, 
			appearanceRangeStart: null, 
			appearanceRangeEnd: null,
		};
	}

	componentDidMount() {
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields && isTags) {
			this.setState({
				...fields.state
			}, () => {
				this.props.form.setFieldsValue({
					...fields.form
				});
			});
		} else {
			this.getList();
			// this.props.getInstallLocationSelect()
		}
	}

	componentWillUnmount() {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			});
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			});
		}
	}

	// 获取列表
	getList = () => {
		const { page, pageSize, searchForm } = this.state;
		this.props.list(Object.assign({ ...searchForm }, { page, pageSize }));
	};

	// 列表分页
	handlePageChange = page => {
		this.setState(
			{
				page
			},
			() => {
				this.getList();
			}
		);
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState(
			{
				page: 1,
				pageSize: size
			},
			() => {
				this.getList();
			}
		);
	};

	// 搜索功能
	handleSearch = () => {
		this.setState(
			{
				page: 1
			},
			() => {
				this.getList();
			}
		);
	};

	// 重置搜索
	handleReset = () => {
		this.setState({ searchForm: Object.assign({}, defaultSearchForm), installRangeStart: null, installRangeEnd: null, appearanceRangeStart: null, appearanceRangeEnd: null, },
			() => {
				this.getList();
			}
		);
	};

	// 查看详情
	handleView = record => {
		this.props.getDetail(record.id);
		this.props.getWaterMeterChangeList(record.id);
		this.setState({ detailModalVisible: true });
	};

	// 编辑
	handleEdit = record => {
		this.props.form.resetFields();
		this.props.getInstallLocationTypeSelect();
		this.setState(
			{
				modalType: 'edit',
				modalTitle: '修改水表信息'
			},
			() => {
				let selects = WATER_METER_TYPE.filter(res =>res.label === (record && record.type));
				let waterMeterType = selects && selects.length > 0 ? selects[0].value : null;
				this.setState({waterMeterType});
				this.props.setState([{ key: 'visible', value: true }]);
				this.props.getDetail(record.id);
			}
		);
	};

	// 取消弹窗
	handleCancel = () => {
		this.props.setState([{ key: 'visible', value: false }, { key: 'detail', value: null }]);
	};

	// 取消详情
	handleCancelDetail = () => {
		this.setState({ detailModalVisible: false });
	};

	// 删除记录
	handleDel = record => {
		const _this = this;
		confirm({
			title: '确认操作',
			content: '是否确认删除该水表？',
			okText: '确定',
			okType: 'danger',
			cancelText: '取消',
			onOk() {
				_this.props.del(record.id, _this.getList);
			},
			onCancel() {
				console.log('Cancel');
			}
		});
	};

	// 设置状态为库存
	handleUpdateToStock = record => {
		const _this = this;
		confirm({
			title: '确认操作',
			content: '是否确认将该水表重新加入库存？',
			okText: '确定',
			okType: 'danger',
			cancelText: '取消',
			onOk() {
				_this.props.updateToStock(record.id, _this.getList);
			},
			onCancel() {
				console.log('Cancel');
			}
		});
	};

	// 提交信息
	handleSubmit = e => {
		const { modalType } = this.state;
		e.preventDefault();
		this.props.form.validateFields((err, values) => {
			if (!err) {
				let params = {};
				if (modalType === 'edit') {
					params = Object.assign({ ...values }, {
						id: this.props.detail.id,
						installTime: this.state.installTime,
						appearanceDate: this.state.appearanceDate
					});
					this.props.modify(params, this.getList);
				} else {
					params = Object.assign({ ...values }, {
						installTime: this.state.installTime,
						appearanceDate: this.state.appearanceDate
					});
					this.props.add(params, this.getList);
				}
				this.handleCancel();
			}
		});
	};

	// 新增记录
	handleAdd = () => {
		this.props.getInstallLocationTypeSelect();
		this.setState(
			{
				modalType: 'add',
				modalTitle: '添加水表'
			},
			() => {
				this.props.setState([{ key: 'visible', value: true }, { key: 'detail', value: null }]);
			}
		);
	};

	// 模板下载
	handleDownload = () => {
		window.open(`${process.env.UPLOAD_ROOT}templates/water_meter_import_template.xlsx`);
	};

	// 安装位置分类改变
	handleChangeInstallLocationType = v => {
		this.props.getInstallLocationSelect(v);
		this.props.form.resetFields(['installLocationId']);
	};

	// 获取安装日期
	getInstallTime = (date, dateString) => {
		this.setState({ installTime: dateString });
	};

	// 获取出厂日期
	getAppearanceDate = (date, dateString) => {
		this.setState({ appearanceDate: dateString });
	};

	// 重置水表类型选择框
	resetKindSelect = waterMeterType => {
		this.setState({ waterMeterType });
		this.props.form.resetFields(['kind']);
	  this.props.form.setFieldsValue({"kind":null});

	};

	//导出
	export() {
		const { searchForm } = this.state;
		let url = `${process.env.API_ROOT}/api/wm/watermeter/exportWaterMeter`;
		http.export(url, searchForm, (res) => {
			const blob = new Blob([res]);
			const downloadElement = document.createElement('a');
			const href = window.URL.createObjectURL(blob);
			const fileName = '水表记录_' + new Date().getTime() + '.xlsx';
			downloadElement.href = href;
			downloadElement.download = fileName;
			document.body.appendChild(downloadElement);
			downloadElement.click();
			document.body.removeChild(downloadElement);
			window.URL.revokeObjectURL(href);
		});
	}

	// 渲染搜索
	_renderSearchForm = () => {
		const { searchWaterMeterType, searchForm, installRangeStart, installRangeEnd, appearanceRangeStart, appearanceRangeEnd } = this.state;
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					<Col span={8}>
						<FormItem label={'水表编号:'}>
							<Input placeholder="请输入水表编号"
										 value={searchForm.no}
										 onChange={v => {
											 this.setState({ searchForm: { ...this.state.searchForm, no: v.target.value } });
										 }}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'二维码编号:'}>
							<Input
								placeholder="请输入二维码编号"
								value={searchForm.qrCodeNumber}
								onChange={v => {
									this.setState({ searchForm: { ...this.state.searchForm, qrCodeNumber: v.target.value } });
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'水表状态:'}>
							<Select
								placeholder="请选择"
								value={searchForm.status}
								onChange={v => {
									this.setState({
										searchForm: { ...this.state.searchForm, status: v }
									});
								}}
							>
								{WATER_METER_STATUS.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>

					<Col span={8}>
							<FormItem label={'阀门状态:'}>
									<Select
											placeholder="请选择"
											value={searchForm.valveStatus}
											onChange={v => {
													this.setState({
															searchForm: { ...this.state.searchForm, valveStatus: v }
													});
											}}
									>
											{VALVE_STATUE.map((item, index) => {
													return (
															<Option key={index} value={item.value}>
																	{item.label}
															</Option>
													);
											})}
									</Select>
							</FormItem>
					</Col>

						<Col span={8}>
								<FormItem label={'电压:'}>
										<InputGroup>
												<InputNumber
														step={1}
														className="noBorderRight"
														style={{ width: '40%' }}
														value={this.state.searchForm.voltageStart}
														onChange={(value) => {
																this.setState({
																		searchForm: {
																				...searchForm,
																				voltageStart: value
																		}
																});
														}}
												/>
												<InputNumber className="midIpt noBorderLeft noBorderRight" style={{ width: '20%' }} placeholder="~"
																		 disabled />
												<InputNumber
														step={1}
														className="noBorderLeft"
														style={{ width: '40%' }}
														value={this.state.searchForm.voltageEnd}
														onChange={(value) => {
																this.setState({
																		searchForm: {
																				...searchForm,
																				voltageEnd: value
																		}
																});
														}}
												/>
										</InputGroup>
								</FormItem>
						</Col>


						<Col span={8}>
								<FormItem label={'水表示数:'}>
										<InputGroup>
												<InputNumber
														step={1}
														className="noBorderRight"
														style={{ width: '40%' }}
														onChange={(value) => {
																this.setState({
																		searchForm: {
																				...searchForm,
																				wheelPresentNumberStart: Math.floor(value)
																		}
																});
														}}
												/>
												<InputNumber className="midIpt noBorderLeft noBorderRight" style={{ width: '20%' }} placeholder="~"
																		 disabled />
												<InputNumber
														step={1}
														className="noBorderLeft"
														style={{ width: '40%' }}
														onChange={(value) => {
																this.setState({
																		searchForm: {
																				...searchForm,
																				wheelPresentNumberEnd: Math.floor(value)
																		}
																});
														}}
												/>
										</InputGroup>
								</FormItem>
						</Col>
				</Row>
				<Row>
					<Col span={8}>
						<FormItem label={'水表类型:'}>
							<Select
								placeholder="请选择"
								value={searchForm.type}
								onChange={v => {
									this.setState({
										searchWaterMeterType: v,
										searchForm: { ...this.state.searchForm, type: v },
										kind: null
									});
								}}
							>
								{WATER_METER_TYPE.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'水表种类:'}>
							<Select
								placeholder="请选择"
								value={searchForm.kind}
								onChange={v => {
									this.setState({
										searchForm: { ...this.state.searchForm, kind: v }
									});
								}}
							>
								{WATER_METER_KIND.map((item, index) => {
									return (
										<Option key={index} value={item.value}
														disabled={
															(searchWaterMeterType == 0 && (item.value < 3 || item.value == 9))
															|| (searchWaterMeterType == 1 && !(item.value == 0 || item.value == 9|| item.value ==10))
															|| (searchWaterMeterType == 2 && item.value != 1 && item.value != 2&& item.value != 11)
														}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'水表厂家:'}>
							<Select
								placeholder="请选择"
								value={searchForm.manufacturer}
								onChange={v => {
									this.setState({
										searchForm: { ...this.state.searchForm, manufacturer: v }
									});
								}}
							>
								{WATER_METER_MANUFACTURER.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={8}>
						<FormItem label={'口径:'}>
							<Input
									placeholder="请输入口径"
									value={searchForm.caliber}
									onChange={v => {
										this.setState({ searchForm: { ...this.state.searchForm, caliber: v.target.value } });
									}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>

					<FormItem label={'表锁号:'}>
						<Input placeholder="请输入表锁号"
									 value={searchForm.lockNumber}H
									 onChange={v => {
										 this.setState({ searchForm: { ...this.state.searchForm, lockNumber: v.target.value } });
									 }}
						/>
					</FormItem>
					</Col>
					<Col span={8}>
					<FormItem label={'安装时间:'}>
									<RangePicker value={[ installRangeStart, installRangeEnd ]} onChange={(date, dateString)=>{
											this.setState({
													installRangeStart: date[0],
													installRangeEnd: date[1],
													searchForm: {
															...this.state.searchForm,
															installTimeStart: dateString[0],
															installTimeEnd: dateString[1]
													}
											});}} placeholder={['开始日期', '结束日期']} />
					</FormItem>
			</Col>
				</Row>
				<Row>
					<Col span={8} >
							<FormItem label={'出厂时间:'}>
											<RangePicker value={[ appearanceRangeStart, appearanceRangeEnd ]} onChange={(date, dateString)=>{
													this.setState({
															appearanceRangeStart: date[0],
															appearanceRangeEnd: date[1],
															searchForm: {
																	...this.state.searchForm,
																	appearanceDateStart: dateString[0],
																	appearanceDateEnd: dateString[1]
															}
													});}} placeholder={['开始日期', '结束日期']} />
							</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={16} offset={8} align="right">
						<FormItem>
							<Button type="primary" onClick={this.handleSearch}>
								搜索
							</Button>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<Button type="default" onClick={this.handleReset}>
								重置
							</Button>
						</FormItem>
					</Col>
				</Row>
			</Form>
		);
	};

	// 渲染操作按钮
	_renderOperationButton = () => {
		return (
			<>
				<Col>
					<Button type="primary" onClick={this.handleAdd} permission={React.$pmn('REVENUE:WATER_METER_MANAGEMENT:WATER_METER_INFO:IMPORT')}>
						添加水表
					</Button>
					<Button className="searchBtn" type="default" onClick={() => this.export()}><Icon
						type="download"/>导出水表记录</Button>
					<Button type="default" onClick={this.handleDownload} style={{ marginLeft: 8 }} permission={React.$pmn('REVENUE:WATER_METER_MANAGEMENT:WATER_METER_INFO:DOWM_IMPORT_TEMPLATE')}>
						导入模板下载
					</Button>
						{/*<Button type="default" onClick={()=>{*/}
						{/*		window.open(`${process.env.UPLOAD_ROOT}templates/batch_update_water_meter.xlsx`);*/}
						{/*}} style={{ marginLeft: 8 }} permission={React.$pmn('REVENUE:WATER_METER_MANAGEMENT:WATER_METER_INFO:DOWN_BATCH_UPDATE_WATERMETER')}>*/}
						{/*	批量修改水表模板*/}
						{/*</Button>*/}

						{/*<Upload*/}
						{/*		headers={{*/}
						{/*				token: localStorage.getItem('token')*/}
						{/*		}}*/}
						{/*		accept={`.xls,.xlsx`}*/}
						{/*		action={`${process.env.API_ROOT}/api/wm/watermeter/importWaterMeterChangeTemplate`}*/}
						{/*		onChange={info => {*/}
						{/*				if (info.file.status === 'done') {*/}
						{/*						if (info.file.response.code === 0) {*/}
						{/*								message.success(info.file.response.msg);*/}
						{/*								this.getList();*/}
						{/*						} else {*/}
						{/*								message.error(info.file.response.msg);*/}
						{/*						}*/}
						{/*				} else if (info.file.status === 'error') {*/}
						{/*						message.error(info.file.response.msg);*/}
						{/*				}*/}
						{/*		}}*/}
						{/*>*/}

						{/*		<Button type="default"  permission={React.$pmn('REVENUE:WATER_METER_MANAGEMENT:WATER_METER_INFO:BATCH_UPDATE_WATERMETER')}>*/}
						{/*				水表批量修改上传*/}
						{/*		</Button>*/}

						{/*</Upload>*/}

					<Upload
						headers={{
							token: localStorage.getItem('token')
						}}
						accept={`.xls,.xlsx`}
						action={`${process.env.API_ROOT}/api/wm/watermeter/batchImport  `}
						onChange={info => {
							if (info.file.status === 'done') {
								if (info.file.response.code === 0) {
									message.success(info.file.response.msg);
									this.getList();
								} else {
									message.error(info.file.response.msg);
								}
							} else if (info.file.status === 'error') {
								message.error(info.file.response.msg);
							}
						}}
					>
						<Button type="default" style={{ marginLeft: 8 }} permission={React.$pmn('REVENUE:WATER_METER_MANAGEMENT:WATER_METER_INFO:BATCH_IMPORT')}>
							水表导入
						</Button>

					</Upload>

				</Col>

			</>
		);
	};

	// 渲染新增弹出框
	_renderModal = () => {
		const { modalTitle, modalType, waterMeterType } = this.state;
		const { detail, visible, installLocationTypeSelect, installLocationSelect } = this.props;
		const { getFieldDecorator } = this.props.form;
		return (
			<Modal className="water-meter-modal" title={modalTitle} visible={visible} onCancel={this.handleCancel}
						 footer={null}>
				<Form {...constants.formItemLayout}>
					<Row>
						<Col span={12}>
							<FormItem label="水表厂家：">
								{modalType === 'view' ? detail && detail.manufacturer
									: getFieldDecorator('manufacturer', {
										initialValue: detail ? detail.manufacturer : null,
										rules: [
											{
												required: true,
												message: '请选择水表厂家'
											}
										]
									})(
										<Select>
											{WATER_METER_MANUFACTURER.map((item, index) => {
												return (
													<Option key={index} value={item.value}>
														{item.label}
													</Option>
												);
											})}
										</Select>
									)}
							</FormItem>
						</Col>
						<Col span={12}>
							<FormItem label="水表编号：">
								{modalType === 'view'
									? detail && detail.no
									: getFieldDecorator('no', {
										initialValue: detail ? detail.no : '',
										rules: [
											{
												required: true,
												message: '请输入水表编号'
											}
										]
									})(<Input disabled={modalType === 'edit'} palceholder="请输入水表编号"/>)}
							</FormItem>
						</Col>
					</Row>
					<Row>
						<Col span={12}>
							<FormItem label="水表口径：">
								{modalType === 'view'
									? detail && detail.caliber
									: getFieldDecorator('caliber', {
										initialValue: detail ? detail.caliber : '',
										rules: [{ required: true, message: '请选择水表口径' }]
									})(
										<Select>
											{WATER_METER_CALIBER.map((item, index) => {
												return (
													<Option key={index} value={item.value}>
														{item.label}
													</Option>
												);
											})}
										</Select>
									)}
							</FormItem>
						</Col>
						<Col span={12}>
							<FormItem label="字轮基数：">
								{modalType === 'view'
									? detail && detail.wheelBaseNumber
									: getFieldDecorator('wheelBaseNumber', {
										initialValue: detail ? detail.wheelBaseNumber : '0',
										rules: [
											{
												required: true,
												message: '请输入字轮基数'
											}
										]
									})(<Input disabled={modalType === 'edit'} palceholder="请输入字轮基数"/>)}
							</FormItem>
						</Col>
					</Row>
					<Row>
						<Col span={12}>
							<FormItem label="水表状态：">
								{detail && detail.status}
							</FormItem>
						</Col>
						<Col span={12}>
						</Col>
					</Row>
					<Row>
						<Col span={12}>
							<FormItem label="水表类型：">
								{modalType === 'view'
									? detail && detail.type
									: getFieldDecorator('type', {
										initialValue: detail ? detail.type : null,
										rules: [
											{
												required: true,
												message: '请选择水表类型'
											}
										]
									})(
										<Select
											disabled={React.$pmn('REVENUE:WATER_METER_MANAGEMENT:WATER_METER_INFO:UPDATE_METERTYPE')!=1 && modalType!='add'}
											onChange={v => {
												this.resetKindSelect(v);
											}}
										>
											{WATER_METER_TYPE.map((item, index) => {
												return (
													<Option key={index} value={item.value}>
														{item.label}
													</Option>
												);
											})}
										</Select>
									)}
							</FormItem>
						</Col>
						<Col span={12}>
							<FormItem label="水表种类：">
								{modalType === 'view'
									? detail && detail.kind
									: getFieldDecorator('kind', {
										initialValue: detail ? detail.kind : null,
										rules: [
											{
												required: true,
												message: '请选择水表种类'
											}
										]
									})(
										<Select>
											{WATER_METER_KIND.map((item, index) => {
												return (
													<Option key={index} value={item.value}
																	disabled={(waterMeterType == 0 && item.value < 3) || (waterMeterType == 1 && item.value != 0 && item.value != 9&& item.value != 10) || (waterMeterType == 2 && item.value != 1 && item.value != 2 && item.value != 11)}>
														{item.label}
													</Option>
												);
											})}
										</Select>
									)}
							</FormItem>
						</Col>
					</Row>
					<Row>
						<Col span={12}>
							<FormItem label="出厂日期：">
								{modalType === 'view'
									? detail && detail.appearanceDate
									: getFieldDecorator('appearanceDate', {
										initialValue: detail && detail.appearanceDate ? moment(detail.appearanceDate, 'YYYY-MM-DD') : '',
										rules: [
											{
												required: true,
												message: '请选择出厂日期'
											}
										]
									})(<DatePicker format={'YYYY-MM-DD'} disabled={modalType === 'view'} onChange={this.getAppearanceDate}
																 placeholder={'出厂日期'} style={{ width: '100%' }}/>)}
							</FormItem>
						</Col>
						<Col span={12}>
							<FormItem label="二维码编号：">
								{modalType === 'view'
									? detail && detail.qrCodeNumber
									: getFieldDecorator('qrCodeNumber', {
										initialValue: detail ? detail.qrCodeNumber : '',
										rules: []
									})(<Input disabled={modalType === 'view'} palceholder="请输入二维码编号"/>)}
							</FormItem>
						</Col>
					</Row>
					<Row>
						<Col span={12}>
							<FormItem label="表锁号：">
								{modalType === 'view'
									? detail && detail.lockNumber
									: getFieldDecorator('lockNumber', {
										initialValue: detail && detail.lockNumber ? detail.lockNumber : '',
										rules: []
									})(<Input disabled={modalType === 'view'} palceholder="请输入表锁号"/>)}
							</FormItem>
						</Col>
						<Col span={12}>
							<FormItem label="钢印号：">
								{modalType === 'view'
									? detail && detail.stampNumber
									: getFieldDecorator('stampNumber', {
										initialValue: detail && detail.stampNumber ? detail.stampNumber : '',
										rules: []
									})(<Input disabled={modalType === 'view'} palceholder="请输入钢印号"/>)}
							</FormItem>
						</Col>
					</Row>
					<Row>
						<Col span={12}>
							<FormItem label="安装位置分类：">
								{modalType === 'view'
									? detail && detail.installLocationTypeId
									: getFieldDecorator('installLocationTypeId', {
										initialValue: detail ? detail.installLocationTypeId : null,
										rules: []
									})(
										<Select
											disabled={modalType === 'view'}
											onChange={v => {
												this.handleChangeInstallLocationType(v);
											}}
										>
											{installLocationTypeSelect.map((item, index) => {
												return (
													<Option key={index} value={item.value}>
														{item.label}
													</Option>
												);
											})}
										</Select>
									)}
							</FormItem>
						</Col>
						<Col span={12}>
							<FormItem label="安装位置：">
								{modalType === 'view'
									? detail && detail.installLocationId
									: getFieldDecorator('installLocationId', {
										initialValue: detail ? detail.installLocationId : null,
										rules: []
									})(
										<Select disabled={modalType === 'view'}>
											{installLocationSelect.map((item, index) => {
												return (
													<Option key={index} value={item.value}>
														{item.label}
													</Option>
												);
											})}
										</Select>
									)}
							</FormItem>
						</Col>
					</Row>
					<Row>
						<Col span={12}>
							<FormItem label="安装日期：">
								{modalType === 'view'
									? detail && detail.installTime
									: getFieldDecorator('installTime', {
										initialValue: detail && detail.installTime ? moment(detail.installTime, 'YYYY-MM-DD') : '',
										rules: []
									})(<DatePicker format={'YYYY-MM-DD'} disabled={modalType === 'view'} onChange={this.getInstallTime}
																 placeholder={'选择安装日期'} style={{ width: '100%' }}/>)}
							</FormItem>
						</Col>
						<Col span={12}/>
					</Row>
					<Row>
						<Col span={12}>
							<FormItem label="安装区域：">
								{modalType === 'view'
									? detail && detail.district
									: getFieldDecorator('district', {
										initialValue: detail ? detail.district : '',
										rules: []
									})(<Input disabled={modalType === 'view'} palceholder="请输入安装区域"/>)}
							</FormItem>
						</Col>
						<Col span={12}>
							<FormItem label="详细地址：">
								{modalType === 'view'
									? detail && detail.address
									: getFieldDecorator('address', {
										initialValue: detail ? detail.address : '',
										rules: []
									})(<Input disabled={modalType === 'view'} palceholder="请输入详细地址"/>)}
							</FormItem>
						</Col>
					</Row>
					<Row>
						<Col span={12}>
							<FormItem label="集中器编号：">
								{modalType === 'view'
									? detail && detail.concentratorNo
									: getFieldDecorator('concentratorNo', {
										initialValue: detail ? detail.concentratorNo : '',
										rules: []
									})(<Input disabled={modalType === 'edit'} palceholder="请输入集中器编号"/>)}
							</FormItem>
						</Col>
						<Col span={12}>
							<FormItem label="集中器通道：">
								{modalType === 'view'
									? detail && detail.concentratorAisle
									: getFieldDecorator('concentratorAisle', {
										initialValue: detail ? detail.concentratorAisle : '',
										rules: []
									})(<Input disabled={modalType === 'edit'} palceholder="请输入集中器通道"/>)}
							</FormItem>
						</Col>
					</Row>

					<Row>
						<Col span={12}>
							<FormItem label="地下表井编号：">
								{modalType === 'view'
									? detail && detail.wellNo
									: getFieldDecorator('wellNo', {
										initialValue: detail ? detail.wellNo : '',
										rules: []
									})(<Input disabled={modalType === 'view'} palceholder="请输入第下表井编号"/>)}
							</FormItem>
						</Col>
						<Col span={12}>
							<FormItem label="阀门状态：">
								{modalType === 'view'
									? detail && detail.valveStatus
									: getFieldDecorator('valveStatus', {
										initialValue: detail ? detail.valveStatus : null,
										rules: []
									})(
										<Select disabled={modalType === 'edit'}>
											{VALVE_STATUE.map((item, index) => {
												return (
													<Option key={index} value={item.value}>
														{item.label}
													</Option>
												);
											})}
										</Select>
									)}
							</FormItem>
						</Col>
					</Row>
					<Row>
						<Col span={24}>
							<FormItem label="备注：">
								{getFieldDecorator('remark', {
									initialValue: detail ? detail.remark : '',
									rules: []
								})(<TextArea/>)}
							</FormItem>
						</Col>
					</Row>
					<Row>
						<Col span={24} align="center">
							{modalType === 'view' ? (
								<Button type="default" onClick={this.handleCancel}>
									关闭
								</Button>
							) : (
								<Fragment>
									<Button type="primary" className="btn" onClick={this.handleSubmit}>
										提交
									</Button>
									<Button type="default" className="btn" onClick={this.handleCancel}>
										取消
									</Button>
								</Fragment>
							)}
						</Col>
					</Row>
				</Form>
			</Modal>
		);
	};

	// 渲染详情弹出框
	_renderDetailModal = () => {
		const { detailModalVisible } = this.state;
		const { detail, waterMeterChangeList } = this.props;
		return (
			<Modal className="water-meter-modal" title="水表详情" visible={detailModalVisible} onCancel={this.handleCancelDetail}
						 footer={null}>
				<WaterMeterDetail detail={detail} waterMeterChangeList={waterMeterChangeList}/>
			</Modal>
		);
	};

	render() {
		const { page, pageSize } = this.state;
		const { dataList, total } = this.props;
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: total => {
				return `共 ${total} 条数据 `;
			},
			onChange: page => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		const columns = [
			{
				title: '水表编号',
				dataIndex: 'no',
				align: 'center'
			},
			{
				title: '二维码编号',
				dataIndex: 'qrCodeNumber',
				align: 'center'
			},
			{
				title: '水表厂家',
				dataIndex: 'manufacturer',
				align: 'center'
			},
			{
				title: '水表类型',
				dataIndex: 'type',
				align: 'center'
			},
			{
				title: '水表种类',
				dataIndex: 'kind',
				align: 'center'
			},
			{
				title: '水表口径',
				dataIndex: 'caliber',
				align: 'center'
			},
		  {
				title: '电压',
				dataIndex: 'voltage',
				align: 'center'
		  },
		  {
				title: '水压',
				dataIndex: 'waterGage',
				align: 'center'
	  	},
			{
				title: '字轮基数',
				dataIndex: 'wheelBaseNumber',
				align: 'center'
			},
			{
				title: '安装时间',
				dataIndex: 'installTime',
				align: 'center'
			},
			{
				title: '安装位置',
				dataIndex: 'installLocationName',
				align: 'center'
			},
			{
				title: '阀门状态',
				dataIndex: 'valveStatus',
				align: 'center',
				render: text => {
					return (
						<Tag color={valveStatusColorMap.get(text)}>
							{text}
						</Tag>
					);
				}
			},
			{
				title: '当前读数',
				dataIndex: 'wheelPresentNumber',
				align: 'center'
			},
			{
				title: '创建人',
				dataIndex: 'createUserName',
				align: 'center'
			},
			{
				title: '水表状态',
				dataIndex: 'status',
				align: 'center',
				width: 80,
				fixed: 'right',
				render: text => {
					return (
						<Tag color={waterMeterStatusColorMap.get(text)}>
							{text}
						</Tag>
					);
				}
			},
			{
				title: '操作',
				key: 'operation',
				width: 150,
				fixed: 'right',
				align: 'center',
				render: (text, record, index) => {
					return (
						<div style={{ display: 'flex', justify: 'space-between' }}>
							<Button title="查看" className="btn" type="primary" size="small" icon="eye"
											onClick={() => this.handleView(record)}/>
							<Button title="编辑" className="btn" type="primary" size="small" icon="form"
											onClick={() => this.handleEdit(record)}/>
							<Button title="删除" className="btn" type="primary" size="small" icon="delete"
											onClick={() => this.handleDel(record)}/>
							{
								record.status === '已删除'? (
									<Button title="设置为库存" className="btn" type="primary" size="small" icon="check-circle"
													permission={React.$pmn('REVENUE:WATER_METER_MANAGEMENT:WATER_METER_INFO:SET_STOCK')}
													onClick={() => this.handleUpdateToStock(record)}/>) : null
							}
						</div>
					);
				}
			}
		];
		return (
			<div className="shadow-radius water-meter">
				<h1>水表管理</h1>
				<Row>{this._renderSearchForm()}</Row>
				<Row>{this._renderOperationButton()}</Row>
				<Row className="main">
					<Table bordered scroll={{ x: 2300 }} columns={columns} rowKey={(record) => record.id} dataSource={dataList}
								 pagination={paginationProps}/>
				</Row>
				{this._renderModal()}
				{this._renderDetailModal()}
			</div>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('waterMeter');
	return {
		fields: data.fields,
		dataList: data.dataList, // 列表数据
		total: data.total, // 总条数
		visible: data.visible, // 是否显示弹窗
		detail: data.detail, // 获取详情
		waterMeterChangeList: data.waterMeterChangeList, // 水表变更记录
		installLocationTypeSelect: data.installLocationTypeSelect, // 安装位置分类选择框
		installLocationSelect: data.installLocationSelect // 安装位置选择框
	};
};
const mapDispatchToProps = dispatch => ({
	setState: data => dispatch(actionCreators.setState(data)), // 设置redux值
	list: data => dispatch(actionCreators.list(data)), // 获取列表
	getDetail: data => dispatch(actionCreators.detail(data)), // 获取详情
	getWaterMeterChangeList: data => dispatch(actionCreators.getWaterMeterChangeList(data)), // 获取水表变更记录
	add: (data, list) => dispatch(actionCreators.add(data, list)), // 新增记录
	del: (data, list) => dispatch(actionCreators.del(data, list)), // 删除记录
	modify: (data, list) => dispatch(actionCreators.modify(data, list)), // 修改记录
	getInstallLocationTypeSelect: () => dispatch(actionCreators.getInstallLocationTypeSelect()), // 安装位置分类选择框
	getInstallLocationSelect: data => dispatch(actionCreators.getInstallLocationSelect(data)), // 安装位置选择框
	batchImport: data => dispatch(actionCreators.batchImport()), // 水表导入
	updateToStock: (data, list) => dispatch(actionCreators.updateToStock(data, list)) // 重新加入库存
});
export default connect(
	mapStateToProps,
	mapDispatchToProps
)(Form.create()(Index));
