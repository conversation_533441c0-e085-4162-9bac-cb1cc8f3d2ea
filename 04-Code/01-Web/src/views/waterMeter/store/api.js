export default {
	list: `/api/wm/watermeter/listPage`,
	add: `/api/wm/watermeter/save`,
	detail: `/api/wm/watermeter/get`,
	modify: `/api/wm/watermeter/update`,
	updateKind: `/api/wm/watermeter/updateKind`,
	del: `/api/wm/watermeter/delete`,
	getInstallLocationTypeSelect: `/api/sys/dictionary/getClassSelect/INSTALL_LOCATION`,
	getInstallLocationSelect: `/api/sys/dictionary/getSelectByClass/`,
	getCaliberSelect: `/api/wm/watermeter/getCaliberSelect`,
	getWaterMeterChangeList: `/api/wm/watermeter/change/list/`,
	batchImport: `/api/wm/watermeter/batchImport`,
	updateToStock: `/api/wm/watermeter/updateStatusToInStock`,
	batchCloseValveForWaterMeter: `api/wm/watermeter/batchCloseValveForWaterMeter`, // 关阀
	batchCloseValveByConditionForWaterMeter:`/api/wm/watermeter/batchCloseValveByConditionForWaterMeter`, //按条件查询批量关阀
	updateIcNo: `/api/wm/watermeter/updateIcNo`
};
