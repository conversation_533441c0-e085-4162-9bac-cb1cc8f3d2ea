import http from '$http';
import * as actionTypes from './constants';
import { message } from 'antd';
import api from './api';

// 数据回填
const payload = (type, data) => ({ type, data });

// 组件操作redux数据状态
const setState = data => ({ type: actionTypes.SET_STATE, data });

// 获取列表
const list = data => {
	return async dispatch => {
		const res = await http.post(api.list, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.LIST_RECORD, res.data));
		}
	};
};
// 获取详情
const detail = data => {
	return async dispatch => {
		const res = await http.restGet(api.detail, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.DETAIL_RECORD, res.data));
		}
	};
};
// 新增记录
const add = (data, list) => {
	return async dispatch => {
		const res = await http.post(api.add, data);
		if (res.code === 0) {
			list();
			message.success("水表添加成功！")
			dispatch(payload(actionTypes.ADD_RECORD, res.data));
		}
	};
};
// 修改
const modify = (data, list) => {
	return async dispatch => {
		const res = await http.post(api.modify, data);
		if (res.code === 0) {
			list();
			message.success("修改成功！")
			dispatch(payload(actionTypes.MODIFY_RECORD, data));
		}
	};
};
// 删除
const del = (data, list) => {
	return async dispatch => {
		const res = await http.del(api.del, data);
		if (res.code === 0) {
			list();
			message.success("删除成功！")
			dispatch(payload(actionTypes.DEL_RECORD, data));
		}
	};
};
// 获取安装位置分类选择框
const getInstallLocationTypeSelect = () => {
	return async dispatch => {
		const res = await http.get(api.getInstallLocationTypeSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_INSTALL_LOCATION_TYPE_SELECT, res.data));
		}
	};
};
// 获取安装位置选择框
const getInstallLocationSelect = data => {
	return async dispatch => {
		const res = await http.restGet(api.getInstallLocationSelect, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_INSTALL_LOCATION_SELECT, res.data));
		}
	};
};
// 获取水表变更记录
const getWaterMeterChangeList = data => {
	return async dispatch => {
		const res = await http.restGet(api.getWaterMeterChangeList, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_WATER_METER_CHANGE_LIST, res.data));
		}
	};
};

// 水表导入
const batchImport = data => {
	return async dispatch => {
		const res = await http.post(api.batchImport, data);
		if (res.code === 0) {
			message.success("水表导入成功！")
			dispatch(payload(actionTypes.BATCH_IMPORT, res.data));
		}
	};
};

// 重新加入库存
const updateToStock = (data, list) => {
	return async dispatch => {
		const res = await http.restGet(api.updateToStock, data);
		if (res.code === 0) {
			message.success(res.msg);
			list();
			dispatch(payload(actionTypes.UPDATE_TO_STOCK, data));
		}
	};
};
export { setState, list, detail, add, modify, del, getInstallLocationTypeSelect, getInstallLocationSelect, getWaterMeterChangeList, batchImport, updateToStock };
