import * as actionTypes from "./constants";
import { dataType } from '$utils'

const defaultState = {
	fields: null,
	dataList: [], // 列表数据
	total: 0, // 总条数
	detail: null, // 详情
	visible: false, // 涉及到交互的弹窗显示控制 默认为false
	waterUseKinds: [], // 用水性质列表
	areas: [], // 片区树
	customers: [], // 客户列表
	orderTotal: {}, // 统计
	minusReason: [],//核减原因
	dazeBillReason: [],//呆账原因
	badBillReason: [],//坏账原因
};

// 重要提示： 请做好各代码段的注释！！！
export default (state = defaultState, action) => {
	switch (action.type) {

		// 回填组件传给redux的值
		case actionTypes.SET_STATE:
			let _data = action.data;
			if (dataType(_data) === 'Object') {
				state[_data.key] = _data.value;
			}
			if (dataType(_data) === 'Array') {
				_data.forEach(item => {
					state[item.key] = item.value;
				})
			}
			return { ...state };

		// 回填列表数据
		case actionTypes.LIST_RECORD:
			state.dataList = action.data.rows;
			state.total = action.data.total;
			return { ...state };

		// 回填详情信息
		case actionTypes.DETAIL_RECORD:
			// ... do something
			return { ...state };

		// 新增记录集
		case actionTypes.ADD_RECORD:
			// ... do something
			return { ...state };

		// 删除记录集
		case actionTypes.DEL_RECORD:
			return { ...state };

		// 修改记录集
		case actionTypes.MODIFY_RECORD:
			// ... do something
			return { ...state };

		// 用水性质
		case actionTypes.WATER_USE_KIND_RECORD:
			state.waterUseKinds = action.data;
			return { ...state };

		// 片区树
		case actionTypes.GET_AREAS_RECORD:
			state.areas = action.data;
			return { ...state };

		// 客户列表
		case actionTypes.GET_CUSTOMER_RECORD:
			state.customers = [{ value: null, label: '全部' }, ...action.data];
			return { ...state };

		case actionTypes.CLOSE_MODAL:
			state.visible = false;
			return { ...state };

		// 统计
		case actionTypes.GET_TOTAL:
			state.orderTotal = action.data;
			return { ...state };

		//获取核减原因
		case actionTypes.GET_MINUS_REASON:
			state.minusReason = action.data;
			return { ...state };

		//获呆账减原因
		case actionTypes.GET_DAZEBILL_REASON:
			state.dazeBillReason = action.data;
			return { ...state };

		//获取坏账原因
		case actionTypes.GET_BADBILL_REASON:
			state.badBillReason = action.data;
			return { ...state };

		// 批量打印税票
		case actionTypes.BATCH_PRINT_TAX:
			return { ...state };

		// 批量打印电子发票
		case actionTypes.BATCH_PRINT_TAX:
			return { ...state };

		case actionTypes.GET_BY_CUSTOMER:
			state.customerDetail = action.data;
			return { ...state };

		default:
			return state;
	}
}
