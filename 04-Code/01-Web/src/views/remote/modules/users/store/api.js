export default {
	list: `api/cm/customer/listFarPassCustomers`, // 获取列表
	detail: `api/cm/customer/simple/detail`, // 查看详情
	getAreaSelect: `api/sys/area/getTree`, // 获取片区
	waterUseKindList: `/api/wuk/waterusekind/getSelect`,
	open: `api/wm/watermeter/batchOpenValve`, // 开阀
	close: `api/wm/watermeter/batchCloseValve`, // 关阀
	batchCloseValveByCondition:`/api/wm/watermeter/batchCloseValveByCondition`, //按条件查询批量关阀
	outAccount: `api/bill/farPassHandworkOutBill`, // 手动出账
	batchOutBill: `api/bill/farPassHandworkBatchOutBill`, // 批量出账
	batchOutBillByCondition: `api/bill/farPassHandworkBatchOutBillByCondition`, // 批量出账
	installPosition: `/api/sys/dictionary/getClassSelect/INSTALL_LOCATION`,
	reallyPosition: `/api/sys/dictionary/getSelectByClass/`,
	getFarPassCustomersStatistic: `api/cm/customer/getFarPassCustomersStatistic`, // 获取远传表统计
	transcribeByWaterMeterNo: `api/wm/concentrator/transcribeByWaterMeterNo`,  //单表抄表
	farPassHandworkOutBillByFill: `api/bill/farPassHandworkOutBillByFill`,  //单表抄表
	transcribe: `api/cm/customer/transcribe`,
	batchTranscribe: `api/cm/customer/batchTranscribe`,                       //根据勾选派发抄表任务
	batchTranscribeByCondition: `api/cm/customer/batchTranscribeByCondition`, //根据条件派发抄表任务
	customerLevelList:`/api/sys/dictionary/getSelectByCode/CUSTOMER_LEVEL`, // 用户等级
	updateLockStatus: `api/cm/customer/updateLockStatus`, // 获取列表
	listByRoleName : `api/sys/user/listByRoleName`,
	updateWaterDayConfig: `api/cm/customer/updateWaterDayConfig`, // 更新用户日用水上下限
};
