import http from '$http';
import { message } from 'antd';

import api from './api';
import * as actionTypes from './constants';

// 数据回填
const payload = (type, data) => ({ type, data })

// 组件操作redux数据状态
const setState = (data) => ({ type: actionTypes.SET_STATE, data })

// 获取列表
const list = data => {
	return async dispatch => {
		const res = await http.post(api.list, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.LIST_RECORD, res.data))
		}
		// const statisticRsp = await http.post(api.getFarPassCustomersStatistic, data);
		// if (statisticRsp.code === 0) {
		// 	dispatch(payload(actionTypes.STATISTIC, statisticRsp.data))
		// }
	}
};

// 获取详情
const detail = data => {
	return async dispatch => {
		const res = await http.restGet(api.detail, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.DETAIL_RECORD, res.data))
		}
	}
};



// 获取片区
const getAreaSelect = (data) => {
	return async dispatch => {
		const res = await http.get(api.getAreaSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_AREA_SELECT, res.data))
		}
	}
};

// 用水分类列表
const waterUseKindList = (type) => {
	return async dispatch => {
		const res = await http.restGet(api.waterUseKindList, type);
		if (res.code === 0) {
			dispatch(payload(actionTypes.WATER_USE_KIND_RECORD, res.data));
		}
	};
};

// 开阀
const valueOpen = (data, setBtn) => {
	return async dispatch => {
		const res = await http.post(api.open, data);
		if (res.code === 0) {
			message.success(res.msg);
			dispatch(payload(actionTypes.VALUE_OPEN, res.data));
		}
		setBtn();
	}
};

// 关阀
const valueClose = (data, setBtn) => {
	return async dispatch => {
		const res = await http.post(api.close, data);
		if (res.code === 0) {
			message.success(res.msg);
			dispatch(payload(actionTypes.VALUE_CLOSE, res.data));
		}
		setBtn();
	}
};

// 关阀
const batchCloseValveByCondition = (data, setBtn) => {
	return async dispatch => {
		const res = await http.post(api.batchCloseValveByCondition, data);
		if (res.code === 0) {
			message.success(res.msg);
			dispatch(payload(actionTypes.BATCH_CLOSE_VALVE_BY_CONDITION, res.data));
		}
		setBtn();
	}
};

// 手动出账
const outAccount = (data, setBtn) => {
	return async dispatch => {
		const res = await http.get(api.outAccount + `/${data}`);
		if (res.code === 0) {
			message.success(res.msg);
			dispatch(payload(actionTypes.OUT_ACCOUNT, res.data));
		}
		setBtn();
	}
};

// 手动生成账单
const farPassHandworkOutBillByFill = (data, setBtn) => {
	return async dispatch => {
		const res = await http.post(api.farPassHandworkOutBillByFill, data);
		if (res.code === 0) {
			message.success(res.msg);
			dispatch(payload(actionTypes.OUT_ACCOUNT, res.data));
		}
		setBtn();
	}
};

// 水表安装位置
const installPosition = () => {
	return async dispatch => {
		const res = await http.get(api.installPosition);
		if (res.code === 0) {
			dispatch(payload(actionTypes.INSTALL_POSITION_RECORD, res.data));
		}
	};
};

// 水表实际安装位置
const reallyPosition = (type) => {
	return async dispatch => {
		const res = await http.restGet(api.reallyPosition, type);
		if (res.code === 0) {
			dispatch(payload(actionTypes.REALLY_POSITION_RECORD, res.data));
		}
	};
};

// 批量出账
const batchOutBill = (data) => {
	return async dispatch => {
		const res = await http.post(api.batchOutBill, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.BATCH_OUT_BILL, res.data));
		}
	}
};

// 批量出账
const batchOutBillByCondition = (data) => {
	return async dispatch => {
		const res = await http.post(api.batchOutBillByCondition, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.BATCH_OUT_BILL_BY_CONDITION, res.data));
		}
	}
};
const updateLockStatus = (data, list) => {
	return async dispatch => {
		const res = await http.post(api.updateLockStatus, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.UPDATE_LOCK_STATUS, res.data));
			list();
		}
	}
}

export {
	batchCloseValveByCondition,
	batchOutBill,
	batchOutBillByCondition,
	detail,
	farPassHandworkOutBillByFill,
	getAreaSelect,
	installPosition,
	list,
	outAccount,
	reallyPosition,
	setState,
	updateLockStatus,
	valueClose,
	valueOpen,
	waterUseKindList,
};
