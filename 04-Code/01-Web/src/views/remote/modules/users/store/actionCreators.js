import http from '$http'
import * as actionTypes from './constants'
import { message } from 'antd'
import api from './api'

// 数据回填
const payload = (type, data) => ({ type, data })

// 组件操作redux数据状态
const setState = (data) => ({ type: actionTypes.SET_STATE, data })

// 获取列表
const list = data => {
	return async dispatch => {
		const res = await http.post(api.list, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.LIST_RECORD, res.data))
		}
		const statisticRsp = await http.post(api.getFarPassCustomersStatistic, data);
		if (statisticRsp.code === 0) {
			dispatch(payload(actionTypes.STATISTIC, statisticRsp.data))
		}
	}
};

// 获取详情
const detail = data => {
	return async dispatch => {
		const res = await http.restGet(api.detail, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.DETAIL_RECORD, res.data))
		}
	}
};


// 获取详情
const transcribeByWaterMeterNo = data => {
		return http.restPost(api.transcribeByWaterMeterNo, data);
};



// 获取片区
const getAreaSelect = (data) => {
	return async dispatch => {
		const res = await http.get(api.getAreaSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_AREA_SELECT, res.data))
		}
	}
};

// 用水性质列表
const waterUseKindList = (type) => {
	return async dispatch => {
		const res = await http.restGet(api.waterUseKindList,type);
		if (res.code === 0) {
			dispatch(payload(actionTypes.WATER_USE_KIND_RECORD, res.data));
		}
	};
};

// 开阀
const valueOpen = (data, setBtn) => {
	return async dispatch => {
		const res = await http.post(api.open, data);
		if (res.code === 0) {
			message.success(res.msg);
			dispatch(payload(actionTypes.VALUE_OPEN, res.data));
		}
		setBtn();
	}
};

// 关阀
const valueClose = (data, setBtn) => {
	return async dispatch => {
		const res = await http.post(api.close, data);
		if (res.code === 0) {
			message.success(res.data);
			dispatch(payload(actionTypes.VALUE_CLOSE, res.data));
		}
		setBtn();
	}
};

// 关阀
const batchCloseValveByCondition = (data, setBtn) => {
	return async dispatch => {
		const res = await http.post(api.batchCloseValveByCondition, data);
		if (res.code === 0) {
			message.success(res.data);
			dispatch(payload(actionTypes.BATCH_CLOSE_VALVE_BY_CONDITION, res.data));
		}
		setBtn();
	}
};

// 手动出账
const outAccount = (data, setBtn) => {
	return async dispatch => {
		const res = await http.get(api.outAccount + `/${data}`);
		if (res.code === 0) {
			message.success(res.msg);
			dispatch(payload(actionTypes.OUT_ACCOUNT, res.data));
		}
		setBtn();
	}
};


// 手动生成账单
const farPassHandworkOutBillByFill = (data, setBtn) => {
	return async dispatch => {
		const res = await http.post(api.farPassHandworkOutBillByFill, data);
		if (res.code === 0) {
			message.success(res.msg);
			dispatch(payload(actionTypes.OUT_ACCOUNT, res.data));
		}
		setBtn();
	}
};


// 水表安装位置
const installPosition = () => {
	return async dispatch => {
		const res = await http.get(api.installPosition);
		if (res.code === 0) {
			dispatch(payload(actionTypes.INSTALL_POSITION_RECORD, res.data));
		}
	};
};

// 水表实际安装位置
const reallyPosition = (type) => {
	return async dispatch => {
		const res = await http.restGet(api.reallyPosition,type);
		if (res.code === 0) {
			dispatch(payload(actionTypes.REALLY_POSITION_RECORD, res.data));
		}
	};
};

//根据勾选派发抄表任务
const batchTranscribe = (data) => {
	return async dispatch => {
		const res = await http.post(api.batchTranscribe, data);
		if (res.code === 0) {
			message.success('批量派发抄表任务成功！');
			dispatch(payload(actionTypes.BATCH_TRANSCRIBE, res.data));
		}
	};
};
//根据条件派发抄表任务
const batchTranscribeByCondition = (data, refurbish) => {
	return async dispatch => {
		const res = await http.post(api.batchTranscribeByCondition, data);
		if (res.code === 0) {
			message.success('批量派发抄表任务成功！');
			refurbish();
		}
	};
};

//获取用户等级
const customerLevelList = () => {
	return async dispatch => {
		const res = await http.get(api.customerLevelList);
		if (res.code === 0) {
			dispatch(payload(actionTypes.CUSTOMER_LEVEL_RECORD, res.data));
		}
	};
};
const transcribe = data => {
		return async dispatch => {
				const res = await http.restPost(api.transcribe, data);
				if (res.code === 0) {
						message.success('派发任务成功！');
				}
		};
};

// 批量出账
const batchOutBill = (data) => {
	return async dispatch => {
		const res = await http.post(api.batchOutBill, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.BATCH_OUT_BILL, res.data));
		}
	}
};

// 批量出账
const batchOutBillByCondition = (data) => {
	return async dispatch => {
		const res = await http.post(api.batchOutBillByCondition, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.BATCH_OUT_BILL_BY_CONDITION, res.data));
		}
	}
};

// 更新用户锁定状态
const updateLockStatus = (data, list) => {
	return async dispatch => {
		const res = await http.post(api.updateLockStatus, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.UPDATE_LOCK_STATUS, res.data));
			list();
		}
	}
}
// 获取抄表员
const getTranscribers = () => {
	return async dispatch => {
		const res = await http.get(api.listByRoleName + '/远传表抄表员');
		if (res.code === 0) {
			dispatch(payload(actionTypes.TRANSCRIBERS, res.data));
		}
	}
}

// 更新用户日用水上下限
const updateWaterDayConfig = (data, list) => {
	return async dispatch => {
		const res = await http.post(api.updateWaterDayConfig, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.UPDATE_WATER_DAY_CONFIG, res.data));
			list();
		}
	}
}
export {
	setState,
	list,
	detail,
	getAreaSelect,
	waterUseKindList,
	valueOpen,
	valueClose,
	batchCloseValveByCondition,
	outAccount,
	batchOutBill,
	batchOutBillByCondition,
	installPosition,
	reallyPosition,
	transcribeByWaterMeterNo,
	farPassHandworkOutBillByFill,
	transcribe,
	batchTranscribe,
	batchTranscribeByCondition,
	customerLevelList,
	updateLockStatus,
	getTranscribers,
	updateWaterDayConfig,
}
