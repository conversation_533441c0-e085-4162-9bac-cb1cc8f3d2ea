import React, { Component } from 'react';
import { actionCreators } from './store';
import { connect } from 'react-redux';
import {Table, Form, Select, Row, Col, Input, Button, DatePicker, Tag, TreeSelect, Tabs, InputNumber} from 'antd';
import { Chart, Geom, Axis, Tooltip, Legend } from 'bizcharts';
import { DataSet } from '@antv/data-set';
import { constants } from '$utils';
import { WATER_METER_MANUFACTURER } from '@/constants/waterMeter';
import './index.scss';
import { valveStatusColorMap } from '@/constants/colorStyle';
import moment from 'moment';

const FormItem = Form.Item;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TreeNode } = TreeSelect;
const { TabPane } = Tabs;
const InputGroup = Input.Group;


const defaultSearchForm = {
	cno: null,
	customerName: null,
	address: null,
	areaId: null,
	waterMeterNo: null,
	manufacturer: null,
	createStartDate: null,
	createEndDate: null
};

class Index extends Component {

	constructor(props) {
		super(props);
		this.state = {
			page: 1,
			pageSize: 10,
			searchForm: defaultSearchForm,
			tabKey: 0
		};
	}

	callback = tabKey => {
		this.setState({ tabKey });
	};

	componentDidMount() {
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields && isTags) {
			this.setState({
				...fields.state
			}, () => {
				this.props.form.setFieldsValue({
					...fields.form
				});
			});
		} else {
			this.props.getAreaSelect();
		}
	}

	componentWillUnmount() {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			});
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			});
		}
	}

	// 获取列表
	getList = () => {
		const { page, pageSize, searchForm } = this.state;
		this.props.list(Object.assign({ ...searchForm }, { page, pageSize }));
	};

	//获取统计
	getStatistics = () => {
		const {searchForm } = this.state;
		this.props.getListStatistics(Object.assign({ ...searchForm }));
	};

	// 列表分页
	handlePageChange = (page) => {
		this.setState(
			{
				page
			},
			() => {
				this.getList();
			}
		);
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState(
			{
				page: 1,
				pageSize: size
			},
			() => {
				this.getList();
			}
		);
	};

	// 搜索功能
	handleSearch = () => {
		const { tabKey } = this.state;
		if (tabKey === 0) {
			this.setState({ page: 1 }, () => {this.getList();});
		} else {
			this.setState({ page: 1 }, () => {this.getStatistics()});
		}
	};

	// 重置搜索
	handleReset = () => {
		const {tabKey}=this.state
		this.props.form.setFieldsValue({
			rangPicker: [undefined, undefined]
		});
		this.setState(
			{
				searchForm: Object.assign({}, defaultSearchForm)
			},
			() => {
				if (tabKey === 0) {
					this.getList();
				} else {
					this.getStatistics()
				}
			}
		);
	};

	// 日期选择
	getDate = (date, dateString) => {
		if (dateString.length === 2) {
			this.setState({
				searchForm: { ...this.state.searchForm, createStartDate: dateString[0], createEndDate: dateString[1] }
			});
		}
	};

	//递归渲染
	_renderTreeNode = treeData => {
		return treeData.map(tree => {
			if (tree.children) {
				return (
					<TreeNode key={tree.id} title={tree.name} value={tree.id}>
						{this._renderTreeNode(tree.children)}
					</TreeNode>
				);
			} else {
				return <TreeNode key={tree.id} title={tree.name} value={tree.id}/>;
			}
		});
	};

	// 渲染搜索
	_renderSearchForm = () => {
		const { areaSelect } = this.props;
		const { getFieldDecorator } = this.props.form;
		const { searchForm } = this.state;
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					<Col span={8}>
						<FormItem label={'用户编号:'}>
							<Input
								placeholder="请输入用户编号"
								value={searchForm.cno}
								onChange={v => {
									this.setState({
										searchForm: {
											...searchForm,
											cno: v.target.value
										}
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'用户名称:'}>
							<Input
								placeholder="请输入用户名称"
								value={searchForm.customerName}
								onChange={v => {
									this.setState({
										searchForm: {
											...searchForm,
											customerName: v.target.value
										}
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'用户地址:'}>
							<Input
								placeholder="请输入用户地址"
								value={searchForm.address}
								onChange={v => {
									this.setState({
										searchForm: {
											...searchForm,
											address: v.target.value
										}
									});
								}}
							/>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={8}>
						<FormItem label={'水表厂家:'}>
							<Select
								placeholder="请选择"
								value={searchForm.manufacturer}
								onChange={(v) => {
									this.setState({
										searchForm: { ...searchForm, manufacturer: v }
									});
								}}
							>
								{WATER_METER_MANUFACTURER.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'水表编号:'}>
							<Input
								placeholder="请输入水表编号"
								value={searchForm.waterMeterNo}
								onChange={(v) => {
									this.setState({
										searchForm: { ...searchForm, waterMeterNo: v.target.value }
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'通讯时间:'}>
							{getFieldDecorator('rangPicker', { rules: [{ type: 'array' }] })(
								<RangePicker onChange={this.getDate} placeholder={['开始时间', '结束时间']}/>
							)}
						</FormItem>
					</Col>

						<Col span={8}>
								<FormItem label={'电压:'}>
										<InputGroup>
												<InputNumber
														step={1}
														className="noBorderRight"
														style={{ width: '40%' }}
														value={this.state.searchForm.voltageStart}
														onChange={(value) => {
																this.setState({
																		searchForm: {
																				...searchForm,
																				voltageStart: value
																		}
																});
														}}
												/>
												<InputNumber className="midIpt noBorderLeft noBorderRight" style={{ width: '20%' }} placeholder="~"
																		 disabled />
												<InputNumber
														step={1}
														className="noBorderLeft"
														style={{ width: '40%' }}
														value={this.state.searchForm.voltageEnd}
														onChange={(value) => {
																this.setState({
																		searchForm: {
																				...searchForm,
																				voltageEnd: value
																		}
																});
														}}
												/>
										</InputGroup>
								</FormItem>
						</Col>
				</Row>
				<Row>
					<Col span={8}>
						<FormItem label={'片区:'}>
							<TreeSelect
								style={{ width: '100%' }} dropdownStyle={{ maxHeight: 400, overflow: 'auto' }} placeholder="请选择片区"
								allowClear
								treeDefaultExpandedKeys={[100]}
								value={searchForm.areaId}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, areaId: v } });
								}}>
								{this._renderTreeNode(areaSelect)}
							</TreeSelect>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={16} offset={8} align="right">
						<FormItem>
							<Button type="primary" onClick={this.handleSearch}>
								搜索
							</Button>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<Button type="default" onClick={this.handleReset}>
								重置
							</Button>
						</FormItem>
					</Col>
				</Row>
			</Form>
		);
	};

	render() {
		const { page, pageSize } = this.state;
		const { dataList, total,statistics } = this.props;
		let newList = []
		statistics.sort(function(a, b) {
			return b.createTime < a.createTime ? 1 : -1
		})
		statistics.map((item) => {
			newList.push({month: moment(item.createTime).format('YYYY年MM月DD日'), value: item.wheelNumber})
		})
		const columns = [
			{
				title: '水表厂家',
				dataIndex: 'manufacturer',
				align: 'center'
			},
			{
				title: '水表编号',
				dataIndex: 'waterMeterNo',
				align: 'center'
			},
			{
				title: '字轮读数',
				dataIndex: 'wheelNumber',
				align: 'center'
			},
	  	{
				title: '电压',
				dataIndex: 'voltage',
				align: 'center'
	  	},
		  {
						title: '水压',
						dataIndex: 'waterGage',
						align: 'center'
		  },
			{
				title: '通讯状态',
				dataIndex: 'connectStatus',
				align: 'center'
			},
			{
				title: '通讯时间',
				dataIndex: 'createTime',
				align: 'center'
			},
			{
				title: '阀门状态',
				dataIndex: 'valveStatus',
				align: 'center',
				render: text => {
					return <Tag color={valveStatusColorMap.get(text)}>{text}</Tag>;
				}
			}
		];
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: (total) => {
				return `共 ${total} 条数据 `;
			},
			onChange: (page) => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		const cols = {
			value: {
				alias:'水量',
				min: 0
			},
			month: {
				range: [0, 1]
			},
			tickCount:20
		};
		return (
			<div className="shadow-radius remote-datas">
				<h1>远传表数据</h1>
				{this._renderSearchForm()}
				<Row className="main">

					<Tabs defaultActiveKey="0" onChange={this.callback}>
						<TabPane tab="远传表数据列表" key="0">
							<Table bordered columns={columns} rowKey={() => Math.random()} dataSource={dataList}
										 pagination={paginationProps}/>
						</TabPane>
						<TabPane tab="远传表数据统计" key="1">
							<Chart height={500} data={newList} scale={cols} forceFit padding="auto">
								<Axis name="year" visible={true}/>
								<Axis name="value"/>
								<Tooltip crosshairs={{ type: "line" }}/>
								<Geom type="area" position="month*value" shape="smooth" />
								<Geom type="line" position="month*value" shape="smooth" size={2} />
							</Chart>

						</TabPane>
					</Tabs>
				</Row>
			</div>
		);
	}
}

const mapStateToProps = (state) => {
	const data = state.get('remoteDatas');
	return {
		fields: data.fields,
		dataList: data.dataList, // 列表数据
		total: data.total, // 总条数
		areaSelect: data.areaSelect, // 片区选择框
		statistics: data.statistics  //统计
	};
};
const mapDispatchToProps = (dispatch) => ({
	setState: (data) => dispatch(actionCreators.setState(data)), // 设置redux值
	list: (data) => dispatch(actionCreators.list(data)), // 获取列表
	getAreaSelect: () => dispatch(actionCreators.getAreaSelect()), //获取片区选择框
	getListStatistics: (data) => dispatch(actionCreators.getListStatistics(data))
});
export default connect(mapStateToProps, mapDispatchToProps)(Form.create()(Index));
