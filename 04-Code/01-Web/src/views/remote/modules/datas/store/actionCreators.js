import http from '$http';
import * as actionTypes from './constants';
import {message} from 'antd';
import api from './api';

// 数据回填
const payload = (type, data) => ({ type, data });

// 组件操作redux数据状态
const setState = data => ({ type: actionTypes.SET_STATE, data });

// 获取列表
const list = data => {
	return async dispatch => {
		const res = await http.post(api.list, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.LIST_RECORD, res.data));
		}
	};
};

// 获取片区选择框
const getAreaSelect = () => {
	return async dispatch => {
		const res = await http.get(api.getAreaSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_AREA_SELECT, res.data));
		}
	};
};

const getListStatistics = data => {
	return async dispatch => {
		const res = await http.post(api.getListStatistics, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_LIST_STATISTICS, res.data));
		}
	};
};
const add = (data, action = () => {
}) => {
	return async dispatch => {
		const res = await http.post(api.add, data);
		if (res.code === 0) {
			message.success("添加成功！")
			action()
		}
	};
};

// 作废
const del = (data, list) => {
	return async dispatch => {
		const res = await http.restGet(api.del, data);
		if (res.code === 0) {
			message.success('删除成功！');
			list();
		}
	}
};
export {add, setState, list, getAreaSelect, getListStatistics, del};
