import React, { Component, Fragment } from 'react';
import { actionCreators } from './store';
import { connect } from 'react-redux';
import http from '$http';
import { Modal, Table, Form, Select, Row, Col, Input, Button, DatePicker, TreeSelect, message, Tag, Upload } from 'antd';
import { constants } from '$utils';
import { TRANSCRIBE_STATUS, TRANSCRIBE_RESULT } from './constants';
import TranscribeTaskDetail from './components/TranscribeTaskDetail';
import { transcribeStatusColorMap } from '@/constants/colorStyle';
import './index.scss';
import moment from 'moment';
import UploadModal from './components/uploadModal';


const FormItem = Form.Item;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TreeNode } = TreeSelect;

const defaultSearchForm = {
	cno: '',
	transcribeStatus: null,
	transcriberId: null,
	customerName: '',
	createStartTime: '',
	createEndTime: '',
	transcribeStartTime: '',
	transcribeEndTime: '',
	address: '',
	areaId: null,
	result: null,
	customerLevelId: null,
	waterMeterKind : 2,
	abnormal: null, // 是否异常
	abnormalReasonId: null, // 异常原因
};

class Index extends Component {

	constructor(props) {
		super(props);
		this.state = {
			expand: false, // 搜索项过多的时候，隐藏部分
			page: 1,
			pageSize: 10,
			searchForm: defaultSearchForm,
			modalVisible: false,
			transcribeVisible: false,
			loading: false,
			selectedRowKeys: [],//列表选中
			selectedRows: []
		};
	}

	componentDidMount() {
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields && isTags) {
			this.setState({
				...fields.state
			}, () => {
				this.props.form.setFieldsValue({
					...fields.form
				});
					this.getList()
			});
		} else {
			this.props.getTranscriberSelect();
			this.props.getAreaSelect();
			this.props.customerLevelList();
			this.props.listAbnormalReason();
		}
	}

	componentWillUnmount() {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			});
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			});
		}
	}

	// 获取列表
	getList = () => {
		const { page, pageSize, searchForm } = this.state;
		this.props.list(Object.assign({ ...searchForm }, { page, pageSize }));
	};

	// 列表分页
	handlePageChange = (page) => {
		this.setState(
			{
				page
			},
			() => {
				this.getList();
			}
		);
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState(
			{
				page: 1,
				pageSize: size
			},
			() => {
				this.getList();
			}
		);
	};

	// 搜索功能
	handleSearch = () => {
		this.setState(
			{ page: 1 },
			() => {
				this.getList();
				this.setState({ selectedRowKeys: [] });
			}
		);
	};

	// 重置搜索
	handleReset = () => {
		this.props.form.setFieldsValue({
			sendTimePicker: [undefined, undefined],
			transcribeTimePicker: [undefined, undefined]
		});
		this.setState(
			{
				page: 1,
				searchForm: Object.assign({}, defaultSearchForm)
			},
			() => {
				this.getList();
				this.setState({ selectedRowKeys: [] });
			}
		);
	};

	// 查看详情
	handleView = (record) => {
		this.props.getDetail(record.id, () => this.setState({ modalVisible: true }));
	};

	// 取消弹窗
	handleCancel = () => {
		this.props.form.resetFields();
		this.setState({ modalVisible: false, transcribeVisible: false });

	};

	handleClose = () => {
		this.props.setState([{ key: 'result', value: null }]);
	};

	// 作废
	handleInvalid = (record) => {
		Modal.confirm({
			title: '删除确认',
			content: '是否确认作废该抄表任务？',
			okText: '确认',
			cancelText: '取消',
			onOk: () => {
				this.props.invalidTask(record.id, this.getList);
			}
		});
	};

	// 派发时间
	getDate = (date, dateString) => {
		if (dateString.length === 2) {
			this.setState({
				searchForm: { ...this.state.searchForm, createStartTime: dateString[0], createEndTime: dateString[1] }
			});
		}
	};

	// 下载抄表任务
	handleDownload = () => {
		const { searchForm } = this.state;
		const url = `${process.env.API_ROOT}/api/transcribe/task/exportTranscribeTask`;
		http.export(url, searchForm, (res) => {
			const blob = new Blob([res]);
			const downloadElement = document.createElement('a');
			const href = window.URL.createObjectURL(blob);
			const fileName = '抄表任务_' + new Date().getTime() + '.xlsx';
			downloadElement.href = href;
			downloadElement.download = fileName;
			document.body.appendChild(downloadElement);
			downloadElement.click();
			document.body.removeChild(downloadElement);
			window.URL.revokeObjectURL(href);
		});
	};

  // 导出抄表任务
	exportTranscribeTask = () => {
		const { searchForm } = this.state;
		const url = `${process.env.API_ROOT}/api/transcribe/task/exportFarPassTranscribeTaskByCondition`;
		http.export(url, searchForm, (res) => {
			const blob = new Blob([res]);
			const downloadElement = document.createElement('a');
			const href = window.URL.createObjectURL(blob);
			const fileName = '抄表任务_' + new Date().getTime() + '.xlsx';
			downloadElement.href = href;
			downloadElement.download = fileName;
			document.body.appendChild(downloadElement);
			downloadElement.click();
			document.body.removeChild(downloadElement);
			window.URL.revokeObjectURL(href);
		});
	}

	// 批量作废抄表任务
	batchInvalidTask = () => {
		const { selectedRowKeys } = this.state;
		if (selectedRowKeys && selectedRowKeys.length > 0) {
			this.props.batchInvalidTask(selectedRowKeys, this.getList);
			this.setState({ selectedRowKeys: [] });
		} else {
			this.getList();
			setTimeout(() => this.openModal(), 1000);
		}
	}

	openModal() {
		if (this.props.total) {
			Modal.confirm({
				title: '提示',
				content: `未勾选指定任务时,根据查询条件来批量作废.一共${this.props.total}条任务，确认要作废吗？`,
				okText: '确认',
				cancelText: '取消',
				onOk: () => {
					const { searchForm } = this.state;
					this.props.batchInvalidTaskByCondition(searchForm, this.getList);
					this.setState({ selectedRowKeys: [] });
				}
			});
		} else {
			message.error('未查询到抄表任务');
		}
	}

	// 抄表时间
	getTranscribeDate = (date, dateString) => {
		if (dateString.length === 2) {
			this.setState({
				searchForm: { ...this.state.searchForm, transcribeStartTime: dateString[0], transcribeEndTime: dateString[1] }
			});
		}
	};

	// 渲染操作按钮
	_renderOperationButton = () => {
		return (
			<Row style={{display:'flex'}}>
					<Button type="primary" onClick={this.handleDownload}>
						下载抄表任务
					</Button>
					<Upload
						headers={{
							token: localStorage.getItem('token')
						}}
						accept={`.xls,.xlsx`}
						action={`${process.env.API_ROOT}/api/transcribe/task/importTranscribeTask`}
						onChange={info => {
							if (info.file.status === 'done') {
								if (info.file.response.code === 0) {
									message.success(info.file.response.msg);
									this.getList();
								} else {
									message.error(info.file.response.msg);
								}
							} else if (info.file.status === 'error') {
								message.error(info.file.response.msg);
							}
						}}
					>
						<Button type="primary" style={{ marginLeft: 8 }}>
							抄表任务导入
						</Button>
					</Upload>

					<UploadModal list={() => this.getList()} />
				  <Button type="primary" style={{ marginLeft: 8 }} onClick={this.exportTranscribeTask}>
					导出抄表任务
				  </Button>
					<Button type="primary" style={{ marginLeft: 8 }} onClick={this.batchInvalidTask}>
						批量作废抄表任务
					</Button>
			</Row>
		);
	};

	//递归渲染
	_renderTreeNode = treeData => {
		return treeData.map(tree => {
			if (tree.children) {
				return (
					<TreeNode key={tree.id} title={tree.name} value={tree.id}>
						{this._renderTreeNode(tree.children)}
					</TreeNode>
				);
			} else {
				return <TreeNode key={tree.id} title={tree.name} value={tree.id}/>;
			}
		});
	};

	// 渲染搜索
	_renderSearchForm = () => {
		const { transcriberSelect, areaSelect, customerLevels , abnormalReasons} = this.props;
		const { getFieldDecorator } = this.props.form;
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					<Col span={8}>
						<FormItem label={'用户编号:'}>
							<Input
								placeholder="请输入用户编号"
								value={this.state.searchForm.cno}
								onChange={(v) => {
									this.setState({
										searchForm: { ...this.state.searchForm, cno: v.target.value }
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'抄表状态:'}>
							<Select
								placeholder="请选择"
								value={this.state.searchForm.transcribeStatus}
								onChange={(v) => {
									this.setState({
										searchForm: { ...this.state.searchForm, transcribeStatus: v }
									});
								}}
							>
								{TRANSCRIBE_STATUS.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'抄表员:'}>
							<TreeSelect showSearch
													placeholder="请选择"
													allowClear
													dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
													value={this.state.searchForm.transcriberId}
													filterTreeNode={(inputValue, treeNode) => treeNode.props.title.indexOf(inputValue) > -1}
													onChange={(v) => {
														this.setState({
															searchForm: { ...this.state.searchForm, transcriberId: v }
														});
													}}>
								{
									transcriberSelect.map((item, index) => {
										return <TreeNode key={index} title={item.label} value={item.value} />;
									})
								}
							</TreeSelect>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={8}>
						<FormItem label={'用户名称:'}>
							<Input
								placeholder="请输入用户名称"
								value={this.state.searchForm.customerName}
								onChange={(v) => {
									this.setState({
										searchForm: { ...this.state.searchForm, customerName: v.target.value }
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'片区:'}>
							<TreeSelect
								style={{ width: '100%' }}
								dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
								showSearch
								filterTreeNode={(inputValue, treeNode) => treeNode.props.title.indexOf(inputValue) > -1}
								value={this.state.searchForm.areaId}
								placeholder="请选择片区"
								allowClear
								treeDefaultExpandedKeys={[100]}
								onChange={(areaId) => {
									this.setState({
										searchForm: {
											...this.state.searchForm,
											areaId: areaId
										}
									});
								}}
							>
								{
									areaSelect && this._renderTreeNode(areaSelect)
								}
							</TreeSelect>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'派发时间:'}>
							{getFieldDecorator('sendTimePicker', { rules: [{ type: 'array' }] })(
								<RangePicker onChange={this.getDate} placeholder={['开始时间', '结束时间']}/>
							)}

						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={8}>
						<FormItem label={'抄表时间:'}>
							{getFieldDecorator('transcribeTimePicker', { rules: [{ type: 'array' }] })(
								<RangePicker onChange={this.getTranscribeDate} placeholder={['开始时间', '结束时间']}/>
							)}
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'用户地址:'}>
							<Input
								value={this.state.searchForm.address}
								placeholder="请输入用户地址"
								onChange={(v) => {
									this.setState({
										searchForm: { ...this.state.searchForm, address: v.target.value }
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'抄表结果:'}>
							<Select
								placeholder="请选择"
								value={this.state.searchForm.result}
								onChange={(v) => {
									this.setState({
										searchForm: { ...this.state.searchForm, result: v }
									});
								}}
							>
								{TRANSCRIBE_RESULT.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={8}>
						<FormItem label={'用户等级:'}>
							<Select
								showSearch
								placeholder="请选择"
								value={this.state.searchForm.customerLevelId}
								onChange={v => {
									this.setState({
										searchForm: {
											...this.state.searchForm,
											customerLevelId: v
										}
									});
								}}
							>
								<Option value={null}>全部</Option>
								{customerLevels.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'是否异常:'}>
							<Select
								showSearch
								placeholder="请选择"
								value={this.state.searchForm.abnormal}
								onChange={v => {
									this.setState({
										searchForm: {
											...this.state.searchForm,
											abnormal: v
										}
									});
								}}
							>
								<Option value={null}>请选择</Option>
								{[{label:'是',value:true}, {label:'否',value:false}].map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'异常原因:'}>
							<Select
								showSearch
								placeholder="请选择"
								value={this.state.searchForm.abnormalReasonId}
								onChange={v => {
									this.setState({
										searchForm: {
											...this.state.searchForm,
											abnormalReasonId: v
										}
									});
								}}
							>
								<Option value={null}>请选择</Option>
								{abnormalReasons.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={24} align="right">
						<Button className="searchBtn" type="primary" onClick={this.handleSearch}>
							搜索
						</Button>
						<Button className="searchBtn" type="default" onClick={this.handleReset}>
							重置
						</Button>
					</Col>
				</Row>
			</Form>
		);
	};

	// 渲染详情
	_renderDetailModal = () => {
		const { modalVisible } = this.state;
		const { detail } = this.props;
		return (
			<Modal className="transcribe-task-modal" title={'详情'} visible={modalVisible} onCancel={this.handleCancel}
						 footer={null}>
				<TranscribeTaskDetail detail={detail}/>
			</Modal>
		);
	};

	//打开抄表弹窗
	handleTranscribe(record) {
		this.props.getDetail(record.id, () => this.setState({ transcribeVisible: true }));

	}

	//渲染抄表弹窗
	_renderTranscribe() {
		const { transcribeVisible } = this.state;
		const { detail, form } = this.props;
		const { getFieldDecorator } = form;
		const footer = (
			<Fragment>
				<Button key="submit" type="primary" onClick={() => this.handleClick(detail)}>提交</Button>
				<Button key="back" onClick={this.handleCancel}>取消</Button>
			</Fragment>
		);
		return (
			<Modal className="transcribe-task-modal" title={'抄表'} visible={transcribeVisible} onCancel={this.handleCancel}
						 footer={footer}>
				<Form {...constants.formItemLayout}>
					<Row>
						<Col span={12}>
							<FormItem label={'用户编号:'}>
								{getFieldDecorator('cno', { initialValue: detail ? detail.cno : '' })(<Input disabled/>)}
							</FormItem>
						</Col>

						<Col span={12}>
							<FormItem label={'用户名称:'}>
								{getFieldDecorator('customerName', { initialValue: detail ? detail.customerName : '' })(<Input
									disabled/>)}
							</FormItem>
						</Col>

						<Col span={12}>
							<FormItem label={'片区:'}>
								{getFieldDecorator('areaName', { initialValue: detail ? detail.customerName : '' })(<Input disabled/>)}
							</FormItem>
						</Col>

						<Col span={12}>
							<FormItem label={'用户地址:'}>
								{getFieldDecorator('customerAddress', { initialValue: detail ? detail.customerAddress : '' })(<Input
									disabled/>)}
							</FormItem>
						</Col>

						<Col span={12}>
							<FormItem label={'上期示数:'}>
								{getFieldDecorator('lastWheelNumber', { initialValue: detail ? detail.lastWheelNumber : '' })(<Input
									disabled/>)}
							</FormItem>
						</Col>

						<Col span={12}>
							<FormItem label={'抄表时间:'}>
								<Input value={moment(new Date()).format('YYYY-MM-DD hh:mm:ss')} disabled/>
							</FormItem>
						</Col>

						<Col span={12}>
							<FormItem label={'本期示数:'}>
								{getFieldDecorator('currentWheelNumber', { rules: [{ required: true, message: '请输入本期示数' }] })
								(<Input placeholder="请输入本期示数"/>)}
							</FormItem>
						</Col>


						{/*<Col span={12}>
							<FormItem label={'抄见水量:'}>
								{getFieldDecorator('water')(<Input disabled/>)}
							</FormItem>
						</Col>*/}

						{/*	<Col span={12}>
							<FormItem label={'历史平均水量:'}>
								{getFieldDecorator('averageWaterAmount', { initialValue: detail ? detail.averageWaterAmount===null?0:detail.averageWaterAmount : 0 })(
									<Input disabled/>)}
							</FormItem>
						</Col>

						<Col span={12}>
							<FormItem label={'原因:'}>
								<Select
									placeholder="请选择"
									value={this.state.searchForm.applyType}
									onChange={v => {
										this.setState({
											searchForm: { ...this.state.searchForm, applyType: v }
										});
									}}
								>
									{selectByCode&&selectByCode.map((item, index) => {
										return (
											<Option key={index} value={item.value}>
												{item.label}
											</Option>
										);
									})}
								</Select>
							</FormItem>
						</Col>*/}
					</Row>
				</Form>
			</Modal>
		);
	}

	//渲染原因
	_renderTranscribeSubmit() {
		const { form, result } = this.props;
		let currentWheelNumber = result ? result.data.currentWheelNumber : 0;
		let lastWheelNumber = result ? result.data.lastWheelNumber : 0;
		let water = Number(currentWheelNumber) - Number(lastWheelNumber);
		const { getFieldDecorator } = form;
		const footer = (
			<Fragment>
				<Button key="submit" type="primary" onClick={() => this.handleClickReason(result)}
								loading={this.state.loading}>提交</Button>
				<Button key="back" onClick={this.handleClose}>取消</Button>
			</Fragment>
		);
		return (
			<Modal title={'原因选择'} visible={result ? true : false}
						 onCancel={this.handleClose}
						 footer={footer}>
				<Form {...constants.formItemLayout}>
					<Row>

						<Col span={24}>
							<FormItem label={'抄见水量:'}>
								<Input value={water} disabled/>
							</FormItem>
						</Col>

						<Col span={24}>
							<FormItem label={'历史平均水量:'}>
								{getFieldDecorator('averageWaterAmount', { initialValue: result ? result.detail.averageWaterAmount === null ? 0 : result.detail.averageWaterAmount : 0 })(
									<Input disabled/>)}
							</FormItem>
						</Col>

						<Col span={24}>
							<FormItem label={'抄表结果:'}>
								<Input value={result && result.result.resultType} disabled/>
							</FormItem>
						</Col>

						{
							result && result.result.selectByCode.length > 0 ?
								<Col span={24}>
									<FormItem label={'原因:'}>
										{getFieldDecorator('reasonId')
										(<Select placeholder="请选择">
											{result && result.result.selectByCode.map((item, index) => {
												return (
													<Option key={index} value={item.value}>
														{item.label}
													</Option>
												);
											})}
										</Select>)}
									</FormItem>
								</Col> : void (0)
						}

					</Row>
				</Form>
			</Modal>
		);
	}

	//提交抄表
	handleClick(detail) {
		this.props.form.validateFields((err, values) => {
			if (!err) {
				this.props.getTranscribeResult(values, detail);
			}
		});
	}

	//提交抄表原因
	handleClickReason(result) {
		this.setState({ loading: true });
		let data = {};
		data.id = result.detail.id;
		data.currentWheelNumber = result.data.currentWheelNumber;
		data.reasonId = this.props.form.getFieldValue('reasonId');
		data.dataSource = 'WEB';
		this.props.getRanscribeMeter(data, this.getList, this.handleCancel, () => this.setState({ loading: false }));
	}

	//列表复选框
	onSelectChange = (selectedRowKeys, selectedRows) => {
		this.setState({ selectedRowKeys, selectedRows });
	};

	render() {
		const { page, pageSize,selectedRowKeys } = this.state;
		const { dataList, total } = this.props;
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: (total) => {
				return `共 ${total} 条数据 `;
			},
			onChange: (page) => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		const rowSelection = {
			fixed: true,
			selectedRowKeys,
			onChange: this.onSelectChange
		};
		const columns = [
			{
				title: '抄表任务编号',
				dataIndex: 'no',
				align: 'center'
			},
			{
				title: '用户编号',
				dataIndex: 'cno',
				align: 'center'
			},
			{
				title: '用户户号',
				dataIndex: 'hno',
				align: 'center'
			},
			{
				title: '用户名称',
				dataIndex: 'customerName',
				align: 'center'
			},
			{
				title: '片区',
				dataIndex: 'areaName',
				align: 'center'
			},
			{
				title: '用户地址',
				dataIndex: 'customerAddress',
				align: 'center'
			},
			{
				title: '抄表员',
				dataIndex: 'transcriberName',
				align: 'center'
			},
			{
				title: '派发时间',
				dataIndex: 'createTime',
				align: 'center'
			},
			{
				title: '抄表时间',
				dataIndex: 'transcribeTime',
				align: 'center'
			},
			{
				title: '抄表结果',
				dataIndex: 'result',
				align: 'center'
			},
			{
				title: '用水量',
				dataIndex: 'waterAmount',
				align: 'center'
			},
			{
				title: '抄表来源',
				dataIndex: 'dataSource',
				align: 'center'
			},
			{
				title: '是否异常',
				dataIndex: 'abnormal',
				align: 'center',
				render: text => {
					return text != null ? text ? '是' : '否' : '';
				}
			},
			{
				title: '异常原因',
				dataIndex: 'abnormalReason',
				align: 'center'
			},
			{
				title: '备注',
				dataIndex: 'remark',
				align: 'center'
			},
			{
				title: '抄表状态',
				dataIndex: 'transcribeStatus',
				align: 'center',
				width: 80,
				fixed: 'right',
				render: text => {
					return (
						<Tag color={transcribeStatusColorMap.get(text)}>
							{text}
						</Tag>
					);
				}
			},
			{
				title: '操作',
				key: 'operation',
				width: 200,
				fixed: 'right',
				align: 'center',
				render: (text, record, index) => {
					return (
						<span>
							<Button title="查看" className="btn" type="primary" size="small" icon="eye"
											onClick={() => this.handleView(record)}/>
							<Button title="抄表" className="btn" type="primary" size="small" icon="dashboard"
											onClick={() => this.handleTranscribe(record)} disabled={record.transcribeStatus !== '未抄表'}/>
							<Button title="删除" className="btn" type="primary" size="small" icon="delete"
											onClick={() => this.handleInvalid(record)} disabled={record.transcribeStatus !== '未抄表'}/>
						</span>
					);
				}
			}
		];
		return (
			<div className="shadow-radius">
				<h1>远传表抄表任务</h1>

				{this._renderSearchForm()}

				<Row className="search-button buttonGroup">{this._renderOperationButton()}</Row>

				<Row className="main">
					<Table bordered
								 rowSelection={rowSelection}
								 columns={columns}
								 rowKey={data => data.id}
								 dataSource={dataList}
								 pagination={paginationProps}
								 scroll={{ x: 2500 }}/>
				</Row>
				{this._renderDetailModal()}
				{this._renderTranscribe()}
				{this._renderTranscribeSubmit()}
			</div>
		);
	}
}

const mapStateToProps = (state) => {
	const data = state.get('amrFarPass');
	return {
		fields: data.fields,
		dataList: data.dataList, // 列表数据
		total: data.total, // 总条数
		transcriberSelect: data.transcriberSelect,//抄表员选择框
		areaSelect: data.areaSelect, // 片区选择框
		visible: data.visible, // 是否显示弹窗
		detail: data.detail, // 获取详情
		modelVisible: data.modelVisible, //弹窗显示
		result: data.result,              //抄表返回
		customerLevels: data.customerLevels, // 用户等级
		abnormalReasons: data.abnormalReasons, // 异常原因
	};
};
const mapDispatchToProps = (dispatch) => ({
	setState: (data) => dispatch(actionCreators.setState(data)), // 设置redux值
	list: (data) => dispatch(actionCreators.list(data)), // 获取列表
	getDetail: (data, transcribeVisible) => dispatch(actionCreators.getDetail(data, transcribeVisible)), // 获取列表
	getTranscriberSelect: (data) => dispatch(actionCreators.getTranscriberSelect(data)), // 获取抄表员选择框
	getAreaSelect: () => dispatch(actionCreators.getAreaSelect()), //获取片区选择框
	invalidTask: (data, list) => dispatch(actionCreators.invalidTask(data, list)), // 作废
	download: (data) => dispatch(actionCreators.download(data)), // 下载抄表任务
	getTranscribeResult: (data, detail) => dispatch(actionCreators.getTranscribeResult(data, detail)),  //获取原因
	getRanscribeMeter: (data, list, handleCancel, loading) => dispatch(actionCreators.getRanscribeMeter(data, list, handleCancel, loading)),  //抄表提交
	customerLevelList: () => dispatch(actionCreators.customerLevelList()), // 用户等级
	listAbnormalReason: () => dispatch(actionCreators.listAbnormalReason()), // 获取异常原因
	batchInvalidTask: (data,list) => dispatch(actionCreators.batchInvalidTask(data,list)), //根据勾作废抄表任务
	batchInvalidTaskByCondition: (data,list) => dispatch(actionCreators.batchInvalidTaskByCondition(data,list)), //根据条件作废抄表任务
});
export default connect(mapStateToProps, mapDispatchToProps)(Form.create()(Index));
