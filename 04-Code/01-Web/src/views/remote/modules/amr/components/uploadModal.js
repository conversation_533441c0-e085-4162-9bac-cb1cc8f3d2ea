import React, { Component, Fragment } from 'react';
import { actionCreators } from '../store';
import { connect } from 'react-redux';
import { Button, Col, Form, Input, Row, Icon, Upload, Modal, Select, Tooltip } from 'antd';
import { constants } from '$utils';

const FormItem = Form.Item;
const { Option } = Select;

class UploadModal extends Component {

	constructor(props) {
		super(props);
		this.state = {
			file: null,
			count: 0,
			visible: false,
			loading: false
		};
	};

	//上传导入
	handleClick() {
		const { file } = this.state;
		const { form, list } = this.props;
		form.validateFields((err, values) => {
			if (!err) {
				this.setState({ loading: true });
				let formData = new FormData();
				formData.append('file', file);
				formData.append('isPay', values.isPay);
				//file以外的对象拼接
				this.props.importChargeBill(formData, list, () => this.setState({ visible: false, loading: false }));
			}
		});
	}

	//编辑弹窗
	editModal(visible) {
		this.setState({ count: 0, visible: visible });
	}

	// 拦截文件上传
	beforeUploadHandle = (file) => {
		const { count } = this.state;
		let newCount = count + 1;
		if (newCount < 2) {
			this.setState({ file: file, count: newCount });
			return false;
		} else {
			Modal.error({ title: '上传的文件不能超过1个' });
		}
	};

	render() {
		const { visible, loading } = this.state;
		const { form } = this.props;
		const { getFieldDecorator } = form;
		const footer = (
			<Fragment>
				<Button key="submit" type="primary" loading={loading} onClick={() => this.handleClick()}>提交</Button>
				<Button key="back" onClick={() => this.editModal(false)} disabled={loading}>取消</Button>
			</Fragment>
		);
		return (
			<div style={{ marginLeft: 10 }}>
				<Button
						type="primary"
						size='middle'
						onClick={() => {
							window.open(`${process.env.UPLOAD_ROOT}/templates/transcribe_task_import_to_bill_template.xlsx`);
						}}
					>
						下载抄表数据模板
				</Button>
				<Button style={{ marginLeft: 10 }} type="primary" size='middle' onClick={() => this.editModal(true)}>导入抄表数据结清账单</Button>

				<Modal title="导入抄表数据" destroyOnClose={true} maskClosable={true} visible={visible}
							 onCancel={() => this.editModal(false)}
							 footer={footer}>
					<Form {...constants.formItemLayout}>
						<Row>
							<Col>
								<FormItem label={'是否结清:'}>
									{getFieldDecorator('isPay', { rules: [{ required: true, message: '请选择是否结清' }] })
									(<Select allowClear placeholder="请选择" style={{ width: 220 }}>
										<Option value={0}>否</Option>
										<Option value={1}>是</Option>
									</Select>)}
									<Tooltip placement="top" title="选择是：表示生成对应的订单去结清这个账单,选择否：表示只生成账单用用户余额来结清。
									目前只支持1.5,4.7,5.07,7.64这四个价格">
										<Icon type="question-circle-o" style={{ marginLeft: 10 }}/>
									</Tooltip>
								</FormItem>
							</Col>
							<Col>
								<FormItem label={'上传文件:'}>
									{getFieldDecorator('file', { rules: [{ required: true, message: '请选择上传文件' }] })(
										<Upload name="files" accept={`.xlsx`} beforeUpload={this.beforeUploadHandle}>
											<Button><Icon type="upload" style={{ marginLeft: 10 }}/> 点击上传文件</Button>
										</Upload>
									)}
								</FormItem>
							</Col>
						</Row>
					</Form>
				</Modal>
			</div>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('batch');
	return {};
};
const mapDispatchToProps = dispatch => ({
	setState: data => dispatch(actionCreators.setState(data)), // 设置redux值
	importChargeBill: (data, list,close) => dispatch(actionCreators.importChargeBill(data, list,close)) //自动结清账单
});
export default connect(mapStateToProps, mapDispatchToProps)(Form.create()(UploadModal));
