import React, { Component, Fragment } from 'react';
import { Row, Col, Descriptions, Divider, Tag } from 'antd';
import Img from 'react-zmage';
import NoData from '$components/NoData';
import './index.scss'
import { transcribeStatusColorMap} from '@/constants/colorStyle'

class TranscribeTaskDetail extends Component {
  render() {
    const { detail } = this.props;
    const annexList = detail && detail.annexList;
    return (
      <Fragment>
        <Row>
          <Descriptions title="抄表任务信息" bordered>
            <Descriptions.Item label="抄表员：">{detail && detail.transcriberName}</Descriptions.Item>
            <Descriptions.Item label="用户名称：">{detail && detail.customerName}</Descriptions.Item>
            <Descriptions.Item label="用户编号：">{detail && detail.cno}</Descriptions.Item>
            <Descriptions.Item label="派发人：">{detail && detail.createPersonName}</Descriptions.Item>
            <Descriptions.Item label="派发时间：">{detail && detail.createTime}</Descriptions.Item>
            <Descriptions.Item label="片区：">{detail && detail.areaName}</Descriptions.Item>
            <Descriptions.Item label="抄表状态：">
							<Tag color={transcribeStatusColorMap.get(detail && detail.transcribeStatus)}>
								{detail && detail.transcribeStatus}
							</Tag>
            </Descriptions.Item>
            <Descriptions.Item label="抄表时间：">{detail && detail.transcribeTime}</Descriptions.Item>
            <Descriptions.Item label="抄表月平均水量：">{detail && detail.averageWaterAmount}</Descriptions.Item>
            <Descriptions.Item label="抄表结果：">{detail && detail.result}</Descriptions.Item>
            <Descriptions.Item label="上期示数：">{detail && detail.lastWheelNumber}</Descriptions.Item>
            <Descriptions.Item label="本期示数：">{detail && detail.currentWheelNumber}</Descriptions.Item>
            <Descriptions.Item label="用水量：">{detail && detail.waterAmount}</Descriptions.Item>
            <Descriptions.Item label="备注：">{detail && detail.reason}</Descriptions.Item>
						<Descriptions.Item label="是否异常：">{detail && detail.abnormal != null ? detail.abnormal ? '是' : '否' : ''}</Descriptions.Item>
						<Descriptions.Item label="异常原因：">{detail && detail.abnormalReason}</Descriptions.Item>
          </Descriptions>
        </Row>
        <Row>
          <Divider dashed orientation="left">抄表任务照片</Divider>
          {
            annexList && annexList.length > 0 ? annexList.map((item, index) => {
              return (
                <Col key={index} className='photoInfo' span={8} align='center'>
                  <Img src={`${process.env.UPLOAD_ROOT}/${item.filePath}/${item.newName}`} alt="" />
                </Col>
              )
            }) : <NoData text='暂无照片信息' />
          }
        </Row>
      </Fragment>
    )
  }
}

export default TranscribeTaskDetail;
