import http from '$http';
import * as actionTypes from './constants';
import { message } from 'antd';
import api from './api';

// 数据回填
const payload = (type, data) => ({ type, data });

// 组件操作redux数据状态
const setState = data => ({ type: actionTypes.SET_STATE, data });

// 获取列表
const list = data => {
	return async dispatch => {
		const res = await http.post(api.list, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.LIST_RECORD, res.data))
		}
	}
};
// 详情
const getDetail = (data) => {
	return async dispatch => {
		const res = await http.restGet(api.detail, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.DETAIL_RECORD, res.data))
		}
	}
};
// 作废
const invalid = (data, list) => {
	return async dispatch => {
		const res = await http.post(api.invalid + data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.INVALID_RECORD, res.data))
		}
		list()
	}
};
// 获取片区选择框
const getAreaSelect = () => {
	return async dispatch => {
		const res = await http.get(api.getAreaSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_AREA_SELECT, res.data));
		}
	};
};
// 获取创建人
const getCreateName = (data) => {
	return async dispatch => {
		const res = await http.post(api.getCreateName,data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_CREATE_NAME, res.data));
		}
	};
};
// 重发短信
const reSendSms = (data,list) => {
	return async dispatch => {
		const res = await http.post(api.reSendSms,data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.RESEND_SMS, res.data));
		}
		list()
	};
};
export { setState, list, getDetail, invalid,getAreaSelect,getCreateName,reSendSms};
