import React, { Component } from 'react'
import { actionCreators } from './store'
import { connect } from 'react-redux'
import { Modal, Table, Form, Select, Row, Col, Input, Button, DatePicker, Tag, TreeSelect } from 'antd';
import { constants } from '$utils';
import { TAX_INVOICE_TYPE, PRINT_STATUS, TAX_INVOICE_KIND, TAX_INVOICE_PURPOSE } from '@/constants/taxInvoice';
import InvoiceDetail from './components/InvoiceDetail'
import { printColorMap } from '@/constants/colorStyle';
import './index.scss';
import moment from "moment";

const FormItem = Form.Item
const { RangePicker } = DatePicker;
const { Option } = Select
const { confirm } = Modal;
const { TreeNode } = TreeSelect;

const defaultSearchForm = {
	billNo: '',
	cno: '',
	type: null,// 发票类型
	invoiceCode: '',
	invoiceNo: '',
	status: null,
	waterMeterType: 2,
	areaId: '',
	createStartDateString: '',
	createEndDateString: '',
	createUid: null,
	taxInvoicePurpose: null,
	taxInvoiceKind: null,//发票渠道类别 纸质发票，电子发票 0, "纸质发票" 1, "电子发票"
	sendPhone: '',// 电子邮箱推送手机号码
	email: ''// 邮箱
}

class Index extends Component {
	constructor(props) {
		super(props)
		this.state = {
			expand: false, // 搜索项过多的时候，隐藏部分
			page: 1,
			pageSize: 10,
			searchForm: defaultSearchForm,
			detailModalVisible: false,
		}
	}

	componentDidMount() {
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields && isTags) {
			this.setState({
				...fields.state
			}, () => {
				this.props.form.setFieldsValue({
					...fields.form
				})
			})
		} else {
			this.getList();
			this.props.getAreaSelect();
			this.props.getCreateName({ waterMeterType: 2 })
		}
	}

	componentWillUnmount() {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			})
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			})
		}
	}

	// 获取列表
	getList = () => {
		const { page, pageSize, searchForm } = this.state
		this.props.list(Object.assign({ ...searchForm }, { page, pageSize }))
	}

	// 设置订单搜索日期
	resetQueryDate = () => {
		this.props.form.setFieldsValue({ rangPicker: ['',''] });
	};

	// 列表分页
	handlePageChange = (page) => {
		this.setState(
			{
				page
			},
			() => {
				this.getList()
			}
		)
	}

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState(
			{
				page: 1,
				pageSize: size
			},
			() => {
				this.getList()
			}
		)
	}

	// 搜索功能
	handleSearch = () => {
		this.setState(
			{
				page: 1
			},
			() => {
				this.getList()
			}
		)
	}

	// 重置搜索
	handleReset = () => {
		this.resetQueryDate();
		this.setState(
			{
				page: 1,
				searchForm: Object.assign({}, defaultSearchForm)
			},
			() => {
				this.getList()
			}
		)
	}

	handleReSendSms = (record) => {
		this.setState({ selectedId: record.id, resendSmsModalVisible: true ,sendPhone:record.sendPhone})
	}

	//发票推送
	handelSmsUpdate = (e) => {
		this.props.form.validateFields((err, values) => {
			if (!err) {
				let params = {
					id: this.state.selectedId,
					sendPhone: values.sendPhone
				};
				this.props.reSendSms(params, this.getList);
				this.handleSmsCancel()
			}
		});
	}
	// 取消弹窗
	handleSmsCancel = () => {
		this.props.form.resetFields();
		this.setState({ resendSmsModalVisible: false,selectedId:null})
	}

	// 查看详情
	handleView = record => {
		this.props.getDetail(record.id);
		this.setState({ detailModalVisible: true })
	};

	// 取消弹窗
	handleCancel = () => {
		this.setState({ detailModalVisible: false })
	};

	// 作废
	handleInvalid = record => {
		const _this = this;
		confirm({
			title: '确认操作',
			content: '确定要作废此发票吗？',
			okText: '确定',
			okType: 'danger',
			cancelText: '取消',
			onOk() {
				_this.props.invalid(record.id, _this.getList);
			},
			onCancel() {
				console.log('Cancel');
			}
		});
	};

	//递归渲染
	_renderTreeNode = treeData => {
		return treeData.map(tree => {
			if (tree.children) {
				return (
					<TreeNode key={tree.id} title={tree.name} value={tree.id}>
						{this._renderTreeNode(tree.children)}
					</TreeNode>
				);
			} else {
				return <TreeNode key={tree.id} title={tree.name} value={tree.id} />;
			}
		});
	};

	// 渲染短信弹出框
	_renderReSendSmsModal = () => {
		const { resendSmsModalVisible ,sendPhone} = this.state
		const { getFieldDecorator } = this.props.form;
		return (
				<Modal className="receipt-record-modal" title="发票手机推送" visible={resendSmsModalVisible}
							 onCancel={this.handleSmsCancel} footer={null} destroyOnClose={true}>
					<Form className="ant-advanced-search-form" {...constants.formItemLayout} key={'111'}>
						<Row>
							<Col span={24}>
								<FormItem label="请输入手机号：">
									{getFieldDecorator('sendPhone', {
										initialValue: sendPhone,
										rules: [
											{
												required: true,
												message: '请输入手机号'
											}
										]
									})(
											<Input placeholder="请输入手机号" />
									)}
								</FormItem>
							</Col>
						</Row>
						<Row>
							<Col span={24} align="center">
								<Button type="primary" className="btn" onClick={this.handelSmsUpdate}>
									发送
								</Button>
								<Button type="default" className="btn" onClick={this.handleSmsCancel}>
									取消
								</Button>
							</Col>
						</Row>
					</Form>
				</Modal>
		)
	}

	//开票时间查询条件
	gerInvoiceDate = (date, dateString) => {
		if (dateString.length === 2) {
			this.setState({
				searchForm: { ...this.state.searchForm, createStartDateString: dateString[0], createEndDateString: dateString[1] }
			});
		}
	};
	// 渲染搜索
	_renderSearchForm = () => {
		const { searchForm } = this.state
		const { areaSelect, createName } = this.props;
		const { getFieldDecorator } = this.props.form;

		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					<Col span={8}>
						<FormItem label={'账单编号:'}>
							<Input
								placeholder="请输入账单编号"
								value={searchForm.billNo}
								onChange={v => this.setState({ searchForm: { ...searchForm, billNo: v.target.value } })}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'用户编号:'}>
							<Input
								placeholder="请输入用户编号"
								value={searchForm.cno}
								onChange={v => this.setState({ searchForm: { ...searchForm, cno: v.target.value } })}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'发票类型:'}>
							<Select
								placeholder="请选择"
								value={searchForm.type}
								onChange={(v) => {
									this.setState({
										searchForm: { ...searchForm, type: v }
									})
								}}
							>
								{TAX_INVOICE_TYPE.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									)
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'发票渠道:'}>
							<Select
								placeholder="请选择"
								value={searchForm.taxInvoiceKind}
								onChange={(v) => {
									this.setState({
										searchForm: { ...searchForm, taxInvoiceKind: v }
									});
								}}
							>
								{TAX_INVOICE_KIND.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'发票种类:'}>
							<Select
								placeholder="请选择"
								value={searchForm.taxInvoicePurpose}
								onChange={(v) => {
									this.setState({
										searchForm: { ...searchForm, taxInvoicePurpose: v }
									});
								}}
							>
								{TAX_INVOICE_PURPOSE.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'发送手机:'}>
							<Input
								placeholder="请输入发送手机"
								value={searchForm.sendPhone}
								onChange={v => this.setState({ searchForm: { ...searchForm, sendPhone: v.target.value } })}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'邮箱:'}>
							<Input
								placeholder="请输入邮箱"
								value={searchForm.email}
								onChange={v => this.setState({ searchForm: { ...searchForm, email: v.target.value } })}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'发票代码:'}>
							<Input
								placeholder="请输入发票代码"
								value={searchForm.invoiceCode}
								onChange={v => this.setState({ searchForm: { ...searchForm, invoiceCode: v.target.value } })}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'发票号码:'}>
							<Input
								placeholder="请输入发票代码"
								value={searchForm.invoiceNo}
								onChange={v => this.setState({ searchForm: { ...searchForm, invoiceNo: v.target.value } })}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'打印状态:'}>
							<Select
								placeholder="请选择"
								value={searchForm.status}
								onChange={(v) => {
									this.setState({
										searchForm: { ...searchForm, status: v }
									})
								}}
							>
								{PRINT_STATUS.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									)
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'开票时间:'}>
							{getFieldDecorator('rangPicker',
									{
										rules: [{ type: 'array' }]
									})(
									<RangePicker
											onChange={this.gerInvoiceDate}
											placeholder={['开始时间', '结束时间']} />)}
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'片区:'}>
							<TreeSelect
								style={{ width: '100%' }} dropdownStyle={{ maxHeight: 400, overflow: 'auto' }} placeholder="请选择片区"
								allowClear
								treeDefaultExpandedKeys={[100]}
								value={searchForm.areaId}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, areaId: v } });
								}}>
								{this._renderTreeNode(areaSelect)}
							</TreeSelect>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'创建人:'}>
							<Select placeholder="请选择" value={searchForm.createUid} onChange={(v) => {
								this.setState({ searchForm: { ...searchForm, createUid: v } });
							}}>
								{createName.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
				</Row>

				<Row>
					<Col span={24} align="right">
						<Button type="primary" onClick={this.handleSearch}>
							搜索
						</Button>
						&nbsp;&nbsp;&nbsp;&nbsp;
						<Button type="default" onClick={this.handleReset}>
							重置
						</Button>
					</Col>
				</Row>
			</Form>
		)
	}

	// 渲染详情
	_renderDetailModal = () => {
		const { detailModalVisible } = this.state;
		const { detail } = this.props;
		return (
			<Modal className="bill-print-invoice-modal" title={'详情'} visible={detailModalVisible} onCancel={this.handleCancel} footer={null}>
				<InvoiceDetail detail={detail} />
			</Modal>
		);
	};

	render() {
		const { page, pageSize } = this.state
		const { dataList, total } = this.props
		const columns = [
			{
				title: '账单编号',
				dataIndex: 'billNo',
				align: 'center'
			},
			{
				title: '用户编号',
				dataIndex: 'cno',
				align: 'center'
			},
			{
				title: '用户名称',
				dataIndex: 'customerName',
				align: 'center'
			},
			{
				title: '片区',
				dataIndex: 'areaName',
				align: 'center'
			},
			{
				title: '用户地址',
				dataIndex: 'address',
				align: 'center',
				sorter: (a, b) => a.address && a.address.localeCompare(b.address)
			},
			{
				title: '打印内容',
				dataIndex: 'contentType',
				align: 'center'
			},
			{
				title: '总水量',
				dataIndex: 'totalWaterAmount',
				align: 'center'
			},
			{
				title: '总金额',
				dataIndex: 'totalFeeAmount',
				align: 'center'
			},
			{
				title: '发票类型',
				dataIndex: 'type',
				align: 'center'
			},
			{
				title: '发票代码',
				dataIndex: 'invoiceCode',
				align: 'center'
			},
			{
				title: '发票号码',
				dataIndex: 'invoiceNo',
				align: 'center'
			},
			{
				title: '发票日期',
				dataIndex: 'printDate',
				align: 'center'
			},
			{
				title: '发票渠道',
				dataIndex: 'taxInvoiceKind',
				align: 'center'
			},
			{
				title: '发票种类',
				dataIndex: 'taxInvoicePurpose',
				align: 'center'
			},
			{
				title: '发送手机',
				dataIndex: 'sendPhone',
				align: 'center'
			},
			{
				title: '邮箱',
				dataIndex: 'email',
				align: 'center'
			},
			{
				title: '开票人',
				dataIndex: 'kpy',
				align: 'center'
			},
			{
				title: '打印人',
				dataIndex: 'createPersonName',
				align: 'center'
			},
			{
				title: '开票时间',
				dataIndex: 'createTime',
				align: 'center'
			},
			{
				title: '状态',
				dataIndex: 'status',
				width: 80,
				align: 'center',
				fixed: 'right',
				render: text => {
					return (
						<Tag color={printColorMap.get(text)}>
							{text}
						</Tag>
					);
				}
			},
			{
				title: '操作',
				key: 'operation',
				width: 150,
				align: 'center',
				fixed: 'right',
				render: (text, record, index) => {
					return (
						<span>
							<Button title="查看" className="btn" type="primary" size="small" icon="eye"
								onClick={() => this.handleView(record)} />
							<Button title="作废" className="btn" type="danger" size="small" icon="delete"
								onClick={() => this.handleInvalid(record)} disabled={record.status == '作废' || record.taxInvoicePurpose == '红票'} />
							{
								record.taxInvoiceKind==='电子发票' ? (
										<Button title="短信重新推送" className="btn" type="primary" size="small" icon="message"
														onClick={() => this.handleReSendSms(record)} disabled={ record.status == '作废' || record.taxInvoicePurpose == '红票'} />
								) :(
										''
								)
							}
						</span>
					);
				}
			}
		];
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: (total) => {
				return `共 ${total} 条数据 `
			},
			onChange: (page) => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		}
		return (
			<div className="shadow-radius bill-print-invoice">
				<Row>
					<Col>
						<h1>账单发票打印记录</h1>
					</Col>
				</Row>
				<Row>{this._renderSearchForm()}</Row>
				<Row className="main">
					<Table
						bordered
						columns={columns}
						scroll={{ x: 3000 }}
						rowKey={() => Math.random()}
						dataSource={dataList}
						pagination={paginationProps}
					/>
				</Row>
				{this._renderDetailModal()}
				{this._renderReSendSmsModal()}
			</div>
		)
	}
}

const mapStateToProps = (state) => {
	const data = state.get('remoteBillPrintInvoiceRecord')
	return {
		fields: data.fields,
		dataList: data.dataList, // 列表数据
		total: data.total, // 总条数
		visible: data.visible, // 是否显示弹窗
		detail: data.detail, // 获取详情
		areaSelect: data.areaSelect, // 片区选择框
		createName: data.createName // 片区选择框
	}
}
const mapDispatchToProps = (dispatch) => ({
	setState: (data) => dispatch(actionCreators.setState(data)), // 设置redux值
	list: (data) => dispatch(actionCreators.list(data)), // 获取列表
	getDetail: data => dispatch(actionCreators.getDetail(data)), // 获取详情
	invalid: (data, list) => dispatch(actionCreators.invalid(data, list)), // 作废
	getAreaSelect: () => dispatch(actionCreators.getAreaSelect()), //获取片区选择框
	getCreateName: (data) => dispatch(actionCreators.getCreateName(data)) ,      //创建人
	reSendSms: (data,list) => dispatch(actionCreators.reSendSms(data,list))       //短信重新推送
})
export default connect(mapStateToProps, mapDispatchToProps)(Form.create()(Index))
