import React, { Component, Fragment } from 'react';
import { withRouter } from 'react-router-dom';
import { actionCreators } from './store';
import { connect } from 'react-redux';
import http from '$http';
import {
	TreeSelect,
	InputNumber,
	Button,
	Col,
	DatePicker,
	Form,
	Input,
	Row,
	Select,
	Table,
	Modal,
	Cascader,
	message,
	Descriptions,
	Tag
} from 'antd';
import { constants } from '$utils';
import {
	WATER_METER_MANUFACTURER,
	VALVE_STATUE,
	WATER_METER_ABNORMAL_TYPE,
	WATER_METER_ABNORMAL_STATUS
} from '@/constants/waterMeter';
import './index.scss';
import { valveStatusColorMap, abnormalStatusColor } from '@/constants/colorStyle';


const FormItem = Form.Item;
const { Option } = Select;
const ButtonGroup = Button.Group;
const { RangePicker } = DatePicker;
const { TreeNode } = TreeSelect;
const InputGroup = Input.Group;
const { confirm } = Modal;
const { TextArea } = Input;
const defaultSearchForm = {
	areaId: null,// 片区ID
	cno: null, // 用户编号
	manufacturer: null,// 水表厂家
	no: null, // 水表编号
	valveStatus: null,// 阀门状态,
	communicationTimeStart: null, // 通讯开始时间
	communicationTimeEnd: null, // 通讯结束时间
	type: null, // 异常原因
	status: null, // 状态
	handlerId: null, // 处理人
	handingTimeStart: null, // 处理开始时间
	handingTimeEnd: null, // 处理结束时间
	createTimeStart: null, // 创建开始时间
	createTimeEnd: null, // 创建结束时间
	typeList: [], // 异常原因数组
	rangePickerStart1: undefined,
	rangePickerEnd1: undefined,
	rangePickerStart2: undefined,
	rangePickerEnd2: undefined,
	rangePickerStart3: undefined,
	rangePickerEnd3: undefined,
};

class Index extends Component {
	constructor(props) {
		super(props);
		this.state = {
			title: '',
			page: 1,
			pageSize: 10,
			searchForm: defaultSearchForm,
			expand: false,
			modalVisible: false,
			modalType: 0 // 0:查看 1:处理 2:待处理
		};
	}

	componentDidMount() {
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields && isTags) {
			this.setState({
				...fields.state
			}, () => {
				this.props.form.setFieldsValue({
					...fields.form
				});
				// React.$goWhere('remote', this.props);
			});
		} else {
			this.props.getAreaSelect();
			this.props.getHandlerSelect();
		}
		document.addEventListener('keypress', this.handleEnterKey);
	}

	componentWillUnmount() {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			});
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			});
		}
		document.removeEventListener('keypress', this.handleEnterKey);
	}

	// 回车事件
	handleEnterKey = (e) => {
		if (e.keyCode === 13) {
			this.getList();
		}
	};
	// 获取列表
	getList = () => {
		const { page, pageSize, searchForm } = this.state;
		let params = {
			page,
			pageSize,
			...searchForm
		};
		this.props.list(params);
		//this.props.getInversionCount();
	};

	// 搜索
	handleSearch = () => {
		this.setState({
			page: 1
		}, () => {
			this.getList();
			this.setState({ selectedRowKeys: [] });

		});
	};

	// 重置搜索
	handleReset = () => {
		this.setState({
			page: 1,
			searchForm: defaultSearchForm,
			rangePickerStart1: undefined,
			rangePickerStart2: undefined,
			rangePickerStart3: undefined,
			rangePickerEnd1: undefined,
			rangePickerEnd2: undefined,
			rangePickerEnd3: undefined
		}, () => {
			this.setState({ selectedRowKeys: [] });
		});
	};

	// 列表分页
	handlePageChange = (page) => {
		this.setState(
			{
				page
			},
			() => {
				this.getList();
			}
		);
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState(
			{
				page: 1,
				pageSize: size
			},
			() => {
				this.getList();
			}
		);
	};

	// 待处理
	pending = (record) => {
		Modal.confirm({
			title: '提示',
			content: `确认为待处理吗？`,
			okText: '确认',
			cancelText: '取消',
			onOk: () => {
				this.props.pending({ id: record.id }, this.getList);
			}
		});
	};

	// 查看详情
	handleView = (record, modalType = 0) => {
		this.props.getDetail(record.id);
		this.setState({ modalVisible: true, modalType: modalType });
	};
	// 取消弹窗
	handleCancel = () => {
		this.setState({
			modalVisible: false
		});
	};
	// 处理后提交
	handleSubmit = (record, modalType) => {
		if (modalType == 1) {
			const remark = this.props.form.getFieldValue('remark');
			const object = { ...record, remark };
			this.props.handleSubmit(object, this.getList);
		} else if (modalType == 2) {
			const pendingRemark = this.props.form.getFieldValue('pendingRemark');
			const object = { ...record, pendingRemark };
			this.props.pending(object, this.getList);
		}
		this.handleCancel();
	};


	// 日期选择
	getDate = (date, dateString, startName, endName) => {
		if (dateString.length === 2) {
			var object = {};
			object[startName] = dateString[0];
			object[endName] = dateString[1];
			this.setState({
				searchForm: { ...this.state.searchForm, ...object }
			});
		}

		if(startName == 'communicationTimeStart'){
			this.setState({ rangePickerStart1: date[0], rangePickerEnd1: date[1] })
		}else if(startName == 'handingTimeStart'){
			this.setState({ rangePickerStart2: date[0], rangePickerEnd2: date[1] })
		}else if(startName == 'createTimeStart'){
			this.setState({ rangePickerStart3: date[0], rangePickerEnd3: date[1] })
		}
	};
	//递归渲染片区选项
	_renderTreeNode = treeData => {
		return treeData.map(tree => {
			if (tree.children) {
				return (
					<TreeNode key={tree.id} title={tree.name} value={tree.id}>
						{this._renderTreeNode(tree.children)}
					</TreeNode>
				);
			} else {
				return <TreeNode key={tree.id} title={tree.name} value={tree.id}/>;
			}
		});
	};

	// 渲染搜索
	_renderSearchForm = () => {
		const { searchForm, expand, rangePickerStart1, rangePickerStart2, rangePickerStart3, rangePickerEnd1, rangePickerEnd2, rangePickerEnd3 } = this.state;
		const { areaSelect, handlers } = this.props;
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					<Col span={8}>
						<FormItem label={'片区:'}>
							<TreeSelect
								style={{ width: '100%' }}
								dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
								value={searchForm.areaId}
								placeholder="请选择片区"
								allowClear
								showSearch
								filterTreeNode={(inputValue, treeNode) => treeNode.props.title.indexOf(inputValue) > -1}
								treeDefaultExpandedKeys={[100]}
								onChange={value => {
									const { searchForm } = this.state;
									this.setState({
										searchForm: {
											...searchForm,
											areaId: value
										}
									});
								}}
							>
								{
									areaSelect && this._renderTreeNode(areaSelect)
								}
							</TreeSelect>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'用户编号:'}>
							<Input
								placeholder="请输入"
								value={searchForm.cno}
								onChange={v => {
									const { searchForm } = this.state;
									this.setState({
										searchForm: {
											...searchForm,
											cno: v.target.value
										}
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'水表厂家:'}>
							<Select
								placeholder="请选择"
								value={this.state.searchForm.manufacturer}
								onChange={(v) => {
									this.setState({
										searchForm: { ...this.state.searchForm, manufacturer: v }
									});
								}}
							>
								{WATER_METER_MANUFACTURER.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={8}>
						<FormItem label={'水表编号:'}>
							<Input
								placeholder="请输入"
								value={searchForm.no}
								onChange={v => {
									const { searchForm } = this.state;
									this.setState({
										searchForm: {
											...searchForm,
											no: v.target.value
										}
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'阀门状态:'}>
							<Select
								placeholder="请选择"
								value={searchForm.valveStatus}
								onChange={v => {
									const { searchForm } = this.state;
									this.setState({
										searchForm: {
											...searchForm,
											valveStatus: v
										}
									});
								}}
							>
								{
									VALVE_STATUE.map((item, index) => {
										return <Option key={index} value={item.value}>{item.label}</Option>;
									})
								}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'通讯时间:'}>
							<RangePicker value={[ rangePickerStart1, rangePickerEnd1 ]} onChange={(d, ds) => {
								this.getDate(d, ds, 'communicationTimeStart', 'communicationTimeEnd');
							}}
													 placeholder={['开始时间', '结束时间']}/>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={8}>
						<FormItem label={'异常原因:'}>
							<Select
								mode="multiple"
								allowClear
								showSearch
								placeholder="请选择"
								value={searchForm.typeList}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, typeList: v } });
								}} showArrow>
								{WATER_METER_ABNORMAL_TYPE.map((item, index) => {
									return (<Option key={index} value={item.value}>{item.label}</Option>);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label="状态：">
							<Select
								allowClear
								value={searchForm.status}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, status: v } });
								}}
							>
								{WATER_METER_ABNORMAL_STATUS.map((item, index) => {
									return (<Option key={index} value={item.value}>{item.label}</Option>);
								})}</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label="处理人：">
							<Select
								allowClear
								value={searchForm.handlerId}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, handlerId: v } });
								}}
							>
								<Option value={null}>请选择</Option>
								{handlers.map((item, index) => {
									return (<Option value={item.value}>{item.label}</Option>);
								})}</Select>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={8} style={{ display: expand ? 'block' : 'none' }}>
						<FormItem label={'处理时间:'}>
							<RangePicker value={[rangePickerStart2, rangePickerEnd2]} onChange={(d, ds) => {
								this.getDate(d, ds, 'handingTimeStart', 'handingTimeEnd');
							}}
													 placeholder={['开始时间', '结束时间']}/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'创建时间:'}>
							<RangePicker value={[rangePickerStart3, rangePickerEnd3]} onChange={(d, ds) => {
								this.getDate(d, ds, 'createTimeStart', 'createTimeEnd');
							}}
													 placeholder={['开始时间', '结束时间']}/>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={24} align="right">
						<Button className="searchBtn" type="primary" onClick={this.handleSearch}>
							搜索
						</Button>
						<Button className="searchBtn" type="default" onClick={this.handleReset}>
							重置
						</Button>
						<Button type="link" onClick={this.showMoreSearch}>
							{this.state.expand ? '关闭高级搜索' : '高级搜索'}
						</Button>
					</Col>
				</Row>
			</Form>
		);
	};

	// 显示高级搜索
	showMoreSearch = () => {
		this.setState({
			expand: !this.state.expand
		});
	};

	// 选中事件
	onSelectChange = (selectedRowKeys, selectedRows) => {
		this.setState({ selectedRowKeys, selectedRows });
	};

	// 导出用户
	handleDownload = () => {
		const { searchForm } = this.state;
		const url = `${process.env.API_ROOT}/api/wm/watermeter/abnormal/exportWaterMeterAbnormal`;
		http.export(url, searchForm, (res) => {
			const blob = new Blob([res]);
			const downloadElement = document.createElement('a');
			const href = window.URL.createObjectURL(blob);
			const fileName = '远传表用户信息_' + new Date().getTime() + '.xlsx';
			downloadElement.href = href;
			downloadElement.download = fileName;
			document.body.appendChild(downloadElement);
			downloadElement.click();
			document.body.removeChild(downloadElement);
			window.URL.revokeObjectURL(href);
		});
	};

	render() {
		const { page, pageSize, selectedRowKeys, modalVisible, modalType, remark, pendingRemark } = this.state;
		const { dataList, detail, total, form, count } = this.props;
		const { getFieldDecorator } = form;
		const columns = [
			{
				title: '片区',
				dataIndex: 'areaName',
				key: 'areaName',
				align: 'center'
			},
			{
				title: '用户编号',
				dataIndex: 'cno',
				key: 'cno',
				align: 'center'
			},
			{
				title: '用户名称',
				dataIndex: 'name',
				key: 'name',
				align: 'center'
			},
			{
				title: '用户地址',
				dataIndex: 'address',
				key: 'address',
				align: 'center',
				sorter: (a, b) => a.address && a.address.localeCompare(b.address)
			},
			{
				title: '水表厂家',
				dataIndex: 'manufacturer',
				key: 'manufacturer',
				align: 'center'
			},
			{
				title: '水表编号',
				dataIndex: 'no',
				key: 'no',
				align: 'center'
			},
			{
				title: '字轮读数',
				dataIndex: 'wheelNumber',
				key: 'wheelNumber',
				align: 'center'
			},
			{
				title: '阀门状态',
				dataIndex: 'valveStatus',
				key: 'valveStatus',
				align: 'center',
				render: text => {
					return (
						<Tag color={valveStatusColorMap.get(text)}>
							{text}
						</Tag>
					);
				}
			},
			{
				title: '通讯时间',
				dataIndex: 'communicationTime',
				key: 'communicationTime',
				align: 'center'
			},
			{
				title: '异常次数',
				dataIndex: 'excetionCount',
				key: 'excetionCount',
				align: 'center'
			},
			{
				title: '异常原因',
				dataIndex: 'type',
				key: 'type',
				align: 'center'
			},
			{
				title: '处理人',
				dataIndex: 'handlerName',
				key: 'handlerName',
				align: 'center'
			},
			{
				title: '处理时间',
				dataIndex: 'handingTime',
				key: 'handingTime',
				align: 'center'
			},
			{
				title: '创建时间',
				dataIndex: 'createTime',
				key: 'createTime',
				align: 'center'
			},
			{
				title: '状态',
				dataIndex: 'status',
				key: 'status',
				align: 'center',
				width: 80,
				fixed: 'right',
				render: text => {
					return (
						<Tag color={abnormalStatusColor.get(text)}>
							{text}
						</Tag>
					);
				}
			},
			{
				title: '操作',
				key: 'operation',
				width: 220,
				align: 'center',
				fixed: 'right',
				render: (text, record, index) => {
					return (
						<span>
							<ButtonGroup>
								<Button
									title="查看"
									className="btn"
									type="primary"
									size="small"
									icon="eye"
									onClick={() => this.handleView(record, 0)}
								/>
								<Button
									title="处理"
									className="btn"
									type="primary"
									size="small"
									disabled={record.status == WATER_METER_ABNORMAL_STATUS[2].label}
									icon="check-circle"
									onClick={() => this.handleView(record, 1)}
								/>
								{record.status == WATER_METER_ABNORMAL_STATUS[1].label ? <Button
									title="待处理"
									className="btn"
									type="primary"
									size="small"
									icon="copy"
									onClick={() => this.handleView(record, 2)}
								/> : null
								}
							</ButtonGroup>
						</span>
					);
				}
			}
		];
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: (total) => {
				return `共 ${total} 条数据 `;
			},
			onChange: (page) => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		const rowSelection = {
			fixed: true,
			selectedRowKeys,
			onChange: this.onSelectChange
		};
		return (
			<div className="shadow-radius">
				{/* 页面标题 */}
				<Row>
					<Col>
						<h1>远传表异常数据</h1>
					</Col>
				</Row>
				<Row>{this._renderSearchForm()}</Row>
				<Row className="search-button buttonGroup">
					<Col>
						<Button type="primary" className="btn" onClick={this.handleDownload}>
							导出
						</Button>
					</Col>
				</Row>
				<Row className="main">
					<Table
						rowKey={data => data.id}
						bordered
						columns={columns}
						scroll={{ x: 2800 }}
						dataSource={dataList}
						pagination={paginationProps}
					/>
				</Row>
				{
					<Modal className="check-meter-task-modal" title={'详情'} visible={modalVisible} onCancel={this.handleCancel}
								 footer={<Fragment>
									 {modalType == 0 ? null :
										 <Button key="submit" type="primary"
														 onClick={() => this.handleSubmit(detail, modalType)}>提交</Button>
									 }
									 <Button key="back" onClick={this.handleCancel}>返回</Button>
								 </Fragment>}>
						<Fragment>
							<Row>
								{
									detail ? <Descriptions title="详情" bordered>
											<Descriptions.Item label="片区">{detail.areaName}</Descriptions.Item>
											<Descriptions.Item label="用户编号：">{detail.cno}</Descriptions.Item>
											<Descriptions.Item label="用户名称：">{detail.name}</Descriptions.Item>
											<Descriptions.Item
												label="抄表员：">{detail.customer && detail.customer.transcribePersonName}</Descriptions.Item>
											<Descriptions.Item label="联系号码：">{detail.contactPhone}</Descriptions.Item>
											<Descriptions.Item label="用户地址：">{detail.address}</Descriptions.Item>
											<Descriptions.Item label="水表厂家：">{detail.manufacturer}</Descriptions.Item>
											<Descriptions.Item label="水表编号：">{detail.no}</Descriptions.Item>
											<Descriptions.Item label="字轮读数：">{detail.wheelNumber}</Descriptions.Item>
											<Descriptions.Item label="阀门状态：">{detail.valveStatus}</Descriptions.Item>
											<Descriptions.Item label="通讯时间：">{detail.communicationTime}</Descriptions.Item>
											<Descriptions.Item label="异常原因：">{detail.type}</Descriptions.Item>
											<Descriptions.Item label="异常次数：">{detail.excetionCount}</Descriptions.Item>

											<Descriptions.Item label="状态：" span={2}>{detail.status}</Descriptions.Item>
											<Descriptions.Item label="异常原因说明：" span={3}>{detail.descr}</Descriptions.Item>
											{
												modalType == 2 ?
													<Descriptions.Item label="待处理备注：" span={3}>
														<FormItem>
															{getFieldDecorator('pendingRemark', {
																initialValue: pendingRemark
															})
															(<TextArea placeholder="请输入待处理备注"/>)
															}
														</FormItem>
													</Descriptions.Item>
													:
													<Descriptions.Item label="待处理备注：" span={3}>{detail.pendingRemark}</Descriptions.Item>
											}
											{
												modalType == 1 ?
													<Descriptions.Item label="处理备注：" span={3}>
														<FormItem>
															{getFieldDecorator('remark', {
																initialValue: remark
															})
															(<TextArea placeholder="请输入处理备注"/>)
															}
														</FormItem>
													</Descriptions.Item> :
													<Descriptions.Item label="处理备注：" span={3}>{detail.remark}</Descriptions.Item>
											}

										</Descriptions>
										: null}
							</Row>
						</Fragment>
					</Modal>
				}
			</div>
		);
	}
}

const mapStateToProps = (state) => {
	const data = state.get('remoteAbnormal');
	return {
		fields: data.fields,
		dataList: data.dataList, // 列表数据
		total: data.total, // 总条数
		count: data.count,
		visible: data.visible, // 是否显示弹窗
		detail: data.detail, // 获取详情
		areaSelect: data.areaSelect, // 片区
		handlers: data.handlers // 历史处理人
	};
};
const mapDispatchToProps = (dispatch) => ({
	setState: (data) => dispatch(actionCreators.setState(data)), // 设置redux值
	list: (data) => dispatch(actionCreators.list(data)), // 获取列表
	getInversionCount: (data) => dispatch(actionCreators.getInversionCount(data)), // 倒装数量
	getDetail: (data) => dispatch(actionCreators.detail(data)), // 获取详情
	getAreaSelect: (data) => dispatch(actionCreators.getAreaSelect()), // 获取片区
	getHandlerSelect: () => dispatch(actionCreators.getHandlerSelect()),// 获取历史处理人
	handleSubmit: (data, list) => dispatch(actionCreators.handleSubmit(data, list)),// 处理后提交
	pending: (data, list) => dispatch(actionCreators.pending(data, list)) // 修改为待处理
});
export default connect(mapStateToProps, mapDispatchToProps)(Form.create()(withRouter(Index)));
