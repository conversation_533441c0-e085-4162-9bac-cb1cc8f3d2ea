export default {
	list: `api/wm/watermeter/abnormal/listPage`, // 获取列表
	getInversionCount: `api/wm/watermeter/abnormal/getInversionCount`,
	detail: `api/wm/watermeter/abnormal/get/`, // 查看详情
	getAreaSelect: `api/sys/area/getTree`, // 获取片区
	waterUseKindList: `/api/wuk/waterusekind/getSelect`,
	reallyPosition: `/api/sys/dictionary/getSelectByClass/`,
	getHandlerSelect:`api/wm/watermeter/abnormal/getHandlerSelect`, // 历史处理人
	handleSubmit : `api/wm/watermeter/abnormal/update`, // 处理更新
	pending : `api/wm/watermeter/abnormal/updatePending`
};
