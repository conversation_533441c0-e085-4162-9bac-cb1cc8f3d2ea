import http from '$http';
import * as actionTypes from './constants';
import { message } from 'antd';
import api from './api';

// 数据回填
const payload = (type, data) => ({ type, data });

// 组件操作redux数据状态
const setState = data => ({ type: actionTypes.SET_STATE, data });

// 获取列表
const list = data => {
	return async dispatch => {
		const res = await http.post(api.list, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.LIST_RECORD, res.data))
		}
	}
};

// 获取片区列表
const getAreaSelect = () => {
	return async dispatch => {
		const res = await http.get(api.getAreaSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_AREAS_RECORD, res.data));
		}
	};
};

// 获取创建人选择框
const getCreateUidSelect = () => {
	return async dispatch => {
		const res = await http.get(api.getCreateUidSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_CREATE_UID_SELECT, res.data));
		}
	};
};
export { setState, list, getCreateUidSelect,getAreaSelect };
