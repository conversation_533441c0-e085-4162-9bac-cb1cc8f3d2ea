import React, { Component } from 'react';
import { actionCreators } from './store';
import { connect } from 'react-redux';
import http from '$http';
import {
	Table,
	Form,
	Select,
	Row,
	Col,
	Input,
	Button,
	DatePicker,
	Icon,
	TreeSelect
} from 'antd';
import { constants } from '$utils';
import { WATER_METER_MANUFACTURER } from '@/constants/waterMeter';
import { VALVE_OPERATION_TYPE, VALVE_OPERATION_RESULT } from '@/constants/valve';
import './index.scss';

const FormItem = Form.Item;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TreeNode } = TreeSelect;


const defaultSearchForm = {
	cno: '',
	manufacturer: null,
	waterMeterNo: '',
	createStartDate: '',
	createEndDate: '',
	operationType: null,
	operationResult: null,
	createUid: null,
	customerName: null,
	customerAddress: null,
};

class Index extends Component {
	constructor(props) {
		super(props);
		this.state = {
			page: 1,
			pageSize: 10,
			searchForm: defaultSearchForm,
			startTime: undefined,
			endTime: undefined,
		};
	}

	componentDidMount() {
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields && isTags) {
			this.setState({
				...fields.state
			}, () => {
				this.props.form.setFieldsValue({
					...fields.form
				});
			});
		} else {
			this.getList();
		}
		this.props.getAreaSelect();

	}

	componentWillUnmount() {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			});
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			});
		}
	}

	// 获取列表
	getList = () => {
		const { page, pageSize, searchForm } = this.state;
		this.props.list(Object.assign({ ...searchForm }, { page, pageSize }));
		this.props.getCreateUidSelect();
	};

	// 列表分页
	handlePageChange = (page) => {
		this.setState(
			{
				page
			},
			() => {
				this.getList();
			}
		);
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState(
			{
				page: 1,
				pageSize: size
			},
			() => {
				this.getList();
			}
		);
	};

	//递归渲染
	_renderTreeNode = treeData => {
		return treeData.map(tree => {
			if (tree.children) {
				return (
						<TreeNode key={tree.id} title={tree.name} value={tree.id}>
							{this._renderTreeNode(tree.children)}
						</TreeNode>
				);
			} else {
				return <TreeNode key={tree.id} title={tree.name} value={tree.id} />;
			}
		});
	};

	// 搜索功能
	handleSearch = () => {
		this.setState(
			{
				page: 1
			},
			() => {
				this.getList();
			}
		);
	};

	// 重置搜索
	handleReset = () => {
		this.props.form.setFieldsValue({
			rangPicker: [undefined, undefined]
		});
		this.setState(
			{
				page: 1,
				searchForm: Object.assign({}, defaultSearchForm),
				startTime: undefined,
				endTime: undefined
			},
			() => {
				this.getList();
			}
		);
	};

	// 订单时间查询条件
	getDate = (date, dateString) => {
		if (dateString.length === 2) {
			this.setState({
				searchForm: { ...this.state.searchForm, createStartDate: dateString[0], createEndDate: dateString[1] },
				startTime: date[0],
				endTime: date[1]
			});
		}
	};

	//导出
	export() {
		const { searchForm } = this.state;
		let url = `${process.env.API_ROOT}/api/wm/valve/operation/exportValveOperationRecord`;
		http.export(url, searchForm, (res) => {
			const blob = new Blob([res]);
			const downloadElement = document.createElement('a');
			const href = window.URL.createObjectURL(blob);
			const fileName = '开关阀记录_' + new Date().getTime() + '.xlsx';
			downloadElement.href = href;
			downloadElement.download = fileName;
			document.body.appendChild(downloadElement);
			downloadElement.click();
			document.body.removeChild(downloadElement);
			window.URL.revokeObjectURL(href);
		});
	}

	// 渲染搜索
	_renderSearchForm = () => {
		const { searchForm, startTime, endTime } = this.state;
		const { createUidSelect ,areaSelect} = this.props;
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					<Col span={8}>
						<FormItem label={'用户编号:'}>
							<Input
								placeholder="请输入用户编号"
								value={searchForm.cno}
								onChange={(v) => {
									this.setState({
										searchForm: { ...searchForm, cno: v.target.value }
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'水表厂家:'}>
							<Select
								placeholder="请选择"
								value={searchForm.manufacturer}
								onChange={(v) => {
									this.setState({
										searchForm: { ...searchForm, manufacturer: v }
									});
								}}
							>
								{WATER_METER_MANUFACTURER.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'水表编号:'}>
							<Input
								placeholder="请输入水表编号"
								value={searchForm.waterMeterNo}
								onChange={(v) => {
									this.setState({
										searchForm: { ...searchForm, waterMeterNo: v.target.value }
									});
								}}
							/>
						</FormItem>
					</Col>

				</Row>

				<Row>
					{/* 所属片区 */}
					<Col span={8} >
						<FormItem label={'所属片区:'}>
							<TreeSelect
									style={{ width: '100%' }}
									dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
									value={this.state.searchForm.areaIdList}
									placeholder="请选择片区"
									allowClear
									showSearch
									multiple
									filterTreeNode={(inputValue, treeNode) => treeNode.props.title.indexOf(inputValue) > -1}
									treeDefaultExpandedKeys={[100]}
									onChange={(areaIdList) => {
										this.setState({ searchForm: { ...this.state.searchForm, areaIdList: areaIdList } });
									}}>
								{
									areaSelect && this._renderTreeNode(areaSelect)
								}
							</TreeSelect>
						</FormItem>
					</Col>

				</Row>

				<Row>
					<Col span={8}>
						<FormItem label={'开关阀时间:'}>
							<RangePicker value={[startTime, endTime]} onChange={this.getDate} placeholder={['开始时间', '结束时间']}/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'阀门动作:'}>
							<Select
								placeholder="请选择"
								value={searchForm.operationType}
								onChange={(v) => {
									this.setState({
										searchForm: { ...searchForm, operationType: v }
									});
								}}
							>
								{VALVE_OPERATION_TYPE.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'操作结果:'}>
							<Select
								placeholder="请选择"
								value={searchForm.operationResult}
								onChange={(v) => {
									this.setState({
										searchForm: { ...searchForm, operationResult: v }
									});
								}}
							>
								{VALVE_OPERATION_RESULT.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={8}>
						<FormItem label={'操作人:'}>
							<Select
								placeholder="请选择"
								value={searchForm.createUid}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, createUid: v }
									});
								}}
							>
								{createUidSelect.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'用户名称:'}>
							<Input
								placeholder="请输入用户名称"
								value={searchForm.customerName}
								onChange={v => {
									this.setState({
										searchForm: {
											...searchForm,
											customerName: v.target.value
										}
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'用户地址:'}>
							<Input
								placeholder="请输入用户地址"
								value={searchForm.customerAddress}
								onChange={v => {
									this.setState({
										searchForm: {
											...searchForm,
											customerAddress: v.target.value
										}
									});
								}}
							/>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={16} offset={8} align="right">
						<FormItem>
							<Button type="primary" onClick={this.handleSearch}>
								搜索
							</Button>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<Button type="default" onClick={this.handleReset}>
								重置
							</Button>
						</FormItem>
					</Col>
				</Row>
			</Form>
		);
	};

	render() {
		const { page, pageSize } = this.state;
		const { dataList, total } = this.props;
		const columns = [
			{
				title: '用户编号',
				dataIndex: 'cno',
				align: 'center'
			},
			{
				title: '用户名称',
				dataIndex: 'customerName',
				align: 'center'
			},
			{
				title: '用户地址',
				dataIndex: 'customerAddress',
				align: 'center',
				sorter: (a, b) => a.customerAddress && a.customerAddress.localeCompare(b.customerAddress)
			},
			{
				title: '水表厂家',
				dataIndex: 'waterMeterManufacturer',
				align: 'center'
			},
			{
				title: '水表编号',
				dataIndex: 'waterMeterNo',
				align: 'center'
			},
			{
				title: '开关阀时间',
				dataIndex: 'createTime',
				align: 'center'
			},
			{
				title: '阀门动作',
				dataIndex: 'operationType',
				align: 'center'
			},
			// {
			// 	title: '结果',
			// 	dataIndex: 'operationResult',
			// 	align: 'center'
			// },
			{
				title: '失败原因',
				dataIndex: 'reason',
				align: 'center'
			},
			{
				title: '操作员',
				dataIndex: 'createPersonName',
				align: 'center'
			},
			{
				title: '集中器号',
				dataIndex: 'concentratorNo',
				align: 'center'
			},
			{
				title: '水表地址',
				dataIndex: 'waterMeterAddress',
				align: 'center'
			}
		];
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: (total) => {
				return `共 ${total} 条数据 `;
			},
			onChange: (page) => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		return (
			<div className="shadow-radius on-off-record">
				<h1>开关阀记录</h1>
				{this._renderSearchForm()}
				<Row>
					<Button className="searchBtn" type="primary" onClick={() => this.export()}><Icon
						type="download"/>导出开关阀记录</Button>
				</Row>
				<Row className='main'>
					<Table
						bordered
						columns={columns}
						rowKey={() => Math.random()}
						dataSource={dataList}
						pagination={paginationProps}
					/>
				</Row>
			</div>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('onOffRecord');
	return {
		fields: data.fields,
		dataList: data.dataList, // 列表数据
		total: data.total, // 总条数
		areas: data.areas,  // 片区树
		createUidSelect: data.createUidSelect, // 创建人选择框
		visible: data.visible, // 是否显示弹窗
		detail: data.detail,// 获取详情
		areaSelect: data.areaSelect,  //区域

	};
};
const mapDispatchToProps = dispatch => ({
	setState: data => dispatch(actionCreators.setState(data)), // 设置redux值
	list: data => dispatch(actionCreators.list(data)), // 获取列表
	getAreaSelect: () => dispatch(actionCreators.getAreaSelect()), //获取片区选择框
	getCreateUidSelect: () => dispatch(actionCreators.getCreateUidSelect()) //获取订单创建人选择框

});
export default connect(
	mapStateToProps,
	mapDispatchToProps
)((Form.create())(Index));
