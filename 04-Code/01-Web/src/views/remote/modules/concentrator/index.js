import React, { Component } from 'react';
import { actionCreators } from './store';
import { connect } from 'react-redux';
import { Modal, Table, Form, Select, Row, Col, Input, Button, TreeSelect } from 'antd';
import { constants } from '$utils';
import { CONCENTRATOR_STATUS } from './constants';
import { WATER_METER_MANUFACTURER } from '@/constants/waterMeter';
import http from '$http';
import WaterMeter from './components/WaterMeter';
import './index.scss';

const FormItem = Form.Item;
const { Option } = Select;
const ButtonGroup = Button.Group;
const { TreeNode } = TreeSelect;

const defaultSearchForm = {
	no: '',
	location: '',
	status: null,
	manufacturer: null
};

class Index extends Component {

	constructor(props) {
		super(props);
		this.state = {
			expand: false, // 搜索项过多的时候，隐藏部分
			page: 1,
			pageSize: 10,
			searchForm: defaultSearchForm,
			modalVisible: false
		};
	}

	componentDidMount() {
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields && isTags) {
			this.setState({
				...fields.state
			}, () => {
				this.props.form.setFieldsValue({
					...fields.form
				});
			});
		} else {
			this.getList();
			this.props.getAreaSelect();
		}
	}

	componentWillUnmount() {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			});
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			});
		}
	}

	// 获取列表
	getList = () => {
		const { page, pageSize, searchForm } = this.state;
		this.props.list(Object.assign({ ...searchForm }, { page, pageSize }));
	};

	// 列表分页
	handlePageChange = (page) => {
		this.setState(
			{
				page
			},
			() => {
				this.getList();
			}
		);
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState(
			{
				page: 1,
				pageSize: size
			},
			() => {
				this.getList();
			}
		);
	};

	// 搜索功能
	handleSearch = () => {
		this.setState(
			{
				page: 1
			},
			() => {
				this.getList();
			}
		);
	};

	// 重置搜索
	handleReset = () => {
		this.setState(
			{
				searchForm: Object.assign({}, defaultSearchForm)
			},
			() => {
				this.getList();
			}
		);
	};

	// 查看水表列表
	handleView = (record) => {
		this.props.listWaterMeter(record.id);
	};

	// 取消弹窗
	handleCancel = () => {
		this.props.setState({ key: 'visible', value: false });
	};

	// 集中器抄表
	handleTranscribe = (record) => {
		this.props.transcribe(record.id, this.getList);
	};

	//递归渲染
	_renderTreeNode = treeData => {
		return treeData.map(tree => {
			if (tree.children) {
				return (
					<TreeNode key={tree.id} title={tree.name} value={tree.id}>
						{this._renderTreeNode(tree.children)}
					</TreeNode>
				);
			} else {
				return <TreeNode key={tree.id} title={tree.name} value={tree.id}/>;
			}
		});
	};

	// 渲染搜索
	_renderSearchForm = () => {
		const {searchForm }=this.state
		const { areaSelect} = this.props;
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					<Col span={8}>
						<FormItem label={'集中器厂家:'}>
							<Select
								placeholder="请选择"
								value={searchForm.manufacturer}
								onChange={(v) => {
									this.setState({
										searchForm: { ...searchForm, manufacturer: v }
									});
								}}
							>
								{WATER_METER_MANUFACTURER.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'集中器编号:'}>
							<Input
								placeholder="请输入集中器编号"
								value={searchForm.no}
								onChange={(v) => {
									this.setState({
										searchForm: { ...searchForm, no: v.target.value }
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'集中器位置:'}>
							<Input
								placeholder="请输入集中器位置"
								value={searchForm.location}
								onChange={(v) => {
									this.setState({
										searchForm: { ...searchForm, location: v.target.value }
									});
								}}
							/>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={8}>
						<FormItem label={'集中器状态:'}>
							<Select
								placeholder="请选择"
								value={searchForm.status}
								onChange={(v) => {
									this.setState({
										searchForm: { ...searchForm, status: v }
									});
								}}
							>
								{CONCENTRATOR_STATUS.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'片区:'}>
							<TreeSelect
								style={{ width: '100%' }} dropdownStyle={{ maxHeight: 400, overflow: 'auto' }} placeholder="请选择片区"
								allowClear
								treeDefaultExpandedKeys={[100]}
								value={searchForm.areaId}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, areaId: v } });
								}}>
								{this._renderTreeNode(areaSelect)}
							</TreeSelect>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={16} offset={8} align="right">
						<FormItem>
							<Button type="primary" onClick={this.handleSearch}>
								搜索
							</Button>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<Button type="default" onClick={this.handleReset}>
								重置
							</Button>
						</FormItem>
					</Col>
				</Row>
			</Form>
		);
	};

	// 渲染弹出框
	_renderModal = () => {
		const { visible, waterMeterList } = this.props;
		return (
			<Modal className="concentrator-modal" title="水表列表" visible={visible} onCancel={this.handleCancel} footer={null}>
				<WaterMeter waterMeterList={waterMeterList}/>
			</Modal>
		);
	};

		exportConcentrator = () => {
				const { searchForm } = this.state;
				const url = `${process.env.API_ROOT}/api/wm/concentrator/exportConcentrator`;
				http.export(url, searchForm, (res) => {
						const blob = new Blob([res]);
						const downloadElement = document.createElement('a');
						const href = window.URL.createObjectURL(blob);
						const fileName = '集中器_' + new Date().getTime() + '.xlsx';
						downloadElement.href = href;
						downloadElement.download = fileName;
						document.body.appendChild(downloadElement);
						downloadElement.click();
						document.body.removeChild(downloadElement);
						window.URL.revokeObjectURL(href);
				});
		}

	render() {
		const { page, pageSize } = this.state;
		const { dataList, total, visible } = this.props;
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: (total) => {
				return `共 ${total} 条数据 `;
			},
			onChange: (page) => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		const columns=[
			{
				title: '序号',
				dataIndex: 'xuhao',
				align: 'center',
				render: (text, record, index) => {
					return index + 1;
				}
			},
			{
				title: '集中器厂家',
				dataIndex: 'manufacturer',
				align: 'center'
			},
			{
				title: '集中器编号',
				dataIndex: 'no',
				align: 'center'
			},
			{
				title: '集中器位置',
				dataIndex: 'location',
				align: 'center'
			},
			{
				title: '挂表数量',
				dataIndex: 'meterAmount',
				align: 'center'
			},
			{
				title: '集中器状态',
				dataIndex: 'status',
				align: 'center'
			},
			{
				title: '更新时间',
				dataIndex: 'updateTime',
				align: 'center'
			},
			{
				title: '操作',
				key: 'operation',
				align: 'center',
				render: (text, record, index) => {
					return (
						<span>
								<ButtonGroup>
									<Button
										title="查看"
										className="btn"
										type="primary"
										size="small"
										icon="eye"
										onClick={() => this.handleView(record)}
									/>
									<Button
										title="批量抄表"
										className="btn"
										type="primary"
										size="small"
										icon="copy"
										onClick={() => this.handleTranscribe(record)}
									/>
								</ButtonGroup>
							</span>
					);
				}
			}
		]
		return (
			<div className="shadow-radius concentrator">
				<h1>集中器管理</h1>
				{this._renderSearchForm()}
					<Button type="primary" style={{ marginLeft: 8 }} onClick={this.exportConcentrator}>
							导出集中器
					</Button>
				<Row className="main">
					<Table
						bordered
						columns={columns}
						rowKey={() => Math.random()}
						dataSource={dataList}
						pagination={paginationProps}
					/>
				</Row>
				{visible && this._renderModal()}
			</div>
		);
	}
}

const mapStateToProps = (state) => {
	const data = state.get('concentrator');
	return {
		fields: data.fields,
		dataList: data.dataList, // 列表数据
		total: data.total, // 总条数
		visible: data.visible, // 是否显示弹窗
		detail: data.detail, // 获取详情
		waterMeterList: data.waterMeterList, // 水表列表
		areaSelect: data.areaSelect, // 片区选择框
	};
};
const mapDispatchToProps = (dispatch) => ({
	setState: (data) => dispatch(actionCreators.setState(data)), // 设置redux值
	list: (data) => dispatch(actionCreators.list(data)), // 获取列表
	listWaterMeter: (data) => dispatch(actionCreators.listWaterMeter(data)), // 获取详水表列表
	transcribe: (data, list) => dispatch(actionCreators.transcribe(data, list)), // 集中器抄表
	getAreaSelect: () => dispatch(actionCreators.getAreaSelect()), //获取片区选择框
});
export default connect(mapStateToProps, mapDispatchToProps)(Form.create()(Index));
