import React, {Component} from 'react';
import {actionCreators} from './store';
import {connect} from 'react-redux';
import './index.scss';
import {
	<PERSON><PERSON>,
	Col,
	DatePicker,
	Form,
	Input,
	PageHeader,
	Row,
	Select
} from "antd";
import {constants, excelExport} from '$utils';
import {VALVE_STATUES} from "../../../../constants/waterMeter";

const InputGroup = Input.Group;
const FormItem = Form.Item;
const {Option} = Select;
const {RangePicker} = DatePicker
const defaultSearchForm = {
	startTime: undefined,
	endTime: undefined,
	useHno: false,
	waterAmountMin: undefined,
	waterAmountMax: undefined,
	valves: []
};

class Index extends Component {

	constructor(props) {
		super(props);
		this.state = {
			searchForm: defaultSearchForm,
			title: '用水量统计',

		};
	}

	openExport = () => {
		const {searchForm} = this.state;
		excelExport("api/pageReports/exportCustomerWaterAmount", "用水量统计",
			searchForm);
	}

	renderSearchForm = () => {
		const {searchForm} = this.state;
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					<Col span={8}>
						<FormItem label={'查询时间:'}>
							<RangePicker style={{width: '100%'}}
													 value={[searchForm.startTime, searchForm.endTime]}
													 onChange={([startTime, endTime]) => this.setState(
														 {searchForm: {...searchForm, startTime, endTime}})}
													 placeholder={['开始时间', '结束时间']}/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'查询方式:'}>
							<Select
								showSearch
								placeholder="请选择"
								value={searchForm.useHno}
								optionFilterProp={'label'}
								onChange={useHno => this.setState(
									{searchForm: {...searchForm, useHno}})}
							>
								<Option label='按用户编号' value={false}>按用户编号</Option>
								<Option label='按用户户号' value={true}>按用户户号</Option>
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'查询水量:'}>
							<InputGroup compact>
								<Input style={{width: '45%', textAlign: 'center'}}
											 placeholder="起始"
											 value={searchForm.waterAmountMin}
											 onChange={v => this.setState({
												 searchForm: {
													 ...searchForm,
													 waterAmountMin: v.target.value
												 }
											 })}/>
								<Input style={{
									width: '10%',
									borderLeft: 0,
									pointerEvents: 'none',
									backgroundColor: '#fff'
								}} placeholder="~" disabled/>
								<Input
									style={{width: '45%', textAlign: 'center', borderLeft: 0}}
									placeholder="结束"
									value={searchForm.waterAmountMax}
									onChange={v => this.setState({
										searchForm: {
											...searchForm,
											waterAmountMax: v.target.value
										}
									})}/>
							</InputGroup>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'有无阀门:'}>
							<Select
								showSearch
								mode="multiple"
								allowClear={true}
								placeholder="请选择"
								value={searchForm.valves}
								optionFilterProp={'label'}
								onChange={valves => this.setState(
									{searchForm: {...searchForm, valves}})}
							>
								{
									VALVE_STATUES.map(({label, value}) => <Option label={label} value={value}>{label}</Option>)
								}
							</Select>
						</FormItem>
					</Col>
				</Row>
			</Form>
		);
	};

	renderOperationButton = () => {
		return (
			<Col>
				<Button type="primary" onClick={() => this.openExport()}>
					导出
				</Button> &nbsp;

			</Col>
		);
	};

	render() {

		return (
			<div className="shadow-radius template">
				<PageHeader title="用水量统计"/>
				<Row>{this.renderSearchForm()}</Row>
				<Row>{this.renderOperationButton()}</Row>
			</div>
		);
	}
}

const mapStateToProps = (state) => {

	return {};
};
const mapDispatchToProps = (dispatch) => ({
	list: (data, action) => dispatch(actionCreators.list(data, action)), // 获取列表
	exportExcel: data => actionCreators.exportExcel(data), // 导出
});
export default connect(mapStateToProps, mapDispatchToProps)(Index);
