import * as actionTypes from './constants';

const defaultState = {
	operators: [], // 操作员
	dataList: [],
};

// 重要提示： 请做好各代码段的注释！！！
export default (state = defaultState, action) => {
	switch (action.type) {
		case actionTypes.GET_OPERATOR_SELECT:
			state.operators = action.data;
			return {...state};

		case actionTypes.LIST_RECORD:
			state.dataList = action.data;
			return {...state};
		default:
			return state;
	}
}
