import React, { Component } from 'react';

import http from '$http';
import { constants } from '$utils';
import {
	Button,
	Col,
	DatePicker,
	Form,
	Row,
	Select,
	TreeSelect,
} from 'antd';
import moment from 'moment';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;


class WaterInfo extends Component {

	constructor(props) {
		super(props);
		this.state = {};
	}



	// 重置搜索
	handleReset = () => {
		this.props.form.resetFields();
	};

	//渲染搜索
	_renderSearchForm = () => {
		const { areas, createUidSelect } = this.props;
		const { getFieldDecorator } = this.props.form;
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					<Col span={8}>
						<FormItem label={'日期范围:'}>
							{getFieldDecorator('correspondenceDatePicker', {
								initialValue: '',
								rules: [{ required: true, message: '请选择日期' }]
							})(
								<RangePicker placeholder={['开始时间', '结束时间']} />
							)}
						</FormItem>
					</Col>
					<Col span={24} align="right">
						<Button type="default" onClick={this.handleReset}>
							重置
						</Button>
					</Col>
				</Row>
			</Form>
		);
	};

	//导出柜台收费报表
	exportChargeOrderChecklist () {
		this.props.form.validateFields((err, values) => {
			if (!err) {
				let param = {};
				param.startTime = moment(values.correspondenceDatePicker[0]).format('YYYY-MM-DD');
				param.endTime = moment(values.correspondenceDatePicker[1]).format('YYYY-MM-DD');
				let url = `${process.env.API_ROOT}/api/statistics/exportWaterInformation`;
				http.export(url, param, (res) => {
					const blob = new Blob([res]);
					const downloadElement = document.createElement('a');
					const href = window.URL.createObjectURL(blob);
					const fileName = '水费信息' + '.xlsx';
					downloadElement.href = href;
					downloadElement.download = fileName;
					document.body.appendChild(downloadElement);
					downloadElement.click();
					document.body.removeChild(downloadElement);
					window.URL.revokeObjectURL(href);
				});
			}
		});
	}

	render () {
		return (
			<div className="shadow-radius bill">
				<h1>水费信息</h1>
				<Row>{this._renderSearchForm()}</Row>
				<Row>
					<Button className="searchBtn" type="primary"
						onClick={() => this.exportChargeOrderChecklist()}>导出水费信息</Button>
				</Row>
			</div>
		);
	}
}


export default (Form.create()(WaterInfo));
