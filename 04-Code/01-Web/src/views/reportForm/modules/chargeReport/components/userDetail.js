import React, { Component } from 'react';
import http from '$http';
import { Form, Select, Row, Col, Button, DatePicker, TreeSelect, Input ,Message} from 'antd';

import { constants } from '$utils';
import moment from 'moment';
import { WATER_USE_KIND_TYPE } from '@/constants/waterUseKind';
import { CHARGE_WAY } from '@/constants/order';

const FormItem = Form.Item;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TreeNode } = TreeSelect;


class UserDetail extends Component {

	// 渲染片区树
	_renderTreeLoop = areas => {
		return areas.map(item => {
			if (item.children) {
				return (
					<TreeNode key={item.id} title={item.name} value={item.id}>
						{this._renderTreeLoop(item.children)}
					</TreeNode>
				);
			} else {
				return <TreeNode key={item.id} title={item.name} value={item.id}/>;
			}
		});
	};

	// 重置搜索
	handleReset = () => {
		this.props.form.resetFields();
	};

	//渲染搜索
	_renderSearchForm = () => {
		const { areas,createUidSelect} = this.props;
		const { getFieldDecorator } = this.props.form;
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					<Col span={8}>
						<FormItem label={'日期范围:'}>
							{getFieldDecorator('correspondenceDatePicker', {
								initialValue: '',
								rules: [{ required: true, message: '请选择日期' }]
							})(
								<RangePicker placeholder={['开始时间', '结束时间']}/>
							)}
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'片区:'}>
							{getFieldDecorator('areaId', {
								initialValue: '',
								rules: [{ required: true, message: '请选择片区' }]
							})(
								<TreeSelect
									style={{ width: '100%' }}
									showSearch
									filterTreeNode={(inputValue, treeNode) => treeNode.props.title.indexOf(inputValue) > -1}
									dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
									placeholder="请选择所属片区"
									allowClear
									treeDefaultExpandedKeys={[100]}
									onChange={this.areaOnChange}>
									{
										this._renderTreeLoop(areas)
									}
								</TreeSelect>
							)}
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'查询方式:'}>
							{getFieldDecorator('type', {
								initialValue: '',
								rules: [{ required: true, message: '请选择查询方式' }]
							})(
								<Select placeholder="请选择">
									<Option value={'0'}>应收</Option>
									<Option value={'1'}>欠费</Option>
									<Option value={'2'}>实收</Option>
								</Select>
							)}
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'用水性质:'}>
							{getFieldDecorator('waterUseKindType')(
								<Select placeholder="请选择">
									{WATER_USE_KIND_TYPE.map((item, index) => {
										return (
											<Option key={index} value={item.value}>
												{item.label}
											</Option>
										)
									})}
								</Select>
							)}
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'用户编号:'}>
							{getFieldDecorator('cno')(
								<Input placeholder="请输入用户编号"/>
							)}
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'用户户号:'}>
							{getFieldDecorator('userName')
							(<Input placeholder="请输入用户户号"/>)}
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'付款方式:'}>
							{getFieldDecorator('chargeWayList')(
								<Select
									mode="multiple"
									placeholder="请选择">
									{CHARGE_WAY.map((item, index) => {
										return (
											<Option key={index} value={item.value}>
												{item.label}
											</Option>
										);
									})}
								</Select>
							)}
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'创建人:'}>
							{getFieldDecorator('createUidList')(
								<Select placeholder="请选择" showSearch mode="multiple"
												filterOption={(inputValue, option) => option.props.children.indexOf(inputValue) > -1}>
									{createUidSelect.map((item, index) => {
										return (
											<Option key={index} value={item.value}>
												{item.label}
											</Option>
										);
									})}
								</Select>
							)}
						</FormItem>
					</Col>
					<Col span={24} align="right">
						<Button type="default" onClick={this.handleReset}>
							重置
						</Button>
					</Col>
				</Row>
			</Form>
		);
	};

	//导出用户明细
	chargeDetails() {
		this.props.form.validateFields((err, values) => {
			if (!err) {
				let param = {};
				param.startTime = moment(values.correspondenceDatePicker[0]).format('YYYY-MM-DD');
				param.endTime = moment(values.correspondenceDatePicker[1]).format('YYYY-MM-DD');
				param.type = values.type;
				param.areaId = values.areaId;
				param.waterUseKindType=values.waterUseKindType
				param.cno=values.cno
				param.userName=values.userName
				param.chargeWayList=values.chargeWayList
				param.createUidList=values.createUidList
				let type = values.type === '0' ? '应收' : values.type === '1' ? '欠费' : '实收';
				let url = `${process.env.API_ROOT}/api/chargeDetails/customer/chargeDetails`;
				http.export(url, param, (res) => {
					const blob = new Blob([res]);
					const downloadElement = document.createElement('a');
					const href = window.URL.createObjectURL(blob);
					const fileName = `用户明细(${type})报表` + '.xlsx';
					downloadElement.href = href;
					downloadElement.download = fileName;
					document.body.appendChild(downloadElement);
					downloadElement.click();
					document.body.removeChild(downloadElement);
					window.URL.revokeObjectURL(href);
				});
			}
		});
	}

	//导出用户明细
	chargeDetailsByPeriod() {
		this.props.form.validateFields((err, values) => {
			if (!err) {
				let param = {};
				param.startTime = moment(values.correspondenceDatePicker[0]).format('YYYY-MM-DD');
				param.endTime = moment(values.correspondenceDatePicker[1]).format('YYYY-MM-DD');
				param.type = values.type;
				param.areaId = values.areaId;
				param.waterUseKindType=values.waterUseKindType
				param.cno=values.cno
				param.userName=values.userName
				param.chargeWayList=values.chargeWayList
				param.createUidList=values.createUidList
				let type = values.type === '0' ? '应收' : values.type === '1' ? '欠费' : '实收';
				let url = `${process.env.API_ROOT}/api/chargeDetails/customer/chargeDetailsByPeriod`;
				http.export(url, param, (res) => {
					const blob = new Blob([res]);
					const downloadElement = document.createElement('a');
					const href = window.URL.createObjectURL(blob);
					const fileName = `用户明细—户(${type})报表` + '.xlsx';
					downloadElement.href = href;
					downloadElement.download = fileName;
					document.body.appendChild(downloadElement);
					downloadElement.click();
					document.body.removeChild(downloadElement);
					window.URL.revokeObjectURL(href);
				});
			}
		});
	}

	//导出用户明细
	inventoryDetails() {
		this.props.form.validateFields((err, values) => {
			if (!err) {
				let param = {};
				param.startTime = moment(values.correspondenceDatePicker[0]).format('YYYY-MM-DD');
				param.endTime = moment(values.correspondenceDatePicker[1]).format('YYYY-MM-DD');
				param.type = values.type;
				param.areaId = values.areaId;
				param.waterUseKindType=values.waterUseKindType
				param.cno=values.cno
				param.userName=values.userName
				param.chargeWayList=values.chargeWayList
				param.createUidList=values.createUidList
				let type = values.type === '0' ? '应收' : values.type === '1' ? '欠费' : '实收';
				let url = `${process.env.API_ROOT}/api/chargeDetails/customer/inventoryDetails`;
				http.export(url, param, (res) => {
					const blob = new Blob([res]);
					const downloadElement = document.createElement('a');
					const href = window.URL.createObjectURL(blob);
					const fileName = `用户清单(${type})报表` + '.xlsx';
					downloadElement.href = href;
					downloadElement.download = fileName;
					document.body.appendChild(downloadElement);
					downloadElement.click();
					document.body.removeChild(downloadElement);
					window.URL.revokeObjectURL(href);
				});
			}
		});
	}

	//导出用户明细
	inventoryDetailsByPeriod() {
		this.props.form.validateFields((err, values) => {
			if (!err) {
				let param = {};
				param.startTime = moment(values.correspondenceDatePicker[0]).format('YYYY-MM-DD');
				param.endTime = moment(values.correspondenceDatePicker[1]).format('YYYY-MM-DD');
				param.type = values.type;
				param.areaId = values.areaId;
				param.waterUseKindType=values.waterUseKindType
				param.cno=values.cno
				param.userName=values.userName
				param.chargeWayList=values.chargeWayList
				param.createUidList=values.createUidList
				let type = values.type === '0' ? '应收' : values.type === '1' ? '欠费' : '实收';
				let url = `${process.env.API_ROOT}/api/chargeDetails/customer/inventoryDetailsByPeriod`;
				http.export(url, param, (res) => {
					const blob = new Blob([res]);
					const downloadElement = document.createElement('a');
					const href = window.URL.createObjectURL(blob);
					const fileName = `用户清单—户(${type})报表` + '.xlsx';
					downloadElement.href = href;
					downloadElement.download = fileName;
					document.body.appendChild(downloadElement);
					downloadElement.click();
					document.body.removeChild(downloadElement);
					window.URL.revokeObjectURL(href);
				});
			}
		});
	}


	//导出用户明细
	exportCustomerOweDetails() {
		this.props.form.validateFields((err, values) => {
			if (!err) {
				let param = {};
				param.startTime = moment(values.correspondenceDatePicker[0]).format('YYYY-MM-DD');
				param.endTime = moment(values.correspondenceDatePicker[1]).format('YYYY-MM-DD');
				param.type = values.type;
				if(values.type!=1){
					Message.error('实时欠费报表只能查询欠费！');
					return;
				}
				param.areaId = values.areaId;
				param.waterUseKindType=values.waterUseKindType
				param.cno=values.cno
				param.userName=values.userName
				param.chargeWayList=values.chargeWayList
				param.createUidList=values.createUidList
				let url = `${process.env.API_ROOT}/api/chargeDetails/exportCustomerOweDetail`;
				http.export(url, param, (res) => {

						const blob = new Blob([res])
						const fileName = `用户实时欠费（户）` + '.xlsx';
						if ('download' in document.createElement('a')) {
								const downloadElement = document.createElement('a')
								const href = window.URL.createObjectURL(blob)
								downloadElement.href = href
								downloadElement.download = fileName
								document.body.appendChild(downloadElement)
								downloadElement.click()
								document.body.removeChild(downloadElement)
								window.URL.revokeObjectURL(href)
						} else {
								// for ie
								navigator.msSaveOrOpenBlob(blob, fileName)
						}
				});
			}
		});
	}

	render() {
		return (
			<div className="shadow-radius bill">
				<h1>用户明细</h1>
				<Row>{this._renderSearchForm()}</Row>
				<Row>
					<Button className="searchBtn" type="primary"
									onClick={() => this.chargeDetails()}>导出用户明细</Button>
					<Button className="searchBtn" type="primary"
									onClick={() => this.chargeDetailsByPeriod()}>导出用户明细—户</Button>
					<Button className="searchBtn" type="primary"
									onClick={() => this.inventoryDetails()}>导出用户清单</Button>
					<Button className="searchBtn" type="primary"
									onClick={() => this.inventoryDetailsByPeriod()}>导出用户清单—户</Button>
					<Button className="searchBtn" type="primary"
									onClick={() => this.exportCustomerOweDetails()}>用户实时欠费（户）</Button>
				</Row>
			</div>
		);
	}
}


export default (Form.create()(UserDetail));
