import React, {Component} from 'react';
import http from '$http';
import {Button, Col, DatePicker, Form, Row, Select, TreeSelect} from 'antd';
import {constants} from '$utils';
import moment from 'moment';

const FormItem = Form.Item;
const {Option} = Select;
const {RangePicker} = DatePicker;
const {TreeNode} = TreeSelect;

class IotIncomeStatistics extends Component {

	constructor(props) {
		super(props);
		this.state = {};
	}

	// 重置搜索
	handleReset = () => {
		this.props.form.resetFields();
	};

	//渲染搜索
	_renderSearchForm = () => {
		const {getFieldDecorator} = this.props.form;
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					<Col span={8}>
						<FormItem label={'日期范围:'}>
							{getFieldDecorator('correspondenceDatePicker', {
								initialValue: '',
								rules: [{required: true, message: '请选择日期'}]
							})(
								<RangePicker placeholder={['开始时间', '结束时间']}/>
							)}
						</FormItem>
					</Col>
					<Col span={24} align="right">
						<Button type="default" onClick={this.handleReset}>
							重置
						</Button>
					</Col>
				</Row>
			</Form>
		);
	};

	//导出结账日报
	exportIotIncome() {
		this.props.form.validateFields((err, values) => {
			if (!err) {
				let param = {};
				param.startTime = moment(values.correspondenceDatePicker[0]).format(
					'YYYY-MM-DD');
				param.endTime = moment(values.correspondenceDatePicker[1]).format(
					'YYYY-MM-DD');

				let url = `${process.env.API_ROOT}/api/statistics/exportIotIncome`;
				http.export(url, param, (res) => {
					const fileName = '物联表收入报表' + (+new Date()) + '.xlsx';
					const blob = new Blob([res]);
					if (window.navigator && window.navigator.msSaveOrOpenBlob) {
						window.navigator.msSaveOrOpenBlob(blob, fileName)
						return
					}
					const downloadElement = document.createElement('a');
					const href = window.URL.createObjectURL(blob);
					downloadElement.href = href;
					downloadElement.download = fileName;
					document.body.appendChild(downloadElement);
					downloadElement.click();
					document.body.removeChild(downloadElement);
					window.URL.revokeObjectURL(href);
				});
			}
		});
	}

	render() {
		return (
			<div className="shadow-radius bill">
				<h1>物联表收入报表</h1>
				<Row>{this._renderSearchForm()}</Row>
				<Row>
					<Button className="searchBtn" type="primary"
									onClick={() => this.exportIotIncome()}>物联表收入报表</Button>
				</Row>
			</div>
		);
	}
}

export default (Form.create()(IotIncomeStatistics));
