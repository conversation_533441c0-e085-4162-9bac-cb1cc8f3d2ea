import React, {Component} from 'react';
import http from '$http';
import {Button, Col, DatePicker, Form, Row, Select, TreeSelect} from 'antd';
import {constants} from '$utils';
import moment from 'moment';

const FormItem = Form.Item;
const {Option} = Select;
const {RangePicker} = DatePicker;
const {TreeNode} = TreeSelect;

class FixCompleteStatistics extends Component {

	constructor(props) {
		super(props);
		this.state = { isOpen: false };
	}

	// 重置搜索
	handleReset = () => {
		this.props.form.resetFields();
	};

	//渲染搜索
	_renderSearchForm = () => {
		const {getFieldDecorator, setFieldsValue} = this.props.form;
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					<Col span={8}>
						<FormItem label={'查询年度:'}>
							{getFieldDecorator('yearDatePicker', {
								initialValue: '',
								rules: [{required: true, message: '请选择日期'}]
							})(
								<DatePicker
									open={this.state.isOpen}
									onOpenChange={status => this.setState({isOpen: status})}
									onPanelChange={value => {
										setFieldsValue({yearDatePicker: value})
										this.setState({isOpen: false})
									}}
									mode="year"
									format='YYYY'
									placeholder={'请选择年份'}
								/>
							)}
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'查询季度:'}>
							{getFieldDecorator('seasonDatePicker', {
								initialValue: null,
								rules: [{required: true, message: '请选择季度'}]
							})(
								<Select showSearch placeholder="季度" showArrow>
									{[{value: 1, label: '第一季度'},
										{value: 2, label: '第二季度'},
										{value: 3, label: '第三季度'},
										{value: 4, label: '第四季度'}].map((item, index) => {
										return (
											<Option key={index} value={item.value}>
												{item.label}
											</Option>
										);
									})}
								</Select>
							)}
						</FormItem>
					</Col>
					<Col span={24} align="right">
						<Button type="default" onClick={this.handleReset}>
							重置
						</Button>
					</Col>
				</Row>
			</Form>
		);
	};

	//导出结账日报
	exportIotIncome() {
		this.props.form.validateFields((err, values) => {
			if (!err) {
				let param = {};

				const year = moment(values.yearDatePicker).format('YYYY')
				param.startTime = moment(`${year}-${values.seasonDatePicker * 3 - 2}`).startOf('month').format('YYYY-MM-DD')
				param.endTime = moment(`${year}-${values.seasonDatePicker * 3}`).endOf('month').format('YYYY-MM-DD')


				//console.log(param)
				let url = `${process.env.API_ROOT}/api/statistics/exportFixComplete`;
				http.export(url, param, (res) => {
					const fileName = '定额应收报表' + (+new Date()) + '.xlsx';
					const blob = new Blob([res]);
					if (window.navigator && window.navigator.msSaveOrOpenBlob) {
						window.navigator.msSaveOrOpenBlob(blob, fileName)
						return
					}
					const downloadElement = document.createElement('a');
					const href = window.URL.createObjectURL(blob);
					downloadElement.href = href;
					downloadElement.download = fileName;
					document.body.appendChild(downloadElement);
					downloadElement.click();
					document.body.removeChild(downloadElement);
					window.URL.revokeObjectURL(href);
				});
			}
		});
	}

	render() {
		return (
			<div className="shadow-radius bill">
				<h1>定额应收报表</h1>
				<Row>{this._renderSearchForm()}</Row>
				<Row>
					<Button className="searchBtn" type="primary"
									onClick={() => this.exportIotIncome()}>定额应收报表</Button>
				</Row>
			</div>
		);
	}
}

export default (Form.create()(FixCompleteStatistics));
