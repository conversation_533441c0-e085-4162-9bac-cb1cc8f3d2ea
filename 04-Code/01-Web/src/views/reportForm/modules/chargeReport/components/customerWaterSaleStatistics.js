import React, { Component } from 'react';

import http from '$http';
import { constants } from '$utils';
import { Button, Col, DatePicker, Form, Input, Row, Select, TreeSelect } from 'antd';
import moment from 'moment';

const FormItem = Form.Item;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TreeNode } = TreeSelect;

class CustomerWaterSaleStatistics extends Component {
	constructor(props) {
		super(props);
		this.state = {};
	}

	// 渲染片区树
	_renderTreeLoop = areas => {
		return areas.map(item => {
			if (item.children) {
				return (
					<TreeNode key={item.id} title={item.name} value={item.id}>
						{this._renderTreeLoop(item.children)}
					</TreeNode>
				);
			} else {
				return <TreeNode key={item.id} title={item.name} value={item.id} />;
			}
		});
	};

	// 重置搜索
	handleReset = () => {
		this.props.form.resetFields();
	};

	//渲染搜索
	_renderSearchForm = () => {
		const { getFieldDecorator } = this.props.form;
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					<Col span={8}>
						<FormItem label={'日期范围:'}>
							{getFieldDecorator('correspondenceDatePicker', {
								initialValue: '',
								rules: [{ required: false, message: '请选择日期' }]
							})(<RangePicker placeholder={['开始时间', '结束时间']} />)}
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'用户编号:'}>
							{getFieldDecorator('cno', {
								initialValue: '',
								rules: [
									{
										required: true,
										message: '用户名称必填'
									}
								]
							})(<Input placeholder="请输入用户编号" />)}
						</FormItem>
					</Col>
					<Col span={24} align="right">
						<Button type="default" onClick={this.handleReset}>
							重置
						</Button>
					</Col>
				</Row>
			</Form>
		);
	};

	//导出柜台收费报表
	exportCustomerWaterSaleStatistics() {
		this.props.form.validateFields((err, values) => {
			if (!err) {
				let param = {};
				if (values.correspondenceDatePicker[0] !== undefined) {
					param.startTime = moment(values.correspondenceDatePicker[0]).format('YYYY-MM-DD');
				}
				if (values.correspondenceDatePicker[1] !== undefined) {
					param.endTime = moment(values.correspondenceDatePicker[1]).format('YYYY-MM-DD');
				}
				param.cno = values.cno || '';
				const url = `${process.env.API_ROOT}/api/statistics/waterSaleStatistics`;
				http.export(url, param, res => {
					const fileName = '用户售水信息明细表' + '.xlsx';
					const blob = new Blob([res]);
					if (window.navigator && window.navigator.msSaveOrOpenBlob) {
						window.navigator.msSaveOrOpenBlob(blob, fileName);
					} else {
						const downloadElement = document.createElement('a');
						const href = window.URL.createObjectURL(blob);
						downloadElement.href = href;
						downloadElement.download = fileName;
						document.body.appendChild(downloadElement);
						downloadElement.click();
						document.body.removeChild(downloadElement);
						window.URL.revokeObjectURL(href);
					}
				});
			}
		});
	}

	render() {
		return (
			<div className="shadow-radius bill">
				<h1>用户售水信息明细表</h1>
				<Row>{this._renderSearchForm()}</Row>
				<Row>
					<Button className="searchBtn" type="primary" onClick={() => this.exportCustomerWaterSaleStatistics()}>
						导出用户售水信息明细表
					</Button>
				</Row>
			</div>
		);
	}
}

export default Form.create()(CustomerWaterSaleStatistics);
