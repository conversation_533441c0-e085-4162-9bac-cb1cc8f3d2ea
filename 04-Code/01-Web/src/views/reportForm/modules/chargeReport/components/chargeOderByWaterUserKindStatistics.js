import React, { Component } from 'react';
import http from '$http';
import { Form, Select, Row, Col, Button, DatePicker, TreeSelect } from 'antd';
import { constants } from '$utils';
import moment from 'moment';


const FormItem = Form.Item;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TreeNode } = TreeSelect;


class ChargeOderByWaterUserKindStatistics extends Component {

	constructor(props) {
		super(props);
		this.state = {};
	}

	// 渲染片区树
	_renderTreeLoop = areas => {
		return areas.map(item => {
			if (item.children) {
				return (
					<TreeNode key={item.id} title={item.name} value={item.id}>
						{this._renderTreeLoop(item.children)}
					</TreeNode>
				);
			} else {
				return <TreeNode key={item.id} title={item.name} value={item.id}/>;
			}
		});
	};

	// 重置搜索
	handleReset = () => {
		this.props.form.resetFields();
	};

	//渲染搜索
	_renderSearchForm = () => {
		const { areas, createUidSelect } = this.props;
		const { getFieldDecorator } = this.props.form;
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					<Col span={8}>
						<FormItem label={'日期范围:'}>
							{getFieldDecorator('correspondenceDatePicker', {
								initialValue: '',
								rules: [{ required: true, message: '请选择日期' }]
							})(
								<RangePicker placeholder={['开始时间', '结束时间']}/>
							)}
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'片区:'}>
							{getFieldDecorator('areaId', {
								initialValue: '',
								rules: [{ required: true, message: '请选择片区' }]
							})(
								<TreeSelect
									style={{ width: '100%' }}
									showSearch
									filterTreeNode={(inputValue, treeNode) => treeNode.props.title.indexOf(inputValue) > -1}
									dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
									placeholder="请选择所属片区"
									allowClear
									treeDefaultExpandedKeys={[100]}
									onChange={this.areaOnChange}>
									{
										this._renderTreeLoop(areas)
									}
								</TreeSelect>
							)}
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'收费员:'}>
							{getFieldDecorator('createUid')(
								<Select placeholder="请选择" showSearch
												filterOption={(inputValue, option) => option.props.children.indexOf(inputValue) > -1}>
									{createUidSelect.map((item, index) => {
										return (
											<Option key={index} value={item.value}>
												{item.label}
											</Option>
										);
									})}
								</Select>
							)}
						</FormItem>
					</Col>
					<Col span={24} align="right">
						<Button type="default" onClick={this.handleReset}>
							重置
						</Button>
					</Col>
				</Row>
			</Form>
		);
	};

	//导出柜台收费报表
	exportChargeOderByWaterUserKindStatistics() {
		this.props.form.validateFields((err, values) => {
			if (!err) {
				let param = {};
				param.startTime = moment(values.correspondenceDatePicker[0]).format('YYYY-MM-DD');
				param.endTime = moment(values.correspondenceDatePicker[1]).format('YYYY-MM-DD');
				param.areaId = values.areaId;
				param.createUid = values.createUid ? values.createUid : null;
				let url = `${process.env.API_ROOT}/api/statistics/charge/chargeOderByWaterUserKindNameStatistics`;
				http.export(url, param, (res) => {
					const blob = new Blob([res]);
					const fileName = '柜台收费报表' + '.xlsx';
					if (window.navigator && window.navigator.msSaveOrOpenBlob){
							window.navigator.msSaveOrOpenBlob(blob, fileName)
							return
					}
					const downloadElement = document.createElement('a');
					const href = window.URL.createObjectURL(blob);
					downloadElement.href = href;
					downloadElement.download = fileName;
					document.body.appendChild(downloadElement);
					downloadElement.click();
					document.body.removeChild(downloadElement);
					window.URL.revokeObjectURL(href);
				});
			}
		});
	}

	render() {
		return (
			<div className="shadow-radius bill">
				<h1>柜台收费报表</h1>
				<Row>{this._renderSearchForm()}</Row>
				<Row>
					<Button className="searchBtn" type="primary"
									onClick={() => this.exportChargeOderByWaterUserKindStatistics()}>导出柜台收费报表</Button>
				</Row>
			</div>
		);
	}
}


export default (Form.create()(ChargeOderByWaterUserKindStatistics));
