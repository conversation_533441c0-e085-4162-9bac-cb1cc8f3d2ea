import http from '$http';
import * as actionTypes from './constants';
import api from './api';

// 数据回填
const payload = (type, data) => ({ type, data });

// 获取片区列表
const areaList = () => {
	return async dispatch => {
		const res = await http.get(api.area);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_AREAS_RECORD, res.data));
		}
	};
};

// 获取订单创建人选择框
const getCreateUidSelect = () => {
	return async dispatch => {
		const res = await http.get(api.getCreateUidSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_CREATE_UID_SELECT, res.data));
		}
	};
};
// xx -不用reduce写法
const waterUseKind = data => {
	return http.restGet(api.waterusekind, data, false);
};

//获取用户等级
const customerLevelList = () => {
		return async dispatch => {
				const res = await http.get(api.customerLevelList);
				if (res.code === 0) {
						dispatch(payload(actionTypes.CUSTOMER_LEVEL_RECORD, res.data));
				}
		};
};


// 获取订单创建人选择框
const getTranscribers = () => {
	return async dispatch => {
		const res = await http.get(api.getTranscribers);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_THREE_TRANSCRIBER, res.data));
		}
	};
};

export { areaList,getCreateUidSelect,waterUseKind, getTranscribers,customerLevelList}
