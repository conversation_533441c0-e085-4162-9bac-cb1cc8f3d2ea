import http from '$http';
import * as actionTypes from './constants';
import { message } from 'antd';
import api from './api';

// 数据回填
const payload = (type, data) => ({ type, data });

// 组件操作redux数据状态
const setState = data => ({ type: actionTypes.SET_STATE, data });

// 获取列表
const list = data => {
	return async dispatch => {
		const res = await http.post(api.list, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.LIST_RECORD, res.data));
		}
	};
};

// 获取详情
const detail = data => {
	return async dispatch => {
		const res = await http.restGet(api.detail, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.DETAIL_RECORD, res.data));
		}
	};
};

// 新增记录
const add = (data, list) => {
	return async dispatch => {
		const res = await http.post(api.add, data);
		if (res.code === 0) {
			list();
			dispatch(payload(actionTypes.ADD_RECORD, res.data));
		}
	};
};

// 修改
const modify = (data, list) => {
	return async dispatch => {
		const res = await http.post(api.modify, data);
		if (res.code === 0) {
			list();
			dispatch(payload(actionTypes.MODIFY_RECORD, data));
		}
	};
};

// 删除
const del = (data, list) => {
	return async dispatch => {
		const res = await http.del(api.del, data);
		if (res.code === 0) {
			list();
			dispatch(payload(actionTypes.DEL_RECORD, data));
		}
	};
};

export { setState, list, detail, add, modify, del };
