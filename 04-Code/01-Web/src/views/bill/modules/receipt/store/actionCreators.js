import http from '$http';
import * as actionTypes from './constants';
import { message } from 'antd';
import api from './api';

// 数据回填
const payload = (type, data) => ({ type, data });

// 组件操作redux数据状态
const setState = data => ({ type: actionTypes.SET_STATE, data });
// 获取部门树
const getDepartmentTree = () => {
	return async dispatch => {
		const res = await http.get(api.getDepartmentTree);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_DEPARTMENT_TREE, res.data))
		}
	}
};
// 获取用户选择框
const getUserSeelct = data => {
	return async dispatch => {
		const res = await http.restGet(api.getUserSeelct, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_USER_SELECT, res.data))
		}
	}
};
// 新增
const add = (data) => {
	return async dispatch => {
		const res = await http.post(api.add, data);
		if (res.code === 0) {
			message.success('领用成功！');
		}
	}
};

export { setState, getDepartmentTree, getUserSeelct, add };
