import http from '$http';
import * as actionTypes from './constants';
import { message } from 'antd';
import api from './api';

// 数据回填
const payload = (type, data) => ({ type, data });

// 组件操作redux数据状态
const setState = data => ({ type: actionTypes.SET_STATE, data });

// 获取列表
const list = data => {
	return async dispatch => {
		const res = await http.post(api.list, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.LIST_RECORD, res.data))
		}
	}
};
// 获取缴费操作员选择框
const getPayUserSelect = () => {
	return async dispatch => {
		const res = await http.get(api.getPayUserSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_PAY_USER_SELECT, res.data))
		}
	}
}
// 获取操作员选择框
const getCreateUserSelect = () => {
	return async dispatch => {
		const res = await http.get(api.getCreateUserSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_CREATE_USER_SELECT, res.data))
		}
	}
}
// 作废
const invalid = (data, list) => {
	return async dispatch => {
		const res = await http.post(api.invalid + data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.INVALID, res.data))
		}
		list()
	}
}
export { setState, list, getPayUserSelect, getCreateUserSelect, invalid };
