import http from '$http'
import * as actionTypes from './constants';
import api from "./api";

// 数据回填
const payload = (type, data) => ({ type, data });

// 组件操作redux数据状态
const setState = data => ({ type: actionTypes.SET_STATE, data });


const list = (data) => {
		return http.post(api.list, data);
};

const getTotalStatics = (data) => {
		return http.post(api.getTotalStatics, data);
}

const getDetail = (data) => {
		return http.restGet(api.getDetail, data);
};
const invalid = (data) => {
		return http.restPost(api.invalid, data);
};

const listWaterMeter = () => {
		return http.get(api.listWaterMeter);
}

//获取发票模板
const getByType = (data) => {
		return http.restGet(api.getByType, data);
}

//获取发票模板
const getDetailByCustomer = (data,ops) => {
		return async dispatch => {
				const res = await http.restGet(api.getDetailByCustomer, data);
				if (res.code === 0) {
						dispatch(payload(actionTypes.GET_BY_CUSTOMER, res.data));
						ops();
				}
		};
};


const submitInvoice = (data,ops) => {
		return async dispatch => {
				const res = await http.post(api.submitInvoice, data);
				if (res.code === 0) {
						ops();
				}
		};
};

// 获取订单创建人选择框
const getCreateUidSelect = () => {
	return async dispatch => {
		const res = await http.get(api.getCreateUidSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_CREATE_UID_SELECT, res.data));
		}
	};
};

export {
		setState,
		list,
		getDetail,
		invalid,
		listWaterMeter,
		getByType,
		getTotalStatics,
		payload,
		getDetailByCustomer,
		submitInvoice,
		getCreateUidSelect
};
