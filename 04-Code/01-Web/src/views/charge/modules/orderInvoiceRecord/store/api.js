export default {
	list: `/api/invoice/print/record/listPageOrder`,
	detail: `/api/invoice/print/record/getDetailOrderById`,
	invalid: `/api/invoice/xsd/order/openRedInvoice`,
	getAreaSelect: `/api/sys/area/getTree`,
	getCreateName: `api/invoice/print/record/getCreateSelectOrder`,
	reSendSms: `api/invoice/print/record/reSendSms`,
	printSpecialInvovice: `api/invoice/nst/getNnSpecialData`,
	revokeRedConfirm: `/api/invoice/nst/revokeRedConfirm`,
	fastInvoiceRed: `/api/invoice/nst/fastInvoiceRed`,
	regeneratePdfUrl: `/api/invoice/xsd/order/getPdfUrl`,
};
