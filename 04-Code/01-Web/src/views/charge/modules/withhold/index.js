	import React, { Component } from 'react';
import http from '$http';
import { actionCreators } from './store';
import { connect } from 'react-redux';
import { Upload, message, Modal, TreeSelect, Button, Col, DatePicker, Form, Row, Table, Select, Tree } from 'antd';
import { constants } from '$utils';
import moment from 'moment';

const FormItem = Form.Item;
const { confirm } = Modal;
const { RangePicker } = DatePicker;
const { TreeNode } = TreeSelect;
const { Option } = Select

const defaultSearchForm = {
	createUid: '',
	status: null,
	startCreateTime: null,
	endCreateTime: null
};

class Index extends Component {

	constructor(props) {
		super(props);
		this.state = {
			page: 1,
			pageSize: 10,
			searchForm: defaultSearchForm
		};
	}

	componentDidMount() {
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields && isTags) {
			this.setState({
				...fields.state
			}, () => {
				this.props.form.setFieldsValue({
					...fields.form
				});
			});
		} else {
			this.getList();
			this.props.getAreaSelect();
			this.props.queryExportPerson();    //查询导出人
		}
	}

	componentWillUnmount() {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			});
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			});
		}
	}

	// 获取列表
	getList = () => {
		const { page, pageSize, searchForm } = this.state;
		this.props.list(Object.assign({ ...searchForm }, { page, pageSize }));
	};

	// 列表分页
	handlePageChange = (page) => {
		this.setState({
			page
		}, () => {
			this.getList();
		});
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState({
			page: 1,
			pageSize: size
		}, () => {
			this.getList();
		});
	};

	//递归渲染片区选项
	_renderTreeNode = treeData => {
		return treeData.map(tree => {
			if (tree.children) {
				return (
					<TreeNode key={tree.id} title={tree.name} value={tree.id}>
						{this._renderTreeNode(tree.children)}
					</TreeNode>
				);
			} else {
				return <TreeNode key={tree.id} title={tree.name} value={tree.id}/>;
			}
		});
	};

	//送盘导出
	handleExport = () => {
		this.props.form.validateFields((err, values) => {
			if (!err) {
				let areaIds = values.areaIds;
				let startTime = moment(values.date[0]).format('YYYY-MM-DD 00:00:00');
				let endTime = moment(values.date[1]).format('YYYY-MM-DD 23:59:59');
				let url = `${process.env.API_ROOT}/api/charge/chargeWithhold/export`;
				const params = { areaIds, startTime, endTime };
				http.export(url, params, (res) => {
						const blob = new Blob([res]);
						const downloadElement = document.createElement('a');
						const href = window.URL.createObjectURL(blob);
						const fileName = moment(values.date[0]).format('YYYY-MM-DD') + '_' + moment(values.date[1]).format('YYYY-MM-DD') + '_' + new Date().getTime() + '.txt';
						downloadElement.href = href;
						downloadElement.download = fileName;
						document.body.appendChild(downloadElement);
						this.getList()
						downloadElement.click();
						document.body.removeChild(downloadElement);
						window.URL.revokeObjectURL(href);
				});
			}
		});
	};

	// 下载
	handleDownload = record => {
		let param = {};
		param.id = record.id;
		let url = `${process.env.API_ROOT}/api/charge/chargeWithhold/download`;
		http.export(`${url}/${record.id}`, {}, (res) => {
			const blob = new Blob([res]);
			const downloadElement = document.createElement('a');
			const href = window.URL.createObjectURL(blob);
			const fileName = '记录' + new Date().getTime() + '.txt';
			downloadElement.href = href;
			downloadElement.download = fileName;
			document.body.appendChild(downloadElement);
			downloadElement.click();
			document.body.removeChild(downloadElement);
			window.URL.revokeObjectURL(href);
		});
	};

	//撤销送盘
	handleRevoke = record => {
		const _this = this;
		confirm({
			title: '操作确认',
			content: '确定要撤销当前记录吗？',
			okText: '确定',
			cancelText: '取消',
			onOk() {
				let params = {};
				params.id = record.id;
				_this.props.cancel(record.id, _this.getList);
			}
		});
	};

	//订单时间查询条件
	getOrderDate = (date, dateString) => {
		if (dateString.length === 2) {
			this.setState({
				searchForm: { ...this.state.searchForm,
					startCreateTime: moment(dateString[0]).format('YYYY-MM-DD 00:00:00'),
					endCreateTime:moment( dateString[1]).format('YYYY-MM-DD 23:59:59') }
			});
		}
	};

	// 搜索功能
	handleSearch = () => {
		this.setState({ page: 1 }, () => {
			this.getList();
		});
	};

	//重置搜索
	handleReset = () => {
		this.props.form.setFieldsValue({ rangPicker: [undefined, undefined] });
		this.setState({ page: 1, searchForm: Object.assign({}, defaultSearchForm) }, () => {
			this.getList();
		});
	};

	// 获取当前选项
	onCheck = (checkedKeys, info) => {
		this.props.form.setFieldsValue({areaIds:checkedKeys})
	};


	render() {
		const { page, pageSize,searchForm } = this.state;
		const { form, datalist, total, areaSelect,exportPersonList } = this.props;
		const { getFieldDecorator } = form;
		const columns = [
			{
				title: '导出日期',
				dataIndex: 'createTime',
				key: 'createTime',
				align: 'center'
			},
			{
				title: '总笔数',
				dataIndex: 'total',
				key: 'total',
				align: 'center'
			},
			{
				title: '总金额',
				dataIndex: 'amount',
				key: 'amount',
				align: 'center'
			},
			{
				title: '出账日期范围',
				dataIndex: 'exportTime',
				key: 'exportTime',
				align: 'center'
			},
			{
				title: '状态',
				dataIndex: 'status',
				key: 'status',
				align: 'center'
			},
			{
				title: '导出部门',
				dataIndex: 'createDepartmentName',
				key: 'createDepartmentName',
				align: 'center'
			},
			{
				title: '导出人',
				dataIndex: 'createPersonName',
				key: 'createPersonName',
				align: 'center'
			},
			{
				title: '操作',
				width: 100,
				align: 'center',
				render: (text, record, index) => {
					return (
						<div style={{ display: 'flex', justifyContent: 'center' }}>
							<Button title='下载' className='btn' type="primary" size="small" icon="download"
											onClick={() => this.handleDownload(record)}/>
							<Upload
								headers={{ token: localStorage.getItem('token') }}
								data={{id: record.id}}
								accept={`.xls,.xlsx`}
								action={`${process.env.API_ROOT}/api/charge/chargeWithhold/importExcel`}
								onChange={info => {
									if (info.file.status === 'done') {
										if (info.file.response.code === 0) {
											message.success(info.file.response.msg);
										} else {
											message.error(info.file.response.msg);
										}
									} else if (info.file.status === 'error') {
										message.error(info.file.response.msg);
									}
								}}>
								<Button title='回盘导入' type="primary" size={'small'} style={{ marginRight: '5px' }} icon='retweet'
												disabled={record.status==='送盘中'?false:true}/>
							</Upload>
							<Button title='撤销送盘' className='btn' type="danger" size="small" icon="close"
											onClick={() => this.handleRevoke(record)}
											disabled={record.status==='送盘中'?false:true}
							/>
						</div>
					);
				}
			}
		];
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: (total) => {
				return `共 ${total} 条数据 `;
			},
			onChange: (page) => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		return (
			<div className="shadow-radius">
				{/* 页面标题 */}
				<Row>
					<Col><h1>代扣</h1></Col>
				</Row>
				<Row>
					<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
						<Col span={8}>
							<FormItem label={'片区:'}>
								{
									getFieldDecorator('areaIds', { rules: [{ required: true, message: '必填' }] })
									/*(<TreeSelect style={{ width: '100%' }} dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
															 placeholder="请选择片区" allowClear treeDefaultExpandedKeys={[100]}
															 multiple>{areaSelect && this._renderTreeNode(areaSelect)}</TreeSelect>)*/
										(
											<Tree checkable autoExpandParent
														onCheck={this.onCheck}
											>
												{this._renderTreeNode(areaSelect)}</Tree>
										)
								}
							</FormItem>
						</Col>
						<Col span={8}>
							<FormItem label={'出账日期:'}>
								{
									getFieldDecorator('date', {
										rules: [{
											required: true,
											message: '必填'
										}]
									})(
										<RangePicker placeholder={['开始时间', '结束时间']} />
									)
								}
							</FormItem>
						</Col>
						<Col span={24} align="right">
							<Button className="searchBtn" type="primary" onClick={() => this.handleExport()}>送盘导出</Button>
						</Col>
					</Form>
				</Row>
				<Row>
					<Col><h1>搜索</h1></Col>
				</Row>
				<Row>
					<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
						<Row>
							<Col span={8}>
								<FormItem label={'导出日期:'}>
									{
										getFieldDecorator('rangPicker')(
											<RangePicker placeholder={['开始时间', '结束时间']} onChange={this.getOrderDate} />
										)
									}
								</FormItem>
							</Col>
							<Col span={8}>
								<FormItem label={'状态:'}>
									<Select placeholder="请选择状态"
										value={searchForm.status} onChange={v => {
											this.setState({ searchForm: { ...searchForm, status: v } });
										}}>
										<Option value={null}>请选择</Option>
										<Option value={0}>已完成</Option>
										<Option value={1}>送盘中</Option>
										<Option value={2}>撤销送盘</Option>
									</Select>
								</FormItem>
							</Col>
							<Col span={8}>
								<FormItem label={'导出人:'}>
									<Select placeholder="请选择导出人"
										value={searchForm.createUid} onChange={v => {
											this.setState({ searchForm: { ...searchForm, createUid: v } });
										}}>
										{exportPersonList && exportPersonList.map((item, index) => {
											return (
												<Option key={index} value={item.createUid}>
													{item.createPersonName}
												</Option>
											);
										})}
									</Select>
								</FormItem>
							</Col>
						</Row>
						<Col span={24} align="right">
							<Button type="primary" onClick={this.handleSearch}>
								搜索
							</Button>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<Button type="default" onClick={this.handleReset}>
								重置
							</Button>
						</Col>
					</Form>
				</Row>
				<Row className='main'>
					<h1>历史记录</h1>
					<Table
						bordered
						columns={columns}
						rowKey={() => Math.random()}
						dataSource={datalist}
						pagination={paginationProps}
					/>
				</Row>
			</div>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('withhold');
	return {
		fields: data.fields,
		datalist: data.datalist, // 列表数据
		total: data.total, // 总条数
		areaSelect: data.areaSelect, // 片区
		exportPersonList: data.exportPersonList
	};
};
const mapDispatchToProps = dispatch => ({
	setState: data => dispatch(actionCreators.setState(data)), // 设置redux值
	list: data => dispatch(actionCreators.list(data)), // 获取列表
	cancel: (data, list) => dispatch(actionCreators.cancel(data, list)), // 导出
	getAreaSelect: () => dispatch(actionCreators.getAreaSelect()), // 获取片区
	queryExportPerson: () => dispatch(actionCreators.queryExportPerson()) //查询导出人
});
export default connect(
	mapStateToProps,
	mapDispatchToProps
)((Form.create())(Index));
