import http from '$http';
import * as actionTypes from './constants';
import {message} from 'antd';
import api from './api';
import Decimal from "decimal.js";
import formatDate from "../../../../../utils/formatDate";
import getLodop from "../../../../../utils/LodopFuncs";
import digitalUppercase from "../../../../../utils/digitalUppercase";
import {Math as MathUtil} from "@/utils/math"

// 数据回填
const payload = (type, data) => ({ type, data });

// 组件操作redux数据状态
const setState = data => ({ type: actionTypes.SET_STATE, data });

// 获取片区选择框
const getAreaSelect = () => {
	return async dispatch => {
		const res = await http.get(api.getAreaSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_AREA_SELECT, res.data));
		}
	};
};
// 获取用户
const getCustomer = (data,fun) => {
	return async dispatch => {
		const res = await http.get(api.getCustomer + data.areaId.toString() + '/' + data.cno);
		if (res.code === 0) {
			if(fun){
				fun(res.data);
			}
			if (!!res.data) {
				if (res.data.waterMeterType === 'IC卡表') {
					dispatch(payload(actionTypes.GET_CUSTOMER, res.data));
				}
			} else {
				message.error("未找到用户")
			}
		}
	};
};

// 获取用水性质选择框
const getWaterUseKindSelect = () => {
	return async dispatch => {
		const res = await http.get(api.getWaterUseKindSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_WATER_USE_KIND_SELECT, res.data));
		}
	};
};
// 获取用水分类单价
const getUnitPrice = (data) => {
	return async dispatch => {
		const res = await http.restGet(api.getUnitPrice, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_UNIT_PRICE, res.data));
		}
	};
};
// 计算收费金额
const getChargeFee = data => {
	return async dispatch => {
		const res = await http.post(api.getChargeFee, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_CHARGE_FEE, res.data));
		}
	};
};
// 添加特抄收费
const addSpecialFee = data => {
	return async dispatch => {
		const res = await http.post(api.addSpecialFee, data);
		if (res.code === 0) {
			message.success(res.msg);
			if (data.invoice === 1) {
				await printInvoice(res.data)
			}
			//dispatch(payload(actionTypes.ADD_SPECIAL_FEE, res.data));
		}
	};
};

const printInvoice = async (data) => {
	if (data == null || data.id == null) {
		return
	}
	const detail = await http.restGet(api.detail, data.id);
	if (detail.code !== 0) {
		message.error('打印失败')
		return
	}
	const { customerName, cno,
		address, createPersonName,
		chargeDetailList,
		specialFeeDetailList } = detail.data
	const { waterUseKindName, waterUseKind: { ladderType, waterUseKindDetailList } } = specialFeeDetailList[0]
	// 单价
	const { cleanWaterFee, waterResourceFee, sewageFee } = waterUseKindDetailList[0]
	// 各水费合计金额
	const { waterAmount, cleanFeeSum, sewageFeeSum, resourceFeeSum } = chargeDetailList.reduce((total, item) => {
		const { waterAmount, cleanFeeSum, sewageFeeSum, resourceFeeSum } = total
		total.waterAmount = new Decimal(waterAmount).add(new Decimal(item.waterAmount))
		total.cleanFeeSum = new Decimal(cleanFeeSum).add(new Decimal(item.cleanWaterFee))
		total.sewageFeeSum = new Decimal(sewageFeeSum).add(new Decimal(item.sewageFee))
		total.resourceFeeSum = new Decimal(resourceFeeSum).add(new Decimal(item.waterResourceFee))
		return total
	}, { waterAmount: new Decimal(0), cleanFeeSum: new Decimal(0), sewageFeeSum: new Decimal(0), resourceFeeSum: new Decimal(0) })
	// 清水总额 + 水资源总额
	const totalAmount = new Decimal(cleanFeeSum).add(new Decimal(resourceFeeSum)).add(new Decimal(sewageFeeSum))
	const yyyy = formatDate(new Date(), 'yyyy')
	const MM = formatDate(new Date(), 'MM')
	const dd = formatDate(new Date(), 'dd')
	let result = null
	let LODOP = getLodop()
	if (ladderType !== '非阶梯') {
		// 阶梯小票
		const orderTemplate = await http.restGet(api.getByType, 12)
		const priceData = waterUseKindDetailList.map(({ ladderLevel, cleanWaterFee }) => ({
			[`ladder${ladderLevel}Count`]: new Decimal(0),
			[`ladder${ladderLevel}Price`]: cleanWaterFee,
			[`ladder${ladderLevel}Amount`]: new Decimal(0)
		})).reduce((total, item) => ({ ...total, ...item }), {})

		chargeDetailList.forEach(({ ladderLevel, waterAmount, cleanWaterFee }) => {
			priceData[`ladder${ladderLevel}Count`] = waterAmount
			priceData[`ladder${ladderLevel}Amount`] = cleanWaterFee
		})
		const param = {
			customerName,
			customerAddress: address,
			cardNo: cno,
			meterAmount: cleanFeeSum,
			totalAmount: totalAmount,
			...priceData,
			resourceCount: MathUtil.comparedTo(resourceFeeSum, new Decimal(0)) > 0 ? waterAmount : 0,
			resourcePrice: waterResourceFee,
			resourceAmount: resourceFeeSum,
			sewageCount: MathUtil.comparedTo(sewageFeeSum,new Decimal(0) )>0?waterAmount:0,
			sewagePrice:sewageFee,
			sewageAmount:sewageFeeSum,
			amountBig: digitalUppercase(totalAmount),
			tollName: createPersonName,
			yyyy, MM, dd
		}
		const template = Object.entries(param)
			.map(([k, v]) => ({ key: k, value: v == null ? '' : v }))
			.reduce((total, { key, value }) => total.replace(key, value), orderTemplate.data.content)
		eval(template)
		result = LODOP.PREVIEW()
		// result = LODOP.PRINT()
	} else {
		// 非阶梯小票
		const invoiceSource = 11
		const orderTemplate = await http.restGet(api.getByType, invoiceSource)
		const param = {
			customerName,
			cardNo: cno,
			waterUseKindName,
			waterAmount,
			amountSmall: totalAmount,
			cleanPrice: cleanWaterFee,
			resourcePrice: waterResourceFee,
			cleanAmount: cleanFeeSum,
			resourceAmount: resourceFeeSum,
			amountBig: digitalUppercase(totalAmount),
			customerAddress: address,
			tollName: createPersonName,
			sewageCount: MathUtil.comparedTo(sewageFeeSum,new Decimal(0) )>0?waterAmount:0,
			sewagePrice:sewageFee,
			sewageAmount:sewageFeeSum,
			yyyy, MM, dd
		}
		const template = Object.entries(param)
			.map(([k, v]) => ({ key: k, value: v == null ? '' : v }))
			.reduce((total, { key, value }) => total.replace(key, value), orderTemplate.data.content)
		eval(template)
		result = LODOP.PREVIEW()
		// result = LODOP.PRINT()
	}

	// if (MathUtil.comparedTo(sewageFeeSum, new Decimal(0)) > 0) {
	// 	const sewageTemplate = await http.restGet(api.getByType, 10)
	// 	const param = {
	// 		customerName,
	// 		cardNo: cno,
	// 		waterUseKindName,
	// 		waterAmount,
	// 		price: sewageFee,
	// 		amount: sewageFeeSum,
	// 		amountBig: digitalUppercase(sewageFeeSum),
	// 		amountSmall: sewageFeeSum,
	// 		customerAddress: address,
	// 		yyyy, MM, dd,
	// 		tollName: createPersonName
	// 	}
	// 	const template = Object.entries(param)
	// 		.map(([k, v]) => ({ key: k, value: v == null ? '' : v }))
	// 		.reduce((total, { key, value }) => total.replace(key, value), sewageTemplate.data.content)
	// 	eval(template)
	// 	result = LODOP.PREVIEW()
	// 	// result = LODOP.PRINT()
	// }

	if (result) {
		message.success('打印成功！');
	}
}
export { setState, getAreaSelect, getCustomer, getWaterUseKindSelect, getUnitPrice, getChargeFee, addSpecialFee };
