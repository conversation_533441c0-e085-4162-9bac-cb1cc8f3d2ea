import http from '$http';
import * as actionTypes from './constants';
import { message } from 'antd';
import api from './api';

// 数据回填
const payload = (type, data) => ({ type, data });

// 组件操作redux数据状态
const setState = data => ({ type: actionTypes.SET_STATE, data });

// 获取片区选择框
const getAreaSelect = () => {
	return async dispatch => {
		const res = await http.get(api.getAreaSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_AREA_SELECT, res.data));
		}
	};
};
// 获取用户
const getCustomer = data => {
	return async dispatch => {
		const res = await http.get(api.getCustomer + data.areaId.toString() + '/' + data.cno);
		if (res.code === 0) {
			if (!!res.data) {
				dispatch(payload(actionTypes.GET_CUSTOMER, res.data));
			} else {
				message.error("未找到用户")
			}
		}
	};
};
// 获取用水分类选择框
const getWaterUseKindSelect = () => {
	return async dispatch => {
		const res = await http.get(api.getWaterUseKindSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_WATER_USE_KIND_SELECT, res.data));
		}
	};
};
// 获取用水性质单价
const getUnitPrice = (data) => {
	return async dispatch => {
		const res = await http.restGet(api.getUnitPrice, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_UNIT_PRICE, res.data));
		}
	};
};
// 计算收费金额
const getChargeFee = data => {
	return async dispatch => {
		const res = await http.post(api.getChargeFee, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_CHARGE_FEE, res.data));
		}
	};
};

// 添加特抄收费
const addSpecialFee = data => {
	return async dispatch => {
		const res = await http.post(api.addSpecialFee, data);
		if (res.code === 0) {
			message.success(res.msg);
			dispatch(payload(actionTypes.ADD_SPECIAL_FEE, res.data));
		}
	};
};
export { setState, getAreaSelect, getCustomer, getWaterUseKindSelect, getUnitPrice, getChargeFee, addSpecialFee };
