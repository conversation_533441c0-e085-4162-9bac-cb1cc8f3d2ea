import http from '$http';
import {message} from 'antd';
import moment from 'moment';

import {
	huaxuRead,
	huaxuWriteCard,
	readCard,
	rechargeXTMF1Card,
	sellConfirm,
	xtWriteCard,
} from '@/utils/cardUtil';

import getLodop from '../../../../../utils/LodopFuncs';
import api from './api';
import * as actionTypes from './constants';

// 数据回填
const payload = (type, data) => ({ type, data });

// 组件操作redux数据状态
const setState = data => ({ type: actionTypes.SET_STATE, data });

// 获取列表
const list = data => {
	return async dispatch => {
		const res = await http.post(api.list, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.LIST_RECORD, res.data));
		}
	};
};

// 缴费
const pay = (data, endLoad, refresh, invoice, newPriceDetail) => {
	return async dispatch => {
		const res = await http.post(api.pay, data);
		if (res.code === 0) {
			let resultData = res.data;
			let flag = false
			if (resultData.waterMeterType === 'IC卡表') {
				let water = 0; //本次
				let totale = 0; //累计
				let zjsj1 = 0;
				let zjsj2 = 0;
				let zjsj3 = 0;
				let sl1 = 0;
				let sl2 = 0;
				if (resultData.waterMeterManufacturer === '扬州恒信') {
					let getAreaCode = readCard();
					let accumulationBuyCount = Number(resultData.accumulationBuyCount) - 1; //次数
					let areaCode = ''; //区域码
					if (resultData.waterMeterKindType === '预付费2') {
						water = resultData.waterAmount;
						areaCode = getAreaCode.substring(12, 18);
					} else if (resultData.waterMeterKindType === '预付费5') {
						totale = resultData.accumulationAmount;
					} else if (resultData.waterMeterKindType === '阶梯2') {
						water = resultData.writeCardAmount;
						accumulationBuyCount = 0;
						areaCode = getAreaCode.substring(12, 18);
						if (resultData.ladderType === '非阶梯') {
							zjsj1 = resultData.price1;
							zjsj2 = resultData.price1;
							zjsj3 = resultData.price1;
							sl1 = 25;
							sl2 = 0;
						} else {
							zjsj1 = resultData.price1 ? resultData.price1 : 0;
							zjsj2 = resultData.price2 ? resultData.price2 : 0;
							zjsj3 = resultData.price3 ? resultData.price3 : 0;
							sl1 = resultData.ladder1 ? resultData.ladder1 : 0;
							sl2 = resultData.ladder2 ? resultData.ladder2 : 0;
						}
					} else if (resultData.waterMeterKindType === '阶梯5') {
						totale = resultData.accumulationBuyAmount;
						accumulationBuyCount = 0;
						if (resultData.ladderType === '非阶梯') {
							zjsj1 = resultData.price1;
							zjsj2 = resultData.price1;
							zjsj3 = resultData.price1;
							sl1 = 25;
							sl2 = 0;
						} else {
							zjsj1 = resultData.price1 ? resultData.price1 : 0;
							zjsj2 = resultData.price2 ? resultData.price2 : 0;
							zjsj3 = resultData.price3 ? resultData.price3 : 0;
							sl1 = resultData.ladder1 ? resultData.ladder1 : 0;
							sl2 = resultData.ladder2 ? resultData.ladder2 : 0;
						}
					} else if (resultData.waterMeterKindType === '预付费4442') {
						water = resultData.waterAmount;
						areaCode = getAreaCode.substring(10, 14);
					}

					let result = sellConfirm(resultData.waterMeterKindType, resultData.cardNo, accumulationBuyCount, water, totale, zjsj1, zjsj2, zjsj3, sl1, sl2, areaCode);
					if (result === 0) {
						flag = true
						resultData.customerId = data.customerId;
						dispatch(updateStatusCompleted(resultData, invoice, endLoad, refresh, newPriceDetail));
						//message.success('缴费成功！');
						dispatch(payload(actionTypes.PAY_MONEY, res.data));
					}
				} else if (resultData.waterMeterManufacturer === '深圳华旭') {
					let resultHuaxu = huaxuRead();
					// let accumulationBuyCount = Number(resultData.accumulationBuyCount) - 1;
					let accumulationBuyCount = resultData.accumulationBuyCount;
					// 9,20011934,01,FFFFFF10000012,0831,
					let cardNo;
					let waterMeterNo = resultHuaxu[1];
					if (resultHuaxu[0] == 9) {
						cardNo = resultHuaxu[3].substring(6, resultHuaxu[3].length);
					} else {
						cardNo = resultHuaxu[26].substring(6, resultHuaxu[26].length);
					}
					// let huaxuResult = huaxuWriteCard(resultData.cardNo, resultData.waterMeterNo, resultData.waterAmount, accumulationBuyCount, 0)
					let huaxuResult = huaxuWriteCard(cardNo, waterMeterNo, resultData.waterAmount, accumulationBuyCount, 0);
					console.log('huaxuResult: ', huaxuResult);
					if (huaxuResult[0] > 0) {
						flag = true
						resultData.customerId = data.customerId;
						dispatch(updateStatusCompleted(resultData, invoice, endLoad, refresh, newPriceDetail));
						message.success('缴费成功！');
						dispatch(payload(actionTypes.PAY_MONEY, res.data));
					}
				} else if (resultData.waterMeterManufacturer === '河南新天') {
					let result = -1;
					if (resultData.waterMeterKindType === '阶梯57') {
						if (resultData.ladderType === '非阶梯') {
							zjsj1 = resultData.price1;
							zjsj2 = resultData.price1;
							zjsj3 = resultData.price1;
							sl1 = resultData.ladder1;
							sl2 = resultData.ladder1;
						} else {
							zjsj1 = resultData.price1 ? resultData.price1 : 0;
							zjsj2 = resultData.price2 ? resultData.price2 : 0;
							zjsj3 = resultData.price3 ? resultData.price3 : 0;
							sl1 = resultData.ladder1 ? resultData.ladder1 : 0;
							sl2 = resultData.ladder2 ? resultData.ladder2 : 0;
						}
						result = xtWriteCard(resultData.cardNo, sl1, sl2, zjsj1, zjsj2, zjsj3, resultData.accumulationBuyAmount, resultData.writeCardAmount);
					} else if (resultData.waterMeterKindType === 'MF1') {
						result = rechargeXTMF1Card(resultData.cardNo, resultData.accumulationAmount, resultData.waterAmount);
					}
					if (result === 1 || result === true) {
						flag = true
						resultData.customerId = data.customerId;
						dispatch(updateStatusCompleted(resultData, invoice, endLoad, refresh, newPriceDetail));
						message.success('缴费成功！');
						dispatch(payload(actionTypes.PAY_MONEY, res.data));
					}
				}
			} else {
				flag = true
			}
			if (flag && invoice.invoice === 1) {
				let LODOP = getLodop();
				let newPrice = resultData.actualAmount;
				/*let newAddress = resultData.address;
				if (newAddress.indexOf('（') > -1) {
					newAddress = newAddress.substring(0, newAddress.indexOf('（'));
				}*/
				let water = resultData.waterAmount;
				let price = newPriceDetail.cleanWaterFee;
				let price1 = newPriceDetail.waterResourceFee;
				let price2 = newPriceDetail.sewageFee;
				let temp = invoice.formwork.content
					.replace('createTime', moment(new Date()).format('YYYY-MM-DD'))
					.replace('cno', resultData.cno)
					.replace('name', resultData.customerName)
					.replace('cardNo', resultData.cardNo || '')
					.replace('address', resultData.address)
					.replace('createPersonName', resultData.createPersonName)
					.replace('waterAmountDecimal', resultData.waterAmountDecimal)
					.replace('waterAmountDecimal', resultData.waterAmountDecimal)
					.replace('waterAmountDecimal', resultData.waterAmountDecimal)
					.replace('cleanWaterFeeUnit', resultData.cleanWaterFeeUnit)
					.replace('sewageFeeUnit', resultData.sewageFeeUnit)
					.replace('waterResourceFeeUnit', resultData.waterResourceFeeUnit)
					.replace('cleanWaterFeeDecimal', resultData.cleanWaterFeeDecimal)
					.replace('sewageFeeDecimal', resultData.sewageFeeDecimal)
					.replace('waterResourceFeeDecimal', resultData.waterResourceFeeDecimal)
					.replace('orderAmountRMB', resultData.orderAmountRMB)
					.replace('orderAmount', resultData.orderAmount)
					.replace('createPersonName', resultData.createPersonName);
				eval(temp);
				let result = LODOP.PRINT();
				if (result) {
					let param = {};
					param.invoiceSource = 0;
					param.sourceId = resultData.id;
					param.customerId = data.customerId;
					param.templateId = invoice.formwork.id;
					//param.invoiceNo = invoice.current;
					param.invoiceAmount = newPrice;
					param.payTime = resultData.createTime;
					param.payUid = resultData.createId;
					dispatch(getRecordSave(param));
				}
			}
			message.success('缴费成功！');
			//}
		}
		endLoad(); // 停止加载
		refresh(); // 刷新列表
	};
};

//修改订单状态
const updateStatusCompleted = (data, invoice, endLoad, refresh, newPriceDetail) => {
	return async dispatch => {
		const res = await http.restGet(api.updateStatusCompleted, data.id);
		if (res.code === 0) {
			if (invoice.invoice === 1) {
				let LODOP = getLodop();
				let newPrice = data.actualAmount;
				let resultData = res.data;
				let newAddress = resultData.address;
				if (newAddress.indexOf('（') > -1) {
					newAddress = newAddress.substring(0, newAddress.indexOf('（'));
				}
				let temp = invoice.formwork.content
					.replace('createTime', moment(new Date()).format('YYYY-MM-DD'))
					.replace('cno', resultData.cno)
					.replace('name', resultData.customerName)
					.replace('cardNo', resultData.cardNo || '')
					.replace('address', newAddress)
					.replace('createPersonName', resultData.createPersonName)
					.replace('waterAmountDecimal', resultData.waterAmountDecimal)
					.replace('waterAmountDecimal', resultData.waterAmountDecimal)
					.replace('waterAmountDecimal', resultData.waterAmountDecimal)
					.replace('cleanWaterFeeUnit', resultData.cleanWaterFeeUnit)
					.replace('sewageFeeUnit', resultData.sewageFeeUnit)
					.replace('waterResourceFeeUnit', resultData.waterResourceFeeUnit)
					.replace('cleanWaterFeeDecimal', resultData.cleanWaterFeeDecimal)
					.replace('sewageFeeDecimal', resultData.sewageFeeDecimal)
					.replace('waterResourceFeeDecimal', resultData.waterResourceFeeDecimal)
					.replace('orderAmountRMB', resultData.orderAmountRMB)
					.replace('orderAmount', resultData.orderAmount)
					.replace('createPersonName', resultData.createPersonName);
				eval(temp);
				let result = LODOP.PRINT();
				if (result) {
					let param = {};
					param.invoiceSource = 0;
					param.sourceId = data.id;
					param.customerId = data.customerId;
					param.templateId = invoice.formwork.id;
					//param.invoiceNo = invoice.current;
					param.invoiceAmount = newPrice;
					param.payTime = data.createTime;
					param.payUid = data.createId;
					dispatch(getRecordSave(param));
				}
			}
			endLoad(); // 停止加载
			refresh(); // 刷新列表
		}
	};
};

// 获取片区选择框
const getAreaSelect = () => {
	return async dispatch => {
		const res = await http.get(api.getAreaSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_AREA_SELECT, res.data));
		}
	};
};

// 获取用户基本信息
const getUserInfo = data => {
	return async dispatch => {
		const res = await http.get(api.userInfo + `/${data}`);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_USERINFO, res.data));
		}
	};
};

// 获取订单列表
const getOrderList = data => {
	return async dispatch => {
		const res = await http.post(api.orderList, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_ORDER_LIST, res.data));
		}
	};
};

// 获取账单列表
const getBillList = data => {
	return async dispatch => {
		const res = await http.post(api.billList, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_BILL_LIST, res.data));
		}
	};
};

// 水量计算金额
const calculateFee = data => {
	return async dispatch => {
		const res = await http.post(api.calculateFee, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.CALCULATE_FEE, res.data));
		}
	};
};

// 金额计算水量
const calculateWaterAmount = (data, userInfo) => {
	return async dispatch => {
		const res = await http.post(api.calculateWaterAmount, data);
		if (res.code === 0) {
			let detail = res.data;
			//计算剩余
			let netReceipts = Number(data.reallyFee) - Number(detail.money) + Number(userInfo.accountBalance);
			detail.netReceipts = netReceipts;
			dispatch(payload(actionTypes.CALCULATE_WATER_AMOUNT, detail));
		}
	};
};

//获取收费打印票据编号
const getCurrentNo = () => {
	return async dispatch => {
		const res = await http.get(api.getCurrentNo);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_CURRENT_NO, res.data));
		}
	};
};

//获取发票模板
const getByType = data => {
	return async dispatch => {
		const res = await http.restGet(api.getByType, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_BY_TYPE, res.data));
		}
	};
};

//保存打印记录
const getRecordSave = data => {
	return async dispatch => {
		await http.post(api.getRecordSave, data);
	};
};

//获取订单详情
const getDetailById = data => {
	return async dispatch => {
		const res = await http.restGet(api.getDetailById, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_DETAIL_BY_ID, res.data));
		}
	};
};

//修改用户姓名
const updateName = (data, editModal) => {
	return async dispatch => {
		const res = await http.post(api.updateName, data);
		if (res.code === 0) {
			dispatch(getUserInfo(data.customerId));
			editModal(false);
			message.success('修改用户姓名成功！');
		}
	};
};

//修改用户电话
const updateContactPhone = (data, editModal) => {
	return async dispatch => {
		const res = await http.post(api.updateContactPhone, data);
		if (res.code === 0) {
			dispatch(getUserInfo(data.customerId));
			editModal(false);
			message.success('修改用户电话成功！');
		}
	};
};

// 修改地址
const updateAddress = (data, editModal) => {
	return async dispatch => {
		const res = await http.post(api.updateAddress, data);
		if (res.code === 0) {
			dispatch(getUserInfo(data.customerId));
			editModal(false);
			message.success('修改成功');
		}
	};
};

const updateChangeNumber = (data, editModal) => {
	return async dispatch => {
		const res = await http.post(api.updateChangeNumber, data);
		if (res.code === 0) {
			dispatch(getUserInfo(data.customerId));
			editModal(false);
			message.success('修改成功！');
		}
	};
};

// 获取订单统计
const getTotal = data => {
	return async dispatch => {
		const res = await http.post(api.getTotal, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_TOTAL, res.data));
		}
	};
};

// 获取账单统计
const getBillTotal = data => {
	return async dispatch => {
		const res = await http.post(api.getBillTotal, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_BILL_TOTAL, res.data));
		}
	};
};

//查询发票单价
const getPriceDetail = data => {
	return async dispatch => {
		const res = await http.restGet(api.getPriceDetail, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_PRICE_DETAIL, res.data));
		}
	};
};

const getWaterSaleDetail = data => {
	return async dispatch => {
		const res = await http.post(api.getWaterSaleDetail, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_WATER_SALE_DETAIL, res.data));
		}
	};
};

// 获取详情
const getWaterMeterChangeList = data => {
	return async dispatch => {
		const res = await http.post(api.getWaterMeterChangeList, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_DETAIL_RECORD, res.data));
		}
	};
};

export {
	calculateFee,
	calculateWaterAmount,
	getAreaSelect,
	getBillList,
	getBillTotal,
	getByType,
	getCurrentNo,
	getDetailById,
	getOrderList,
	getPriceDetail,
	getTotal,
	getUserInfo,
	getWaterMeterChangeList,
	getWaterSaleDetail,
	list,
	pay,
	setState,
	updateAddress,
	updateChangeNumber,
	updateContactPhone,
	updateName,
};
