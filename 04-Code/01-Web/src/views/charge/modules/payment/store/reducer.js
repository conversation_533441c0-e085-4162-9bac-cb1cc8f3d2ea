import { dataType } from '$utils';

import * as actionTypes from './constants';

const defaultState = {
	fields: null,
	datalist: [], // 柜台缴费列表数据
	total: 0, // 总条数
	visible: false, // 涉及到交互的弹窗显示控制 默认为false
	areaSelect: [], // 片区选择框
	userInfo: {}, // 用户基本信息
	orderList: [], // 订单列表
	orderTotal: 0, // 订单总数
	billList: [], // 账单列表
	billTotal: 0, // 订单总数
	calculateFee: 0, //计算金额
	calculateWaterAmount: 0,  //计算量
	netReceipts: null,         //剩余金额
	current: null, // 收费员发票详情
	formwork: null,    //发票模板
	orderDetailList: [],  //订单详情
	orderCalc: {},
	billCalc: {},
	priceDetail: {},
	meterChangeList: [],
	meterChangeTotal: 0,
	codePayUrl: null,
	orderId: null,
};

// 重要提示： 请做好各代码段的注释！！！
export default (state = defaultState, action) => {
	switch (action.type) {
		// 回填组件传给redux的值
		case actionTypes.SET_STATE:
			let _data = action.data;
			console.log('action:', action);
			if (dataType(_data) === 'Object') {
				state[_data.key] = _data.value;
			}
			if (dataType(_data) === 'Array') {
				_data.forEach(item => {
					state[item.key] = item.value;
				});
			}

			console.log('state:', state);
			return { ...state };

		// 回填列表数据
		case actionTypes.LIST_RECORD:
			state.datalist = action.data.rows;
			state.total = action.data.total;
			return { ...state };

		// 缴费
		case actionTypes.PAY_MONEY:
			state.visible = false;
			return { ...state };

		// 获取片区选择框
		case actionTypes.GET_AREA_SELECT:
			state.areaSelect = action.data;
			return { ...state };

		//获取用户基本信息
		case actionTypes.GET_USERINFO:
			state.userInfo = action.data;
			return { ...state };

		// 获取订单列表
		case actionTypes.GET_ORDER_LIST:
			state.orderList = action.data.rows;
			state.orderTotal = action.data.total;
			return { ...state };

		// 获取账单列表
		case actionTypes.GET_BILL_LIST:
			state.billList = action.data.rows;
			state.billTotal = action.data.total;
			return { ...state };

		// 水量计算金额
		case actionTypes.CALCULATE_FEE:
			state.calculateFee = action.data;
			return { ...state };

		// 金额算水量
		case actionTypes.CALCULATE_WATER_AMOUNT:
			state.calculateFee = action.data.money;
			state.calculateWaterAmount = action.data.tonne;
			state.netReceipts = action.data.netReceipts;
			return { ...state };

		//获取收费员打印发票编号
		case actionTypes.GET_CURRENT_NO:
			state.current = action.data;
			return { ...state };

		//获取发票模板
		case actionTypes.GET_BY_TYPE:
			state.formwork = action.data;
			return { ...state };

		//获取发票模板
		case actionTypes.GET_DETAIL_BY_ID:
			state.orderDetailList = action.data;
			return { ...state };

		case actionTypes.GET_TOTAL:
			state.orderCalc = action.data;
			return { ...state };

		case actionTypes.GET_BILL_TOTAL:
			state.billCalc = action.data;
			return { ...state };

		//查询发票单价
		case actionTypes.GET_PRICE_DETAIL:
			state.priceDetail = action.data;
			return { ...state };

		// 换表记录
		case actionTypes.GET_METER_CHANGE:
			state.meterChangeList = action.data.rows;
			state.meterChangeTotal = action.data.total;
			return { ...state };
		case actionTypes.CODE_PAY:
			state.codePayUrl = action.data.codeUrl;
			state.orderId = action.data.orderId;
			return { ...state };
		default:
			return state;
	}
}
