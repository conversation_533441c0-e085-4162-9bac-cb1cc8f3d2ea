import React, { Component, Fragment } from 'react';

import Operation from '$components/bills';
import http from '$http';
import { Button, Col, DatePicker, Form, message, Row, Table, Tag } from 'antd';
import moment from 'moment';
import Print from 'rc-print';
import { connect } from 'react-redux';

import { billStatusColorMap } from '@/constants/colorStyle';

import { actionCreators } from '../store';

const { RangePicker } = DatePicker;
const FormItem = Form.Item;

class BillTable extends Component {
	constructor(props) {
		super(props);
		this.state = {
			page: 1,
			pageSize: 10,
			total: 100,
			visible: false,
			record: {},
			selectedRowKeys: [],
			createTimeStart: null,
			createTimeEnd: null
		};
	}

	getList() {
		const { page, pageSize } = this.state;
		const { billList } = this.props;
		let param = { page: page, pageSize: pageSize, customerId: billList[0].customerId };
		this.props.getBillList(param);
	}

	// 列表分页
	handlePageChange = page => {
		this.setState(
			{
				page
			},
			() => {
				this.getList();
			}
		);
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState(
			{
				page: 1,
				pageSize: size
			},
			() => {
				this.getList();
			}
		);
	};
	// 取消主弹窗
	handlePrimaryCancel = () => {
		this.props.setState([
			{ key: 'visible', value: false },
			{ key: 'netReceipts', value: 0 },
			{ key: 'calculateWaterAmount', value: '' },
			{ key: 'calculateFee', value: null }
		]);
	};

	// 查看详情
	handleView = record => {
		http.restGet(`api/bill/getDetail`, record.id).then(res => {
			if (res.code === 0) {
				this.setState({
					title: '查看详情',
					type: 0,
					record: res.data,
					visible: true
				});
			}
		});
	};

	// 关闭弹窗
	handleModalCancel = () => {
		this.setState({
			visible: false
		});
	};

	//查询订单
	queryBillByTime() {
		const { createTimeStart, createTimeEnd } = this.state;
		const { detail } = this.props;
		let param = {
			createTimeStart: createTimeStart,
			createTimeEnd: createTimeEnd,
			page: 1,
			pageSize: 10,
			customerId: detail.id
		};
		this.props.getBillList(param); // 获取订单列表
	}

	// 选中事件
	onSelectChange = (selectedRowKeys, selectedRows) => {
		this.setState({ selectedRowKeys, selectedRows });
	};

	//订单时间查询条件
	getOrderDate = (date, dateString) => {
		if (dateString.length === 2) {
			this.setState({
				createTimeStart: moment(dateString[0]).format('YYYY-MM-DD'),
				createTimeEnd: moment(dateString[1]).format('YYYY-MM-DD')
			});
		}
	};

	//打印订单
	printBill() {
		const { selectedRows } = this.state;
		if (selectedRows && selectedRows.length > 0) {
			this.refs.test.onPrint();
		} else {
			message.error('请至少选择一条记录打印！');
		}
	}

	render() {
		const { page, pageSize, visible, record, selectedRowKeys, selectedRows } = this.state;
		const { billList, billTotal, billCalc } = this.props;
		const columns = [
			{
				title: '上期示数',
				dataIndex: 'lastWheelNumber',
				key: 'lastWheelNumber',
				align: 'center'
			},
			{
				title: '本期示数',
				dataIndex: 'currentWheelNumber',
				key: 'currentWheelNumber',
				align: 'center'
			},
			{
				title: '结算水量',
				dataIndex: 'settleAmount',
				key: 'settleAmount',
				align: 'center'
			},
			{
				title: '账单金额',
				dataIndex: 'actualFee',
				key: 'actualFee',
				align: 'center'
			},
			{
				title: '已结金额',
				dataIndex: 'settleFee',
				key: 'settleFee',
				align: 'center'
			},
			{
				title: '用户编号',
				dataIndex: 'cno',
				key: 'cno',
				align: 'center'
			},
			{
				title: '账单编号',
				dataIndex: 'billNo',
				key: 'billNo',
				align: 'center'
			},
			{
				title: '水表编号',
				dataIndex: 'waterMeterNo',
				key: 'waterMeterNo',
				align: 'center'
			},
			{
				title: '水表种类',
				dataIndex: 'waterMeterKind',
				key: 'waterMeterKind',
				align: 'center'
			},
			{
				title: '账单类型',
				dataIndex: 'billType',
				key: 'billType',
				align: 'center',
				render: text => {
					if (text === '红冲出账') {
						return <span style={{ color: 'red' }}>{text}</span>;
					} else {
						return text;
					}
				}
			},
			{
				title: '账单状态',
				dataIndex: 'billStatus',
				key: 'billStatus',
				align: 'center',
				render: text => {
					return <Tag color={billStatusColorMap.get(text)}>{text}</Tag>;
				}
			},
			{
				title: '出账日期',
				dataIndex: 'createTime',
				key: 'createTime',
				align: 'center'
			},
			{
				title: '平账日期',
				dataIndex: 'averageAccountTime',
				key: 'averageAccountTime',
				align: 'center'
			},
			{
				title: '账单创建人',
				dataIndex: 'createPersonName',
				key: 'createPersonName',
				align: 'center'
			},
			{
				title: '送盘标志',
				dataIndex: 'sendDiscMark',
				key: 'sendDiscMark',
				align: 'center'
			},
			{
				title: '操作',
				align: 'center',
				width: 100,
				fixed: 'right',
				render: (text, record, index) => {
					return (
						<Button title="查看" className="btn" type="primary" size="small" icon="eye" onClick={() => this.handleView(record)}>
							详情
						</Button>
					);
				}
			}
		];
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: billTotal,
			showTotal: total => {
				return `共 ${billTotal} 条数据 `;
			},
			onChange: page => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		const formItemLayout = {
			labelCol: {
				xs: { span: 2 }
			}
		};
		const rowSelection = { fixed: true, selectedRowKeys, onChange: this.onSelectChange };
		const otherStyle = `
      @media print{
          .black{
            font-size:10px;
          }
          .tableType table tr th{
            border-color: #000;
            font-size: 10px;
          }
          .tableType table tr td{
              border-color: #000;
          }
          .tableType1 {
              font-size: 10px!important;
          }
          .printPage .next-form-item-control .next-input-medium{
              border:none;
              border-radius: 0;
          }

      }
  `;
		return (
			<Fragment>
				<Print ref="test" isIframe={false} insertHead={false} otherStyle={otherStyle} lazyRender clearIframeCache={true}>
					<Fragment>
						<h2 align="center">用户出账信息</h2>
						<div>用户编号：{selectedRows && selectedRows[0].cno}</div>
						<div>用户名称：{selectedRows && selectedRows[0].customerName}</div>
						<div>用户地址：{selectedRows && selectedRows[0].customerAddress}</div>
						<Table dataSource={selectedRows} pagination={false}>
							<Table.Column title="水表编号" dataIndex="waterMeterNo" align="center" width={150} />
							<Table.Column title="本期示数" dataIndex="currentWheelNumber" align="center" width={150} />
							<Table.Column title="水量" dataIndex="settleAmount" align="center" width={150} />
							<Table.Column title="金额" dataIndex="actualFee" align="center" width={150} />
							<Table.Column title="开账日期" dataIndex="createTime" align="center" width={150} />
						</Table>
					</Fragment>
				</Print>
				<FormItem label={'账单日期:'} {...formItemLayout}>
					<RangePicker onChange={this.getOrderDate} placeholder={['开始日期', '结束日期']} />
					<Button type="primary" onClick={() => this.queryBillByTime()} style={{ marginLeft: 10 }}>
						查询
					</Button>
					<Button type="primary" onClick={() => this.printBill()} style={{ marginLeft: 10 }}>
						打印
					</Button>
				</FormItem>
				<Row>
					<Col style={{ marginBottom: 5, color: '#108ee9' }}>
						总金额: {billCalc ? billCalc.orderAmount : ''}元 &emsp;&emsp;&emsp; 总用水量：{billCalc ? billCalc.waterAmount : ''}吨 &emsp;&emsp;&emsp;
					</Col>
				</Row>
				<Table bordered columns={columns} rowSelection={rowSelection} rowKey={data => data.id} dataSource={billList} scroll={{ x: 2500 }} pagination={paginationProps} />
				<Operation title="查看详情" visible={visible} record={record} type={0} onCancel={this.handleModalCancel} onSubmit={() => {}} />
				<Row>
					<Col span={24} align="center">
						<Button className="btn" onClick={this.handlePrimaryCancel}>
							取消
						</Button>
					</Col>
				</Row>
			</Fragment>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('payment');
	return {
		fields: data.fields,
		billList: data.billList,
		billTotal: data.billTotal
	};
};
const mapDispatchToProps = dispatch => ({
	setState: data => dispatch(actionCreators.setState(data)),
	getBillList: data => dispatch(actionCreators.getBillList(data)) // 获取账单列表
});
export default connect(mapStateToProps, mapDispatchToProps)(Form.create()(BillTable));
