import React, {
	Component,
	Fragment,
} from 'react';

import http from '$http';
import { constants } from '$utils';
import {
	Button,
	Col,
	Descriptions,
	Form,
	Icon,
	Input,
	InputNumber,
	message,
	Modal,
	Radio,
	Row,
	Spin,
} from 'antd';
import Decimal from 'decimal.js';
import moment from 'moment';
import qrcode from 'qrcode-generator';
import { connect } from 'react-redux';

import {
	qsRead,
	readCard,
	xtRead,
} from '@/utils/cardUtil';
import { Math as MathUtil } from '@/utils/math';

import digitalUppercase from '../../../../../utils/digitalUppercase';
import formatDate from '../../../../../utils/formatDate';
import getLodop from '../../../../../utils/LodopFuncs';
import { actionCreators } from '../store';

const FormItem = Form.Item;
const Group = Radio.Group;
const { TextArea } = Input;

class Pay extends Component {

	constructor(props) {
		super(props);
		this.state = {
			page: 1,
			pageSize: 10,
			cno: '', // 用户编号
			cardNo: '', // 用户卡号
			waterMeterNo: '', //水表编号
			name: '', // 用户名称
			areaName: [], // 片区
			address: '', // 用户地址
			netReceipts: 0, //剩余金额
			loading: false,
			visible: false,
			detail: undefined,
			type: '',
			startTime: null,
			endTime: null,
			payDTO: null,
			intervalId: null,
			countdown: 300,
		};
	}

	componentDidMount () {
		const { detail } = this.props;
		console.log('detail: ', detail);
		this.props.getPriceDetail(detail.waterUseKindId);
	}

	//计算
	handleCount (value, userInfo, type) {
		//优惠金额
		let numB = this.props.form.getFieldValue('discountedAmount');
		//优惠金额
		let houseOweMoney = userInfo.houseOweMoney != 0 ? Math.abs(Number(userInfo.houseOweMoney)) : '0';
		//应收金额
		let receivableAmount = this.props.form.getFieldValue('receivableAmount');
		//剩余金额
		let numC = Number(value).toFixed(2) - Number(numB).toFixed(2) - Number(receivableAmount).toFixed(2);
		if (numC < 0) {
			this.props.form.setFieldsValue({ netReceipts: 0 });
		} else {
			this.props.form.setFieldsValue({ netReceipts: numC });
		}
		let reallyFee = 0;
		if (type === '卡表') {
			//结余方式
			let method = this.props.form.getFieldValue('method');
			if (method === 0) {
				//实收金额
				reallyFee = (value - numB).toFixed(2);
				if (reallyFee < 0) {
					this.props.form.setFieldsValue({ reallyFee: 0 });
				} else {
					this.props.form.setFieldsValue({ reallyFee: reallyFee });
				}
				this.props.form.setFieldsValue({ thisBalance: numC });
			} else {
				//实收金额=收款金额-优惠金额-剩余金额
				reallyFee = (value - numB - numC).toFixed(2);
				if (reallyFee < 0) {
					this.props.form.setFieldsValue({ reallyFee: 0.00 });
				} else {
					this.props.form.setFieldsValue({ reallyFee: reallyFee });
				}
				this.props.form.setFieldsValue({ thisBalance: 0 });
			}
		} else {
			//实收金额
			let reallyFee = (Number(value) - Number(numB)).toFixed(2);
			if (reallyFee < 0) {
				this.props.form.setFieldsValue({ reallyFee: 0 });
			} else {
				if (reallyFee < value) {
					this.props.form.setFieldsValue({ reallyFee: reallyFee });
				} else {
					if (this.props.form.getFieldValue('method') == 1) {
						if (reallyFee < houseOweMoney) {
							this.props.form.setFieldsValue({ reallyFee: reallyFee });
						} else {
							this.props.form.setFieldsValue({ reallyFee: houseOweMoney });
						}
					} else {
						this.props.form.setFieldsValue({ reallyFee: reallyFee });
					}
				}
			}
			//本次余额=实收+账户余额+欠费金额(这个是负数)
			let thisBalance = value - numB + Number(userInfo.houseAccountBalance) + Number(userInfo.houseOweMoney);
			if (thisBalance < 0) {
				this.props.form.setFieldsValue({ thisBalance: 0 });
			} else {
				this.props.form.setFieldsValue({ thisBalance: thisBalance });
			}
		}

	};

	// 开始加载
	startLoad = () => {
		this.setState({ loading: true });
	};

	// 结束加载
	endLoad = () => {
		setTimeout(() => {
			this.setState({ loading: false }, () => this.props.setState([{ key: 'visible', value: false }]));
		}, 500);
		this.handleCancel();
	};

	// 缴费
	handlePay = () => {
		//this.props.getCurrentNo();      //查询发票编号
		const { formwork, detail, priceDetail, userInfo, codePayUrl } = this.props;
		const { payDTO } = this.state
		let newPriceDetail = priceDetail && priceDetail.waterUseKindDetailList ? (priceDetail.waterUseKindDetailList)[0] : {};
		this.props.form.validateFields((err, values) => {
			if (!err) {
				// 用户id
				// 收款金额
				// 优惠金额
				// 实收金额
				// 备注
				// 付款方式
				// 打印收据
				if (values.chargeWay === 9) {
					console.log('扫码付钱');
					values.customerId = detail.id;
					values.waterMeterType = detail.waterMeterType;
					values.waterAmount = values.waterAmount ? values.waterAmount : 0
					this.setState({ payDTO: values }, () => { this.props.codePay(values, () => this.renderCodePay()) })
				} else {
					Modal.confirm({
						title: '确认缴费吗',
						content: <p>用户编号：{detail.cno}<br />
							用户名称：{userInfo.name}<br />
							用户地址：{detail.address}<br />
							缴费金额：{Number(values.reallyFee).toFixed(2)}元</p>,
						okText: '确认',
						cancelText: '取消',
						onOk: async () => {
							//const { current } = this.props;
							this.startLoad(); // 加载
							// 跳出选择营业厅弹出框
							values.businessHallId = JSON.parse(localStorage.getItem('bh'));
							values.customerId = detail.id;
							values.orderSource = 0;
							values.waterMeterType = detail.waterMeterType;
							Number(values.reallyFee).toFixed(2);
							//打印
							await this.props.pay(values, this.endLoad, this.props.refresh, values.invoice, newPriceDetail);
						}
					});
				}


			}
		});
	};

	//取消弹窗
	handleCancel = () => {
		this.props.form.resetFields();
		this.props.setState(
			[
				{ key: 'visible', value: false },
				{ key: 'netReceipts', value: 0 },
				{ key: 'calculateWaterAmount', value: '' },
				{ key: 'calculateFee', value: null }
			]);
	};

	//根据水量计算金额
	calculateFee = () => {
		const waterAmountValue = this.props.form.getFieldValue('waterAmount');
		if (waterAmountValue == null || waterAmountValue === '') {
			message.error('购买水量不能为空');
			return
		}
		const waterAmount = Number(waterAmountValue)
		if (!Number.isInteger(waterAmount)) {
			message.error('购买水量只能为整数');
			return
		}
		if (waterAmount <= 0) {
			message.error('购买水量必须大于0吨');
			return
		}
		this.props.form.resetFields(['payAmount', 'netReceipts', 'reallyFee', 'thisBalance', 'calcAmount', 'receivableAmount']);
		this.props.setState({ key: 'netReceipts', value: null });
		const param = {
			customerId: this.props.detail.id,
			waterAmount
		}
		this.props.calculateFee(param, payAmount =>
			this.props.form.setFieldsValue({ payAmount, reallyFee: payAmount })
		)
	}

	//根据金额算水量
	calculateWaterAmount (userInfo) {
		let newPayAmount = this.props.form.getFieldValue('payAmount');
		if (newPayAmount) {
			this.props.form.resetFields(['waterAmount', 'netReceipts', 'calcAmount', 'receivableAmount']);
			let method = this.props.form.getFieldValue('method');
			if (method === 0) {
				this.props.form.resetFields('thisBalance');
			}
			let param = {
				customerId: this.props.detail.id,
				reallyFee: newPayAmount
			};
			this.props.calculateWaterAmount(param, userInfo);
		} else {
			message.error('购买金额不能为空');
		}
	}

	//改变结余方式
	handleChangeMethod (value, userInfo) {
		//收款金额
		let payAmount = this.props.form.getFieldValue('payAmount');
		//优惠金额
		let discountedAmount = this.props.form.getFieldValue('discountedAmount');
		//剩余金额
		let netReceipts = this.props.form.getFieldValue('netReceipts');
		let reallyFee = 0;

		if (value === 0) {
			//实收金额=收款金额-优惠金额
			reallyFee = Number(payAmount).toFixed(2) - Number(discountedAmount).toFixed(2);
			if (reallyFee < 0) {
				this.props.form.setFieldsValue({ reallyFee: 0.00 });
			} else {
				this.props.form.setFieldsValue({ reallyFee: reallyFee });
			}
			if (userInfo.waterMeterType === 'IC卡表') {
				this.props.form.setFieldsValue({ thisBalance: netReceipts });
			} else {
				this.props.form.setFieldsValue({ thisBalance: reallyFee });
			}

		} else {
			//实收金额=收款金额-优惠金额-剩余金额
			reallyFee = Number(payAmount).toFixed(2) - Number(discountedAmount).toFixed(2) - Number(netReceipts).toFixed(2);
			if (reallyFee < 0) {
				this.props.form.setFieldsValue({ reallyFee: 0.00 });
			} else {
				this.props.form.setFieldsValue({ reallyFee: reallyFee });
			}
			this.props.form.setFieldsValue({ thisBalance: 0 });
		}
	}

	//改变结余方式
	handleChangeMethodNotIC (value, userInfo) {
		//收款金额
		let payAmount = this.props.form.getFieldValue('payAmount');
		//优惠金额
		let discountedAmount = this.props.form.getFieldValue('discountedAmount');
		//优惠金额
		let houseOweMoney = userInfo.houseOweMoney != 0 ? Math.abs(Number(userInfo.houseOweMoney)) : '0';
		//剩余金额
		let reallyFee = 0;
		if (value === 0) {
			//实收金额=收款金额-优惠金额
			reallyFee = Number(payAmount).toFixed(2) - Number(discountedAmount).toFixed(2);
			if (reallyFee < 0) {
				this.props.form.setFieldsValue({ reallyFee: 0.00 });
			} else {
				this.props.form.setFieldsValue({ reallyFee: reallyFee });
			}
			let thisBalance = Number(payAmount).toFixed(2) - (-Number(userInfo.houseOweMoney).toFixed(2));
			thisBalance = Number(thisBalance).toFixed(2);
			console.log(thisBalance);
			this.props.form.setFieldsValue({ thisBalance: thisBalance });
		} else {
			//实收金额=欠费金额
			this.props.form.setFieldsValue({ reallyFee: -userInfo.houseOweMoney });
			this.props.form.setFieldsValue({ thisBalance: 0.00 });
		}

		if (payAmount > houseOweMoney) {
			if (this.props.form.getFieldValue('method') == 0) {
				this.props.form.setFieldsValue({ reallyFee: houseOweMoney });
			} else {
				this.props.form.setFieldsValue({ reallyFee: payAmount });
			}
		} else {
			this.props.form.setFieldsValue({ reallyFee: payAmount });
		}

	}

	realBalance = (balance, realTimeFee) => {
		if (balance == null) {
			return null
		}
		if (realTimeFee == null) {
			return balance
		}
		return new Decimal(balance).sub(new Decimal(realTimeFee)).toNumber()
	}

	//渲染卡表表单
	renderIcForm () {
		const { form, userInfo, calcFee, calcWater, newNetReceipts, current, getFieldValue } = this.props;
		const { getFieldDecorator } = form;
		return <Form {...constants.formItemLayout}>
			<Row>
				<Descriptions size="small" bordered>
					<Descriptions.Item label="用户编号:">{userInfo && userInfo.cno}</Descriptions.Item>
					<Descriptions.Item label="用户名称:">
						<div style={{ display: 'flex', justifyContent: 'space-between' }}>
							{userInfo && userInfo.name}
							<Icon
								type="edit"
								style={{ cursor: 'pointer', textAlign: 'right', color: 'blue' }}
								onClick={() => this.updateNameModal(userInfo)}
							/>
						</div>
					</Descriptions.Item>
					<Descriptions.Item label="用户电话:">
						<div style={{ display: 'flex', justifyContent: 'space-between' }}>
							{userInfo && userInfo.contactPhone}
							<Icon
								type="edit"
								style={{ cursor: 'pointer', textAlign: 'right', color: 'blue' }}
								onClick={() => this.updatePhoneModal(userInfo)}
							/>
						</div>
					</Descriptions.Item>
					<Descriptions.Item label="用水性质:">{userInfo && userInfo.waterUseKindName}</Descriptions.Item>
					<Descriptions.Item label="水费单价：">{userInfo && userInfo.price}</Descriptions.Item>
					<Descriptions.Item label="用水分类:">{userInfo && userInfo.waterUseKindType}</Descriptions.Item>
					<Descriptions.Item label="户籍人数:">{userInfo && userInfo.population}</Descriptions.Item>
					<Descriptions.Item label="户数:">{userInfo && userInfo.numberOfHouse}</Descriptions.Item>
					<Descriptions.Item label="水表编号:">{userInfo && userInfo.waterMeterNo}</Descriptions.Item>
					<Descriptions.Item label="水表类型:">{userInfo && userInfo.waterMeterType}</Descriptions.Item>
					<Descriptions.Item
						label="水表厂家:">{userInfo && userInfo.waterMeterManufacturer}</Descriptions.Item>
					<Descriptions.Item label="水表种类:">{userInfo && userInfo.waterMeterKindType}</Descriptions.Item>
					<Descriptions.Item label="用户地址:">{userInfo && userInfo.address}</Descriptions.Item>
					<Descriptions.Item label="阶梯使用量:">{userInfo && userInfo.houseLadderUse}</Descriptions.Item>
					<Descriptions.Item label="阶梯总量:">{userInfo && userInfo.houseLadderSum}</Descriptions.Item>
					<Descriptions.Item label="账户余额:">{userInfo && this.realBalance(userInfo.houseAccountBalance, userInfo.realTimeWaterFee)}</Descriptions.Item>
					<Descriptions.Item label="户预存水量:">{userInfo.housePreStoreWaterAmount}</Descriptions.Item>
					<Descriptions.Item
						label="已购阶梯量:">{userInfo && userInfo.houseEstimateLadderUse}</Descriptions.Item>
					<Descriptions.Item
						label="购买阶梯总量:">{userInfo && userInfo.houseEstimateLadderSum}</Descriptions.Item>
					<Descriptions.Item label="欠费金额:">
						<span style={{ color: 'red' }}>{userInfo && userInfo.houseOweMoney}</span>
					</Descriptions.Item>

				</Descriptions>
			</Row>
			<br />
			<h3>购水缴费</h3>

			<Row>
				<Col span={12}>
					<FormItem label="购买水量(吨):">
						{
							getFieldDecorator('waterAmount',
								{
									initialValue: calcWater ? calcWater : '',
									rules: [{ required: true, message: '请输入最新读数' },
									{
										validator: (rule, value, callback) => {
											if (value <= 0) {
												callback('输入吨数不能小于0或等于0');
											} else if (!/^(?!0+(?:\.0+)?$)(?:[1-9]\d*|0)(?:\.\d{1,2})?$/.test(value)) {
												callback('该项只能输入整数或者小数点2位');
											} else {
												callback();
											}
										}
									}]
								})
								(<InputNumber max={*********} style={{ width: 150 }} />)
						}
						<Button type="primary" onClick={() => this.calculateFee(userInfo)}
							style={{ marginLeft: 10 }}>按水量计算金额</Button>
					</FormItem>
				</Col>

				<Col span={12}>
					<FormItem label="计算金额(元):">
						{
							getFieldDecorator('calcAmount', {
								initialValue: calcFee ? calcFee : ''
							})
								(<InputNumber style={{ width: 200 }} precision={2} disabled />)
						}
					</FormItem>
				</Col>

				<Col span={12}>
					<FormItem label="优惠金额(元):">
						{
							getFieldDecorator('discountedAmount', {
								initialValue: 0,
								rules: [{ required: true, message: '必填' }]
							})
								(<InputNumber min={0} max={9999}
									onBlur={(v) => {
										this.handleCount(v.target.value);
									}} style={{ width: 150 }} disabled precision={2} />)
						}
					</FormItem>
				</Col>

				<Col span={12}>
					<FormItem label="应收金额(元):">
						{
							getFieldDecorator('receivableAmount', { initialValue: calcFee ? Number(calcFee - userInfo.accountBalance).toFixed(2) <= 0 ? 0 : Number(calcFee - userInfo.accountBalance).toFixed(2) : '' })
								(<InputNumber style={{ width: 200 }} disabled precision={2} />)
						}
					</FormItem>
				</Col>

				<Col span={12}>
					<FormItem label="收款金额(元):">
						{getFieldDecorator('payAmount', {
							rules: [{ required: true, message: '必填' }]
						})
							(<InputNumber max={*********} precision={2} style={{ width: 150 }}
								disabled
								onChange={(v) => {
									this.handleCount(v, userInfo, '卡表');
								}} />)}
						{/*<Button type="primary" onClick={() => this.calculateWaterAmount(userInfo)}
										style={{ marginLeft: 10 }}>按金额计算水量</Button>*/}
					</FormItem>
				</Col>

				<Col span={12}>
					<FormItem label="剩余金额(元):">
						{
							getFieldDecorator('netReceipts', { initialValue: newNetReceipts ? newNetReceipts : 0 })
								(<InputNumber disabled style={{ width: 200 }} precision={2} />)
						}
					</FormItem>
				</Col>

				<Col span={12}>
					<FormItem label="实收金额(元):">
						{
							getFieldDecorator('reallyFee')
								(<InputNumber disabled style={{ width: 150 }} precision={2} />)
						}
					</FormItem>
				</Col>

				<Col span={12}>
					<FormItem label="本次余额(元):">
						{
							getFieldDecorator('thisBalance', { initialValue: newNetReceipts ? newNetReceipts : 0 })
								(<InputNumber disabled style={{ width: 200 }} precision={2} />)
						}
					</FormItem>
				</Col>

				<Col span={24} pull={4}>
					<FormItem label="备注:">
						{
							getFieldDecorator('remark')(<TextArea rows={3} palceholder='请输入备注' />)
						}
					</FormItem>
				</Col>

				<Col span={24} pull={4}>
					<FormItem label="付款方式:">
						{
							getFieldDecorator('chargeWay', { initialValue: 0, rules: [{ required: true, message: '必选' }] })
								(<Group>
									<Radio value={0}>现金</Radio>
									<Radio value={9}>扫码支付</Radio>
									<Radio value={2}>POS机</Radio>
									<Radio value={3}>银行回单</Radio>
									<Radio value={12}>代金券</Radio>
									<Radio value={14}>挂账</Radio>

								</Group>)
						}
					</FormItem>
				</Col>

				{
					this.props.form.getFieldValue('chargeWay') === 3 ?
						<Col span={12}>
							<FormItem label="转账日期:">
								{getFieldDecorator('wireTransferNo', { rules: [{ required: true, message: '必填' }] })
									(<Input precision={2} style={{ width: 200 }} />)}
							</FormItem>
						</Col> : void (0)

				}

				{
					this.props.form.getFieldValue('chargeWay') === 12 ?
						<Col span={12}>
							<FormItem label="水券号:">
								{getFieldDecorator('wireTransferNo', { rules: [{ required: true, message: '必填' }] })
									(<Input precision={2} style={{ width: 200 }} />)}
							</FormItem>
						</Col> : void (0)

				}


				<Col span={24} pull={4}>
					<FormItem label="打印收据:">
						{getFieldDecorator('invoice', {
							initialValue: 1, rules: [{ required: true, message: '必选' }]
						})(
							<Group>
								<Radio value={1}>打印</Radio>
								<Radio value={0}>不打印</Radio>
							</Group>
						)
						}
					</FormItem>
				</Col>
				{
					this.props.form.getFieldValue('invoice') === 1 ?
						<div>
							<Col span={10}>
								<FormItem label="水费票号:">
									{getFieldDecorator('cleanNo')(<Input precision={2} maxLength={100} style={{ width: 200 }} />)}
								</FormItem>
							</Col>
							<Col span={10}>
								<FormItem label="污水票号:">
									{getFieldDecorator('sewageNo',)(<Input precision={2} maxLength={100} style={{ width: 200 }} />)}
								</FormItem>
							</Col>
						</div>
						: void (0)
				}

				{
					this.props.form.getFieldValue('invoice') === 0 ?
						<div>
							<Col span={10}>
								<FormItem label="水费发票号码:">
									{getFieldDecorator('cleanNo')(<Input precision={2} maxLength={100} style={{ width: 200 }} />)}
								</FormItem>
							</Col>
							<Col span={10}>
								<FormItem label="污水发票号码:">
									{getFieldDecorator('sewageNo')(<Input precision={2} maxLength={100} style={{ width: 200 }} />)}
								</FormItem>
							</Col>
						</div>
						: void (0)
				}
			</Row>

			<Row style={{paddingBlock: '10px'}}>
				<Col span={24} align="center">
					<Button type="primary" className="btn" onClick={() => this.handlePay()}>缴费</Button>
					<Button className="btn" onClick={this.handleCancel}>取消</Button>
				</Col>
			</Row>
		</Form>;
	}

	//修改名字
	updateNameModal (userInfo) {
		this.setState({ visible: true, detail: userInfo, type: 'name' });
	}

	//修改电话
	updatePhoneModal (userInfo) {
		this.setState({ visible: true, detail: userInfo, type: 'phone' });
	}

	//编辑修改弹窗
	editModal (visible) {
		this.setState({ visible: visible });
	}

	//支付二维码弹窗
	renderCodePay () {
		const { codePayUrl } = this.props;
		console.log('要生成的二维码:', codePayUrl);
		if (!codePayUrl) {
			return;
		}
		// QRCode.toDataURL(codePayUrl, (err, url) => {
		// 	if (err) {
		// 		console.error('生成二维码失败：', err);
		// 		message.error('生成二维码失败');
		// 		return;
		// 	}
		// 	console.log('url:', url);
		// 	// 将生成的二维码图片插入到 modal 中
		// 	const qrCodeContainer = document.getElementById('qr-code-container');
		// 	if (qrCodeContainer) {
		// 		qrCodeContainer.innerHTML = `<img src="${url}" alt="QR Code" />`;
		// 	}
		// });
		const qr = qrcode(0, 'M');
		qr.addData(codePayUrl);
		qr.make();
		const qrCodeContainer = document.getElementById('qr-code-container');
		if (qrCodeContainer) {
			qrCodeContainer.innerHTML = `<img src="${qr.createDataURL()}" alt="QR Code" />`;
		}
	}
	//修改提交
	handleClick (type, detail) {
		let param = { customerId: detail.id };
		if (type === 'name') {
			param.name = this.props.form.getFieldValue('name');
			this.props.updateName(param, (data) => this.editModal(data));
		} else {
			param.contactPhone = this.props.form.getFieldValue('contactPhone');
			this.props.updateContactPhone(param, (data) => this.editModal(data));
		}
	}

	codePayComplete () {
		this.props.setState([{ key: 'codePayUrl', value: null }]);
		clearInterval(this.state.intervalId);
		this.setState({ intervalId: null });
		this.props.toOrderList();
	}

	closeCodePayModal () {
		this.props.setState([{ 'key': 'codePayUrl', 'value': null }]);
		clearInterval(this.state.intervalId);
		this.setState({ intervalId: null, countdown: 300 });
	}
	//写卡
	writeCard (record) {
		console.log('开始写卡');
		if (record.manufacturer === '扬州恒信') {
			let userCard = readCard();
			let cardType = userCard.substring(1, 2);
			let cardNo = null;
			if (cardType === '3' || cardType === '4') {
				cardNo = userCard.substring(2, 12);
			} else {
				cardNo = userCard.substring(2, 10);
			}
			if (cardNo === record.cardNo) {
				let state = '';       //刷卡状态
				let temp = false;     //刷卡标志位

				if (record.waterMeterKindType === '预付费2') {
					state = userCard.substr(-4);
					if (state !== '0000') {
						temp = true;
						message.error('上次缴费未刷表');
					}
				} else if (record.waterMeterKindType === '预付费4442') {
					state = userCard.substr(16, 20);
					if (state !== '0000') {
						temp = true;
						message.error('上次缴费未刷表');
					}
				} else if (record.waterMeterKindType === '阶梯2') {
					state = userCard.substring(20, 26);
					if (parseInt(state, 16) !== 0) {
						temp = true;
						message.error('上次缴费未刷表');
					}
				}
				if (!temp) {
					this.props.getICDetail(record.id);
				}
			} else {
				message.error('该卡卡号和当前用户卡号不一致！请检查');
			}
		} else if (record.manufacturer === '河南新天') {
			let resultXT = xtRead();
			resultXT = resultXT.split(',');
			console.log('resultXT', resultXT)
			console.log('record.waterMeterKind', record.waterMeterKind)
			if (parseInt(resultXT[2], 16) !== Number(record.cardNo)) {
				alert('该卡卡号和当前用户卡号不一致！请检查');
			} else {
				if (parseInt(resultXT[5], 16) !== 0) {
					alert('上次缴费未刷表,请先刷表');
				} else {
					this.props.getICDetail(record.id);
				}
			}
		} else if (record.manufacturer === '泰安') {
			qsRead().then(qsResult => {
				console.log('qsRead: ', qsRead);
				if (qsResult.errcode == 0) {
					if (qsResult.cardtype < 0) {
						message.error('非用户卡！');
					} else {
						if (qsResult.usercode != record.cardNo) {
							alert('该卡卡号和当前用户卡号不一致！请检查');
							return;
						}
						if (qsResult.data.length > 0 && qsResult.data[0].cardstatus == 0) {
							alert('上次缴费未刷表,请先刷表');
							return;
						}
						this.props.getICDetail(record.id);
					}
				}
			})
		}
	}
	//打印
	async printReceipt (record) {
		const {
			customerName,
			address,
			cno,
			waterAmount, // 订单吨数
			orderAmount, // 订单金额
			chargeDetailList,
			createPersonName,
			waterUseKind: { ladderType, name, waterUseKindDetailList }
		} = record
		// 单价
		const { cleanWaterFee, waterResourceFee, sewageFee } = waterUseKindDetailList[0]
		// 各水费合计金额
		const { cleanFeeSum, sewageFeeSum, resourceFeeSum } = chargeDetailList.reduce((total, item) => {
			const { cleanFeeSum, sewageFeeSum, resourceFeeSum } = total
			total.cleanFeeSum = new Decimal(cleanFeeSum).add(new Decimal(item.cleanWaterFee))
			total.sewageFeeSum = new Decimal(sewageFeeSum).add(new Decimal(item.sewageFee))
			total.resourceFeeSum = new Decimal(resourceFeeSum).add(new Decimal(item.waterResourceFee))
			return total
		}, { cleanFeeSum: new Decimal(0), sewageFeeSum: new Decimal(0), resourceFeeSum: new Decimal(0) })
		// 清水总额 + 水资源总额
		const totalAmount = new Decimal(orderAmount)
		const yyyy = formatDate(new Date(), 'yyyy')
		const MM = formatDate(new Date(), 'MM')
		const dd = formatDate(new Date(), 'dd')
		let LODOP = getLodop()
		if (ladderType !== '非阶梯') {
			// 阶梯小票
			const invoiceSource = 12
			const orderTemplate = await http.restGet(`api/invoice/template/getByType`, invoiceSource)
			const priceData = waterUseKindDetailList.map(({ ladderLevel, cleanWaterFee }) => ({
				[`ladder${ladderLevel}Count`]: new Decimal(0),
				[`ladder${ladderLevel}Price`]: cleanWaterFee,
				[`ladder${ladderLevel}Amount`]: new Decimal(0)
			})).reduce((total, item) => ({ ...total, ...item }), {})

			chargeDetailList.forEach(({ ladderLevel, waterAmount, cleanWaterFee }) => {
				priceData[`ladder${ladderLevel}Count`] = waterAmount
				priceData[`ladder${ladderLevel}Amount`] = cleanWaterFee
			})
			const param = {
				customerName,
				customerAddress: address,
				cardNo: cno,
				waterUseKindName: name,
				meterAmount: cleanFeeSum,
				totalAmount: totalAmount,
				...priceData,
				resourceCount: MathUtil.comparedTo(resourceFeeSum, new Decimal(0)) > 0 ? waterAmount : 0,
				resourcePrice: waterResourceFee,
				resourceAmount: resourceFeeSum,
				sewageCount: MathUtil.comparedTo(sewageFeeSum, new Decimal(0)) > 0 ? waterAmount : 0,
				sewagePrice: sewageFee,
				sewageAmount: sewageFeeSum,
				amountBig: digitalUppercase(totalAmount),
				tollName: createPersonName,
				yyyy, MM, dd
			}
			const template = Object.entries(param)
				.map(([k, v]) => ({ key: k, value: v == null ? '' : v }))
				.reduce((total, { key, value }) => total.replace(key, value), orderTemplate.data.content)
			eval(template)
			let result = LODOP.PREVIEW();
			if (result) {
				dispatch(getRecordSave({
					invoiceSource: 0,
					sourceId: resultData.id,
					customerId: data.customerId,
					templateId: orderTemplate.data.id,
					invoiceAmount: totalAmount,
					payTime: resultData.createTime,
					payUid: resultData.createId,
				}))
			}
		} else {
			// 非阶梯小票
			const invoiceSource = 11
			const orderTemplate = await http.restGet(`api/invoice/template/getByType`, invoiceSource)
			const param = {
				customerName,
				cardNo: cno,
				waterUseKindName: name,
				waterAmount,
				amountSmall: totalAmount,
				cleanPrice: cleanWaterFee,
				resourcePrice: waterResourceFee,
				cleanAmount: cleanFeeSum,
				resourceAmount: resourceFeeSum,
				amountBig: digitalUppercase(totalAmount),
				customerAddress: address,
				tollName: createPersonName,
				sewageCount: MathUtil.comparedTo(sewageFeeSum, new Decimal(0)) > 0 ? waterAmount : 0,
				sewagePrice: sewageFee,
				sewageAmount: sewageFeeSum,
				yyyy, MM, dd
			}
			const template = Object.entries(param)
				.map(([k, v]) => ({ key: k, value: v == null ? '' : v }))
				.reduce((total, { key, value }) => total.replace(key, value), orderTemplate.data.content)
			eval(template)
			let result = LODOP.PREVIEW();
			if (result) {
				dispatch(getRecordSave({
					invoiceSource: 0,
					sourceId: resultData.id,
					customerId: data.customerId,
					templateId: orderTemplate.data.id,
					invoiceAmount: totalAmount,
					payTime: resultData.createTime,
					payUid: resultData.createId,
				}))
			}
		}
	}
	getOrderStatus (id) {
		http.restGet(`/api/charge/order/getOrderById`, id).then(res => {
			console.log('res:', res);
			if (res.code === 0 && res.data && res.data.status === '已支付待刷卡') {
				this.writeCard(res.data)
				message.success('缴费写卡成功');
				if (res.data.invoice) {
					this.printReceipt(res.data)
				}
				this.closeCodePayModal()
				this.props.closeModel()
			} else if (res.code === 0 && res.data && res.data.status === '已完成') {
				message.success('缴费成功');
				if (res.data.invoice) {
					this.printReceipt(res.data)
				}
				this.closeCodePayModal()
				this.props.closeModel()

			} else if (res.code === 0 && res.data && (res.data.status === '已退款' || res.data.status === '红冲')) {
				message.error('订单已退款');
				this.closeCodePayModal()
				this.props.closeModel()
			}
		})
	}
	renderCodePayModal () {
		const { codePayUrl, userInfo } = this.props;
		const { payDTO, countdown, intervalId } = this.state;
		//动倒计时
		if (codePayUrl && !this.state.intervalId) {
			const id = setInterval(() => {
				this.setState(prevState => ({
					countdown: prevState.countdown - 1
				}), () => {
					if (this.state.countdown <= 0) {
						clearInterval(this.state.intervalId);
						this.props.setState([{ 'key': 'codePayUrl', 'value': null }]);
						this.setState({ intervalId: null });
					}
					if (this.state.countdown % 3 == 0 && this.props.orderId && this.props.codePayUrl) {
						this.getOrderStatus(this.props.orderId)
					}
				});
			}, 1000);
			this.setState({ intervalId: id });
		}

		const footer = (
			<Fragment>
				<Button key="submit" type="primary"
					onClick={() => this.codePayComplete()}>确定</Button>
				<Button key="back" onClick={() => { this.closeCodePayModal() }}>取消</Button>
			</Fragment>
		);
		return (
			<Modal title="扫码支付" visible={codePayUrl != null}
				footer={footer}
				onCancel={() => { this.closeCodePayModal() }}>
				<div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: '100%' }}>
					<p>请使用扫码工具扫描以下二维码进行支付：</p>
					<div style={{ fontSize: '20px', marginBottom: '10px' }}>{`为用户${userInfo.cno}买水${payDTO ? payDTO.waterAmount : ''}吨`}</div>
					<div style={{ fontSize: '20px', marginBottom: '10px' }}>{`${payDTO ? payDTO.reallyFee : ''}元`}</div>

					<div id="qr-code-container"></div> {/* 用于显示二维码的容器 */}
					<div style={{ marginTop: '20px', fontSize: '18px' }}>剩余时间: {Math.floor(countdown / 60)}分 {countdown % 60}秒</div>
				</div>
			</Modal>)
	}
	//渲染修改弹窗
	renderUpdateName () {
		const { form, loading } = this.props;
		const { detail, type, visible } = this.state;
		const { getFieldDecorator } = form;
		const footer = (
			<Fragment>
				<Button key="submit" type="primary" loading={loading}
					onClick={() => this.handleClick(type, detail)}>提交</Button>
				<Button key="back" onClick={() => this.editModal(false)}>取消</Button>
			</Fragment>
		);
		return (
			<Modal title={type === 'name' ? '修改用户姓名' : '修改用户电话'} visible={visible}
				onCancel={() => this.editModal(false)}
				footer={footer}>
				<Form {...constants.formItemLayout}>
					<Row>
						<Col>
							{
								type === 'name' ?
									<FormItem label={'修改用户姓名:'}>
										{getFieldDecorator('name', {
											initialValue: detail ? detail.name : ''
										})
											(<Input placeholder="请输入" />)}
									</FormItem> :
									<FormItem label={'修改用户电话:'}>
										{getFieldDecorator('contactPhone', {
											initialValue: detail ? detail.contactPhone : ''
										})
											(<Input placeholder="请输入" />)}
									</FormItem>
							}
						</Col>
					</Row>
				</Form>
			</Modal>
		);
	}
	componentWillUnmount () {
		console.log('清除倒计时');
		clearInterval(this.state.intervalId);
	}
	//渲染非卡表表单
	renderNoIcForm () {
		const { form, userInfo, calcFee, calcWater, newNetReceipts, current } = this.props;
		const { getFieldDecorator } = form;
		const fixedQuantity = Boolean(userInfo && userInfo.fixedQuantity)
		return (
			<Form {...constants.formItemLayout}>
				<Row>

					<Descriptions size="small" bordered>
						<Descriptions.Item label="用户编号:">{userInfo && userInfo.cno}</Descriptions.Item>
						<Descriptions.Item label="用户名称:">
							<div style={{ display: 'flex', justifyContent: 'space-between' }}>
								{userInfo && userInfo.name}
								<Icon
									type="edit"
									style={{ cursor: 'pointer', textAlign: 'right', color: 'blue' }}
									onClick={() => this.updateNameModal(userInfo)}
								/>
							</div>
						</Descriptions.Item>
						<Descriptions.Item label="用户电话:">
							<div style={{ display: 'flex', justifyContent: 'space-between' }}>
								{userInfo && userInfo.contactPhone}
								<Icon
									type="edit"
									style={{ cursor: 'pointer', textAlign: 'right', color: 'blue' }}
									onClick={() => this.updatePhoneModal(userInfo)}
								/>
							</div>
						</Descriptions.Item>
						<Descriptions.Item label="用水性质:">{userInfo && userInfo.waterUseKindName}</Descriptions.Item>
						<Descriptions.Item label="水费单价：">{userInfo && userInfo.price}</Descriptions.Item>
						<Descriptions.Item label="用水分类:">{userInfo && userInfo.waterUseKindType}</Descriptions.Item>
						<Descriptions.Item label="户籍人数:">{userInfo && userInfo.population}</Descriptions.Item>
						<Descriptions.Item label="户数:">{userInfo && userInfo.numberOfHouse}</Descriptions.Item>
						<Descriptions.Item label="水表编号:">{userInfo && userInfo.waterMeterNo}</Descriptions.Item>
						<Descriptions.Item label="水表类型:">{userInfo && userInfo.waterMeterType}</Descriptions.Item>
						<Descriptions.Item
							label="水表厂家:">{userInfo && userInfo.waterMeterManufacturer}</Descriptions.Item>
						<Descriptions.Item label="水表种类:">{userInfo && userInfo.waterMeterKindType}</Descriptions.Item>
						<Descriptions.Item label="用户地址:">{userInfo && userInfo.address}</Descriptions.Item>
						<Descriptions.Item label="阶梯使用量:">{userInfo && userInfo.houseLadderUse}</Descriptions.Item>
						<Descriptions.Item label="阶梯总量:">{userInfo && userInfo.houseLadderSum}</Descriptions.Item>
						<Descriptions.Item label="账户余额:">{userInfo && this.realBalance(userInfo.houseAccountBalance, userInfo.realTimeWaterFee)}</Descriptions.Item>
						<Descriptions.Item label="户预存水量:">{userInfo.housePreStoreWaterAmount}</Descriptions.Item>
						<Descriptions.Item
							label="已购阶梯量:">{userInfo && userInfo.houseEstimateLadderUse}</Descriptions.Item>
						<Descriptions.Item
							label="购买阶梯总量:">{userInfo && userInfo.houseEstimateLadderSum}</Descriptions.Item>
						<Descriptions.Item label="欠费金额:">
							<span style={{ color: 'red' }}>{userInfo && userInfo.houseOweMoney}</span>
						</Descriptions.Item>

					</Descriptions>

				</Row>
				<br />
				<h3>购水缴费</h3>
				<Row>
					<Col span={8}>
						<FormItem label="购买水量(吨):">
							{getFieldDecorator('waterAmount', {
								initialValue: calcWater ? calcWater : '',
								rules: [
									{ required: !fixedQuantity, message: '请输入最新读数' },
									{
										validator: (rule, value, callback) => {
											if (fixedQuantity) { // 固定用户不需要检查
												callback()
												return
											}
											if (!Number.isInteger(value)) {
												callback('该项只能输入整数');
											} else {
												callback();
											}
										}
									}
								]
							})(<InputNumber disabled={fixedQuantity} max={*********} style={{ width: 150 }} />)}
							{fixedQuantity ? undefined : <Button
								type="primary"
								onClick={() => this.calculateFee(userInfo)}
								style={{ marginLeft: 10 }}
							>
								按水量计算金额
							</Button>}
						</FormItem>
					</Col>

					<Col span={8}>
						<FormItem label="计算金额(元):">
							{getFieldDecorator('calcAmount', {
								initialValue: fixedQuantity ? Math.abs(userInfo.houseOweMoney) : calcFee ? calcFee : ''
							})(<InputNumber style={{ width: 200 }} precision={2} disabled />)}
						</FormItem>
					</Col>

					<Col span={8}>
						<FormItem label="优惠金额(元):">
							{getFieldDecorator('discountedAmount', {
								initialValue: 0,
								rules: [{ required: true, message: '必填' }]
							})(
								<InputNumber
									min={0}
									max={9999}
									onBlur={(v) => {
										this.handleCount(v.target.value);
									}}
									style={{ width: 200 }}
									disabled
									precision={2}
								/>
							)}
						</FormItem>
					</Col>

					<Col span={8}>
						<FormItem label="应收金额(元):">
							{getFieldDecorator('receivableAmount', {
								initialValue: fixedQuantity ? Math.abs(userInfo.houseOweMoney) : calcFee
									? Number(calcFee).toFixed(2) <= 0
										? 0
										: Number(calcFee).toFixed(2)
									: ''
							})(<InputNumber style={{ width: 200 }} disabled precision={2} />)}
						</FormItem>
					</Col>

					<Col span={8}>
						<FormItem label="收款金额(元):">
							{getFieldDecorator('payAmount', {
								initialValue: fixedQuantity ? Math.abs(userInfo.houseOweMoney) : undefined,
								rules: [{ required: true, message: '必填' }]
							})(
								<InputNumber
									max={*********}
									precision={2}
									disabled
									style={{ width: 200 }}
									onChange={(v) => {
										this.handleCount(v, userInfo);
									}}
								/>
							)}
							{/*<Button
								type="primary"
								onClick={() => this.calculateWaterAmount(userInfo)}
								style={{ marginLeft: 10 }}
							>
								按金额计算水量
							</Button>*/}
						</FormItem>
					</Col>

					<Col span={8}>
						<FormItem label="剩余金额(元):">
							{getFieldDecorator('netReceipts', { initialValue: newNetReceipts ? newNetReceipts : 0 })(
								<InputNumber disabled style={{ width: 200 }} precision={2} />
							)}
						</FormItem>
					</Col>

					<Col span={8}>
						<FormItem label="实收金额(元):">
							{getFieldDecorator('reallyFee', {
								initialValue: fixedQuantity ? Math.abs(userInfo.houseOweMoney) : undefined,
							})(
								<InputNumber disabled style={{ width: 200 }} precision={2} />
							)}
						</FormItem>
					</Col>

					<Col span={8}>
						<FormItem label="本次余额(元):">
							{getFieldDecorator('thisBalance', { initialValue: newNetReceipts ? newNetReceipts : 0 })(
								<InputNumber disabled style={{ width: 200 }} precision={2} />
							)}
						</FormItem>
					</Col>

					<Col span={24} pull={4}>
						<FormItem label="备注:">
							{getFieldDecorator('remark')(<TextArea rows={3} palceholder="请输入备注" />)}
						</FormItem>
					</Col>

					<Col span={24} pull={4}>
						<FormItem label="付款方式:">
							{getFieldDecorator('chargeWay', {
								initialValue: 0,
								rules: [{ required: true, message: '必选' }]
							})(
								<Group>
									<Radio value={0}>现金</Radio>
									<Radio value={9}>扫码支付</Radio>
									<Radio value={2}>POS机</Radio>
									<Radio value={3}>银行回单</Radio>
									<Radio value={12}>代金券</Radio>
									<Radio value={14}>挂账</Radio>
								</Group>
							)}
						</FormItem>
					</Col>
					{this.props.form.getFieldValue('chargeWay') === 3 ? (
						<Col span={12}>
							<FormItem label="转账日期:">
								{getFieldDecorator('wireTransferNo', { rules: [{ required: true, message: '必填' }] })(
									<Input precision={2} style={{ width: 200 }} />
								)}
							</FormItem>
						</Col>
					) : (
						void 0
					)}

					{
						this.props.form.getFieldValue('chargeWay') === 12 ?
							<Col span={12}>
								<FormItem label="水券号:">
									{getFieldDecorator('wireTransferNo', { rules: [{ required: true, message: '必填' }] })
										(<Input precision={2} style={{ width: 200 }} />)}
								</FormItem>
							</Col> : void (0)

					}


					<Col span={24} pull={4}>
						<FormItem label="打印收据:">
							{getFieldDecorator('invoice', {
								initialValue: 1,
								rules: [{ required: true, message: '必选' }]
							})(
								<Group>
									<Radio value={1}>打印</Radio>
									<Radio value={0}>不打印</Radio>
								</Group>
							)}
						</FormItem>
					</Col>

					{
						this.props.form.getFieldValue('invoice') === 1 ?
							<div>
								<Col span={10}>
									<FormItem label="水费票号:">
										{getFieldDecorator('cleanNo')(<Input precision={2} maxLength={100} style={{ width: 200 }} />)}
									</FormItem>
								</Col>
								<Col span={10}>
									<FormItem label="污水票号:">
										{getFieldDecorator('sewageNo',)(<Input precision={2} maxLength={100} style={{ width: 200 }} />)}
									</FormItem>
								</Col>
							</div>
							: void (0)
					}

					{
						this.props.form.getFieldValue('invoice') === 0 ?
							<div>
								<Col span={10}>
									<FormItem label="水费发票号码:">
										{getFieldDecorator('cleanNo')(<Input precision={2} maxLength={100} style={{ width: 200 }} />)}
									</FormItem>
								</Col>
								<Col span={10}>
									<FormItem label="污水发票号码:">
										{getFieldDecorator('sewageNo')(<Input precision={2} maxLength={100} style={{ width: 200 }} />)}
									</FormItem>
								</Col>
							</div>
							: void (0)
					}
				</Row>

				<Row style={{paddingBlock: '10px'}}>
					<Col span={24} align="center">
						<Button type="primary" className="btn" onClick={this.handlePay}>
							缴费
						</Button>
						<Button className="btn" onClick={this.handleCancel}>
							取消
						</Button>
					</Col>
				</Row>
			</Form>
		);
	}

	//导出
	export () {
		const { startTime, endTime } = this.state;
		if (startTime || endTime) {
			let startTime = moment(startTime).format('YYYY-MM-DD hh:mm:ss');
			let endTime = moment(endTime).format('YYYY-MM-DD hh:mm:ss');
			let url = `${process.env.API_ROOT}/api/cm/customer/exportBillAndOrder`;
			const params = { startTime, endTime };
			http.export(url, params, (res) => {
				if (res && res.size > 0 && res.type === 'text/xml') {
					const blob = new Blob([res]);
					const downloadElement = document.createElement('a');
					const href = window.URL.createObjectURL(blob);
					const fileName = moment(startTime).format('YYYY-MM-DD') + '_' + moment(endTime).format('YYYY-MM-DD') + '.xlsx';
					downloadElement.href = href;
					downloadElement.download = fileName;
					document.body.appendChild(downloadElement);
					downloadElement.click();
					document.body.removeChild(downloadElement);
					window.URL.revokeObjectURL(href);
				} else {
					message.error('导出失败');
				}
			});
		} else {
			message.error('请选择日期！');
		}
	}

	// 开户时间选择
	getDate = (date, dateString) => {
		if (dateString.length === 2) {
			this.setState({
				startTime: dateString[0],
				endTime: dateString[1]
			});
		}
	};

	render () {
		const { loading } = this.state;
		const { userInfo } = this.props;
		return (
			<Spin spinning={loading} tip="Loading...">
				{
					userInfo.waterMeterType === 'IC卡表' ?
						this.renderIcForm()
						:
						this.renderNoIcForm()
				}
				{this.renderUpdateName()}
				{this.renderCodePayModal()}
			</Spin>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('payment');
	return {
		fields: data.fields,
		datalist: data.datalist, // 列表数据
		total: data.total, // 总条数
		visible: data.visible, // 是否显示弹窗
		areaSelect: data.areaSelect, // 片区
		userInfo: data.userInfo, // 用户信息
		calcFee: data.calculateFee,  //计算金额
		calcWater: data.calculateWaterAmount, //计算水量
		newNetReceipts: data.netReceipts,  //剩余金额
		formwork: data.formwork, //发票模板
		current: data.current,  //票据号码
		priceDetail: data.priceDetail,   //发票单价
		codePayUrl: data.codePayUrl,
		orderId: data.orderId,
	};
};

const mapDispatchToProps = dispatch => ({
	setState: data => dispatch(actionCreators.setState(data)), // 设置redux值
	pay: async (data, endLoad, refresh, invoice, newPriceDetail) => dispatch(await actionCreators.pay(data, endLoad, refresh, invoice, newPriceDetail)), // 缴费
	codePay: async (data, action) => dispatch(await actionCreators.codePay(data, action)),
	list: (data) => dispatch(actionCreators.list(data)), // 获取列表
	calculateFee: (data, action) => dispatch(actionCreators.calculateFee(data, action)),  //计算金额
	calculateWaterAmount: (data, userInfo) => dispatch(actionCreators.calculateWaterAmount(data, userInfo)),  //钱算量
	getCurrentNo: () => dispatch(actionCreators.getCurrentNo()),  //获取用户领用号
	updateName: (data, editModal) => dispatch(actionCreators.updateName(data, editModal)),            //修改用户姓名
	updateContactPhone: (data, editModal) => dispatch(actionCreators.updateContactPhone(data, editModal)),
	getPriceDetail: (data) => dispatch(actionCreators.getPriceDetail(data)),
	getICDetail: (id) => dispatch(actionCreators.getICDetail(id)),
});
export default connect(
	mapStateToProps,
	mapDispatchToProps
)((Form.create())(Pay));
