import React, { Component, Fragment } from 'react';
import { actionCreators } from './store';
import { connect } from 'react-redux';
import { Tabs, TreeSelect, Modal, Button, Col, Form, Input, Row, Table, Tag, message } from 'antd';
import { readCard, xtRead, huaxuRead } from '@/utils/cardUtil';
import Pay from './components/pay';
import { constants } from '$utils';
import OrderTable from './components/orderTable';
import BillTable from './components/billTable';
import './index.scss';
import { userStatusColorMap } from '@/constants/colorStyle';

const FormItem = Form.Item;
const { TreeNode } = TreeSelect;
const { TabPane } = Tabs;

const defaultSearchForm = {
	cno: '', // 用户编号
	hno: '', //用户户号
	cardNo: '', // 用户卡号
	waterMeterNo: '', //水表编号
	name: null, // 用户名称
	areaId: '', // 片区
	address: null, // 用户地址
	detail: null, // 缴费查看详情
	areaCode: null
};

class Index extends Component {

	constructor(props) {
		super(props);
		this.state = {
			page: 1,
			pageSize: 10,
			searchForm: defaultSearchForm,
			loading: false,
			resultHuaxu: null
		};
	}

	componentDidMount() {
		this.props.getAreaSelect();
		document.addEventListener('keypress', this.handleEnterKey);
	}

	componentWillUnmount() {
		document.removeEventListener('keypress', this.handleEnterKey);
	}

	// 回车事件
	handleEnterKey = (e) => {
		if (e.keyCode === 13) {
			this.getList();
		}
	};

	// 获取列表
	getList = () => {
		const { searchForm, page, pageSize } = this.state;
		this.props.list(Object.assign({ ...searchForm }, { page, pageSize }));
	};

	// 列表分页
	handlePageChange = page => {
		this.setState({ page }, () => {
			this.getList();
		});
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState({ page: 1, pageSize: size }, () => {
			this.getList();
		});
	};

	//搜索
	handleSeacrch = () => {
		this.setState({ page: 1 }, () => {
			this.getList();
		});
	};

	//重置搜索
	handleReset = () => {
		const { searchForm } = this.state;
		searchForm.cardNo = null;
		searchForm.areaCode = null;
		this.setState({ page: 1, searchForm: Object.assign({}, defaultSearchForm) });
	};

	// 缴费
	handlePay = record => {
		if (record.status === '销户') {
			message.error('销户用户无法缴费');
			return;
		}
		const { pageSize } = this.state;
		if (record.waterMeterType === 'IC卡表') {
			if (record.waterMeterManufacturer === '扬州恒信') {
				let userCard = readCard();
				let cardType = userCard.substring(1, 2);
				let cardNo = null;
				if (cardType === '3' || cardType === '4') {
					cardNo = userCard.substring(2, 12);
				} else {
					cardNo = userCard.substring(2, 10);
				}
				if (cardNo === record.cardNo) {
					if (true) {
						let state = ''; //刷卡状态
						if (record.waterMeterKindType === '预付费2') {
							state = userCard.substr(-4);
							if (state != '0000') {
								alert('上次缴费未刷表');
							} else {
								this.props.setState([{ key: 'visible', value: true }]);
							}
						} else if (record.waterMeterKindType === '预付费4442') {
							state = userCard.substring(16, 20);
							if (state != '0000') {
								alert('上次缴费未刷表');
							} else {
								this.props.setState([{ key: 'visible', value: true }]);
							}
						} else if (record.waterMeterKindType === '阶梯2') {
							state = userCard.substring(20, 26);
							if (parseInt(state, 16) != 0) {
								alert('上次缴费未刷表');
							} else {
								this.props.setState([{ key: 'visible', value: true }]);
							}
						} else {
							this.props.setState([{ key: 'visible', value: true }]);
						}
					} else {
						alert('上次缴费有未刷卡的订单,请先刷卡');
					}
				} else {
					alert('该卡卡号和当前用户卡号不一致！请检查');
				}
			} else if (record.waterMeterManufacturer === '河南新天') {
				let resultXT = xtRead();
				resultXT = resultXT.split(',');
				if (parseInt(resultXT[2], 16) !== Number(record.cardNo)) {
					alert('该卡卡号和当前用户卡号不一致！请检查');
				} else {
					if (parseInt(resultXT[9], 16) !== 0) {
						alert('上次缴费未刷表,请先刷表');
					} else {
						this.props.setState([{ key: 'visible', value: true }]);
					}
				}
			} else if (record.waterMeterManufacturer === '华旭') {
				let resultHuaxu = huaxuRead();
				console.log('resultHuaxu: ', resultHuaxu);
				let cardNo;
				if (resultHuaxu[0] == 9) {
					cardNo = resultHuaxu[3].substring(6, resultHuaxu[3].length);
				} else {
					cardNo = resultHuaxu[26].substring(6, resultHuaxu[26].length);
				}
				if (cardNo !== record.cardNo) {
					alert('该卡卡号和当前用户卡号不一致！请检查');
				} else {
					if (resultHuaxu[2] == 0) {
						alert('上次缴费未刷表,请先刷表');
					} else {
						this.props.setState([{ key: 'visible', value: true }]);
					}
				}
			}
		} else {
			this.props.setState([{ key: 'visible', value: true }]);

		}
		let params = {};
		params.page = 1;
		params.pageSize = pageSize;
		params.customerId = record.id; // 用户id
		params.outStatus = 1;
		this.props.getUserInfo(params.customerId, this.verification); // 获取用户基本信息
		this.props.getOrderList(params); // 获取订单列表
		this.props.getBillList(params); // 获取账单列表
		this.props.getTotal({ customerId: record.id });
		this.props.getBillTotal({ hno: record.hno });
		this.setState({ detail: record });
	};

	// 补水
	handleSupplementWaterAmount = record => {
		if (record.status === '销户') {
			message.error('销户用户无法补水');
			return;
		}
		if (record.waterMeterType === 'IC卡表') {
			if (record.waterMeterManufacturer === '扬州恒信') {
				let userCard = readCard();
				let cardType = userCard.substring(1, 2);
				let cardNo = null;
				if (cardType === '3' || cardType === '4') {
					cardNo = userCard.substring(2, 12);
				} else {
					cardNo = userCard.substring(2, 10);
				}
				if (cardNo === record.cardNo) {
					if (true) {
						let state = ''; //刷卡状态
						if (record.waterMeterKindType === '预付费2') {
							state = userCard.substr(-4);
							if (state != '0000') {
								alert('上次缴费未刷表');
							} else {
								this.props.setState([{ key: 'supplementWaterAmountVisible', value: true }]);
							}
						} else if (record.waterMeterKindType === '预付费4442') {
							state = userCard.substring(16, 20);
							if (state != '0000') {
								alert('上次缴费未刷表');
							} else {
								this.props.setState([{ key: 'supplementWaterAmountVisible', value: true }]);
							}
						} else if (record.waterMeterKindType === '阶梯2') {
							state = userCard.substring(20, 26);
							if (parseInt(state, 16) != 0) {
								alert('上次缴费未刷表');
							} else {
								this.props.setState([{ key: 'supplementWaterAmountVisible', value: true }]);
							}
						} else {
							this.props.setState([{ key: 'supplementWaterAmountVisible', value: true }]);
						}
					} else {
						alert('上次缴费有未刷卡的订单,请先刷卡');
					}
				} else {
					alert('该卡卡号和当前用户卡号不一致！请检查');
				}
			} else if (record.waterMeterManufacturer === '河南新天') {
				let resultXT = xtRead();
				resultXT = resultXT.split(',');
				if (parseInt(resultXT[2], 16) !== Number(record.cardNo)) {
					alert('该卡卡号和当前用户卡号不一致！请检查');
				} else {
					if (parseInt(resultXT[9], 16) !== 0) {
						alert('上次缴费未刷表,请先刷表');
					} else {
						this.props.setState([{ key: 'supplementWaterAmountVisible', value: true }]);
					}
				}
			} else if (record.waterMeterManufacturer === '华旭') {
				let resultHuaxu = huaxuRead();
				this.setState({ resultHuaxu: resultHuaxu });
				let cardNo;
				if (resultHuaxu[0] == 9) {
					cardNo = resultHuaxu[3].substring(6, resultHuaxu[3].length);
				} else {
					cardNo = resultHuaxu[26].substring(6, resultHuaxu[26].length);
				}
				if (cardNo !== record.cardNo) {
					alert('该卡卡号和当前用户卡号不一致！请检查');
				} else {
					if (resultHuaxu[2] == 0) {
						alert('上次缴费未刷表,请先刷表');
					} else {
						this.props.setState([{ key: 'supplementWaterAmountVisible', value: true }]);
					}
				}
			}
		} else {
			this.props.setState([{ key: 'supplementWaterAmountVisible', value: true }]);
		}
		this.setState({ detail: record, customerId: record.id });
	};

	// 开始加载
	startLoad = () => {
		this.setState({ loading: true });
	};

	// 结束加载
	endLoad = () => {
		setTimeout(() => {
			this.setState({ loading: false }, () => this.props.setState([{
				key: 'supplementWaterAmountVisible',
				value: false
			}]));
		}, 500);
		this.getList();
	};

	// 提交补水
	handleSupplementWaterAmountSubmit = () => {
		this.props.form.validateFields((err, values) => {
			if (values.tonne == null || values.tonne <= 0) {
				message.error('水量必须大于0！');
			} else {
				this.startLoad(); // 加载
				const { detail, customerId, resultHuaxu } = this.state;
				//校验
				let flag = this.checkICFlag(detail);
				if (flag) {
					const { formwork, priceDetail } = this.props;
					let newPriceDetail = priceDetail && priceDetail.waterUseKindDetailList ? (priceDetail.waterUseKindDetailList)[0] : {};
					values.customerId = customerId;
					let invoice = { invoice: false, formwork: formwork, current: '' };
					this.props.supplementWaterAmount(values, this.endLoad, this.props.refresh, invoice, newPriceDetail, resultHuaxu);
				}
			}
		});
	};

	checkICFlag = (detail) => {
		if (detail.waterMeterType === 'IC卡表') {
			if (detail.waterMeterManufacturer === '扬州恒信') {
				let userCard = readCard();
				let cardType = userCard.substring(1, 2);
				let cardNo = null;
				if (cardType === '3' || cardType === '4') {
					cardNo = userCard.substring(2, 12);
				} else {
					cardNo = userCard.substring(2, 10);
				}
				if (cardNo === detail.cardNo) {
					if (true) {
						let state = ''; //刷卡状态
						if (detail.waterMeterKindType === '预付费2') {
							state = userCard.substr(-4);
							if (state != '0000') {
								alert('上次缴费未刷表');
								return false;
							} else {
								return true;
							}
						} else if (detail.waterMeterKindType === '预付费4442') {
							state = userCard.substring(16, 20);
							if (state != '0000') {
								alert('上次缴费未刷表');
								return false;
							} else {
								return true;
							}
						} else if (detail.waterMeterKindType === '阶梯2') {
							state = userCard.substring(20, 26);
							if (parseInt(state, 16) != 0) {
								alert('上次缴费未刷表');
								return false;
							} else {
								return true;
							}
						} else {
							return true;
						}
					} else {
						alert('上次缴费有未刷卡的订单,请先刷卡');
						return false;
					}
				} else {
					alert('该卡卡号和当前用户卡号不一致！请检查');
					return false;
				}
			} else if (detail.waterMeterManufacturer === '河南新天') {
				let resultXT = xtRead();
				resultXT = resultXT.split(',');
				if (parseInt(resultXT[2], 16) !== Number(detail.cardNo)) {
					alert('该卡卡号和当前用户卡号不一致！请检查');
					return false;
				} else {
					if (parseInt(resultXT[9], 16) !== 0) {
						alert('上次缴费未刷表,请先刷表');
						return false;
					} else {
						return true;
					}
				}
			} else if (detail.waterMeterManufacturer === '华旭') {
				let resultHuaxu = huaxuRead();
				let cardNo;
				if (resultHuaxu[0] == 9) {
					cardNo = resultHuaxu[3].substring(6, resultHuaxu[3].length);
				} else {
					cardNo = resultHuaxu[26].substring(6, resultHuaxu[26].length);
				}
				if (cardNo !== detail.cardNo) {
					alert('该卡卡号和当前用户卡号不一致！请检查');
					return false;
				} else {
					if (resultHuaxu[2] == 0) {
						alert('上次缴费未刷表,请先刷表');
						return false;
					} else {
						return true;
					}
				}
			}
		} else {
			return true;
		}
	};

	verification = (userInfo) => {
		if (userInfo.contactPhone === null || userInfo.name === null) {
			alert('请补全用户信息');
		}
	};

	// 取消弹窗
	handleCancel = () => {
		this.child.handleCancel();
	};

	// 取消补水弹窗
	handleSupplementWaterAmountCancel = () => {
		this.props.setState([{ key: 'supplementWaterAmountVisible', value: false }]);
		this.getList();
	};

	//渲染搜索
	_renderSearchForm = () => {
		const { searchForm } = this.state;
		const { areaSelect } = this.props;
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					<Col span={8}>
						<FormItem label={'用户编号:'}>
							<Input placeholder="请输入用户编号" value={searchForm.cno}
										 onChange={v => this.setState({ searchForm: { ...searchForm, cno: v.target.value } })}/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'用户卡号:'}>
							<Input placeholder="请输入用户卡号" value={searchForm.cardNo}
										 onChange={v => this.setState({ searchForm: { ...searchForm, cardNo: v.target.value } })}/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'水表编号:'}>
							<Input placeholder="请输入水表编号" value={searchForm.waterMeterNo}
										 onChange={v => this.setState({ searchForm: { ...searchForm, waterMeterNo: v.target.value } })}/>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={8}>
						<FormItem label={'用户名称:'}>
							<Input placeholder="请输入用户名称" value={searchForm.name}
										 onChange={v => this.setState({ searchForm: { ...searchForm, name: v.target.value } })}/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'片区:'}>
							<TreeSelect style={{ width: '100%' }} showSearch
													filterTreeNode={(inputValue, treeNode) => treeNode.props.title.indexOf(inputValue) > -1}
													dropdownStyle={{ maxHeight: 400, overflow: 'auto' }} value={searchForm.areaId}
													placeholder="请选择片区" allowClear treeDefaultExpandedKeys={[100]}
													onChange={v => this.setState({ searchForm: { ...searchForm, areaId: v } })}>
								{areaSelect && this._renderTreeNode(areaSelect)}
							</TreeSelect>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'用户地址:'}>
							<Input placeholder="请输入用户地址" value={searchForm.address}
										 onChange={v => this.setState({ searchForm: { ...searchForm, address: v.target.value } })}/>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={8}>
						<FormItem label={'用户户号:'}>
							<Input placeholder="请输入用户户号" value={searchForm.hno}
										 onChange={v => this.setState({ searchForm: { ...searchForm, hno: v.target.value } })}/>
						</FormItem>
					</Col>
				</Row>
				<Col span={24} align="right">
					<Button className="searchBtn" type="primary" onClick={this.handleSeacrch}>
						搜索
					</Button>
					<Button className="searchBtn" type="default" onClick={this.handleReset}>
						重置
					</Button>
					<Button className="searchBtn" type="default" onClick={() => this.readCard()}>
						读卡
					</Button>
				</Col>
			</Form>
		);
	};

	//递归渲染片区选项
	_renderTreeNode = treeData => {
		return treeData.map(tree => {
			if (tree.children) {
				return (
					<TreeNode key={tree.id} title={tree.name} value={tree.id}>
						{this._renderTreeNode(tree.children)}
					</TreeNode>
				);
			} else {
				return <TreeNode key={tree.id} title={tree.name} value={tree.id}/>;
			}
		});
	};

	//读卡
	readCard() {
		const SYS_NAME = process.env.SYS_NAME;
		if (SYS_NAME === 'qhd') {
			let resultHuaxu = (SZHXMETERCARD_Web.HXCD_4442_DisposeData_Web('1|1|', '')).split('|');
			console.log('payment read resultHuaxu: ', resultHuaxu);
			let cardNo = null;
			let areaCode = null;
			if (resultHuaxu[0] > 0) {
				if (resultHuaxu[0] == 9) {
					cardNo = resultHuaxu[3].substring(6, resultHuaxu[3].length);
				} else {
					cardNo = resultHuaxu[26].substring(6, resultHuaxu[26].length);
				}
				console.log('cardNo: ', cardNo);
			} else {
				let result = readCard();
				let cardType = result.substring(1, 2);
				if (cardType === '3' || cardType === '4') {
					cardNo = result.substring(2, 12);
				} else {
					cardNo = result.substring(2, 10);
				}
				if (cardType === '1') {
					areaCode = result.substring(12, 18);
				} else if (cardType === '2') {
					areaCode = result.substring(10, 14);
				}
			}
			if (cardNo && cardNo.length >= 8) {
				const { searchForm } = this.state;
				searchForm.cardNo = cardNo;
				searchForm.areaCode = areaCode;
				searchForm.cno = '';
				searchForm.hno = '';
				searchForm.waterMeterNo = '';
				searchForm.name = null;
				searchForm.areaId = '';
				searchForm.address = null;
				this.setState({ ...searchForm }, () => {
					this.getList();
				});
			}
		} else if (SYS_NAME === 'ewkq') {
			let hxdll = '';
			if (document.getElementById('hxdll')) {
				hxdll = document.getElementById('hxdll');
			}
			let sunfs = '';
			if (document.getElementById('sunfs')) {
				sunfs = document.getElementById('sunfs');
			}
			//读恒信卡
			let readtype = hxdll.chk_card();
			//读取新天卡
			sunfs.openport(3, 146);
			let resultXT = sunfs.readcard();
			if (resultXT === 1) {
				resultXT = sunfs.fsdata;
				sunfs.closeport();
			} else {
				resultXT = null;
				sunfs.closeport();
			}
			if (readtype > 3 && resultXT === null) {
				let result = readCard();
				let cardType = result.substring(1, 2);
				let cardNo = null;
				if (cardType === '3' || cardType === '4') {
					cardNo = result.substring(2, 12);
				} else {
					cardNo = result.substring(2, 10);
				}
				let areaCode = null;
				if (cardType === '1') {
					areaCode = result.substring(12, 18);
				} else if (cardType === '2') {
					areaCode = result.substring(10, 14);
				}
				if (cardNo && cardNo.length >= 8) {
					this.props.form.setFieldsValue({ cardNo: cardNo });
					const { searchForm } = this.state;
					searchForm.cardNo = cardNo;
					searchForm.areaCode = areaCode;
					this.setState({ ...searchForm }, () => {
						this.getList();
					});
				}
				sunfs.closeport();
			} else if (resultXT !== null && readtype < 3) {
				let cardType = resultXT.split(',')[1];
				if (cardType === '04') {
					let cardNo = resultXT.split(',')[2];
					const { searchForm } = this.state;
					searchForm.cardNo = parseInt(cardNo, 16);
					this.setState({ ...searchForm }, () => {
						this.getList();
					});
				} else {
					message.error('非用户卡！');
				}
				sunfs.closeport();
			} else if (readtype > 3 && resultXT) {
				message.error('读卡器上存在多张卡请检查！');
				sunfs.closeport();
			} else {
				message.error('读卡器无卡,或该卡为空卡请检查！');
				sunfs.closeport();
			}
		}
	}

	render() {
		const { page, pageSize, detail, loading } = this.state;
		const { datalist, total, visible, supplementWaterAmountVisible, orderCalc, billCalc, form } = this.props;
		const { getFieldDecorator } = form;
		const columns = [
			{
				title: '用户编号',
				dataIndex: 'cno',
				key: 'cno',
				align: 'center'
			},
			{
				title: '用户名称',
				dataIndex: 'name',
				key: 'name',
				align: 'center'
			},
			{
				title: '片区',
				dataIndex: 'areaName',
				key: 'areaName',
				align: 'center',
				sorter: (a, b) => a.areaName - b.areaName
			},
			{
				title: '用户地址',
				dataIndex: 'address',
				key: 'address',
				align: 'center',
				sorter: (a, b) => a.address && a.address.localeCompare(b.address)
			},
			{
				title: '水表类型',
				dataIndex: 'waterMeterType',
				key: 'waterMeterType',
				align: 'center'
			},
			{
				title: '水表种类',
				dataIndex: 'waterMeterKindType',
				key: 'waterMeterKindType',
				align: 'center'
			},
			{
				title: '用水性质',
				dataIndex: 'waterUseKindType',
				key: 'waterUseKindType',
				align: 'center'
			},
			{
				title: '用水分类',
				dataIndex: 'waterUseKindName',
				key: 'waterUseKindName',
				align: 'center'
			},
			{
				title: '用户户号',
				dataIndex: 'hno',
				key: 'hno',
				align: 'center'
			},
			{
				title: '用户卡号',
				dataIndex: 'cardNo',
				key: 'cardNo',
				align: 'center'
			},
			{
				title: '水表编号',
				dataIndex: 'waterMeterNo',
				key: 'waterMeterNo',
				align: 'center'
			},
			{
				title: '用户状态',
				dataIndex: 'status',
				key: 'status',
				align: 'center',
				fixed: 'right',
				width: 80,
				render: text => {
					return (
						<Tag color={userStatusColorMap.get(text)}>
							{text}
						</Tag>
					);
				}
			},
			{
				title: '操作',
				width: 200,
				fixed: 'right',
				align: 'center',
				render: (text, record, index) => {
					return (
						<span>
							<Button permission={React.$pmn('REVENUE:PAY_COST_MANAGEMENT:PAY_COST_COUNTER:PAYMENT')}
											title="缴费" className="btn" type="primary" size="small" icon="pay-circle"
											onClick={() => this.handlePay(record)}>
								缴费
							</Button>
							{record.waterMeterType === 'IC卡表' &&
								<Button permission={React.$pmn('REVENUE:PAY_COST_MANAGEMENT:PAY_COST_COUNTER:SUPPLEMENT_WATER_AMOUNT')}
												title="补水" className="btn" type="primary" size="small" icon="audit"
												onClick={() => this.handleSupplementWaterAmount(record)}>
									补水
								</Button>
							}
						</span>
					);
				}
			}
		];
		const paginationProps = {
			current: page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: total => {
				return `共 ${total} 条数据 `;
			},
			onChange: page => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		return (
			<div className="shadow-radius">
				<h1>柜台缴费</h1>
				<Row>{this._renderSearchForm()}</Row>
				<Row className="main">
					<Table bordered scroll={{ x: 2800 }} columns={columns} rowKey={(record) => record.id} dataSource={datalist}
								 pagination={paginationProps}/>
				</Row>
				<Modal className="payModal" title="柜台缴费" centered={true} maskClosable={false} closable={false} keyboard={false}
							 visible={visible} onCancel={this.handleCancel} footer={null} destroyOnClose={true}>
					<Tabs>
						<TabPane tab="基本信息" key="0">
							<Pay detail={detail} refresh={this.handleReset}/>
						</TabPane>
						<TabPane tab="订单记录" key="1">
							<OrderTable getOrderList={data => this.props.getOrderList(data)} detail={detail} orderCalc={orderCalc}/>
						</TabPane>
						<TabPane tab="账单记录" key="2">
							<BillTable getBillList={data => this.props.getBillList(data)} detail={detail} billCalc={billCalc}/>
						</TabPane>
					</Tabs>
				</Modal>

				<Modal visible={supplementWaterAmountVisible} title="补水" centered={true} maskClosable={false} closable={false}
							 keyboard={false}
							 onCancel={this.handleSupplementWaterAmountCancel}
							 footer={
								 <Fragment>
									 <Button type="primary" loading={loading} onClick={this.handleSupplementWaterAmountSubmit}>
										 提交
									 </Button>
									 <Button onClick={this.handleSupplementWaterAmountCancel}>取消</Button>
								 </Fragment>} destroyOnClose={true}>
					<Form  {...constants.formItemLayout}>
						<Row>
							<Col span={24}>
								<FormItem label="水量：">
									{getFieldDecorator(`tonne`, {
										rules: [
											{ required: true, message: '水量必填' },
											{
												validator: (rule, value, callback) => {
													if (value == null || value === '') {
														callback();
													} else {
														if (value <= 0) {
															callback('水量必须大于0');
														} else {
															callback();
														}
													}
												}
											}]
									})
									(<Input placeholder="水量"/>)}
								</FormItem>
							</Col>
						</Row>
					</Form>
				</Modal>
			</div>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('payment');
	return {
		fields: data.fields,
		datalist: data.datalist, // 列表数据
		total: data.total, // 总条数
		visible: data.visible, // 是否显示弹窗
		supplementWaterAmountVisible: data.supplementWaterAmountVisible, // 是否显实补水弹窗
		areaSelect: data.areaSelect, // 片区
		orderCalc: data.orderCalc, //订单统计
		billCalc: data.billCalc,   //账单统计
		userInfo: data.userInfo,   //账单统计
		formwork: data.formwork, //发票模板
		priceDetail: data.priceDetail   //发票单价
	};
};
const mapDispatchToProps = dispatch => ({
	setState: data => dispatch(actionCreators.setState(data)), // 设置redux值
	list: data => dispatch(actionCreators.list(data)), // 获取列表
	getAreaSelect: data => dispatch(actionCreators.getAreaSelect()), // 获取片区
	getUserInfo: (data, callback) => dispatch(actionCreators.getUserInfo(data, callback)),//获取用户基本信息
	getOrderList: data => dispatch(actionCreators.getOrderList(data)), // 获取订单列表
	getBillList: data => dispatch(actionCreators.getBillList(data)),// 获取账单列表
	getTotal: data => dispatch(actionCreators.getTotal(data)),       //订单统计
	getBillTotal: data => dispatch(actionCreators.getBillTotal(data)),      //获取远传表账单统计
	supplementWaterAmount: (data, endLoad, refresh, invoice, newPriceDetail, resultHuaxu) => dispatch(actionCreators.supplementWaterAmount(data, endLoad, refresh, invoice, newPriceDetail, resultHuaxu)) // 缴费
});
export default connect(mapStateToProps, mapDispatchToProps)(Form.create()(Index));
