import { dataType } from '$utils';

import * as actionTypes from './constants';

const defaultState = {
	fields: null,
	areaSelect: [], // 片区选择框
	customer: {}, //用户选择框
	waterUseKindSelect: [], // 用水性质选择框
	createUidSelect: [],
	chargeFee: 0, // 收费金额
	codePayUrl: null,
	inspectFeeId: null
};

// 重要提示： 请做好各代码段的注释！！！
export default (state = defaultState, action) => {
	switch (action.type) {
		// 回填组件传给redux的值
		case actionTypes.SET_STATE:
			let _data = action.data;
			if (dataType(_data) === 'Object') {
				state[_data.key] = _data.value;
			}
			if (dataType(_data) === 'Array') {
				_data.forEach(item => {
					state[item.key] = item.value;
				});
			}
			return { ...state };
		// 获取片区选择框
		case actionTypes.GET_AREA_SELECT:
			state.areaSelect = action.data;
			return { ...state };
		// 获取用户选择框
		case actionTypes.GET_CUSTOMER:
			state.customer = action.data;
			return { ...state };
		// 获取用水性质选择框
		case actionTypes.GET_WATER_USE_KIND_SELECT:
			state.waterUseKindSelect = action.data;
			return { ...state };
		// 计算收费金额
		case actionTypes.GET_CHARGE_FEE:
			state.chargeFee = action.data;
			return { ...state };
		case actionTypes.GET_CREATE_UID_SELECT:
			state.createUidSelect = action.data;
			return { ...state };
		// 添加特抄收费
		case actionTypes.ADD_SPECIAL_FEE:
			return { ...state };
		case actionTypes.CODE_PAY:
			state.codePayUrl = action.data.codeUrl;
			state.inspectFeeId = action.data.inspectFeeId;
			return { ...state };
		default:
			return state;
	}
};
