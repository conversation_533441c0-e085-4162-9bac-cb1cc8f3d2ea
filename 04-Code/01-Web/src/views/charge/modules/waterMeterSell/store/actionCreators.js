import http from '$http';
import * as actionTypes from './constants';
import api from "./api";

// 数据回填
const payload = (type, data) => ({ type, data });

// 组件操作redux数据状态
const setState = data => ({ type: actionTypes.SET_STATE, data });


const save = (data) => {
		return http.post(api.save, data);
};

const listWaterMeter = () => {
		return http.get(api.listWaterMeter);
}

// 获取发票模板
const getByType = (data) => {
	return http.restGet(api.getByType, data);
}

// 获取水表类型详情，获取单价
const getUnitPrice = (data) => {
	return http.restGet(api.getUnitPrice, data);
}

export {
		setState,
		payload,
		save,
		listWaterMeter,
		getByType,
		getUnitPrice
};
