import './index.scss';

import React, {
	Component,
	Fragment,
} from 'react';

import http from '$http';
import { constants } from '$utils';
import {
	Button,
	Col,
	DatePicker,
	Descriptions,
	Divider,
	Form,
	Icon,
	Input,
	message,
	Modal,
	Row,
	Select,
	Table,
	Tag,
	TreeSelect,
} from 'antd';
import { connect } from 'react-redux';

import { specialStatusColorMap } from '@/constants/colorStyle';
import { CHARGE_WAY } from '@/constants/order';

import { FEE_CAUSE } from './constants';
import { actionCreators } from './store';

const { confirm } = Modal;
const FormItem = Form.Item;
const { Option } = Select;
const ButtonGroup = Button.Group;
const { RangePicker } = DatePicker;
const { TreeNode } = TreeSelect;
const InputGroup = Input.Group;

const inspectFeeDetailColumns = [
	{
		title: '用水分类',
		dataIndex: 'waterUseKindName',
		align: 'center'
	},
	{
		title: '水量',
		dataIndex: 'waterAmount',
		align: 'center'
	}
];
const defaultSearchForm = {
	customerName: undefined,
	feeCause: undefined, // 收费原因
	status: 2, // 状态：待收费、核减中、已完成
	createUid: undefined, // 操作员
	chargeUid: undefined, // 收费员
	chargeTimeStart: undefined, // 收费时间
	chargeTimeEnd: undefined, // 收费时间
	createTimeStart: undefined, // 创建时间开始
	createTimeEnd: undefined, // 创建时间结束
	cno: undefined
};

class Index extends Component {
	constructor(props) {
		super(props);
		this.state = {
			expand: false, // 搜索项过多的时候，隐藏部分
			page: 1,
			pageSize: 10,
			searchForm: defaultSearchForm,
			invoiceVisible: false
		};
	}
	handleElePrint (record) {
		this.props.getDetailByCustomer(record.customerId, () => {
			this.setState({ invoiceVisible: true, record: record });

		});
	}
	handleOpenInvoice (record) {
		if (record.customerId) {
			http.restGet(`api/cm/vat/getByCustomer`, record.customerId).then(res => {
				if (res.code === 0) {
					res.data.address ? res.data.remark = res.data.address : '';
					this.setState({
						vatInfo: res.data,
					}, () => {
						this.setState({ invoiceVisible: true, record: record, })
					})
				}
			})
		} else {

			this.setState({ invoiceVisible: true, record: record, vatInfo: { name: record.customerName, address: record.address, remark: record.address } })
		}
	}
	//关闭修改付款方式弹窗
	cancelHandleElePrint () {
		this.setState({ invoiceVisible: false });
	}
	summitHandleElePrint (record) {
		const { vatInfo } = this.state;
		this.props.form.validateFields((err, values) => {
			console.log('this.props:', values);
			if (!err) {
				let vat = {
					vatNumber: values.vatNumber,
					type: values.type,
					name: values.name,
					address: values.address,
					openBank: values.openBank,
					bankAccount: values.bankAccount,
					phone: values.phone,
					generalTaxpayer: 0,
					email: values.email,
					id: vatInfo.id || null
				}
				let param = { id: record.id, remark: values.remark || values.address, vat: vat }
				this.props.openXsdInvoice(param, this.getList, values, this.getList);
				this.cancelHandleElePrint();
			}
		});
	}
	_handleElePrint () {
		const { form } = this.props;
		const { record, invoiceVisible, vatInfo } = this.state;
		const { getFieldDecorator } = form;

		const footer = (
			<Fragment>
				<Button key="submit" type="primary" onClick={() => this.summitHandleElePrint(record)}>提交</Button>
				<Button key="back" onClick={() => this.cancelHandleElePrint()}>取消</Button>
			</Fragment>
		);
		return invoiceVisible &&
			<Modal title="发票开具" destroyOnClose={true} maskClosable={true} visible={invoiceVisible}
				onCancel={() => this.cancelHandleElePrint()}
				footer={footer}>
				<Form {...constants.formItemLayout}>
					<Row>
						<Col>
							<FormItem label={'税号:'}>
								{getFieldDecorator('vatNumber', { initialValue: vatInfo.vatNumber, rules: [{ required: false }] })(
									<Input
										placeholder="请输税号"
									/>
								)}
							</FormItem>
						</Col>
						<Col>
							<FormItem label={'开具类型:'}>
								{getFieldDecorator('type', {
									initialValue: vatInfo.type ? (vatInfo.type === "个人" ? 0 : 1) : undefined,
									rules: [
										{
											required: true,
											message: '请选择开具类型'
										}
									]
								})(
									<Select placeholder="请选择开具类型">
										<Option key={0} value={0}>
											个人
										</Option>
										<Option key={1} value={1}>
											企业
										</Option>
									</Select>
								)}
							</FormItem>
						</Col>
						<Col>
							<FormItem label={'发票抬头:'}>
								{getFieldDecorator('name', { initialValue: vatInfo.name, rules: [{ required: true }] })(
									<Input
										placeholder="请输发票抬头"
									/>
								)}
							</FormItem>
						</Col>
						<Col>
							<FormItem label={'用户地址:'}>
								{getFieldDecorator('address', { initialValue: vatInfo.address, rules: [{ required: false }] })(
									<Input
										placeholder="请输用户地址"
									/>
								)}
							</FormItem>
						</Col>
						<Col>
							<FormItem label={'开户银行:'}>
								{getFieldDecorator('openBank', { initialValue: vatInfo.openBank, rules: [{ required: false }] })(
									<Input
										placeholder="请输开户银行"
									/>
								)}
							</FormItem>
						</Col>
						<Col>
							<FormItem label={'银行账号:'}>
								{getFieldDecorator('bankAccount', { initialValue: vatInfo.bankAccount, rules: [{ required: false }] })(
									<Input
										placeholder="请输银行账号"
									/>
								)}
							</FormItem>
						</Col>
						<Col>
							<FormItem label={'电话:'}>
								{getFieldDecorator('phone', { initialValue: vatInfo.phone, rules: [{ required: false }] })(
									<Input
										placeholder="请输入电话"
									/>
								)}
							</FormItem>
						</Col>
						<Col>
							<FormItem label={'邮箱:'}>
								{getFieldDecorator('email', { initialValue: vatInfo.email, rules: [{ required: false }] })(
									<Input
										placeholder="请输入邮箱"
									/>
								)}
							</FormItem>
						</Col>
						{/* <Col>
							<FormItem label={'是否一般纳税人:'}>
								{getFieldDecorator('generalTaxpayer', {
									initialValue: vatInfo.generalTaxpayer,
									rules: [
										{
											required: true,
											message: '是否一般纳税人'
										}
									]
								})(
									<Select placeholder="是否一般纳税人">
										<Option key={1} value={true}>
											是
										</Option>
										<Option key={0} value={false}>
											否
										</Option>
									</Select>
								)}
							</FormItem>
						</Col> */}
						<Col>
							<FormItem label={'备注:'}>
								{getFieldDecorator('remark', { initialValue: vatInfo.remark, rules: [{ required: false }] })(
									<Input
										placeholder="请输入备注"
									/>
								)
								}
							</FormItem>
						</Col>
					</Row>
				</Form>
			</Modal>;
	}

	componentDidMount () {
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前  fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields && isTags) {
			this.setState(
				{
					...fields.state
				},
				() => {
					this.props.form.setFieldsValue({
						...fields.form
					});
				}
			);
		} else {
			this.getList();
			this.queryListSelect();
		}
	}

	componentWillUnmount () {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			});
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			});
		}
	}

	//获取操作员
	queryListSelect () {
		this.props.queryListSelect();
		this.props.getTollUidSelect();
	}

	// 获取列表
	getList = () => {
		const { page, pageSize, searchForm } = this.state;
		this.props.list(Object.assign({ ...searchForm }, { page, pageSize }));
		this.props.getStatistics(Object.assign({ ...searchForm }, { page, pageSize }));
	};

	// 列表分页
	handlePageChange = page => {
		this.setState(
			{
				page
			},
			() => {
				this.getList();
			}
		);
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState(
			{
				page: 1,
				pageSize: size
			},
			() => {
				this.getList();
			}
		);
	};

	// 搜索功能
	handleSearch = () => {
		this.setState(
			{
				page: 1
			},
			() => {
				this.getList();
			}
		);
	};

	// 重置搜索
	handleReset = () => {
		this.props.form.setFieldsValue({ rangPicker: [undefined, undefined] });
		this.setState(
			{
				searchForm: Object.assign({}, defaultSearchForm)
			},
			() => {
				this.getList();
			}
		);
	};

	// 查看详情
	handleView = record => {
		this.setState(
			{
				modalType: 'view',
				modalTitle: '查看详情'
			},
			() => {
				// 设置redux visible 为true 显示弹窗
				this.props.setState([
					{ key: 'visible', value: true },
					{ key: 'detail', value: null }
				]);
				// 获取详情
				this.props.getDetail(record.id);
			}
		);
	};

	handlePrint = record => {
		confirm({
			title: '操作确认',
			content: '确定要打印小票吗？',
			okText: '确定',
			cancelText: '取消',
			onOk: () => this.props.printInvoice(record)
		});
	};

	// 删除
	handleDel = record => {
		const _this = this;
		confirm({
			title: '确认操作',
			content: '是否取消此收费吗？',
			okText: '确定',
			okType: 'danger',
			cancelText: '取消',
			onOk () {
				_this.props.del(record.id, _this.getList);
			}
		});
	};

	// 取消弹窗
	handleCancel = () => {
		this.props.setState([
			{ key: 'visible', value: false },
			{ key: 'detail', value: null }
		]);
	};

	//订单时间查询条件
	getOrderDate = (date, dateString) => {
		if (dateString.length === 2) {
			this.setState({
				searchForm: { ...this.state.searchForm, chargeTimeStart: dateString[0], chargeTimeEnd: dateString[1] }
			});
		}
	};

	//创建时间查询条件
	getCreateDate = (date, dateString) => {
		if (dateString.length === 2) {
			this.setState({
				searchForm: { ...this.state.searchForm, createTimeStart: dateString[0], createTimeEnd: dateString[1] }
			});
		}
	};

	updateRemark (record) {
		this.props.form.validateFields(err => {
			if (!err) {
				let param = {
					id: record.id,
					remark: this.props.form.getFieldValue('remark')
				};
				this.props.updateRemark(param, () => {
					message.success('修改成功');
					this.setState({ showUpdateRemark: false });
					this.handleSearch();
				});
			}
		});
	}

	invoiceHandlePrint (record, type) {
		this.props.form.validateFields(err => {
			if (!err) {
				let param = {
					orderId: record.id,
					cleanNo: this.props.form.getFieldValue('cleanNo'),
					sewageNo: this.props.form.getFieldValue('sewageNo')
				};
				if (type == 0) {
					param.invoice = 1;
				}
				if (type == 1) {
					param.taxInvoice = 2;
				}
				this.props.submitInvoice(param, () => {
					this.setState({ inVisible: false });
				});
			}
		});
	}

	_xiaopiaoPrint () {
		const { form } = this.props;
		const { record, inVisible, type, title } = this.state;
		const { getFieldDecorator } = form;
		const footer = (
			<Fragment>
				<Button key="submit" type="primary" onClick={() => this.invoiceHandlePrint(record, type)}>
					提交
				</Button>
				<Button key="back" onClick={() => this.setState({ inVisible: false })}>
					取消
				</Button>
			</Fragment>
		);
		return (
			inVisible && (
				<Modal title={title} destroyOnClose={true} maskClosable={true} visible={inVisible} onCancel={() => this.setState({ inVisible: false })} footer={footer}>
					<Form {...constants.formItemLayout}>
						<Row>
							<Col>
								<FormItem label={'水票号:'}>{getFieldDecorator('cleanNo')(<Input placeholder="请输入水票号" />)}</FormItem>
							</Col>
							<Col>
								<FormItem label={'污水号:'}>{getFieldDecorator('sewageNo')(<Input placeholder="请输入污水号" />)}</FormItem>
							</Col>
						</Row>
					</Form>
				</Modal>
			)
		);
	}
	_updateRemark () {
		const { form } = this.props;
		const { record, showUpdateRemark } = this.state;
		const { getFieldDecorator } = form;
		const footer = (
			<Fragment>
				<Button key="submit" type="primary" onClick={() => this.updateRemark(record)}>
					提交
				</Button>
				<Button key="back" onClick={() => this.setState({ showUpdateRemark: false })}>
					取消
				</Button>
			</Fragment>
		);
		return (
			showUpdateRemark && (
				<Modal title="修改备注" destroyOnClose={true} maskClosable={true} visible={showUpdateRemark} onCancel={() => this.setState({ showUpdateRemark: false })} footer={footer}>
					<Form {...constants.formItemLayout}>
						<Row>
							<Col>
								<FormItem label={'备注:'}>{getFieldDecorator('remark', { rules: [{ required: true, message: '请输入备注' }] })(<Input placeholder="备注" />)}</FormItem>
							</Col>
						</Row>
					</Form>
				</Modal>
			)
		);
	}

	// 渲染搜索
	_renderSearchForm = () => {
		const { form, createList, tollUidSelect } = this.props;
		const { searchForm } = this.state;
		const { getFieldDecorator } = form;
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					<Col span={8}>
						<FormItem label={'用户名称:'}>
							<Input
								placeholder="请输入用户名称"
								value={searchForm.customerName}
								onChange={v => {
									this.setState({
										searchForm: {
											...searchForm,
											customerName: v.target.value
										}
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'用户编号:'}>
							<Input
								placeholder="请输入用户编号"
								value={searchForm.cno}
								onChange={v => {
									this.setState({
										searchForm: {
											...searchForm,
											cno: v.target.value
										}
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'操作员:'}>
							<Select
								placeholder="请选择操作员"
								allowClear
								value={searchForm.createUid}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, createUid: v } });
								}}
							>
								{createList.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'收费员:'}>
							<Select
								placeholder="请选择收费员"
								allowClear
								value={searchForm.chargeUid}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, chargeUid: v } });
								}}
							>
								{tollUidSelect.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'状态:'}>
							<Select
								placeholder="请选择"
								value={searchForm.status}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, status: v } });
								}}
							>
								{constants.InspectFeeStatusEnum.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'收费时间:'}>{getFieldDecorator('rangPicker', { rules: [{ type: 'array' }] })(<RangePicker onChange={this.getOrderDate} placeholder={['开始时间', '结束时间']} />)}</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'创建时间:'}>{getFieldDecorator('createTimeChangePicker', { rules: [{ type: 'array' }] })(<RangePicker onChange={this.getCreateDate} placeholder={['开始时间', '结束时间']} />)}</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'收费原因:'}>
							<Select
								placeholder="请选择"
								value={searchForm.feeCause}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, feeCause: v } });
								}}
							>
								{FEE_CAUSE.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>

					<Col span={8}>
						<FormItem label={'付款方式:'}>
							<Select
								placeholder="请选择"
								allowClear
								value={searchForm.chargeWay}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, chargeWay: v }
									});
								}}
							>
								{CHARGE_WAY.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>

					<Col span={8}>
						<FormItem label={'水票号:'}>
							<InputGroup>
								<Input
									value={searchForm.cleanNoStart}
									className="noBorderRight"
									style={{ width: '40%' }}
									placeholder="开始水票号"
									onChange={v => {
										this.setState({
											searchForm: { ...searchForm, cleanNoStart: v.target.value }
										});
									}}
								/>
								<Input className="midIpt noBorderLeft noBorderRight" style={{ width: '20%' }} placeholder="~" disabled />
								<Input
									value={searchForm.cleanNoEnd}
									className="noBorderLeft"
									style={{ width: '40%' }}
									placeholder="结束水票号"
									onChange={v => {
										this.setState({
											searchForm: { ...searchForm, cleanNoEnd: v.target.value }
										});
									}}
								/>
							</InputGroup>
						</FormItem>
					</Col>

					<Col span={8}>
						<FormItem label={'污水号:'}>
							<InputGroup>
								<Input
									value={searchForm.sewageNoStart}
									className="noBorderRight"
									style={{ width: '40%' }}
									placeholder="开始污水号"
									onChange={v => {
										this.setState({
											searchForm: { ...searchForm, sewageNoStart: v.target.value }
										});
									}}
								/>
								<Input className="midIpt noBorderLeft noBorderRight" style={{ width: '20%' }} placeholder="~" disabled />
								<Input
									value={searchForm.sewageNoEnd}
									className="noBorderLeft"
									style={{ width: '40%' }}
									placeholder="结束污水号"
									onChange={v => {
										this.setState({
											searchForm: { ...searchForm, sewageNoEnd: v.target.value }
										});
									}}
								/>
							</InputGroup>
						</FormItem>
					</Col>
				</Row>

				<Col span={24} align="right">
					<FormItem>
						<Button type="primary" onClick={this.handleSearch}>
							搜索
						</Button>
						&nbsp;&nbsp;&nbsp;&nbsp;
						<Button type="default" onClick={this.handleReset}>
							重置
						</Button>
					</FormItem>
				</Col>
			</Form>
		);
	};

	// 渲染操作按钮
	_renderOperationButton = () => {
		return (
			<Col>
				<Button className="searchBtn" type="primary" onClick={() => this.export()}>
					<Icon type="download" />
					导出稽查收费记录
				</Button>
			</Col>
		);
	};

	// 渲染详情弹出框
	_renderDetailModal = () => {
		const { detail, visible } = this.props;
		const inspectFeeDetailList = detail.inspectFeeDetailList;
		const chargeDetailList = detail.chargeDetailList;
		const chargeDetailColumns = [
			{
				title: '用水分类',
				dataIndex: 'waterUseKindType',
				align: 'center'
			},
			{
				title: '用水性质',
				dataIndex: 'waterUseKindName',
				align: 'center'
			},
			{
				title: '阶梯名称',
				dataIndex: 'ladderName',
				align: 'center'
			},
			{
				title: '阶梯用水量',
				dataIndex: 'waterAmount',
				align: 'center'
			},
			{
				title: '清水费',
				dataIndex: 'cleanWaterFee',
				align: 'center'
			},
			{
				title: '污水费',
				dataIndex: 'sewageFee',
				align: 'center'
			},
			{
				title: '水资源税',
				dataIndex: 'waterResourceFee',
				align: 'center'
			},
			{
				title: '其他费用',
				dataIndex: 'otherFee',
				align: 'center'
			}
		];
		return (
			visible && (
				<Modal className="special-record-modal" title="详情" visible={visible} onCancel={this.handleCancel} footer={null}>
					<Form {...constants.formItemLayout}>
						<Row>
							<Divider dashed orientation="left">
								基本信息
							</Divider>
							<Descriptions bordered>
								<Descriptions.Item label="用户名称：">{detail && detail.customerName}</Descriptions.Item>
								<Descriptions.Item label="用户编号：">{detail && detail.cno}</Descriptions.Item>
								<Descriptions.Item label="用户地址：">{detail && detail.address}</Descriptions.Item>
								<Descriptions.Item label="收费编号：">{detail && detail.inspectFeeNo}</Descriptions.Item>
								<Descriptions.Item label="收费金额：">{detail && detail.feeAmount}</Descriptions.Item>
								<Descriptions.Item label="财务年月：">{detail && detail.period}</Descriptions.Item>
								<Descriptions.Item label="付款方式：">{detail && detail.chargeWay}</Descriptions.Item>
								<Descriptions.Item label="收费时间：">{detail && detail.createTime}</Descriptions.Item>
								<Descriptions.Item label="收费原因：">{detail && detail.feeCause}</Descriptions.Item>
								<Descriptions.Item label="原因说明：">{detail && detail.reasonRemark}</Descriptions.Item>
								<Descriptions.Item label="状态：">{detail && detail.status}</Descriptions.Item>
							</Descriptions>
						</Row>
						<Divider dashed orientation="left">
							收费内容
						</Divider>
						<Row className="main">
							<Table bordered columns={inspectFeeDetailColumns} rowKey={() => Math.random()} dataSource={inspectFeeDetailList} />
						</Row>
						<Divider dashed orientation="left">
							费用明细
						</Divider>
						<Row className="main">
							<Table bordered columns={chargeDetailColumns} rowKey={() => Math.random()} dataSource={chargeDetailList} />
						</Row>
						<Row>
							<Col span={24} align="center">
								<Button type="default" onClick={this.handleCancel}>
									关闭
								</Button>
							</Col>
						</Row>
					</Form>
				</Modal>
			)
		);
	};
	//修改付款方式
	_renderPayWay () {
		const { form } = this.props;
		const { record, payVisible } = this.state;
		const { getFieldDecorator } = form;
		const footer = (
			<Fragment>
				<Button key="submit" type="primary" onClick={() => this.editPayWay(record)}>
					确定
				</Button>
				<Button key="back" onClick={() => this.payWayClose()}>
					取消
				</Button>
			</Fragment>
		);
		return (
			payVisible && (
				<Modal title="修改付款方式" destroyOnClose={true} maskClosable={true} visible={payVisible} onCancel={() => this.payWayClose()} footer={footer}>
					<Form {...constants.formItemLayout}>
						<Row>
							<Col>
								<FormItem label={'付款方式:'}>
									{getFieldDecorator('chargeWay', { rules: [{ required: true, message: '请选择付款方式' }] })(
										<Select placeholder="请选择">
											{constants.payWayUpdate.map((item, index) => {
												return (
													<Option key={index} value={item.value}>
														{item.label}
													</Option>
												);
											})}
										</Select>
									)}
								</FormItem>
							</Col>
						</Row>
					</Form>
				</Modal>
			)
		);
	}
	//导出
	export () {
		const { searchForm } = this.state;
		let url = `${process.env.API_ROOT}/api/charge/inspectFee/exportInspectFee`;
		http.export(url, searchForm, res => {
			const fileName = '稽查收费记录_' + new Date().getTime() + '.xlsx';
			const blob = new Blob([res]);
			if (window.navigator && window.navigator.msSaveOrOpenBlob) {
				window.navigator.msSaveOrOpenBlob(blob, fileName);
				return;
			}
			const downloadElement = document.createElement('a');
			const href = window.URL.createObjectURL(blob);
			downloadElement.href = href;
			downloadElement.download = fileName;
			document.body.appendChild(downloadElement);
			downloadElement.click();
			document.body.removeChild(downloadElement);
			window.URL.revokeObjectURL(href);
		});
	}
	//打开修改付款方式弹窗
	handlePayWay (record) {
		this.setState({ payVisible: true, record: record });
	}

	//关闭修改付款方式弹窗
	payWayClose () {
		this.setState({ payVisible: false });
	}

	//修改付款方式
	editPayWay (record) {
		this.props.form.validateFields((error, values) => {
			if (!error) {
				values.id = record.id;
				this.props.charge(values, this.getList, () => this.payWayClose());
				this.payWayClose();
			}
		});
	}
	render () {
		const { page, pageSize } = this.state;
		const { dataList, total, statistics } = this.props;
		const columns = [
			{
				title: '收费编号',
				dataIndex: 'inspectFeeNo',
				align: 'center'
			},
			{
				title: '用户名称',
				dataIndex: 'customerName',
				align: 'center'
			},
			{
				title: '用户编号',
				dataIndex: 'cno',
				align: 'center'
			},
			{
				title: '用户地址',
				dataIndex: 'address',
				align: 'center'
			},
			{
				title: '收费原因',
				dataIndex: 'feeCause',
				align: 'center'
			},
			{
				title: '收费金额',
				dataIndex: 'feeAmount',
				align: 'center'
			},
			{
				title: '状态',
				dataIndex: 'status',
				key: 'status',
				align: 'center',
				render: text => {
					return <Tag color={specialStatusColorMap.get(text)}>{text}</Tag>;
				}
			},
			{
				title: '收费时间',
				dataIndex: 'chargeTime',
				align: 'center'
			},
			{
				title: '收费部门',
				dataIndex: 'departmentName',
				align: 'departmentName'
			},
			{
				title: '创建时间',
				dataIndex: 'createTime',
				align: 'center'
			},
			{
				title: '收费员',
				dataIndex: 'chargePersonName',
				align: 'center'
			},
			{
				title: '操作员',
				dataIndex: 'createPersonName',
				align: 'center'
			},
			{
				title: '原因说明',
				dataIndex: 'reasonRemark',
				align: 'center'
			},
			{
				title: '备注',
				dataIndex: 'remark',
				align: 'center'
			},
			{
				title: '付款方式',
				dataIndex: 'chargeWay',
				align: 'chargeWay'
			},
			{
				title: '付款时间',
				dataIndex: 'wireTransferNo',
				align: 'chargeWay'
			},
			{
				title: '小票状态',
				key: 'invoice',
				align: 'center',
				render: (text, record, index) => {
					return record.invoice ? '已打印' : '未打印';
				}
			},
			{
				title: '税票状态',
				dataIndex: 'taxInvoice',
				key: 'taxInvoice',
				align: 'center'
			},

			{
				title: '水票号',
				dataIndex: 'cleanNo',
				key: 'cleanNo',
				align: 'center'
			},

			{
				title: '污水号',
				dataIndex: 'sewageNo',
				key: 'sewageNo',
				align: 'center'
			},
			{
				title: '操作',
				key: 'operation',
				width: 250,
				align: 'center',
				fixed: 'right',
				render: (text, record, index) => {
					return (
						<span>
							<ButtonGroup>
								<Button title="查看" className="btn" type="primary" size="small" icon="eye" onClick={() => this.handleView(record)} />
								<Button title="打印" className="btn" type="primary" size="small" icon="printer" disabled={record.status !== '已完成'} onClick={() => this.handlePrint(record)} />
								<Button title="作废" className="btn" type="danger" size="small" icon="delete" disabled={!(record.status === '已完成' || record.status === '待收费')} onClick={() => this.handleDel(record)} />

								<Button title="修改付款方式" className="btn" type="primary" size="small" icon="account-book" onClick={() => this.handlePayWay(record)} />
								<Button
									title="修改备注"
									className="btn"
									type="primary"
									size="small"
									icon="edit"
									onClick={() => {
										this.setState({ showUpdateRemark: true, record: record });
									}}
								/>
								<Button
									title="修改小票"
									className="btn"
									type="primary"
									size="small"
									icon="profile"
									onClick={() => {
										this.setState({ inVisible: true, record: record, type: 0, title: '修改小票' });
									}}
								/>
								<Button
									title="修改税票"
									className="btn"
									type="primary"
									size="small"
									icon="profile"
									onClick={() => {
										this.setState({ inVisible: true, record: record, type: 1, title: '修改税票' });
									}}
								/>

								<Button title="开蓝票" className='btn' type="primary" size="small" icon='credit-card' onClick={() => {
									this.handleOpenInvoice(record)
								}}
									disabled={!((record.status == '已支付待刷卡' || record.status == '已完成') && (!record.taxInvoice || record.taxInvoice == "未打印"))} />
							</ButtonGroup>
						</span>
					);
				}
			}
		];
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: total => {
				return `共 ${total} 条数据 `;
			},
			onChange: page => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};

		return (
			<div className="shadow-radius special-record">
				<h1>稽查收费记录</h1>
				<Row>{this._renderSearchForm()}</Row>
				<Row span={24} style={{ marginBottom: 5 }}>
					{this._renderOperationButton()}
				</Row>
				<Row>
					<Col style={{ marginBottom: 5 }}>
						稽查收费总金额: {statistics ? statistics.waterFee : ''}元 &emsp;&emsp;&emsp; 总吨数：{statistics ? statistics.tonne : ''}吨 &emsp;&emsp;&emsp; 总清水费：{statistics ? statistics.cleanWaterFee : ''}元 &emsp;&emsp;&emsp; 总污水费： {statistics ? statistics.sewageFee : ''}元
					</Col>
					<Table bordered scroll={{ x: 2300 }} columns={columns} rowKey={data => data.id} dataSource={dataList} pagination={paginationProps} />
				</Row>
				{this.props.visible && this.props.detail && this._renderDetailModal()}
				{this._xiaopiaoPrint()}
				{this._renderPayWay()}
				{this._updateRemark()}
				{this._handleElePrint()}
			</div>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('inspectRecord');
	return {
		fields: data.fields,
		dataList: data.dataList, // 列表数据
		statistics: data.statistics, // 统计信息
		areaSelect: data.areaSelect, // 片区选择框
		total: data.total, // 总条数
		visible: data.visible, // 是否显示弹窗
		detail: data.detail, // 获取详情
		createList: data.createList,
		tollUidSelect: data.tollUidSelect
	};
};
const mapDispatchToProps = dispatch => ({
	setState: data => dispatch(actionCreators.setState(data)), // 设置redux值
	list: data => dispatch(actionCreators.list(data)), // 获取列表
	charge: (data, list) => dispatch(actionCreators.charge(data, list)), // 确认收费
	getStatistics: data => dispatch(actionCreators.getStatistics(data)), // 获取统计信息
	submitInvoice: (data, ops) => dispatch(actionCreators.submitInvoice(data, ops)), //获取发票模板
	updateRemark: (data, ops) => dispatch(actionCreators.updateRemark(data, ops)), //获取发票模板
	getDetail: data => dispatch(actionCreators.detail(data)), // 获取详情
	del: (data, list) => dispatch(actionCreators.del(data, list)), // 作废
	queryListSelect: () => dispatch(actionCreators.queryListSelect()), //获取抄表员
	printInvoice: data => dispatch(actionCreators.printInvoice(data)), // 获取列表
	getTollUidSelect: () => dispatch(actionCreators.getTollUidSelect()), //获取订单创建人选择框
	openXsdInvoice: (data, list) => dispatch(actionCreators.openXsdInvoice(data, list)),
});
export default connect(mapStateToProps, mapDispatchToProps)(Form.create()(Index));
