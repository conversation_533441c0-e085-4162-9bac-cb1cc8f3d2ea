import React, { Component, Fragment } from "react";
import {
	Table,
	Form,
	Select,
	Row,
	Col,
	Input,
	Button,
	DatePicker,
	Tag,
	TreeSelect,
	message, Modal, Tabs, Descriptions
} from 'antd';
import { actionCreators } from "./store";
import { constants } from '$utils';
import { readCard, xtRead, qsRead, qsEmptyCard, qsOpenCard, qsWriteCard, sleep } from '@/utils/cardUtil';
import { WATER_METER_TYPE, WATER_METER_KIND } from '@/constants/waterMeter';
import { ORDER_SOURCE, CHARGE_WAY, CHARGE_RESULT_MAP, CHARGE_THIRD_PART_ORDER_STATUS_MAP } from '@/constants/order';
import { connect } from 'react-redux';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { TreeNode } = TreeSelect;
const InputGroup = Input.Group;


const defaultSearchForm = {
	cno: null,
	hno: null,
	cardNo: null,
	customerName: null,
	customerAddress: null,
	waterMeterNo: null,
	waterMeterType: null,
	waterMeterKindType: null,
	reallyFeeStart: null,
	reallyFeeEnd: null,
	createTimeStart: null,
	createTimeEnd: null,
	areaId: null,
	chargeWay: null,
	orderSource: null,
	status: null,
	bizId: null,
	result: null,
	areaCode: null,
};

class Index extends Component {

	constructor(props) {
		super(props);
		this.state = {
			searchWaterMeterType: null,
			expand: false, // 搜索项过多的时候，隐藏部分
			page: 1,
			pageSize: 10,
			dataList: [],
			total: 0,
			searchForm: defaultSearchForm,
			visible: false,
			refoundVisible: false,
		};
	}

	//显示高级搜索
	showMoreSearch = () => {
		this.setState({
			expand: !this.state.expand
		});
	};

	//递归渲染片区
	_renderTreeNode = treeData => {
		return treeData.map(tree => {
			if (tree.children) {
				return (
					<TreeNode key={tree.id} title={tree.name} value={tree.id}>
						{this._renderTreeNode(tree.children)}
					</TreeNode>
				);
			} else {
				return <TreeNode key={tree.id} title={tree.name} value={tree.id} />;
			}
		});
	};

	//读卡
	async readCard() {
		let cardNo, areaCode = null;
		let hxdll = '';
		if (document.getElementById('hxdll')) {
			hxdll = document.getElementById('hxdll');
		}
		let sunfs = '';
		if (document.getElementById('sunfs')) {
			sunfs = document.getElementById('sunfs');
		}
		//泰安轻松卡
		let qsResult = await qsRead()
		console.log('qsResult: ', qsResult);
		if (qsResult.errcode == 0) {
			if (qsResult.cardtype < 0) {
				message.error('非用户卡！');
			} else {
				cardNo = qsResult.usercode.toString().padStart(5, '0');
			}
		} else {
			//读取新天卡
			let openport = sunfs.openport(3, 104);
			console.log('openport: ', openport);
			sleep(300);
			let resultXT = sunfs.readcard();
			console.log('resultXT: ', resultXT);
			sunfs.closeport();
			if (resultXT === 1) {
				resultXT = sunfs.fsdata;
			} else {
				resultXT = null;
			}
			if (resultXT !== null) {
				let cardType = resultXT.split(',')[1];
				if (cardType === '0A') {
					cardNo = parseInt(resultXT.split(',')[2], 16);
				} else {
					message.error('非用户卡！');
				}
			} else {
				//读恒信卡
				let readtype = hxdll.chk_card();
				if (readtype > 3) {
					let result = readCard();
					let cardType = result.substring(1, 2);
					if (cardType === '3' || cardType === '4') {
						cardNo = result.substring(2, 12);
					} else {
						cardNo = result.substring(2, 10);
					}
					if (cardType === '1') {
						areaCode = result.substring(12, 18);
					} else if (cardType === '2') {
						areaCode = result.substring(10, 14);
					}
				}
			}
		}
		console.log('cardNo: ', cardNo);
		if (cardNo) {
			const { searchForm } = this.state;
			searchForm.cardNo = cardNo.toString().trim();
			// searchForm.areaCode = areaCode;
			this.setState({ ...searchForm }, () => {
				this.getList();
			});
		} else {
			message.error('读卡器无卡,或该卡为空卡请检查！');
		}
	}

	componentDidMount() {
		this.props.getAreaSelect();
		this.getList();
	}

	componentWillUnmount() {
	}

	handleSearch = () => {
		this.setState(
			{
				page: 1
			},
			() => {
				this.getList();
			}
		);
	}

	// 订单时间查询条件
	getCreateDate = (date, dateString) => {
		if (dateString.length === 2) {
			this.setState({
				searchForm: { ...this.state.searchForm, createTimeStart: dateString[0], createTimeEnd: dateString[1] }
			});
		}
	};

	// 重置搜索
	handleReset = () => {
		this.props.form.setFieldsValue({ rangPicker: [undefined, undefined] });
		this.setState(
			{
				searchForm: Object.assign({}, defaultSearchForm)
			},
			() => {
				this.getList();
			}
		);
	};

	// 列表分页
	handlePageChange = page => {
		this.setState(
			{
				page
			},
			() => {
				this.getList();
			}
		);
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState(
			{
				page: 1,
				pageSize: size
			},
			() => {
				this.getList();
			}
		);
	};

	// 取消弹窗
	handleCancel = () => {
		this.setState({ 'visible': false, 'detail': null })
	}

	// 渲染搜索
	_renderSearchForm = () => {
		const { form, areaSelect } = this.props;
		const { searchForm, expand, searchWaterMeterType } = this.state;
		const { getFieldDecorator } = form;
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					<Col span={8}>
						<FormItem label={'用户编号:'}>
							<Input placeholder="请输入用户编号" value={searchForm.cno}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, cno: v.target.value }
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'用户户号:'}>
							<Input placeholder="请输入用户户号" value={searchForm.hno}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, hno: v.target.value }
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'用户卡号:'}>
							<Input placeholder="请输入用户卡号" value={searchForm.cardNo}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, cardNo: v.target.value }
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'用户名称:'}>
							<Input placeholder="请输入用户名称" value={searchForm.customerName}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, customerName: v.target.value }
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'用户地址:'}>
							<Input placeholder="请输入用户地址" value={searchForm.customerAddress}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, customerAddress: v.target.value }
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'水表编号:'}>
							<Input placeholder="请输水表编号" value={searchForm.waterMeterNo}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, waterMeterNo: v.target.value }
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'水表类型:'}>
							<Select
								allowClear
								placeholder="请选择"
								value={searchForm.waterMeterType}
								onChange={v => {
									this.setState({
										searchWaterMeterType: v,
										searchForm: { ...searchForm, waterMeterType: v }
									});
								}}
							>
								{WATER_METER_TYPE.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'水表种类:'}>
							<Select
								allowClear
								placeholder="请选择"
								value={this.state.searchForm.waterMeterKind}
								onChange={v => {
									this.setState({
										searchForm: { ...this.state.searchForm, waterMeterKind: v }
									});
								}}
							>
								{WATER_METER_KIND.map((item, index) => {
									return (
										<Option key={index} value={item.value}
											disabled={(searchWaterMeterType === 0 && item.value < 3) || (searchWaterMeterType === 1 && item.value !== 0) || (searchWaterMeterType === 2 && item.value !== 1 && item.value !== 2)}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'收费时间:'}>
							{getFieldDecorator('rangPicker', { rules: [{ type: 'array' }] })(<RangePicker
								onChange={this.getCreateDate}
								placeholder={['开始时间', '结束时间']} />)}
						</FormItem>
					</Col>
				</Row>
				<Row>
					{expand ? (
						<Fragment>
							<Col span={8}>
								<FormItem label={'片区:'}>
									<TreeSelect
										style={{ width: '100%' }}
										dropdownStyle={{
											maxHeight: 400,
											overflow: 'auto'
										}} placeholder="请选择片区"
										allowClear
										treeDefaultExpandedKeys={[100]}
										value={searchForm.areaId}
										onChange={v => {
											this.setState({
												searchForm: {
													...searchForm,
													areaId: v
												}
											});
										}}>
										{this._renderTreeNode(
											areaSelect)}
									</TreeSelect>
								</FormItem>
							</Col>
							<Col span={8}>
								<FormItem label={'订单来源:'}>
									<Select
										allowClear
										placeholder="请选择"
										value={searchForm.orderSource}
										onChange={v => {
											this.setState({
												searchForm: { ...searchForm, orderSource: v }
											});
										}}
									>
										{ORDER_SOURCE.map((item, index) => {
											return (
												<Option key={index} value={item.value}>
													{item.label}
												</Option>
											);
										})}
									</Select>
								</FormItem>
							</Col>
							<Col span={8}>
								<FormItem label={'付款方式:'}>
									<Select
										placeholder="请选择"
										allowClear
										value={searchForm.chargeWay}
										onChange={v => {
											this.setState({
												searchForm: { ...searchForm, chargeWay: v }
											});
										}}
									>
										{CHARGE_WAY.map((item, index) => {
											return (
												<Option key={index} value={item.value}>
													{item.label}
												</Option>
											);
										})}
									</Select>
								</FormItem>
							</Col>
							<Col span={8}>
								<FormItem label={'状态:'}>
									<Select
										placeholder="请选择"
										allowClear
										value={searchForm.status}
										onChange={v => {
											this.setState({
												searchForm: { ...searchForm, status: v }
											});
										}}
									>
										{CHARGE_THIRD_PART_ORDER_STATUS_MAP.map((item, index) => {
											return (
												<Option key={index} value={item.value}>
													{item.label}
												</Option>
											);
										})}
									</Select>
								</FormItem>
							</Col>
							<Col span={8}>
								<FormItem label={'缴费结果:'}>
									<Select
										placeholder="请选择"
										allowClear
										value={searchForm.status}
										onChange={v => {
											this.setState({
												searchForm: { ...searchForm, status: v }
											});
										}}
									>
										{CHARGE_RESULT_MAP.map((item, index) => {
											return (
												<Option key={index} value={item.value}>
													{item.label}
												</Option>
											);
										})}
									</Select>
								</FormItem>
							</Col>
							<Col span={8}>
								<FormItem label={'记录id:'}>
									<Input placeholder="请输记录id" value={searchForm.bizId}
										onChange={v => {
											this.setState({
												searchForm: { ...searchForm, bizId: v.target.value }
											});
										}}
									/>
								</FormItem>
							</Col>
							<Col span={8}>
								<FormItem label={'缴费金额:'}>
									<InputGroup>
										<Input
											value={searchForm.reallyFeeStart}
											className="noBorderRight"
											style={{ width: '40%' }}
											placeholder="开始金额"
											onChange={v => {
												this.setState({
													searchForm: { ...searchForm, reallyFeeStart: v.target.value }
												});
											}}
										/>
										<Input className="midIpt noBorderLeft noBorderRight" style={{ width: '20%' }} placeholder="~"
											disabled />
										<Input
											value={searchForm.reallyFeeEnd}
											className="noBorderLeft"
											style={{ width: '40%' }}
											placeholder="结束金额"
											onChange={v => {
												this.setState({
													searchForm: { ...searchForm, reallyFeeEnd: v.target.value }
												});
											}}
										/>
									</InputGroup>
								</FormItem>
							</Col>

						</Fragment>
					) : null}
				</Row>
				<Col span={24} align="right">
					<FormItem>
						<Button type="primary" onClick={this.handleSearch}>
							搜索
						</Button>
						&nbsp;&nbsp;&nbsp;&nbsp;
						<Button type="default" onClick={this.handleReset}>
							重置
						</Button>
						&nbsp;&nbsp;&nbsp;&nbsp;
						<Button className="searchBtn" type="default" onClick={() => this.readCard()}>
							读卡
						</Button>
						<Button type="link" onClick={this.showMoreSearch}>
							{expand ? '关闭高级搜索' : '高级搜索'}
						</Button>
					</FormItem>
				</Col>
			</Form>
		);
	};

	_detail = () => {
		const { detail, visible } = this.state;
		return (
			<Modal className="order-invoice-record-modal" title={'详情'} visible={visible}
				onCancel={this.handleCancel} footer={null}>
				<Fragment>
					<Row>
						<Descriptions title="第三方支付信息" bordered>
							<FormItem label="用户编号：">{detail && detail.cno}</FormItem>
							<FormItem label="用户户号：">{detail && detail.hno}</FormItem>
							<FormItem label="用户卡号：">{detail && detail.cardNo}</FormItem>
							<FormItem label="用户名称：">{detail && detail.customerName}</FormItem>
							<FormItem label="片区名称：">{detail && detail.areaName}</FormItem>
							<FormItem label="用户地址：">{detail && detail.customerAddress}</FormItem>
							<FormItem label="水表编号：">{detail && detail.waterMeterNo}</FormItem>
							<FormItem label="水表类型：">{detail && detail.waterMeterType}</FormItem>
							<FormItem label="水表种类：">{detail && detail.waterMeterKindType}</FormItem>
							<FormItem label="对应订单编号：">{detail && detail.OrderNo}</FormItem>
							<FormItem label="缴费方式：">{detail && detail.chargeWay}</FormItem>
							<FormItem label="订单来源：">{detail && detail.orderSource}</FormItem>
							<FormItem label="设备id：">{detail && detail.facilityId}</FormItem>
							<FormItem label="缴费结果：">{detail && detail.result}</FormItem>
							<FormItem label="状态：">{detail && detail.status}</FormItem>
						</Descriptions>
					</Row>
				</Fragment>
			</Modal>
		)
	};

	//查看详情
	handleView = (record) => {
		actionCreators.get(record.id).then(res => {
			if (res.code === 0) {
				this.setState({ detail: res.data, visible: true })
			}
		});
	}

	// 取消弹窗
	handleRefund = (record) => {
		if (this.state.refoundVisible) {
			this.setState({ "record": null });
		} else {
			this.setState({ "record": record });
			this.props.getRefundReasonSelect();
		}
		this.setState({ 'refoundVisible': !this.state.refoundVisible });
		this.getList();
	}

	refund = (record) => {
		this.props.form.validateFields((error, values) => {
			if (!error) {
				values.orderId = record.id;
				actionCreators.refund(values).then(res => {
					if (res.code === 0) {
						this.handleRefund(null);
					}
				});
			}
		});
	}

	//退款申请弹窗
	_renderRefundModal() {
		const { form, orderRefundReason } = this.props;
		const { record, refoundVisible } = this.state;
		const { getFieldDecorator } = form;
		const footer = (
			<Fragment>
				<Button key="submit" type="primary" onClick={() => this.refund(record)} >确定</Button>
				<Button key="back" onClick={() => this.handleRefund()}>取消</Button>
			</Fragment>
		);
		return <Modal title="退款" destroyOnClose={true} maskClosable={true} visible={refoundVisible}
			onCancel={() => this.handleRefund()}
			footer={footer}>
			<Form {...constants.formItemLayout}>
				<Row>
					<Col>
						<FormItem label={'退款原因:'}>
							{getFieldDecorator('reasonId', { rules: [{ required: true, message: '请选择退款原因' }] })
								(<Select placeholder="请选择">
									{orderRefundReason && orderRefundReason.map((item, index) => {
										return (
											<Option key={index} value={item.value}>
												{item.label}
											</Option>
										);
									})}
								</Select>)}
						</FormItem>
					</Col>
					<Col>
						<FormItem label={'退款描述:'}>
							{getFieldDecorator('description')
								(<Input placeholder="请输入" />)}
						</FormItem>
					</Col>
				</Row>
			</Form>
		</Modal>;
	}

	//获取列表
	getList = () => {
		const { searchForm, page, pageSize } = this.state
		actionCreators.list(Object.assign({ ...searchForm }, { page, pageSize })).then(res => {
			if (res.code === 0) {
				this.setState({ dataList: res.data.rows, total: res.data.total, page: res.data.page, pageSize: res.data.pageSize })
			}
		})
	}

	render() {
		const { page, pageSize, dataList, total } = this.state;
		const columns = [
			{
				title: '用户名称',
				dataIndex: 'customerName',
				align: 'center'
			},
			{
				title: '用户编号',
				dataIndex: 'cno',
				align: 'center'
			},
			{
				title: '用户卡号',
				dataIndex: 'cardNo',
				align: 'center'
			},
			{
				title: '片区',
				dataIndex: 'areaName',
				align: 'center'
			},
			{
				title: '用户地址',
				dataIndex: 'customerAddress',
				align: 'center'
			},
			{
				title: '付款方式',
				dataIndex: 'chargeWay',
				align: 'center'
			},
			{
				title: '订单来源',
				dataIndex: 'orderSource',
				align: 'center'
			},
			{
				title: '水表编号',
				dataIndex: 'waterMeterNo',
				align: 'center'
			},
			{
				title: '水表类型',
				dataIndex: 'waterMeterType',
				align: 'center'
			},
			{
				title: '水表种类',
				dataIndex: 'waterMeterKindType',
				align: 'center'
			},
			{
				title: '付款时间',
				dataIndex: 'createTime',
				align: 'center'
			},
			{
				title: '记录流水号',
				dataIndex: 'bizId',
				align: 'center'
			},
			{
				title: '记录状态',
				dataIndex: 'status',
				align: 'center',
				width: 80,
				fixed: 'right',
				render: text => {
					return (
						<Tag color="">
							{text}
						</Tag>
					);
				}
			},
			{
				title: '操作',
				key: 'operation',
				width: 150,
				align: 'center',
				fixed: 'right',
				render: (text, record, index) => {
					return (
						<span>
							<Button title="查看" className="btn" type="primary" size="small"
								icon="eye" onClick={() => this.handleView(record)} />
							<Button title="退款" className="btn" type="danger" size="small"
								icon="property-safety" onClick={() => this.handleRefund(record)}
							/>
						</span>
					);
				}
			}
		];

		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: (total) => {
				return `共 ${total} 条数据 `;
			},
			onChange: (page) => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current,
				size)
		};
		return (
			<div className="shadow-radius order-invoice-record">
				<h1>第三方支付记录</h1>
				{this._renderSearchForm()}
				<Table
					bordered
					columns={columns}
					scroll={{ x: 2500 }}
					rowKey={() => Math.random()}
					dataSource={dataList}
					pagination={paginationProps} />
				{this._detail()}
				{this._renderRefundModal()}
			</div>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('thirdPartyChargeOrderRecord');
	return {
		areaSelect: data.areaSelect, // 片区选择框
		orderRefundReason: data.orderRefundReason,  //订单退款原因
	};
};

const mapDispatchToProps = (dispatch) => ({
	getAreaSelect: () => dispatch(actionCreators.getAreaSelect()), //获取片区选择框
	getRefundReasonSelect: () => dispatch(actionCreators.getRefundReasonSelect()),       //获取退款原因

});


export default connect(mapStateToProps, mapDispatchToProps)(Form.create()(Index))

