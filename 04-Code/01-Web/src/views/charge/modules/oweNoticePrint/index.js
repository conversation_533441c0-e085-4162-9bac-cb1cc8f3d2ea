import React, { Component } from 'react'
import { actionCreators } from './store'
import { connect } from 'react-redux'
import {Modal, Table, Form, Select, Row, Col, Input, Button, DatePicker, Icon, TreeSelect, message} from 'antd'
import { constants } from '$utils'
import './index.scss'
import moment from "moment";
import getLodop from "../../../../utils/LodopFuncs";

const { TreeNode } = TreeSelect;
const FormItem = Form.Item

const defaultSearchForm = {
	waterMeterType: 0,
	oweAmountStart: '0.01',
	con: undefined,
	areaId: undefined,
	customerName: undefined
}

class Index extends Component {
	constructor(props) {
		super(props)
		this.state = {
			expand: false, // 搜索项过多的时候，隐藏部分
			page: 1,
			pageSize: 10,
			searchForm: defaultSearchForm,
			selectedRowKeys: [],
			selectedRow: []
		}
	}

	componentDidMount() {
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields && isTags) {
			this.setState({
				...fields.state
			}, () => {
				this.props.form.setFieldsValue({
					...fields.form
				})
			})
		} else {
			this.getList()
			this.props.getAreaSelect();
		}
	}

	componentWillUnmount() {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			})
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			})
		}
	}
	// 获取列表
	getList = () => {
		const { page, pageSize, searchForm } = this.state
		this.props.list(Object.assign({ ...searchForm }, { page, pageSize }))
	}
	// 列表分页
	handlePageChange = (page) => {
		this.setState(
			{
				page
			},
			() => {
				this.getList()
			}
		)
	}
	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState(
			{
				page: 1,
				pageSize: size
			},
			() => {
				this.getList()
			}
		)
	}
	// 搜索功能
	handleSearch = () => {
		this.setState(
			{
				page: 1
			},
			() => {
				this.getList()
			}
		)
	}
		// 选中事件
	onSelectChange = (selectedRowKeys, selectedRow) => {
		this.setState({ selectedRowKeys, selectedRow });
	};
	// 重置搜索
	handleReset = () => {
		this.props.form.setFieldsValue({
			rangPicker: [undefined, undefined]
		})
		this.setState(
			{
				page: 1,
				searchForm: defaultSearchForm
			},
			() => {
				this.getList()
			}
		)
	}
	// 批量打印欠费通知单
	handleBatchPrint = () => {
		const { selectedRow, selectedRowKeys } = this.state
		if (selectedRowKeys.length === 0) {
				message.error('请先勾选数据');
		} else {
				Modal.confirm({
						title: '提示',
						content: `一共${selectedRowKeys.length}条，确认要打印吗？`,
						okText: '确认',
						cancelText: '取消',
						onOk: () => {
								this.print(selectedRow)
						}
				});
		}
	}

	templateTable = ([name, phone, address, no, use, cno, buy, year, month, day, lastBuyDate]) => {
			return `<base href=""/>
<style> table,td,th {border: 1px solid black;border-style: solid;border-collapse: collapse}</style>
<table style="width: 600px" border="1">
  <tr style="height:50px">
    <td style="width: 200px;">用户名</td>
    <td style="width: 200px;">${name}</td>
    <td style="width: 200px;">联系电话</td>
    <td style="width: 200px;">${phone}</td>
  </tr>
  <tr style="height:50px">
    <td>地址</td>
    <td colspan="3">${address}</td>
  </tr>
  <tr style="height:50px">
    <td>水表号</td>
    <td>${no}</td>
    <td>使用量</td>
    <td>${use}吨</td>
  </tr>
  <tr style="height:50px">
    <td>用户编号</td>
    <td>${cno}</td>
    <td>购买量</td>
    <td>${buy}吨</td>
  </tr>
  <tr style="height:50px">
    <td>催费日期</td>
    <td>${year}年 ${month}月 ${day}日</td>
    <td>最后一次购买日期</td>
    <td>${lastBuyDate}</td>
  </tr>
  <tr style="height:50px">
    <td>内勤</td>
    <td></td>
    <td>抄表员</td>
    <td></td>
  </tr>
  <tr style="height:50px">
    <td>备注</td>
    <td colspan="3">经抄表核实，您已欠费，请于抄表之日起五日内到营业厅核实缴费。逾期将进行停水处理！停水用户补缴水费后预计8小时内供水。</td>
  </tr>
  <tr>
    <td colspan="4" style="height:300px"><p>温馨提示：
需要缴费的用户请携带此单及自家的水卡，前往营业
厅，本小区部分水表己经处在无电状态，为了水司和用户更准确的掌握用水信息，水司将安排人员普查抄表和完善用户信息，并告知用户水表余额或欠费情况。用户在房屋买卖或出租交接时，应及时到水司窗口进行水费查询。我公司从即日起安排抄表员入户抄表，请用水户积极配
合抄表员工作。<p>

<p>交费地址：火车站街3号白来水营业厅  0470-3560011
河东根河路金水岸商业楼自来水营业厅  0470-8223577
河西沿河路康桥祥福里小区东侧自来水营业厅 0470-3194488<p>

<p>收费员签字：</font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font>用户确认无误签字：</font></p>
</td>
  </tr>
</table>`
	}

	print(records) {
		const { editStore } = this.props
		const [year, month, day] = moment().format('YYYY-MM-DD').split('-')
		const LODOP = getLodop()
		records.map(({id, cno, name, address, contactPhone, waterMeterNo, lastBuyDate}) => {
				const {use, buy} = editStore[id]
				return [name, contactPhone, address, waterMeterNo, use, cno, buy, year, month, day, lastBuyDate]
						.map(i => i == null ? '': i)
		}).map(this.templateTable)
		.forEach(i => {
				LODOP.PRINT_INITA(0, 0, 1480, 2100, '')
				LODOP.ADD_PRINT_TEXT(20, 0, 1480, 2100, '催缴水费通知单')  // 居中显示，根据纸张尺寸和标题长度微调位置
				LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
				LODOP.SET_PRINT_STYLEA(0, 'FontSize', 20)
				LODOP.ADD_PRINT_TABLE(50, 10, 1300, 1600, i)
				const result = LODOP.PRINT()
				if (result) {
						this.setState({ selectedRowKeys: [], selectedRows: [] })
				}
		})

	}
	// 递归渲染
	_renderTreeNode = treeData => {
				return treeData.map(tree => {
						if (tree.children) {
								return (
										<TreeNode key={tree.id} title={tree.name} value={tree.id}>
												{this._renderTreeNode(tree.children)}
										</TreeNode>
								);
						} else {
								return <TreeNode key={tree.id} title={tree.name} value={tree.id} />;
						}
				});
		};
	// 渲染搜索
	_renderSearchForm = () => {
		const { searchForm } = this.state;
		const { areaSelect } = this.props;
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					<Col span={8}>
						<FormItem label={'用户编号:'}>
								<Input
										placeholder="请输入用户编号"
										value={searchForm.cno}
										onChange={v => {
												this.setState({
														searchForm: {
																...searchForm,
																cno: v.target.value
														}
												});
										}}
								/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'片区:'}>
								<TreeSelect
										style={{ width: '100%' }} dropdownStyle={{ maxHeight: 400, overflow: 'auto' }} placeholder="请选择片区"
										allowClear
										treeDefaultExpandedKeys={[100]}
										value={searchForm.areaId}
										onChange={v => {
												this.setState({ searchForm: { ...searchForm, areaId: v } });
										}}>
										{this._renderTreeNode(areaSelect)}
								</TreeSelect>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'用户名称:'}>
								<Input
										placeholder="请输入用户名称"
										value={searchForm.customerName}
										onChange={v => {
												this.setState({
														searchForm: {
																...searchForm,
																customerName: v.target.value
														}
												});
										}}
								/>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={16} offset={8} align="right">
						<FormItem>
							<Button type="primary" onClick={this.handleSearch}>
								搜索
							</Button>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<Button type="default" onClick={this.handleReset}>
								重置
							</Button>
						</FormItem>
					</Col>
				</Row>
			</Form>
		)
	}
	// 渲染操作按钮
	_renderOperationButton = () => {
		return (
			<Col>
				<Button className="searchBtn" type="primary" onClick={() => this.handleBatchPrint()}><Icon type="download" />打印欠费通知单</Button>
			</Col>
		)
	}
	render() {
		const { page, pageSize, selectedRowKeys } = this.state
		const { dataList, total, editStore, setState } = this.props
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: (total) => {
				return `共 ${total} 条数据 `
			},
			onChange: (page) => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		}

		const rowSelection = {
			fixed: true,
			selectedRowKeys,
			onChange: this.onSelectChange
		};
		const columns = [
			{
				title: '用户编号',
				dataIndex: 'cno',
				align: 'center'
			},
			{
				title: '用户名称',
				dataIndex: 'name',
				align: 'center'
			},
			{
				title: '地址',
				dataIndex: 'address',
				align: 'center'
			},
			{
				title: '片区',
				dataIndex: 'areaName',
				align: 'center'
			},
			{
				title: '联系电话',
				dataIndex: 'contactPhone',
				align: 'center'
			},
			{
				title: '水表编号',
				dataIndex: 'waterMeterNo',
				align: 'center'
			},
			{
				title: '使用量',
				align: 'center',
				render: (text, record) => {
					return <Input
							placeholder="请输入使用量"
							value={editStore[record.id].use}
							onChange={(v) => {
									const newEditStore = {...editStore}
									newEditStore[record.id].use = v.target.value
									setState({ key: 'editStore', value: newEditStore })
							}}
					/>
				}
			},
			{
				title: '购买量',
				align: 'center',
				render: (text, record) => {
						return <Input
								placeholder="请输入使用量"
								value={editStore[record.id].buy}
								onChange={(v) => {
										const newEditStore = {...editStore}
										newEditStore[record.id].buy = v.target.value
										setState({ key: 'editStore', value: newEditStore })
								}}
						/>
				}
			},
			{
				title: '购水次数',
				dataIndex: 'accumulationBuyCount',
				align: 'center'
			},
			{
				title: '最近购水日期',
				dataIndex: 'lastBuyDate',
				align: 'center'
			},
			{
				title: '最近购水金额',
				dataIndex: 'lastBuyAmount',
				align: 'center'
			}
		];
		return (
			<div className="shadow-radius receipt-record">
				<Row>
					<Col>
						<h1>打印欠费通知单</h1>
					</Col>
				</Row>
				<Row>{this._renderSearchForm()}</Row>
				<Row>{this._renderOperationButton()}</Row>
				<Row className="main">
					<Table
						bordered
						columns={columns}
						rowKey={({id}) => id}
						dataSource={dataList}
						rowSelection={rowSelection}
						pagination={paginationProps}
					/>
				</Row>
			</div>
		)
	}
}
const mapStateToProps = (state) => {
	const data = state.get('oweNoticePrint')
	return {
		fields: data.fields,
		dataList: data.dataList, // 列表数据
		editStore: data.editStore,
		total: data.total, // 总条数
		areaSelect: data.areaSelect,// 领用人选择框
	}
}
const mapDispatchToProps = (dispatch) => ({
	setState: (data) => dispatch(actionCreators.setState(data)), // 设置redux值
	list: (data) => dispatch(actionCreators.list(data)), // 获取列表
	getAreaSelect: () => dispatch(actionCreators.getAreaSelect()), // 获取领用人选择框
})
export default connect(mapStateToProps, mapDispatchToProps)(Form.create()(Index))
