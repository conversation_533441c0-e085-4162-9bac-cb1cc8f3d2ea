import React, { Component, Fragment } from 'react';
import { Descriptions, Table, Row, Divider } from 'antd';

class InvoiceDetail extends Component {

	render() {
		const { detail } = this.props;
		const invoicePrintRecordDetailList = detail && detail.invoicePrintRecordDetailList;
		const columns = [
			{
				title: '商品名称',
				dataIndex: 'productName',
				align: 'center'
			},
			{
				title: '规格',
				dataIndex: 'specification',
				align: 'center'
			},
			{
				title: '单位',
				dataIndex: 'unit',
				align: 'center'
			},
			{
				title: '数量',
				dataIndex: 'quantity',
				align: 'center'
			},
			{
				title: '含税单价',
				dataIndex: 'taxUnitPrice',
				align: 'center'
			},
			{
				title: '不含税单价',
				dataIndex: 'nonTaxUnitPrice',
				align: 'center'
			},
			{
				title: '含税金额',
				dataIndex: 'taxPrice',
				align: 'center'
			},
			{
				title: '不含税金额',
				dataIndex: 'nonTaxPrice',
				align: 'center'
			},
			{
				title: '税率',
				dataIndex: 'taxRate',
				align: 'center'
			},
			{
				title: '税额',
				dataIndex: 'tax',
				align: 'center'
			}
		];
		return (
			<Fragment>
				<Descriptions title="基础信息" bordered>
					<Descriptions.Item label="特抄收费编号：">{detail && detail.specialFeeNo}</Descriptions.Item>
					<Descriptions.Item label="用户编号：">{detail && detail.cno}</Descriptions.Item>
					<Descriptions.Item label="客户名称：">{detail && detail.customerName}</Descriptions.Item>
					<Descriptions.Item label="打印内容：">{detail && detail.contentType}</Descriptions.Item>
					<Descriptions.Item label="总水量：">{detail && detail.totalWaterAmount}</Descriptions.Item>
					<Descriptions.Item label="总金额：">{detail && detail.totalFeeAmount}</Descriptions.Item>
					<Descriptions.Item label="发票类型：">{detail && detail.type}</Descriptions.Item>
					<Descriptions.Item label="税号：">{detail && detail.vatNumber}</Descriptions.Item>
					<Descriptions.Item label="姓名：">{detail && detail.name}</Descriptions.Item>
					<Descriptions.Item label="地址：">{detail && detail.address}</Descriptions.Item>
					<Descriptions.Item label="开户行：">{detail && detail.openBank}</Descriptions.Item>
					<Descriptions.Item label="银行账号：">{detail && detail.bankAccount}</Descriptions.Item>
					<Descriptions.Item label="手机号码：">{detail && detail.phone}</Descriptions.Item>
					<Descriptions.Item label="是否一般纳税人：">{detail && detail.generalTaxpayer ? '是' : '否'}</Descriptions.Item>
					<Descriptions.Item label="发票状态：">{detail && detail.status}</Descriptions.Item>
					<Descriptions.Item label="发票代码：">{detail && detail.invoiceCode}</Descriptions.Item>
					<Descriptions.Item label="发票号码：">{detail && detail.invoiceNo}</Descriptions.Item>
					<Descriptions.Item label="发票日期：">{detail && detail.printDate}</Descriptions.Item>
					<Descriptions.Item label="发票渠道：">{detail && detail.taxInvoiceKind}</Descriptions.Item>
					<Descriptions.Item label="发票种类：">{detail && detail.taxInvoicePurpose}</Descriptions.Item>
					<Descriptions.Item label="发送手机：">{detail && detail.sendPhone}</Descriptions.Item>
					<Descriptions.Item label="邮箱：">{detail && detail.email}</Descriptions.Item>
					<Descriptions.Item label="pdf下载地址："><a href={detail && detail.pdfUrl}>{detail && detail.pdfUrl}</a></Descriptions.Item>
					<Descriptions.Item label="开票人：">{detail && detail.createPersonName}</Descriptions.Item>
					<Descriptions.Item label="开票时间：">{detail && detail.createTime}</Descriptions.Item>
					<Descriptions.Item label="备注：" span={3}>{detail && detail.remark}</Descriptions.Item>
					<Descriptions.Item label="红字确认单状态：">{detail && detail.redConfirmBillStatus}</Descriptions.Item>
					<Descriptions.Item label="失败原因：" span={3}>{detail && detail.reason}</Descriptions.Item>
				</Descriptions>
				<Divider dashed orientation="center">
					打印明细
				</Divider>
				<Row>
					<Table bordered columns={columns} rowKey={() => Math.random()} dataSource={invoicePrintRecordDetailList} />
				</Row>
			</Fragment>
		);
	}
}

export default InvoiceDetail;
