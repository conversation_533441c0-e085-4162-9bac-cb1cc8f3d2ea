import * as actionTypes from './constants';
import { dataType } from '$utils';

const defaultState = {
	fields: null,
	dataList: [], // 列表数据
	total: 0, // 总条数
	createDepartmentSelect: [], // 获取订单部门选择框
	statistics: null,	// 统计
	detail: null, // 详情
	visible: false, // 涉及到交互的弹窗显示控制 默认为false
	waterUseKindSelect: [], // 用水分类选择框
	chargeFee: 0, // 收费金额
	formwork: null,   //发票模板
	createList: [],      //操作员
	sysuserSelect: [],      //收费员
	areaSelect: [], // 片区选择框
};

// 重要提示： 请做好各代码段的注释！！！
export default (state = defaultState, action) => {
	switch (action.type) {
		// 回填组件传给redux的值
		case actionTypes.SET_STATE:
			let _data = action.data;
			if (dataType(_data) === 'Object') {
				state[_data.key] = _data.value;
			}
			if (dataType(_data) === 'Array') {
				_data.forEach(item => {
					state[item.key] = item.value;
				});
			}
			return { ...state };
		// 回填列表数据
		case actionTypes.LIST_RECORD:
			state.dataList = action.data.rows;
			state.total = action.data.total;
			return { ...state };
			// 获取片区选择框
		case actionTypes.GET_SYSUSER_SELECT:
			state.sysuserSelect = action.data;
			return { ...state };
		// 回填列表数据
		case actionTypes.GET_STATISTICS:
			state.statistics = action.data;
			return { ...state };
		// 回填详情信息
		case actionTypes.DETAIL_RECORD:
			state.detail = action.data;
			return { ...state };
		// 确认收费
		case actionTypes.CHARGE:
			return { ...state };
		// 获取用水分类选择框
		case actionTypes.GET_WATER_USE_KIND_SELECT:
			state.waterUseKindSelect = action.data;
			return { ...state };
		// 计算收费金额
		case actionTypes.GET_CHARGE_FEE:
			state.chargeFee = action.data;
			return { ...state };
		// 核减
		case actionTypes.MINUS:
			return { ...state };
		// 删除
		case actionTypes.DELETE:
			return { ...state };
		//获取发票模板
		case actionTypes.GET_BY_TYPE:
			state.formwork = action.data;
			return { ...state };
		case actionTypes.QUERY_LIST_SELECT:
			state.createList = action.data;
			return { ...state };
		// 批量打印税票
		case actionTypes.BATCH_PRINT_TAX:
			return { ...state };
		// 打印税票
		case actionTypes.PRINT_TAX:
			return { ...state };
		// 打印电子发票
		case actionTypes.PRINT_ETAX:
			return { ...state };
		// 打印税票获取详情
		case actionTypes.GET_VAL:
			state.detail = action.data;
			return { ...state };
		// 获取片区选择框
		case actionTypes.GET_AREA_SELECT:
			state.areaSelect = action.data;
			return { ...state };
			// 获取订单部门选择框
		case actionTypes.GET_CREATE_DEPARTMENT_SELECT:
			state.createDepartmentSelect = action.data;
			return { ...state };
		default:
			return state;
	}
};
