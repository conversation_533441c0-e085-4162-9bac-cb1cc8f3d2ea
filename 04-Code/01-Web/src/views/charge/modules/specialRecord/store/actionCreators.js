import http from '$http';
import * as actionTypes from './constants';
import { message } from 'antd';
import api from './api';
import formatDate from "../../../../../utils/formatDate";
import Decimal from "decimal.js";
import digitalUppercase from "../../../../../utils/digitalUppercase";
import getLodop from "../../../../../utils/LodopFuncs";
import { Math as MathUtil } from "@/utils/math"
// 数据回填
const payload = (type, data) => ({ type, data });
// 组件操作redux数据状态
const setState = data => ({ type: actionTypes.SET_STATE, data });
// 获取列表
const list = data => {
	return async dispatch => {
		const res = await http.post(api.list, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.LIST_RECORD, res.data));
		}
	};
};
// 获取统计
const getStatistics = data => {
	return async dispatch => {
		const res = await http.post(api.getStatistics, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_STATISTICS, res.data));
		}
	};
};
// 获取详情
const detail = data => {
	return async dispatch => {
		const res = await http.restGet(api.detail, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.DETAIL_RECORD, res.data));
		}
	};
};
// 确认收费
const charge = (data, list) => {
	return async dispatch => {
		const res = await http.post(api.charge, data);
		if (res.code === 0) {
			list();
		}
	};
};
// 获取用水性质选择框
const getWaterUseKindSelect = () => {
	return async dispatch => {
		const res = await http.get(api.getWaterUseKindSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_WATER_USE_KIND_SELECT, res.data));
		}
	};
};
// 计算收费金额
const getChargeFee = data => {
	return async dispatch => {
		const res = await http.post(api.getChargeFee, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_CHARGE_FEE, res.data));
		}
	};
};
// 核减
const minus = (data, list) => {
	return async dispatch => {
		const res = await http.post(api.minus, data);
		if (res.code === 0) {
			list();
			dispatch(payload(actionTypes.MINUS, res.data));
		}
	};
};
// 删除
const del = (data, list) => {
	return async dispatch => {
		const res = await http.restPost(api.delete, data);
		if (res.code === 0) {
			list();
			dispatch(payload(actionTypes.DELETE, res.data));
		}
	};
};

//确认退款
const confirmRefund = (data, list) => {
	return async dispatch => {
		const res = await http.restPost(api.confirmRefund, data);
		if (res.code === 0) {
			list();
		}
	};
};

//获取发票模板
const getByType = (data) => {
	return async dispatch => {
		const res = await http.restGet(api.getByType, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_BY_TYPE, res.data));
		}
	};
};
//保存打印记录
const getRecordSave = (data) => {
	return async dispatch => {
		const res = await http.post(api.getRecordSave, data);
		if (res.code === 0) {
			message.success('打印成功！');
		}
	};
};
//获取操作员
const queryListSelect = () => {
	return async dispatch => {
		const res = await http.get(api.queryListSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.QUERY_LIST_SELECT, res.data));
		}
	};
};
// 批量打印税票
const batchPrintTax = (data, list) => {
	return async dispatch => {
		const res = await http.post(api.batchPrintTax, data);
		if (res.code === 0) {
			message.success('打印数据已生成，请在地税系统中打印');
			dispatch(payload(actionTypes.BATCH_PRINT_TAX, res.data));
		}
		list();
	};
};
// 打印税票
const printTax = (data, list) => {
	return async dispatch => {
		const res = await http.post(api.printTax, data);
		if (res.code === 0) {
			message.success('打印数据已生成，请在地税系统中打印');
			dispatch(payload(actionTypes.PRINT_TAX, res.data));
		}
		list();
	};
};
// 打印电子发票
const printETax = (data, list) => {
	return async dispatch => {
		const res = await http.post(api.printETax, data);
		if (res.code === 0) {
			message.success('电子发票已推送');
			dispatch(payload(actionTypes.PRINT_ETAX, res.data));
		}
		list();
	};
};
// 打印税票获取详情
const getVal = (data) => {
	return async dispatch => {
		const res = await http.restGet(api.getVal, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_VAL, res.data));
		}
	};
};

//获取发票模板
const getDetailByCustomer = (data,ops) => {
		return async dispatch => {
				const res = await http.restGet(api.getDetailByCustomer, data);
				if (res.code === 0) {
						dispatch(payload(actionTypes.GET_BY_CUSTOMER, res.data));
						ops();
				}
		};
};


const submitInvoice = (data,ops) => {
		return async dispatch => {
				const res = await http.post(api.submitInvoice, data);
				if (res.code === 0) {
						ops();
				}
		};
};



// 获取片区选择框
const getAreaSelect = () => {
	return async dispatch => {
		const res = await http.get(api.getAreaSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_AREA_SELECT, res.data));
		}
	};
};

// 打印小票
const printInvoice = (data) => {
	return async dispatch => {
		const detail = await http.restGet(api.detail, data.id);
		if (detail.code !== 0) {
			message.error('打印失败')
			return
		}
		const { customerName,cno,
			address, createPersonName,
			chargeDetailList,
			specialFeeDetailList } = detail.data
		const { waterUseKindName, waterUseKind:{ ladderType, waterUseKindDetailList } } = specialFeeDetailList[0]
		// 单价
		const { cleanWaterFee, waterResourceFee, sewageFee } = waterUseKindDetailList[0]
		// 各水费合计金额
		const { waterAmount, cleanFeeSum, sewageFeeSum, resourceFeeSum } = chargeDetailList.reduce((total, item) => {
			const { waterAmount, cleanFeeSum, sewageFeeSum, resourceFeeSum } = total
			total.waterAmount = new Decimal(waterAmount).add(new Decimal(item.waterAmount))
			total.cleanFeeSum = new Decimal(cleanFeeSum).add(new Decimal(item.cleanWaterFee))
			total.sewageFeeSum = new Decimal(sewageFeeSum).add(new Decimal(item.sewageFee))
			total.resourceFeeSum = new Decimal(resourceFeeSum).add(new Decimal(item.waterResourceFee))
			return total
		}, { waterAmount: new Decimal(0), cleanFeeSum: new Decimal(0), sewageFeeSum: new Decimal(0), resourceFeeSum: new Decimal(0)})
		// 清水总额 + 水资源总额
		const totalAmount = new Decimal(cleanFeeSum).add(new Decimal(resourceFeeSum)).add(new Decimal(sewageFeeSum))
		const yyyy = formatDate(new Date(), 'yyyy')
		const MM = formatDate(new Date(), 'MM')
		const dd = formatDate(new Date(), 'dd')
		let result = null
		let LODOP = getLodop()
		if (ladderType !== '非阶梯') {
			// 阶梯小票
			const orderTemplate = await http.restGet(api.getByType, 12)
			const priceData = waterUseKindDetailList.map(({ladderLevel, cleanWaterFee}) => ({
				[`ladder${ladderLevel}Count`]: new Decimal(0),
				[`ladder${ladderLevel}Price`]: cleanWaterFee,
				[`ladder${ladderLevel}Amount`]: new Decimal(0)
			})).reduce((total, item) => ({...total, ...item}), {})

				chargeDetailList.forEach(({ladderLevel, waterAmount, cleanWaterFee}) => {
						priceData[`ladder${ladderLevel}Count`] = waterAmount
						priceData[`ladder${ladderLevel}Amount`] = cleanWaterFee
				})
			const param = {
				customerName,
				customerAddress: address,
				cardNo: cno,
				meterAmount: cleanFeeSum,
				totalAmount: totalAmount,
				...priceData,
				resourceCount: MathUtil.comparedTo(resourceFeeSum,new Decimal(0) )>0?waterAmount:0 ,
				resourcePrice: waterResourceFee,
				resourceAmount: resourceFeeSum,
				sewageCount: MathUtil.comparedTo(sewageFeeSum,new Decimal(0) )>0?waterAmount:0,
				sewagePrice:sewageFee,
				sewageAmount:sewageFeeSum,
				amountBig: digitalUppercase(totalAmount),
				tollName: createPersonName,
				yyyy, MM, dd
			}
			const template = Object.entries(param)
				.map(([k, v]) => ({key: k, value: v == null ? '' : v}))
				.reduce((total, {key, value}) => total.replace(key, value), orderTemplate.data.content)
			eval(template)
			result = LODOP.PREVIEW()
		} else {
			// 非阶梯小票
			const invoiceSource = 11
			const orderTemplate = await http.restGet(api.getByType, invoiceSource)
			const param = {
				customerName,
				cardNo: cno,
				waterUseKindName,
				waterAmount,
				amountSmall: totalAmount,
				cleanPrice: cleanWaterFee,
				resourcePrice: waterResourceFee,
				cleanAmount: cleanFeeSum,
				resourceAmount: resourceFeeSum,
				sewageCount: MathUtil.comparedTo(sewageFeeSum,new Decimal(0) )>0?waterAmount:0,
				sewagePrice:sewageFee,
				sewageAmount:sewageFeeSum,
				amountBig: digitalUppercase(totalAmount),
				customerAddress: address,
				tollName: createPersonName,
				yyyy, MM, dd
			}
			const template = Object.entries(param)
				.map(([k, v]) => ({key: k, value: v == null ? '' : v}))
				.reduce((total, {key, value}) => total.replace(key, value), orderTemplate.data.content)
			eval(template)
			result = LODOP.PREVIEW()
		}

		// if(MathUtil.comparedTo(sewageFeeSum, new Decimal(0)) > 0){
		// 	const sewageTemplate = await http.restGet(api.getByType, 10)
		// 	const param = {
		// 		customerName,
		// 		cardNo: cno,
		// 		waterUseKindName,
		// 		waterAmount,
		// 		price: sewageFee,
		// 		amount: sewageFeeSum,
		// 		amountBig: digitalUppercase(sewageFeeSum),
		// 		amountSmall: sewageFeeSum,
		// 		customerAddress: address,
		// 		yyyy, MM, dd,
		// 		tollName: createPersonName
		// 	}
		// 	const template = Object.entries(param)
		// 		.map(([k, v]) => ({key: k, value: v == null ? '' : v}))
		// 		.reduce((total, {key, value}) => total.replace(key, value), sewageTemplate.data.content)
		// 	eval(template)
		// 	result = LODOP.PREVIEW()
		// }

		if (result) {
			message.success('打印成功！');
		}
	};
};
export {
	setState, list, getStatistics, detail, charge, getWaterUseKindSelect, getChargeFee, minus, del,
	getByType,
	getRecordSave,
	queryListSelect,
	batchPrintTax,
	printTax,
	printETax,
	getVal,
	getAreaSelect,
	confirmRefund,
	getDetailByCustomer,
	submitInvoice,
	printInvoice
};
