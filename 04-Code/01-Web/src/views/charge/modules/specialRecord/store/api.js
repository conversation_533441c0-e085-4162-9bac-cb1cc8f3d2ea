export default {
	list: `/api/charge/specialFee/listPage`,
	getStatistics: `/api/charge/specialFee/statistics`, // 获取统计
	detail: `/api/charge/specialFee/get`,
	charge: `/api/charge/specialFee/charge`,
	getWaterUseKindSelect: `/api/wuk/waterusekind/getSelect`,
	getChargeFee: `/api/charge/specialFee/getChargeFee`,
	addSpecialFee: `api/charge/specialFee/save`,
	minus: `/api/charge/specialFee/minus`,
	delete: `api/charge/specialFee/invalidSpecialFee`,              //作废
	getByType: `api/invoice/template/getByType`,                     //获取发票模板
	getRecordSave: `api/invoice/record/save`,                         //保存打印记录
	queryListSelect: `api/charge/specialFee/listSelect`,               //保存打印记录
	batchPrintTax: `/api/charge/specialFee/batchPrintTax`,// 批量打印税票
	printTax: `/api/charge/specialFee/printTax`, // 打印税票
	printETax: `/api/charge/specialFee/printInvoice`, // 打印电子发票
	getVal: `api/cm/vat/getByCustomer`, // 打印税票获取信息
	getAreaSelect: `/api/sys/area/getTree`,
	confirmRefund: `/api/charge/specialFee/confirmRefund`,
	getDetailByCustomer: `api/cm/vat/getByCustomer`,                 //通过用户获取详情
	submitInvoice: `api/charge/specialFee/updateTaxNo`,
};
