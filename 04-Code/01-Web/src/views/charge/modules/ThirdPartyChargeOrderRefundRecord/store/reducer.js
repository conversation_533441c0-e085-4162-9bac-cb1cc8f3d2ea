import * as actionTypes from './constants';
import { dataType } from '$utils';

const defaultState = {
		areaSelect: [], // 片区选择框
};


// 重要提示： 请做好各代码段的注释！！！
export default (state = defaultState, action) => {
		switch (action.type) {
						// 回填组件传给redux的值
				case actionTypes.SET_STATE:
						let _data = action.data;
						if (dataType(_data) === 'Object') {
								state[_data.key] = _data.value;
						}
						if (dataType(_data) === 'Array') {
								_data.forEach(item => {
										state[item.key] = item.value;
								});
						}
						return { ...state };
						// 获取片区选择框
				case actionTypes.GET_AREA_SELECT:
						state.areaSelect = action.data;
						return { ...state };
				default:
						return state;
		}
};
