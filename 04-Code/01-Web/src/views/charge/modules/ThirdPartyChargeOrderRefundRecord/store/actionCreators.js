import http from '$http';
import * as actionTypes from './constants';
import { message } from 'antd';
import api from './api';

// 数据回填
const payload = (type, data) => ({ type, data });

// 组件操作redux数据状态
const setState = data => ({ type: actionTypes.SET_STATE, data });

// 获取列表
const list = data => {
	return http.post(api.list, data);
};

// 获取片区选择框

const getAreaSelect = () => {
	return async dispatch => {
		const res = await http.get(api.getAreaSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_AREA_SELECT, res.data));
		}
	};
};

// 获取详情
const get = (data) => {
		return http.restGet(api.get, data);
};

// 获取详情
const refundConfirm = (data) => {
		return http.restPost(api.refundConfirm, data);
};




export { setState, list, getAreaSelect, get, refundConfirm};
