import './index.scss';

import React, {
	Component,
	Fragment,
} from 'react';

import http from '$http';
import { constants } from '$utils';
import {
	Button,
	Checkbox,
	Col,
	DatePicker,
	Form,
	Icon,
	Input,
	InputNumber,
	message,
	Modal,
	Row,
	Select,
	Table,
	Tag,
	TreeSelect,
} from 'antd';
import Decimal from 'decimal.js';
import moment from 'moment';
import { connect } from 'react-redux';

import OrderDetailModal from '@/components/order';
import { orderStatusColorMap } from '@/constants/colorStyle';
import {
	CHARGE_WAY,
	ORDER_SOURCE,
	ORDER_STATUS,
	PRINT_TYPE,
} from '@/constants/order';
import {
	VALVE_STATUE,
	WATER_METER_KIND,
	WATER_METER_MANUFACTURER,
	WATER_METER_TYPE,
} from '@/constants/waterMeter';
import { WATER_USE_KIND_TYPE } from '@/constants/waterUseKind';
import {
	qsRead,
	readCard,
	sleep,
	xtRead,
} from '@/utils/cardUtil';
import { Math as MathUtil } from '@/utils/math';

import digitalUppercase from '../../../../utils/digitalUppercase';
import formatDate from '../../../../utils/formatDate';
import getLodop from '../../../../utils/LodopFuncs';
import { actionCreators } from './store';
import api from './store/api';

const FormItem = Form.Item;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TreeNode } = TreeSelect;
const InputGroup = Input.Group;
const { confirm } = Modal;

const now = new Date();
const dateFormat = 'YYYY-MM-DD';
const defaultSearchForm = {
	orderNo: '',
	cno: '',
	hno: undefined,
	areaStart: null,
	areaEnd: null,
	customerName: '',
	address: '',
	waterMeterNo: '',
	waterMeterType: null,
	waterMeterKind: null,
	areaId: '',// 订单片区
	customerAreaId: '',// 用户片区
	waterUseKindType: null,
	waterUseKindIdList: [],
	orderSource: null,
	chargeWay: null,
	statusList: [],
	createStartDate: moment(now.getFullYear() + '-' + moment().format('MM') + '-01', dateFormat).format(dateFormat),
	createEndDate: moment(now, dateFormat).format(dateFormat),
	waterStartAmount: null,
	waterEndAmount: null,
	orderStartAmount: null,
	orderEndAmount: null,
	invoice: null,
	receiptNo: '',
	taxInvoice: null,
	createUid: null,
	departmentId: null,
	cardNo: null,
	areaCode: null,
	generalTaxpayer: null,
	customerLevelId: null,
	transcriberIdList: [],
	businessHallId: null,
	valveStatusIntegerList: [],
};

class Index extends Component {

	constructor(props) {
		super(props);
		this.state = {
			expand: false, // 搜索项过多的时候，隐藏部分
			page: 1,
			pageSize: 10,
			searchForm: defaultSearchForm,
			searchWaterMeterType: null,
			selectedRowKeys: [],//列表选中
			detailModalVisible: false,
			reFoundVisible: false,   //退款审批弹窗
			record: null,
			payVisible: false,
			wukVisible: false,
			loading: false,
			remarkVisible: false,
			vatInfo: null
		};
	}

	componentDidMount () {
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields && isTags) {
			this.setState({
				...fields.state
			}, () => {
				this.props.form.setFieldsValue({
					...fields.form
				});
			});
		} else {
			this.props.getAreaSelect();
			this.props.getCreateUidSelect();
			this.props.getCreateDepartmentSelect();
			this.props.customerLevelList();
			this.getAllTranscriber();
			this.props.getBusinessHallSelect();
			this.props.getTranscriberTree();// 获取抄表员树
		}
		document.addEventListener('keypress', this.handleEnterKey);
	}

	// 获取抄表员列表
	getAllTranscriber = () => {
		let data = {};
		actionCreators.getAllTranscriber(data).then(res => {
			if (res.code == 0) {
				this.setState({ allTranscriber: res.data });
			}
		});
	}

	componentWillUnmount () {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({ key: 'fields', value: { form: form.getFieldsValue(), state: this.state } });
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({ key: 'fields', value: { state: this.state } });
		}
		document.removeEventListener('keypress', this.handleEnterKey);
	}

	// 回车事件
	handleEnterKey = (e) => {
		if (e.keyCode === 13) {
			this.getList();
		}
	};

	// 获取列表
	getList = () => {
		const { page, pageSize, searchForm } = this.state;
		this.props.setState({ key: 'orderTotal', value: null });
		this.props.list(Object.assign({ ...searchForm }, { page, pageSize }));
	};

	// 列表分页
	handlePageChange = (page) => {
		this.setState({ page }, () => {
			this.getList();
		});
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState({ page: 1, pageSize: size }, () => {
			this.getList();
		});
	};

	// 搜索功能
	handleSearch = () => {
		this.setState({ page: 1 }, () => {
			this.getList();
			this.setState({ selectedRowKeys: [] });

		});
	};

	//重置搜索
	handleReset = () => {
		const { searchForm } = this.state;
		searchForm.cardNo = null;
		searchForm.areaCode = null;
		this.resetQueryDate();
		this.setState({ page: 1, searchForm: Object.assign({}, defaultSearchForm) });
		this.setState({ selectedRowKeys: [] });
	};

	// 设置订单搜索日期
	resetQueryDate = () => {
		this.props.form.setFieldsValue({ rangPicker: [moment(moment().year(moment().year() - 1).startOf('year'), dateFormat), moment(now, dateFormat)] });
	};

	//获取用水性质选择框
	getWaterUseKindSelect = v => {
		this.props.getWaterUseKindSelect(v);
	};

	//订单时间查询条件
	getOrderDate = (date, dateString) => {
		if (dateString.length === 2) {
			this.setState({
				searchForm: { ...this.state.searchForm, createStartDate: dateString[0], createEndDate: dateString[1] }
			});
		}
	};


	//订单时间查询条件
	getOpenDate = (date, dateString) => {
		if (dateString.length === 2) {
			this.setState({
				searchForm: { ...this.state.searchForm, openStartDate: dateString[0], openEndDate: dateString[1] }
			});
		}
	};

	//查看详情
	handleView = record => {
		this.setState({ detailModalVisible: true });
		this.props.getDetail(record.id);
	};

	handleCancel = () => {
		this.setState({ detailModalVisible: false });
	};

	//显示高级搜索
	showMoreSearch = () => {
		this.setState({
			expand: !this.state.expand
		});
	};

	// 渲染树
	_renderTreeLoop = v => {
		return v.map(item => {
			if (item.children) {
				return (
					<TreeNode key={item.id} title={item.name} value={item.id}>
						{this._renderTreeLoop(item.children)}
					</TreeNode>
				);
			} else {
				return <TreeNode key={item.id} title={item.name} value={item.id} />;
			}
		});
	};

	//递归渲染
	_renderTreeNode = treeData => {
		return treeData.map(tree => {
			if (tree.children) {
				return (
					<TreeNode key={tree.id} title={tree.name} value={tree.id}>
						{this._renderTreeNode(tree.children)}
					</TreeNode>
				);
			} else {
				return <TreeNode key={tree.id} title={tree.name} value={tree.id} />;
			}
		});
	};

	// 渲染搜索
	_renderSearchForm = () => {
		const { expand, searchForm, searchWaterMeterType } = this.state;
		const { areaSelect, waterUseKindSelect, createUidSelect, createDepartmentSelect, customerLevels, businessHallSelect, transcriberTree } = this.props;
		const { getFieldDecorator } = this.props.form;
		const { transcriberIdList, valveStatusIntegerList } = searchForm;
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					<Col span={8}>
						<FormItem label={'订单编号:'}>
							<Input
								placeholder="请输入订单编号"
								value={searchForm.orderNo}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, orderNo: v.target.value } });
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'用户户号:'}>
							<Input placeholder="请输入用户户号" value={searchForm.hno}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, hno: v.target.value } });
								}} />
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'用户编号:'}>
							<Input
								placeholder="请输入用户编号"
								value={searchForm.cno}
								onChange={v => {
									this.setState({
										searchForm: {
											...searchForm,
											cno: v.target.value
										}
									});
								}}
							/>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={8}>
						<FormItem label={'用户名称:'}>
							<Input
								placeholder="请输入用户名称"
								value={searchForm.customerName}
								onChange={v => {
									this.setState({
										searchForm: {
											...searchForm,
											customerName: v.target.value
										}
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'用户卡号:'}>
							<Input
								placeholder="请输入用户卡号"
								value={searchForm.cardNo}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, cardNo: v.target.value } });
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'用户地址:'}>
							<Input
								placeholder="请输入用户地址"
								value={searchForm.address}
								onChange={v => {
									this.setState({
										searchForm: {
											...searchForm,
											address: v.target.value
										}
									});
								}}
							/>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={8}>
						<FormItem label={'水表编号:'}>
							<Input
								placeholder="请输入水表编号"
								value={searchForm.waterMeterNo}
								onChange={v => {
									this.setState({
										searchForm: {
											...searchForm,
											waterMeterNo: v.target.value
										}
									});
								}}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'水表类型:'}>
							<Select
								allowClear
								placeholder="请选择"
								value={searchForm.waterMeterType}
								onChange={v => {
									this.setState({
										searchWaterMeterType: v,
										searchForm: { ...searchForm, waterMeterType: v }
									});
								}}
							>
								{WATER_METER_TYPE.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'水表厂家:'}>
							<Select
								showSearch
								placeholder="请选择"
								value={searchForm.manufacturer}
								onChange={v => {
									this.setState({
										searchForm: {
											...searchForm,
											manufacturer: v
										}
									});
								}}
							>
								{WATER_METER_MANUFACTURER.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={8}>
						<FormItem label={'水表种类:'}>
							<Select
								allowClear
								placeholder="请选择"
								value={this.state.searchForm.waterMeterKind}
								onChange={v => {
									this.setState({
										searchForm: { ...this.state.searchForm, waterMeterKind: v }
									});
								}}
							>
								{WATER_METER_KIND.map((item, index) => {
									return (
										<Option key={index} value={item.value}
											disabled={(searchWaterMeterType === 0 && item.value !== 5 && item.value !== 6 && item.value !== 7) || (searchWaterMeterType === 1 && item.value !== 0 && item.value !== 9 && item.value !== 10) || (searchWaterMeterType === 2 && item.value !== 1 && item.value !== 2 && item.value !== 8)}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					{/* <Col span={8}>
						<FormItem label={'部门:'}>
							<TreeSelect
								style={{ width: '100%' }}
								dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
								placeholder="请选择领用部门"
								treeData={createDepartmentSelect}
								allowClear
								value={searchForm.departmentId}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, departmentId: v }
									});
								}}
								treeDefaultExpandedKeys={[100]}>
							</TreeSelect>
						</FormItem>
					</Col> */}
					<Col span={8}>
						<FormItem label={'用水分类:'}>
							<Select
								allowClear
								placeholder="请选择"
								value={searchForm.waterUseKindType}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, waterUseKindType: v }
									});
									this.getWaterUseKindSelect(v);
								}}
							>
								{WATER_USE_KIND_TYPE.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'用水性质:'}>
							<Select
								allowClear
								mode="multiple"
								placeholder="请选择"
								value={searchForm.waterUseKindIdList}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, waterUseKindIdList: v }
									});
								}}
							>
								{waterUseKindSelect.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
				</Row>

				<Row>
					<Col span={8}>
						<FormItem label={'订单来源:'}>
							<Select
								allowClear
								placeholder="请选择"
								value={searchForm.orderSource}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, orderSource: v }
									});
								}}
							>
								{ORDER_SOURCE.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'付款方式:'}>
							<Select
								placeholder="请选择"
								allowClear
								value={searchForm.chargeWay}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, chargeWay: v }
									});
								}}
							>
								{CHARGE_WAY.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'订单状态:'}>
							<Select
								mode="multiple"
								placeholder="请选择"
								value={searchForm.statusList}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, statusList: v } });
								}} showArrow>
								{ORDER_STATUS.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={8}>
						<FormItem label={'订单时间:'}>
							{getFieldDecorator('rangPicker',
								{
									initialValue: [moment(now.getFullYear() + '-' + moment().format('MM') + '-01', dateFormat), moment(now, dateFormat)],
									rules: [{ type: 'array' }]
								})(
									<RangePicker
										onChange={this.getOrderDate}
										placeholder={['开始时间', '结束时间']} />)}
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'水量:'}>
							<InputGroup>
								<InputNumber
									value={searchForm.waterStartAmount}
									className="noBorderRight"
									style={{ width: '40%' }}
									placeholder="开始吨数"
									onChange={v => {
										this.setState({
											searchForm: { ...searchForm, waterStartAmount: v }
										});
									}}
								/>
								<InputNumber className="midIpt noBorderLeft noBorderRight" style={{ width: '20%' }}
									placeholder="~"
									disabled />
								<InputNumber
									value={searchForm.waterEndAmount}
									className="noBorderLeft"
									style={{ width: '40%' }}
									placeholder="结束吨数"
									onChange={v => {
										this.setState({
											searchForm: { ...searchForm, waterEndAmount: v }
										});
									}}
								/>
							</InputGroup>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'订单金额:'}>
							<InputGroup>
								<Input
									value={searchForm.orderStartAmount}
									className="noBorderRight"
									style={{ width: '40%' }}
									placeholder="开始金额"
									onChange={v => {
										this.setState({
											searchForm: { ...searchForm, orderStartAmount: v.target.value }
										});
									}}
								/>
								<Input className="midIpt noBorderLeft noBorderRight" style={{ width: '20%' }}
									placeholder="~"
									disabled />
								<Input
									value={searchForm.orderEndAmount}
									className="noBorderLeft"
									style={{ width: '40%' }}
									placeholder="结束金额"
									onChange={v => {
										this.setState({
											searchForm: { ...searchForm, orderEndAmount: v.target.value }
										});
									}}
								/>
							</InputGroup>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={8}>
						<FormItem label="是否定量">
							<Select
								placeholder="请选择"
								value={searchForm.fixedQuantity}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, fixedQuantity: v } });
								}} showArrow

							>
								{constants.fixedQuantity.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>

					</Col>
				</Row>

				<Row>
					{expand ? (
						<Fragment>
							<Row>
								<Col span={8}>
									<FormItem label={'小票状态:'}>
										<Select placeholder="请选择" value={searchForm.invoice} allowClear
											onChange={v => {
												this.setState({ searchForm: { ...searchForm, invoice: v } });
											}}>
											<Option value={0}>未打印</Option>
											<Option value={1}>已打印</Option>
										</Select>
									</FormItem>
								</Col>
								<Col span={8}>
									<FormItem label={'税票状态:'}>
										<Select
											allowClear
											placeholder="请选择"
											value={searchForm.taxInvoice}
											onChange={v => {
												this.setState({
													searchForm: { ...searchForm, taxInvoice: v }
												});
											}}
										>
											{PRINT_TYPE.map((item, index) => {
												return (
													<Option key={index} value={item.value}>
														{item.label}
													</Option>
												);
											})}
										</Select>
									</FormItem>
								</Col>
								<Col span={8}>
									<FormItem label={'水票号:'}>
										<InputGroup>
											<Input
												value={searchForm.cleanNoStart}
												className="noBorderRight"
												style={{ width: '40%' }}
												placeholder="开始水票号"
												onChange={v => {
													this.setState({
														searchForm: { ...searchForm, cleanNoStart: v.target.value }
													});
												}}
											/>
											<Input className="midIpt noBorderLeft noBorderRight" style={{ width: '20%' }}
												placeholder="~"
												disabled />
											<Input
												value={searchForm.cleanNoEnd}
												className="noBorderLeft"
												style={{ width: '40%' }}
												placeholder="结束水票号"
												onChange={v => {
													this.setState({
														searchForm: { ...searchForm, cleanNoEnd: v.target.value }
													});
												}}
											/>
										</InputGroup>
									</FormItem>
								</Col>

								<Col span={8}>
									<FormItem label={'污水号:'}>
										<InputGroup>
											<Input
												value={searchForm.sewageNoStart}
												className="noBorderRight"
												style={{ width: '40%' }}
												placeholder="开始污水号"
												onChange={v => {
													this.setState({
														searchForm: { ...searchForm, sewageNoStart: v.target.value }
													});
												}}
											/>
											<Input className="midIpt noBorderLeft noBorderRight" style={{ width: '20%' }}
												placeholder="~"
												disabled />
											<Input
												value={searchForm.sewageNoEnd}
												className="noBorderLeft"
												style={{ width: '40%' }}
												placeholder="结束污水号"
												onChange={v => {
													this.setState({
														searchForm: { ...searchForm, sewageNoEnd: v.target.value }
													});
												}}
											/>
										</InputGroup>
									</FormItem>
								</Col>
								{/* <Col span={8}>
									<FormItem label={'票据编号:'}>
										<Input
											placeholder="请输入票据编号"
											value={searchForm.receiptNo}
											onChange={v => {
												this.setState({
													searchForm: {
														...searchForm,
														receiptNo: v.target.value
													}
												});
											}}
										/>
									</FormItem>
								</Col> */}

							</Row>
							<Row>
								<Col span={8}>
									<FormItem label={'收款人:'}>
										<Select
											placeholder="请选择"
											showSearch
											allowClear
											filterOption={(inputValue, option) => option.props.children.indexOf(inputValue) > -1}
											value={searchForm.createUid}
											onChange={v => {
												this.setState({
													searchForm: { ...searchForm, createUid: v }
												});
											}}
										>
											{createUidSelect && createUidSelect.map((item, index) => {
												return (
													<Option key={index} value={item.value}>
														{item.label}
													</Option>
												);
											})}
										</Select>
									</FormItem>
								</Col>
								<Col span={8}>
									<FormItem label={'用户片区:'}>
										<TreeSelect
											style={{ width: '100%' }} dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
											placeholder="请选择用户片区"
											allowClear
											treeDefaultExpandedKeys={[100]}
											value={searchForm.customerAreaId}
											onChange={v => {
												this.setState({ searchForm: { ...searchForm, customerAreaId: v } });
											}}>
											{this._renderTreeNode(areaSelect)}
										</TreeSelect>
									</FormItem>
								</Col>
								<Col span={8}>
									<FormItem label={'订单片区:'}>
										<TreeSelect
											style={{ width: '100%' }} dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
											placeholder="请选择订单片区"
											allowClear
											treeDefaultExpandedKeys={[100]}
											value={searchForm.areaId}
											onChange={v => {
												this.setState({ searchForm: { ...searchForm, areaId: v } });
											}}>
											{this._renderTreeNode(areaSelect)}
										</TreeSelect>
									</FormItem>
								</Col>
							</Row>
							<Row>
								{/* <Col span={8}>
									<FormItem label={'片区范围:'}>
										<InputGroup compact>
											<Input style={{ width: '40%', textAlign: 'center' }} placeholder="起始片区号"
												value={searchForm.areaStart}
												onChange={v => {
													this.setState({ searchForm: { ...searchForm, areaStart: v.target.value } });
												}} />
											<Input style={{ width: '10%', borderLeft: 0, pointerEvents: 'none', backgroundColor: '#fff' }}
												placeholder="~" disabled />
											<Input style={{ width: '40%', textAlign: 'center', borderLeft: 0 }} placeholder="结束片区号"
												value={searchForm.areaEnd}
												onChange={v => {
													this.setState({ searchForm: { ...searchForm, areaEnd: v.target.value } });
												}} />
										</InputGroup>
									</FormItem>
								</Col> */}
								<Col span={8}>
									<FormItem label={'一般纳税人:'}>
										<Select
											allowClear
											placeholder="请选择"
											value={searchForm.generalTaxpayer}
											onChange={v => {
												this.setState({ searchForm: { ...searchForm, generalTaxpayer: v } });
											}}>
											<Option value={0}>否</Option>
											<Option value={1}>是</Option>
										</Select>
									</FormItem>
								</Col>
								<Col span={8}>
									<FormItem label={'开户时间:'}>
										{getFieldDecorator('rangPicker1',
											{
												rules: [{ type: 'array' }]
											})(
												<RangePicker
													onChange={this.getOpenDate}
													placeholder={['开始时间', '结束时间']} />)}
									</FormItem>
								</Col>
								<Col span={8} style={{ display: expand ? 'block' : 'none' }}>
									<FormItem label={'抄表员:'}>
										<TreeSelect
											style={{ width: '100%' }}
											value={transcriberIdList}
											showSearch
											filterTreeNode={(inputValue, treeNode) => treeNode.props.title.indexOf(inputValue) > -1}
											dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
											placeholder="请选择抄表员"
											allowClear
											multiple={true}
											treeDefaultExpandedKeys={[100]}
											onChange={value => {
												this.setState({
													searchForm: {
														...searchForm,
														transcriberIdList: value
													}
												});
											}}
										>
											{this._renderTreeLoop(transcriberTree)}
										</TreeSelect>
									</FormItem>
								</Col>
								<Col span={8} style={{ display: expand ? 'block' : 'none' }}>
									<FormItem label={'阀门状态:'}>
										<Select
											mode="multiple"
											placeholder="请选择"
											value={searchForm.valveStatusIntegerList}
											onChange={value => {
												this.setState({
													searchForm: {
														...searchForm,
														valveStatusIntegerList: value
													}
												});
											}}
										>
											{
												VALVE_STATUE.map((item, index) => {
													return <Option key={index} value={item.value}>{item.label}</Option>;
												})
											}
										</Select>
									</FormItem>
								</Col>
								<Col span={8} style={{ display: expand ? 'block' : 'none' }}>
									<FormItem label={'星驿付单号:'}>
										<Input
											placeholder="请输入星驿付单号"
											value={searchForm.transactionNo}
											onChange={v => {
												this.setState({ searchForm: { ...searchForm, transactionNo: v.target.value } });
											}}
										/>
									</FormItem>
								</Col>
								{/* <Col span={8}>
									<FormItem label="用户等级：">
										<Select
											allowClear
											value={searchForm.customerLevelId}
											onChange={v => {
												this.setState({ searchForm: { ...searchForm, customerLevelId: v } });
											}}
										>
											<Option key={0} value={null}>{'请选择'}</Option>
											{customerLevels.map((item, index) => {
												return (<Option key={index + 1} value={item.value}>{item.label}</Option>);
											})}</Select>
									</FormItem>
								</Col> */}
								{/*<Col span={8} style={{ display: expand ? 'block' : 'none' }}>*/}
								{/*	<FormItem label={'抄表员:'}>*/}
								{/*		<TreeSelect showSearch*/}
								{/*			placeholder="请选择"*/}
								{/*			allowClear*/}
								{/*			dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}*/}
								{/*			value={this.state.searchForm.transcriber}*/}
								{/*			filterTreeNode={(inputValue, treeNode) => treeNode.props.title.indexOf(inputValue) > -1}*/}
								{/*			onChange={(v) => {*/}
								{/*				this.setState({*/}
								{/*					searchForm: {*/}
								{/*						...searchForm,*/}
								{/*						transcriber: v*/}
								{/*					}*/}
								{/*				});*/}
								{/*			}}>*/}
								{/*			{*/}
								{/*				this.state.allTranscriber.map((item, index) => {*/}
								{/*					return <TreeNode key={index} title={item.personName} value={item.id} />;*/}
								{/*				})*/}
								{/*			}*/}
								{/*		</TreeSelect>*/}
								{/*	</FormItem>*/}
								{/*</Col>*/}
							</Row>
						</Fragment>
					) : null}
					<Col span={24} align="right">
						<Button type="primary" onClick={this.handleSearch}>
							搜索
						</Button>
						&nbsp;&nbsp;&nbsp;&nbsp;
						<Button type="default" onClick={this.handleReset}>
							重置
						</Button>
						<Button className="searchBtn" type="default" onClick={() => this.readCard()}>
							读卡
						</Button>
						<Button type="link" onClick={this.showMoreSearch}>
							{expand ? '关闭高级搜索' : '高级搜索'}
						</Button>
					</Col>
				</Row>
			</Form>
		);
	};

	// 渲染详情
	_renderDetailModal = () => {
		const { detailModalVisible } = this.state;
		const { detail } = this.props;
		return (
			<OrderDetailModal visible={detailModalVisible} detail={detail} handleCancel={this.handleCancel}
			/>
		);
	};

	//写卡
	writeCard (record) {
		if (record.manufacturer === '扬州恒信') {
			let userCard = readCard();
			let cardType = userCard.substring(1, 2);
			let cardNo = null;
			if (cardType === '3' || cardType === '4') {
				cardNo = userCard.substring(2, 12);
			} else {
				cardNo = userCard.substring(2, 10);
			}
			if (cardNo === record.cardNo) {
				let state = '';       //刷卡状态
				let temp = false;     //刷卡标志位

				if (record.waterMeterKindType === '预付费2') {
					state = userCard.substr(-4);
					if (state !== '0000') {
						temp = true;
						message.error('上次缴费未刷表');
					}
				} else if (record.waterMeterKindType === '预付费4442') {
					state = userCard.substr(16, 20);
					if (state !== '0000') {
						temp = true;
						message.error('上次缴费未刷表');
					}
				} else if (record.waterMeterKindType === '阶梯2') {
					state = userCard.substring(20, 26);
					if (parseInt(state, 16) !== 0) {
						temp = true;
						message.error('上次缴费未刷表');
					}
				}
				if (!temp) {
					this.props.getICDetail(record.id, this.getList);
				}
			} else {
				message.error('该卡卡号和当前用户卡号不一致！请检查');
			}
		} else if (record.manufacturer === '河南新天') {
			let resultXT = xtRead();
			resultXT = resultXT.split(',');


			if (parseInt(resultXT[2], 16) !== Number(record.cardNo)) {
				alert('该卡卡号和当前用户卡号不一致！请检查');
			} else {
				if (parseInt(resultXT[5], 16) !== 0) {
					alert('上次缴费未刷表,请先刷表');
				} else {
					this.props.getICDetail(record.id, this.getList);
				}
			}
		} else if (record.manufacturer === '泰安') {
			qsRead().then(qsResult => {

				if (qsResult.errcode == 0) {
					if (qsResult.cardtype < 0) {
						message.error('非用户卡！');
					} else {
						if (qsResult.usercode != record.cardNo) {
							alert('该卡卡号和当前用户卡号不一致！请检查');
							return;
						}
						if (qsResult.data.length > 0 && qsResult.data[0].cardstatus == 0) {
							alert('上次缴费未刷表,请先刷表');
							return;
						}
						this.props.getICDetail(record.id, this.getList);
					}
				}
			})
		}
	}

	//退款审批
	async invalid (record) {
		this.setState({ record: record });
		if (record.waterMeterType === 'IC卡表') {
			if (record.manufacturer === '扬州恒信') {
				let userCard = readCard();
				let cardType = userCard.substring(1, 2);
				let cardNo = null;
				if (cardType === '3' || cardType === '4') {
					cardNo = userCard.substring(2, 12);
				} else {
					cardNo = userCard.substring(2, 10);
				}
				if (cardNo === record.cardNo) {
					let state = '';       //刷卡状态
					let temp = false;
					if (record.waterMeterKind === '预付费2') {
						state = parseInt(userCard.substr(-4), 16);
						if (state === 0) {
							temp = true;
							message.error('上次缴费已刷表订单不可退');
						} else if (state !== record.waterAmount) {
							temp = true;
							message.error('卡上购买的吨数和订单吨数不匹配');
						}
					} else if (record.waterMeterKind === '预付费4442') {
						state = parseInt(userCard.substr(16, 20), 16);
						if (state === 0) {
							temp = true;
							message.error('上次缴费已刷表订单不可退');
						} else if (state !== record.waterAmount) {
							temp = true;
							message.error('卡上购买的吨数和订单吨数不匹配');
						}
					} else if (record.waterMeterKind === '阶梯2') {
						state = parseInt(userCard.substring(20, 26), 16) / 100;
						if (state === 0) {
							temp = true;
							message.error('上次缴费已刷表订单不可退');
						}
					} else {
						state = userCard.substring(13, 14);
						if (state === '3') {
							temp = true;
							message.error('上次缴费已刷表订单不可退');
						}
					}
					if (!temp) {
						this.editRefundModal(true);
						this.props.getRefundReasonSelect();
					}
				} else {
					alert('该卡卡号和当前用户卡号不一致！请检查');
				}
			} else if (record.manufacturer === '河南新天') {
				let resultXT = xtRead();
				resultXT = resultXT.split(',');
				if (parseInt(resultXT[2], 16) !== Number(record.cardNo)) {
					alert('该卡卡号和当前用户卡号不一致！请检查');
				} else {
					if (parseInt(resultXT[5], 16) === 0) {
						alert('上次缴费已刷表,无法退款');
					} else {
						this.editRefundModal(true);
						this.props.getRefundReasonSelect();
					}
				}
			} else if (record.manufacturer === '泰安') {
				let qsResult = await qsRead()
				if (qsResult.errcode == 0) {
					if (qsResult.cardtype < 0) {
						message.error('非用户卡！');
					} else {
						if (qsResult.usercode != record.cardNo) {
							alert('该卡卡号和当前用户卡号不一致！请检查');
							return;
						}
						if (qsResult.data.length > 0 && qsResult.data[0].cardstatus == 1) {
							alert('上次缴费已刷表,无法退款');
							return;
						}
						this.editRefundModal(true);
						this.props.getRefundReasonSelect();
					}
				}
			} else {
				this.editRefundModal(true);
				this.props.getRefundReasonSelect();
			}
		} else {
			this.editRefundModal(true);
			this.props.getRefundReasonSelect();
		}
	}

	//退款
	handleRefund (id) {
		this.props.form.validateFields((error, values) => {
			if (!error) {
				this.setState({ loading: true });
				values.orderId = id;
				this.props.invalid(values, this.getList, (data) => this.editRefundModal(data), () => {
					this.setState({ loading: false });
				});
			}
		});
	}

	// 修改备注
	handleRemark = (id) => {
		this.props.form.validateFields((error, values) => {
			if (!error) {
				this.props.updateRemark({ id, remark: values.remark }, this.getList)
				this.setState({ remarkVisible: false })
			}
		});
	}
	//退款申请弹窗
	_renderRefundModal () {
		const { form, orderRefundReason } = this.props;
		const { record, reFoundVisible, loading } = this.state;
		const { getFieldDecorator } = form;
		const footer = (
			<Fragment>
				<Button key="submit" type="primary" onClick={() => this.handleRefund(record.id)}
					loading={loading}>确定</Button>
				<Button key="back" onClick={() => this.editRefundModal(false)}>取消</Button>
			</Fragment>
		);
		return reFoundVisible && <Modal title="退款" destroyOnClose={true} maskClosable={true} visible={reFoundVisible}
			onCancel={() => this.editRefundModal(false)}
			footer={footer}>
			<Form {...constants.formItemLayout}>
				<Row>
					<Col>
						<FormItem label={'退款原因:'}>
							{getFieldDecorator('reasonId', { rules: [{ required: true, message: '请选择退款原因' }] })
								(<Select placeholder="请选择">
									{orderRefundReason && orderRefundReason.map((item, index) => {
										return (
											<Option key={index} value={item.value}>
												{item.label}
											</Option>
										);
									})}
								</Select>)}
						</FormItem>
					</Col>
					<Col>
						<FormItem label={'退款描述:'}>
							{getFieldDecorator('description')
								(<Input placeholder="请输入" />)}
						</FormItem>
					</Col>
				</Row>
			</Form>
		</Modal>;
	}

	//编辑退款弹窗
	editRefundModal (visible) {
		this.setState({ reFoundVisible: visible });
	}

	_renderEditRemarkModal () {
		const { form } = this.props;
		const { record, remarkVisible } = this.state;
		const { getFieldDecorator } = form;
		const footer = (
			<Fragment>
				<Button key="submit" type="primary" onClick={() => this.handleRemark(record.id)}>确定</Button>
				<Button key="back" onClick={() => this.setState({ remarkVisible: false })}>取消</Button>
			</Fragment>
		);
		return <Modal title="修改备注" destroyOnClose={true} maskClosable={true} visible={remarkVisible}
			onCancel={() => this.setState({ remarkVisible: false })}
			footer={footer}>
			<Form {...constants.formItemLayout}>
				<Row>
					<Col>
						<FormItem label={'备注:'}>
							{getFieldDecorator('remark', {
								initialValue: record && record.remark
							})(<Input placeholder="请输入" />)}
						</FormItem>
					</Col>
				</Row>
			</Form>
		</Modal>;
	}
	//读卡
	async readCard () {
		let cardNo, areaCode = null;
		let hxdll = '';
		if (document.getElementById('hxdll')) {
			hxdll = document.getElementById('hxdll');
		}
		let sunfs = '';
		if (document.getElementById('sunfs')) {
			sunfs = document.getElementById('sunfs');
		}
		//泰安轻松卡
		let qsResult = await qsRead()

		if (qsResult.errcode == 0) {
			if (qsResult.cardtype < 0) {
				message.error('非用户卡！');
			} else {
				cardNo = qsResult.usercode.toString().padStart(5, '0');
			}
		} else {
			//读取新天卡
			let openport = sunfs.openport(3, 104);

			sleep(300);
			let resultXT = sunfs.readcard();

			sunfs.closeport();
			if (resultXT === 1) {
				resultXT = sunfs.fsdata;
			} else {
				resultXT = null;
			}
			if (resultXT !== null) {
				let cardType = resultXT.split(',')[1];
				if (cardType === '0A') {
					cardNo = parseInt(resultXT.split(',')[2], 16);
				} else {
					message.error('非用户卡！');
				}
			} else {
				//读恒信卡
				let readtype = hxdll.chk_card();
				if (readtype > 3) {
					let result = readCard();
					let cardType = result.substring(1, 2);
					if (cardType === '3' || cardType === '4') {
						cardNo = result.substring(2, 12);
					} else {
						cardNo = result.substring(2, 10);
					}
					if (cardType === '1') {
						areaCode = result.substring(12, 18);
					} else if (cardType === '2') {
						areaCode = result.substring(10, 14);
					}
				}
			}
		}

		if (cardNo) {
			const { searchForm } = this.state;
			searchForm.cardNo = cardNo.toString().trim();
			// searchForm.areaCode = areaCode;
			this.setState({ ...searchForm }, () => {
				this.getList();
			});
		} else {
			message.error('读卡器无卡,或该卡为空卡请检查！');
		}
	}

	//选中事件
	onSelectChange = selectedRowKeys => {
		this.setState({ selectedRowKeys });
	};

	//批量打印税票
	batchPrintTax = () => {
		const { selectedRowKeys } = this.state;
		if (selectedRowKeys.length > 0) {
			this.props.batchPrintTax(selectedRowKeys, this.getList);
			this.setState({ selectedRowKeys: [] });
		} else {
			this.getList();
			setTimeout(() => this.openModal(), 2000);
		}
	};

	onTimeOk = (record, dateString) => {
		this.props.updateOrder({ id: record.id, createUid: null, createTime: dateString.format('YYYY-MM-DD HH:mm:ss') });
	};

	openModal () {
		if (this.props.total) {
			Modal.confirm({
				title: '提示',
				content: `未勾选指定用户时,根据查询条件来批量打印.一共${this.props.total}户，确认要打印吗？`,
				okText: '确认',
				cancelText: '取消',
				onOk: () => {
					const { searchForm } = this.state;
					this.props.batchPrintTaxByCondition(searchForm);
				}
			});
		} else {
			message.error('未查询到用户');
		}
	}

	// 批量打印电子发票
	batchPrintETax = () => {
		const { selectedRowKeys } = this.state;
		if (selectedRowKeys.length > 0) {
			this.props.batchPrintETax(selectedRowKeys, this.getList);
			this.setState({ selectedRowKeys: [] });
		} else {
			this.getList();
			setTimeout(() => this.openETaxModal(), 2000);
		}
	};

	openETaxModal () {
		if (this.props.total) {
			Modal.confirm({
				title: '提示',
				content: `未勾选指定用户时,根据查询条件来批量打印.一共${this.props.total}户，确认要打印吗？`,
				okText: '确认',
				cancelText: '取消',
				onOk: () => {
					const { searchForm } = this.state;
					this.props.batchPrintETaxByCondition(searchForm);
				}
			});
		} else {
			message.error('未查询到用户');
		}
	}

	//导出订单
	exportChargeOrder () {
		const { searchForm } = this.state;
		let url = `${process.env.API_ROOT}/api/charge/order/exportChargeOrder`;
		http.export(url, searchForm, (res) => {
			const fileName = '订单明细_' + new Date().getTime() + '.xlsx';
			const blob = new Blob([res]);
			if (window.navigator && window.navigator.msSaveOrOpenBlob) {
				window.navigator.msSaveOrOpenBlob(blob, fileName)
				return
			}
			const downloadElement = document.createElement('a');
			const href = window.URL.createObjectURL(blob);
			downloadElement.href = href;
			downloadElement.download = fileName;
			document.body.appendChild(downloadElement);
			downloadElement.click();
			document.body.removeChild(downloadElement);
			window.URL.revokeObjectURL(href);
		});
	}
	handleOpenInvoice (record) {
		http.restGet(`api/cm/vat/getByCustomer`, record.customerId).then(res => {
			if (res.code === 0) {
				res.data.address ? res.data.remark = res.data.address : '';
				this.setState({
					vatInfo: res.data,
				}, () => {
					this.setState({ invoiceVisible: true, record: record, })
				})
			}
		})
	}
	//确认是否打印
	handlePrint (record) {
		this.props.getByType(0);
		this.props.getPriceDetail(record.waterUseKindId);
		const _this = this;
		confirm({
			title: '操作确认',
			content: '确定要打印小票吗？',
			okText: '确定',
			cancelText: '取消',
			onOk () {
				_this.printInvoice(record);
			}
		});
	}

	//乘法运算
	accMul (num1, num2) {
		let m = 0, s1 = num1.toString(), s2 = num2.toString();
		try {
			m += s1.split('.')[1].length;
		} catch (e) {
		}
		try {
			m += s2.split('.')[1].length;
		} catch (e) {
		}
		return Number(s1.replace('.', '')) * Number(s2.replace('.', '')) / Math.pow(10, m);
	}


	//打印
	async printInvoice (record) {
		const chargeOrder = await http.get("api/charge/order/getDetailById" + '/' + record.id)
		const {
			customerName,
			address,
			cno,
			waterAmount, // --> 总水量
			waterAmountDD, // 订单吨数
			orderAmount, // -->实收总金额
			orderAmountDD, // 订单金额
			chargeDetailList,
			createPersonName,
			waterUseKind: { ladderType, name, waterUseKindDetailList }
		} = chargeOrder.data
		// 单价
		const { cleanWaterFee, waterResourceFee, sewageFee } = waterUseKindDetailList[0]
		// 各水费合计金额
		const { cleanFeeSum, sewageFeeSum, resourceFeeSum } = chargeDetailList.reduce((total, item) => {
			const { cleanFeeSum, sewageFeeSum, resourceFeeSum } = total
			total.cleanFeeSum = new Decimal(cleanFeeSum).add(new Decimal(item.cleanWaterFee))
			total.sewageFeeSum = new Decimal(sewageFeeSum).add(new Decimal(item.sewageFee))
			total.resourceFeeSum = new Decimal(resourceFeeSum).add(new Decimal(item.waterResourceFee))
			return total
		}, { cleanFeeSum: new Decimal(0), sewageFeeSum: new Decimal(0), resourceFeeSum: new Decimal(0) })
		// 有污水则 清水总额 + 水资源总额 否则 取订单总额
		const totalAmount = new Decimal(orderAmount)
		const yyyy = formatDate(new Date(), 'yyyy')
		const MM = formatDate(new Date(), 'MM')
		const dd = formatDate(new Date(), 'dd')

		let LODOP = getLodop()
		if (ladderType !== '非阶梯') {
			// 阶梯小票
			const invoiceSource = 12
			const orderTemplate = await http.restGet(api.getByType, invoiceSource)
			const priceData = waterUseKindDetailList.map(({ ladderLevel, cleanWaterFee }) => ({
				[`ladder${ladderLevel}Count`]: new Decimal(0),
				[`ladder${ladderLevel}Price`]: cleanWaterFee,
				[`ladder${ladderLevel}Amount`]: new Decimal(0)
			})).reduce((total, item) => ({ ...total, ...item }), {})

			chargeDetailList.forEach(({ ladderLevel, waterAmount, cleanWaterFee }) => {
				priceData[`ladder${ladderLevel}Count`] = waterAmount
				priceData[`ladder${ladderLevel}Amount`] = cleanWaterFee
			})
			const param = {
				customerName,
				customerAddress: address,
				cardNo: cno,
				waterUseKindName: name,
				meterAmount: cleanFeeSum,
				totalAmount: totalAmount,
				...priceData,
				resourceCount: MathUtil.comparedTo(resourceFeeSum, new Decimal(0)) > 0 ? waterAmount : 0,
				resourcePrice: waterResourceFee,
				resourceAmount: resourceFeeSum,
				amountBig: digitalUppercase(totalAmount),
				tollName: createPersonName,
				sewageCount: MathUtil.comparedTo(sewageFeeSum, new Decimal(0)) > 0 ? waterAmount : 0,
				sewagePrice: sewageFee,
				sewageAmount: sewageFeeSum,
				yyyy,
				MM,
				dd
			}
			const template = Object.entries(param)
				.map(([k, v]) => ({ key: k, value: v == null ? '' : v }))
				.reduce((total, { key, value }) => total.replace(key, value), orderTemplate.data.content)
			eval(template)
			let result = LODOP.PREVIEW()
			if (result) {
				let param = {};
				param.invoiceSource = 0;
				param.sourceId = record.id;
				param.customerId = record.customerId;
				param.templateId = orderTemplate.data.id;
				param.invoiceAmount = totalAmount;
				param.payTime = record.createTime;
				param.payUid = record.createUid;
				this.props.save(param);
			}
		} else {
			// 非阶梯小票
			const invoiceSource = 11
			const orderTemplate = await http.restGet(api.getByType, invoiceSource)
			const param = {
				customerName,
				cardNo: cno,
				waterUseKindName: name,
				waterAmount: waterAmount,
				amountSmall: totalAmount,
				cleanPrice: cleanWaterFee,
				resourcePrice: waterResourceFee,
				cleanAmount: cleanFeeSum,
				resourceAmount: resourceFeeSum,
				amountBig: digitalUppercase(totalAmount),
				customerAddress: address,
				tollName: createPersonName,
				sewageCount: MathUtil.comparedTo(sewageFeeSum, new Decimal(0)) > 0 ? waterAmount : 0,
				sewagePrice: sewageFee,
				sewageAmount: sewageFeeSum,
				yyyy,
				MM,
				dd
			}
			const template = Object.entries(param)
				.map(([k, v]) => ({ key: k, value: v == null ? '' : v }))
				.reduce((total, { key, value }) => total.replace(key, value), orderTemplate.data.content)
			eval(template)
			let result = LODOP.PREVIEW()
			if (result) {
				let param = {};
				param.invoiceSource = 0;
				param.sourceId = record.id;
				param.customerId = record.customerId;
				param.templateId = orderTemplate.data.id;
				param.invoiceAmount = totalAmount;
				param.payTime = record.createTime;
				param.payUid = record.createUid;
				this.props.save(param);
			}
		}

		// if (MathUtil.comparedTo(sewageFeeSum.abs(), new Decimal(0)) > 0) {
		// 	const invoiceSource = 10
		// 	const sewageTemplate = await http.restGet("api/invoice/template/getByType", invoiceSource)
		// 	const param = {
		// 		customerName,
		// 		cardNo: cno,
		// 		waterUseKindName: name,
		// 		waterAmount,
		// 		price: sewageFee,
		// 		amount: sewageFeeSum,
		// 		amountBig: digitalUppercase(sewageFeeSum),
		// 		amountSmall: sewageFeeSum,
		// 		customerAddress: address,
		// 		sewageCount: MathUtil.comparedTo(sewageFeeSum,new Decimal(0) )>0?waterAmount:0,
		// 		sewagePrice:sewageFee,
		// 		sewageAmount:sewageFeeSum,
		// 		yyyy,
		// 		MM,
		// 		dd,
		// 		tollName: createPersonName
		// 	}
		// 	const template = Object.entries(param)
		// 		.map(([k, v]) => ({ key: k, value: v == null ? '' : v }))
		// 		.reduce((total, { key, value }) => total.replace(key, value), sewageTemplate.data.content)
		// 	eval(template)
		// 	let result = LODOP.PREVIEW()
		// 	if (result) {
		// 		let param = {};
		// 		param.invoiceSource = 0;
		// 		param.sourceId = record.id;
		// 		param.customerId = record.customerId;
		// 		param.templateId = sewageTemplate.data.id;
		// 		param.invoiceAmount = sewageFeeSum;
		// 		param.payTime = record.createTime;
		// 		param.payUid = record.createUid;
		// 		this.props.save(param);
		// 	}
		// }

	}

	//打开修改付款方式弹窗
	handlePayWay (record) {
		this.setState({ payVisible: true, record: record });
	}


	//打开修改付款方式弹窗
	handFixQuantity (record) {
		this.setState({ fixQuantityVisible: true, record: record });
	}
	//打开挂账收回
	handleChargeUp (record) {
		this.setState({ chargeUpVisible: true, record: record });
	}

	//关闭修改付款方式弹窗f
	fixQuantityClose () {
		this.setState({ fixQuantityVisible: false });
	}

	//关闭修改付款方式弹窗
	payWayClose () {
		this.setState({ payVisible: false });
		this.setState({ chargeUpVisible: false });
	}

	//修改付款方式
	editPayWay (record) {
		this.props.form.validateFields((error, values) => {
			if (!error) {
				values.id = record.id;
				this.props.updateChargeWay(values, this.getList, () => this.payWayClose());
			}
		});
	}

	editFixQuantity (record) {
		this.props.form.validateFields((error, values) => {
			if (!error) {
				values.id = record.id;
				this.props.updateFixQuantity(values, this.getList, () => this.fixQuantityClose());
			}
		});
	}


	//打开修改用水性质弹窗
	handleWuk (record) {
		this.setState({ wukVisible: true, record: record });
	}

	//关闭修改用水性质弹窗
	wukClose () {
		this.setState({ wukVisible: false });
	}

	//修改修改用水性质
	editWuk (record) {
		this.props.form.validateFields((error, values) => {
			if (!error) {
				values.id = record.id;
				this.props.updateWuk(values, this.getList, () => this.wukClose());
			}
		});
	}

	handleElePrint (record) {
		this.props.getDetailByCustomer(record.customerId, () => {
			this.setState({ invoiceVisible: true, record: record });

		});
	}

	invoiceHandlePrint (record, type) {
		this.props.form.validateFields((err) => {
			if (!err) {
				let param = {
					orderId: record.id,
					cleanNo: this.props.form.getFieldValue('cleanNo'),
					sewageNo: this.props.form.getFieldValue('sewageNo'),
				};
				if (type == 0) {
					param.invoice = 1;
				}
				if (type == 1) {
					param.taxInvoice = 2;
				}
				this.props.submitInvoice(param);
				this.setState({ inVisible: false })
			}
		});

	}


	summitHandleElePrint (record) {
		const { vatInfo } = this.state;
		this.props.form.validateFields((err, values) => {

			if (!err) {
				let vat = {
					vatNumber: values.vatNumber,
					type: values.type,
					name: values.name,
					address: values.address,
					openBank: values.openBank,
					bankAccount: values.bankAccount,
					phone: values.phone,
					generalTaxpayer: 0,
					email: values.email,
					id: vatInfo.id || null
				}
				let param = { id: record.id, remark: values.remark || values.address, vat: vat }
				this.props.openXsdInvoice(param, this.getList, values, this.getList);
				this.cancelHandleElePrint();
			}
		});


	}



	// 手动出账
	fixedQuantityOutBillByPopulartion = () => {
		const { detail } = this.state;
		this.props.form.validateFields((err, values) => {
			if (err) {
				return;
			}
			values.id = detail.id
			this.props.fixedQuantityOutBillByPopulartion(values, () => {
				this.setState({ fixedQuantityVisible: false })
			});

		});
	};

	// 修改水表
	updateWaterMeter = () => {
		const { detail } = this.state;
		this.props.form.validateFields((err, values) => {

			if (err) {
				return;
			}
			values.id = detail.id
			values.customerId = detail.customerId
			this.props.updateWaterMeter(values, () => {
				this.setState({ updateWaterMeterVisible: false })
			});

		});
	};



	//关闭修改付款方式弹窗
	cancelHandleElePrint () {
		this.setState({ invoiceVisible: false });
	}

	//划账收回
	_renderChargeUp () {
		const { form } = this.props;
		const { record, chargeUpVisible } = this.state;
		const { getFieldDecorator } = form;
		const footer = (
			<Fragment>
				<Button key="submit" type="primary" onClick={() => this.editPayWay(record)}>确定</Button>
				<Button key="back" onClick={() => this.payWayClose()}>取消</Button>
			</Fragment>
		);
		return chargeUpVisible && <Modal title="挂账收回" destroyOnClose={true} maskClosable={true} visible={chargeUpVisible}
			onCancel={() => this.payWayClose()}
			footer={footer}>
			<Form {...constants.formItemLayout}>
				<Row>
					<Col>
						<FormItem label={'付款方式:'}>
							{getFieldDecorator('chargeWay', { initialValue: 3, rules: [{ required: true, message: '请选择付款方式' }] })
								(<Select placeholder="请选择" disabled={true}>
									{constants.payWayUpdate.map((item, index) => {
										return (
											<Option key={index} value={item.value}>
												{item.label}
											</Option>
										);
									})}
								</Select>)}
						</FormItem>
					</Col>
					{
						this.props.form.getFieldValue('chargeWay') === 3 ?
							<Col>
								<FormItem label="转账日期:">
									{getFieldDecorator('wireTransferNo', { rules: [{ required: true, message: '请输入电汇编号' }] })
										(<Input precision={2} />)}
								</FormItem>
							</Col> : void (0)
					}
				</Row>
			</Form>
		</Modal>;
	}


	_xiaopiaoPrint () {
		const { form, customerDetail } = this.props;
		const { record, inVisible, type, title } = this.state;
		const { getFieldDecorator } = form;
		const footer = (
			<Fragment>
				<Button key="submit" type="primary" onClick={() => this.invoiceHandlePrint(record, type)}>提交</Button>
				<Button key="back" onClick={() => this.setState({ inVisible: false })}>取消</Button>
			</Fragment>
		);
		return inVisible && customerDetail &&
			<Modal title={title} destroyOnClose={true} maskClosable={true} visible={inVisible}
				onCancel={() => this.setState({ inVisible: false })}
				footer={footer}>
				<Form {...constants.formItemLayout}>
					<Row>
						<Col>
							<FormItem label={'水票号:'}>
								{getFieldDecorator('cleanNo')(
									<Input
										placeholder="请输入水票号"
									/>
								)
								}
							</FormItem>
						</Col>
						<Col>
							<FormItem label={'污水号:'}>
								{getFieldDecorator('sewageNo')(
									<Input
										placeholder="请输入污水号"
									/>
								)
								}
							</FormItem>
						</Col>
					</Row>
				</Form>
			</Modal>;
	}

	_handleElePrint () {
		const { form, customerDetail } = this.props;
		const { record, invoiceVisible, vatInfo } = this.state;
		const { getFieldDecorator } = form;

		const footer = (
			<Fragment>
				<Button key="submit" type="primary" onClick={() => this.summitHandleElePrint(record)}>提交</Button>
				<Button key="back" onClick={() => this.cancelHandleElePrint()}>取消</Button>
			</Fragment>
		);
		return invoiceVisible && customerDetail &&
			<Modal title="发票开具" destroyOnClose={true} maskClosable={true} visible={invoiceVisible}
				onCancel={() => this.cancelHandleElePrint()}
				footer={footer}>
				<Form {...constants.formItemLayout}>
					<Row>
						<Col>
							<FormItem label={'税号:'}>
								{/* {getFieldDecorator('vatNumber', { initialValue: vatInfo.vatNumber, rules: [{ required: this.props.form.getFieldValue('type') }] })( */}
								{getFieldDecorator('vatNumber', { initialValue: vatInfo.vatNumber })(
									<Input
										placeholder="请输税号"
									/>
								)}
							</FormItem>
						</Col>
						<Col>
							<FormItem label={'开具类型:'}>
								{getFieldDecorator('type', {
									initialValue: vatInfo.type ? (vatInfo.type === "个人" ? 0 : 1) : undefined,
									rules: [
										{
											required: true,
											message: '请选择开具类型'
										}
									]
								})(
									<Select placeholder="请选择开具类型">
										<Option key={0} value={0}>
											个人
										</Option>
										<Option key={1} value={1}>
											企业
										</Option>
									</Select>
								)}
							</FormItem>
						</Col>
						<Col>
							<FormItem label={'发票抬头:'}>
								{getFieldDecorator('name', { initialValue: vatInfo.name, rules: [{ required: true }] })(
									<Input
										placeholder="请输发票抬头"
									/>
								)}
							</FormItem>
						</Col>
						<Col>
							<FormItem label={'用户地址:'}>
								{getFieldDecorator('address', { initialValue: vatInfo.address, rules: [{ required: false }] })(
									<Input
										placeholder="请输用户地址"
									/>
								)}
							</FormItem>
						</Col>
						<Col>
							<FormItem label={'开户银行:'}>
								{getFieldDecorator('openBank', { initialValue: vatInfo.openBank, rules: [{ required: false }] })(
									<Input
										placeholder="请输开户银行"
									/>
								)}
							</FormItem>
						</Col>
						<Col>
							<FormItem label={'银行账号:'}>
								{getFieldDecorator('bankAccount', { initialValue: vatInfo.bankAccount, rules: [{ required: false }] })(
									<Input
										placeholder="请输银行账号"
									/>
								)}
							</FormItem>
						</Col>
						<Col>
							<FormItem label={'电话:'}>
								{getFieldDecorator('phone', { initialValue: vatInfo.phone, rules: [{ required: false }] })(
									<Input
										placeholder="请输入电话"
									/>
								)}
							</FormItem>
						</Col>
						<Col>
							<FormItem label={'邮箱:'}>
								{getFieldDecorator('email', { initialValue: vatInfo.email, rules: [{ required: false }] })(
									<Input
										placeholder="请输入邮箱"
									/>
								)}
							</FormItem>
						</Col>
						{/* <Col>
							<FormItem label={'是否一般纳税人:'}>
								{getFieldDecorator('generalTaxpayer', {
									initialValue: vatInfo.generalTaxpayer,
									rules: [
										{
											required: true,
											message: '是否一般纳税人'
										}
									]
								})(
									<Select placeholder="是否一般纳税人">
										<Option key={1} value={true}>
											是
										</Option>
										<Option key={0} value={false}>
											否
										</Option>
									</Select>
								)}
							</FormItem>
						</Col> */}
						<Col>
							<FormItem label={'备注:'}>
								{getFieldDecorator('remark', { initialValue: vatInfo.remark, rules: [{ required: false }] })(
									<Input
										placeholder="请输入备注"
									/>
								)
								}
							</FormItem>
						</Col>
					</Row>
				</Form>
			</Modal>;
	}

	onChange = (value, selectedOptions) => {

	};

	//修改付款方式
	_renderPayWay () {
		const { form } = this.props;
		const { record, payVisible } = this.state;
		const { getFieldDecorator } = form;
		const footer = (
			<Fragment>
				<Button key="submit" type="primary" onClick={() => this.editPayWay(record)}>确定</Button>
				<Button key="back" onClick={() => this.payWayClose()}>取消</Button>
			</Fragment>
		);
		return payVisible && <Modal title="修改付款方式" destroyOnClose={true} maskClosable={true} visible={payVisible}
			onCancel={() => this.payWayClose()}
			footer={footer}>
			<Form {...constants.formItemLayout}>
				<Row>
					<Col>
						<FormItem label={'付款方式:'}>
							{getFieldDecorator('chargeWay', { rules: [{ required: true, message: '请选择付款方式' }] })
								(<Select placeholder="请选择">
									{constants.payWayUpdate.map((item, index) => {
										return (
											<Option key={index} value={item.value}>
												{item.label}
											</Option>
										);
									})}
								</Select>)}
						</FormItem>
					</Col>
					{
						this.props.form.getFieldValue('chargeWay') === 3 ?
							<Col>
								<FormItem label="转账日期:">
									{getFieldDecorator('wireTransferNo', {
										rules: [{
											required: true,
											message: '请输入转账日期'
										}]
									})
										(<Input precision={2} />)}
								</FormItem>
							</Col> : void (0)
					}
					{
						this.props.form.getFieldValue('chargeWay') === 12 ?
							<Col span={12}>
								<FormItem label="水券号:">
									{getFieldDecorator('wireTransferNo', { rules: [{ required: true, message: '必填' }] })
										(<Input precision={2} style={{ width: 200 }} />)}
								</FormItem>
							</Col> : void (0)

					}
				</Row>
			</Form>
		</Modal>;
	}

	//修改是否定量
	_renderFixQuantity () {
		const { form } = this.props;
		const { record, fixQuantityVisible } = this.state;
		const { getFieldDecorator } = form;
		const footer = (
			<Fragment>
				<Button key="submit" type="primary" onClick={() => this.editFixQuantity(record)}>确定</Button>
				<Button key="back" onClick={() => this.fixQuantityClose()}>取消</Button>
			</Fragment>
		);
		return fixQuantityVisible && <Modal title="修改是否定量" destroyOnClose={true} maskClosable={true} visible={fixQuantityVisible}
			onCancel={() => this.fixQuantityClose()}
			footer={footer}>
			<Form {...constants.formItemLayout}>
				<Row>
					<Col>
						<FormItem label={'是否定量:'}>
							{getFieldDecorator('fixedQuantity', { rules: [{ required: true, message: '修改是否定量' }] })
								(<Select placeholder="请选择">
									{[
										{
											label: '否',
											value: 0
										},
										{
											label: '是',
											value: 1
										},
									].map((item, index) => {
										return (
											<Option key={index} value={item.value}>
												{item.label}
											</Option>
										);
									})}
								</Select>)}
						</FormItem>
					</Col>
				</Row>
			</Form>
		</Modal>;
	}

	//修改用水性质
	_renderWuk () {
		const { form, waterUseKindSelect } = this.props;
		const { record, wukVisible, searchForm } = this.state;
		const { getFieldDecorator } = form;
		const footer = (
			<Fragment>
				<Button key="submit" type="primary" onClick={() => this.editWuk(record)}>确定</Button>
				<Button key="back" onClick={() => this.wukClose()}>取消</Button>
			</Fragment>
		);
		return wukVisible && <Modal title="修改用水性质" destroyOnClose={true} maskClosable={true} visible={wukVisible}
			onCancel={() => this.wukClose()}
			footer={footer}>
			<Form {...constants.formItemLayout}>
				<Row>
					<Col span={24}>
						<FormItem label={'用水分类:'}>
							<Select
								allowClear
								placeholder="请选择"
								onChange={v => {
									// this.setState({
									// 	searchForm: { ...searchForm, waterUseKindType: v }
									// });
									this.getWaterUseKindSelect(v);
								}}
							>
								{WATER_USE_KIND_TYPE.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={24}>
						<FormItem label={'用水性质:'}>
							{getFieldDecorator('waterUseKindId', {
								rules: [
									{
										required: true,
										message: '请选择用水性质'
									}
								]
							})(
								<Select
									allowClear
									placeholder="请选择"
								>
									{waterUseKindSelect.map((item, index) => {
										return (
											<Option key={index} value={item.value}>
												{item.label}
											</Option>
										);
									})}
								</Select>
							)}
						</FormItem>
					</Col>
				</Row>
			</Form>
		</Modal>;
	}

	render () {
		const { page, pageSize, selectedRowKeys, fixedQuantityVisible, updateWaterMeterVisible, detail } = this.state;
		const { dataList, total, orderTotal, createUidSelect, } = this.props;
		const { getFieldDecorator } = this.props.form;

		const columns = [
			{
				title: '用户名称',
				dataIndex: 'customerName',
				key: 'customerName',
				align: 'center'
			},
			{
				title: '用户片区',
				dataIndex: 'customerAreaName',
				key: 'customerAreaName',
				align: 'center'
			},
			// {
			// 	title: '订单片区',
			// 	dataIndex: 'areaName',
			// 	key: 'areaName',
			// 	align: 'center'
			// },
			{
				title: '用户地址',
				dataIndex: 'address',
				key: 'address',
				align: 'center'
			},
			{
				title: '付款方式',
				dataIndex: 'chargeWay',
				key: 'chargeWay',
				align: 'center'
			},
			{
				title: '订单水量',
				dataIndex: 'waterAmount',
				key: 'waterAmount',
				align: 'center'
			},
			{
				title: '水票号',
				dataIndex: 'cleanNo',
				key: 'cleanNo',
				align: 'center'
			},
			{
				title: '污水号',
				dataIndex: 'sewageNo',
				key: 'sewageNo',
				align: 'center'
			},
			{
				title: '订单金额',
				dataIndex: 'orderAmount',
				key: 'orderAmount',
				align: 'center'
			},
			{
				title: '户号',
				dataIndex: 'hno',
				key: 'hno',
				align: 'center'
			},
			{
				title: '订单编号',
				dataIndex: 'orderNo',
				key: 'orderNo',
				align: 'center'
			},
			{
				title: '用户编号',
				dataIndex: 'cno',
				key: 'cno',
				align: 'center'
			},
			{
				title: '用户卡号',
				dataIndex: 'cardNo',
				key: 'cardNo',
				align: 'center'
			},
			{
				title: '水表编号',
				dataIndex: 'waterMeterNo',
				key: 'waterMeterNo',
				align: 'center'
			},
			{
				title: '水表类型',
				dataIndex: 'waterMeterType',
				key: 'waterMeterType',
				align: 'center'
			},
			{
				title: '水表种类',
				dataIndex: 'waterMeterKind',
				key: 'waterMeterKind',
				align: 'center'
			},
			{
				title: '订单来源',
				dataIndex: 'orderSource',
				key: 'orderSource',
				align: 'center',
				render: text => {
					if (text === '红冲') {
						return (
							<span style={{ color: 'red' }}>
								{text}
							</span>
						);
					} else {
						return text;
					}
				}
			},
			// {
			// 	title: '用水分类',
			// 	dataIndex: 'waterUseKindType',
			// 	key: 'waterUseKindType',
			// 	align: 'center'
			// },
			{
				title: '用水性质',
				dataIndex: 'waterUseKindName',
				key: 'waterUseKindName',
				align: 'center'
			},
			{
				title: '收费员',
				dataIndex: 'createPersonName',
				key: 'createPersonName',
				align: 'center'
				// render: (createPersonName, record) => {
				// 	return (
				// 		<Select
				// 			placeholder="请选择"
				// 			showSearch
				// 			allowClear
				// 			disabled={!React.$pmn('REVENUE:PAY_COST_MANAGEMENT:ORDER_MANAGEMENT:UPDATE_ORDER')}
				// 			filterOption={(inputValue, option) => option.props.children.indexOf(inputValue) > -1}
				// 			value={record.createUid}
				// 			onChange={v => {
				// 				this.props.updateOrder({ id: record.id, createUid: v, createTime: null });
				// 				this.getList();
				// 			}}
				// 		>
				// 			{createUidSelect.map((item, index) => {
				// 				return (
				// 					<Option key={index} value={item.value}>
				// 						{item.label}
				// 					</Option>
				// 				);
				// 			})}
				// 		</Select>
				// 	);
				// }
			},
			{
				title: '抄表员',
				dataIndex: 'transcriberName',
				key: 'transcriberName',
				align: 'center'
			},
			{
				title: '阀门状态',
				dataIndex: 'valveStatus',
				key: 'valveStatus',
				align: 'center'
			},
			// {
			// 	title: '收费部门',
			// 	dataIndex: 'departmentName',
			// 	key: 'departmentName',
			// 	align: 'center'
			// },
			{
				title: '缴费时间',
				dataIndex: 'createTime',
				key: 'createTime',
				align: 'center',
				// render: (createTime, record) => {
				// 	return (
				// 		<DatePicker onOk={this.onTimeOk.bind(this, record)}
				// 			disabled={!React.$pmn('REVENUE:PAY_COST_MANAGEMENT:ORDER_MANAGEMENT:UPDATE_ORDER')}
				// 			showTime defaultValue={moment(createTime, 'YYYY-MM-DD HH:mm:ss')} />
				// 	);
				// }
			},
			{
				title: '小票状态',
				key: 'invoice',
				align: 'center',
				render: (text, record, index) => {
					return record.invoice ? '已打印' : '未打印';
				}
			},
			{
				title: '税票状态',
				dataIndex: 'taxInvoice',
				key: 'taxInvoice',
				align: 'center'
			},
			{
				title: '备注',
				dataIndex: 'remark',
				key: 'remark',
				align: 'center'
			},
			{
				title: '订单状态',
				dataIndex: 'status',
				key: 'status',
				align: 'center',
				width: 80,
				fixed: 'right',
				render: text => {
					return (
						<Tag color={orderStatusColorMap.get(text)}>
							{text}
						</Tag>
					);
				}
			},
			{
				title: '操作',
				key: 'operation',
				width: 170,
				align: 'center',
				fixed: 'right',
				render: (text, record, index) => {
					return (
						<span style={{ display: 'flex' }}>
							<Button title='查看' className='btn'
								permission={React.$pmn('REVENUE:PAY_COST_MANAGEMENT:ORDER_MANAGEMENT:VIEW')}
								type="primary" size="small" icon='eye' onClick={() => {
									this.handleView(record);
								}} />
							<Button title='写卡' className='btn'
								permission={React.$pmn('REVENUE:PAY_COST_MANAGEMENT:ORDER_MANAGEMENT:XK')}
								type="primary" size="small" icon='credit-card' onClick={() => {
									this.writeCard(record);
								}}
								disabled={(!((record.waterMeterType === 'IC卡表') && record.status === '已支付待刷卡'))} />

							<Button title='退款' className='btn' type="primary" size="small" icon='property-safety'
								permission={React.$pmn('REVENUE:PAY_COST_MANAGEMENT:ORDER_MANAGEMENT:TK')}
								disabled={(record.orderSource !== '柜台' && record.orderSource !== '微信' && record.orderSource !== '小额借记' && record.orderSource !== '代扣' && record.orderSource !== '终端机' && record.orderSource !== 'APP' && record.orderSource !== '自助终端' && record.orderSource !== '星驿付') || (record.status !== '已支付待刷卡' && record.status !== '已完成')}
								onClick={() => {
									this.invalid(record);
								}} />
							<Button title='修改表号' className='btn' type="primary" size="small" icon='edit' permission={React.$pmn('REVENUE:PAY_COST_MANAGEMENT:ORDER_MANAGEMENT:UPDATE_METER_NO')} onClick={() => { this.setState({ updateWaterMeterVisible: true, detail: record }) }}/>
							<Button title='打印' className='btn' type="primary" size="small" icon='printer'
								permission={React.$pmn('REVENUE:PAY_COST_MANAGEMENT:ORDER_MANAGEMENT:DY')}
								onClick={() => this.handlePrint(record)} />


							{record.fixedQuantityAmount && <Button
								permission={React.$pmn('REVENUE:PAY_COST_MANAGEMENT:ORDER_MANAGEMENT:FIXDATA')}
								title='修复数据' className='btn' type="danger" size="small" icon='fire'

								onClick={() => {
									this.setState({ fixedQuantityVisible: true, detail: record })
								}
								}
							/>}
							<Button title='修改小票' className='btn' type="primary" size="small" icon='profile'
								disabled={(record.taxInvoice !== '未打印')}
								permission={React.$pmn('REVENUE:PAY_COST_MANAGEMENT:ORDER_MANAGEMENT:XGXP')}
								onClick={() => {
									this.setState({ inVisible: true, record: record, type: 0, title: "修改小票" })
								}
								}
							/>
							<Button title='修改税票' className='btn' type="primary" size="small" icon='profile'
								permission={React.$pmn('REVENUE:PAY_COST_MANAGEMENT:ORDER_MANAGEMENT:XGSP')}
								onClick={() => {
									this.setState({ inVisible: true, record: record, type: 1, title: "修改税票" })
								}
								}
							/>
							<Button title='修改付款方式' className='btn' type="primary" size="small" icon='account-book'
								permission={React.$pmn('REVENUE:PAY_COST_MANAGEMENT:ORDER_MANAGEMENT:XGFKFS')}
								onClick={() => this.handlePayWay(record)}
								disabled={record.chargeWay === '现金' || record.chargeWay === 'POS机' || record.chargeWay === '银行回单' || record.chargeWay === '扫码支付' ? false : true} />

							{record.waterMeterType === '机械表' && <Button title='修改是否定量'
								permission={React.$pmn('REVENUE:PAY_COST_MANAGEMENT:ORDER_MANAGEMENT:FIXQUANTITY')}
								className='btn' type="primary" size="small" icon='account-book'
								onClick={() => this.handFixQuantity(record)}
							/>
							}
							<Button title='挂账收回' className='btn' type="primary" size="small" icon='account-book'
								permission={React.$pmn('REVENUE:PAY_COST_MANAGEMENT:ORDER_MANAGEMENT:GZSH')}
								onClick={() => this.handleChargeUp(record)}
								disabled={record.chargeWay === '挂账' ? false : true} />
							<Button title='修改备注' className='btn' type="primary" size="small" icon='edit'
								permission={React.$pmn('REVENUE:PAY_COST_MANAGEMENT:ORDER_MANAGEMENT:XGBZ')}

								onClick={() => { this.setState({ remarkVisible: true, record: record }) }}
							/>
							<Button permission={React.$pmn('REVENUE:PAY_COST_MANAGEMENT:ORDER_MANAGEMENT:WUK')} title='修改用水性质' className='btn' type="primary" size="small" icon='control'
								onClick={() => this.handleWuk(record)}></Button>

							<Button title="开蓝票" className='btn' type="primary"
								permission={React.$pmn('REVENUE:PAY_COST_MANAGEMENT:ORDER_MANAGEMENT:KLP')}

								size="small" icon='credit-card' onClick={() => {
									this.handleOpenInvoice(record)
								}}
								disabled={!((record.status == '已支付待刷卡' || record.status == '已完成') && record.taxInvoice == "未打印")} />
						</span>

					);
				}
			}
		];
		const rowSelection = {
			fixed: true,
			selectedRowKeys,
			onChange: this.onSelectChange
		};
		const plainOptions = [
			'一月',
			'二月',
			'三月',
			'四月',
			'五月',
			'六月',
			'七月',
			'八月',
			'九月',
			'十月',
			'十一月',
			'十二月',
		];
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: (total) => {
				return `共 ${total} 条数据 `;
			},
			onChange: (page) => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		return (
			<div className="shadow-radius order">
				<h1>订单管理</h1>
				<Row>{this._renderSearchForm()}</Row>
				<h1>订单列表</h1>
				<Row span={24}>
					{/* <Button className="searchBtn" type="primary" onClick={this.batchPrintTax}>
						<Icon type="printer" />批量打印税票</Button> */}
					<Button className="searchBtn" type="primary" onClick={() => this.exportChargeOrder()}><Icon
						type="download" />导出订单</Button>
					<Button className="searchBtn" type="primary" onClick={() => this.batchPrintTax()}><Icon
						type="container" />批量开票</Button>
				</Row>
				<Row className="main">
					<Col style={{ marginBottom: 5 }}>
						{/*实收总金额：{orderTotal ? MathUtil.add(orderTotal.orderAmount, orderTotal.refundFee).toString() : ''}元 &emsp;&emsp;&emsp;*/}
						实收总金额：{orderTotal ? orderTotal.orderAmount : ''}元 &emsp;&emsp;&emsp;
						{/*总水量：{orderTotal ? (orderTotal.waterAmount - orderTotal.refundAmount) : ''}吨 &emsp;&emsp;&emsp;*/}
						总水量：{orderTotal ? orderTotal.waterAmount : ''}吨 &emsp;&emsp;&emsp;
						订单总金额: {orderTotal ? orderTotal.orderAmountDD : ''}元 &emsp;&emsp;&emsp;
						订单总水量：{orderTotal ? orderTotal.waterAmountDD : ''}吨 &emsp;&emsp;&emsp;
						退款金额：{orderTotal ? orderTotal.refundFee : ''}元 &emsp;&emsp;&emsp;
						退款水量： {orderTotal ? orderTotal.refundAmount : ''}吨
					</Col>
					<Table
						bordered
						rowSelection={rowSelection}
						scroll={{ x: 3500 }}
						columns={columns}
						rowKey={data => data.id}
						dataSource={dataList}
						pagination={paginationProps} />
				</Row>
				{this._renderDetailModal()}
				{this._renderRefundModal()}
				{this._renderPayWay()}
				{this._renderFixQuantity()}
				{this._renderChargeUp()}
				{this._renderWuk()}
				{this._handleElePrint()}
				{this._xiaopiaoPrint()}
				{fixedQuantityVisible && <Modal title="定量出账" visible={fixedQuantityVisible} onCancel={() => {
					this.setState({ fixedQuantityVisible: false })
				}} footer={null}>
					<Row>
						{
							detail ?
								<div>
									<Form {...constants.formItemLayout}>
										<FormItem label='人口数：'>
											{getFieldDecorator('population', {
												initialValue: detail.population,
												rules: [
													{
														required: true,
														message: '请选择人口数'
													}
												]
											})(
												<Input />
											)}

										</FormItem>


										<FormItem label="定量值">
											{getFieldDecorator('fixedQuantityAmount', {
												initialValue: detail ? detail.fixedQuantityAmount : '',
												rules: [{ required: true, message: '请输入定量值' }]
											})(
												<Select>
													{constants.fixedQuantitySelect.map((item, index) => {
														return (
															<Option key={index} value={item.value}>
																{item.label}
															</Option>
														);
													})}
												</Select>
											)}
										</FormItem>

										<FormItem label='月份：'>
											{getFieldDecorator('months', {
												initialValue: [
													'一月',
													'二月',
													'三月',
													'四月',
													'五月',
													'六月',
													'七月',
													'八月',
													'九月',
													'十月',
													'十一月',
													'十二月'],
												rules: [
													{
														required: true,
														message: '请选择人口数'
													}
												]
											})(
												<Checkbox.Group options={plainOptions} defaultValue={plainOptions}
													onChange={this.onChange} />
											)}

										</FormItem>


									</Form>


								</div>

								: null
						}
						<Col align='center'>
							<Button
								className='btn' type='primary' onClick={this.fixedQuantityOutBillByPopulartion}>确定</Button>
							<Button className='btn' type='default' onClick={() => {
								this.setState({ fixedQuantityVisible: false })
							}}>取消</Button>
						</Col>
					</Row>
				</Modal>
				}



				{updateWaterMeterVisible && <Modal title="修改水表号" visible={updateWaterMeterVisible} onCancel={() => { this.setState({ updateWaterMeterVisible: false }) }} footer={null}>
					<Row>
						{
							detail ?
								<div>
									<Form {...constants.formItemLayout}>
										<FormItem label='水表厂家：'>
											{getFieldDecorator('manufacturer', {
												rules: [
													{
														required: true,
														message: '请选择水表厂家'
													}
												]
											})(
												<Select
													showSearch
													placeholder="请选择"
												>
													{WATER_METER_MANUFACTURER.map((item, index) => {
														return (
															<Option key={index} value={item.value}>
																{item.label}
															</Option>
														);
													})}
												</Select>
											)}

										</FormItem>


										<FormItem label="水表号">
											{getFieldDecorator('no', {
												rules: [{ required: true, message: '请输入水表号' }]
											})(
												<Input
													placeholder="请输入水表编号"
												/>

											)}
										</FormItem>



									</Form>


								</div>

								: null
						}
						<Col align='center'>
							<Button
								className='btn' type='primary' onClick={this.updateWaterMeter}>确定</Button>
							<Button className='btn' type='default' onClick={() => { this.setState({ updateWaterMeterVisible: false }) }}>取消</Button>
						</Col>
					</Row>
				</Modal>}

				{this._renderEditRemarkModal()}
			</div>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('order');
	return {
		fields: data.fields,
		dataList: data.dataList, // 列表数据
		total: data.total, // 总条数
		areaSelect: data.areaSelect, // 片区选择框
		waterUseKindSelect: data.waterUseKindSelect, // 用水性质选择框
		createUidSelect: data.createUidSelect, // 订单创建人选择框
		createDepartmentSelect: data.createDepartmentSelect, // 获取订单部门选择框
		visible: data.visible, // 是否显示弹窗
		detail: data.detail, // 获取详情
		orderTotal: data.orderTotal, // 统计
		customerDetail: data.customerDetail,
		orderDetail: data.orderDetail, //订单详情
		orderRefundReason: data.orderRefundReason,  //订单退款原因
		formwork: data.formwork, //发票模板
		priceDetail: data.priceDetail,   //发票单价
		customerLevels: data.customerLevels, // 用户等级
		transcriberList: data.transcriberList, // 查表员集合
		businessHallSelect: data.businessHallSelect, // 营业厅选择框
		transcriberTree: data.transcriberTree
	};
};
const mapDispatchToProps = dispatch => ({
	setState: data => dispatch(actionCreators.setState(data)), // 设置redux值
	list: data => dispatch(actionCreators.list(data)), // 获取列表
	getAreaSelect: () => dispatch(actionCreators.getAreaSelect()), //获取片区选择框
	getWaterUseKindSelect: data => dispatch(actionCreators.getWaterUseKindSelect(data)), //获取用水性质选择框
	getCreateUidSelect: () => dispatch(actionCreators.getCreateUidSelect()), //获取订单创建人选择框
	getCreateDepartmentSelect: () => dispatch(actionCreators.getCreateDepartmentSelect()), //获取订单部门选择框
	getDetail: data => dispatch(actionCreators.detail(data)), // 获取详情
	getTotal: data => dispatch(actionCreators.getTotal(data)), // 统计
	invalid: (data, list, editRefundModal, loading) => dispatch(actionCreators.invalid(data, list, editRefundModal, loading)),  //退款申请
	getICDetail: (data, list) => dispatch((actionCreators.getICDetail(data, list))),      //查询订单详情
	batchPrintTax: (data, list) => dispatch(actionCreators.batchPrintTax(data, list)),  // 批量打印税票
	getRefundReasonSelect: () => dispatch(actionCreators.getRefundReasonSelect()),       //获取退款原因
	getByType: (data) => dispatch(actionCreators.getByType(data)),    //获取发票模板
	getDetailByCustomer: (data, ops) => dispatch(actionCreators.getDetailByCustomer(data, ops)),    //获取发票模板
	submitInvoice: (data, ops) => dispatch(actionCreators.submitInvoice(data, ops)),    //获取发票模板
	getBySource: (data, printInvoice) => dispatch(actionCreators.getBySource(data, printInvoice)),    //获取打印后的发票号码
	save: (data) => dispatch(actionCreators.getRecordSave(data)),      //保存记录
	updateChargeWay: (data, list, visible) => dispatch(actionCreators.updateChargeWay(data, list, visible)),      //修改付款方式
	updateFixQuantity: (data, list, visible) => dispatch(actionCreators.updateFixQuantity(data, list, visible)),      //修改付款方式
	updateWuk: (data, list, visible) => dispatch(actionCreators.updateWuk(data, list, visible)),      //修改订单用水性质
	getPriceDetail: (data) => dispatch(actionCreators.getPriceDetail(data)),                  //查询发票单价
	batchPrintTaxByCondition: (data) => dispatch(actionCreators.batchPrintTaxByCondition(data)),
	batchPrintETax: (data, list) => dispatch(actionCreators.batchPrintETax(data, list)),  // 批量打印电子发票
	batchPrintETaxByCondition: (data) => dispatch(actionCreators.batchPrintETaxByCondition(data)), // 根据查询条件批量打印电子发票
	customerLevelList: () => dispatch(actionCreators.customerLevelList()), // 用户等级
	fixedQuantityOutBillByPopulartion: (data, list) => dispatch(actionCreators.fixedQuantityOutBillByPopulartion(data, list)), // 手动出账
	updateWaterMeter: (data, list) => dispatch(actionCreators.updateWaterMeter(data, list)), // 手动出账
	updateOrder: (data) => dispatch(actionCreators.updateOrder(data)), // 用户等级
	getBusinessHallSelect: () => dispatch(actionCreators.getBusinessHallSelect()), //获取营业厅选择框
	updateRemark: (data, action) => dispatch(actionCreators.updateRemark(data, action)),
	getTranscriberTree: data => dispatch(actionCreators.getTranscriberTree()), // 获取抄表员树

	openXsdInvoice: (data, list) => dispatch(actionCreators.openXsdInvoice(data, list)),
});
export default connect(
	mapStateToProps,
	mapDispatchToProps
)(Form.create()(Index));
