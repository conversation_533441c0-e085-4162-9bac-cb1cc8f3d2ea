import * as actionTypes from './constants';
import { dataType } from '$utils';

const defaultState = {
	fields: null,
	dataList: [], // 列表数据
	total: 0, // 总条数
	areaSelect: [], // 片区选择框
	waterUseKindSelect: [], // 用水性质选择框
	createUidSelect: [], // 订单创建人选择框
	createDepartmentSelect: [], // 获取订单部门选择框
	detail: null, // 详情
	visible: false, // 涉及到交互的弹窗显示控制 默认为false
	orderTotal: null, // 订单金额统计
	orderDetail: {}, // 订单详情
	current: null, // 收费员发票详情
	formwork: null,    //发票模板
	orderRefundReason: [],  //退款原因
	priceDetail: {},
	customerLevels: [], // 用户等级
	transcriberIdList: [], // 抄表员列表
  customerDetail:{},
	transcriberTree: [], // 抄表员树
};

// 重要提示： 请做好各代码段的注释！！！
export default (state = defaultState, action) => {
	switch (action.type) {
		// 回填组件传给redux的值
		case actionTypes.SET_STATE:
			let _data = action.data;
			if (dataType(_data) === 'Object') {
				state[_data.key] = _data.value;
			}
			if (dataType(_data) === 'Array') {
				_data.forEach(item => {
					state[item.key] = item.value;
				});
			}
			return { ...state };
		// 回填列表数据
		case actionTypes.LIST_RECORD:
			state.dataList = action.data.rows;
			state.total = action.data.total;
			return { ...state };
		// 回填详情信息
		case actionTypes.DETAIL_RECORD:
			state.detail = action.data;
			return { ...state };
		// 获取片区选择框
		case actionTypes.GET_AREA_SELECT:
			state.areaSelect = action.data;
			return { ...state };
		// 获取用水性质选择框
		case actionTypes.GET_WATER_USE_KIND_SELECT:
			state.waterUseKindSelect = action.data;
			return { ...state };
		// 获取订单创建人选择框
		case actionTypes.GET_CREATE_UID_SELECT:
			state.createUidSelect = action.data;
			return { ...state };
		// 获取订单部门选择框
		case actionTypes.GET_CREATE_DEPARTMENT_SELECT:
			state.createDepartmentSelect = action.data;
			return { ...state };
		// 订单金额统计
		case actionTypes.GET_TOTAL:
			state.orderTotal = action.data;
			return { ...state };
	 case actionTypes.GET_BY_CUSTOMER:
			state.customerDetail = action.data;
			 return { ...state };
		// 退款
		case actionTypes.INVALID:
			state.orderDetail = action.data;
			return { ...state };
		// 订单详情
		case actionTypes.GET_IC_DETAIL:
			return { ...state };
		//获取收费员打印发票编号
		case actionTypes.GET_CURRENT_NO:
			state.current = action.data;
			return { ...state };
		//获取发票模板
		case actionTypes.GET_BY_TYPE:
			state.formwork = action.data;
			return { ...state };
		// 批量打印税票
		case actionTypes.BATCH_PRINT_TAX:
			return { ...state };
		// 批量打印电子发票
		case actionTypes.BATCH_PRINT_TAX:
			return { ...state };
		//获取退款原因
		case actionTypes.GET_REFUND_REASON_SELECT:
			state.orderRefundReason = action.data;
			return { ...state };
		//获取退款原因
		case actionTypes.GET_BY_SOURCE:
			state.current = action.data;
			return { ...state };
		//查询发票单价
		case actionTypes.GET_PRICE_DETAIL:
			state.priceDetail = action.data;
			return { ...state };
		//用户等级
		case actionTypes.CUSTOMER_LEVEL_RECORD:
			state.customerLevels = action.data;
			return { ...state };
		// 回填抄表员列表
		case actionTypes.GET_THREE_TRANSCRIBER_LIST:
			state.transcriberList = action.data
			return { ...state };
		// 获取营业厅选择框
		case actionTypes.GET_BUSINESS_HALL_SELECT:
			state.businessHallSelect = action.data;
			return { ...state };
		// 回填抄表员树
		case actionTypes.GET_TRANSCRIBER_TREE:
			state.transcriberTree = action.data
			return { ...state };
		default:
			return state;
	}
};
