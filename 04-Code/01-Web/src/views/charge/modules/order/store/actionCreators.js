import http from '$http';
import * as actionTypes from './constants';
import {message} from 'antd';
import api from './api';
import {
	huaxuRead,
	huaxuWriteCard,
	readCard,
	rechargeXTMF1Card,
	sellConfirm,
	xtWriteCard
} from '@/utils/cardUtil';

// 数据回填
const payload = (type, data) => ({ type, data });

// 组件操作redux数据状态
const setState = data => ({ type: actionTypes.SET_STATE, data });

// 获取列表
const list = data => {
	return async dispatch => {
		const res = await http.post(api.list, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.LIST_RECORD, res.data));
			dispatch(getTotal(data));
		}
	};
};

// 获取详情
const detail = data => {
	return async dispatch => {
		const res = await http.restGet(api.detail, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.DETAIL_RECORD, res.data));
		}
	};
};

// 获取片区选择框
const getAreaSelect = () => {
	return async dispatch => {
		const res = await http.get(api.getAreaSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_AREA_SELECT, res.data));
		}
	};
};

// 获取用水分类
const getWaterUseKindSelect = data => {
	return async dispatch => {
		const res = await http.restGet(api.getWaterUseKindSelect, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_WATER_USE_KIND_SELECT, res.data));
		}
	};
};

// 获取用水分类
const getWaterUseKindSelectByType = data => {
	return async dispatch => {
		const res = await http.restGet(api.getWaterUseKindSelectByType, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_WATER_USE_KIND_SELECT, res.data));
		}
	};
};

// 获取订单创建人选择框
const getCreateUidSelect = () => {
	return async dispatch => {
		const res = await http.get(api.getCreateUidSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_CREATE_UID_SELECT, res.data));
		}
	};
};

// 获取订单部门选择框
const getCreateDepartmentSelect = () => {
	return async dispatch => {
		const res = await http.get(api.getCreateDepartmentSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_CREATE_DEPARTMENT_SELECT, res.data));
		}
	};
};

// 获取订单统计
const getTotal = data => {
	return async dispatch => {
		const res = await http.post(api.getTotal, data, false);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_TOTAL, res.data));
		}
	};
};

//退款前读卡校验
const invalid = (data, list, editRefundModal, loading) => {
	return async dispatch => {
		const res = await http.post(api.invalid, data);
		if (res.code === 0) {
			message.success('退款申请成功！');
			list();
			editRefundModal(false);
		}
		setTimeout(() => {
			loading();
		}, 1500);
	};
};

const getICDetailData = data => {
	return http.restGet(api.getICDetail, data);
};

// 获取订单详情
const getICDetail = (data, list) => {

	return async dispatch => {
		const res = await http.restGet(api.getICDetail, data);
		if (res.code === 0) {
			let orderDetail = res.data;
			let zjsj1 = 0;
			let zjsj2 = 0;
			let zjsj3 = 0;
			let sl1 = 0;
			let sl2 = 0;
			if (orderDetail.waterMeterManufacturer === '扬州恒信') {
				let getAreaCode = readCard();
				let accumulationBuyCount = Number(orderDetail.accumulationBuyCount) - 1; //次数
				let water = 0; //本次
				let totale = 0; //累计
				let areaCode = ''; //区域码
				if (orderDetail.waterMeterKindType === '预付费2') {
					water = orderDetail.waterAmount;
					areaCode = getAreaCode.substring(12, 18);
				} else if (orderDetail.waterMeterKindType === '预付费5') {
					totale = orderDetail.accumulationAmount;
				} else if (orderDetail.waterMeterKindType === '阶梯2') {
					water = orderDetail.writeCardAmount;
					accumulationBuyCount = 0;
					areaCode = getAreaCode.substring(12, 18);
					if (orderDetail.ladderType === '非阶梯') {
						zjsj1 = orderDetail.price1;
						zjsj2 = orderDetail.price1;
						zjsj3 = orderDetail.price1;
						sl1 = 25;
						sl2 = 0;
					} else {
						zjsj1 = orderDetail.price1 ? orderDetail.price1 : 0;
						zjsj2 = orderDetail.price2 ? orderDetail.price2 : 0;
						zjsj3 = orderDetail.price3 ? orderDetail.price3 : 0;
						sl1 = orderDetail.Ladder1 ? orderDetail.Ladder1 : 0;
						sl2 = orderDetail.Ladder2 ? orderDetail.Ladder2 : 0;
					}
				} else if (orderDetail.waterMeterKindType === '阶梯5') {
					totale = orderDetail.accumulationBuyAmount;
					if (orderDetail.ladderType === '非阶梯') {
						zjsj1 = orderDetail.price1;
						zjsj2 = orderDetail.price1;
						zjsj3 = orderDetail.price1;
						sl1 = 25;
						sl2 = 0;
					} else {
						zjsj1 = orderDetail.price1 ? orderDetail.price1 : 0;
						zjsj2 = orderDetail.price2 ? orderDetail.price2 : 0;
						zjsj3 = orderDetail.price3 ? orderDetail.price3 : 0;
						sl1 = orderDetail.Ladder1 ? orderDetail.Ladder1 : 0;
						sl2 = orderDetail.Ladder2 ? orderDetail.Ladder2 : 0;
					}
				} else if (orderDetail.waterMeterKindType === '预付费4442') {
					water = orderDetail.waterAmount;
					areaCode = getAreaCode.substring(10, 14);
				}
				let result = sellConfirm(orderDetail.waterMeterKindType, orderDetail.cardNo, accumulationBuyCount, water, totale, zjsj1, zjsj2, zjsj3, sl1, sl2, areaCode);
				if (result === 0) {
					dispatch(updateStatusCompleted(orderDetail.id, list));
				}
			} else if (orderDetail.waterMeterManufacturer === '深圳华旭') {
				let resultHuaxu = huaxuRead();
				// let accumulationBuyCount = Number(orderDetail.accumulationBuyCount) - 1;
				let accumulationBuyCount = orderDetail.accumulationBuyCount;
				// 9,20011934,01,FFFFFF10000012,0831,
				let cardNo = orderDetail.cardNo;
				let waterMeterNo = resultHuaxu[1] || orderDetail.waterMeterNo;
				if (resultHuaxu[0] == 9) {
					cardNo = resultHuaxu[3].substring(6, resultHuaxu[3].length);
				} else {
					cardNo = resultHuaxu[26].substring(6, resultHuaxu[26].length);
				}
				console.log('cardNo: ', cardNo);
				// let huaxuResult = huaxuWriteCard(orderDetail.cardNo, orderDetail.waterMeterNo, orderDetail.waterAmount, accumulationBuyCount, 0)
				console.log('waterMeterNo: ', waterMeterNo);
				console.log('orderDetail.waterAmount: ', orderDetail.waterAmount);
				console.log('accumulationBuyCount: ', accumulationBuyCount);
				let huaxuResult = huaxuWriteCard(cardNo, waterMeterNo, orderDetail.waterAmount, accumulationBuyCount, 0);
				console.log('huaxuResult: ', huaxuResult);
				if (huaxuResult[0] > 0) {
					dispatch(updateStatusCompleted(orderDetail.id, list));
				}
			} else if (orderDetail.waterMeterManufacturer === '河南新天') {
				let result = -1;
				if (orderDetail.waterMeterKindType === '阶梯57') {
					if (orderDetail.ladderType === '非阶梯') {
						zjsj1 = orderDetail.price1;
						zjsj2 = orderDetail.price1;
						zjsj3 = orderDetail.price1;
						sl1 = orderDetail.ladder1;
						sl2 = orderDetail.ladder1;
					}
					else {
						zjsj1 = orderDetail.price1 ? orderDetail.price1 : 0;
						zjsj2 = orderDetail.price2 ? orderDetail.price2 : 0;
						zjsj3 = orderDetail.price3 ? orderDetail.price3 : 0;
						sl1 = orderDetail.ladder1 ? orderDetail.ladder1 : 0;
						sl2 = orderDetail.ladder2 ? orderDetail.ladder2 : 0;
					}
					result = xtWriteCard(orderDetail.cardNo,sl1,sl2,zjsj1,zjsj2,zjsj3,orderDetail.accumulationBuyAmount, orderDetail.writeCardAmount);
				} else if (orderDetail.waterMeterKindType === 'MF1') {
					//卡号，总金额，本次充值金额
					result = rechargeXTMF1Card(orderDetail.cardNo, orderDetail.accumulationAmount, orderDetail.waterAmount);
				}
				if (result === 1 || result === true) {
					dispatch(updateStatusCompleted(orderDetail.id, list));
				}
			}
		}
	};
};

const updateStatusCompleted = (data, list) => {
	return async dispatch => {
		const res = await http.restGet(api.updateStatusCompleted, data);
		if (res.code === 0) {
			list();
			message.success('写卡成功');
		}
	};
};

//获取发票模板
const getByType = data => {
	return async dispatch => {
		const res = await http.restGet(api.getByType, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_BY_TYPE, res.data));
		}
	};
};

//保存打印记录
const getRecordSave = data => {
	return async dispatch => {
		const res = await http.post(api.getRecordSave, data);
		if (res.code === 0) {
			message.success('打印成功！');
		}
	};
};

// 批量打印税票
const batchPrintTax = (data, list) => {
	return async dispatch => {
		const res = await http.post(api.batchPrintTax, data);
		if (res.code === 0) {
			message.success('打印数据已生成，请在地税系统中打印');
			dispatch(payload(actionTypes.BATCH_PRINT_TAX, res.data));
		}
		list();
	};
};

// 获取退款原因
const getRefundReasonSelect = () => {
	return async dispatch => {
		const res = await http.get(api.getRefundReasonSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_REFUND_REASON_SELECT, res.data));
		}
	};
};

// 获取打印过的发票编号
const getBySource = (data, printInvoice) => {
	return async dispatch => {
		const res = await http.get(`${api.getBySource}/${data.invoiceSource}/${data.sourceId}`);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_BY_SOURCE, res.data));
			printInvoice();
		}
	};
};

//重置发票号码
const updateChargeWay = (data, list, visible) => {
	return async dispatch => {
		const res = await http.post(api.updateChargeWay, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.UPDATE_CHARGE_WAY, res.data));
			list();
			visible();
		}
	};
};

//查询发票单价
const getPriceDetail = data => {
	return async dispatch => {
		const res = await http.restGet(api.getPriceDetail, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_PRICE_DETAIL, res.data));
		}
	};
};

const batchPrintTaxByCondition = data => {
	return async dispatch => {
		const res = await http.post(api.batchPrintTaxByCondition, data);
		if (res.code === 0) {
			message.success('批量打印税票成功！');
		}
	};
};

// 批量打印电子发票
const batchPrintETax = (data, list) => {
	return async dispatch => {
		const res = await http.post(api.batchPrintETax, data);
		if (res.code === 0) {
			message.success('电子发票已推送');
			dispatch(payload(actionTypes.BATCH_PRINT_ETAX, res.data));
		}
		list();
	};
};
// 根据查询条件批量打印电子发票
const batchPrintETaxByCondition = data => {
	return async dispatch => {
		const res = await http.post(api.batchPrintETaxByCondition, data);
		if (res.code === 0) {
			message.success('批量打印电子发票成功！');
		}
	};
};

//修改收费员
const updateOrder = data => {
	return async dispatch => {
		const res = await http.post(api.updateOrder, data);
		if (res.code === 0) {
			message.success('修改成功');
		}
	};
};

//修改污水和水费票号
const updateTaxNo = (data, list) => {
	return async dispatch => {
		const res = await http.post(api.updateTaxNo, data);
		if (res.code === 0) {
			list();
		}
	};
};

const getDetailByCustomer = (data,ops) => {
	return async dispatch => {
		const res = await http.restGet(api.getDetailByCustomer, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_BY_CUSTOMER, res.data));
			ops();
		}
	};
};

const submitInvoice = (data,list) => {
	return async dispatch => {
		const res = await http.post(api.submitInvoice, data);
		if (res.code === 0) {
			message.success('发票开具中 请稍后');
		}
		list();
	};
};

// 批量修改用水单价
const batchUpdateOrderKind = (data, list) => {
	return async dispatch => {
		const res = await http.post(api.batchUpdateOrderKind, data);
		dispatch(payload(actionTypes.BATCH_IMPORT, { visible: false, loading: false }));
		message.success('修改成功！');
		list();
	};
};


export { setState, list, detail, getAreaSelect, getWaterUseKindSelect, getCreateUidSelect, getCreateDepartmentSelect, getTotal, invalid,
	getICDetail, getByType, getRecordSave, batchPrintTax, getRefundReasonSelect, getBySource, updateChargeWay, getPriceDetail, batchPrintTaxByCondition,
	batchPrintETax, batchPrintETaxByCondition, getICDetailData, updateOrder, updateTaxNo,getDetailByCustomer,submitInvoice,batchUpdateOrderKind,getWaterUseKindSelectByType };
