import http from '$http';
import { message } from 'antd';

import { WATER_METER_MANUFACTURER } from '@/constants/waterMeter';
import {
	qsWriteCard,
	readCard,
	sellConfirm,
	xtWriteCard,
} from '@/utils/cardUtil';

import api from './api';
import * as actionTypes from './constants';

// 数据回填
const payload = (type, data) => ({ type, data });

// 组件操作redux数据状态
const setState = data => ({ type: actionTypes.SET_STATE, data });

// 获取列表
const list = data => {
	return async dispatch => {
		const res = await http.post(api.list, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.LIST_RECORD, res.data));
			dispatch(getTotal(data));
		}
	};
};

// 获取详情
const detail = data => {
	return async dispatch => {
		const res = await http.restGet(api.detail, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.DETAIL_RECORD, res.data));
		}
	};
};

// 获取片区选择框
const getAreaSelect = () => {
	return async dispatch => {
		const res = await http.get(api.getAreaSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_AREA_SELECT, res.data));
		}
	};
};

// 获取用水性质
const getWaterUseKindSelect = data => {
	return async dispatch => {
		const res = await http.restGet(api.getWaterUseKindSelect, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_WATER_USE_KIND_SELECT, res.data));
		}
	};
};

// 获取订单创建人选择框
const getCreateUidSelect = () => {
	return async dispatch => {
		const res = await http.get(api.getCreateUidSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_CREATE_UID_SELECT, res.data));
		}
	};
};

// 获取订单部门选择框
const getCreateDepartmentSelect = () => {
	return async dispatch => {
		const res = await http.get(api.getCreateDepartmentSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_CREATE_DEPARTMENT_SELECT, res.data));
		}
	};
};

// 获取订单统计
const getTotal = (data) => {
	return async dispatch => {
		const res = await http.post(api.getTotal, data, false);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_TOTAL, res.data));
		}
	};
};

//退款前读卡校验
const invalid = (data, list, editRefundModal, loading) => {
	return async dispatch => {
		const res = await http.post(api.invalid, data);
		if (res.code === 0) {
			message.success('退款申请成功！');
			list();
			editRefundModal(false);
		}
		setTimeout(() => {
			loading();
		}, 1500);
	};
};

// 获取订单详情
const getICDetail = (data, list) => {
	return async dispatch => {
		const res = await http.restGet(api.getICDetail, data);
		if (res.code === 0) {
			let orderDetail = res.data;
			let zjsj1 = 0;
			let zjsj2 = 0;
			let zjsj3 = 0;
			let sl1 = 0;
			let sl2 = 0;
			if (orderDetail.waterMeterManufacturer === '扬州恒信') {
				let getAreaCode = readCard();
				let accumulationBuyCount = Number(orderDetail.accumulationBuyCount);
				// let accumulationBuyCount = Number(orderDetail.accumulationBuyCount) - 1; //次数
				let water = 0;                                               //本次
				let totale = 0;                                              //累计
				let areaCode = '';                                               //区域码
				if (orderDetail.waterMeterKindType === '预付费2') {
					water = orderDetail.waterAmount;
					areaCode = getAreaCode.substring(12, 18);
				} else if (orderDetail.waterMeterKindType === '预付费5') {
					totale = orderDetail.accumulationAmount;
				}
				else if (orderDetail.waterMeterKindType === '阶梯2') {
					water = orderDetail.writeCardAmount;
					accumulationBuyCount = 0;
					areaCode = getAreaCode.substring(12, 18);
					if (orderDetail.ladderType === '非阶梯') {
						zjsj1 = orderDetail.price1;
						zjsj2 = orderDetail.price1;
						zjsj3 = orderDetail.price1;
						sl1 = 25;
						sl2 = 0;
					} else {
						zjsj1 = orderDetail.price1 ? orderDetail.price1 : 0;
						zjsj2 = orderDetail.price2 ? orderDetail.price2 : 0;
						zjsj3 = orderDetail.price3 ? orderDetail.price3 : 0;
						sl1 = orderDetail.Ladder1 ? orderDetail.Ladder1 : 0;
						sl2 = orderDetail.Ladder2 ? orderDetail.Ladder2 : 0;
					}
				}
				else if (orderDetail.waterMeterKindType === '阶梯5') {
					totale = orderDetail.accumulationBuyAmount;
					if (orderDetail.ladderType === '非阶梯') {
						zjsj1 = orderDetail.price1;
						zjsj2 = orderDetail.price1;
						zjsj3 = orderDetail.price1;
						sl1 = 25;
						sl2 = 0;
					} else {
						zjsj1 = orderDetail.price1 ? orderDetail.price1 : 0;
						zjsj2 = orderDetail.price2 ? orderDetail.price2 : 0;
						zjsj3 = orderDetail.price3 ? orderDetail.price3 : 0;
						sl1 = orderDetail.Ladder1 ? orderDetail.Ladder1 : 0;
						sl2 = orderDetail.Ladder2 ? orderDetail.Ladder2 : 0;
					}
				}
				else if (orderDetail.waterMeterKindType === '预付费4442') {
					water = orderDetail.waterAmount;
					areaCode = getAreaCode.substring(10, 14);
				}
				let result = await sellConfirm(orderDetail.waterMeterKindType, orderDetail.cardNo, accumulationBuyCount, water, totale, zjsj1, zjsj2, zjsj3, sl1, sl2, areaCode);
				console.info("result" + result)
				if (result === 0) {
					dispatch(updateStatusCompleted(orderDetail.id, list));
				}
			}
			else if (orderDetail.waterMeterManufacturer === '河南新天') {
				let result = xtWriteCard(orderDetail.cardNo, orderDetail.accumulationAmount, orderDetail.waterAmount, orderDetail.accumulationBuyCount);
				if (result === 1) {
					dispatch(updateStatusCompleted(orderDetail.id, list));
				}
			}
			else if (orderDetail.waterMeterManufacturer === '泰安') {
				qsWriteCard(orderDetail.cardNo, orderDetail.waterAmount, orderDetail.accumulationBuyCount).then(qsResult => {
					if (qsResult.errcode == 0) {
						dispatch(updateStatusCompleted(orderDetail.id, list));
					} else {
						message.error(qsResult.errmsg)
					}
				})

			}
		}
	};
};

const updateStatusCompleted = (data, list) => {
	return async dispatch => {
		const res = await http.restGet(api.updateStatusCompleted, data);
		if (res.code === 0) {
			list();
			message.success('写卡成功');
		}
	};
};

//获取发票模板
const getByType = (data) => {
	return async dispatch => {
		const res = await http.restGet(api.getByType, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_BY_TYPE, res.data));
		}
	};
};


//获取发票模板
const getDetailByCustomer = (data, ops) => {
	return async dispatch => {
		const res = await http.restGet(api.getDetailByCustomer, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_BY_CUSTOMER, res.data));
			ops();
		}
	};
};


const submitInvoice = (data, ops) => {
	return async dispatch => {
		const res = await http.post(api.submitInvoice, data);
		if (res.code === 0) {
			ops();
		}
	};
};
//保存打印记录
const getRecordSave = (data) => {
	return async dispatch => {
		const res = await http.post(api.getRecordSave, data);
		if (res.code === 0) {
			message.success('打印成功！');
		}
	};
};

// 批量打印税票
const batchPrintTax = (data, list) => {
	return async dispatch => {
		const res = await http.post(api.batchPrintTax, data);
		if (res.code === 0) {
			message.success('打印数据已生成，请在地税系统中打印');
			dispatch(payload(actionTypes.BATCH_PRINT_TAX, res.data));
		}
		list();
	};
};
const openXsdInvoice = (data, list) => {
	return async dispatch => {
		const res = await http.post(api.openXsdInvoice, data);
		if (res.code === 0) {
			message.success('开票成功');
			list();
			return
		}
	};
}
// 获取退款原因
const getRefundReasonSelect = () => {
	return async dispatch => {
		const res = await http.get(api.getRefundReasonSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_REFUND_REASON_SELECT, res.data));
		}
	};
};

// 获取打印过的发票编号
const getBySource = (data, printInvoice) => {
	return async dispatch => {
		const res = await http.get(`${api.getBySource}/${data.invoiceSource}/${data.sourceId}`);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_BY_SOURCE, res.data));
			printInvoice();
		}
	};
};

//重置发票号码
const updateChargeWay = (data, list, visible) => {
	return async dispatch => {
		const res = await http.post(api.updateChargeWay, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.UPDATE_CHARGE_WAY, res.data));
			list();
			visible();
		}
	};
};

const updateFixQuantity = (data, list, visible) => {
	return async dispatch => {
		const res = await http.post(api.updateFixQuantity, data);
		if (res.code === 0) {
			message.success('修改成功');
			list();
			visible();
		}
	};
};



//修改订单用水性质
const updateWuk = (data, list, visible) => {
	return async dispatch => {
		const res = await http.post(api.updateWuk, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.UPDATE_WUK, res.data));
			list();
			visible();
		}
	};
};

//查询发票单价
const getPriceDetail = (data) => {
	return async dispatch => {
		const res = await http.restGet(api.getPriceDetail, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_PRICE_DETAIL, res.data));
		}
	};
};

const batchPrintTaxByCondition = data => {
	return async dispatch => {
		const res = await http.post(api.batchPrintTaxByCondition, data);
		if (res.code === 0) {
			message.success("批量打印税票成功！")
		}
	};
};

// 批量打印电子发票
const batchPrintETax = (data, list) => {
	return async dispatch => {
		const res = await http.post(api.batchPrintETax, data);
		if (res.code === 0) {
			message.success('电子发票已推送');
			dispatch(payload(actionTypes.BATCH_PRINT_ETAX, res.data));
		}
		list();
	};
};
// 根据查询条件批量打印电子发票
const batchPrintETaxByCondition = data => {
	return async dispatch => {
		const res = await http.post(api.batchPrintETaxByCondition, data);
		if (res.code === 0) {
			message.success("批量打印电子发票成功！")
		}
	};
};

//获取用户等级
const updateOrder = (data) => {
	return async dispatch => {
		const res = await http.post(api.updateOrder, data);
		if (res.code === 0) {
			message.success("修改成功")

		}
	};
};
//获取用户等级
const customerLevelList = () => {
	return async dispatch => {
		const res = await http.get(api.customerLevelList);
		if (res.code === 0) {
			dispatch(payload(actionTypes.CUSTOMER_LEVEL_RECORD, res.data));
		}
	};
};


// 手动出账
const fixedQuantityOutBillByPopulartion = (data, list) => {
	return async dispatch => {
		const res = await http.post(api.fixedQuantityOutBillByPopulartion, data);
		if (res.code === 0) {
			list();
			message.success('出账成功！');
		}
	};
};

const updateWaterMeter = (data, list) => {
	return async dispatch => {
		const res = await http.restGet(api.getWaterMeter, data.customerId);
		if (res.code === 0) {
			const v = (WATER_METER_MANUFACTURER.filter(ele => ele.label == res.data.manufacturer))[0].value;
			if (res.data && res.data.waterMeterNo == data.no && v == data.manufacturer && res.data.waterMeterType == '机械表') {
				const req = { id: data.id, waterMeterId: res.data.oldMeterId };
				const res2 = await http.post(api.updateWaterMeter, req);
				if (res2.code === 0) {
					list();
					message.success('修改成功！');
				}
			} else {
				message.error("未查询到");

			}




		}
	};
};



// 获取营业厅选择框
const getBusinessHallSelect = () => {
	return async dispatch => {
		const res = await http.get(api.getBusinessHallSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_BUSINESS_HALL_SELECT, res.data));
		}
	};
};

//获取所有抄表员
const getAllTranscriber = data => {
	return http.post(api.getAllTranscriber, data);
}

const updateRemark = (data, action) => {
	return async dispatch => {
		const res = await http.post(api.updateRemark, data);
		if (res.code === 0) {
			message.success("修改成功")
			action()
		}
	};
}

// 获取抄表员，查表员，远传表抄表员
const getTranscriberTree = () => {
	return async dispatch => {
		const res = await http.get(api.transcriberTree);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_TRANSCRIBER_TREE, res.data));
		}
	}
}

export {
	batchPrintETax,
	batchPrintETaxByCondition,
	batchPrintTax,
	batchPrintTaxByCondition,
	customerLevelList,
	detail,
	fixedQuantityOutBillByPopulartion,
	getAllTranscriber,
	getAreaSelect,
	getBusinessHallSelect,
	getBySource,
	getByType,
	getCreateDepartmentSelect,
	getCreateUidSelect,
	getDetailByCustomer,
	getICDetail,
	getPriceDetail,
	getRecordSave,
	getRefundReasonSelect,
	getTotal,
	getTranscriberTree,
	getWaterUseKindSelect,
	invalid,
	list,
	openXsdInvoice,
	setState,
	submitInvoice,
	updateChargeWay,
	updateFixQuantity,
	updateOrder,
	updateRemark,
	updateWaterMeter,
	updateWuk,
};
