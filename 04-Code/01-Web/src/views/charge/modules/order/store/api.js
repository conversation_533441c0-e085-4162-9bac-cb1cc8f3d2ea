export default {
	list: `/api/charge/order/listPage`,
	detail: `/api/charge/order/getDetailById`,
	getAreaSelect: `/api/sys/area/getTree`,
	getWaterUseKindSelect: `/api/wuk/waterusekind/getSelect`,
	getCreateUidSelect: `/api/charge/order/getCreateUidSelect`,
	getCreateDepartmentSelect: `/api/sys/department/getTreeData`,
	getTotal: `api/charge/order/orderTotal`, // 获取统计
	invalid: `api/charge/order/invalid`, //退款审批
	getICDetail: `api/charge/order/getICDetail`,  //获取订单详情
	updateStatusCompleted: `api/charge/order/updateStatusCompleted`, //修改订单状态
	getCurrentNo: `api/invoice/receive/getCurrentNo`,                //获取收费员打印发票编号
	getByType: `api/invoice/template/getByType`,                     //获取发票模板
	getDetailByCustomer: `api/cm/vat/getByCustomer`,                 //通过用户获取详情
	submitInvoice: `api/charge/order/updateTaxNo`,
	getRecordSave: `api/invoice/record/save`,                         //保存打印记录
	batchPrintTax: `/api/charge/order/batchXsdPrintTax`,	               // 批量打印税票
	getRefundReasonSelect: `/api/sys/dictionary/getSelectByCode/ORDER_REFUND_REASON`, //获取退款原因
	getBySource: `api/invoice/record/getBySource`,                     //获取已打印后的发票号码
	updateChargeWay: `api/charge/order/updateChargeWay`,               //修改付款方式
	updateFixQuantity: `api/charge/order/updateFixQuantity`,
	updateWuk: `api/charge/order/updateOrderWaterUseKind`,               //修改订单用水性质
	getPriceDetail: `/api/wuk/waterusekind/get`,                      //查询发票单价
	batchPrintTaxByCondition: `api/charge/order/batchXsdPrintTaxByCondition`,                    //按条件查询批量打印税票
	batchPrintETax: `/api/charge/order/batchPrintInvoice`,	               // 批量打印电子发票
	batchPrintETaxByCondition: `/api/charge/order/batchPrintInvoiceByCondition`,                    //按条件查询批量打印电子发票
	customerLevelList: `/api/sys/dictionary/getSelectByCode/CUSTOMER_LEVEL`, // 用户等级
	updateOrder: `api/charge/order/updateCreateTimeOrCreateUid`, // 用户等级
	getAllTranscriber: `/api/transcriber/list`,//获取所有抄表员
	getBusinessHallSelect: `api/sys/businessHall/getSelect`, // 获取营业厅下拉框信息
	fixedQuantityOutBillByPopulartion: `api/charge/order/fixData`, // 获取营业厅下拉框信息
	getWaterMeter: `api/cm/customer/waterMeter/change/getSuccessCustomerWaterMeterChange`, // 获取营业厅下拉框信息
	updateWaterMeter: `api/charge/order/updateWaterNo`,
	updateRemark: `api/charge/order/updateRemark`,
	transcriberTree: `api/sys/user/transcriberTree`,
	openXsdInvoice: `api/invoice/xsd/order/openBlueInvoice`
};
