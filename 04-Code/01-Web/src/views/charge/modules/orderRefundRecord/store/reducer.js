import * as actionTypes from "./constants";
import { dataType } from '$utils'

const defaultState = {
	fields: null,
	datalist: [], // 柜台缴费列表数据
	total: 0, // 总条数
	detail: null, // 详情
	visible: false, // 涉及到交互的弹窗显示控制 默认为false
	applyList: [], // 申请人列表
	refundList: [], // 退款人列表
	refundDepartList: [], // 退款人部门
	refundReasonSelect: [], // 退款原因选择框
	areas:[], // 片区
   order: {}, // 统计数据
		recordTotal: null, // 统计数据
};

// 重要提示： 请做好各代码段的注释！！！
export default (state = defaultState, action) => {
	switch (action.type) {

		// 回填组件传给redux的值
		case actionTypes.SET_STATE:
			let _data = action.data;
			if (dataType(_data) === 'Object') {
				state[_data.key] = _data.value;
			}
			if (dataType(_data) === 'Array') {
				_data.forEach(item => {
					state[item.key] = item.value;
				})
			}
			return { ...state };

		// 回填列表数据
		case actionTypes.LIST_RECORD:
			state.datalist = action.data.rows;
			state.total = action.data.total;
			return { ...state };

		case actionTypes.APPLY_RECORD:
			state.applyList = action.data;
			return { ...state };

		case actionTypes.REFUND_RECORD:
			state.refundList = action.data;
			return { ...state };

		case actionTypes.REFUND_DEPART_RECORD:
			state.refundDepartList = action.data;
			return { ...state };

		// 退款原因
		case actionTypes.GET_REFUND_REASON_SELECT:
			state.refundReasonSelect = action.data;
			return { ...state };
		// 片区树
		case actionTypes.GET_AREAS_RECORD:
			state.areas = action.data;
			return { ...state };
	case actionTypes.GET_RECORD_ORDER:
			state.order = action.data;
			return { ...state };
		// 统计数据
		case actionTypes.GET_RECORD_STATISTICS:
			state.recordTotal = action.data;
			return { ...state };

		default:
			return state;
	}
}
