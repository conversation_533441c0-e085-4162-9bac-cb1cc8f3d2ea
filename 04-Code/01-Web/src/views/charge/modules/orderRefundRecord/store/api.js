export default {
	list: `/api/charge/order/refund/listPage`, // 订单退款记录列表
	refundConfirm: `/api/charge/order/refund/confirmRefund`, // 退款
	applyList: `/api/charge/order/refund/getCreateUidSelect`, // 获取申请人
	refundList: `/api/charge/order/refund/getRefundUidSelect`, // 获取退款人
	refundDepartList: `/api/sys/department/getTree`, // 获取退款人部门
	confirmRefundBefore: `/api/charge/order/refund/confirmRefundBefore`,    //卡表确认退款
	getRefundReasonSelect: `/api/sys/dictionary/getSelectByCode/ORDER_REFUND_REASON`,// 退款原因
};
