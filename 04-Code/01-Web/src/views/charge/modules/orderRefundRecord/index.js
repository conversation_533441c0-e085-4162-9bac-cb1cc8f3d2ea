import React, { Component } from 'react';
import { actionCreators } from './store';
import { connect } from 'react-redux';
import http from '$http';
import { Modal, Button, Col, DatePicker, Form, Input, Row, Select, Table, Icon, Tag, message, Cascader } from 'antd';
import { constants } from '$utils';
import { REFUND_STATUS, APPLY_STATUS_BY_OTHER } from '@/constants/order';
import { readCard, xtRead, qsRead, qsEmptyCard, qsOpenCard, qsWriteCard, qsRefundCard, sleep } from '@/utils/cardUtil';
import './index.scss';
import { applyStatusColorMap, applyStatusTkColorMap } from '@/constants/colorStyle';

const FormItem = Form.Item;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { confirm } = Modal;

const defaultSearchForm = {
	chargeOrderStatus: null, // 退款状态
	orderNo: '', // 订单编号
	hno: '', // 户号
	cno: '', // 用户编号
	cardNo: null, // 用户卡号
	applyId: null, // 申请人id
	applyTimeStart: '', // 申请时间开始
	applyTimeEnd: '', // 申请时间结束
	applyStatus: null, //  申请状态
	refundId: null, // 退款人id
	refundTimeStart: '', // 退款时间开始
	refundTimeEnd: '', // 退款时间结束
	refundDepartmentId: null, // 退款人部门id
	reasonId: null,// 退款原因
	areaCode: null
};

class Index extends Component {

	constructor(props) {
		super(props);
		this.state = {
			expand: false, // 搜索项过多的时候，隐藏部分
			page: 1,
			pageSize: 10,
			searchForm: defaultSearchForm
		};
	}

	componentDidMount() {
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields && isTags) {
			this.setState({
				...fields.state
			}, () => {
				this.props.form.setFieldsValue({
					...fields.form
				});
			});
		} else {
			this.getList();
			this.props.getApplyList(); // 申请人列表
			this.props.getRefundList(); // 退款人列表
			this.props.getRefundDepartList(); // 退款部门列表
		}
	}

	componentWillUnmount() {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			});
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			});
		}
	}

	// 获取列表
	getList = () => {
		const { searchForm, page, pageSize } = this.state;
		let params = { page, pageSize, ...searchForm };
		this.props.list(params);
		this.props.getRefundReasonSelect();
	};

	// 显示高级搜索
	showMoreSearch = () => {
		this.setState({
			expand: !this.state.expand
		});
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState({
			page: 1,
			pageSize: size
		}, () => {
			this.getList();
		});
	};

	// 列表分页
	handlePageChange = (page) => {
		this.setState({
			page
		}, () => {
			this.getList();
		});
	};

	//搜索
	handleSearch = () => {
		this.setState({
			page: 1
		}, () => this.getList());
	};

	// 重置搜索条件
	handleReset = () => {
		const { setFieldsValue } = this.props.form;
		setFieldsValue({ rangPicker: [undefined, undefined] });
		setFieldsValue({ rangPickerRefund: [undefined, undefined] });
		const { searchForm } = this.state;
		searchForm.cardNo = null;
		searchForm.areaCode = null;
		this.setState({
			searchForm: Object.assign({}, defaultSearchForm)
		}, () => this.getList());
	};

	//导出
	export() {
		const { searchForm } = this.state;
		let url = `${process.env.API_ROOT}/api/charge/order/refund/exportChargeOrderRefundRecord`;
		http.export(url, searchForm, (res) => {
			const fileName = '订单退款记录_' + new Date().getTime() + '.xlsx';
			const blob = new Blob([res]);
			if (window.navigator && window.navigator.msSaveOrOpenBlob){
					window.navigator.msSaveOrOpenBlob(blob, fileName)
					return
			}
			const downloadElement = document.createElement('a');
			const href = window.URL.createObjectURL(blob);
			downloadElement.href = href;
			downloadElement.download = fileName;
			document.body.appendChild(downloadElement);
			downloadElement.click();
			document.body.removeChild(downloadElement);
			window.URL.revokeObjectURL(href);
		});
	}

	//读卡
	handleReadCard = async () => {
		let cardNo, areaCode = null;
		let hxdll = '';
		if (document.getElementById('hxdll')) {
			hxdll = document.getElementById('hxdll');
		}
		let sunfs = '';
		if (document.getElementById('sunfs')) {
			sunfs = document.getElementById('sunfs');
		}
		//泰安轻松卡
		let qsResult = await qsRead()
		console.log('qsResult: ', qsResult);
		if (qsResult.errcode == 0) {
			if (qsResult.cardtype < 0) {
				message.error('非用户卡！');
			} else {
				cardNo = qsResult.usercode.toString().padStart(5, '0');
			}
		} else {
			//读取新天卡
			let openport = sunfs.openport(3, 104);
			console.log('openport: ', openport);
			sleep(300);
			let resultXT = sunfs.readcard();
			console.log('resultXT: ', resultXT);
			sunfs.closeport();
			if (resultXT === 1) {
				resultXT = sunfs.fsdata;
			} else {
				resultXT = null;
			}
			if (resultXT !== null) {
				let cardType = resultXT.split(',')[1];
				if (cardType === '0A') {
					cardNo = parseInt(resultXT.split(',')[2], 16);
				} else {
					message.error('非用户卡！');
				}
			} else {
				//读恒信卡
				let readtype = hxdll.chk_card();
				if (readtype > 3) {
					let result = readCard();
					let cardType = result.substring(1, 2);
					if (cardType === '3' || cardType === '4') {
						cardNo = result.substring(2, 12);
					} else {
						cardNo = result.substring(2, 10);
					}
					if (cardType === '1') {
						areaCode = result.substring(12, 18);
					} else if (cardType === '2') {
						areaCode = result.substring(10, 14);
					}
				}
			}
		}
		console.log('cardNo: ', cardNo);
		if (cardNo) {
			const { searchForm } = this.state;
			searchForm.cardNo = cardNo.toString().trim();
			// searchForm.areaCode = areaCode;
			this.setState({ ...searchForm }, () => {
				this.getList();
			});
		} else {
			message.error('读卡器无卡,或该卡为空卡请检查！');
		}
	};

	// 确认退款
	handleRefund = async record => {
		let flag = false;
		if (record.chargeWay === '现金' && record.orderStatus === '已支付待刷卡') {
			flag = true;
		} else {
			if (record.waterMeterType === 'IC卡表') {
				if (record.manufacturer === '扬州恒信') {
					let userCard = readCard();
					let cardType = userCard.substring(1, 2);
					let cardNo = null;
					if (cardType === '3' || cardType === '4') {
						cardNo = userCard.substring(2, 12);
					} else {
						cardNo = userCard.substring(2, 10);
					}
					if (cardNo === record.cardNo) {
						let state = '';       //刷卡状态
						if (record.waterMeterKind === '预付费2') {
							state = parseInt(userCard.substr(-4), 16);
							if (state === 0) {
								alert('上次缴费已刷表订单不可退');
							} else if (state !== record.waterAmount) {
								alert('卡上购买的吨数和订单吨数不匹配');
							} else {
								flag = true;
							}
						} else if (record.waterMeterKind === '预付费4442') {
							state = parseInt(userCard.substr(16, 20), 16);
							if (state === 0) {
								alert('上次缴费已刷表订单不可退');
							} else if (state !== record.waterAmount) {
								alert('卡上购买的吨数和订单吨数不匹配');
							} else {
								flag = true;
							}
						} else if (record.waterMeterKind === '阶梯2') {
							state = parseInt(userCard.substring(20, 26), 16) / 100;
							if (state === 0) {
								alert('上次缴费已刷表订单不可退');
							} else {
								flag = true;
							}
						} else {
							state = userCard.substring(13, 14);
							if (state === '3') {
								alert('上次缴费已刷表订单不可退');
							} else {
								flag = true;
							}
						}
					} else {
						alert('该卡卡号和当前用户卡号不一致！请检查');
					}
				} else if (record.manufacturer === '河南新天') {
					let resultXT = xtRead();
					resultXT = resultXT.split(',');
					if (parseInt(resultXT[2], 16) !== Number(record.cardNo)) {
						alert('该卡卡号和当前用户卡号不一致！请检查');
					} else {
						if (parseInt(resultXT[5], 16) === 0) {
							alert('上次缴费已刷表,无法退款');
						} else {
							flag = true;
						}
					}
				} else if (record.manufacturer === '泰安') {
					let qsResult = await qsRead();
					if (qsResult.errcode == 0) {
						if (qsResult.cardtype < 0) {
							message.error('非用户卡！');
						} else {
							if (qsResult.usercode != record.cardNo) {
								alert('该卡卡号和当前用户卡号不一致！请检查');
							} else if (qsResult.data.length > 0 && qsResult.data[0].cardstatus == 1) {
								alert('上次缴费已刷表,无法退款');
							} else {
								flag = true;
							}
						}
					}
				}
			} else {
				flag = true;
			}
		}
		if (flag) {
			confirm({
				title: '操作确认',
				content: <p>是否确认退款？{record.orderSource === '微信' ? '确认退款后订单金额将会退回到用户付款微信账户'
					: `确认退款后退款金额为${record.orderAmount}元`}</p>,
				okText: '确认',
				cancelText: '取消',
				onOk: () => {
					if (record.waterMeterType === 'IC卡表') {
						this.props.confirmRefundBefore(record.id, this.getList);
					} else {
						this.props.refundConfirm(record.id, this.getList);
					}
				}
			});
		}
	};

	// 取消弹窗
	handleCancel = () => {
		this.props.setState([{ key: 'visible', value: false }, { key: 'detail', value: null }]);
		this.props.form.resetFields();
	};

	// 申请时间
	onChangeApply = (date, dateString) => {
		const { searchForm } = this.state;
		this.setState({
			searchForm: {
				...searchForm,
				applyTimeStart: dateString[0],
				applyTimeEnd: dateString[1]
			}
		});
	};

	// 退款时间
	onChangeRefund = (date, dateString) => {
		const { searchForm } = this.state;
		this.setState({
			searchForm: {
				...searchForm,
				refundTimeStart: dateString[0],
				refundTimeEnd: dateString[1]
			}
		});
	};

	// 显示最后一层选项
	displayRender = (label) => {
		return label[label.length - 1];
	};

	// 渲染搜索
	_renderSearchForm = () => {
		const { applyList, refundList, refundDepartList, refundReasonSelect, form } = this.props;
		const { getFieldDecorator } = form;
		const { searchForm, expand } = this.state;
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
				<Col span={8}>
						<FormItem label={'用户户号:'}>
							<Input placeholder="请输入用户户号" value={searchForm.hno}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, hno: v.target.value } });
								}} />
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'订单编号:'}>
							<Input
								placeholder="请输入订单编号"
								value={searchForm.orderNo}
								onChange={v => this.setState({ searchForm: { ...searchForm, orderNo: v.target.value } })}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'用户编号:'}>
							<Input
								placeholder="请输入用户编号"
								value={searchForm.cno}
								onChange={v => this.setState({ searchForm: { ...searchForm, cno: v.target.value } })}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'用户卡号:'}>
							<Input
								placeholder="请输入用户卡号"
								value={searchForm.cardNo}
								onChange={v => this.setState({ searchForm: { ...searchForm, cardNo: v.target.value } })}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'申请人:'}>
							<Select
								placeholder='请选择'
								value={searchForm.applyId}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, applyId: v } });
								}}
							>
								{
									[{ label: '全部', value: null }, ...applyList].map((item, index) => {
										return <Option key={index} value={item.value}>{item.label}</Option>;
									})
								}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'申请时间:'}>
							{getFieldDecorator('rangPicker', { rules: [{ type: 'array' }] })(<RangePicker
								onChange={this.onChangeApply}
								placeholder={['开始时间', '结束时间']} />)}
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'申请状态:'}>
							<Select
								placeholder='请选择'
								value={searchForm.applyStatus}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, applyStatus: v } });
								}}>
								{
									APPLY_STATUS_BY_OTHER.map((item, index) => {
										return <Option key={index} value={item.value}>{item.label}</Option>;
									})
								}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'退款状态:'}>
							<Select
								placeholder='请选择'
								value={searchForm.chargeOrderStatus}
								onChange={v => {
									this.setState({
										searchForm: {
											...searchForm,
											chargeOrderStatus: v
										}
									});
								}}
							>
								{
									REFUND_STATUS.map((item, index) => {
										return <Option key={index} value={item.value}>{item.label}</Option>;
									})
								}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'退款人:'}>
							<Select
								placeholder='请选择'
								value={searchForm.refundId}
								onChange={v => {
									this.setState({
										searchForm: {
											...searchForm,
											refundId: v
										}
									});
								}}
							>
								{
									[{ label: '全部', value: null }, ...refundList].map((item, index) => {
										return <Option key={index} value={item.value}>{item.label}</Option>;
									})
								}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'退款部门:'}>
							<Cascader
								placeholder='请选择'
								options={refundDepartList}
								displayRender={this.displayRender}
								fieldNames={
									{
										label: 'name',
										value: 'id',
										children: 'children'
									}
								}
								// expandTrigger="hover"
								changeOnSelect
								onChange={v => this.setState({ searchForm: { ...searchForm, refundDepartmentId: v[v.length - 1] } })}
							/>
						</FormItem>
						{/* <FormItem label={'退款部门:'}>
							<Select
								placeholder='请选择'
								value={searchForm.refundDepartmentId}
								onChange={v => {
									this.setState({
										searchForm: {
											...searchForm,
											refundDepartmentId: v
										}
									});
								}}
							>
								{
									[{ label: '全部', value: null }, ...refundDepartList].map((item, index) => {
										return <Option key={index} value={item.value}>{item.label}</Option>;
									})
								}

							</Select>
						</FormItem> */}
					</Col>
					<Col span={8} style={{ display: expand ? 'block' : 'none' }}>
						<FormItem label={'退款时间:'}>
							{getFieldDecorator('rangPickerRefund', { rules: [{ type: 'array' }] })(<RangePicker
								onChange={this.onChangeRefund}
								placeholder={['开始时间', '结束时间']} />)}
						</FormItem>
					</Col>
					<Col span={8} style={{ display: expand ? 'block' : 'none' }}>
						<FormItem label={'退款原因:'}>
							<Select
								placeholder='请选择'
								value={searchForm.reasonId}
								onChange={v => {
									this.setState({
										searchForm: {
											...searchForm,
											reasonId: v
										}
									});
								}}
							>
								{
									[{ label: '全部', value: null }, ...refundReasonSelect].map((item, index) => {
										return <Option key={index} value={item.value}>{item.label}</Option>;
									})
								}
							</Select>
						</FormItem>
					</Col>
				</Row>

				<Col span={24} align="right">
					<Button className="searchBtn" type="primary" onClick={this.handleSearch}>搜索</Button>
					<Button className="searchBtn" type="default" onClick={this.handleReset}>重置</Button>
					<Button className="searchBtn" type="default" onClick={this.handleReadCard}>读卡</Button>
					<Button type="link" onClick={this.showMoreSearch}>{expand ? '关闭高级搜索' : '高级搜索'}</Button>
				</Col>

			</Form>
		);
	};

	render() {
		const { page, pageSize } = this.state;
		const { datalist, total } = this.props;
		const columns = [
			{
				title: '订单编号',
				dataIndex: 'orderNo',
				key: 'orderNo',
				align: 'center'
			},
			{
				title: '用户编号',
				dataIndex: 'cno',
				key: 'cno',
				align: 'center'
			},
			{
				title: '用户名称',
				dataIndex: 'customerName',
				key: 'customerName',
				align: 'center'
			},
			{
				title: '片区',
				dataIndex: 'areaName',
				key: 'areaName',
				align: 'center'
			},
			{
				title: '用户地址',
				dataIndex: 'customerAddress',
				key: 'customerAddress',
				align: 'center'
			},
			{
				title: '订单水量',
				dataIndex: 'waterAmount',
				key: 'waterAmount',
				align: 'center'
			},
			{
				title: '订单金额',
				dataIndex: 'orderAmount',
				key: 'orderAmount',
				align: 'center'
			},
			{
				title: '订单来源',
				dataIndex: 'orderSource',
				key: 'orderSource',
				align: 'center'
			},
			{
				title: '付款方式',
				dataIndex: 'chargeWay',
				key: 'chargeWay',
				align: 'center'
			},
			{
				title: '收费员',
				dataIndex: 'chargeOrderName',
				key: 'chargeOrderName',
				align: 'center'
			},
			{
				title: '收费部门',
				dataIndex: 'chargeOrderDepartmentName',
				key: 'chargeOrderDepartmentName',
				align: 'center'
			},
			{
				title: '申请人',
				dataIndex: 'applyName',
				key: 'applyName',
				align: 'center'
			},
			{
				title: '申请时间',
				dataIndex: 'createTime',
				key: 'createTime',
				align: 'center'
			},
			{
				title: '申请状态',
				dataIndex: 'applyStatus',
				key: 'applyStatus',
				align: 'center',
				render: text => {
					return (
						<Tag color={applyStatusColorMap.get(text)}>
							{text}
						</Tag>
					);
				}
			},
			{
				title: '退款原因',
				dataIndex: 'reasonValue',
				key: 'reasonValue',
				align: 'center'
			},
			{
				title: '退款描述',
				dataIndex: 'description',
				key: 'description',
				align: 'center'
			},
			{
				title: '退款人',
				dataIndex: 'refundName',
				key: 'refundName',
				align: 'center'
			},
			{
				title: '退款时间',
				dataIndex: 'refundTime',
				key: 'refundTime',
				align: 'center'
			},
			{
				title: '退款状态',
				dataIndex: 'chargeOrderStatus',
				key: 'chargeOrderStatus',
				align: 'center',
				width: 80,
				fixed: 'right',
				render: text => {
					return (
						<Tag color={applyStatusTkColorMap.get(text)}>
							{text}
						</Tag>
					);
				}
			},
			{
				title: '操作',
				width: 100,
				align: 'center',
				fixed: 'right',
				render: (text, record, index) => {
					return (
						<span>
							<Button
								title='确认退款'
								className='btn'
								disabled={!(record.applyStatus === '已完成' && record.chargeOrderStatus === '待退款')}
								type="primary"
								size="small"
								icon="pay-circle"
								onClick={() => this.handleRefund(record)}
							/>
							&nbsp;
						</span>
					);
				}
			}
		];
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: (total) => {
				return `共 ${total} 条数据 `;
			},
			onChange: (page) => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		return (
			<div className="shadow-radius">
				<h1>订单退款记录</h1>
				{this._renderSearchForm()}
				<Row span={24}>
					<Button className="searchBtn" type="primary" onClick={() => this.export()}><Icon
						type="download" />导出订单退款记录</Button>
				</Row>
				<Row className='main'>
					<Table
						bordered
						scroll={{ x: 2800 }}
						columns={columns}
						rowKey={() => Math.random()}
						dataSource={datalist}
						pagination={paginationProps} />
				</Row>
			</div>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('orderRefundRecord');
	return {
		fields: data.fields,
		datalist: data.datalist, // 列表数据
		total: data.total, // 总条数
		visible: data.visible, // 是否显示弹窗
		applyList: data.applyList, // 申请人列表
		refundList: data.refundList, // 退款人列表
		refundDepartList: data.refundDepartList, // 退款部门列表
		refundReasonSelect: data.refundReasonSelect // 退款原因
	};
};
const mapDispatchToProps = dispatch => ({
	setState: data => dispatch(actionCreators.setState(data)), // 设置redux值
	list: data => dispatch(actionCreators.list(data)), // 获取列表
	refundConfirm: (data, list) => dispatch(actionCreators.refundConfirm(data, list)), // 确认退款
	confirmRefundBefore: (data, list) => dispatch(actionCreators.confirmRefundBefore(data, list)), //卡表退款
	getApplyList: () => dispatch(actionCreators.applyList()), // 申请人列表
	getRefundList: () => dispatch(actionCreators.refundList()), // 退款人列表
	getRefundDepartList: () => dispatch(actionCreators.refundDepartList()), // 退款部门列表
	getRefundReasonSelect: () => dispatch(actionCreators.getRefundReasonSelect()) // 退款部门列表
});
export default connect(
	mapStateToProps,
	mapDispatchToProps
)((Form.create())(Index));
