import './index.scss';

import React, {
	Component,
	Fragment,
} from 'react';

import http from '$http';
import { constants } from '$utils';
import {
	Button,
	Col,
	DatePicker,
	Form,
	Input,
	message,
	Modal,
	PageHeader,
	Row,
	Select,
	Table,
	TreeSelect,
} from 'antd';
import moment from 'moment';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';

import { WATER_METER_KIND } from '@/constants/waterMeter';
import {
	_getXT57ReadPort,
	readCard,
	readXTMF1Card,
	sleep,
} from '@/utils/cardUtil';

import { actionCreators } from './store';

const InputGroup = Input.Group;

const { confirm } = Modal;
const { RangePicker } = DatePicker;
const { TreeNode } = TreeSelect;
const FormItem = Form.Item;
const { Option } = Select;
const defaultSearchForm = {
	cno: undefined,
	areaId: undefined,
	name: undefined,
	address: undefined,
	thisCopyDate: '',
	cardNo: null,
	waterMeterNo: null,
	waterMeterKindType: null,
	remainFeeStart: '',
	remainFeeEnd: '',
};

class Index extends Component {
	constructor(props) {
		super(props);
		this.state = {
			page: 1,
			pageSize: 10,
			searchForm: { ...defaultSearchForm },
			selectedRowKeys: [],
			datePickerValue: undefined,
			modalVisible: false,
			updateWheelPresentNumberVisible: false,
			record: {},
			detail: {
				customerId: undefined,
				page: undefined,
				pageSize: 10
			},
		};
	}

	componentDidMount () {
		this.getList();
		this.props.getAreaSelect();
	}
	// 获取列表
	getList = () => {
		const { page, pageSize, searchForm } = this.state;
		this.props.list(Object.assign({ ...searchForm }, { page, pageSize }));
	};
	// 列表分页
	handlePageChange = (page) => {
		this.setState({ page }, () => {
			this.getList();
		});
	};
	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState({ page: 1, pageSize: size }, () => {
			this.getList();
		});
	};
	// 搜索功能
	handleSearch = () => {
		this.setState({ page: 1, selectedRowKeys: [] }, () => {
			this.getList();
		});
	};

	// 重置搜索
	handleReset = () => {
		this.setState({
			searchForm: Object.assign({}, defaultSearchForm),
			datePickerValue: undefined,
			selectedRowKeys: []
		}, () => {
			this.getList();
		});
	};

	onSelectChange = (selectedRowKeys) => {
		this.setState({ selectedRowKeys: selectedRowKeys });
	};
	//发送
	handleSend = () => {
		const { selectedRowKeys } = this.state;
		if (selectedRowKeys.length > 0) {
			this.setState({ visible: true });
		} else {
			this.getList();
			setTimeout(() => this.openModal(), 1000);
		}
	};

	openModal () {
		if (this.props.total) {
			Modal.confirm({
				title: '提示',
				content: `未勾选指定用户时,根据查询条件来批量发送.一共${this.props.total}户，确认要发送吗？`,
				okText: '确认',
				cancelText: '取消',
				onOk: () => {
					this.setState({ visible: true });
				}
			});
		} else {
			message.error('未查询到用户');
		}
	}
	// 取消弹窗
	handleCancel = () => {
		this.setState({ modalVisible: false });
	};
	handleAdd = (record) => {
		const { detail: { pageSize } } = this.state
		this.props.form.resetFields();
		this.setState({
			modalVisible: true,
			record,
			detail: { customerId: record.customerId, page: 1, pageSize }
		}, this.getDetailList);
	};
	handleUpdateWheelPresentNumber = (record) => {
		this.props.form.resetFields();
		this.setState({
			updateWheelPresentNumberVisible: true,
			record
		});
	};
	handleUpdateWheelPresentNumberCancel = () => {
		this.setState({ updateWheelPresentNumberVisible: false });
	};
	UpdateWheelPresentNumber = (e) => {
		const { record } = this.state
		this.props.form.validateFields((err, values) => {
			let param = { id: record.customerId, wheelPresentNumber: values.wheelPresentNumber }
			if (!err) {
				this.setState({ doLoading: true });
				http.post(`api/cm/customer/updateWheelPresentNumber`, param).then(res => {
					if (res.code === 0) {
						message.success('操作成功');
						this.setState({ doLoading: false });
						this.setState({ updateWheelPresentNumberVisible: false });
						this.getDetailList()
						this.getList()
					}
				}).catch(err => {
					this.setState({ doLoading: false });
				});
			}
		})
	}
	// 删除记录
	handleDel = record => {
		const _this = this;
		confirm({
			title: '确认操作',
			content: '是否确认删除该条记录？',
			okText: '确定',
			okType: 'danger',
			cancelText: '取消',
			onOk () {
				_this.props.del(record.id, () => {
					_this.getDetailList()
					_this.getList()
				});
			},
			onCancel () {
				console.log('Cancel');
			}
		});
	};
	// 提交信息
	handleSubmit = (e) => {
		e.preventDefault();
		const { detail: { customerId } } = this.state
		this.props.form.validateFields(['thisNum', 'thisCopyDate','remark'], (err, values) => {
			if (!err) {
				const { thisCopyDate } = values
				this.props.add({ ...values, thisCopyDate: thisCopyDate.format('YYYY-MM-DD'), customerId }, this.getList);

				this.handleCancel();
			}
		});
	};
	//递归渲染
	_renderTreeNode = treeData => {
		return treeData.map(tree => {
			if (tree.children) {
				return (
					<TreeNode key={tree.id} title={tree.name} value={tree.id}>
						{this._renderTreeNode(tree.children)}
					</TreeNode>
				);
			} else {
				return <TreeNode key={tree.id} title={tree.name} value={tree.id} />;
			}
		});
	};



	//读卡
	readCard () {
		let cardNo = null;
		let waterMeterNo = null;
		let waterMeterKindType = null;
		let hxdll = '';
		if (document.getElementById('hxdll')) {
			hxdll = document.getElementById('hxdll');
		}
		//读恒信卡
		let readtype = hxdll.chk_card();
		console.log('userInfo read hx: ', readtype);
		if (readtype > 3) {
			let result = readCard();
			console.log('result: ', result);
			let cardType = result.substring(1, 2);
			if (cardType === '3' || cardType === '4') {
				cardNo = result.substring(2, 12);
			} else {
				cardNo = result.substring(2, 10);
			}
		}
		// 读华旭卡
		let resultHuaxu = SZHXMETERCARD_Web.HXCD_4442_DisposeData_Web('1|1|', '').split('|');
		console.log('userInfo read resultHuaxu: ', resultHuaxu);
		if (resultHuaxu[0] > 0) {
			if (resultHuaxu[0] == 9) {
				cardNo = resultHuaxu[3].substring(6, resultHuaxu[3].length);
			} else {
				cardNo = resultHuaxu[26].substring(6, resultHuaxu[26].length);
			}
			waterMeterNo = resultHuaxu[1];
		}
		//读取新天57卡
		let openport = sunfs.openport(_getXT57ReadPort(), 192);
		console.log('openport 3 104: ', openport);
		sleep(300);
		let resultXT = sunfs.readcard();
		console.log('userInfo resultXT: ', resultXT);
		if (resultXT === 1) {
			resultXT = sunfs.fsdata;
		} else {
			resultXT = null;
		}
		console.log('userInfo resultXT: ', resultXT);
		if (resultXT !== null) {
			let cardType = resultXT.split(',')[1];
			if (cardType === '04') {
				cardNo = parseInt(resultXT.split(',')[2], 16);
				waterMeterKindType = 8;
			} else {
				message.error('非用户卡！');
			}
		}
		sunfs.closeport();
		//MF1
		try {
			let mf1 = readXTMF1Card(true);
			console.log('mf1读卡结果：', mf1);
			if (mf1) {
				if (mf1.cardType != '1') {
					message.warning('当前卡座上非mf1用户卡！');
				} else {
					cardNo = mf1.cardNo;
					waterMeterKindType = 9;
				}
			}
		} catch (e) {
			console.log('mf1读卡异常: ', e);
		}
		console.log('cardNo: ', cardNo);
		if (cardNo) {
			const { searchForm } = this.state;
			searchForm.cardNo = cardNo.toString().trim();
			searchForm.waterMeterNo = waterMeterNo;
			searchForm.waterMeterKindType = waterMeterKindType;
			this.setState({ ...searchForm }, () => {
				this.getList();
			});
		} else {
			message.error('读卡器无卡,或该卡为空卡请检查！');
		}
	}


	// 渲染搜索
	renderSearchForm = () => {
		const { searchForm, datePickerValue } = this.state
		const { areaSelect } = this.props
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					<Col span={8}>
						<FormItem label="用户编号：">
							<Input
								placeholder="用户编号"
								value={searchForm.cno}
								onChange={(v) =>
									this.setState({ searchForm: { ...searchForm, cno: v.target.value } })
								}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label="片区：">
							<TreeSelect
								style={{ width: '100%' }} dropdownStyle={{ maxHeight: 400, overflow: 'auto' }} placeholder="请选择片区"
								allowClear
								treeDefaultExpandedKeys={[100]}
								value={searchForm.areaId}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, areaId: v } });
								}}>
								{this._renderTreeNode(areaSelect)}
							</TreeSelect>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label="用户地址：">
							<Input
								placeholder="用户地址"
								value={searchForm.address}
								onChange={(v) =>
									this.setState({ searchForm: { ...searchForm, address: v.target.value } })
								}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label="用户名称：">
							<Input
								placeholder="用户名称"
								value={searchForm.name}
								onChange={(v) =>
									this.setState({ searchForm: { ...searchForm, name: v.target.value } })
								}
							/>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label="日期：">
							<DatePicker
								style={{ width: '100%' }}
								value={datePickerValue}
								onChange={(date, thisCopyDate) => this.setState({ searchForm: { ...searchForm, thisCopyDate }, datePickerValue: date })}
								placeholder={'日期'}
							/>
						</FormItem>
					</Col>

					<Col span={8}>
						<FormItem label={'水表编号:'}>
							<Input
								placeholder="请输入水表编号"
								value={searchForm.waterMeterNo}
								onChange={v => {
									this.setState({
										searchForm: {
											...searchForm,
											waterMeterNo: v.target.value
										}
									});
								}}
							/>
						</FormItem>
					</Col>

					<Col span={8}>
						<FormItem label={'用户卡号:'}>
							<Input
								placeholder="请输入用户卡号"
								value={searchForm.cardNo}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, cardNo: v.target.value } });
								}}
							/>
						</FormItem>
					</Col>

					<Col span={8}>
						<FormItem label={'水表种类:'}>
							<Select
								showSearch
								allowClear
								placeholder="请选择"
								value={searchForm.waterMeterKindType}
								onChange={v => {
									this.setState({
										searchForm: {
											...searchForm,
											waterMeterKindType: v
										}
									});
								}}
							>
								<Option value={null}>全部</Option>
								{WATER_METER_KIND.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>

					<Col span={8}>
						<FormItem label={'剩余水量区间:'}>
							<InputGroup compact>
								<Input
									style={{ width: '40%', textAlign: 'center' }}
									placeholder="起始水量"
									value={searchForm.remainFeeStart}
									onChange={(v) => {
										this.setState({
											searchForm: { ...searchForm, remainFeeStart: v.target.value }
										})
									}}
								/>
								<Input
									style={{
										width: '20%',
										borderLeft: 0,
										pointerEvents: 'none',
										backgroundColor: '#fff',
									}}
									placeholder="~"
									disabled
								/>
								<Input
									style={{ width: '40%', textAlign: 'center', borderLeft: 0 }}
									placeholder="结束水量"
									value={searchForm.remainFeeEnd}
									onChange={(v) => {
										this.setState({
											searchForm: { ...searchForm, remainFeeEnd: v.target.value }
										})
									}}
								/>
							</InputGroup>
						</FormItem>
					</Col>

				</Row>
				<Row>
					<Col span={16} offset={8} align="right">
						<FormItem>
							<Button type="primary" onClick={this.handleSearch}>
								搜索
							</Button>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<Button type="default" onClick={this.handleReset}>
								重置
							</Button>
							&nbsp;&nbsp;
							<Button className="searchBtn" type="default" onClick={() => this.readCard()}>
								读卡
							</Button>
						</FormItem>
					</Col>
				</Row>
			</Form>
		);
	};
	// 渲染操作按钮
	renderOperationButton = () => {
		return (
			<Col />
		);
	};

	getDetailList = () => {
		const { detail: { customerId, page, pageSize } } = this.state;
		this.props.getDetailList({ customerId, page, pageSize });
	}

	editRefundModal (visible) {
		this.setState({ visible: visible });
	}

	handleRefund () {
		this.props.form.validateFields(['type'], (err, values) => {
			if (!err) {
				const { selectedRowKeys, searchForm } = this.state;
				this.setState({ doLoading: true });
				if (selectedRowKeys.length > 0) {
					let param = { customerIdList: selectedRowKeys, type: values.type };
					this.props.sendMasSms(param, (visible) => this.editRefundModal(visible), this.getList, () => this.setState({ doLoading: false }));
				} else {
					searchForm.sendType = values.type
					this.props.sendMasSmsForIC(searchForm, (visible) => this.editRefundModal(visible), this.getList, () => this.setState({ doLoading: false }));
				}
			}
		});
	}
	_updateWheelPresentNumberModal () {
		const { form } = this.props;
		const { updateWheelPresentNumberVisible, doLoading, record } = this.state;

		const { getFieldDecorator } = form;
		return <Modal title="修改已购字轮读数" destroyOnClose={true} maskClosable={true} footer={null} visible={updateWheelPresentNumberVisible}
			onCancel={() => this.handleUpdateWheelPresentNumberCancel()}>
			{
				updateWheelPresentNumberVisible ? (
					<Form {...constants.formItemLayout}>
						<Row>
							<Col span={24}>
								<FormItem label="旧示数：">{record.wheelPresentNumber}</FormItem>
							</Col>
							<Col span={24}>
								<FormItem label={'新示数:'}>
									{getFieldDecorator('wheelPresentNumber', { rules: [{ required: true, message: '请输入新示数' }] })
										(<Input placeholder="请输入" />)}
								</FormItem>
							</Col>
						</Row>
						<Row>
							<Col span={24} align="center">
								<Fragment>
									<Button type="primary" className="btn" onClick={this.UpdateWheelPresentNumber}>
										提交
									</Button>
									<Button type="default" className="btn" onClick={this.handleUpdateWheelPresentNumberCancel}>
										取消
									</Button>
								</Fragment>
							</Col></Row>
					</Form>) : null}
		</Modal>;
	}

	_renderSMSModal () {
		const { form } = this.props;
		const { visible, doLoading } = this.state;
		const { getFieldDecorator } = form;
		const footer = (
			<Fragment>
				<Button key="submit" type="primary" onClick={() => this.handleRefund()} loading={doLoading}>确定</Button>
				<Button key="back" onClick={() => this.editRefundModal(false)} disabled={doLoading}>取消</Button>
			</Fragment>
		);
		return visible ? (
			<Modal title="短信模板" destroyOnClose={true} maskClosable={true} visible={visible}
				onCancel={() => this.editRefundModal(false)}
				footer={footer}>
				<Form {...constants.formItemLayout}>
					<Row>
						<Col>
							<FormItem label={'选择短信模板:'}>
								{getFieldDecorator('type', { rules: [{ required: true, message: '选择短信模板' }] })
									(<Select placeholder="请选择">
										<Option value={6}>欠费模板</Option>
									</Select>)
								}
							</FormItem>
						</Col>
					</Row>
				</Form>
			</Modal>) : null
	}

	// 渲染弹出框
	renderModal = () => {
		const { modalVisible, record, detail: { customerId, page, pageSize } } = this.state;
		const { detailList, detailTotal, form: { getFieldDecorator } } = this.props;
		const columns = [
			{
				title: '片区',
				dataIndex: 'areaName',
				align: 'center',
			},
			{
				title: '用户地址',
				dataIndex: 'address',
				align: 'center',
			},
			{
				title: '用户编号',
				dataIndex: 'cno',
				align: 'center',
			},
			{
				title: '用户名称',
				dataIndex: 'cname',
				align: 'center',
			},
			{
				title: '本次抄表日期',
				dataIndex: 'thisCopyDate',
				align: 'center',
			},
			{
				title: '本次抄表读数',
				dataIndex: 'thisNum',
				align: 'center',
			},
			{
				title: '期间用量',
				dataIndex: 'copiedAmount',
				align: 'center',
			},
			{
				title: '剩余水量',
				dataIndex: 'remainFee',
				align: 'center',
			},
			{
				title: '备注',
				dataIndex: 'remark',
				align: 'center',
			},
			{
				title: '操作',
				key: 'operation',
				width: 80,
				fixed: 'right',
				align: 'center',
				render: (text, record, index) => {
					return (
						<div style={{ display: 'flex', justify: 'space-between' }}>
							<Button title='删除' className='btn' type='primary' size='small'
								icon='delete'
								onClick={() => this.handleDel(record)} />
						</div>
					);
				},
			},
		];
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: detailTotal,
			showTotal: (total) => `共 ${total} 条数据 `,
			onChange: (page) => this.setState({ detail: { customerId, page, pageSize } }, this.getDetailList),
			onShowSizeChange: (current, pageSize) => this.setState({ detail: { customerId, page: 1, pageSize } }, this.getDetailList),
		};
		return (<Modal
			className="templateModal"
			title={"查表"}
			visible={modalVisible}
			onCancel={this.handleCancel}
			footer={null}
		>
			{modalVisible ? (
				<Form {...constants.formItemLayout}>
					<Row>
						<Col span={12}>
							<FormItem label="用户片区：">{record.areaName}</FormItem>
						</Col>
						<Col span={12}>
							<FormItem label="用户地址：">{record.address}</FormItem>
						</Col>
						<Col span={12}>
							<FormItem label="用户编号：">{record.cno}</FormItem>
						</Col>
						<Col span={12}>
							<FormItem label="用户名称：">{record.cname}</FormItem>
						</Col>
						<Col span={12}>
							<FormItem label={'本次抄表日期：'}>
								{getFieldDecorator('thisCopyDate', {
									initialValue: moment(),
									rules: [
										{
											required: true,
											message: '本次抄表日期必选'
										},
										{
											validator: (rule, value, callback) => {
												callback();
											}
										}
									]
								})(<DatePicker style={{ width: '100%' }} placeholder="请选择本次抄表日期" />)}
							</FormItem>
						</Col>
						<Col span={12}>
							<FormItem label="本次抄表读数：">
								{getFieldDecorator('thisNum', {
									rules: [{
										required: true,
										message: '本次抄表读数必填'
									}]
								})(<Input
									placeholder="请输入本次抄表读数"
									onChange={(e) => {
										if (detailList.length > 0) {
											this.props.form.setFieldsValue({
												'copiedAmount': e.target.value - detailList[0].thisNum,
												'remainFee': record.wheelPresentNumber ? record.wheelPresentNumber - e.target.value : 0 - e.target.value
											});
										} else {
											this.props.form.setFieldsValue({
												'remainFee': record.wheelPresentNumber ? record.wheelPresentNumber - e.target.value : 0 - e.target.value
											});
										}
									}}
								/>)}
							</FormItem>
						</Col>
						<Col span={12}>
							<FormItem label="用户已购水量：">
								<Input disabled value={record.wheelPresentNumber} />
							</FormItem>
						</Col>
						<Col span={12}>
							<FormItem label="期间用量：">
								{getFieldDecorator('copiedAmount')(<Input disabled placeholder="请输入期间用量" />)}
							</FormItem>
						</Col>
						<Col span={12}>
							<FormItem label="剩余水量：">
								{getFieldDecorator('remainFee', {
									required: true,
									message: '剩余水量必填'
								})(<Input disabled placeholder="请输入剩余水量" />)}
							</FormItem>
						</Col>
						<Col span={12}>
							<FormItem label="备注：">
								{getFieldDecorator('remark')(<Input placeholder="请输入备注" />)}
							</FormItem>
						</Col>
					</Row>
					<Row>
						<Col span={24} align="center">
							<Fragment>
								<Button type="primary" className="btn" onClick={this.handleSubmit}>
									提交
								</Button>
								<Button type="default" className="btn" onClick={this.handleCancel}>
									取消
								</Button>
							</Fragment>
						</Col>
					</Row>
					<br />
					<Row>
						<Table
							bordered
							columns={columns}
							rowKey={(record) => record.id}
							dataSource={detailList}
							pagination={paginationProps}
						/>
					</Row>
				</Form>) : null}
		</Modal>
		);
	};

	render () {
		const { page, pageSize, selectedRowKeys } = this.state;
		const { dataList, total } = this.props;
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: (total) => {
				return `共 ${total} 条数据 `;
			},
			onChange: (page) => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size),
		};
		const columns = [
			{
				title: '片区',
				dataIndex: 'areaName',
				align: 'center',
			},
			{
				title: '用户编号',
				dataIndex: 'cno',
				align: 'center',
			},
			{
				title: '用户名称',
				dataIndex: 'cname',
				align: 'center',
			},
			{
				title: '用户地址',
				dataIndex: 'address',
				align: 'center',
			},
			{
				title: '用户已购水量',
				dataIndex: 'wheelPresentNumber',
				align: 'center',
			},
			{
				title: '上次抄表日期',
				dataIndex: 'lastCopyDate',
				align: 'center',
			},
			{
				title: '上次抄表读数',
				dataIndex: 'lastNum',
				align: 'center',
			},
			{
				title: '本次抄表日期',
				dataIndex: 'thisCopyDate',
				align: 'center',
			},
			{
				title: '本次抄表读数',
				dataIndex: 'thisNum',
				align: 'center',
			},
			{
				title: '期间用量',
				dataIndex: 'copiedAmount',
				align: 'center',
			},
			{
				title: '剩余水量',
				dataIndex: 'remainAmount',
				align: 'center',
			},
			{
				title: '备注',
				dataIndex: 'remark',
				align: 'center',
			},
			{
				title: '手机号码',
				dataIndex: 'contactPhone',
				align: 'center',
			},
			{
				title: '操作',
				key: 'operation',
				width: 80,
				fixed: 'right',
				align: 'center',
				render: (text, record, index) => {
					return (
						<div style={{ display: 'flex', justify: 'space-between' }}>
							<Button
								title="查表"
								className="btn"
								type="primary"
								size="small"
								icon="form"
								onClick={() => this.handleAdd(record)}
							/>
							<Button
								title="修改已购字轮读数"
								className="btn"
								type="primary"
								size="small"
								icon="edit"
								onClick={() => this.handleUpdateWheelPresentNumber(record)}
							/>
						</div>
					);
				},
			},
		];
		const rowSelection = {
			fixed: true,
			selectedRowKeys,
			onChange: this.onSelectChange
		};
		return (
			<div className="shadow-radius template">
				<PageHeader title="卡表查表记录" />
				<Row>{this.renderSearchForm()}</Row>
				<Row>{this.renderOperationButton()}</Row>
				<Button type="primary" onClick={this.handleSend}>批量发送短信</Button>
				<Row className="main">
					<Table
						bordered
						columns={columns}
						rowSelection={rowSelection}
						rowKey={(record) => record.id}
						dataSource={dataList}
						pagination={paginationProps}
					/>
				</Row>
				{this.renderModal()}
				{this._renderSMSModal()}
				{this._updateWheelPresentNumberModal()}
			</div>
		);
	}
}

const mapStateToProps = (state) => {
	const data = state.get('checkRecord');
	return {
		dataList: data.dataList,
		total: data.total,
		detail: data.detail,
		areaSelect: data.areaSelect,
		detailList: data.detailList,
		detailTotal: data.detailTotal,
	};
};
const mapDispatchToProps = (dispatch) => ({
	setState: (data) => dispatch(actionCreators.setState(data)), // 设置redux值
	list: (data) => dispatch(actionCreators.list(data)), // 获取列表
	getDetailList: (data) => dispatch(actionCreators.detailList(data)), // 获取详情列表
	add: (data, list) => dispatch(actionCreators.add(data, list)), // 新增记录
	del: (data, list) => dispatch(actionCreators.del(data, list)), // 删除记录
	getAreaSelect: () => dispatch(actionCreators.getAreaSelect()), //获取片区选择框
	sendMasSms: (data, editRefundModal, getList, close) => dispatch(actionCreators.sendMasSms(data, editRefundModal, getList, close)),
	sendMasSmsForIC: (data, editRefundModal, getList, close) => dispatch(actionCreators.sendMasSmsForIC(data, editRefundModal, getList, close))
});
export default connect(mapStateToProps, mapDispatchToProps)(Form.create()(withRouter(Index)));
