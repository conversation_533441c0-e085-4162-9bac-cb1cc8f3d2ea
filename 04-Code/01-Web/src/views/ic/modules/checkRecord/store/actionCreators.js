import http from '$http';
import * as actionTypes from './constants';
import { message } from 'antd';
import api from './api';

// 数据回填
const payload = (type, data) => ({ type, data });

// 组件操作redux数据状态
const setState = data => ({ type: actionTypes.SET_STATE, data });

// 获取列表
const list = data => {
	return async dispatch => {
		const res = await http.post(api.listCustomer, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.LIST_RECORD, res.data));
		}
	};
};
const detailList = data => {
		return async dispatch => {
				const res = await http.post(api.list, data);
				if (res.code === 0) {
						dispatch(payload(actionTypes.DETAIL_LIST_RECORD, res.data));
				}
		};
};
// 新增记录
const add = (data, list) => {
	return async dispatch => {
		const res = await http.post(api.add, data);
		if (res.code === 0) {
			list();
			dispatch(payload(actionTypes.ADD_RECORD, res.data));
		}
	};
};
// 删除
const del = (data, list) => {
		return async dispatch => {
				const res = await http.del(api.del, data);
				if (res.code === 0) {
						list();
						message.success("删除成功！")
				}
		};
};
// 获取片区选择框
const getAreaSelect = () => {
		return async dispatch => {
				const res = await http.get(api.getAreaSelect);
				if (res.code === 0) {
						dispatch(payload(actionTypes.GET_AREA_SELECT, res.data));
				}
		};
};

const sendMasSms = (data, editRefundModal, getList,close) => {
	return async dispatch => {
		const res = await http.post(api.sendMasSms, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.SEND_MAS_SMS, res.data));
			editRefundModal(false);
			getList();
			close();
			message.success("发送成功！"+res.data)
		}
	};
};

const sendMasSmsForIC = (data, editRefundModal, getList,close) => {
	return async dispatch => {
		const res = await http.post(api.sendMasSmsForIC, data);
		if (res.code === 0) {
			editRefundModal(false);
			getList();
			close();
			message.success("发送成功！"+res.data)
		}
	};
};
export { setState, list, add, detailList, getAreaSelect, del,sendMasSms,sendMasSmsForIC }
