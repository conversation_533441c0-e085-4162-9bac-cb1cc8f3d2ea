import http from '$http';
import * as actionTypes from './constants';
import { message } from 'antd';
import api from './api';

// 数据回填
const payload = (type, data) => ({ type, data });

// 组件操作redux数据状态
const setState = data => ({ type: actionTypes.SET_STATE, data });

// 获取列表
const list = data => {
	return async dispatch => {
		const res = await http.post(api.list, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.LIST_RECORD, res.data));
		}
	};
};
// 获取片区选择框
const getAreaSelect = () => {
	return async dispatch => {
		const res = await http.get(api.getAreaSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_AREA_SELECT, res.data));
		}
	};
};
// 获取创建人选择框
const getCreateUidSelect = () => {
	return async dispatch => {
		const res = await http.get(api.getCreateUidSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_CREATE_UID_SELECT, res.data));
		}
	};
};
// 获取创建人选择框
const getCreateUidSelectHistory = () => {
	return async dispatch => {
		const res = await http.post(api.getCreateUidSelectHistory);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_CREATE_UID_SELECT_HISTORY, res.data));
		}
	};
};

// 获取创建人选择框
const getReceiveUidSelect = () => {
	return async dispatch => {
		const res = await http.post(api.getReceiveUidSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_RECEIVE_UID_SELECT, res.data));
		}
	};
};

// 获取订单部门选择框
const getCreateDepartmentSelect = () => {
	return async dispatch => {
		const res = await http.get(api.getCreateDepartmentSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_CREATE_DEPARTMENT_SELECT, res.data));
		}
	};
};
// 获取创建人选择框
const getRfcType = () => {
	return async dispatch => {
		const res = await http.get(api.getRfcType);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_RFC_TYPE, res.data));
		}
	};
};
// 删除
const del = (data, list) => {
	return async dispatch => {
		const res = await http.post(api.del, data);
		if (res.code === 0) {
			message.success('删除成功！');
			list();
		}
	};
};

// 删除
const save = (data,handleReset) => {
	return async dispatch => {
		const res = await http.post(api.save, data);
		if (res.code === 0) {
			message.success('添加成功！');
			handleReset();
		}
	};
};

// 获取创建人选择框
const getById = (data) => {
	return async dispatch => {
		const res = await http.restGet(api.getById, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.DETAIL_RECORD, res.data));
		}
	};
};
export { setState, list, getAreaSelect, getCreateUidSelect, del,save,getById,getReceiveUidSelect,getRfcType,getCreateDepartmentSelect,getCreateUidSelectHistory };
