import * as actionTypes from "./constants";
import { dataType } from '$utils'

const defaultState = {
	fields: null,
	dataList: [], // 列表数据
	areaSelect: [], // 片区选择框
	createUidSelect: [], // 创建人选择框
	createUidSelectHistory: [], // 创建人选择框
	receiveUidSelect: [],
	createDepartmentSelect: [], // 获取部门选择框
	rfcType: [],
	total: 0, // 总条数
	detail: null, // 详情
};

// 重要提示： 请做好各代码段的注释！！！
export default (state = defaultState, action) => {
	switch (action.type) {
		// 回填组件传给redux的值
		case actionTypes.SET_STATE:
			let _data = action.data;
			if (dataType(_data) === 'Object') {
				state[_data.key] = _data.value;
			}
			if (dataType(_data) === 'Array') {
				_data.forEach(item => {
					state[item.key] = item.value;
				})
			}
			return { ...state };
		// 回填列表数据
		case actionTypes.LIST_RECORD:
			state.dataList = action.data.rows;
			state.total = action.data.total;
			return { ...state };
		// 获取片区选择框
		case actionTypes.GET_AREA_SELECT:
			state.areaSelect = action.data;
			return { ...state };
		// 获取创建人选择框
		case actionTypes.GET_CREATE_UID_SELECT_HISTORY:
			state.createUidSelectHistory = action.data;
			return { ...state };
			// 获取创建人选择框
		case actionTypes.GET_CREATE_UID_SELECT:
			state.createUidSelect = action.data;
			return { ...state };
		case actionTypes.GET_RECEIVE_UID_SELECT:
			state.receiveUidSelect = action.data;
			return { ...state };
		case actionTypes.DETAIL_RECORD:
			state.detail = action.data;
			return { ...state };
		case actionTypes.GET_RFC_TYPE:
			state.rfcType = action.data;
			return { ...state };
		case actionTypes.GET_CREATE_DEPARTMENT_SELECT:
			state.createDepartmentSelect = action.data;
				return { ...state };
		default:
			return state;
	}
}
