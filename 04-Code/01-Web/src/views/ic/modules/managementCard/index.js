import React, { Component, Fragment } from 'react';
import { Button, Col, Descriptions, Form, Modal, Row, Select, message } from 'antd';
import { constants } from '$utils';
import './index.scss';
import Readusercardmodal from './components/readUserCardModal';
import ReadCheckCardModal from './components/readCheckCardModal';
import ReadXTusercardmodal from './components/readXTUserCardModal';
import ReadXTCheckCardModal from './components/readXTCheckCardModal';
import ReadTAusercardmodal from './components/readTAUserCardModal';
import {
	errCard,
	toolCard,
	checkCardStatus,
	readCard,
	sellConfirm,
	xtClearCard,
	xtClearCheckCard,
	xtClearBigCheckCard,
	qsEmptyCard,
	qsClearCard,
	qsOpenCard,
	qsWriteCard
} from '@/utils/cardUtil';

const FormItem = Form.Item;
const { Option } = Select;

class Index extends Component {

	constructor(props) {
		super(props);
		this.state = {
			cardType: [],
			visible: false,
			result: null
		};
	}

	//修正错误0或者00
	correction(number) {
		let result = errCard(number);
		if (result === 0) {
			if (number === 0) {
				alert('错误0修正成功！');
			} else {
				alert('错误00修正成功！');
			}
		}
	}

	//选择水表
	changeWaterType(value) {
		this.props.form.resetFields('cardType');
		let temp = [];
		if (value === '1') {
			temp = [
				{ label: '测零卡', value: 0 },
				{ label: '开关卡', value: 1 },
				{ label: '清除卡', value: 2 },
				{ label: '检查卡', value: 3 },
				{ label: '功能卡', value: 4 }
			];
		} else if (value === '2') {
			temp = [
				{ label: '清除卡', value: 15 },
				{ label: '开关卡', value: 16 },
				{ label: '显示卡', value: 17 },
				{ label: '检查卡', value: 18 },
				{ label: '功能卡', value: 19 },
				{ label: '补卡卡', value: 20 }
			];
		} else if (value === '3' || value === '4') {
			temp = [
				{ label: '清除卡', value: 5 },
				{ label: '开关卡', value: 6 },
				{ label: '检查卡', value: 7 },
				{ label: '校验卡', value: 8 }
			];
		} else {
			temp = [
				{ label: '测零卡', value: 9 },
				{ label: '开关卡', value: 10 },
				{ label: '清除卡', value: 11 },
				{ label: '显示卡', value: 12 },
				{ label: '检查卡', value: 13 },
				{ label: '功能卡', value: 14 }
			];
		}
		this.setState({ cardType: temp });
	}

	//制作
	cardMange() {
		this.props.form.validateFields((err, values) => {
			if (!err) {
				let result = toolCard(values.cardType, values.areaCode);
				if (result === 0) {
					message.success('工具卡制作成功');
				}
			}
		});
	}

	//读取卡状态
	readCardStats() {
		let result = checkCardStatus();
		if (result) {
			this.setState({ result: result, visible: true });
		}
	}

	_renderCheckCardModal() {
		const { result, visible } = this.state;
		return <Modal visible={visible} onCancel={() => this.setState({ visible: false })} footer={null}>
			<Descriptions bordered>
				<Descriptions.Item label="卡类型">{result}</Descriptions.Item>
			</Descriptions>
		</Modal>;
	}

	//卡清零
	async clearCard() {
		let cardInfo = readCard();
		let cardNo = cardInfo.substring(2, 10);
		let areaCode = cardInfo.substring(12, 18);
		//卡清零
		let result = await sellConfirm('预付费2', cardNo, 0, 0, 0, 0, 0, 0, 0, 0, areaCode);
		if (result == 0) {
			message.success('卡清零成功');
		}
	}

	//制作清空卡
	clearXTCard() {
		let result = xtClearCard();
		if (result === 5) {
			message.success('空卡制作成功');
		}
	}

	//制作检查卡
	checkXTCard() {
		let result = xtClearCheckCard();
		if (result === 1) {
			message.success('检查卡制作成功');
		}
	}

	//制作检查卡(大表)
	checkBigXTCard() {
		let result = xtClearBigCheckCard();
		if (result === 1) {
			message.success('检查卡制作成功');
		}
	}

	//制作清空卡
	clearTACard() {
		qsEmptyCard().then(res => {
			if (res.errcode == 0) {
				message.success('空卡制作成功');
			}
		})
	}

	//制作清除卡
	clearTAClearCard() {
		qsClearCard().then(res => {
			if (res.errcode == 0) {
				message.success('清除卡制作成功');
			}
		})
	}

	taTestCard() {
		qsEmptyCard().then(res => {
			if (res.errcode == 0) {
				qsOpenCard('18969').then(res1 => {
					if (res1.errcode === 0) {
						qsWriteCard('18969', 0, 1).then(
							res2 => {
								if (res2.errcode == 0) {
									message.error(res2.errmsg)
								} else {
									message.error(res2.errmsg)
								}
							}
						)
					} else {
						message.error(res1.errmsg);
					}
				});
			} else {
				message.error(res.errmsg);
			}
		});
	}


	render() {
		const { cardType } = this.state;
		const { getFieldDecorator } = this.props.form;
		return (
			<Fragment>
				<div className="shadow-radius-manageCard">
					<h1>错误卡修正</h1>
					<Row className='main-manageCard'>
						<Button type="primary" className='btn' onClick={() => this.correction(0)}>错误0修正</Button>

						<Button type="primary" onClick={() => this.correction(1)}>错误:00修正</Button>
					</Row>
					<span className="tip">提示：当水表显示错误0或者00时,点击按钮修正</span>
				</div>

				<div className="shadow-radius-manageCard">
					<h1>管理卡制作</h1>
					<Form {...constants.formItemLayout}>
						<Row>
							<Col span={8}>
								<FormItem label="水表种类：">
									{getFieldDecorator('waterMeterKind', {
										rules: [{ required: true, message: '请水表种类' }]
									})(
										<Select onChange={(value) => this.changeWaterType(value)}
											placeholder="请选择水表类型">
											<Option value="1">预付费2</Option>
											<Option value="3">预付费5</Option>
										</Select>
									)}
								</FormItem>
							</Col>
						</Row>
						<Row>
							<Col span={8}>
								<FormItem label="管理卡类型：">
									{getFieldDecorator('cardType', {
										rules: [{ required: true, message: '请填写管理卡类型' }]
									})(
										<Select placeholder="请先选择水表类型">
											{cardType.map((item, index) => {
												return (
													<Option key={index} value={item.value}>
														{item.label}
													</Option>
												);
											})}
										</Select>
									)}
								</FormItem>
							</Col>
						</Row>
						<div style={{ display: 'flex', width: 300, textAlign: 'center' }}>

							<Button type="primary"
								permission={React.$pmn('REVENUE:IC_CARD_MANAGEMENT:MANAGEMENT_CARD_MAKING:MAKE')} title='查看'
								onClick={() => this.cardMange()} >制作</Button>

						</div>
					</Form>
				</div>
				<div className="shadow-radius-manageCard">
					<h1>恒信卡</h1>
					<Row>
						<Col span={24}>
							<Readusercardmodal />
							<ReadCheckCardModal />
							<Button type="primary" style={{ width: 120, marginLeft: 20 }} className="button"
								onClick={() => this.readCardStats()}>
								读取卡状态
							</Button>
							<Button type="primary" style={{ marginLeft: 20 }} className="button"
								onClick={() => this.clearCard()}>
								蓝色钥匙卡清空
							</Button>
						</Col>
					</Row>
					<div style={{ marginTop: 10 }} className="tip"> 提示：阶梯和预付费圆扣检查卡卡读用户卡即可</div>
				</div>
				<div className="shadow-radius-manageCard">
					<h1>新天卡</h1>
					<Row>
						<Col span={24}>
							<ReadXTusercardmodal />
							<ReadXTCheckCardModal />
							<Button type="primary" style={{ marginLeft: 20 }} className="button"
								onClick={() => this.clearXTCard()}>
								新天卡清空
							</Button>
						</Col>
					</Row>
				</div>
				<div className="shadow-radius-manageCard">
					<h1>泰安卡</h1>
					<Row>
						<Col span={24}>
							<ReadTAusercardmodal />
							<Button type="primary" style={{ marginLeft: 20 }} className="button"
								onClick={() => this.clearTACard()}>
								泰安卡清空
							</Button>
							<Button type="primary" style={{ marginLeft: 20 }} className="button"
								onClick={() => this.clearTAClearCard()}>
								泰安清除卡
							</Button>
							{/* <Button type="primary" style={{ marginLeft: 20 }} className="button"
								onClick={() => this.taTestCard()}>
								泰安测试卡
							</Button> */}
						</Col>
					</Row>

				</div>
				{this._renderCheckCardModal()}
			</Fragment>
		);
	}
}


export default (Form.create())(Index);
