import React, { Component, Fragment } from 'react';
import { Modal, Button, Descriptions, message } from 'antd';
import { xtRead } from '@/utils/cardUtil';

class ReadXTCheckCardModal extends Component {

	constructor(props) {
		super(props);
		this.state = {
			visible: false,
			detail: {}
		};
	}

	//编辑弹窗
	editModal(visible) {
		this.setState({ visible: visible });
	}

	//读检查卡
	reardXTCheckCard() {
		let detail = {};
		let result = xtRead();
		result = result.split(',');
		if (result[1] === '04') {
			let flag = parseInt(result[9], 16) === 0 ? '已刷表' : '未刷表';    //刷表标志位
			detail.flag = flag;
			detail.cardNo = parseInt(result[2], 16);                           //卡号
			detail.total = parseInt(result[8], 16) / 100; //累计金额
			detail.this = parseInt(result[9], 16) / 100; //本次金额
			if (flag === '已刷表') {
				detail.blance = parseInt(result[11], 16) / 100; //剩余金额
			}
		} else {
			message.error('新天卡读卡失败：非用户卡');
		}
		console.log(result);
		this.editModal(true);
		this.setState({ detail: detail });
	}

	//渲染读用户卡
	renderDesciptions(detail) {
		return (
			<Descriptions bordered>
				<Descriptions.Item label="用户卡号">{detail.cardNo}</Descriptions.Item>
				<Descriptions.Item label="用户卡类型">阶梯57用户卡</Descriptions.Item>
				<Descriptions.Item label="刷表状态">{detail.flag}</Descriptions.Item>
				<Descriptions.Item label="总购买金额">{detail.total}元</Descriptions.Item>
				<Descriptions.Item label="本次购买金额">{detail.this}元</Descriptions.Item>
				{
					detail.blance?
						<Descriptions.Item label="表剩余金额">{detail.blance}元</Descriptions.Item> : ''
				}
			</Descriptions>
		);
	}

	render() {
		const { visible, detail } = this.state;
		return (
			<Fragment>
				<Button type="primary" style={{ marginLeft: 20 }} className="button"
								onClick={() => this.reardXTCheckCard()}>
					读取新天检查卡
				</Button>

				<Modal width={'60%'} visible={visible} title='用户卡详情' onCancel={() => this.editModal(false)} footer={null}>
					{this.renderDesciptions(detail)}
				</Modal>
			</Fragment>
		);
	}
}

export default ReadXTCheckCardModal;
