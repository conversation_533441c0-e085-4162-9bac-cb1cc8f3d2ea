import React, { Component, Fragment } from 'react';
import { Modal, Button, Descriptions, message } from 'antd';
import { xtRead } from '@/utils/cardUtil';

class ReadXTUserCardModal extends Component {

	constructor(props) {
		super(props);
		this.state = {
			visible: false,
			detail: {}
		};
	}

	//编辑弹窗
	editModal(visible) {
		this.setState({ visible: visible });
	}

	//读卡
	readUserCard() {
		let detail = {};
		let result = xtRead();
		console.log('result: ', result);
		result = result.split(',');
		if (result[1] === '0A') {
			let flag = parseInt(result[5], 16) === 0 ? '已刷表' : '未刷表';    //刷表标志位
			detail.flag = flag;
			detail.cardNo = parseInt(result[2], 16);                           //卡号
			detail.total = parseInt(result[6], 16); //累计
			detail.this = parseInt(result[5], 16); //本次
		} else {
			message.error('新天卡读卡失败：非用户卡');
		}
		this.editModal(true);
		this.setState({ detail: detail });
	}

	//渲染读用户卡
	renderDesciptions(detail) {
		return (
			<Descriptions bordered>
				<Descriptions.Item label="用户卡号">{detail.cardNo}</Descriptions.Item>
				<Descriptions.Item label="用户卡类型">新天用户卡</Descriptions.Item>
				<Descriptions.Item label="刷表状态">{detail.flag}</Descriptions.Item>
				<Descriptions.Item label="总购买">{detail.total}吨</Descriptions.Item>
				<Descriptions.Item label="本次购买">{detail.this}吨</Descriptions.Item>
			</Descriptions>
		);
	}

	render() {
		const { visible, detail } = this.state;
		return (
			<Fragment>
				<Button type="primary" style={{ marginLeft: 20 }} className="button"
								onClick={() => this.readUserCard()}>
					读取新天用户卡
				</Button>

				<Modal width={'60%'} visible={visible} title='用户卡详情' onCancel={() => this.editModal(false)} footer={null}>
					{this.renderDesciptions(detail)}
				</Modal>
			</Fragment>
		);
	}
}

export default ReadXTUserCardModal;
