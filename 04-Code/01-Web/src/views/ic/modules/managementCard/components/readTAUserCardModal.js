import React, { Component, Fragment } from 'react';
import { <PERSON><PERSON>, Button, Descriptions, message } from 'antd';
import { qsRead } from '@/utils/cardUtil';

class ReadTAUserCardModal extends Component {

	constructor(props) {
		super(props);
		this.state = {
			visible: false,
			detail: {}
		};
	}

	//编辑弹窗
	editModal(visible) {
		this.setState({ visible: visible });
	}

	//读卡
	async readUserCard() {
		let detail = {};
		let qsResult = await qsRead()
		console.log('qsResult: ', JSON.stringify(qsResult));
		if (qsResult.errcode == 0) {
			if (qsResult.cardtype < 0) {
				message.error('非用户卡！');
				return;
			} else {
				detail.cardNo = qsResult.usercode.toString().padStart(5, '0');
			}
		}
		if (qsResult.data.length > 0) {
			detail.buytimes = qsResult.data[0].buytimes; //次数
			if (qsResult.data[0].cardstatus == 0) {
				detail.flag = '未刷表';
				detail.this = qsResult.data[0].buynum / 10; //本次
			} else { 
				detail.flag = '已刷表';
				detail.this = qsResult.data[0].meternum / 10; //本次
			}
			
		}
		this.editModal(true);
		this.setState({ detail: detail });
	}

	//渲染读用户卡
	renderDesciptions(detail) {
		return (
			<Descriptions bordered>
				<Descriptions.Item label="用户卡号">{detail.cardNo}</Descriptions.Item>
				<Descriptions.Item label="用户卡类型">泰安用户卡</Descriptions.Item>
				<Descriptions.Item label="购买次数">{detail.buytimes}</Descriptions.Item>
				<Descriptions.Item label="刷表状态">{detail.flag}</Descriptions.Item>
				<Descriptions.Item label="本次购买">{detail.this}吨</Descriptions.Item>
			</Descriptions>
		);
	}

	render() {
		const { visible, detail } = this.state;
		return (
			<Fragment>
				<Button type="primary" style={{ marginLeft: 20 }} className="button"
								onClick={() => this.readUserCard()}>
					读取泰安用户卡
				</Button>

				<Modal width={'60%'} visible={visible} title='用户卡详情' onCancel={() => this.editModal(false)} footer={null}>
					{this.renderDesciptions(detail)}
				</Modal>
			</Fragment>
		);
	}
}

export default ReadTAUserCardModal;
