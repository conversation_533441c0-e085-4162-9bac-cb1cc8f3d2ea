import React, { Component, Fragment } from 'react';
import { Mo<PERSON>, Button, Descriptions, message } from 'antd';

class ReadHuaXuCheckCardModal extends Component {

	constructor(props) {
		super(props);
		this.state = {
			visible: false,
			detail: {}
		};
	}

	//编辑弹窗
	editModal(visible) {
		this.setState({ visible: visible });
	}

	//读检查卡
	readHuaXuCheckCard() {
		let detail = {};
		let resultHuaxu = (SZHXMETERCARD_Web.HXCD_4442_DisposeData_Web('1|1|', "")).split("|")
		console.log('resultHuaxu: ', resultHuaxu);
		if (resultHuaxu[1] == '0') {
			message.error('该检查卡还未使用请回去贴表');
		} else if (resultHuaxu[0] == '3') {
			// if (resultHuaxu[0] == '9') {
			// 	detail.cardNo = resultHuaxu[3].substring(6, resultHuaxu[3].length)
			// } else {
			// 	detail.cardNo = resultHuaxu[26].substring(6, resultHuaxu[26].length)
			// }
			detail.total = resultHuaxu[4]	//表累计量
			detail.date = resultHuaxu[7]	//购水日期
			detail.this = resultHuaxu[8] //水表余量
			detail.time = resultHuaxu[10]	//购水次数
			detail.totalBuyAmount = resultHuaxu[11]	//总购量
		} else {
			message.error('该卡非检查卡');
		}
		this.editModal(true);
		this.setState({ detail: detail });
	}

	//渲染读用户卡
	renderDesciptions(detail) {
		return (
			<Descriptions bordered>
				{/* <Descriptions.Item label="用户卡号">{detail.cardNo}</Descriptions.Item> */}
				<Descriptions.Item label="用户卡类型">华旭4442</Descriptions.Item>
				<Descriptions.Item label="总购量">{detail.totalBuyAmount}吨</Descriptions.Item>
				<Descriptions.Item label="表累计量">{detail.total}吨</Descriptions.Item>
				<Descriptions.Item label="水表余量">{detail.this}吨</Descriptions.Item>
				<Descriptions.Item label="购水日期">{detail.date}</Descriptions.Item>
			</Descriptions>
		);
	}

	render() {
		const { visible, detail } = this.state;
		return (
			<Fragment>
				<Button type="primary" style={{ marginLeft: 20 }} className="button"
								onClick={() => this.readHuaXuCheckCard()}>
					读取华旭检查卡
				</Button>

				<Modal width={'60%'} visible={visible} title='用户卡详情' onCancel={() => this.editModal(false)} footer={null}>
					{this.renderDesciptions(detail)}
				</Modal>
			</Fragment>
		);
	}
}

export default ReadHuaXuCheckCardModal;
