import React, { Component, Fragment } from 'react';
import { <PERSON><PERSON>, Button, Descriptions, message } from 'antd';
import { readJTCheckCard } from '@/utils/cardUtil';

class ReadCheckCardModal extends Component {

	constructor(props) {
		super(props);
		this.state = {
			visible: false,
			detail: {}
		};
	}

	//编辑弹窗
	editModal(visible) {
		this.setState({ visible: visible });
	}

	//读检查卡
	readCheckCard() {
		let result = readJTCheckCard();
		// let cardNo = parseInt(result.substring(0, 8), 16);
		let cardNo = result.substring(0, 8);
		let detail = {};
		detail.no = result.length;
		detail.cardNo = cardNo;
		if (cardNo !== 0) {
			if (result.length > 35) {
				//阶梯2
				detail.total = parseInt(result.substring(18, 24), 16) / 100;
				detail.water = parseInt(result.substring(24, 30), 16) / 100;
				detail.last = parseInt(result.substring(30, 36), 16) / 100;
			} else {
				//预付费2
				detail.number = parseInt(result.substring(16, 18), 16);
				detail.total = parseInt(result.substring(18, 22), 16);
				detail.water = parseInt(result.substring(22, 26), 16);
				detail.last = parseInt(result.substring(26, 30), 16);
			}
			this.editModal(true);
			this.setState({ detail: detail });
		} else {
			message.error('该检查卡还未贴表');
		}

	}

	//渲染检查卡
	renderDesciptions(detail) {
		return (
			<Descriptions bordered>
				<Descriptions.Item label="用户卡号">{detail.cardNo}</Descriptions.Item>
				{
					detail.no > 35 ? <Descriptions.Item label="用户卡类型">阶梯2检查卡</Descriptions.Item> :
						<Descriptions.Item label="用户卡类型">预付费2检查卡</Descriptions.Item>
				}

				{
					detail.number ?
						<Descriptions.Item label="表内次数">{detail.number}</Descriptions.Item> : ''
				}

				{
					detail.total ?
						<Descriptions.Item label="表内累计">{detail.total}</Descriptions.Item> : ''
				}

				{
					detail.water ?
						<Descriptions.Item label="表内本次">{detail.water}</Descriptions.Item> : ''
				}
				{
					detail.last ?
						<Descriptions.Item label="表内剩余">{detail.last}</Descriptions.Item> : ''
				}
			</Descriptions>
		);
	}

	render() {
		const { visible, detail } = this.state;
		return (
			<Fragment>
				<Button type="primary" style={{ width: 150, marginLeft: 20 }} onClick={() => this.readCheckCard()}>
					钥匙卡读取检查卡
				</Button>

				<Modal width={'60%'} visible={visible} title='检查详情' onCancel={() => this.editModal(false)} footer={null}>
					{this.renderDesciptions(detail)}
				</Modal>
			</Fragment>
		);
	}
}

export default ReadCheckCardModal;
