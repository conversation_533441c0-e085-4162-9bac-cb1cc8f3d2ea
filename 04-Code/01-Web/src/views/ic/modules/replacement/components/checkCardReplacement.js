import React, { Component, Fragment } from 'react';
import { Button, Col, Form, Input, message, Modal, Radio } from 'antd';
import { actionCreators } from '../store';
import { connect } from 'react-redux';
import { checkCardStatus, readYFFCheckCard, readJTCheckCard, copyToUser, replacementCard, sellConfirm } from '@/utils/cardUtil';

const areaNo =process.env.LOCAL_CODE;
const FormItem = Form.Item;

class CheckCardReplacement extends Component {

	constructor(props) {
		super(props);
		this.state = {
			data: null,
			checkCardVisible: false,
		};
	}

	//取消弹窗
	handleCancel = () => {
		this.setState({ checkCardVisible: false });
	};

	//检查卡补卡
	checkReissue() {
		let result = checkCardStatus();
		let data = {};
		if (result.indexOf('检查卡') > -1) {
			let resultYFF = readYFFCheckCard();
			let cardNo = resultYFF.split(',')[0]; //表内卡号
			let time = resultYFF.split(',')[1];//表内购水次数
			let flag = resultYFF.split(',')[2]; //标识位
			let code = resultYFF.split(',')[3]; //表内区域码
			data = { cardNo: cardNo, time: time, flag: flag, code: code };
			this.props.getLastOrderByCardNo(data, () => this.setState({ checkCardVisible: true }));
		}
		this.setState({ data: data });
	}

	//补卡
	handleSubmit = () => {
		const { order } = this.props;
		const { data } = this.state;
		this.props.form.validateFields((err, values) => {
			if (!err) {
				let newReissueAmount = 0;                 //本次
				if (values.useStatus === 0) {
					newReissueAmount = order.waterAmount;
				}
				let params = {
					customerId: order.customerId,
					reissueAmount: newReissueAmount,
					useStatus: values.useStatus,
					reissueFee: values.reissueFee,
					remark: values.remark
				}
					// let result = copyToUser(order.waterMeterKindType, order.cardNo, newReissueAmount, order.accumulationBuyCount, data.flag);
				let result = copyToUser(data.code, data.cardNo, newReissueAmount, order.accumulationBuyCount, data.flag);
				if (result === 0) {
					this.props.reissueSave(params, this.props.getList());
				}
			}
		});
	};

	render() {
		const { checkCardVisible, data } = this.state;
		const { form, order, loading } = this.props;
		const formItemLayout = {
			labelCol: { span: 6 },
			wrapperCol: { span: 15 }
		};
		const { getFieldDecorator } = form;
		return (
			<div align='right' style={{ marginBottom: 10,marginRight:10 }}>
				<Button type="primary" onClick={() => this.checkReissue()}>检查卡补卡</Button>
				<Modal visible={checkCardVisible} title="检查卡补卡" onCancel={this.handleCancel}
							 footer={(
								 <Fragment>
									 <Button key="submit" type="primary" loading={loading} onClick={this.handleSubmit}>补卡</Button>
									 <Button key="back" onClick={this.handleCancel}>取消</Button>
								 </Fragment>)} destroyOnClose={true}>
					<Form {...formItemLayout}>
						<Fragment>

							<Col>
								<FormItem label="用户卡号：">
									{getFieldDecorator('cardNo', { initialValue: order ? order.cardNo : '' })
									(<Input disabled/>)}
								</FormItem>
							</Col>

							<Col>
								<FormItem label="用户姓名：">
									{getFieldDecorator('name', { initialValue: order ? order.name : '' })
									(<Input disabled/>)}
								</FormItem>
							</Col>

							<Col>
								<FormItem label="用户地址：">
									{getFieldDecorator('cardNo', { initialValue: order ? order.address : '' })
									(<Input disabled/>)}
								</FormItem>
							</Col>

							<Col>
								<FormItem label="使用状态：">
									{getFieldDecorator('useStatus', {
										initialValue: (data && (Number(data.time)) > (order && Number(order.accumulationBuyCount))) ? 0 : 1
									})(
										<Radio.Group>
											<Radio value={0}>未使用</Radio>
											<Radio value={1}>已使用</Radio>
										</Radio.Group>
									)}
								</FormItem>
							</Col>

							<Col>
								<FormItem label="收费金额：">
									{getFieldDecorator('reissueFee', { rules: [{ required: true, message: '请输入收费金额' }] })
									(<Input/>)}
								</FormItem>
							</Col>

							<Col>
								<FormItem label="换卡原因：">
									{getFieldDecorator('remark')
									(<Input.TextArea/>)}
								</FormItem>
							</Col>

						</Fragment>
					</Form>
				</Modal>
			</div>

		);
	}
}

const mapStateToProps = state => {
	const data = state.get('replacement');
	return {
		checkCardVisible: data.checkCardVisible, // 是否显示弹窗
		order: data.order //订单数据
	};
};
const mapDispatchToProps = dispatch => ({
	setState: data => dispatch(actionCreators.setState(data)), // 设置redux值
	getLastOrderByCardNo: (data, visible) => dispatch(actionCreators.getLastOrderByCardNo(data, visible)),
	reissueSave: (data, list) => dispatch(actionCreators.reissueSave(data, list)),  //补卡
});
export default connect(
	mapStateToProps,
	mapDispatchToProps
)((Form.create())(CheckCardReplacement));

