import React, { Component, Fragment } from 'react';
import { actionCreators } from './store';
import { connect } from 'react-redux';
import { Button, Col, Form, Input, Modal, Row, Select, Table, TreeSelect, Radio, message } from 'antd';
import { constants } from '$utils';
import {
	replacementCard,
	newUser,
	sellConfirm,
	xtWriteOpen,
	xtClearCard,
	xtReplacementCard,
	huaxuWriteOpen,
	huaxuReplacementCard
} from '@/utils/cardUtil';
import CheckCardReplacement from './components/checkCardReplacement';
import SupplementaryCard from './components/supplementaryCard';
import './index.scss';
import { WATER_METER_KIND } from '@/constants/waterMeter';

const FormItem = Form.Item;
const { Option } = Select;
const { TreeNode } = TreeSelect;


const formItemLayout = {
	labelCol: { span: 6 },
	wrapperCol: { span: 15 }
};

const defaultSearchForm = {
	waterMeterType: 0,
	cno: '',
	cardNo: '',
	waterMeterNo: '',
	waterMeterKindType: null,
	areaId: '',
	address: '',
	name: null
};

class Index extends Component {

	constructor(props) {
		super(props);
		this.state = {
			page: 1,
			pageSize: 10,
			visible: false,
			replacementOpenVisible: false,
			searchForm: defaultSearchForm
		};
	}

	componentDidMount() {
		this.props.getAreaSelect();
	}

	// 获取列表
	getList = () => {
		const { page, pageSize, searchForm } = this.state;
		this.props.list(Object.assign({ ...searchForm }, { page, pageSize, statusList: [0, 1, 2] }));
	};

	// 列表分页
	handlePageChange = (page) => {
		this.setState({
			page
		}, () => {
			this.getList();
		});
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState({
			page: 1,
			pageSize: size
		}, () => {
			this.getList();
		});
	};

	// 重置搜索
	handleReset = () => {
		this.setState({ searchForm: Object.assign({}, defaultSearchForm) },
			() => {
				this.getList();
			}
		);
	};

	// 搜索
	handleSearch = () => {
		this.setState({ page: 1 }, () => {
			this.getList();
		});
	};

	//递归渲染
	_renderTreeNode = treeData => {
		return treeData.map(tree => {
			if (tree.children) {
				return (
					<TreeNode key={tree.id} title={tree.name} value={tree.id}>
						{this._renderTreeNode(tree.children)}
					</TreeNode>
				);
			} else {
				return <TreeNode key={tree.id} title={tree.name} value={tree.id} />;
			}
		});
	};

	// 渲染搜索
	_renderSearchForm = () => {
		const { searchForm } = this.state;
		const { areaSelect } = this.props;
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					<Col span={8}>
						<FormItem label={'用户编号:'}>
							<Input placeholder="请输入用户编号" value={searchForm.cno}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, cno: v.target.value } });
								}} />
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'用户卡号:'}>
							<Input placeholder="请输入用户卡号" value={searchForm.cardNo}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, cardNo: v.target.value } });
								}} />
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'水表编号:'}>
							<Input placeholder="请输入水表编号" value={searchForm.waterMeterNo}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, waterMeterNo: v.target.value } });
								}} />
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={8}>
						<FormItem label={'片区:'}>
							<TreeSelect style={{ width: '100%' }} dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
								placeholder="请选择片区" allowClear treeDefaultExpandedKeys={[100]} onChange={(v) => {
									this.setState({ searchForm: { ...searchForm, areaId: v } });
								}} value={searchForm.areaId}>
								{this._renderTreeNode(areaSelect)}
							</TreeSelect>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'水表种类:'}>
							<Select
								showSearch
								placeholder="请选择"
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, waterMeterKindType: v } });
								}} value={searchForm.waterMeterKindType}>
								{WATER_METER_KIND.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'用户地址:'}>
							<Input
								placeholder="请输入用户地址"
								value={searchForm.address}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, address: v.target.value } });
								}}
							/>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={8}>
						<FormItem label={'用户名称:'}>
							<Input placeholder="请输用户名称" value={searchForm.name}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, name: v.target.value } });
								}} />
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={24} align="right">
						<Button type="primary" onClick={this.handleSearch}>搜索</Button>
						&nbsp;&nbsp;&nbsp;&nbsp;
						<Button type="default" onClick={this.handleReset}>重置</Button>
						&nbsp;&nbsp;&nbsp;&nbsp;
					</Col>
				</Row>
			</Form>
		);
	};

	//打开补卡弹窗
	handleView = record => {
		this.props.getDetail(record, 1, () => this.setState({ visible: true }));
	};

	//打开补开户卡弹窗
	handleOpenView = record => {
		this.props.getDetail(record, 2, () => this.setState({ replacementOpenVisible: true }));
	};

	//取消弹窗
	handleCancel = () => {
		this.setState({ visible: false, replacementOpenVisible: false });
	};

	//渲染补卡弹窗
	_renderReplacementModal() {
		const { order, detail, form } = this.props;
		const { visible } = this.state;
		const { getFieldDecorator } = form;
		return (
			<Modal visible={visible} title="补卡" onCancel={this.handleCancel}
				footer={(<Fragment>
					<Button type="primary" onClick={this.handleSubmit}>补卡</Button>
					<Button key="back" onClick={this.handleCancel}>取消</Button>
				</Fragment>)} destroyOnClose={true}>
				<Form {...formItemLayout}>
					<Fragment>

						<Col>
							<FormItem label="用户卡号：">
								{getFieldDecorator('cardNo', { initialValue: detail ? detail.cardNo : '' })
									(<Input disabled />)}
							</FormItem>
						</Col>

						<Col>
							<FormItem label="用户姓名：">
								{getFieldDecorator('name', { initialValue: detail ? detail.name : '' })
									(<Input disabled />)}
							</FormItem>
						</Col>



						{
							detail && (detail.waterMeterKindType === '预付费2' || detail.waterMeterKindType === '阶梯2' || detail.waterMeterKindType === '预付费4442') ?
								<Col>
									<FormItem label="使用状态：">
										{getFieldDecorator('useStatus', {
											initialValue: 1,
											rules: [{ required: true, message: '请选择使用状态' }]
										})(
											<Radio.Group>
												<Radio value={0} disabled={order ? false : true}>未使用</Radio>
												<Radio value={1}>已使用</Radio>
											</Radio.Group>
										)}
									</FormItem>
								</Col> : void (0)
						}


						<Col>
							<FormItem label="收费金额：">
								{getFieldDecorator('reissueFee', { rules: [{ required: true, message: '请输入收费金额' }] })
									(<Input />)}
							</FormItem>
						</Col>

						<Col>
							<FormItem label="换卡原因：">
								{getFieldDecorator('remark')(<Input.TextArea />)}
							</FormItem>
						</Col>

					</Fragment>
				</Form>
			</Modal>
		);
	}

	//渲染补开户卡弹窗
	_renderReplacementOpenModal() {
		const { detail, form } = this.props;
		const { replacementOpenVisible } = this.state;
		const { getFieldDecorator } = form;
		return (
			<Modal visible={replacementOpenVisible} title="补开户卡" onCancel={this.handleCancel}
				footer={(<Fragment>
					<Button key="submit" type="primary"
						onClick={() => this.handleSubmitOpen()}>补开户卡</Button>
					<Button key="back" onClick={this.handleCancel}>取消</Button>
				</Fragment>)} destroyOnClose={true}>
				<Form {...formItemLayout}>
					<Fragment>

						<Col>
							<FormItem label="用户卡号：">
								{getFieldDecorator('cardNo', { initialValue: detail ? detail.cardNo : '' })
									(<Input disabled />)}
							</FormItem>
						</Col>

						<Col>
							<FormItem label="用户姓名：">
								{getFieldDecorator('name', { initialValue: detail ? detail.name : '' })
									(<Input disabled />)}
							</FormItem>
						</Col>

						<Col>
							<FormItem label="水表编号：">
								{getFieldDecorator('waterMeterNo', { initialValue: detail ? detail.waterMeterNo : '' })
									(<Input disabled />)}
							</FormItem>
						</Col>

					</Fragment>
				</Form>
			</Modal>
		);
	}

	//补卡
	handleSubmit = () => {
		const { order, detail, areaNo } = this.props;
		this.props.form.validateFields((err, values) => {
			if (!err) {
				let zjsj1 = 0;
				let zjsj2 = 0;
				let zjsj3 = 0;
				let sl1 = 0;
				let sl2 = 0;
				if (detail.waterMeterManufacturer === '扬州恒信') {
					if (detail.waterMeterKindType === '阶梯5') {
						let accumulationBuyCount = Number(detail.accumulationBuyCount) - 1; //次数
						let totale = detail.accumulationBuyAmount;
						if (detail.ladderType === '非阶梯') {
							zjsj1 = detail.price1;
							zjsj2 = detail.price1;
							zjsj3 = detail.price1;
							sl1 = 25;
							sl2 = 0;
						} else {
							zjsj1 = detail.price1 ? detail.price1 : 0;
							zjsj2 = detail.price2 ? detail.price2 : 0;
							zjsj3 = detail.price3 ? detail.price3 : 0;
							sl1 = detail.ladder1 ? detail.ladder1 : 0;
							sl2 = detail.ladder2 ? detail.ladder2 : 0;
						}
						let result = replacementCard(detail.waterMeterKindType, detail.cardNo, detail.waterMeterNo, detail.accumulationBuyCount, 0, totale, areaNo);
						let params = {
							customerId: detail.id,
							reissueAmount: detail.accumulationBuyAmount,
							useStatus: 1,
							reissueFee: values.reissueFee,
							remark: values.remark
						};
						if (result === 0) {
							let result1 = sellConfirm(detail.waterMeterKindType, detail.cardNo, accumulationBuyCount, 0, totale, zjsj1, zjsj2, zjsj3, sl1, sl2, areaNo);
							if (result1 === 0) {
								this.props.reissueSave(params, this.getList());
								this.setState({ visible: false });
							}
						}
					} else {
						let newReissueAmount = 0;                   //本次
						let newAllReissueAmount = 0;                //累计
						let newUseStatus = 0;
						if (detail.waterMeterKindType === '预付费2') {
							newReissueAmount = order ? order.waterAmount : 0;
							newAllReissueAmount = detail.accumulationAmount;
							newUseStatus = values.useStatus;
							if (newUseStatus === 1) {
								newReissueAmount = 0;
							}
						} else if (detail.waterMeterKindType === '预付费5') {
							newAllReissueAmount = detail.accumulationAmount;
							newUseStatus = 1;
						} else if (detail.waterMeterKindType === '阶梯2') {
							newReissueAmount = order ? order.writeCardAmount : 0;
							newAllReissueAmount = order.accumulationBuyAmount;
							newUseStatus = values.useStatus;
							if (newUseStatus === 1) {
								newReissueAmount = 0;
							}
						}
						let params = {
							customerId: detail.id,
							reissueAmount: newReissueAmount,
							useStatus: newUseStatus,
							reissueFee: values.reissueFee,
							remark: values.remark
						};
						let result = replacementCard(detail.waterMeterKindType, detail.cardNo, detail.waterMeterNo, detail.accumulationBuyCount, newReissueAmount, newAllReissueAmount, areaNo);
						if (result === 0) {
							this.props.reissueSave(params, this.getList());
							this.setState({ visible: false });
						}
					}
				} else if (detail.waterMeterManufacturer === '河南新天') {
					if (detail.ladderType === '非阶梯') {
						zjsj1 = detail.price1;
						zjsj2 = detail.price1;
						zjsj3 = detail.price1;
						sl1 = detail.ladder1;
						sl2 = detail.ladder1;
					} else {
						zjsj1 = detail.price1 ? detail.price1 : 0;
						zjsj2 = detail.price2 ? detail.price2 : 0;
						zjsj3 = detail.price3 ? detail.price3 : 0;
						sl1 = detail.ladder1 ? detail.ladder1 : 0;
						sl2 = detail.ladder2 ? detail.ladder2 : 0;
					}
					let xtClearCardResult = xtClearCard();
					if (xtClearCardResult === 5) {
						let result = xtReplacementCard('04', detail.cardNo, sl1, sl2, zjsj1, zjsj2, zjsj3, detail.accumulationBuyAmount, order.writeCardAmount);
						if (result === 1) {
							let params = {
								customerId: detail.id,
								reissueAmount: detail.accumulationBuyAmount,
								useStatus: 1,
								reissueFee: values.reissueFee,
								remark: values.remark
							};
							this.props.reissueSave(params, this.getList());
							this.setState({ visible: false });
						}
					} else {
						message.error('新天卡补卡失败：请重试');
					}
				} else if (detail.waterMeterManufacturer === '华旭') {
					if (detail.ladderType === '非阶梯') {
						zjsj1 = detail.price1;
						zjsj2 = detail.price1;
						zjsj3 = detail.price1;
						sl1 = detail.ladder1;
						sl2 = detail.ladder1;
					} else {
						zjsj1 = detail.price1 ? detail.price1 : 0;
						zjsj2 = detail.price2 ? detail.price2 : 0;
						zjsj3 = detail.price3 ? detail.price3 : 0;
						sl1 = detail.ladder1 ? detail.ladder1 : 0;
						sl2 = detail.ladder2 ? detail.ladder2 : 0;
					}
					let resultHuaxu = huaxuReplacementCard(detail.cardNo, detail.waterMeterNo, order.waterAmount, detail.accumulationBuyCount, values.useStatus)
					console.log('resultHuaxu: ', resultHuaxu);
					if (resultHuaxu[0] > 0) {
						let params = {
							customerId: detail.id,
							reissueAmount: detail.accumulationBuyAmount,
							useStatus: values.useStatus,
							reissueFee: values.reissueFee,
							remark: values.remark
						};
						this.props.reissueSave(params, this.getList());
						this.setState({ visible: false });
					} else {
						message.error('华旭卡补卡失败：请重试');
					}
				}
			}
		});
	};

	//补开户卡
	handleSubmitOpen() {
		const { detail, areaNo } = this.props;
		if (detail.waterMeterManufacturer === '扬州恒信') {
			let result = newUser(detail.cardNo, detail.waterMeterNo, detail.waterMeterKindType, areaNo);
			if (result === 0) {
				message.success('开户补卡成功！');
				this.setState({ replacementOpenVisible: false });
			}
		} else if (detail.waterMeterManufacturer === '河南新天') {
			let result = xtWriteOpen('04', detail.cardNo);
			if (result === 1) {
				message.success('开户补卡成功！');
				this.setState({ replacementOpenVisible: false });
			}
		} else if (detail.waterMeterManufacturer === '华旭') {
			let resultHuaxu = huaxuWriteOpen(detail.cardNo, detail.waterMeterNo);
			console.log('resultHuaxu: ', resultHuaxu);
			if (resultHuaxu[0] > 0) {
				message.success('开户补卡成功！');
				this.setState({ replacementOpenVisible: false });
			}
		}

	}

	render() {
		const { page, pageSize } = this.state;
		const { dataList, total } = this.props;
		const columns = [
			{
				title: '用户编号',
				dataIndex: 'cno',
				key: 'cno',
				align: 'center'
			},
			{
				title: '用户名称',
				dataIndex: 'name',
				key: 'name',
				align: 'center'
			},
			{
				title: '片区',
				dataIndex: 'areaName',
				key: 'areaName',
				align: 'center'
			},
			{
				title: '用户地址',
				dataIndex: 'address',
				key: 'address',
				align: 'center'
			},
			{
				title: '用户卡号',
				dataIndex: 'cardNo',
				key: 'cardNo',
				align: 'center'
			},
			{
				title: '水表编号',
				dataIndex: 'waterMeterNo',
				key: 'waterMeterNo',
				align: 'center'
			},
			{
				title: '水表种类',
				dataIndex: 'waterMeterKindType',
				key: 'waterMeterKindType',
				align: 'center'
			},
			{
				title: '操作',
				key: 'operation',
				align: 'center',
				render: (text, record, index) => {
					return (
						<span>
							<Button title='补卡' className='btn' type="primary" size="small" icon='profile'
								onClick={() => this.handleView(record)} />
							<Button title='补开户卡' className='btn' type="primary" size="small" icon='user'
								onClick={() => this.handleOpenView(record)} />
						</span>
					);
				}
			}
		];
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: (total) => {
				return `共 ${total} 条数据 `;
			},
			onChange: (page) => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		return (
			<div className="shadow-radius">
				<h1>补卡</h1>
				{this._renderSearchForm()}
				<Row className='main'>
					<div style={{ display: 'flex', justifyContent: 'flex-end' }}>

						<CheckCardReplacement />

						<SupplementaryCard getList={() => this.getList()} />

					</div>
					<Table bordered columns={columns} rowKey={() => Math.random()} dataSource={dataList}
						pagination={paginationProps} />
				</Row>
				{this._renderReplacementModal()}
				{this._renderReplacementOpenModal()}
			</div>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('replacement');
	return {
		fields: data.fields,
		dataList: data.dataList,    //列表数据
		detail: data.detail,        //获取详情
		areaSelect: data.areaSelect,//片区选择框
		total: data.total,          //总条数
		order: data.order,          //订单数据
		areaNo: data.areaNo         // 区域码
	};
};

const mapDispatchToProps = dispatch => ({
	setState: data => dispatch(actionCreators.setState(data)), // 设置redux值
	list: data => dispatch(actionCreators.list(data)), // 获取列表
	getDetail: (data, type, visible) => dispatch(actionCreators.getDetail(data, type, visible)), // 获取详情
	getAreaSelect: () => dispatch(actionCreators.getAreaSelect()), //获取片区选择框
	reissueSave: (data, list) => dispatch(actionCreators.reissueSave(data, list)),  //补卡
	getLastOrderByCardNo: data => dispatch(actionCreators.getLastOrderByCardNo(data))
});
export default connect(
	mapStateToProps,
	mapDispatchToProps
)((Form.create())(Index));
