import http from '$http';
import * as actionTypes from './constants';
import { message } from 'antd';
import api from './api';
import getLodop from '../../../../../utils/LodopFuncs';
import moment from 'moment';
import digitalUppercase from '../../../../../utils/digitalUppercase';

//数据回填
const payload = (type, data) => ({ type, data });
//组件操作redux数据状态
const setState = data => ({ type: actionTypes.SET_STATE, data });

//获取列表
const list = data => {
	return async dispatch => {
		const res = await http.post(api.list, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.LIST_RECORD, res.data));
		}
	};
};

//获取详情
const getDetail = (data, type, visible) => {
	return async dispatch => {
		const res = await http.post(api.getDetail, { customerId: data.id });
		if (res.code === 0) {
			dispatch(payload(actionTypes.DETAIL_RECORD, res.data));
			dispatch(getAreaNoById(data, type, visible));
		}
	};
};

//获取区域码
const getAreaNoById = (data, type, visible) => {
	return async dispatch => {
		const res = await http.restGet(api.getAreaNoById, data.id);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_AREA_NO_BY_ID, res.data));
			if (type === 1) {
				if (data.waterMeterKindType === '预付费2' || data.waterMeterKindType === '阶梯2' || data.waterMeterKindType === '预付费4442' || data.waterMeterKindType === '阶梯57' || data.waterMeterKindType === 'MF1') {
					dispatch(getLastOrder(data, visible));
				} else {
					visible();
				}
			} else {
				visible();
			}
		}
	};
};

//获取最后一笔订单(通过用户号)
const getLastOrder = (data, visible) => {
	return async dispatch => {
		const res = await http.restGet(api.getLastOrder, data.id);
		if (res.code === 0) {
			if (data.waterMeterManufacturer === '河南新天') {
				const res1 = await http.restGet(api.selectNoToCard, data.id);
				if (res1.code === 0) {
					if (res1.data) {
						message.error('该用户有已支付未刷卡的订单请先退掉该笔订单在进行补卡！');
					} else {
						dispatch(payload(actionTypes.GET_LAST_ORDER, res.data));
						visible();
					}
				}
			} else {
				dispatch(payload(actionTypes.GET_LAST_ORDER, res.data));
				visible();
			}
		}
	};
};

//获取最后一笔订单(通过卡号)
const getLastOrderByCardNo = (data, visible) => {
	return async dispatch => {
		const res = await http.restGet(api.getLastOrderByCardNo, data.cardNo);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_LAST_ORDER_BY_CARD_NO, res.data));
			visible();
		}
	};
};

// 获取片区选择框
const getAreaSelect = () => {
	return async dispatch => {
		const res = await http.get(api.getAreaSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_AREA_SELECT, res.data));
		}
	};
};

//补卡
const reissueSave = (data, list) => {
	return async dispatch => {
		const res = await http.post(api.reissueSave, data);
		if (res.code === 0) {
			if (data.invoice === 1) {
					const reissueTemplate = await http.restGet(api.getByType, 6)
					if (reissueTemplate.code === 0){
							const param = {createDate: moment(new Date()).format('YYYY年MM月DD日'),
									customerName: res.data.customerName,
									customerAddress: res.data.customerAddress,
									kind: res.data.waterMeterKind,
									quantity: '1',
									price: res.data.reissueFee,
									pay: res.data.reissueFee,
									amountSmall: res.data.reissueFee,
									amountBig: digitalUppercase(res.data.reissueFee),
									operator: res.data.createPersonName,}
							const template = Object.entries(param)
									.map(([k, v]) => ({key: k, value: v == null ? '' : v}))
									.reduce((total, {key, value}) => total.replace(key, value), reissueTemplate.data.content)
							let LODOP = getLodop();
							eval(template);
							let result = LODOP.PRINT();
					}
			}
			message.success('补卡成功！');
			list();
			dispatch(payload(actionTypes.REISSUE_SAVE, res.data));
		}
	};
};


export { setState, list, getDetail, getAreaSelect, reissueSave, getLastOrder, getLastOrderByCardNo, getAreaNoById };
