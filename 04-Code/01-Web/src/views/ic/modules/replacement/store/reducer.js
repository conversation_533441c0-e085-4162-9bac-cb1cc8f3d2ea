import * as actionTypes from './constants';
import { dataType } from '$utils';

const defaultState = {
	fields: null,
	total: 0, // 总条数
	dataList: [], // 列表数据
	detail: null, // 详情
	areaSelect: [], // 片区选择框
	order: null,  //订单数据
	areaNo:null,   //获取区域码
};

// 重要提示： 请做好各代码段的注释！！！
export default (state = defaultState, action) => {

	switch (action.type) {

		// 回填组件传给redux的值
		case actionTypes.SET_STATE:
			let _data = action.data;
			if (dataType(_data) === 'Object') {
				state[_data.key] = _data.value;
			}
			if (dataType(_data) === 'Array') {
				_data.forEach(item => {
					state[item.key] = item.value;
				});
			}
			return { ...state };

		// 回填列表数据
		case actionTypes.LIST_RECORD:
			state.dataList = action.data.rows;
			state.total = action.data.total;
			return { ...state };

		//获取区域码
		case actionTypes.GET_AREA_NO_BY_ID:
			state.areaNo = action.data;
			return { ...state };

		//获取详情
		case actionTypes.DETAIL_RECORD:
			state.detail = action.data;
			return { ...state };

		// 获取片区选择框
		case actionTypes.GET_AREA_SELECT:
			state.areaSelect = action.data;
			return { ...state };

		// 获取最后一笔有效订单
		case actionTypes.GET_LAST_ORDER:
			state.order = action.data;
			return { ...state };

		//提交补卡记录
		case actionTypes.REISSUE_SAVE:
			return { ...state };

		//检查卡补卡通过卡号获取最后一笔有效订单
		case actionTypes.GET_LAST_ORDER_BY_CARD_NO:
			state.order = action.data;
			return { ...state };
		default:
			return state;
	}
}
