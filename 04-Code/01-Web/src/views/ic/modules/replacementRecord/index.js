import React, { Component } from 'react';
import { actionCreators } from './store';
import { connect } from 'react-redux';
import http from '$http';
import { Table, Form, Select, Row, Col, Input, Button, DatePicker, TreeSelect, Icon, Modal, message } from 'antd';
import { constants } from '$utils';
import './index.scss';
import getLodop from '../../../../utils/LodopFuncs';
import moment from 'moment';
import digitalUppercase from '../../../../utils/digitalUppercase';

const FormItem = Form.Item;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TreeNode } = TreeSelect;
const InputGroup = Input.Group;
const { confirm } = Modal

const defaultSearchForm = {
	cno: '',
	cardNo: '',
	waterMeterNo: '',
	customerName: '',
	createUid: null,
	useStatus: null,
	areaId: null,
	createStartDate: '',
	createEndDate: '',
	reissueStartAmount: '',
	reissueEndAmount: '',
	reissueFee: null
};

class Index extends Component {

	constructor(props) {
		super(props);
		this.state = {
			expand: false, // 搜索项过多的时候，隐藏部分
			page: 1,
			pageSize: 10,
			searchForm: defaultSearchForm
		};
	}

	componentDidMount() {
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields && isTags) {
			this.setState({
				...fields.state
			}, () => {
				this.props.form.setFieldsValue({
					...fields.form
				})
			})
		} else {
			this.getList();
			this.props.getAreaSelect();
			this.props.getCreateUidSelect();
			this.props.getByType(6)
		}
	}

	componentWillUnmount() {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			})
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			})
		}
	}

	// 获取列表
	getList = () => {
		const { page, pageSize, searchForm } = this.state;
		this.props.list(Object.assign({ ...searchForm }, { page, pageSize }));
	};

	// 列表分页
	handlePageChange = (page) => {
		this.setState({ page }, () => {
			this.getList();
		});
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState({ page: 1, pageSize: size }, () => {
			this.getList();
		});
	};

	// 搜索功能
	handleSearch = () => {
		this.setState({ page: 1 }, () => {
			this.getList();
		});
	};

	// 重置搜索
	handleReset = () => {
		this.props.form.setFieldsValue({
			rangPicker: [undefined, undefined]
		});
		this.setState(
			{
				page: 1,
				searchForm: Object.assign({}, defaultSearchForm)
			},
			() => {
				this.getList();
			}
		);
	};

	//递归渲染
	_renderTreeNode = treeData => {
		return treeData.map(tree => {
			if (tree.children) {
				return (
					<TreeNode key={tree.id} title={tree.name} value={tree.id}>
						{this._renderTreeNode(tree.children)}
					</TreeNode>
				);
			} else {
				return <TreeNode key={tree.id} title={tree.name} value={tree.id} />;
			}
		});
	};

	// 时间查询条件
	getDate = (date, dateString) => {
		if (dateString.length === 2) {
			this.setState({
				searchForm: { ...this.state.searchForm, createStartDate: dateString[0], createEndDate: dateString[1] }
			});
		}
	};

	//导出
	export() {
		const { searchForm } = this.state;
		let url = `${process.env.API_ROOT}/api/cm/customer/card/reissue/exportCustomerCardReissue`;
		http.export(url, searchForm, (res) => {
			const blob = new Blob([res]);
			const downloadElement = document.createElement('a');
			const href = window.URL.createObjectURL(blob);
			const fileName = '补卡记录_' + new Date().getTime() + '.xlsx';
			downloadElement.href = href;
			downloadElement.download = fileName;
			document.body.appendChild(downloadElement);
			downloadElement.click();
			document.body.removeChild(downloadElement);
			window.URL.revokeObjectURL(href);
		});
	}

	// 渲染搜索
	_renderSearchForm = () => {
		const { searchForm } = this.state;
		const { areaSelect, createUidSelect } = this.props;
		const { getFieldDecorator } = this.props.form;
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					<Col span={8}>
						<FormItem label={'用户编号:'}>
							<Input placeholder="请输入用户编号" value={searchForm.cno}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, cno: v.target.value } });
								}} />
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'用户卡号:'}>
							<Input placeholder="请输入用户卡号" value={searchForm.cardNo}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, cardNo: v.target.value } });
								}} />
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'水表编号:'}>
							<Input placeholder="请输入水表编号" value={searchForm.waterMeterNo}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, waterMeterNo: v.target.value } });
								}}
							/>
						</FormItem>
					</Col>
				</Row>

				<Row>
					<Col span={8}>
						<FormItem label={'用户名称:'}>
							<Input placeholder="请输入用户名称" value={searchForm.customerName} onChange={v => {
								this.setState({ searchForm: { ...searchForm, customerName: v.target.value } });
							}} />
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'使用状态:'}>
							<Select placeholder="请选择使用状态"
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, useStatus: v } });
								}} value={searchForm.useStatus}>
								<Option value="null">全部</Option>
								<Option value="0">未使用</Option>
								<Option value="1">已使用</Option>
							</Select>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'操作员:'}>
							<Select
								placeholder="请选择"
								value={searchForm.createUid}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, createUid: v }
									});
								}}
							>
								{createUidSelect.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
				</Row>

				<Row>
					<Col span={8}>
						<FormItem label={'片区:'}>
							<TreeSelect style={{ width: '100%' }} dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
								placeholder="请选择片区" allowClear treeDefaultExpandedKeys={[100]} onChange={(v) => {
									this.setState({ searchForm: { ...searchForm, areaId: v } });
								}}>
								{this._renderTreeNode(areaSelect)}
							</TreeSelect>
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'补卡时间:'}>
							{getFieldDecorator('rangPicker', { rules: [{ type: 'array' }] })(<RangePicker onChange={this.getDate} placeholder={['开始时间', '结束时间']} />)}
						</FormItem>
					</Col>
					<Col span={8}>
						<FormItem label={'补发水量/金额:'}>
							<InputGroup>
								<Input
									className="noBorderRight"
									style={{ width: '40%' }}
									placeholder="开始水量/金额"
									onChange={v => {
										this.setState({
											searchForm: { ...searchForm, reissueStartAmount: v }
										});
									}}
								/>
								<Input className="midIpt noBorderLeft noBorderRight" style={{ width: '20%' }} placeholder="~" disabled />
								<Input
									className="noBorderLeft"
									style={{ width: '40%' }}
									placeholder="结束水量/金额"
									onChange={v => {
										this.setState({
											searchForm: { ...searchForm, reissueEndAmount: v }
										});
									}}
								/>
							</InputGroup>
						</FormItem>
					</Col>
				</Row>

				<Row>
					<Col span={8}>
						<FormItem label={'补卡金额:'}>
							<Input placeholder="请输入补卡金额" value={searchForm.reissueFee}
								onChange={v => {
									this.setState({ searchForm: { ...searchForm, reissueFee: v.target.value } });
								}} />
						</FormItem>
					</Col>
				</Row>

				<Row>
					<Col span={24} align="right">
						<Button type="primary" onClick={this.handleSearch}>
							搜索
						</Button>
						&nbsp;&nbsp;&nbsp;&nbsp;
						<Button type="default" onClick={this.handleReset}>
							重置
						</Button>
					</Col>
				</Row>
			</Form>
		);
	};

	// 删除记录
	handleDel = record => {
		const _this = this;
		confirm({
			title: '确认操作',
			content: '是否确认删除该补卡记录？',
			okText: '确定',
			okType: 'danger',
			cancelText: '取消',
			onOk() {
				_this.props.del(record.id, _this.getList);
			},
		});
	};
	// 打印小票
	handlePrint = record => {
		const { formwork } = this.props
		let LODOP = getLodop();
		let temp = formwork.content.replace('saleTime', moment(new Date()).format('YYYY年MM月DD日'))
			.replace('cname', record.customerName)
			.replace('amountSmall', record.reissueFee)
			.replace('amountBig', digitalUppercase(record.reissueFee))
			.replace('pay', record.reissueFee)
			.replace('price', record.reissueFee)
			.replace('waterMeterKind', record.waterMeterKind || "")
			.replace('customerAddress', record.customerAddress || "")
			.replace('quantity', '1')
			.replace('yyyy', moment(new Date()).format('YYYY'))
			.replace('MM', moment(new Date()).format('MM'))
			.replace('dd', moment(new Date()).format('DD'))
			.replace('operator', record.createPersonName);
		eval(temp);
		let result = LODOP.PREVIEW();
		if (result) {
			message.success('打印成功')
		}
	}
	render() {
		const { page, pageSize } = this.state;
		const { dataList, total } = this.props;
		const columns = [
			{
				title: '用户编号',
				dataIndex: 'cno',
				align: 'center'
			},
			{
				title: '水表编号',
				dataIndex: 'waterMeterNo',
				align: 'center'
			},
			{
				title: '用户卡号',
				dataIndex: 'cardNo',
				align: 'center'
			},
			{
				title: '用户名称',
				dataIndex: 'customerName',
				align: 'center'
			},
			{
				title: '水表种类',
				dataIndex: 'waterMeterKind',
				align: 'center'
			},
			{
				title: '补发水量/金额',
				dataIndex: 'reissueAmount',
				align: 'center'
			},
			{
				title: '使用状态',
				dataIndex: 'useStatus',
				align: 'center'
			},
			{
				title: '补卡费用',
				dataIndex: 'reissueFee',
				align: 'center'
			},
			{
				title: '付款方式',
				dataIndex: 'chargeWay',
				align: 'center'
			},
				{
						title: '转账日期',
						dataIndex: 'wireTransferNo',
						align: 'center'
				},
			{
				title: '补卡时间',
				dataIndex: 'createTime',
				align: 'center'
			},
			{
				title: '部门',
				dataIndex: 'departmentName',
				align: 'center'
			},
			{
				title: '操作人',
				dataIndex: 'createPersonName',
				align: 'center'
			},
			{
				title: '操作',
				key: 'operation',
				width: 120,
				fixed: 'right',
				align: 'center',
				render: (text, record, index) => {
					return (
						<span style={{ display: 'flex' }}>
							<Button title="删除" className="btn" type="primary" size="small" icon="delete" onClick={() => this.handleDel(record)} />
							<Button title='打印' className='btn' type="primary" size="small" icon='printer' onClick={() => this.handlePrint(record)} />
						</span>
					);
				}
			}
		];
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: (total) => {
				return `共 ${total} 条数据 `;
			},
			onChange: (page) => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		return (
			<div className="shadow-radius replacement-record">
				<h1>补卡记录</h1>
				<Row>
					{this._renderSearchForm()}
				</Row>
				<Row>
					<Button className="searchBtn" type="primary" onClick={() => this.export()}><Icon type="download" />导出补卡记录</Button>
				</Row>
				<Row className='main'>
					<Table
						scroll={{ x: 2000 }}
						bordered
						columns={columns}
						rowKey={() => Math.random()}
						dataSource={dataList}
						pagination={paginationProps}
					/>
				</Row>
			</div>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('replacementRecord');
	return {
		fields: data.fields,
		dataList: data.dataList, // 列表数据
		areaSelect: data.areaSelect, // 片区选择框
		createUidSelect: data.createUidSelect, // 创建人选择框
		total: data.total, // 总条数
		visible: data.visible, // 是否显示弹窗
		detail: data.detail, // 获取详情
		formwork: data.formwork, //发票模板
	};
};
const mapDispatchToProps = dispatch => ({
	setState: data => dispatch(actionCreators.setState(data)), // 设置redux值
	list: data => dispatch(actionCreators.list(data)), // 获取列表
	getAreaSelect: () => dispatch(actionCreators.getAreaSelect()), //获取片区选择框
	getCreateUidSelect: () => dispatch(actionCreators.getCreateUidSelect()), //获取订单创建人选择框
	del: (data, list) => dispatch(actionCreators.del(data, list)), // 删除记录
	getByType: (data) => dispatch(actionCreators.getByType(data)),    //获取发票模板
});
export default connect(
	mapStateToProps,
	mapDispatchToProps
)((Form.create())(Index));
