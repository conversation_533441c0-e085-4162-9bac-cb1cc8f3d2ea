import React, { Component, Fragment } from 'react';
import { Row, Col, Divider, Descriptions, Table, Tabs } from 'antd';
const { TabPane } = Tabs;

class Terminal extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      page: 1,
      pageSize: 10,
      tabKey: 1
    };
  }
  // 获取列表
  getList = () => {
    const { page, pageSize } = this.state
    this.props.listOrder({ page, pageSize })
  }
  // tab页切换
  callback = key => {
    this.setState({ tabKey: key }, () => {
      this.getList();
    });
  };
  // 列表分页
  handlePageChange = (page) => {
    this.setState(
      {
        page
      },
      () => {
        this.getList()
      }
    )
  }
  // 改变分页条数
  pageSizeChange = (current, size) => {
    this.setState(
      {
        page: 1,
        pageSize: size
      },
      () => {
        this.getList()
      }
    )
  }
  render() {
    const { page, pageSize } = this.state;
    const { detail, orderList, total } = this.props;
    const columns = [
      {
        title: '订单编号',
        dataIndex: 'orderNo',
        key: 'orderNo',
        align: 'center'
      },
      {
        title: '用户编号',
        dataIndex: 'cno',
        key: 'cno',
        align: 'center'
      },
      // {
      //   title: '水表编号',
      //   dataIndex: 'waterMeterNo',
      //   key: 'waterMeterNo',
      //   align: 'center'
      // },
      // {
      //   title: '水表种类',
      //   dataIndex: 'waterMeterKind',
      //   key: 'waterMeterKind',
      //   align: 'center'
      // },
      {
        title: '订单来源',
        dataIndex: 'orderSource',
        key: 'orderSource',
        align: 'center'
      },
      {
        title: '付款方式',
        dataIndex: 'chargeWay',
        key: 'chargeWay',
        align: 'center'
      },
      {
        title: '订单水量',
        dataIndex: 'waterAmount',
        key: 'waterAmount',
        align: 'center'
      },
      // {
      //   title: '用水分类',
      //   dataIndex: 'waterUseKindName',
      //   key: 'waterUseKindName',
      //   align: 'center'
      // },
      {
        title: '订单金额',
        dataIndex: 'orderAmount',
        key: 'orderAmount',
        align: 'center'
      },
      {
        title: '订单状态',
        dataIndex: 'status',
        key: 'status',
        align: 'center'
      },
      {
        title: '收费员',
        dataIndex: 'createPersonName',
        key: 'createPersonName',
        align: 'center'
      },
      // {
      //   title: '收费部门',
      //   dataIndex: 'departmentName',
      //   key: 'departmentName',
      //   align: 'center'
      // },
      {
        title: '缴费时间',
        dataIndex: 'createTime',
        key: 'createTime',
        align: 'center'
      },
      {
        title: '刷卡时间',
        dataIndex: 'writeCardTime',
        key: 'writeCardTime',
        align: 'center'
      },
    ];
    const paginationProps = {
      page,
      pageSize,
      showSizeChanger: true,
      showQuickJumper: true,
      pageSizeOptions: ['10', '20', '50', '100'],
      total: total,
      showTotal: total => {
        return `共 ${total} 条数据 `;
      },
      onChange: page => this.handlePageChange(page),
      onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
    };
    return (
      <Row className="main">
        <Tabs defaultActiveKey="1" onChange={this.callback}>
          <TabPane tab="基础信息" key="1">
            <Descriptions bordered>
              <Descriptions.Item label="设备ID：">{detail && detail.deviceId}</Descriptions.Item>
              <Descriptions.Item label="片区：">{detail && detail.areaName}</Descriptions.Item>
              <Descriptions.Item label="位置：">{detail && detail.location}</Descriptions.Item>
              <Descriptions.Item label="负责人：">{detail && detail.contact}</Descriptions.Item>
              <Descriptions.Item label="负责人电话：">{detail && detail.contactPhone}</Descriptions.Item>
              <Descriptions.Item label="设备状态：">{detail && detail.status}</Descriptions.Item>
              <Descriptions.Item label="订单数量：">{detail && detail.orderNum}</Descriptions.Item>
            </Descriptions>
          </TabPane>
          <TabPane tab="订单列表" key="2">
            <Table bordered columns={columns} rowKey={() => Math.random()} dataSource={orderList} pagination={paginationProps} />
          </TabPane>
        </Tabs>
      </Row>
    );
  }
}

export default Terminal;
