import * as actionTypes from './constants';
import { dataType } from '$utils';

const defaultState = {
	fields: null,
	total: 0,
	datalist: [],
	areaSelect: [], // 片区选择框
	waterUseKindSelect: [], // 用水性质选择框
};

export default (state = defaultState, action) => {
	switch (action.type) {

		// 回填组件传给redux的值
		case actionTypes.SET_STATE:
			let _data = action.data;
			if (dataType(_data) === 'Object') {
				state[_data.key] = _data.value;
			}
			if (dataType(_data) === 'Array') {
				_data.forEach(item => {
					state[item.key] = item.value;
				});
			}
			return { ...state };

		case actionTypes.LIST_RECORD:
			const data = action.data;
			// 赋值列表
			state.datalist = data.rows;
			// 赋值总条数
			state.total = data.total;
			// 更新state
			return { ...state };
		// 获取片区选择框
		case actionTypes.GET_AREA_SELECT:
			state.areaSelect = action.data;
			return { ...state };
		// 获取用水性质选择框
		case actionTypes.GET_WATER_USE_KIND_SELECT:
			state.waterUseKindSelect = action.data;
			return { ...state };
		default:
			return state;
	}
}
