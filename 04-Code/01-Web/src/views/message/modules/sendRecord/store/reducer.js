import * as actionTypes from './constants';
import { dataType } from '$utils'
const defaultState = {
	fields: null,
	total: 0,
	customers: [], // 客户列表
	datalist: [],
	customerList:[]
};

export default (state = defaultState, action) => {
	switch(action.type) {
		// 回填组件传给redux的值
		case actionTypes.SET_STATE:
			let _data = action.data;
			if (dataType(_data) === 'Object') {
				state[_data.key] = _data.value;
			}
			if (dataType(_data) === 'Array') {
				_data.forEach(item => {
					state[item.key] = item.value;
				})
			}
			return { ...state };

		case actionTypes.LIST_RECORD:
			const data = action.data;
			// 赋值列表
			state.datalist = data.rows;
			// 赋值总条数
			state.total = data.total;
			// 更新state
			return {...state};

		case actionTypes.GET_CUSTOMER_RECORD:
			// 赋值列表
			state.customerList = action.data;
			// 更新state
			return {...state};
		default:
			return state;
	}
}
