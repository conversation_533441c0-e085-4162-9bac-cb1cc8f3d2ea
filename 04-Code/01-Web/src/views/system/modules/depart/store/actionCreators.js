import http from '$http';
import * as actionTypes from './constants';
import api from './api';
import { message } from 'antd';

// 数据回填
const payload = (type, data) => ({type, data});

// 获取列表
const list = (data) => {
	return async dispatch => {
		const res = await http.get(api.list, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.LIST_RECORD, res.data))
		}
	}
};

// 新增
const add = data => {
	return async dispatch => {
		const res = await http.post(api.add, data);
		if (res.code === 0) {
			message.success('新增成功！');
			dispatch(setState({key: 'visible', value: false}));
			dispatch(list())

		} else {
			message.error(res.msg);
		}
	}
};

// 更新
const modify = data => {
	return async dispatch => {
		const res = await http.post(api.modify, data);
		if (res.code === 0) {
			message.success('修改成功');
			dispatch(payload(actionTypes.MODIFY_RECORD))
			dispatch(list());
		}
	}
};

// 删除
const del = (data, list) => {
	return async dispatch => {
		const res = await http.del(api.del, data);
		if (res.code === 0) {
			message.success('删除成功');
			list();
		}
	}
};

// 获取详情
const detail = data => {
	return async dispatch => {
		const res = await http.restGet(api.detail, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.DETAIL_RECORD, res.data))
		}
	}
};

// 设置state
const setState = data => {
	return dispatch => {
		dispatch(payload(actionTypes.SET_STATE, data))
	}
};


export { list, add, modify, del, detail, setState };
