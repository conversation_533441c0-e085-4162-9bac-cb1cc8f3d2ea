import React, { Component } from 'react';
import { connect } from 'react-redux';
import { actionCreators } from './store';
import { Modal, Button, Dropdown, Menu, Col, Form, Input, Row, Table, Icon } from 'antd';
import './index.scss'

const FormItem = Form.Item;
let loading = false;
class Index extends Component {
	constructor(props){
		super(props);
		this.state = {
			title: '新增模块',
			searchName: '', // 搜索内容
			parentId: null, // 父级id
			parentSN: '', // 父级SN
			columns: [
				{
					title: '模块名称',
					dataIndex: 'name',
					key: 'name',
					width: 200,
				},
				{
					title: '模块编码',
					dataIndex: 'sn',
					key: 'sn',
					width: 100,
					align: 'center',
				},
				{
					title: '操作',
					key: 'operation',
					width: 200,
					align: 'center',
					render: (text, record, index) => {
						return (
							<span>
								<Button permission={React.$pmn('REVENUE:SYSTEM_SETTINGS:MODULE_MANAGEMENT:EDIT')} className='btn' type="primary" size="small" icon="form" onClick={() => this.handleEdit(record)}>编辑</Button>
								<Dropdown overlay={
									<Menu onClick={e => this.handleMenuClick(e, record)}>
										<Menu.Item key="0" disabled={!record.parentId}>新增同级</Menu.Item>
										<Menu.Item key="1">新增子级</Menu.Item>
									</Menu>
								}>
									<Button permission={React.$pmn('REVENUE:SYSTEM_SETTINGS:MODULE_MANAGEMENT:ADD')} type="primary" size="small">
										新增 <Icon type="down" />
									</Button>
								</Dropdown>
							</span>
						)
					},
				},
			]
		};
	}

	componentDidMount() {
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields&&isTags) {
			this.setState({
				...fields.state
			}, () => {
				this.props.form.setFieldsValue({
					...fields.form
				})
			})
		} else {
			this.getList();
		}
	}

	componentWillUnmount() {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			})
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			})
		}
	}

	// 获取列表
	getList = () => {
		this.props.list();
	};

	handleMenuClick = (e, record) => {
		this.props.form.resetFields();
		loading = false;
		let sns = record.sn.split(':');
		this.setState({
			title: e.key === '0' ? '新增同级模块' : '新增子级模块',
			parentId: e.key === '0' ? record.parentId: record.id,
			parentSN:  e.key === '0' ? `${sns.splice(0,sns.length - 1).join(':')}:` : `${sns.join(':')}:`,
		}, () => this.props.setState({key: 'visible', value: true}))
	};

	// 编辑
	handleEdit = record => {
		this.props.form.resetFields();
		loading = false;
		this.setState({
			title: '编辑模块',
		}, () => {
			this.props.getDetail(record.id);
		})
	};

	// 取消弹窗
	handleCancel = () => {
		loading = false;
		this.props.setState([{key: 'visible', value: false},{key: 'detail', value: null}]);
	};

	// 新增功能权限列表
	addPermissionList = () => {
		const { form } = this.props;
		// can use data-binding to get
		let keys = form.getFieldValue('keys') || [];
		let ids = keys.length;
		const nextKeys = keys.concat(ids);
		// can use data-binding to set
		// important! notify form to detect changes
		form.setFieldsValue({
			keys: nextKeys,
		});
	};

	remove = k => {
		const { form } = this.props;
		// can use data-binding to get
		const keys = form.getFieldValue('keys');
		// We need at least one passenger
		// if (keys.length === 1) {
		// 	return;
		// }
		// can use data-binding to set
		form.setFieldsValue({
			keys: keys.filter(key => key !== k),
		});

	};

	_renderFormItems = () => {
		const { form, detail } = this.props;
		const { getFieldDecorator, getFieldValue } = form;
		let list = [];
		const formSubLayout = {
			labelCol: { span: 7 },
			wrapperCol: { span: 15 }
		};
		// 如果是渲染详情，将第一条数据删除（第一条数据为 当前子权限的父权限）
		if (detail) {
			list = detail.permissionList;
			list = list.slice(0);
			list.shift();
		}
		getFieldDecorator('keys', { initialValue: list });
		const keys = getFieldValue('keys');
		return keys.map((k, index) => {
			return (
				<Row>
					<Col span={11} key={k}>
						<FormItem label="功能：" {...formSubLayout}>
							{getFieldDecorator(`remark${k}`, {
								initialValue: k ? k.name : '',
								rules: [
									{
										required: true,
										message: '功能名称必填'
									}
								]
							})(<Input placeholder="请输入功能名称" />)}
						</FormItem>
					</Col>
					<Col span={11}>
						<FormItem label="权限码：" {...formSubLayout}>
							{getFieldDecorator(`sns${k}`, {
								initialValue: k ? k.sn : ``,
								rules: [
									{
										required: true,
										message: '模块编码必填'
									}
								]
							})(<Input placeholder="请输入模块编码" />)}
						</FormItem>
					</Col>
					<Col span={2}>
						<Icon
							className="dynamic-delete-button"
							type="minus-circle-o"
							onClick={() => this.remove(k)}
						/>
					</Col>
				</Row>
			)
		});
	};

	handleSubmit = e => {
		e.preventDefault();
		loading = true;
		this.props.form.validateFields((err, values) => {
			if (!err) {
				let permissionList = values.keys ? values.keys.map((item, index) => {
					if (typeof values.keys[index] === 'object') {
						return values.keys[index];
					} else {
						let _obj = {};
						let csn = `${values[`sns${index}`]}`;
						let psn = `${values.sn}`;
						let str = csn.split(':').pop();
						let sn =`${psn}:${str}`;
						_obj['name'] = values[`remark${index}`];
						_obj['sn'] = sn;
						return _obj;
					}
				}) : [];
				if (this.state.title === '编辑模块') {
					const { detail } = this.props;
					let params = {
						id: detail.id,
						name:  values.name,
						parentId: detail.parentId,
						sn: values.sn,
						permissionList: [
							{
								name: values.name,
								sn: values.sn
							},
							...permissionList
						],
					};
					this.props.modify(params);
				} else {
					let params = {
						name:  values.name,
						parentId: this.state.parentId,
						sn: values.sn,
						permissionList: [
							{
								name: values.name,
								sn: values.sn
							},
							...permissionList
						],
					};
					this.props.add(params);
				}
				loading = false;
			}
		});
	};

	render() {
		const { title, parentSN } = this.state;
		const { form, detail, visible } = this.props;
		const { getFieldDecorator } = form;
		const formItemLayout = {
			labelCol: { span: 4 },
			wrapperCol: { span: 19 }
		};
		const formItemLayoutWithOutLabel = {
			wrapperCol: {
				xs: { span: 24, offset: 0 },
				sm: { span: 24, offset: 0 },
			},
		};

		return (
			<div className="shadow-radius modules">
				{/* 页面标题 */}
				<Row>
					<Col><h1>模块管理</h1></Col>
				</Row>
				<div >
					<Table
						rowKey={record => record.id}
						columns={this.state.columns}
						dataSource={this.props.datalist}
						scroll={{ x: 1000 }}
						pagination={{
							disabled: true,
							hideOnSinglePage: true,
						}}
					/>
				</div>
				<Modal className="modulesModal" title={title} visible={visible} onCancel={this.handleCancel} footer={null}>
					<Form {...formItemLayout}>
						<FormItem label="模块名称">
							{getFieldDecorator('name', {
								initialValue: detail ? detail.name : '',
								rules: [
									{
										required: true,
										message: '模块名称必填'
									}
								]
							})(<Input />)}
						</FormItem>
						<FormItem label="模块编码">
							{getFieldDecorator('sn', {
								initialValue: `${detail ? detail.sn : parentSN.split(':').splice(0,parentSN.split(':').length - 1).join(':')+':'}`,
								rules: [
									{
										required: true,
										message: '模块编码必填'
									}
								]
							})(<Input placeholder={`${detail ? detail.sn.split(':').splice(0,detail.sn.split(':').length - 1).join(':') : parentSN.split(':').splice(0,parentSN.split(':').length - 1).join(':')}:模块编码`} />)}
						</FormItem>
						{this._renderFormItems()}
						<Row>
							<Col align="center">
								<FormItem {...formItemLayoutWithOutLabel}>
									<Button type="dashed" onClick={this.addPermissionList} style={{ width: '60%' }}>
										<Icon type="plus" /> 新增功能权限
									</Button>
								</FormItem>
							</Col>
						</Row>
						<Row>
							<Col align="center">
								<Button type="primary" onClick={this.handleSubmit} loading={loading}>
									提交
								</Button>
								&nbsp;&nbsp;&nbsp;&nbsp;
								<Button type="default" onClick={this.handleCancel}>
									取消
								</Button>
							</Col>
						</Row>
					</Form>
				</Modal>
			</div>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('modules');
	return {
		fields: data.fields,
		datalist: data.datalist, // 数据列表
		detail: data.detail, // 片区详情
		visible: data.visible, // 是否显示弹窗
	}
};
const mapDispatchToProps = dispatch => ({
	list: data => {
		dispatch(actionCreators.list(data))
	},
	add: data => {
		dispatch(actionCreators.add(data))
	},
	modify: data => {
		dispatch(actionCreators.modify(data))
	},
	getDetail: data => {
		dispatch(actionCreators.detail(data))
	},
	setState: data => {
		dispatch(actionCreators.setState(data))
	}
});
export default connect(
	mapStateToProps,
	mapDispatchToProps
)((Form.create())(Index));
