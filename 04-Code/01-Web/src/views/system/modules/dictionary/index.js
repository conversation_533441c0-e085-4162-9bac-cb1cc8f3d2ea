import React, { Component, Fragment } from 'react';
import { actionCreators } from './store';
import { connect } from 'react-redux';
import { Modal, Table, Form, Select, Row, Col, Input, Button, DatePicker } from 'antd';
import { constants } from '$utils';
import './index.scss';

const { confirm } = Modal;
const FormItem = Form.Item;
const { Option } = Select;
const ButtonGroup = Button.Group;
const { RangePicker } = DatePicker;
const { TextArea } = Input;

const defaultSearchForm = {};

class Index extends Component {
	constructor(props) {
		super(props);
		this.state = {
			page: 1,
			pageSize: 10,
			searchForm: defaultSearchForm,
			columns: [
				{
					title: '字典类型',
					dataIndex: 'type',
					align: 'center'
				},
				{
					title: '字典键值',
					dataIndex: 'value',
					align: 'center'
				},
				{
					title: '所属类型',
					dataIndex: 'className',
					align: 'center'
				},
				{
					title: '上级字典',
					dataIndex: 'parentName',
					align: 'center'
				},
				{
					title: '操作',
					key: 'operation',
					width: 200,
					align: 'center',
					render: (text, record, index) => {
						return (
							<span>
								{/* <Button title="查看" className="btn" type="primary" size="small" icon="eye" onClick={() => this.handleView(record)} /> */}
								<Button title="编辑" className="btn" type="primary" size="small" icon="form" onClick={() => this.handleEdit(record)} />
								<Button title="删除" className="btn" type="danger" size="small" icon="delete" onClick={() => this.handleDel(record)} />
							</span>
						);
					}
				}
			],
			addType: '',
			modalType: 'view',
			modalTitle: ''
		};
	}

	componentDidMount() {
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields&&isTags) {
			this.setState({
				...fields.state
			}, () => {
				this.props.form.setFieldsValue({
					...fields.form
				})
			})
		} else {
			this.getList();
		}
	}

	componentWillUnmount() {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			})
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			})
		}
	}

	// 获取列表
	getList = () => {
		const { page, pageSize, searchForm } = this.state;
		this.props.list(Object.assign({ ...searchForm }, { page, pageSize }));
	};

	// 列表分页
	handlePageChange = page => {
		this.setState(
			{
				page
			},
			() => {
				this.getList();
			}
		);
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState(
			{
				page: 1,
				pageSize: size
			},
			() => {
				this.getList();
			}
		);
	};

	// 搜索功能
	handleSearch = () => {
		this.setState(
			{
				page: 1
			},
			() => {
				this.getList();
			}
		);
	};

	// 重置搜索
	handleReset = () => {
		this.props.form.setFieldsValue({
			rangPicker: [undefined, undefined]
		});
		this.setState(
			{
				searchForm: Object.assign({}, defaultSearchForm)
			},
			() => {
				this.getList();
			}
		);
	};

	// 新增子类
	handleAddClass = () => {
		this.setState(
			{
				addType: 'class',
				modalType: 'add',
				modalTitle: '添加字典子类'
			},
			() => {
				this.props.setState([{ key: 'visible', value: true }, { key: 'detail', value: null }]);
				this.props.getClassSelect();
			}
		);
	};

	// 新增值
	handleAddValue = () => {
		this.setState(
			{
				addType: 'value',
				modalType: 'add',
				modalTitle: '添加字典值'
			},
			() => {
				this.props.setState([{ key: 'visible', value: true }, { key: 'detail', value: null }]);
				this.props.getClassSelect();
			}
		);
	};

	// 编辑记录
	handleEdit = record => {
		this.setState(
			{
				modalType: 'edit',
				modalTitle: '编辑'
			},
			() => {
				// 设置redux visible 为true 显示弹窗
				this.props.setState([{ key: 'modifyVisible', value: true }, { key: 'detail', value: null }]);
				// 获取详情
				this.props.getDetail(record.id);
			}
		);
	};

	// 取消弹窗
	handleCancel = () => {
		this.props.setState([{ key: 'visible', value: false }, { key: 'modifyVisible', value: false }, { key: 'detail', value: null }]);
		this.props.form.resetFields();
	};

	// 提交信息
	handleSubmit = e => {
		const { addType } = this.state;
		e.preventDefault();
		this.props.form.validateFields((err, values) => {
			if (!err) {
				let params = {};
				if (addType === 'class') {
					params = Object.assign({ ...values });
				} else {
					let classId = !!!values.classId ? values.parentId : values.classId;
					params = {
						classId: classId,
						value: values.value
					};
				}
				this.props.add(params, this.getList);
			}
		});
		this.props.form.resetFields();
	};

	// 修改记录
	handleModify = e => {
		e.preventDefault();
		let params = {
			id: this.props.detail.id,
			value: this.props.form.getFieldValue('value')
		};
		this.props.modify(params, this.getList);
		this.handleCancel()
	};

	// 删除记录
	handleDel = record => {
		const _this = this;
		confirm({
			title: '确认操作',
			content: '是否确认删除该条记录？',
			okText: '确定',
			okType: 'danger',
			cancelText: '取消',
			onOk() {
				_this.props.del(record.id, _this.getList);
			},
			onCancel() {
				console.log('Cancel');
			}
		});
	};

	// 渲染操作按钮
	_renderOperationButton = () => {
		return (
			<Col>
				<Button type="primary" className="btn" onClick={this.handleAddClass}>
					新增字典子类
				</Button>
				<Button type="primary" className="btn" onClick={this.handleAddValue}>
					新增字典值
				</Button>
			</Col>
		);
	};

	// 父节点选中
	parentOnChange = value => {
		this.props.getClassSelectByParent(value);
	};

	// 渲染新增弹出框
	_renderModal = () => {
		const { addType, modalTitle, modalType } = this.state;
		const { detail, visible, classSelect, subClassSelect } = this.props;
		const { getFieldDecorator } = this.props.form;
		// 子类列
		const _renderClassRow = () => {
			return (
				<Row>
					<Col span={12}>
						<FormItem label="父节点：">
							{getFieldDecorator('parentId', {
								initialValue: null,
								rules: [
									{
										required: true,
										message: '请选择父节点'
									}
								]
							})(
								<Select palceholder="请选择父节点">
									{classSelect.map((item, index) => {
										return (
											<Option key={index} value={item.value}>
												{item.label}
											</Option>
										);
									})}
								</Select>
							)}
						</FormItem>
					</Col>
					<Col span={12}>
						<FormItem label="子类值：">
							{getFieldDecorator('value', {
								initialValue: '',
								rules: [
									{
										required: true,
										message: '请输入子类值'
									}
								]
							})(<Input palceholder="请输入子类值" />)}
						</FormItem>
					</Col>
				</Row>
			);
		};

		// 字典值列
		const _renderValueRow = () => {
			return (
				<Fragment>
					<Row>
						<Col span={24}>
							<FormItem label="字典类：">
								{getFieldDecorator('parentId', {
									initialValue: null,
									rules: [
										{
											required: true,
											message: '请选择字典类'
										}
									]
								})(
									<Select palceholder="请选择字典类" onChange={this.parentOnChange}>
										{classSelect.map((item, index) => {
											return (
												<Option key={index} value={item.value}>
													{item.label}
												</Option>
											);
										})}
									</Select>
								)}
							</FormItem>
						</Col>
					</Row>
					{subClassSelect.length > 0 ? (
						<Row>
							<Col span={24}>
								<FormItem label="字典子类：">
									{getFieldDecorator('classId', {
										initialValue: null,
										rules: [
											{
												required: true,
												message: '请选择字典子类'
											}
										]
									})(
										<Select palceholder="请选择字典子类">
											{subClassSelect.map((item, index) => {
												return (
													<Option key={index} value={item.value}>
														{item.label}
													</Option>
												);
											})}
										</Select>
									)}
								</FormItem>
							</Col>
						</Row>
					) : null}
					<Row>
						<Col span={24}>
							<FormItem label="字典值：">
								{getFieldDecorator('value', {
									initialValue: '',
									rules: [
										{
											required: true,
											message: '请输入字典值'
										}
									]
								})(<Input palceholder="请输入字典值" />)}
							</FormItem>
						</Col>
					</Row>
				</Fragment>
			);
		};

		return (
			<Modal className="dictionary-modal" title={modalTitle} visible={visible} onCancel={this.handleCancel} footer={null}>
				<Form {...constants.formItemLayout}>
					{addType === 'class' ? _renderClassRow() : _renderValueRow()}
					<Row>
						<Col span={24} align="center">
							{
								<Fragment>
									<Button type="primary" className="btn" onClick={this.handleSubmit}>
										提交
									</Button>
									<Button type="default" className="btn" onClick={this.handleCancel}>
										取消
									</Button>
								</Fragment>
							}
						</Col>
					</Row>
				</Form>
			</Modal>
		);
	};

	// 渲染修改弹出框
	_renderModifyModal = () => {
		const { detail, modifyVisible } = this.props;
		const { getFieldDecorator } = this.props.form;
		return (
			<Modal className="dictionary-modal" title="修改" visible={modifyVisible} onCancel={this.handleCancel} footer={null}>
				<Form {...constants.formItemLayout}>
					<Row>
						<Col span={24}>
							<FormItem label="字典值：">
								{getFieldDecorator('value', {
									initialValue: detail ? detail.value : '',
									rules: [
										{
											required: true,
											message: '请输入字典值'
										}
									]
								})(<Input palceholder="请输入字典值" />)}
							</FormItem>
						</Col>
					</Row>
					<Row>
						<Col span={24} align="center">
							{
								<Fragment>
									<Button type="primary" className="btn" onClick={this.handleModify}>
										提交
									</Button>
									<Button type="default" className="btn" onClick={this.handleCancel}>
										取消
									</Button>
								</Fragment>
							}
						</Col>
					</Row>
				</Form>
			</Modal>
		);
	};

	// 渲染搜索
	_renderSearchForm = () => {
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					<Col span={24} align="right">
						<Button type="primary" onClick={this.handleSearch}>
							搜索
						</Button>
						&nbsp;&nbsp;&nbsp;&nbsp;
						{/* <Button type="default" onClick={this.handleReset}>
							重置
						</Button> */}
					</Col>
				</Row>
			</Form>
		);
	};

	render() {
		const { page, pageSize, columns } = this.state;
		const { dataList, total } = this.props;
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: total => {
				return `共 ${total} 条数据 `;
			},
			onChange: page => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		return (
			<div className="shadow-radius dictionary">
				<Row>
					<Col>
						<h1>数据字典</h1>
					</Col>
				</Row>
				<Row>{this._renderSearchForm()}</Row>
				<Row>{this._renderOperationButton()}</Row>
				<Row className="main">
					<Table bordered columns={columns} rowKey={() => Math.random()} dataSource={dataList} pagination={paginationProps} />
				</Row>
				{this._renderModal()}
				{this._renderModifyModal()}
			</div>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('dictionary');
	return {
		fields: data.fields,
		dataList: data.dataList, // 列表数据
		classSelect: data.classSelect, // 字典项选择框
		subClassSelect: data.subClassSelect, // 子节点选择框
		total: data.total, // 总条数
		visible: data.visible, // 是否显示弹窗
		modifyVisible: data.modifyVisible, // 是否显示修改弹窗
		detail: data.detail // 获取详情
	};
};
const mapDispatchToProps = dispatch => ({
	setState: data => dispatch(actionCreators.setState(data)), // 设置redux值
	list: data => dispatch(actionCreators.list(data)), // 获取列表
	getClassSelect: () => dispatch(actionCreators.getClassSelect()), // 获取字典项选择框
	getClassSelectByParent: data => dispatch(actionCreators.getClassSelectByParent(data)), // 根据父节点获取字典项选择框
	getDetail: data => dispatch(actionCreators.detail(data)), // 获取详情
	add: (data, list) => dispatch(actionCreators.add(data, list)), // 新增记录
	del: (data, list) => dispatch(actionCreators.del(data, list)), // 删除记录
	modify: (data, list) => dispatch(actionCreators.modify(data, list)) // 修改记录
});
export default connect(
	mapStateToProps,
	mapDispatchToProps
)(Form.create()(Index));
