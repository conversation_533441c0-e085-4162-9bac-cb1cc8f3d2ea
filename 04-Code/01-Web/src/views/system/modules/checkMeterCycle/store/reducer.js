import * as actionTypes from "./constants";
import { dataType } from '$utils'

const defaultState = {
	fields: null,
	dataList: [], // 列表数据
};

// 重要提示： 请做好各代码段的注释！！！
export default (state = defaultState, action) => {
	switch (action.type) {

		// 回填组件传给redux的值
		case actionTypes.SET_STATE:
			let _data = action.data;
			if (dataType(_data) === 'Object') {
				state[_data.key] = _data.value;
			}
			if (dataType(_data) === 'Array') {
				_data.forEach(item => {
					state[item.key] = item.value;
				})
			}
			return { ...state };
		// 回填列表数据
		case actionTypes.LIST_RECORD:
			state.dataList = action.data;
			return { ...state };
		// 添加
		case actionTypes.ADD_RECORD:
			return { ...state };
		default:
			return state;
	}
}
