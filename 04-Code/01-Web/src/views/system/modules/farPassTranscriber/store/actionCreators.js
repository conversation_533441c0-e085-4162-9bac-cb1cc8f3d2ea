import http from '$http';
import * as actionTypes from './constants';
import { message } from 'antd';
import api from './api';

// 数据回填
const payload = (type, data) => ({ type, data });

// 组件操作redux数据状态
const setState = data => ({ type: actionTypes.SET_STATE, data });
// 获取列表
const list = data => {
	return async dispatch => {
		const res = await http.post(api.list, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.LIST_RECORD, res.data));
		}
	};
};
// 获取片区选择框
const getAreaSelect = () => {
	return async dispatch => {
		const res = await http.get(api.getAreaSelect);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_AREA_SELECT, res.data));
		}
	};
};

// 设置抄表片区
const add = (data) => {
	return async dispatch => {
		const res = await http.post(api.add, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.ADD_RECORD, res.data));
		}
	};
};
// 获取除抄表员外已配置的片区ID列表
const getUsedAreaIdList = (data,visible) => {
	return async dispatch => {
		const res = await http.restGet(api.getUsedAreaIdList, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.GET_USED_AREA_ID_LIST, res.data));
			dispatch(getAreaSelect())
			dispatch(getAreaIdList(data,visible))
		}
	};
};
// 获取抄表员已配置的片区ID列表
const getAreaIdList = (data,visible) => {
	return async dispatch => {
		const res = await http.restGet(api.getAreaIdList, data);
		if (res.code === 0) {
			visible(res.data)
			dispatch(payload(actionTypes.GET_AREA_ID_LIST, res.data));
		}
	};
};
export { setState, list, getAreaSelect, add, getUsedAreaIdList, getAreaIdList };
