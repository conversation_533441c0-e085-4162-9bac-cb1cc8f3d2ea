import * as actionTypes from "./constants";
import { dataType } from '$utils'

const defaultState = {
	fields: null,
	dataList: [], // 列表数据
	total: 0, // 总条数
	detail: null, // 详情
	visible: false, // 涉及到交互的弹窗显示控制 默认为false,
	areaSelect: [], // 片区选择框
	usedAreaIdList: [], // 除查表员外已配置的片区ID列表
	areaIdList: [], // 查表员已配置的片区ID列表
};

// 重要提示： 请做好各代码段的注释！！！
export default (state = defaultState, action) => {
	switch (action.type) {

		// 回填组件传给redux的值
		case actionTypes.SET_STATE:
			let _data = action.data;
			if (dataType(_data) === 'Object') {
				state[_data.key] = _data.value;
			}
			if (dataType(_data) === 'Array') {
				_data.forEach(item => {
					state[item.key] = item.value;
				})
			}
			return { ...state };

		// 回填列表数据
		case actionTypes.LIST_RECORD:
			state.dataList = action.data.rows;
			state.total = action.data.total;
			return { ...state };
		// 获取片区选择框
		case actionTypes.GET_AREA_SELECT:
			state.areaSelect = action.data;
			return { ...state };
		// 设置片区权限
		case actionTypes.ADD_RECORD:
			state.usedAreaIdList = [];
			return { ...state };
		// 获取除查表员外已配置的片区ID列表
		case actionTypes.GET_USED_AREA_ID_LIST:
			let tmpList = action.data || [];
			state.usedAreaIdList.length = 0;
			tmpList.map(item => { state.usedAreaIdList.push(item + '') })
			return { ...state };
		// 获取查表员已配置的片区ID列表
		case actionTypes.GET_AREA_ID_LIST:
			state.areaIdList = action.data;
			return { ...state };
		default:
			return state;
	}
}
