// 申请类型
const CHECK_OUT_CYCLE = [
	{
		label: '请选择',
		value: ''
	},
	{
		label: '1',
		value: '01'
  },
  {
		label: '2',
		value: '02'
  },
  {
		label: '3',
		value: '03'
  },
  {
		label: '4',
		value: '04'
  },
  {
		label: '5',
		value: '05'
  },
  {
		label: '6',
		value: '06'
  },
  {
		label: '7',
		value: '07'
  },
  {
		label: '8',
		value: '08'
  },
  {
		label: '9',
		value: '09'
  },
  {
		label: '10',
		value: '10'
  },
  {
		label: '11',
		value: '11'
  },
  {
		label: '12',
		value: '12'
  },
  {
		label: '13',
		value: '13'
  },
  {
		label: '14',
		value: '14'
  },
  {
		label: '15',
		value: '15'
  },
  {
		label: '16',
		value: '16'
  },
  {
		label: '17',
		value: '17'
  },
  {
		label: '18',
		value: '18'
  },
  {
		label: '19',
		value: '19'
  },
  {
		label: '20',
		value: '20'
  },
  {
		label: '21',
		value: '21'
  },
  {
		label: '22',
		value: '22'
  },
  {
		label: '23',
		value: '23'
  },
  {
		label: '24',
		value: '24'
  },
  {
		label: '25',
		value: '25'
  },
  {
		label: '26',
		value: '26'
  },
  {
		label: '27',
		value: '27'
  },
  {
		label: '28',
		value: '28'
  },
  {
		label: '29',
		value: '29'
  },
  {
		label: '30',
		value: '30'
  },
  {
		label: '31',
		value: '31'
  },
]

// 发送消息与自动关阀
const AUTO_MATA = [
	{
		label: '全部开启',
		value: '0'
	},
	{
		label: '关闭自动关阀',
		value: '1'
	},
	{
		label: '关闭自动关阀与发送消息',
		value: '2'
	},
]

export { CHECK_OUT_CYCLE, AUTO_MATA };
