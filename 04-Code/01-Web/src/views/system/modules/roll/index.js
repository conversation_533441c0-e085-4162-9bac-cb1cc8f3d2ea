import React, { Component } from 'react';
import { connect } from 'react-redux';
import { actionCreators } from './store';
import { Modal, Button, Row, Col, Form, Input, Table, DatePicker, Tree } from 'antd';
import ModuleTree from '$components/module';
import './index.scss';

const { RangePicker } = DatePicker;
const FormItem = Form.Item;
const { confirm } = Modal;
const { TreeNode } = Tree;

class Index extends Component {
	constructor(props) {
		super(props);
		this.state = {
			title: '新增角色', // 弹窗标题
			modalType: 'add',
			page: 1,
			pageSize: 10,
			name: '',
			startTime: '', // 开始时间
			endTime: '', // 结束时间
			recordId: '',
			rangeStartTime: undefined, 
			rangeEndTime: undefined
		};
	}

	componentDidMount() {
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields && isTags) {
			this.setState({
				...fields.state
			}, () => {
				this.props.form.setFieldsValue({
					...fields.form
				})
			})
		} else {
			this.getList()
		}
	}

	componentWillUnmount() {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			})
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			})
		}
	}

	// 获取列表数据
	getList = () => {
		const { name, page, pageSize, startTime, endTime } = this.state;
		let params = {
			page,
			pageSize,
			name,
			startTime,
			endTime
		};
		this.props.list(params);
	};

	onChange = (date, dateString) => {
		console.log(date, dateString);
		this.setState({ startTime: dateString[0], endTime: dateString[1], rangeStartTime: date[0], rangeEndTime: date[1] });
	};

	// 分配权限
	handleAuthority = record => {
		this.setState({ recordId: record.id }, () => {
			this.props.setState({ key: 'show', value: true });
		})
	};

	// 关闭权限弹窗
	handleCancelAuthority = () => {
		this.props.setState({ key: 'show', value: false });
	};

	// 提交片区权限
	handleSubmitAuthority = arr => {
		let params = {
			roleId: this.state.recordId,
			permissionIds: arr
		};
		this.props.moduleSave(params);
		this.handleCancelAuthority();
	};

	// 搜索
	handleSearch = () => {
		this.setState({
			page: 1,
		}, () => {
			this.getList();
		})
	};

	// 重置搜索条件
	handleReset = () => {
		this.setState({
			name: '',
			startTime: '',
			endTime: '',
			rangeStartTime: undefined,
			rangeEndTime: undefined,
			page: 1,
		}, () => {

		})
	};

	// 列表分页
	handlePageChange = (page) => {
		this.setState({
			page
		}, () => {
			this.getList();
		})
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState({
			page: 1,
			pageSize: size,
		}, () => {
			this.getList();
		})
	};

	// 查看详情
	handleView = record => {
		this.setState({
			title: '查看详情'
		}, () => {
			this.props.setState({ key: 'visible', value: true });
			this.props.getDetail(record.id)
		});
	};

	// 提交
	handleSubmit = e => {
		e.preventDefault();
		const { title } = this.state;
		const { detail } = this.props;
		this.props.form.validateFields((err, values) => {
			if (!err) {

				if (title === '新增角色') {
					let params = {
						...values
					};
					this.props.add(params, this.getList)
				}
				if (title === '编辑角色') {
					let params = {
						...values,
						id: detail.id
					};
					this.props.modify(params, this.getList)
				}
			}
		});
	};

	// 取消弹窗
	handleCancel = () => {
		this.props.form.resetFields();
		this.props.setState([{ key: 'visible', value: false }, { key: 'detail', value: null }]);
	};

	// 编辑
	handleEdit = record => {
		this.setState({
			title: '编辑角色'
		}, () => {
			this.props.setState({ key: 'visible', value: true });
			this.props.getDetail(record.id)
		})
	};

	// 删除
	handleDel = record => {
		const _this = this;
		confirm({
			title: '确认操作',
			content: '是否确认删除该条记录？',
			okText: '确定',
			okType: 'danger',
			cancelText: '取消',
			onOk() {
				_this.props.del(record.id, _this.getList)
			},
			onCancel() {
				console.log('Cancel');
			},
		});
	};

	// 新增角色
	handleAdd = () => {
		this.setState({
			title: '新增角色'
		}, () => {
			this.props.setState({ key: 'visible', value: true });
		})
	};

	render() {
		const { title, name, page, pageSize, modalType, recordId, rangeStartTime, rangeEndTime } = this.state;
		const { form, datalist, total, visible, detail, show } = this.props;
		const { getFieldDecorator } = form;
		const formItemLayout = {
			labelCol: {
				xs: { span: 6 },
				sm: { span: 6 },
			},
			wrapperCol: {
				xs: { span: 12 },
				sm: { span: 12 },
			},
		};
		const formBtnLayout = {
			labelCol: {
				xs: { span: 0 },
				sm: { span: 0 },
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: { span: 24 },
			},
		};
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: (total) => { return `共 ${total} 条数据 ` },
			onChange: (page) => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		const columns = [
			{
				title: '序号',
				dataIndex: 'xuhao',
				width: 50,
				align: 'center',
				render: (text, record, index) => {
					return index + 1
				}
			},
			{
				title: '角色名称',
				dataIndex: 'name',
				key: 'name',
				align: 'center',
				width: 100,
			},
			{
				title: '角色编码',
				dataIndex: 'code',
				key: 'code',
				align: 'center',
				width: 100,
			},
			{
				title: '创建日期',
				dataIndex: 'createTime',
				key: 'createTime',
				align: 'center',
				width: 100,
			},
			{
				title: '操作',
				key: 'operation',
				width: 200,
				align: 'center',
				render: (text, record, index) => {
					return (
						<span>
							<Button permission={React.$pmn('REVENUE:SYSTEM_SETTINGS:ROLE_MANAGEMENT:VIEW')} title='查看' className='btn' type="primary" size="small" icon="eye" onClick={() => this.handleView(record)} />
							<Button permission={React.$pmn('REVENUE:SYSTEM_SETTINGS:ROLE_MANAGEMENT:EDIT')} title='编辑' className='btn' type="primary" size="small" icon="form" onClick={() => this.handleEdit(record)} />
							<Button permission={React.$pmn('REVENUE:SYSTEM_SETTINGS:ROLE_MANAGEMENT:DEL')} title='删除' className='btn' type="primary" size="small" icon="delete" onClick={() => this.handleDel(record)} />
							<Button permission={React.$pmn('REVENUE:SYSTEM_SETTINGS:ROLE_MANAGEMENT:PERMISSION')} title='模块权限' className='btn' type="primary" size="small" icon="file-protect" onClick={() => this.handleAuthority(record)} />
							&nbsp;	
						</span>
					)
				},
			}
		];
		return (
			<div className="shadow-radius roll">
				{/* 页面标题 - 搜索条件 */}
				<Row>
					<Col><h1>角色管理</h1></Col>
				</Row>
				{/* 搜索 */}
				<Row>
					<Form {...formItemLayout} className="ant-advanced-search-form">
						<Col span={10}>
							<FormItem label={'角色名称:'}>
								<Input placeholder="请输入员工账号" value={name} onChange={v => this.setState({ name: v.target.value })} />
							</FormItem>
						</Col>
						<Col span={10}>
							<FormItem label={'创建时间:'}>
								<RangePicker value={[rangeStartTime, rangeEndTime]} onChange={this.onChange} placeholder={['开始日期', '结束日期']} />
							</FormItem>
						</Col>
						<Col span={4} align='right'>
							<FormItem {...formBtnLayout}>
								<Button type='primary' onClick={this.handleSearch}>搜索</Button>
								&nbsp;&nbsp;&nbsp;&nbsp;
								<Button type='default' onClick={this.handleReset}>重置</Button>
							</FormItem>
						</Col>
					</Form>
				</Row>
				<Row>
					<Button permission={React.$pmn('REVENUE:SYSTEM_SETTINGS:ROLE_MANAGEMENT:ADD')} type="primary" onClick={this.handleAdd}>新增角色</Button>
				</Row>
				<Row className='main'>
					<Table
						bordered
						columns={columns}
						rowKey={() => Math.random()}
						dataSource={datalist}
						pagination={paginationProps}
					/>
				</Row>
				<Modal className="rollModal" title={title} visible={visible} onCancel={this.handleCancel} footer={null}>
					<Form {...formItemLayout}>
						<Row>
							<Col span={12}>
								<FormItem label="角色名称：">
									{
										modalType === 'view' ?
											detail && detail.name
											:
											getFieldDecorator('name', {
												initialValue: detail ? detail.name : '',
												rules: [{
													required: true,
													message: '角色名称必填',
												}, {
													validator: (rule, value, callback) => {
														if (/\s+/.test(value)) {
															callback('不允许空格');
														} else {
															callback();
														}
													}
												}]
											})(<Input disabled={modalType === 'view'} palceholder='请输入员工姓名' />)
									}
								</FormItem>
							</Col>
							<Col span={12}>
								<FormItem label="角色编码：">
									{
										modalType === 'view' ?
											detail && detail.code
											:
											getFieldDecorator('code', {
												initialValue: detail ? detail.code : '',
												rules: [
													{
														required: true,
														message: '角色代码必填',
													},
													{
														validator: (rule, value, callback) => {
															if (/\s+/.test(value)) {
																callback('不允许空格');
															} else {
																callback();
															}
														}
													}]
											})(<Input disabled={modalType === 'view'} palceholder='请输入员工工号' />)
									}
								</FormItem>
							</Col>
						</Row>
						<Row>
							<Col span={24} align="center">
								<Button type="primary" className="btn" onClick={this.handleSubmit}>提交</Button>
								<Button className="btn" onClick={this.handleCancel}>取消</Button>
							</Col>
						</Row>
					</Form>
				</Modal>
				{/* 片区配置弹窗 */}
				<ModuleTree visible={show} id={parseInt(recordId)} onCancel={this.handleCancelAuthority} onSubmit={this.handleSubmitAuthority} />
			</div>
		)
	}
}

const mapStateToProps = state => {
	const data = state.get('roll');
	return {
		fields: data.fields,
		datalist: data.datalist, // 列表数据
		total: data.total, // 总条数
		visible: data.visible, // 是否显示弹窗
		detail: data.detail, // 获取详情
		show: data.show, // 模块树控制器
	}
};
const mapDispatchToProps = dispatch => ({
	setState: data => dispatch(actionCreators.setState(data)), // 设置redux值
	add: (data, list) => dispatch(actionCreators.add(data, list)), // 新增
	modify: (data, list) => dispatch(actionCreators.modify(data, list)), // 修改
	list: data => dispatch(actionCreators.list(data)), // 获取列表
	getDetail: data => dispatch(actionCreators.detail(data)), // 获取详情
	moduleSave: data => dispatch(actionCreators.moduleSave(data)), // 保存模块权限
	del: (data, list) => dispatch(actionCreators.del(data, list)), // 删除记录
});
export default connect(
	mapStateToProps,
	mapDispatchToProps
)((Form.create())(Index));
