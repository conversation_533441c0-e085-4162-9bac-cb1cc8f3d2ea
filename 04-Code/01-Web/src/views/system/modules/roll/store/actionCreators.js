import http from '$http';
import * as actionTypes from './constants';
import api from './api';
import { message } from 'antd';

// 数据回填至redux
const payload = (type, data) => ({type, data});

const setState = data => {
	return dispatch => dispatch({type: actionTypes.SET_STATE, data})
};

// 获取列表数据
const list = data => {
	return async dispatch => {
		const res = await http.post(api.list, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.LIST_RECORD, res.data));
		}
	}
};

// 获取详情
const detail = data => {
	return async dispatch => {
		const res = await http.restGet(api.detail, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.DETAIL_RECORD, res.data))
		}
	}
};

const add = (data, list) => {
	return async dispatch => {
		const res = await http.post(api.add, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.ADD_RECORD))
			message.success('新增成功！');
			list();
		}
	}
};

const modify = (data, list) => {
	return async dispatch => {
		const res = await http.post(api.modify, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.MODIFY_RECORD))
			message.success('修改成功！');
			list();
		}
	}
};

// 删除记录
const del = (data, list) => {
	return async dispatch => {
		const res = await http.del(api.del, data);
		if (res.code === 0) {
			message.success('删除成功');
			list()
		}
	}
};

const moduleSave = data => {
	return async dispatch => {
		const res = await http.post(api.moduleSave, data);
		if (res.code === 0) {
			message.success('保存成功');
		}
	}
};

export { list, add, modify, detail, setState, del, moduleSave, };
