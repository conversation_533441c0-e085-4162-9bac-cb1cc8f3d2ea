import * as actionTypes from './constants';
import { dataType } from '$utils'

const defaultState = {
	fields: null,
	total: 0, // 总条数
	datalist: [], // 片区列表
	detail: null, // 片区详情
	visible: false, // 是否显示弹框
	show: false, // 用户片区权限弹框
	departments: [], // 部门
	rolls: [], // 员工角色列表
	areas: [], // 片区列表
	defaultExpandedKeys: [], // 默认展开的树节点
	defaultCheckedKeys: [], // 默认选中的树节点
};

const getIds = id => {
	let ids = `${id}`
	let m = ids.length / 3;
	let _ids = [];
	for (let i = 1; i <= m; i++) {
		_ids.push(parseInt(ids.slice(0, i * 3)));
	}
	return _ids;
};

export default (state = defaultState, action) => {
	switch (action.type) {
		// 获取列表
		case actionTypes.LIST_RECORD:
			const data = action.data;
			// 赋值列表
			state.datalist = data.rows;
			// 赋值总条数
			state.total = data.total;
			// 更新state
			return { ...state };

		// 获取详情
		case actionTypes.GET_DETAIL:
			state.detail = action.data;
			return { ...state };

		// 修改记录
		case actionTypes.MODIFY_RECORD:
			state.visible = false;
			state.detail = null;
			return { ...state };

		// 新增记录
		case actionTypes.ADD_RECORD:
			state.visible = false;
			state.detail = null;
			return { ...state };

		// 删除记录
		case actionTypes.DELETE_RECORD:
			let idx = state.datalist.findIndex(item => item.id === action.data);
			state.datalist[idx].accountStatus = '不可用';
			state.datalist[idx].status = '删除';
			state.datalist = [...state.datalist];
			return { ...state };

		// 设置state值
		case actionTypes.SET_STATE:
			let _data = action.data;
			if (dataType(_data) === 'Object') {
				state[_data.key] = _data.value;
			}
			if (dataType(_data) === 'Array') {
				_data.forEach(item => {
					state[item.key] = item.value;
				})
			}
			return { ...state };

		// 获取角色列表
		case actionTypes.GET_ROLLS:
			state.rolls = action.data;
			return { ...state };

		// 获取部门树
		case actionTypes.GET_DEPTS:
			state.departments = action.data;
			return { ...state };
		case actionTypes.GET_AREAS:
			state.areas = action.data;
			return { ...state };
		// 获取用户片区权限
		case actionTypes.GET_USER_AREAS:
			let acd = action.data;
			let _arr = [];
			// 根据规则(3-3-3-3-3-3-....-3)打散所有id,并重新组合
			if (acd.length > 0) {
				acd.forEach(item => {
					_arr = [..._arr, ...getIds(item)]
				});
				// 去重所有id
				_arr = [...new Set(_arr)]
			}
			// 赋值需要展开的列表
			state.defaultExpandedKeys = _arr;
			// 设置默认选中的树节点
			state.defaultCheckedKeys = acd;
			state.show = true;
			return { ...state };
		default:
			return state;
	}
}
