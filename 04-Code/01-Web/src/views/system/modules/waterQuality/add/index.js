import React, { Component, Fragment } from 'react';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import { actionCreators } from '../store';
import { constants } from '$utils';
import { Row, Col, Form, Select, Input, Button, Icon, Checkbox, Divider, message, Tooltip } from 'antd';

import './index.scss';

const FormItem = Form.Item;
const { Option } = Select;
const InputGroup = Input.Group;

let isDone = false;
let id = 0;
const month = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'];
let temp = {}; // 月份 中间缓存
let nums = {
	1: '0'
};

// 普通form item
const formItemLayout = {
	labelCol: {
		xs: { span: 9 },
		sm: { span: 9 }
	},
	wrapperCol: {
		xs: { span: 12 },
		sm: { span: 12 }
	}
};

// 无FormItem Label 的item
const formItemLayoutWithOutLabel = {
	wrapperCol: {
		xs: { span: 16, offset: 4 },
		sm: { span: 16, offset: 4 }
	}
};

// 阶梯配置相关
const formItemWithLadder = {
	labelCol: {
		xs: { span: 4 },
		sm: { span: 4 }
	},
	wrapperCol: {
		xs: { span: 20 },
		sm: { span: 20 }
	}
};

class Index extends Component {
	constructor(props) {
		super(props);
		this.state = {
			ladderType: '' // 阶梯类型
		};
	}

	componentDidMount() {
		const types = sessionStorage.getItem('_waterUseKindOpType');
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields && isTags) {
			this.setState({
				...fields.state
			}, () => {
				this.props.form.setFieldsValue({
					...fields.form
				})
			})
		}
		if (types !== 'add') {
			const id = sessionStorage.getItem('_waterUseKindId');
			this.props.getDetail(id);
		}
	}

	componentWillUnmount() {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			})
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			})
		}
	}

	// 取消
	handleCancel = () => {
		this.props.history.push('/sysSettings/waterQuality');
		sessionStorage.removeItem('_waterUseKindOpType');
		sessionStorage.removeItem('_waterUseKindId');
	};

	// 提交
	handleSubmit = e => {
		const types = sessionStorage.getItem('_waterUseKindOpType');
		e.preventDefault();
		const {
			form: { validateFields },
			add
		} = this.props;
		validateFields((err, values) => {
			if (!err) {
				let { name, type, reliefAmount, criticalAmount, penaltyProportion, ladderType, cleanWaterFee, sewageFee, waterResourceFee, otherFee } = values;
				reliefAmount = parseFloat(reliefAmount);
				criticalAmount = parseFloat(criticalAmount);
				penaltyProportion = parseFloat(penaltyProportion);
				let params = null;
				let waterUserKingDetailDTOList = [];
				let keys = values.keys;
				if (values.ladderType === 0) {
					keys.forEach((item, index) => {
						let cur = `${item}${index}`; // 当前循环值
						waterUserKingDetailDTOList.push({
							ladderName: '非阶梯',
							ladderLevel: 1,
							cleanWaterFee: parseFloat(values[`cleanWaterFee${cur}`]), // 清水费
							sewageFee: parseFloat(values[`sewageFee${cur}`]), // 污水费
							otherFee: parseFloat(values[`otherFee${cur}`]), // 其他
							waterResourceFee: parseFloat(values[`waterResourceFee${cur}`]), // 水资源税
							ladderStartAmount: 0,
							ladderEndAmount: -1,
							basicPopulation: 0,
							ladderWaterIncrement: 0,
							effectMonth: temp[`${cur}`].join(',')
						})
					});
					params = {
						name,
						type,
						reliefAmount,
						criticalAmount,
						penaltyProportion,
						ladderType,
						waterUserKingDetailDTOList
					};
				} else {
					let idx = 0;
					let ladderStartAmount = 0; // 水量范围：阶梯开始量
					let ladderEndAmount = 0; // 水量范围：阶梯结束量
					let basicPopulation = 0; // 人口基数
					let ladderWaterIncrement = 0; // 人口阶梯增量
					let ladderName = '';
					keys.forEach((item, index) => {
						let ladderLevel = item; // 阶梯级别
						let cur = `${item}${index}`; // 当前循环值
						// 每个阶梯 第一组数据
						if (item !== idx) {
							idx = item;
							ladderName = `阶梯${item}`;
							ladderStartAmount = parseFloat(values[`ladderStartAmount${item}${index}`]); // 水量范围：阶梯开始量
							ladderEndAmount = parseFloat(values[`ladderEndAmount${item}${index}`]); // 水量范围：阶梯结束量
							basicPopulation = parseFloat(values[`basicPopulation${item}${index}`]); // 人口基数
							ladderWaterIncrement = parseFloat(values[`ladderWaterIncrement${item}${index}`]); // 人口阶梯增量
						}
						waterUserKingDetailDTOList.push({
							ladderName,
							cleanWaterFee: parseFloat(values[`cleanWaterFee${cur}`]), // 清水费
							sewageFee: parseFloat(values[`sewageFee${cur}`]), // 污水费
							otherFee: parseFloat(values[`otherFee${cur}`]), // 其他
							waterResourceFee: parseFloat(values[`waterResourceFee${cur}`]), // 水资源税
							ladderLevel,
							ladderStartAmount,
							ladderEndAmount,
							basicPopulation,
							ladderWaterIncrement,
							effectMonth: temp[`${cur}`].join(',')
						});
					});
					params = {
						name,
						type,
						reliefAmount,
						criticalAmount,
						penaltyProportion,
						ladderType,
						waterUserKingDetailDTOList
					};
				}
				add(params, this.props.history);
			}
		});
	};

	// 阶梯类型select
	ladderTypeChange = v => {
		const { form: { getFieldValue, resetFields, setFieldsValue } } = this.props;
		this.setState({ ladderType: v }, () => {
			const keys = [];
			const ladderType = v;
			// 切换选项时，初始化部分表单
			const type = getFieldValue('type');
			const name = getFieldValue('name');
			const penaltyProportion = getFieldValue('penaltyProportion');
			const reliefAmount = getFieldValue('reliefAmount');
			const criticalAmount = getFieldValue('criticalAmount');
			setFieldsValue({
				type, // 用水性质
				name, // 用水分类
				penaltyProportion, // 滞纳金比例
				reliefAmount, // 减免吨数
				criticalAmount, // 临界吨数
				keys, // 动态keys
				ladderType // 阶梯类型
			});
			// 月份筛选相关参数重置
			temp = {};
			id = 0;
			if (id === 0) {
				this.ladderConfig();
			}
		});
	};

	// 执行月份 checked / unchecked 事件、业务逻辑
	handleCheckBox = (key, index, values) => {
		const { form } = this.props;
		if (values.length === 0) {
			message.error('单条记录至少保留一个月份');
			setTimeout(() => {
				form.setFieldsValue({
					[`effectMonth${key}${index}`]: temp[`${key}${index}`] // 赋值当前的执行月份
				});
			}, 0);
			return;
		}
		let TEMP = []; // 缓存全局temp的值

		// 获取当前所有的 阶梯列表 keys
		const keys = form.getFieldValue('keys');

		// 判断当前该记录是否存在，如果不存在，则新建一条记录
		if (typeof temp[`${key}${index + 1}`] === 'undefined') {
			// 在temp中插入一条记录
			temp[`${key}${index + 1}`] = [];
			// 查询当前阶梯的定位
			const idx = index + 1;
			// 并在当前定位后面插入一条数据
			keys.splice(idx, 0, key);
			// 重新配置阶梯列表
			form.setFieldsValue({
				keys
			});
		}

		// 对上一次记录进行循环，并筛选出被选月份
		temp[`${key}${index}`].forEach(item => {
			if (values.indexOf(item) < 0) {
				temp[`${key}${index + 1}`].push(item);
			}
		});

		// 重新赋值当前记录集
		temp[`${key}${index}`] = values;

		// 循环当前已存在对象，并临时存储
		Object.keys(temp).forEach(item => {
			TEMP.push(temp[item]);
		});
		temp = {};
		keys.forEach((item, idx) => {
			temp[`${item}${idx}`] = TEMP[idx];
		});

		// 赋值阶梯配置的执行月份
		form.setFieldsValue({
			[`effectMonth${key}${index}`]: values, // 赋值当前的执行月份
			[`effectMonth${key}${index + 1}`]: temp[`${key}${index + 1}`] // 给当前阶梯配置的下一个阶梯配置赋值执行月份
		});
	};

	// 添加阶梯配置
	ladderConfig = () => {
		const { form } = this.props;
		// 对循环添加变量进行自增
		id += 1;
		// can use data-binding to get
		const keys = form.getFieldValue('keys');
		// 将每一次新增的阶梯key 绑定到temp变量中，并给该key值赋值 []
		const nextKeys = keys.concat(id);
		temp[`${id}${nextKeys.length - 1}`] = month;
		// nums[id] = form.getFieldValue(`ladderEndAmount${id-1}${nextKeys.length - 1}`) || '0';
		// can use data-binding to set
		// important! notify form to detect changes
		form.setFieldsValue({
			keys: nextKeys
		});
	};

	// 删除阶梯配置记录集
	remove = (key, index) => {
		const { form } = this.props;
		// can use data-binding to get
		const keys = form.getFieldValue('keys');
		// 对应的 id 顺序 -1
		if (index > 0 && keys[index] !== keys[index - 1]) {
			id -= 1;
		}
		// We need at least one passenger
		if (keys.length === 1) {
			return;
		}

		// 获取当前被删除记录的执行月份
		const cur = form.getFieldValue(`effectMonth${key}${index}`);
		// 获取当前记录的上一条记录的执行月份
		const prev = form.getFieldValue(`effectMonth${key}${index - 1}`) || [];

		// 删除对应记录
		keys.splice(index, 1);

		// 回填keys
		form.setFieldsValue({
			keys
		});

		if (form.getFieldValue(`effectMonth${key}${index - 1}`)) {
			// 回填上一条的执行月份
			form.setFieldsValue({
				[`effectMonth${key}${index - 1}`]: prev.concat(cur)
			});
		}
	};

	// 渲染非阶梯详情
	_renderUnLadderConfigList = list => {
		return list.map((item, index) => {
			let months = item.effectMonth.split(',');
			months = months.sort(function (a, b) {
				return a * 1 - b * 1;
			});
			months = months.join(',');
			return (
				<Fragment key={index}>
					<Col span={18} offset={3}>
						<Divider dashed orientation="left">
							{item.ladderName}
						</Divider>
						{/* 清水价 - span(13) */}
						<Col span={13}>
							<FormItem label="清水价（元）：">{item.cleanWaterFee}</FormItem>
						</Col>
						{/* 污水价 - span(11) */}
						<Col span={11}>
							<FormItem label="污水价（元）：">{item.sewageFee}</FormItem>
						</Col>
						{/* 水资源税 - span(11) */}
						<Col span={13}>
							<FormItem label="水资源税（元）：">{item.waterResourceFee}</FormItem>
						</Col>
						{/* 其他 - span(11) */}
						<Col span={11}>
							<FormItem label="其他（元）：">{item.otherFee}</FormItem>
						</Col>
						{/* 执行月份 - span(24) */}
						<Col span={24}>
							<FormItem label="执行月份：" {...formItemWithLadder}>
								{months}
							</FormItem>
						</Col>
					</Col>
					<Col span={2} />
				</Fragment>
			);
		});
	};

	// 渲染月阶梯、年阶梯详情
	_renderLadderConfigList = list => {
		return list.map((item, index) => {
			let months = item.effectMonth.split(',');
			months = months.sort(function (a, b) {
				return a * 1 - b * 1;
			});
			months = months.join(',');
			return (
				<Fragment key={index}>
					<Col span={18} offset={3}>
						<Divider dashed orientation="left">
							{item.ladderName}
						</Divider>
						{/* 清水价 - span(13) */}
						<Col span={13}>
							<FormItem label="清水价（元）：">{item.cleanWaterFee}</FormItem>
						</Col>
						{/* 污水价 - span(11) */}
						<Col span={11}>
							<FormItem label="污水价（元）：">{item.sewageFee}</FormItem>
						</Col>
						{/* 水资源税 - span(11) */}
						<Col span={13}>
							<FormItem label="水资源税（元）：">{item.waterResourceFee}</FormItem>
						</Col>
						{/* 其他 - span(11) */}
						<Col span={11}>
							<FormItem label="其他（元）：">{item.otherFee}</FormItem>
						</Col>
						{/* 水量范围 - span(13) */}
						<Col span={13}>
							<FormItem label="水量范围：">{`${item.ladderStartAmount} ~ ${item.ladderEndAmount}`}</FormItem>
						</Col>
						{/* 人口基数 - span(13) */}
						<Col span={13}>
							<FormItem label="人口基数（人）：">{item.basicPopulation}</FormItem>
						</Col>
						{/* 人口阶梯增量 - span(11) */}
						<Col span={11}>
							<FormItem label="人口阶梯增量（m³）：">{item.ladderWaterIncrement}</FormItem>
						</Col>
						{/* 执行月份 - span(24) */}
						<Col span={24}>
							<FormItem label="执行月份：" {...formItemWithLadder}>
								{months}
							</FormItem>
						</Col>
					</Col>
					<Col span={2} />
				</Fragment>
			);
		});
	};

	// 渲染阶梯配置 （月阶梯、年阶梯）
	_renderLadderConfig = () => {
		const { getFieldDecorator, getFieldValue, setFieldsValue } = this.props.form;
		getFieldDecorator('keys', { initialValue: [] });
		const keys = getFieldValue('keys');
		let last = 0; // 上一次的阶梯 key 值
		let ids = 0; // 上一次的阶梯 key 值对应的 index 值
		return keys.map((key, index) => {
			let disabled = true; // 设定 是否 disabled
			if (key !== last) {
				last = key;
				ids = index;
				disabled = false;
			}
			return (
				<Fragment key={index}>
					<Col span={18} offset={3}>
						<Divider dashed orientation="left">
							阶梯{key}
						</Divider>
						{/* 清水价 - span(13) */}
						<Col span={13}>
							<FormItem label="清水价（元）：">
								{getFieldDecorator(`cleanWaterFee${key}${index}`, {
									initialValue: '',
									rules: [
										{ required: true, message: '清水价必填' },
										{
											validator: (rule, value, callback) => {
												if (/\s+/.test(value)) {
													this.props.form.setFieldsValue({
														cleanWaterFee: value.replace(/\s+/, '')
													});
													callback();
												} else {
													if (/^\d+\.?\d{0,2}$/.test(value)) {
														callback();
													} else {
														setFieldsValue({
															cleanWaterFee: `${value}`.substr(0, `${value}`.length - 1)
														});
														callback();
													}
												}
											}
										}
									]
								})(<Input placeholder="请输入清水价" suffix="元" />)}
							</FormItem>
						</Col>
						{/* 污水价 - span(11) */}
						<Col span={11}>
							<FormItem label="污水价（元）：">
								{getFieldDecorator(`sewageFee${key}${index}`, {
									initialValue: '',
									rules: [
										{
											required: true,
											message: '污水价必填'
										},
										{
											validator: (rule, value, callback) => {
												if (/\s+/.test(value)) {
													setFieldsValue({
														sewageFee: value.replace(/\s+/, '')
													});
													callback();
												} else {
													if (/^\d+\.?\d{0,2}$/.test(value)) {
														callback();
													} else {
														setFieldsValue({
															sewageFee: `${value}`.substr(0, `${value}`.length - 1)
														});
														callback();
													}
												}
											}
										}
									]
								})(<Input placeholder="请输入污水价" suffix="元" />)}
							</FormItem>
						</Col>

						{/* 水资源税 - span(13) */}
						<Col span={13}>
							<FormItem label="水资源税（元）：">
								{getFieldDecorator(`waterResourceFee${key}${index}`, {
									initialValue: '',
									rules: [
										{
											required: true,
											message: '水资源税必填'
										},
										{
											validator: (rule, value, callback) => {
												if (/\s+/.test(value)) {
													this.props.form.setFieldsValue({
														waterResourceFee: value.replace(/\s+/, '')
													});
													callback();
												} else {
													if (/^\d+\.?\d{0,2}$/.test(value)) {
														callback();
													} else {
														this.props.form.setFieldsValue({
															waterResourceFee: `${value}`.substr(0, `${value}`.length - 1)
														});
														callback();
													}
												}
											}
										}
									]
								})(<Input placeholder="请输入水资源税" suffix="元" />)}
							</FormItem>
						</Col>
						{/* 其他 - span(11) */}
						<Col span={11}>
							<FormItem label="其他（元）：">
								{getFieldDecorator(`otherFee${key}${index}`, {
									initialValue: '',
									rules: [
										{
											required: true,
											message: '其他费用必填'
										},
										{
											validator: (rule, value, callback) => {
												if (/\s+/.test(value)) {
													setFieldsValue({
														otherFee: value.replace(/\s+/, '')
													});
													callback();
												} else {
													if (/^\d+\.?\d{0,2}$/.test(value)) {
														callback();
													} else {
														setFieldsValue({
															otherFee: `${value}`.substr(0, `${value}`.length - 1)
														});
														callback();
													}
												}
											}
										}
									]
								})(<Input placeholder="请输入其他费用" suffix="元" />)}
							</FormItem>
						</Col>

						{/* 水量范围 - span(13) */}
						<Col span={13}>
							<FormItem label="水量范围：">
								<InputGroup>
									{getFieldDecorator(`ladderStartAmount${key}${ids}`, {
										initialValue: index === 0 ? '0' : nums[key - 1],
										rules: [
											{
												required: true,
												message: '阶梯开始量必填'
											}
										]
									})(<Input className="betweenIpt noBorderRight" placeholder="阶梯开始量" disabled={true} />)}
									<Input className="midIpt noBorderLeft noBorderRight" placeholder="~" disabled={true} />
									{getFieldDecorator(`ladderEndAmount${key}${ids}`, {
										initialValue: '',
										rules: [
											{
												required: true,
												message: '阶梯结束量必填'
											},
											{
												validator: (rule, value, callback) => {
													if (/\s+/.test(value)) {
														setFieldsValue({
															[`ladderEndAmount${key}${ids}`]: value.replace(/\s+/, '')
														});
														callback();
													} else {
														if (/^\d+\.?\d{0,2}$/.test(value)) {
															nums[key] = value;
															callback();
														} else if (value === '-' || value === '-1') {
															callback();
														} else {
															setFieldsValue({
																[`ladderEndAmount${key}${ids}`]: `${value}`.substr(0, `${value}`.length - 1)
															});
															callback();
														}
													}
												}
											}
										]
									})(<Input
										className="betweenIpt noBorderLeft"
										placeholder="阶梯结束量"
										disabled={disabled}
									/>)}
									<Tooltip placement="top" title="阶梯结束量不限时请填写-1">
										<Icon type="question-circle-o" style={{ marginLeft: 5 }} />
									</Tooltip>
								</InputGroup>
							</FormItem>
						</Col>
						{/* 人口基数 - span(13) */}
						<Col span={13}>
							<FormItem label="人口基数（人）：">
								{getFieldDecorator(`basicPopulation${key}${ids}`, {
									initialValue: '',
									rules: [
										{
											required: true,
											message: '人口基数必填'
										},
										{
											validator: (rule, value, callback) => {
												if (/\s+/.test(value)) {
													setFieldsValue({
														basicPopulation: value.replace(/\s+/, '')
													});
													callback();
												} else {
													if (/^\d+\.?\d{0,2}$/.test(value)) {
														callback();
													} else {
														setFieldsValue({
															basicPopulation: `${value}`.substr(0, `${value}`.length - 1)
														});
														callback();
													}
												}
											}
										}
									]
								})(<Input placeholder="请输入人口基数" suffix="人" disabled={disabled} />)}
							</FormItem>
						</Col>
						{/* 人口阶梯增量 - span(11) */}
						<Col span={11}>
							<FormItem label="人口阶梯增量（m³）：">
								{getFieldDecorator(`ladderWaterIncrement${key}${ids}`, {
									initialValue: '',
									rules: [
										{
											required: true,
											message: '人口阶梯增量必填'
										},
										{
											validator: (rule, value, callback) => {
												if (/\s+/.test(value)) {
													setFieldsValue({
														ladderWaterIncrement: value.replace(/\s+/, '')
													});
													callback();
												} else {
													if (/^\d+\.?\d{0,2}$/.test(value)) {
														callback();
													} else {
														setFieldsValue({
															ladderWaterIncrement: `${value}`.substr(0, `${value}`.length - 1)
														});
														callback();
													}
												}
											}
										}
									]
								})(<Input placeholder="请输入人口阶梯增量" suffix="m³" disabled={disabled} />)}
							</FormItem>
						</Col>
						{/* 执行月份 - span(24) */}
						<Col span={24}>
							<FormItem label="执行月份：" {...formItemWithLadder}>
								{getFieldDecorator(`effectMonth${key}${index}`, {
									initialValue: temp[`${key}${index}`] || '',
									rules: [
										{
											required: true,
											message: ' '
										}
									]
								})(
									<Checkbox.Group style={{ width: '100%' }} onChange={values => this.handleCheckBox(key, index, values)}>
										<Row>
											<Col span={4}>
												<Checkbox disabled={getFieldValue(`effectMonth${key}${index}`).indexOf('01') < 0} value="01">
													一月
												</Checkbox>
											</Col>
											<Col span={4}>
												<Checkbox disabled={getFieldValue(`effectMonth${key}${index}`).indexOf('02') < 0} value="02">
													二月
												</Checkbox>
											</Col>
											<Col span={4}>
												<Checkbox disabled={getFieldValue(`effectMonth${key}${index}`).indexOf('03') < 0} value="03">
													三月
												</Checkbox>
											</Col>
											<Col span={4}>
												<Checkbox disabled={getFieldValue(`effectMonth${key}${index}`).indexOf('04') < 0} value="04">
													四月
												</Checkbox>
											</Col>
											<Col span={4}>
												<Checkbox disabled={getFieldValue(`effectMonth${key}${index}`).indexOf('05') < 0} value="05">
													五月
												</Checkbox>
											</Col>
											<Col span={4}>
												<Checkbox disabled={getFieldValue(`effectMonth${key}${index}`).indexOf('06') < 0} value="06">
													六月
												</Checkbox>
											</Col>
											<Col span={4}>
												<Checkbox disabled={getFieldValue(`effectMonth${key}${index}`).indexOf('07') < 0} value="07">
													七月
												</Checkbox>
											</Col>
											<Col span={4}>
												<Checkbox disabled={getFieldValue(`effectMonth${key}${index}`).indexOf('08') < 0} value="08">
													八月
												</Checkbox>
											</Col>
											<Col span={4}>
												<Checkbox disabled={getFieldValue(`effectMonth${key}${index}`).indexOf('09') < 0} value="09">
													九月
												</Checkbox>
											</Col>
											<Col span={4}>
												<Checkbox disabled={getFieldValue(`effectMonth${key}${index}`).indexOf('10') < 0} value="10">
													十月
												</Checkbox>
											</Col>
											<Col span={4}>
												<Checkbox disabled={getFieldValue(`effectMonth${key}${index}`).indexOf('11') < 0} value="11">
													十一月
												</Checkbox>
											</Col>
											<Col span={4}>
												<Checkbox disabled={getFieldValue(`effectMonth${key}${index}`).indexOf('12') < 0} value="12">
													十二月
												</Checkbox>
											</Col>
										</Row>
									</Checkbox.Group>
								)}
							</FormItem>
						</Col>
					</Col>
					<Col span={2}>{index === keys.length - 1 && index !== 0 ? <Icon className="dynamic-delete-button" type="minus-circle-o" onClick={() => this.remove(key, index)} /> : null}</Col>
				</Fragment>
			);
		});
	};

	// 渲染非阶梯配置
	_renderUnLadderConfig = () => {
		const { form } = this.props;
		const { getFieldDecorator, getFieldValue, setFieldsValue } = form;
		getFieldDecorator('keys', { initialValue: [] });
		const keys = getFieldValue('keys');
		let last = 0; // 上一次的阶梯 key 值
		let ids = 0; // 上一次的阶梯 key 值对应的 index 值
		return keys.map((key, index) => {
			if (key !== last) {
				last = key;
				ids = index;
			}
			return (
				<Fragment key={index}>
					<Col span={18} offset={3}>
						<Divider dashed orientation="left">
							非阶梯配置
						</Divider>
						<Col span={12}>
							<FormItem label="清水价（元）：">
								{
									getFieldDecorator(`cleanWaterFee${key}${index}`, {
										initialValue: '',
										rules: [
											{
												required: true,
												message: '清水价必填'
											},
											{
												validator: (rule, value, callback) => {
													if (/\s+/.test(value)) {
														setFieldsValue({
															cleanWaterFee: value.replace(/\s+/, '')
														});
														callback();
													} else {
														if (/^\d+\.?\d{0,2}$/.test(value)) {
															callback();
														} else {
															setFieldsValue({
																cleanWaterFee: `${value}`.substr(0, `${value}`.length - 1)
															});
															callback();
														}
													}
												}
											}
										]
									})(<Input placeholder="请输入清水价" suffix="元" />)
								}
							</FormItem>
						</Col>
						<Col span={12}>
							<FormItem label="污水价（元）：">
								{
									getFieldDecorator(`sewageFee${key}${index}`, {
										initialValue: '',
										rules: [
											{
												required: true,
												message: '污水价必填'
											},
											{
												validator: (rule, value, callback) => {
													if (/\s+/.test(value)) {
														setFieldsValue({
															sewageFee: value.replace(/\s+/, '')
														});
														callback();
													} else {
														if (/^\d+\.?\d{0,2}$/.test(value)) {
															callback();
														} else {
															setFieldsValue({
																sewageFee: `${value}`.substr(0, `${value}`.length - 1)
															});
															callback();
														}
													}
												}
											}
										]
									})(<Input placeholder="请输入污水价" suffix="元" />)
								}
							</FormItem>
						</Col>
						<Col span={12}>
							<FormItem label="水资源税（元）：">
								{
									getFieldDecorator(`waterResourceFee${key}${index}`, {
										initialValue: '',
										rules: [
											{
												required: true,
												message: '水资源税必填'
											},
											{
												validator: (rule, value, callback) => {
													if (/\s+/.test(value)) {
														setFieldsValue({
															waterResourceFee: value.replace(/\s+/, '')
														});
														callback();
													} else {
														if (/^\d+\.?\d{0,2}$/.test(value)) {
															callback();
														} else {
															setFieldsValue({
																waterResourceFee: `${value}`.substr(0, `${value}`.length - 1)
															});
															callback();
														}
													}
												}
											}
										]
									})(<Input placeholder="请输入水资源税" suffix="元" />)
								}
							</FormItem>
						</Col>
						<Col span={12}>
							<FormItem label="其他（元）：">
								{
									getFieldDecorator(`otherFee${key}${index}`, {
										initialValue: '',
										rules: [
											{
												required: true,
												message: '其他费用必填'
											},
											{
												validator: (rule, value, callback) => {
													if (/\s+/.test(value)) {
														setFieldsValue({
															otherFee: value.replace(/\s+/, '')
														});
														callback();
													} else {
														if (/^\d+\.?\d{0,2}$/.test(value)) {
															callback();
														} else {
															setFieldsValue({
																otherFee: `${value}`.substr(0, `${value}`.length - 1)
															});
															callback();
														}
													}
												}
											}
										]
									})(<Input placeholder="请输入其他费用" suffix="元" />)
								}
							</FormItem>
						</Col>
						{/* 执行月份 - span(24) */}
						<Col span={24}>
							<FormItem label="执行月份：" {...formItemWithLadder}>
								{getFieldDecorator(`effectMonth${key}${index}`, {
									initialValue: temp[`${key}${index}`] || '',
									rules: [
										{
											required: true,
											message: ' '
										}
									]
								})(
									<Checkbox.Group style={{ width: '100%' }} onChange={values => this.handleCheckBox(key, index, values)}>
										<Row>
											<Col span={4}>
												<Checkbox disabled={getFieldValue(`effectMonth${key}${index}`).indexOf('01') < 0} value="01">
													一月
												</Checkbox>
											</Col>
											<Col span={4}>
												<Checkbox disabled={getFieldValue(`effectMonth${key}${index}`).indexOf('02') < 0} value="02">
													二月
												</Checkbox>
											</Col>
											<Col span={4}>
												<Checkbox disabled={getFieldValue(`effectMonth${key}${index}`).indexOf('03') < 0} value="03">
													三月
												</Checkbox>
											</Col>
											<Col span={4}>
												<Checkbox disabled={getFieldValue(`effectMonth${key}${index}`).indexOf('04') < 0} value="04">
													四月
												</Checkbox>
											</Col>
											<Col span={4}>
												<Checkbox disabled={getFieldValue(`effectMonth${key}${index}`).indexOf('05') < 0} value="05">
													五月
												</Checkbox>
											</Col>
											<Col span={4}>
												<Checkbox disabled={getFieldValue(`effectMonth${key}${index}`).indexOf('06') < 0} value="06">
													六月
												</Checkbox>
											</Col>
											<Col span={4}>
												<Checkbox disabled={getFieldValue(`effectMonth${key}${index}`).indexOf('07') < 0} value="07">
													七月
												</Checkbox>
											</Col>
											<Col span={4}>
												<Checkbox disabled={getFieldValue(`effectMonth${key}${index}`).indexOf('08') < 0} value="08">
													八月
												</Checkbox>
											</Col>
											<Col span={4}>
												<Checkbox disabled={getFieldValue(`effectMonth${key}${index}`).indexOf('09') < 0} value="09">
													九月
												</Checkbox>
											</Col>
											<Col span={4}>
												<Checkbox disabled={getFieldValue(`effectMonth${key}${index}`).indexOf('10') < 0} value="10">
													十月
												</Checkbox>
											</Col>
											<Col span={4}>
												<Checkbox disabled={getFieldValue(`effectMonth${key}${index}`).indexOf('11') < 0} value="11">
													十一月
												</Checkbox>
											</Col>
											<Col span={4}>
												<Checkbox disabled={getFieldValue(`effectMonth${key}${index}`).indexOf('12') < 0} value="12">
													十二月
												</Checkbox>
											</Col>
										</Row>
									</Checkbox.Group>
								)}
							</FormItem>
						</Col>
					</Col>
					<Col span={2}>{index === keys.length - 1 && index !== 0 ? <Icon className="dynamic-delete-button" type="minus-circle-o" onClick={() => this.remove(key, index)} /> : null}</Col>
				</Fragment>
			);
		});
	};

	render() {
		const { ladderType } = this.state;
		const { form, detail } = this.props;
		const types = sessionStorage.getItem('_waterUseKindOpType');
		const { getFieldDecorator, setFieldsValue } = form;
		return (
			<div className="shadow-radius waterQualityAdd">
				{/* 页面标题 */}
				<Row>
					<Col span={22}>
						<h1>{types === 'add' ? '新增用水分类' : '用水分类详情'}</h1>
					</Col>
					<Col span={2}>
						<Button type="default" onClick={this.handleCancel}>
							返回
						</Button>
					</Col>
				</Row>
				{/* 页面主体 */}
				<Row className="main">
					{/*{this._renderForm()}*/}
					<Form {...formItemLayout} onSubmit={this.handleSubmit} refs="form">
						{/* 用水性质 */}
						<Col span={12} offset={6}>
							<FormItem label="用水性质：">
								{types === 'view'
									? detail && detail.type
									: getFieldDecorator('type', {
										rules: [
											{
												required: true,
												message: '用水性质必填'
											}
										]
									})(
										<Select placeholder="请选择用水性质">
											{constants.waterQualityMap.map((item, index) => {
												return (
													<Option key={index} value={item.value}>
														{item.label}
													</Option>
												);
											})}
										</Select>
									)}
							</FormItem>
						</Col>
						{/* 用水分类 */}
						<Col span={12} offset={6}>
							<FormItem label="用水分类：">
								{types === 'view'
									? detail && detail.name
									: getFieldDecorator('name', {
										initialValue: '',
										rules: [
											{
												required: true,
												message: '用水分类必填'
											}
										]
									})(<Input placeholder="请输入用水分类" />)}
							</FormItem>
						</Col>
						{/* 滞纳金比例 */}
						<Col span={12} offset={6}>
							<FormItem label="滞纳金比例（%）：">
								{types === 'view'
									? detail && detail.penaltyProportion
									: getFieldDecorator('penaltyProportion', {
										initialValue: ''
									})(
										<Input
											placeholder="请输入滞纳金比例"
											onBlur={v => {
												let value = v.target.value;
												let attr = `${value}`;
												let last = `${value}`.split('');
												if (last[attr.length - 1].indexOf('.') > -1) {
													v.target.value = last.slice(0, -1).join('');
													setFieldsValue({
														penaltyProportion: last.slice(0, -1).join('')
													});
												}
											}}
											onChange={v => {
												let value = v.target.value;
												if (/\s+/.test(`${value}`)) {
													let val = `${value}`.replace(/\s+/, '');
													v.target.value = val;
												} else {
													if (/^((0|(0\.\d{0,2}))|(([1-9][0-9]?)|([1-9][0-9]?\.\d{0,2}))|100)$/.test(`${value}`)) {
														v.target.value = `${value}`;
													} else {
														v.target.value = `${value}`.substr(0, `${value}`.length - 1);
													}
												}
												setFieldsValue({
													penaltyProportion: v.target.value
												});
											}}
											suffix="%"
										/>
									)}
							</FormItem>
						</Col>
						{/* 减免吨数 */}
						<Col span={12} offset={6}>
							<FormItem label="减免吨数（m³）：">
								{types === 'view'
									? detail && detail.reliefAmount
									: getFieldDecorator('reliefAmount', {
										initialValue: '',
										rules: [
											{
												validator: (rule, value, callback) => {
													if (/\s+/.test(value)) {
														setFieldsValue({
															reliefAmount: value.replace(/\s+/, '')
														});
														callback();
													} else {
														if (/^\d+$/.test(value)) {
															callback();
														} else {
															setFieldsValue({
																reliefAmount: `${value}`.substr(0, `${value}`.length - 1)
															});
															callback();
														}
													}
												}
											}
										]
									})(<Input placeholder="请输入减免吨数" suffix="m³" />)}
							</FormItem>
						</Col>
						{/* 临界吨数 */}
						<Col span={12} offset={6}>
							<FormItem label="临界吨数（m³）：">
								{types === 'view'
									? detail && detail.criticalAmount
									: getFieldDecorator('criticalAmount', {
										initialValue: '',
										rules: [
											{
												validator: (rule, value, callback) => {
													if (/\s+/.test(value)) {
														setFieldsValue({
															criticalAmount: value.replace(/\s+/, '')
														});
														callback();
													} else {
														if (/^\d+$/.test(value)) {
															callback();
														} else {
															setFieldsValue({
																criticalAmount: `${value}`.substr(0, `${value}`.length - 1)
															});
															callback();
														}
													}
												}
											}
										]
									})(<Input placeholder="请输入临界吨数" suffix="m³" />)}
							</FormItem>
						</Col>
						{/* 阶梯类型 */}
						<Col span={12} offset={6}>
							<FormItem label="阶梯类型：">
								{types === 'view'
									? detail && detail.ladderType
									: getFieldDecorator('ladderType', {
										rules: [
											{
												required: true,
												message: '阶梯类型必选'
											}
										]
									})(
										<Select placeholder="请选择阶梯类型" onChange={this.ladderTypeChange}>
											{constants.ladderTypeMap.map((item, index) => {
												return (
													<Option key={index} value={item.value}>
														{item.label}
													</Option>
												);
											})}
										</Select>
									)}
							</FormItem>
						</Col>
						{types === 'view' && detail ? (
							<Fragment>
								<Col span={12} offset={6}>
									<FormItem label="创建时间：">{detail && detail.createTime}</FormItem>
								</Col>
								<Col span={12} offset={6}>
									<FormItem label="更新时间：">{detail && detail.updateTime}</FormItem>
								</Col>
							</Fragment>
						) : null}
						{/* 非阶梯 */}
						{types === 'add' && ladderType === 0 && this._renderUnLadderConfig()}
						{/* 非阶梯详情渲染 */}
						{types === 'view' && detail && detail.ladderType === '非阶梯' ? this._renderUnLadderConfigList(detail.waterUseKindDetailList) : null}
						{/* 月阶梯 、 年阶梯 */}
						{types === 'add' && ladderType !== 0 && this._renderLadderConfig()}
						{types === 'view' && detail && (detail.ladderType === '月阶梯' || detail.ladderType === '年阶梯') ? this._renderLadderConfigList(detail.waterUseKindDetailList) : null}
						{/* 月阶梯 、 年阶梯 新增阶梯按钮 */}
						{ladderType && ladderType !== 0 ? (
							<Fragment>
								<Col span={12} offset={6} align="center">
									<FormItem {...formItemLayoutWithOutLabel}>
										<Button type="dashed" onClick={this.ladderConfig} style={{ width: '60%' }}>
											<Icon type="plus" /> 新增阶梯配置
										</Button>
									</FormItem>
								</Col>
							</Fragment>
						) : null}
						<Col span={24} align="center">
							{types !== 'view' ? (
								<Button type="primary" className="btn20" onClick={this.handleSubmit}>
									提交
								</Button>
							) : null}
							<Button type="default" className="btn20" onClick={this.handleCancel}>
								{types !== 'view' ? '取消' : '返回'}
							</Button>
						</Col>
					</Form>
				</Row>
			</div>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('waterQuality');
	return {
		fields: data.fields,
		detail: data.detail // 用水性质详情
	};
};
const mapDispatchToProps = dispatch => ({
	setState: data => dispatch(actionCreators.setState(data)), // 设置redux值
	add: (data, props) => dispatch(actionCreators.add(data, props)), // 新增记录
	modify: (data, props) => dispatch(actionCreators.modify(data, props)), // 修改记录
	getDetail: data => dispatch(actionCreators.detail(data)) // 获取详情
});

export default connect(
	mapStateToProps,
	mapDispatchToProps
)(Form.create()(withRouter(Index)));
