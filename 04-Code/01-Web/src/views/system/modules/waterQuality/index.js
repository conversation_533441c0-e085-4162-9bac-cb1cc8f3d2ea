import React, { Component } from 'react';
import { withRouter } from 'react-router-dom';
import { actionCreators } from "./store";
import { connect } from "react-redux";
import { Button, Col, DatePicker, Form, Input, Row, Select, Table, Modal } from "antd";
import { constants } from '$utils';
import { WATER_USE_KIND_TYPE } from '@/constants/waterUseKind';

const FormItem = Form.Item;
const { Option } = Select;
const { RangePicker } = DatePicker;
const defaultSearchForm = {
	type: null,
	name: '',
	status: null,
};

class Index extends Component {
	constructor(props) {
		super(props);
		this.state = {
			page: 1,
			pageSize: 10,
			searchForm: defaultSearchForm,
		};
	}

	componentDidMount() {
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields && isTags) {
			this.setState({
				...fields.state
			}, () => {
				this.props.form.setFieldsValue({
					...fields.form
				})
			})
		} else {
			this.getList();
		}
	}

	componentWillUnmount() {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			})
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			})
		}
	}

	// 获取列表
	getList = () => {
		const { page, pageSize, searchForm } = this.state;
		let params = {
			page,
			pageSize,
			...searchForm
		};
		this.props.list(params);
	};

	// 搜索功能
	handleSearch = () => {
		this.setState(
			{
				page: 1
			},
			() => {
				this.getList()
			}
		)
	}

	// 重置搜索
	handleReset = () => {
		this.setState(
			{
				page: 1,
				searchForm: defaultSearchForm
			},
			() => {
				this.getList()
			}
		)
	}

	// 列表分页
	handlePageChange = (page) => {
		this.setState({
			page
		}, () => {
			this.getList();
		})
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState({
			page: 1,
			pageSize: size,
		}, () => {
			this.getList();
		})
	};

	// 查看详情
	handleView = record => {
		sessionStorage.setItem('_waterUseKindOpType', 'view');
		sessionStorage.setItem('_waterUseKindId', record.id)
		this.props.history.push('/waterQuality/view');
	};

	// 编辑记录
	handleEdit = record => {
		sessionStorage.setItem('_waterUseKindOpType', 'edit');
		sessionStorage.setItem('_waterUseKindId', record.id)
		this.props.history.push('/waterQuality/edit');
	};

	// 删除记录
	handleDel = record => {
		Modal.confirm({
			title: '删除确认',
			content: '是否确认删除该记录？',
			okText: '确认',
			cancelText: '取消',
			onOk: () => {
				this.props.del(record.id, this.getList)
			}
		});
	};

	handleAdd = () => {
		sessionStorage.setItem('_waterUseKindOpType', 'add')
		this.props.history.push('/waterQuality/add');
	};

	// 渲染搜索
	_renderSearchForm = () => {
		const { searchForm } = this.state;
		const { getFieldDecorator } = this.props.form;
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					<Col span={12}>
						<FormItem label={'用水分类:'}>
							<Select
								placeholder="请选择"
								value={searchForm.type}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, type: v }
									});
								}}
							>
								{WATER_USE_KIND_TYPE.map((item, index) => {
									return (
										<Option key={index} value={item.value}>
											{item.label}
										</Option>
									);
								})}
							</Select>
						</FormItem>
					</Col>
					<Col span={12}>
						<FormItem label={'用水性质名称:'}>
							<Input
								placeholder="请输入用水性质名称"
								value={searchForm.name}
								onChange={v => {
									this.setState({
										searchForm: { ...searchForm, name: v.target.value }
									});
								}}
							/>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={16} offset={8} align="right">
						<FormItem>
							<Button type="primary" onClick={this.handleSearch}>
								搜索
							</Button>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<Button type="default" onClick={this.handleReset}>
								重置
							</Button>
						</FormItem>
					</Col>
				</Row>
			</Form>
		)
	};

	_renderOperateButton = () => {
		return (
			<Row>
				<Col>
					<Button permission={React.$pmn('REVENUE:SYSTEM_SETTINGS:WATER_CLASSIFICATION:ADD')} type='primary' onClick={this.handleAdd}>新增</Button>
				</Col>
			</Row>
		)
	};

	render() {
		const { page, pageSize } = this.state;
		const { datalist, total } = this.props;
		const columns = [
			{
				title: '用水分类',
				dataIndex: 'type',
				key: 'type',
				align: 'center',
			},
			{
				title: '用水性质',
				dataIndex: 'name',
				key: 'name',
				align: 'center',
			},
			{
				title: '阶梯',
				dataIndex: 'ladderType',
				key: 'ladderType',
				align: 'center',
			},
			{
				title: '状态',
				dataIndex: 'status',
				key: 'status',
				align: 'center',
			},
			{
				title: '更新人',
				dataIndex: 'updatePersonName',
				key: 'updatePersonName',
				align: 'center',
			},
			{
				title: '更新时间',
				dataIndex: 'updateTime',
				key: 'updateTime',
				align: 'center',
				width: 100,
			},
			{
				title: '操作',
				key: 'operation',
				width: 200,
				align: 'center',
				render: (text, record, index) => {
					return (
						<span>
							<Button permission={React.$pmn('REVENUE:SYSTEM_SETTINGS:WATER_CLASSIFICATION:VIEW')} title='查看' className='btn' type="primary" size="small" icon="eye" onClick={() => this.handleView(record)} />
							<Button permission={React.$pmn('REVENUE:SYSTEM_SETTINGS:WATER_CLASSIFICATION:EDIT')} title='编辑' className='btn' type="primary" size="small" icon="form" onClick={() => this.handleEdit(record)} />
							<Button permission={React.$pmn('REVENUE:SYSTEM_SETTINGS:WATER_CLASSIFICATION:DEL')} title='删除' className='btn' type="primary" size="small" icon="delete" onClick={() => this.handleDel(record)} />
							&nbsp;
						</span>
					)
				},
			}
		];
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: (total) => { return `共 ${total} 条数据 ` },
			onChange: (page) => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		return (
			<div className="shadow-radius">
				{/* 页面标题 */}
				<Row>
					<Col><h1>用水性质</h1></Col>
				</Row>
				<Row>
					{this._renderSearchForm()}
				</Row>
				{this._renderOperateButton()}
				<Row className='main'>
					<Table
						bordered
						columns={columns}
						rowKey={() => Math.random()}
						dataSource={datalist}
						pagination={paginationProps}
					/>
				</Row>
			</div>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('waterQuality');
	return {
		fields: data.fields,
		datalist: data.datalist, // 列表数据
		total: data.total, // 总条数
		visible: data.visible, // 是否显示弹窗
		detail: data.detail, // 获取详情
	}
};
const mapDispatchToProps = dispatch => ({
	setState: data => dispatch(actionCreators.setState(data)), // 设置redux值
	list: data => dispatch(actionCreators.list(data)), // 获取列表
	getDetail: data => dispatch(actionCreators.detail(data)), // 获取详情
	add: data => dispatch(actionCreators.add(data)), // 新增记录
	del: (data, list) => dispatch(actionCreators.del(data, list)), // 删除记录
	modify: data => dispatch(actionCreators.modify(data)), // 修改记录
});
export default connect(
	mapStateToProps,
	mapDispatchToProps
)((Form.create())(withRouter(Index)));
