import http from '$http';
import * as actionTypes from './constants';
import { message } from 'antd';
import api from './api';

// 数据回填
const payload = (type, data) => ({type, data});

// 组件操作redux数据状态
const setState = data => ({type: actionTypes.SET_STATE, data});

// 获取列表
const list = data => {
	return async dispatch => {
		const res = await http.post(api.list, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.LIST_RECORD, res.data))
		}
	}
};

// 获取详情
const detail = data => {
	return async dispatch => {
		const res = await http.restGet(api.detail, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.DETAIL_RECORD, res.data))
		}
	}
};

// 新增
const add = (data, props) => {
	return async dispatch => {
		const res = await http.post(api.add, data);
		if (res.code === 0) {
			message.success('新增成功');
			props.push('/sysSettings/waterQuality');
			sessionStorage.removeItem('types');
		}
	}
};

// 删除
const del = (data, list) => {
	return async dispatch => {
		const res = await http.del(api.del, data);
		if (res.code === 0) {
			message.success('删除成功');
			list();
		}
	}
};

// 修改
const modify = (data, props) => {
	return async dispatch => {
		const res = await http.post(api.modify, data);
		if (res.code === 0) {
			message.success('修改成功');
			props.push('/sysSettings/waterQuality');
			sessionStorage.removeItem('types');
			sessionStorage.removeItem('_id');
		}
	}
};

export { setState, list, detail, add, modify, del };
