import React, { Component, Fragment } from 'react';
import { connect } from 'react-redux';
import { actionCreators } from './store';
import { Button, Col, DatePicker, Form, Input, Row, Select, Table, Modal, TreeSelect, Timeline, Menu, Icon, Transfer, message, Tree } from 'antd';
import { constants } from '$utils';
import './index.scss';

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;
const { SubMenu } = Menu;
const { TreeNode } = Tree;

const defaultSearchForm = {
	roleId: null
};

class RoleGroupForm extends React.Component {
	render() {
		const { handleSubmitGroup, handleCancelGroup, form } = this.props;
		const { getFieldDecorator } = form;
		return (
			<Form {...constants.formItemLayout}>
				<Row>
					<Col span={24}>
						<FormItem label="名称：">
							{getFieldDecorator('name', {
								initialValue: '',
								rules: [
									{
										required: true,
										message: '请输入名称'
									}
								]
							})(<Input palceholder="请输入名称" />)}
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={24} align="center">
						<Button type="primary" className="btn" onClick={handleSubmitGroup}>
							提交
						</Button>
						<Button type="default" className="btn" onClick={handleCancelGroup}>
							取消
						</Button>
					</Col>
				</Row>
			</Form>
		);
	}
}
const RoleGroup = Form.create()(RoleGroupForm);

class RoleForm extends React.Component {
	render() {
		const { handleSubmitRole, handleCancelRole, form, groupList } = this.props;
		const { getFieldDecorator } = form;
		return (
			<Form {...constants.formItemLayout}>
				<Row>
					<Col span={24}>
						<FormItem label="名称：">
							{getFieldDecorator('name', {
								initialValue: '',
								rules: [
									{
										required: true,
										message: '请输入名称'
									}
								]
							})(<Input palceholder="请输入名称" />)}
						</FormItem>
					</Col>
					<Col span={24}>
						<FormItem label="分组到：">
							{getFieldDecorator('groupId', {
								initialValue: null,
								rules: [
									{
										required: true,
										message: '请选择'
									}
								]
							})(
								<Select placeholder="请选择">
									{groupList.map((item, index) => {
										return (
											<Option key={index} value={item.value}>
												{item.label}
											</Option>
										);
									})}
								</Select>
							)}
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span={24} align="center">
						<Button type="primary" className="btn" onClick={handleSubmitRole}>
							提交
						</Button>
						<Button type="default" className="btn" onClick={handleCancelRole}>
							取消
						</Button>
					</Col>
				</Row>
			</Form>
		);
	}
}
const Role = Form.create()(RoleForm);

class Index extends Component {

	constructor(props) {
		super(props);
		this.state = {
			page: constants.page,
			pageSize: constants.pageSize,
			searchForm: defaultSearchForm,
			columns: [
				{
					title: '姓名',
					dataIndex: 'personName',
					key: 'personName',
					align: 'center'
				},
				{
					title: '部门',
					dataIndex: 'departmentName',
					key: 'departmentName',
					align: 'center'
				},
				{
					title: '职务',
					dataIndex: 'duty',
					key: 'createTime',
					align: 'duty'
				},
				{
					title: '管理范围',
					width: 120,
					fixed: 'right',
					align: 'center',
					render: (text, record, index) => {
						return (
							<span>
								<Button type="link" onClick={() => this.handleAddDepartment(record)}>
									设置
								</Button>
							</span>
						);
					}
				}
			],
			selectedIdList: [],
			selectedRoleId: null,
			addUserIdList: [],
			selectedId: []
		};
	}

	componentDidMount() {
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields && isTags) {
			this.setState({
				...fields.state
			}, () => {
				this.props.form.setFieldsValue({
					...fields.form
				})
			})
		} else {
			this.getList();
		}
	}

	componentWillUnmount() {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			})
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			})
		}
	}

	// 获取列表
	getList = () => {
		this.props.listGroup();
	};

	getUserList = () => {
		const { page, pageSize, searchForm } = this.state;
		searchForm.roleId = this.state.selectedRoleId;
		this.props.listUser(Object.assign({ ...searchForm }, { page, pageSize }));
	};

	// 列表分页
	handlePageChange = page => {
		this.setState(
			{
				page
			},
			() => {
				this.getUserList();
			}
		);
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState(
			{
				page: 1,
				pageSize: size
			},
			() => {
				this.getUserList();
			}
		);
	};

	// -----------------
	handleClick = e => {
		this.setState(
			{
				selectedRoleId: parseInt(e.key)
			},
			() => {
				this.props.getRole(this.state.selectedRoleId);
				this.getUserList();
			}
		);
	};

	//--------- 角色组 -------------
	handleAddGroup = () => {
		this.props.setState([{ key: 'groupModalVisible', value: true }]);
	};

	handleCancelGroup = () => {
		this.props.setState([{ key: 'groupModalVisible', value: false }]);
	};

	handleSubmitGroup = e => {
		e.preventDefault();
		this.roleGroupForm.validateFields((err, values) => {
			if (!err) {
				let params = Object.assign({ ...values });
				this.props.addGroup(params, this.getList);
			}
		});
		this.props.setState([{ key: 'groupModalVisible', value: false }]);
	};

	//--------- 角色 -------------
	handleAddRole = () => {
		this.props.getGroupSelect();
		this.props.setState([{ key: 'roleModalVisible', value: true }]);
	};
	handleCancelRole = () => {
		this.props.setState([{ key: 'roleModalVisible', value: false }]);
	};
	handleSubmitRole = e => {
		e.preventDefault();
		this.roleForm.validateFields((err, values) => {
			if (!err) {
				let params = Object.assign({ ...values });
				this.props.addRole(params, this.getList);
			}
		});
		this.props.setState([{ key: 'roleModalVisible', value: false }]);
	};
	handleEditRole = () => {
		if (!!!this.state.selectedRoleId) {
			message.error('请选择角色');
			return;
		}
		this.props.getRole(this.state.selectedRoleId);
		this.props.setState([{ key: 'roleEditModalVisible', value: true }]);
	};
	// 删除角色
	handleDeleteRole = () => {
		if (!!!this.state.selectedRoleId) {
			message.error('请选择角色');
			return;
		}
		const _this = this
		Modal.confirm({
			title: '提示',
			content: `确定删除该角色吗？`,
			okText: '确认',
			cancelText: '取消',
			onOk: () => {
				this.setState({ selectedRoleId: null })
				this.props.deleteRole(this.state.selectedRoleId, _this.getList)
			}
		});
	};
	handleCancelRoleEdit = () => {
		this.props.setState([{ key: 'roleEditModalVisible', value: false }]);
	};
	handleSubmitRoleEdit = e => {
		e.preventDefault();
		this.props.form.validateFields((err, values) => {
			if (!err) {
				let params = Object.assign({ ...values }, { id: this.state.selectedRoleId });
				this.props.modifyRole(params, () => {
					this.getList();
					this.getUserList();
					this.props.role.name = params.name;
				});
			}
		});
		this.props.form.resetFields();
		this.props.setState([{ key: 'roleEditModalVisible', value: false }]);
	};
	//--------- 人员 -------------
	handleAddUser = () => {
		if (!!!this.state.selectedRoleId) {
			message.error('请选择角色');
			return;
		}
		this.props.getUserSelect(this.state.selectedRoleId);
		this.props.setState([{ key: 'userModalVisible', value: true }]);
	};
	handleCancelUser = () => {
		this.props.setState([{ key: 'userModalVisible', value: false }]);
	};
	handleSubmitUser = () => {
		if (!!!this.state.selectedRoleId) {
			message.error('请选择角色');
			this.props.setState([{ key: 'userModalVisible', value: false }]);
			return;
		}
		const params = {
			roleId: this.state.selectedRoleId,
			userIdList: this.state.addUserIdList
		};
		this.props.addUser(params, this.getUserList);
		this.props.setState([{ key: 'userModalVisible', value: false }, { key: 'addUserIdList', value: [] }]);
	};
	handleDeleteUser = () => {
		const params = {
			idList: this.state.selectedIdList
		};
		this.props.deleteUser(params, this.getUserList);
	};

	//--------设置部门-------------
	handleAddDepartment = record => {
		this.setState({ selectedId: record.id });
		this.props.getDepartmentSelect();
		const params = {
			roleId: this.state.selectedRoleId,
			userId: record.userId
		};
		this.props.listDepartment(params);
		this.props.setState([{ key: 'departmentModalVisible', value: true }]);
	};
	onSelect = (selectedKeys, info) => {
		// console.log('selected', selectedKeys);
	};
	onCheck = (selectedDepartmentIdList, info) => {
		this.props.setState([{ key: 'selectedDepartmentIdList', value: selectedDepartmentIdList }]);
	};
	handleSubmitDepartment = () => {
		const { selectedDepartmentIdList } = this.props;
		let params = {
			id: this.state.selectedId,
			departmentIdList: selectedDepartmentIdList
		};
		this.props.addDepartment(params, this.getUserList);
		this.props.setState([{ key: 'departmentModalVisible', value: false }]);
	};
	handleCancelDepartment = () => {
		this.props.setState([{ key: 'departmentModalVisible', value: false }]);
	};
	// 渲染左侧角色列表
	_renderRoleGroupList = () => {
		const { roleGroupList } = this.props;
		let openKeys = [];
		roleGroupList.map(g => {
			openKeys.push(g.id.toString());
		});
		return (
			<Menu onClick={this.handleClick} openKeys={openKeys} mode="inline">
				{roleGroupList.map(group => {
					let roleList = group.sysAuditRoleList || [];
					return (
						<SubMenu
							key={group.id}
							title={
								<span>
									<Icon type="mail" />
									<span>{group.name}</span>
									{/* <Button type="normal" size="small" style={{ marginLeft: 10 }} onClick={this.handleEditGroup}>
										编辑
									</Button>
									<Button type="danger" size="small" style={{ marginLeft: 10 }}>
										删除
									</Button> */}
								</span>
							}
						>
							{roleList.map(role => {
								return (
									<Menu.Item key={role.id}>
										<Icon type="user" />
										{role.name}
									</Menu.Item>
								);
							})}
						</SubMenu>
					);
				})}
			</Menu>
		);
	};
	// 角色组弹出框
	_renderGroupModal = () => {
		const { groupModalVisible } = this.props;
		return (
			<Modal className="audit-role-modal" title={'新增角色组'} visible={groupModalVisible} onCancel={this.handleCancelGroup} footer={null}>
				<RoleGroup
					ref={form => {
						this.roleGroupForm = form;
					}}
					handleSubmitGroup={this.handleSubmitGroup}
					handleCancelGroup={this.handleCancelGroup}
				/>
			</Modal>
		);
	};
	// 角色弹出框
	_renderRoleModal = () => {
		const { roleModalVisible, groupList } = this.props;
		return (
			<Modal className="audit-role-modal" title={'新增角色'} visible={roleModalVisible} onCancel={this.handleCancelRole} footer={null}>
				<Role
					ref={form => {
						this.roleForm = form;
					}}
					handleSubmitRole={this.handleSubmitRole}
					handleCancelRole={this.handleCancelRole}
					groupList={groupList}
				/>
			</Modal>
		);
	};
	_renderRoleEditModal = () => {
		const { role, roleEditModalVisible } = this.props;
		const { getFieldDecorator } = this.props.form;
		return (
			<Modal className="audit-role-modal" title={'修改角色名称'} visible={roleEditModalVisible} onCancel={this.handleCancelRoleEdit} footer={null}>
				<Form {...constants.formItemLayout}>
					<Row>
						<Col span={24}>
							<FormItem label="名称：">
								{getFieldDecorator('name', {
									initialValue: role.name,
									rules: [
										{
											required: true,
											message: '请输入名称'
										}
									]
								})(<Input palceholder="请输入名称" />)}
							</FormItem>
						</Col>
					</Row>
					<Row>
						<Col span={24} align="center">
							<Button type="primary" className="btn" onClick={this.handleSubmitRoleEdit}>
								提交
							</Button>
							<Button type="default" className="btn" onClick={this.handleCancelRoleEdit}>
								取消
							</Button>
						</Col>
					</Row>
				</Form>
			</Modal>
		);
	};
	handleChange = addUserIdList => {
		this.setState({ addUserIdList });
	};
	handleSearch = (dir, value) => {
		console.log('search:', dir, value);
	};
	// 人员选择弹出框
	_renderUserModal = () => {
		const { userModalVisible, userList } = this.props;
		let userSelectList = [];
		for (let i in userList) {
			userSelectList.push({
				key: userList[i].value,
				title: userList[i].label
			});
		}
		return (
			<Modal className="audit-role-modal" title={'选择人员'} visible={userModalVisible} onCancel={this.handleCancelUser} footer={null}>
				<Transfer
					dataSource={userSelectList}
					showSearch
					listStyle={{
						width: '48%',
						height: 600
					}}
					locale={{
						itemUnit: '人',
						itemsUnit: '人',
						searchPlaceholder: '请输入搜索内容'
					}}
					titles={['待选', '已选']}
					filterOption={(inputValue, option) => option.title.indexOf(inputValue) > -1}
					targetKeys={this.state.addUserIdList}
					onChange={this.handleChange}
					onSearch={this.handleSearch}
					render={item => item.title}
				/>
				<Row style={{ marginTop: 20 }}>
					<Col span={24} align="center">
						<Button type="primary" className="btn" onClick={this.handleSubmitUser}>
							提交
						</Button>
						<Button type="default" className="btn" onClick={this.handleCancelUser}>
							取消
						</Button>
					</Col>
				</Row>
			</Modal>
		);
	};
	//递归渲染
	_renderTreeNode = departmentTreeData => {
		return departmentTreeData.map(department => {
			if (department.children) {
				return (
					<TreeNode key={department.id} title={department.label}>
						{this._renderTreeNode(department.children)}
					</TreeNode>
				);
			} else {
				return <TreeNode key={department.key} title={department.label} />;
			}
		});
	};
	_renderDepartmentModal = () => {
		const { departmentTreeData, departmentModalVisible, selectedDepartmentIdList } = this.props;
		return (
			<Modal className="audit-role-modal" title={'选择管理部门'} visible={departmentModalVisible} onCancel={this.handleCancelDepartment} footer={null}>
				<Tree checkable onSelect={this.onSelect} onCheck={this.onCheck} checkedKeys={selectedDepartmentIdList}>
					{this._renderTreeNode(departmentTreeData)}
				</Tree>
				<Row style={{ marginTop: 20 }}>
					<Col span={24} align="center">
						<Button type="primary" className="btn" onClick={this.handleSubmitDepartment}>
							提交
						</Button>
						<Button type="default" className="btn" onClick={this.handleCancelDepartment}>
							取消
						</Button>
					</Col>
				</Row>
			</Modal>
		);
	};

	handleEditGroup = e => {
		e.preventDefault();
		console.log(e);
	};

	// 选中事件
	onSelectChange = selectedIdList => {
		this.setState({ selectedIdList });
	};

	render() {
		const { page, pageSize, columns, selectedIdList } = this.state;
		const { dataList, total, role } = this.props;
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: total => {
				return `共 ${total} 条数据 `;
			},
			onChange: page => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		const rowSelection = {
			fixed: true,
			selectedIdList,
			onChange: this.onSelectChange
		};
		return (
			<div className="shadow-radius audit-role">
				<Row>
					<Col span={6}>
						<Row>
							<Button type="normal" style={{ marginRight: 10 }} onClick={this.handleAddGroup}>
								新增角色组
							</Button>
							<Button type="normal" onClick={this.handleAddRole}>
								新增角色
							</Button>
						</Row>
						{this._renderRoleGroupList()}
					</Col>
					<Col span={18}>
						<Row style={{ marginBottom: 20, fontSize: 25 }}>
							<Icon type="user" style={{ marginRight: 5 }} />
							{role.name}({total}人)
							<Button type="normal" size="small" style={{ marginLeft: 10 }} onClick={this.handleEditRole}>
								编辑
							</Button>
							<Button type="danger" size="small" style={{ marginLeft: 10 }} onClick={this.handleDeleteRole}>
								删除
							</Button>
						</Row>
						<Row>
							<Button type="primary" style={{ marginRight: 10 }} onClick={this.handleDeleteUser}>
								批量移除
							</Button>
							<Button type="primary" onClick={this.handleAddUser}>
								添加成员
							</Button>
						</Row>
						<Row className="main">
							<Table rowSelection={rowSelection} bordered columns={columns} rowKey={data => data.id} dataSource={dataList} pagination={paginationProps} />
						</Row>
					</Col>
				</Row>
				{this._renderGroupModal()}
				{this._renderRoleModal()}
				{this._renderRoleEditModal()}
				{this._renderUserModal()}
				{this._renderDepartmentModal()}
			</div>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('auditRole');
	return {
		fields: data.fields,
		dataList: data.dataList, // 数据列表
		total: data.total,
		roleGroupList: data.roleGroupList,
		groupModalVisible: data.groupModalVisible,
		roleModalVisible: data.roleModalVisible,
		roleEditModalVisible: data.roleEditModalVisible,
		userModalVisible: data.userModalVisible,
		departmentModalVisible: data.departmentModalVisible,
		role: data.role,
		groupList: data.groupList,
		userList: data.userList,
		departmentTreeData: data.departmentTreeData,
		departmentList: data.departmentList,
		selectedDepartmentIdList: data.selectedDepartmentIdList
	};
};
const mapDispatchToProps = dispatch => ({
	setState: data => dispatch(actionCreators.setState(data)), // 设置redux值
	listGroup: () => dispatch(actionCreators.listGroup()), // 角色组列表
	addGroup: (data, list) => dispatch(actionCreators.addGroup(data, list)), // 新增角色组
	getGroupSelect: () => dispatch(actionCreators.getGroupSelect()), // 获取角色组选择框
	getRole: data => dispatch(actionCreators.getRole(data)), // 获取角色详情
	addRole: (data, list) => dispatch(actionCreators.addRole(data, list)), // 新增角色
	modifyRole: (data, list) => dispatch(actionCreators.modifyRole(data, list)), // 新增角色
	deleteRole: (data, list) => dispatch(actionCreators.deleteRole(data, list)), // 删除角色
	getUserSelect: data => dispatch(actionCreators.getUserSelect(data)), // 获取人员选择框
	listUser: data => dispatch(actionCreators.listUser(data)), // 获取人员列表
	addUser: (data, list) => dispatch(actionCreators.addUser(data, list)), // 添加人员
	deleteUser: (data, list) => dispatch(actionCreators.deleteUser(data, list)), // 批量删除人员
	getDepartmentSelect: () => dispatch(actionCreators.getDepartmentSelect()), // 获取部门选择框
	listDepartment: data => dispatch(actionCreators.listDepartment(data)), // 获取人员部门列表
	addDepartment: (data, list) => dispatch(actionCreators.addDepartment(data, list)) // 添加人员部门
});

export default connect(
	mapStateToProps,
	mapDispatchToProps
)(Form.create()(Index));
