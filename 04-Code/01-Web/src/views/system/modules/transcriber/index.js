import React, { Component, Fragment } from 'react';
import { actionCreators } from './store';
import { connect } from 'react-redux';
import { Modal, Table, Form, Row, Col, Input, Button, Tree } from 'antd';
import { constants } from '$utils';
import './index.scss';

const FormItem = Form.Item;
const { TreeNode } = Tree;

const defaultSearchForm = {
	departmentId: null,
	personName: ''
};

class Index extends Component {

	constructor(props) {
		super(props);
		this.state = {
			expand: false, // 搜索项过多的时候，隐藏部分
			page: 1,
			pageSize: 10,
			searchForm: defaultSearchForm,
			modalVisible: false,
			selectedId: null,
			selectedAreaIdList: []
		};
	}

	componentDidMount() {
		this.getList();
	}

	// 获取列表
	getList = () => {
		const { page, pageSize, searchForm } = this.state;
		this.props.list(Object.assign({ ...searchForm }, { page, pageSize }));
	};

	// 列表分页
	handlePageChange = (page) => {
		this.setState({ page }, () => {
			this.getList();
		});
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState({ page: 1, pageSize: size }, () => {
			this.getList();
		});
	};

	// 搜索功能
	handleSearch = () => {
		this.setState({ page: 1 }, () => {
			this.getList();
		});
	};

	// 重置搜索
	handleReset = () => {
		this.setState({ searchForm: Object.assign({}, defaultSearchForm) }, () => {
			this.getList();
		});
	};

	// 设置片区
	handleSetArea(record) {
		this.props.getUsedAreaIdList(record.id,(data)=>this.setState({selectedAreaIdList:data,modalVisible: true,selectedId: record.id}));
	};

	// 取消弹窗
	handleCancel = () => {
		this.setState({ modalVisible: false,selectedAreaIdList:[] });
	};

	// 提交信息
	handleSubmit = (e) => {
		const { selectedId, selectedAreaIdList } = this.state;
		e.preventDefault();
		let params = {
			transcriberId: selectedId,
			areaIdList: selectedAreaIdList
		};
		this.props.add(params);
		this.handleCancel();
	};

	// 获取当前选项
	onCheck = (selectedAreaIdList, info) => {
		this.setState({ selectedAreaIdList });
	};

	// 渲染搜索
	_renderSearchForm = () => {
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Row>
					{/* <Col span={12}>
						<FormItem label={'所属部门:'}>
							<Select
								placeholder="请选择"
								value={this.state.searchForm.departmentId}
								onChange={(v) => {
									this.setState({
										searchForm: { ...this.state.departmentId, status: v }
									})
								}}
							>
							</Select>
						</FormItem>
					</Col> */}
					<Col span={12}>
						<FormItem label={'抄表员:'}>
							<Input placeholder="请输入抄表员"
										 onChange={(v) => {
											 this.setState({ searchForm: { ...this.state.searchForm, personName: v.target.value } });
										 }}/>
						</FormItem>
					</Col>
				</Row>

				<Row>
					<Col span={24} align="right">
						<FormItem>
							<Button type="primary" onClick={this.handleSearch}>
								搜索
							</Button>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<Button type="default" onClick={this.handleReset}>
								重置
							</Button>
						</FormItem>
					</Col>
				</Row>
			</Form>
		);
	};

	//递归渲染
	_renderTreeNode = (treeData, usedAreaIdList) => {
		return treeData.map(tree => {
			if (tree.children) {
				return (
					<TreeNode key={tree.id} title={tree.name} value={tree.id}
									>
						{this._renderTreeNode(tree.children, usedAreaIdList)}
					</TreeNode>
				);
			} else {
				return <TreeNode key={tree.id} title={tree.name} value={tree.id}
												/>;
			}
		});
	};

	// 判断id是否已经被配置
	getAreaIdUsed = (list, id) => {
		return list && list.includes(id);
	};

	// 渲染弹出框
	_renderModal = () => {
		let { modalVisible, selectedAreaIdList } = this.state;
		const { areaSelect, usedAreaIdList } = this.props;
		return (
			<Modal className="transcriber-modal" title='设置抄表片区' visible={modalVisible} onCancel={this.handleCancel}
						 footer={null}>
				{
					areaSelect.length >= 0 && usedAreaIdList.length >= 0 && (
						<Tree autoExpandParent={false} checkable checkedKeys={selectedAreaIdList} onCheck={this.onCheck}>
							{this._renderTreeNode(areaSelect, usedAreaIdList)}
						</Tree>)
				}
				<Row>
					<Col span={24} align="center">
						<Button type="primary" className="btn" onClick={this.handleSubmit}>
							提交
						</Button>
						<Button type="default" className="btn" onClick={this.handleCancel}>
							取消
						</Button>
					</Col>
				</Row>
			</Modal>
		);
	};

	render() {
		const { page, pageSize } = this.state;
		const { dataList, total } = this.props;
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: (total) => {
				return `共 ${total} 条数据 `;
			},
			onChange: (page) => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		const columns = [
			{
				title: '抄表员',
				dataIndex: 'personName',
				align: 'center'
			},
			{
				title: '所属部门',
				dataIndex: 'departmentName',
				align: 'center'
			},
			{
				title: '更新时间',
				dataIndex: 'updateTime',
				align: 'center'
			},
			{
				title: '操作',
				key: 'operation',
				align: 'center',
				render: (text, record, index) => {
					return (
						<Button className='btn' type="link" size="small" icon="form"
										onClick={() => this.handleSetArea(record)}>设置抄表片区</Button>
					);
				}
			}
		];
		return (
			<div className="shadow-radius transcriber">
				<h1>抄表员管理</h1>
				<Row>{this._renderSearchForm()}</Row>
				<Row className="main">
					<Table bordered columns={columns} rowKey={() => Math.random()}
								 dataSource={dataList} pagination={paginationProps}/>
				</Row>
				{this._renderModal()}
			</div>
		);
	}
}

const mapStateToProps = (state) => {
	const data = state.get('transcriber');
	return {
		fields: data.fields,
		dataList: data.dataList, // 列表数据
		total: data.total, // 总条数
		visible: data.visible, // 是否显示弹窗
		detail: data.detail, // 获取详情
		areaSelect: data.areaSelect, // 片区选择框
		usedAreaIdList: data.usedAreaIdList, // 已配置的片区ID列表
		areaIdList: data.areaIdList // 抄表员已配置的片区ID列表
	};
};
const mapDispatchToProps = (dispatch) => ({
	setState: (data) => dispatch(actionCreators.setState(data)), // 设置redux值
	list: (data) => dispatch(actionCreators.list(data)), // 获取列表
	getAreaSelect: () => dispatch(actionCreators.getAreaSelect()), // 获取片区选择框
	add: (data) => dispatch(actionCreators.add(data)), // 保存抄表片区
	getUsedAreaIdList: (data,visible) => dispatch(actionCreators.getUsedAreaIdList(data,visible)),// 获取除抄表员外已配置的片区ID列表
	getAreaIdList: (data) => dispatch(actionCreators.getAreaIdList(data))// 获取抄表员已配置的片区ID列表
});
export default connect(mapStateToProps, mapDispatchToProps)(Form.create()(Index));
