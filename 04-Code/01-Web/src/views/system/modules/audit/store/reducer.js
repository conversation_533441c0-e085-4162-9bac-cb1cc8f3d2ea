import * as actionTypes from './constants';
import { dataType } from '$utils';

const defaultState = {
	fields: null,
	total: 0,
	dataList: [],
	visible: false,
	detailVisible: false,
	detail: null,
	auditRoleTree: []
};

export default (state = defaultState, action) => {
	switch (action.type) {
		case actionTypes.SET_STATE:
			let _data = action.data;
			if (dataType(_data) === 'Object') {
				state[_data.key] = _data.value;
			}
			if (dataType(_data) === 'Array') {
				_data.forEach(item => {
					state[item.key] = item.value;
				});
			}
			return { ...state };
		// 获取列表
		case actionTypes.LIST_RECORD:
			let data = action.data;
			state.total = data.total;
			state.dataList = data.rows;
			return { ...state };
		// 获取详情
		case actionTypes.DETAIL_RECORD:
			state.detail = action.data;
			return { ...state };
		// 修改记录
		case actionTypes.MODIFY_RECORD:
			state.visible = false;
			state.detail = null;
			return { ...state };
		// 新增记录
		case actionTypes.ADD_RECORD:
			state.visible = false;
			state.detail = null;
			return { ...state };
		// 删除记录
		case actionTypes.DEL_RECORD:
			return { ...state };
		// 获取审批角色选择框
		case actionTypes.GET_AUDIT_ROLE_SELECT:
			state.auditRoleTree = action.data;
			return { ...state };
		default:
			return state;
	}
};
