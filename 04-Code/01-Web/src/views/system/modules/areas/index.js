import React, { Component } from 'react';
import { connect } from 'react-redux';
import { actionCreators } from './store';
import { Modal, Button, Select, Dropdown, Menu, Col, Form, Input, Row, Table, Icon } from 'antd';
import './index.scss'

const FormItem = Form.Item;
const { Option } = Select;
let isBeing = true;


class Index extends Component {
	constructor(props) {
		super(props);
		this.state = {
			title: '新增片区',
			searchName: '', // 搜索内容
			parentId: null, // 父级id
		};
	}

	componentDidMount() {
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields && isTags) {
			this.setState({
				...fields.state
			}, () => {
				this.props.form.setFieldsValue({
					...fields.form
				})
			})
		} else {
			this.props.list();
		}
	}

	componentWillUnmount() {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			})
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			})
		}
	}

	componentWillReceiveProps(nextProps, nextContext) {
		if (nextProps.detail && isBeing) {
			isBeing = false;
			const detail = nextProps.detail;
			this.props.form.setFieldsValue({
				contact: detail.contact,
				contactEmail: detail.contactEmail,
				contactPhone: detail.contactPhone,
				location: detail.location,
				name: detail.name,
				no: detail.no,
				code: detail.code,
				taxId: detail.taxId,
				debitBankId: detail.debitBankId,
				withholdingBankId: detail.withholdingBankId,
				drawer: detail.drawer,
				reviewer: detail.reviewer,
				payee: detail.payee,
				invoiceAuthCode: detail.invoiceAuthCode,
				invoiceEncode: detail.invoiceEncode
			})
		}
	}

	handleMenuClick = (e, record) => {
		this.props.form.resetFields();
		this.props.selects();
		this.setState({
			title: e.key === '0' ? '新增同级片区' : '新增子级片区',
			parentId: e.key === '0' ? record.parentId : record.id
		}, () => this.props.setState({ key: 'visible', value: true }))
	};

	// 编辑片区
	handleEdit = row => {
		this.setState({
			title: '编辑片区'
		}, () => {
			this.props.getDetail(row.id);
		})
	};

	//
	handleCancel = () => {
		this.props.setState([{ key: 'visible', value: false }, { key: 'detail', value: null }]);
		isBeing = true
	};

	handleSubmit = e => {
		e.preventDefault();
		this.props.form.validateFields((err, values) => {
			if (!err) {
				if (this.state.title === '编辑片区') {
					const { detail } = this.props;
					let params = {
						contact: values.contact,
						contactEmail: values.contactEmail,
						contactPhone: values.contactPhone,
						id: detail.id,
						location: values.location,
						name: values.name,
						no: values.no,
						code: values.code,
						parentId: detail.parentId,
						taxId: values.taxId,
						debitBankId: values.debitBankId,
						withholdingBankId: values.withholdingBankId,
						drawer: values.drawer,
						reviewer: values.reviewer,
						review: values.review,
						payee: values.payee,
						invoiceAuthCode: detail.invoiceAuthCode,
						invoiceEncode: detail.invoiceEncode
					};
					this.props.modify(params);
				} else {
					let params = {
						contact: values.contact,
						contactEmail: values.contactEmail,
						contactPhone: values.contactPhone,
						location: values.location,
						name: values.name,
						no: values.no,
						code: values.code,
						parentId: this.state.parentId,
						taxId: values.taxId,
						debitBankId: values.debitBankId,
						withholdingBankId: values.withholdingBankId,
						drawer: values.drawer,
						reviewer: values.reviewer,
						review: values.review,
						payee: values.payee,
						invoiceAuthCode: values.invoiceAuthCode,
						invoiceEncode: values.invoiceEncode
					};
					this.props.add(params);
					this.handleCancel();
				}
			}
		});


	};

	// 搜索框 值回填
	iptOnChange = v => {
		this.setState({
			searchName: v.target.value
		})
	};

	// 搜索
	handleSearch = () => { this.props.list({ searchName: this.state.searchName }) };

	// 重置搜索条件
	handleReset = () => this.setState({ searchName: '' }, () => this.props.list());

	render() {
		const { title, searchName } = this.state;
		const { form, visible, tax, bank, datalist } = this.props;
		const { getFieldDecorator, setFieldsValue } = form;
		const formItemLayout = {
			labelCol: { span: 6 },
			wrapperCol: { span: 18 }
		};
		const formLongLayout = {
			labelCol: { span: 8 },
			wrapperCol: { span: 16 }
		};
		const formTailLayout = {
			labelCol: { span: 4 },
			wrapperCol: { span: 20, offset: 4 }
		};
		const columns = [
			{
				title: '片区名称',
				dataIndex: 'name',
				key: 'name',
				// fixed: 'left',
			},
			{
				title: '区域码',
				dataIndex: 'code',
				key: 'code',
				align: 'center',
			},
			{
				title: '片区编号',
				dataIndex: 'no',
				key: 'no',
				align: 'center',
			},
			{
				title: '操作',
				key: 'operation',
				// width: 200,
				// fixed: 'right',
				align: 'center',
				render: (text, record, index) => {
					return (
						<span>
							<Button permission={React.$pmn('REVENUE:SYSTEM_SETTINGS:AREA_MANAGEMENT:EDIT')} className='btn' type="primary" size="small" icon="form" onClick={() => this.handleEdit(record)}>编辑</Button>
							<Dropdown overlay={
								<Menu onClick={e => this.handleMenuClick(e, record)}>
									<Menu.Item key="0" disabled={!record.parentId}>新增同级</Menu.Item>
									<Menu.Item key="1">新增子级</Menu.Item>
								</Menu>
							}>
								<Button permission={React.$pmn('REVENUE:SYSTEM_SETTINGS:AREA_MANAGEMENT:ADD')} type="primary" size="small">
									新增 <Icon type="down" />
								</Button>
							</Dropdown>
						</span>
					)
				},
			},
		];
		return (
			<div className="shadow-radius areas">
				{/* 页面标题 - 搜索条件 */}
				<Row>
					<Col span={6}><h1>片区管理</h1></Col>
					<Col span={18}>
						<Form layout="inline" onSubmit={this.handleSearch}>
							{/* <Col span={20} align={'right'}>
								<FormItem>
									<Input placeholder="请输入搜索信息" value={searchName} onChange={this.iptOnChange} />
								</FormItem>
							</Col>
							<Col span={4}>
								<FormItem>
									&nbsp;
									<Button type="primary" htmlType="submit">
										搜索
									</Button>
									&nbsp;
									<Button type="danger" onClick={this.handleReset}>
										重置
									</Button>
								</FormItem>
							</Col> */}
						</Form>
					</Col>
				</Row>
				<div className="main">
					<Table
						rowKey={record => record.id}
						columns={columns}
						dataSource={datalist}
						// scroll={{ x: 800 }}
						defaultExpandedRowKeys={[100]}
						pagination={false}
					/>
				</div>
				<Modal title={title} visible={visible} onCancel={this.handleCancel} footer={null}>
					<Form onSubmit={this.handleSubmit} refs="editForm">
						<FormItem label="片区名称" {...formItemLayout}>
							{getFieldDecorator('name', {
								initialValue: '',
								rules: [
									{
										required: true,
										message: '片区名称必填'
									}
								]
							})(<Input placeholder='请输入片区名称' />)}
						</FormItem>
						<FormItem label="区域码" {...formItemLayout}>
							{getFieldDecorator('code', {
								initialValue: '',
								rules: [
									{
										required: true,
										message: '区域码必填'
									},
									{
										validator: (rule, value, callback) => {
											if (/\s+/.test(value)) {
												setFieldsValue({
													code: value.replace(/\s+/, '')
												});
												callback();
											} else {
												if (/^\d*$/.test(value)) {
													if (value.length > 3) {
														setFieldsValue({
															code: `${value}`.substr(0, `${value}`.length - 1)
														});
														callback();
													}
													if (value.length < 3) {
														setFieldsValue({
															code: value
														});
														callback('区域码必须为三位数字');
													}
													if (value.length == 3) {
														setFieldsValue({
															code: value
														});
														callback();
													}
												} else {
													setFieldsValue({
														code: `${value}`.substr(0, `${value}`.length - 1)
													});
													callback();
												}
											}
										}
									}
								]
							})(
								<Input placeholder='请输入区域码' />
							)}
						</FormItem>
						<FormItem label="片区编号" {...formItemLayout}>
							{getFieldDecorator('no', {
								initialValue: '',
								rules: [
									{
										required: true,
										message: '片区编号必填'
									},
									{
										validator: (rule, value, callback) => {
											if (/\s+/.test(value)) {
												setFieldsValue({
													no: value.replace(/\s+/, '')
												});
												callback();
											} else {
												if (/^\d*$/.test(value)) {
													if (value.length > 2) {
														setFieldsValue({
															no: `${value}`.substr(0, `${value}`.length - 1)
														});
														callback();
													}
													if (value.length < 2) {
														setFieldsValue({
															no: value
														});
														callback('片区编号必须为两位数字');
													}
													if (value.length == 2) {
														setFieldsValue({
															no: value
														});
														callback();
													}
												} else {
													setFieldsValue({
														no: `${value}`.substr(0, `${value}`.length - 1)
													});
													callback();
												}
											}
										}
									}
								]
							})(
								<Input placeholder='请输入片区编号' />
							)}
						</FormItem>
						<FormItem label="片区地址" {...formItemLayout}>
							{getFieldDecorator('location', {
								initialValue: '',
							})(<Input placeholder='请输入片区地址' />)}
						</FormItem>
						<FormItem label="负责人" {...formItemLayout}>
							{getFieldDecorator('contact', {
								initialValue: '',
							})(<Input placeholder='请输入负责人' />)}
						</FormItem>
						<FormItem label="联系电话" {...formItemLayout}>
							{getFieldDecorator('contactPhone', {
								initialValue: '',
							})(<Input placeholder='请输入联系电话' />)}
						</FormItem>
						<FormItem label="邮箱" {...formItemLayout}>
							{getFieldDecorator('contactEmail', {
								initialValue: '',
							})(<Input placeholder='请输入邮箱' />)}
						</FormItem>
						<FormItem label="水司税务信息" {...formItemLayout}>
							{getFieldDecorator('taxId', {
								rules: [
									{
										required: true,
										message: '水司税务信息必选'
									}
								]
							})(
								<Select placeholder="请选择水司税务信息">
									{
										tax.map((item, index) => {
											return <Option value={item.value}>{item.label}</Option>
										})
									}
								</Select>
							)}
						</FormItem>
						<FormItem label="小额借记账户" {...formItemLayout}>
							{getFieldDecorator('debitBankId', {
								rules: [
									{
										required: true,
										message: '小额借记进账账户必选'
									}
								]
							})(
								<Select placeholder='请选择小额借记账户'>
									{
										bank.map((item, index) => {
											return <Option value={item.value}>{item.label}</Option>
										})
									}
								</Select>
							)}
						</FormItem>
						<FormItem label="代扣账户" {...formItemLayout}>
							{getFieldDecorator('withholdingBankId', {
								rules: [
									{
										required: true,
										message: '代扣进账账户必选'
									}
								]
							})(
								<Select placeholder='请选择代扣账户'>
									{
										bank.map((item, index) => {
											return <Option value={item.value}>{item.label}</Option>
										})
									}
								</Select>
							)}
						</FormItem>
						<FormItem label="开票人" {...formItemLayout}>
							{getFieldDecorator('drawer', {
								initialValue: '',
							})(<Input placeholder='请输入开票人' />)}
						</FormItem>
						<FormItem label="复核人 " {...formItemLayout}>
							{getFieldDecorator('reviewer', {
								initialValue: '',
							})(<Input placeholder='请输入复核人 ' />)}
						</FormItem>
						<FormItem label="收款人" {...formItemLayout}>
							{getFieldDecorator('payee', {
								initialValue: '',
							})(<Input placeholder='请输入收款人' />)}
						</FormItem>
						<FormItem label="发票编码" {...formItemLayout}>
							{getFieldDecorator('invoiceEncode', {
								initialValue: '',
							})(<Input placeholder='请输入发票编码' />)}
						</FormItem>
						<FormItem label="发票授权码" {...formItemLayout}>
							{getFieldDecorator('invoiceAuthCode', {
								initialValue: '',
							})(<Input placeholder='请输入发票授权码' />)}
						</FormItem>
						<FormItem label="app换表是否审批" {...formItemLayout}>
								{getFieldDecorator('review', {
										initialValue: 1,
										rules: [
												{
														required: true,
														message: '请选择是否收费'
												}
										]
								})(
								    <Select placeholder="请选择是否收费">
												<Option key="0" value={0} >
														否
												</Option>
												<Option key="1" value={1}  >
														是
												</Option>
								    </Select>
								)}
						</FormItem>
						<Form.Item {...formTailLayout}>
							<Button type="primary" onClick={this.handleSubmit}>
								提交
							</Button>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<Button type="default" onClick={this.handleCancel}>
								取消
							</Button>
						</Form.Item>
					</Form>
				</Modal>
			</div>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('areas');
	return {
		fields: data.fields,
		datalist: data.datalist, // 数据列表
		detail: data.detail, // 片区详情
		visible: data.visible, // 是否显示弹窗
		tax: data.tax, // 税务信息
		bank: data.bank, // 银行信息
	}
};
const mapDispatchToProps = dispatch => ({
	list: data => {
		dispatch(actionCreators.list(data))
	},
	add: data => {
		dispatch(actionCreators.add(data))
	},
	modify: data => {
		dispatch(actionCreators.modify(data))
	},
	getDetail: data => {
		dispatch(actionCreators.detail(data))
	},
	setState: data => {
		dispatch(actionCreators.setState(data))
	},
	selects: () => dispatch(actionCreators.selects()),
});
export default connect(
	mapStateToProps,
	mapDispatchToProps
)((Form.create())(Index));
