import http from '$http';
import * as actionTypes from './constants';
import api from './api';
import { message } from 'antd';

// 数据回填
const payload = (type, data) => ({type, data});

// 获取列表
const list = (data) => {
	return async dispatch => {
		const res = await http.get(api.list, data);
		if (res.code === 0) {
			dispatch(payload(actionTypes.LIST_RECORD, res.data))
		}
	}
};

// 新增
const add = (data) => {
	return async dispatch => {
		const res = await http.post(api.add, data);
		if (res.code === 0) {
			message.success('新增成功！');
			dispatch(setState({key: 'visible', value: false}));
			dispatch(list())

		}
	}
};

// 更新
const modify = data => {
	return async dispatch => {
		const res = await http.post(api.modify, data);
		if (res.code === 0) {
			message.success('修改成功');
			dispatch(payload(actionTypes.MODIFY_RECORD))
			dispatch(list());
		}
	}
};

// 获取详情
const detail = data => {
	return async dispatch => {
		const res = await http.restGet(api.detail, data);
		if (res.code === 0) {
			dispatch(taxSelect());
			dispatch(bankSelect());
			dispatch(payload(actionTypes.DETAIL_RECORD, res.data))
		}
	}
};

// 设置state
const setState = data => {
	return dispatch => {
		dispatch(payload(actionTypes.SET_STATE, data))
	}
};

const taxSelect = () => {
	return async dispatch => {
		const res = await http.get(api.tax);
		if (res.code === 0) {
			dispatch(payload(actionTypes.TAX_SELECT, res.data))
		}
	}
};

const bankSelect = () => {
	return async dispatch => {
		const res = await http.get(api.bank);
		if (res.code === 0) {
			dispatch(payload(actionTypes.BANK_SELECT, res.data))
		}
	}
};

const selects = () => {
	return dispatch => {
		dispatch(taxSelect());
		dispatch(bankSelect());
	}
};


export { setState, list, add, modify, detail, selects };
