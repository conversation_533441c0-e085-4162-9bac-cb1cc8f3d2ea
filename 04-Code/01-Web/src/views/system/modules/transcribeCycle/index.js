import React, { Component, Fragment } from 'react'
import { actionCreators } from './store'
import { connect } from 'react-redux'
import { Modal, Table, Form, Select, Row, Col, Input, Button, DatePicker, Tree, Checkbox, message } from 'antd'
import moment from 'moment';
import { constants } from '$utils'
import './index.scss'

const { confirm } = Modal
const FormItem = Form.Item
const { Option } = Select
const ButtonGroup = Button.Group
const { RangePicker } = DatePicker
const { TreeNode } = Tree;

const monthList = [
	{
		'label': '一月',
		'value': '01',
	},
	{
		'label': '二月',
		'value': '02',
	},
	{
		'label': '三月',
		'value': '03',
	},
	{
		'label': '四月',
		'value': '04',
	},
	{
		'label': '五月',
		'value': '05',
	},
	{
		'label': '六月',
		'value': '06',
	},
	{
		'label': '七月',
		'value': '07',
	},
	{
		'label': '八月',
		'value': '08',
	},
	{
		'label': '九月',
		'value': '09',
	},
	{
		'label': '十月',
		'value': '10',
	},
	{
		'label': '十一月',
		'value': '11',
	},
	{
		'label': '十二月',
		'value': '12'
	},
]

class Index extends Component {
	constructor(props) {
		super(props)
		this.state = {
			columns: [
				{
					title: '片区名称',
					dataIndex: 'name',
					key: 'name',
					align: 'center',
				},
				{
					title: '片区编号',
					dataIndex: 'no',
					key: 'no',
					align: 'center',
				},
				{
					title: '负责人',
					dataIndex: 'contact',
					key: 'contact',
					align: 'center',
				},
				{
					title: '抄表月份',
					dataIndex: 'transcribeCycle',
					key: 'transcribeCycle',
					align: 'center',
				},
			],
			selectedRowKeys: [], // 选中的行数据
			modalVisible: false,
			checkedValues: [], // 选中的月份
		}
	}
	componentDidMount() {
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields&&isTags) {
			this.setState({
				...fields.state
			}, () => {
				this.props.form.setFieldsValue({
					...fields.form
				})
			})
		} else {
			this.getList()
		}
	}

	componentWillUnmount() {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			})
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			})
		}
	}

	getList = () => {
		this.props.list()
	}
	// 列表选中
	onSelectChange = (selectedRowKeys, selectedRows) => {
		this.setState({ selectedRowKeys, selectedRows })
	};
	handleAdd = () => {
		if (this.state.selectedRowKeys.length === 0) {
			message.error('请选择片区')
			return
		}
		this.setState({ modalVisible: true })
	}
	handleCancel = () => {
		this.setState({ modalVisible: false, checkedValues: [] })
	}
	// 提交信息
	handleSubmit = (e) => {
		e.preventDefault()
		let params = {
			type: 0, // 类型为片区
			typeIdList: this.state.selectedRowKeys,
			transcribeCycle: this.state.checkedValues.join()
		}
		this.props.add(params, this.getList)
		this.handleCancel()
	}
	// 抄表周期选中
	onChange = (checkedValues) => {
		this.setState({ checkedValues })
	}
	_renderModal = () => {
		const { modalVisible } = this.state
		return (
			<Modal
				className="transcribe-cycle-modal"
				title='选择抄表月份'
				visible={modalVisible}
				onCancel={this.handleCancel}
				footer={null}
			>
				<Row>
					<Checkbox.Group
						options={monthList}
						onChange={this.onChange}
					/>
				</Row>
				<Row style={{ marginTop: 20 }}>
					<Col span={24} align="center">
						<Button type="primary" className="btn" onClick={this.handleSubmit}>
							提交
							</Button>
						<Button type="default" className="btn" onClick={this.handleCancel}>
							取消
							</Button>
					</Col>
				</Row>
			</Modal>
		)
	}
	render() {
		const { columns, selectedRowKeys } = this.state
		const { dataList } = this.props
		const rowSelection = {
			fixed: true,
			selectedRowKeys,
			onChange: this.onSelectChange,
		};
		return (
			<div className="shadow-radius transcribe-cycle">
				<Row>
					<Col>
						<h1>抄表周期配置</h1>
					</Col>
				</Row>
				<Row>
					<Col>
						<Button type="primary" onClick={this.handleAdd}> 批量配置 </Button>
					</Col>
				</Row>
				<Row className="main">
					{dataList && dataList.length ? (
					<Table
						rowSelection={rowSelection}
						rowKey={record => record.id}
						columns={columns}
						dataSource={dataList}
						defaultExpandedRowKeys={'100'}
						pagination={{
							disabled: true,
							hideOnSinglePage: true,
						}}
					/>) : '暂无数据'}
				</Row>
				{this._renderModal()}
			</div>
		)
	}
}
const mapStateToProps = (state) => {
	const data = state.get('transcribeCycle')
	return {
		fields: data.fields,
		dataList: data.dataList, // 列表数据
	}
}
const mapDispatchToProps = (dispatch) => ({
	setState: (data) => dispatch(actionCreators.setState(data)), // 设置redux值
	list: () => dispatch(actionCreators.list()), // 获取列表
	add: (data, list) => dispatch(actionCreators.add(data, list)), // 添加
})
export default connect(mapStateToProps, mapDispatchToProps)(Form.create()(Index))
