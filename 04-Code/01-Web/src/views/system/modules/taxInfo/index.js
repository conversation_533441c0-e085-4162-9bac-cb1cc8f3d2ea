import React, { Component } from 'react';
import { connect } from 'react-redux';
import { actionCreators } from './store';
import { Button, Col, Form, Input, Row, Select, Table, Modal } from 'antd';
import { constants } from '$utils';
import './index.scss';

const FormItem = Form.Item;
const { confirm } = Modal;
const defaultSearchForm = {};

class Index extends Component {

	constructor(props) {
		super(props);
		this.state = {
			page: constants.page,
			pageSize: constants.pageSize,
			searchForm: defaultSearchForm,
			modalType: 'add',
			modalTitle: ''
		};
	}

	componentDidMount() {
		const { fields } = this.props;
		const isTags = localStorage.getItem('tags');
		// 获取当前 reducer fields的值，
		// 如果已存在，则不重新刷新页面，并将存储的值回填进当前组件
		if (fields && isTags) {
			this.setState({
				...fields.state
			}, () => {
				this.props.form.setFieldsValue({
					...fields.form
				})
			})
		} else {
			this.getList();
		}
	}

	componentWillUnmount() {
		const { form, setState } = this.props;
		// 判断当前页面是否存在 antd form 的控件
		// 如果有控件 则存储 form 控件的值和state的值
		if (form) {
			setState({
				key: 'fields',
				value: {
					form: form.getFieldsValue(),
					state: this.state
				}
			})
		} else {
			// 如果没有form 控件的值，则只存储state的值
			setState({
				key: 'fields',
				value: {
					state: this.state
				}
			})
		}
	}

	// 获取列表
	getList = () => {
		const { page, pageSize, searchForm } = this.state;
		this.props.list(Object.assign({ ...searchForm }, { page, pageSize }));
	};

	// 列表分页
	handlePageChange = page => {
		this.setState(
			{
				page
			},
			() => {
				this.getList();
			}
		);
	};

	// 改变分页条数
	pageSizeChange = (current, size) => {
		this.setState(
			{
				page: 1,
				pageSize: size
			},
			() => {
				this.getList();
			}
		);
	};

	// 新增记录
	handleAdd = () => {
		this.setState(
			{
				modalType: 'add',
				modalTitle: '新增水司税务信息'
			},
			() => {
				// 设置redux visible 为true 显示弹窗
				this.props.setState([{ key: 'visible', value: true }, { key: 'detail', value: null }]);
			}
		);
	};

	// 查看详情
	handleView = record => {
		this.setState(
			{
				modalType: 'view',
				modalTitle: '查看详情'
			},
			() => {
				// 设置redux visible 为true 显示弹窗
				this.props.setState([{ key: 'visible', value: true }, { key: 'detail', value: null }]);
				// 获取详情
				this.props.getDetail(record.id);
			}
		);
	};

	// 编辑记录
	handleEdit = record => {
		this.props.form.resetFields();
		this.setState(
			{
				modalType: 'edit',
				modalTitle: '编辑'
			},
			() => {
				// 设置redux visible 为true 显示弹窗
				this.props.setState([{ key: 'visible', value: true }, { key: 'detail', value: null }]);
				// 获取详情
				this.props.getDetail(record.id);
			}
		);
	};

	// 删除记录
	handleDel = record => {
		const _this = this;
		confirm({
			title: '确认操作',
			content: '是否确认删除该条记录？',
			okText: '确定',
			okType: 'danger',
			cancelText: '取消',
			onOk() {
				_this.props.del(record.id, _this.getList);
			},
			onCancel() {
				console.log('Cancel');
			}
		});
	};

	// 提交信息
	handleSubmit = e => {
		const { modalType } = this.state;
		e.preventDefault();
		this.props.form.validateFields((err, values) => {
			if (!err) {
				let params = {};
				if (modalType === 'edit') {
					params = Object.assign({ ...values }, { id: this.props.detail.id });
					this.props.modify(params, this.getList);
				} else {
					let params = Object.assign({ ...values });
					this.props.add(params, this.getList);
				}
			}
		});
	};

	// 取消弹窗
	handleCancel = () => {
		this.props.setState([{ key: 'visible', value: false }, { key: 'detail', value: null }]);
	};

	// 渲染搜索
	_renderSearchForm = () => {
		return (
			<Form className="ant-advanced-search-form" {...constants.formItemLayout}>
				<Col span={8} />
				<Col span={16} align="right">
					<Button className="searchBtn" type="primary">
						搜索
					</Button>
				</Col>
			</Form>
		);
	};

	// 渲染操作按钮
	_renderOperateButton = () => {
		return (
			<Col>
				<Button type="primary" onClick={this.handleAdd}>
					新增水司税务信息
				</Button>
			</Col>
		);
	};

	// 渲染新增弹出框
	_renderModal = () => {
		const { modalTitle, modalType } = this.state;
		const { detail, visible } = this.props;
		const { getFieldDecorator } = this.props.form;
		return (
			<Modal className="staffModal" title={modalTitle} visible={visible} onCancel={this.handleCancel} footer={null}>
				<Form {...constants.formItemLayout}>
					<Row>
						<Col span={24}>
							<FormItem label="户名：">
								{modalType === 'view'
									? detail && detail.accountName
									: getFieldDecorator('accountName', {
										initialValue: detail ? detail.accountName : '',
										rules: [
											{
												required: true,
												message: '请输入户名'
											}
										]
									})(<Input disabled={modalType === 'view'} palceholder="请输入户名" />)}
							</FormItem>
						</Col>
					</Row>
					<Row>
						<Col span={24}>
							<FormItem label="税号：">
								{modalType === 'view'
									? detail && detail.taxId
									: getFieldDecorator('taxId', {
										initialValue: detail ? detail.taxId : '',
										rules: [
											{
												required: true,
												message: '请输入税号'
											}
										]
									})(<Input disabled={modalType === 'view'} palceholder="请输入税号" />)}
							</FormItem>
						</Col>
					</Row>
					<Row>
						<Col span={24}>
							<FormItem label="开户银行：">
								{modalType === 'view'
									? detail && detail.bankName
									: getFieldDecorator('bankName', {
										initialValue: detail ? detail.bankName : '',
										rules: [
											{
												required: true,
												message: '请输入开户银行'
											}
										]
									})(<Input disabled={modalType === 'view'} palceholder="请输入开户银行" />)}
							</FormItem>
						</Col>
					</Row>
					<Row>
						<Col span={24}>
							<FormItem label="银行账号：">
								{modalType === 'view'
									? detail && detail.bankAccount
									: getFieldDecorator('bankAccount', {
										initialValue: detail ? detail.bankAccount : '',
										rules: [
											{
												required: true,
												message: '请输入银行账号'
											}
										]
									})(<Input disabled={modalType === 'view'} palceholder="请输入银行账号" />)}
							</FormItem>
						</Col>
					</Row>
					<Row>
						<Col span={24}>
							<FormItem label="用户地址：">
								{modalType === 'view'
									? detail && detail.address
									: getFieldDecorator('address', {
										initialValue: detail ? detail.address : '',
										rules: [{ required: true, message: '请输入用户地址' }]
									})(<Input disabled={modalType === 'view'} palceholder="请输入用户地址" />)}
							</FormItem>
						</Col>
					</Row>
					<Row>
						<Col span={24}>
							<FormItem label="联系电话：">
								{modalType === 'view'
									? detail && detail.phone
									: getFieldDecorator('phone', {
										initialValue: detail ? detail.phone : '',
										rules: [{ required: true, message: '请输入联系电话' }]
									})(<Input disabled={modalType === 'view'} palceholder="请输入联系电话" />)}
							</FormItem>
						</Col>
					</Row>
					<Row>
						<Col span={24} align="center">
							{modalType === 'view' ? (
								<Button type="default" onClick={this.handleCancel}>
									关闭
								</Button>
							) : (
									<>
										<Button type="primary" className="btn" onClick={this.handleSubmit}>
											提交
									</Button>
										<Button type="default" className="btn" onClick={this.handleCancel}>
											取消
									</Button>
									</>
								)}
						</Col>
					</Row>
				</Form>
			</Modal>
		);
	};

	render() {
		const { page, pageSize } = this.state;
		const { dataList, total } = this.props;
		const paginationProps = {
			page,
			pageSize,
			showSizeChanger: true,
			showQuickJumper: true,
			pageSizeOptions: ['10', '20', '50', '100'],
			total: total,
			showTotal: total => {
				return `共 ${total} 条数据 `;
			},
			onChange: page => this.handlePageChange(page),
			onShowSizeChange: (current, size) => this.pageSizeChange(current, size)
		};
		const columns = [
			{
				title: '户名',
				dataIndex: 'accountName',
				key: 'accountName',
				align: 'center'
			},
			{
				title: '税号',
				dataIndex: 'taxId',
				key: 'taxId',
				align: 'center'
			},
			{
				title: '开户银行',
				dataIndex: 'bankName',
				key: 'bankName',
				align: 'center'
			},
			{
				title: '银行账号',
				dataIndex: 'bankAccount',
				key: 'bankAccount',
				align: 'center'
			},
			{
				title: '更新人',
				dataIndex: 'updatePersonName',
				key: 'updatePersonName',
				align: 'left'
			},
			{
				title: '更新时间',
				dataIndex: 'updateTime',
				key: 'updateTime',
				align: 'left'
			},
			{
				title: '操作',
				width: 200,
				fixed: 'right',
				align: 'center',
				render: (text, record, index) => {
					return (
						<span>
							<Button title="查看" className="btn" type="primary" size="small" icon="eye" onClick={() => this.handleView(record)} />
							<Button title="编辑" className="btn" type="primary" size="small" icon="form" onClick={() => this.handleEdit(record)} />
							<Button title="删除" className="btn" type="primary" size="small" icon="delete" onClick={() => this.handleDel(record)} />
						</span>
					);
				}
			}
		];
		return (
			<div className="shadow-radius bank-info">
				<Row>
					<Col>
						<h1>水司税务信息管理</h1>
					</Col>
				</Row>
				<Row>{this._renderSearchForm()}</Row>
				<Row>{this._renderOperateButton()}</Row>
				<Row className="main">
					<Table
						bordered
						scroll={{ x: 2500 }}
						columns={columns}
						rowKey={() => Math.random()}
						dataSource={dataList}
						pagination={paginationProps} />
				</Row>
				{this._renderModal()}
			</div>
		);
	}
}

const mapStateToProps = state => {
	const data = state.get('taxInfo');
	return {
		fields: data.fields,
		dataList: data.dataList, // 数据列表
		total: data.total,
		detail: data.detail,
		visible: data.visible
	};
};
const mapDispatchToProps = dispatch => ({
	list: data => dispatch(actionCreators.list(data)),
	setState: data => dispatch(actionCreators.setState(data)), // 设置redux值
	getDetail: data => dispatch(actionCreators.detail(data)), // 获取详情
	add: (data, list) => dispatch(actionCreators.add(data, list)), // 新增记录
	del: (data, list) => dispatch(actionCreators.del(data, list)), // 删除记录
	modify: (data, list) => dispatch(actionCreators.modify(data, list)) // 修改记录
});

export default connect(
	mapStateToProps,
	mapDispatchToProps
)(Form.create()(Index));
