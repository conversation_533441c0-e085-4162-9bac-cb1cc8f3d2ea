import axios from 'axios';
// import qs from 'query-string';
import { message } from 'antd';
import { constants } from '$utils';
import store from '@/store';

let _isLogin = false;
let isLoading = true;


const _config = {
	baseURL: process.env.API_ROOT,
	timeout: 600000,
	retry: 4,
	retryDelay: 1000,
};

const http = axios.create(_config);

// 加载 / 销毁 loading
const loading = data => {
	store.dispatch({
		type: 'layout/SHOW_LOADING',
		data
	})
};

//请求拦截
http.interceptors.request.use(
	function (config) {
		// 在发送请求之前做些什么
		// 通过reudx的store拿到拿到全局状态树的token ，添加到请求报文，后台会根据该报文返回status
		// 此处应根据具体业务写token
		// const token = store.getState().user.token || localStorage.getItem('token');
		// 填充默认的pageSize，如果有则不处理，如果没有则添加pageSize参数
		loading(isLoading);
		if (config.data && config.data.page) {
			if (!config.data.pageSize) {
				config.data = {
					...config.data,
					pageSize: constants.pageSize
				}
			}
		}
		// 拿到当前登录的token
		const token = localStorage.getItem('token');
		// 未登录的时候，不处理token
		// 登录后，或者当前已有token存在，则添加token
		if (_isLogin || token) {
			config.headers['token'] = token;
		}
		return config;
	},
	function (error) {
		loading(false);
		isLoading = true;
		// 对请求错误做些什么
		message.error(error);
		return Promise.reject(error);
	}
);

// 添加响应拦截器
http.interceptors.response.use(
	function (response) {
		loading(false);
		isLoading = true;
		// 对响应数据做点什么
		const res = response.data;
		if (res.code) {
			if ([1001, 1002, 1003, 1004].indexOf(res.code) > 0) {
				message.error(res.msg);
				localStorage.removeItem('token');
				localStorage.removeItem('ISL');
				localStorage.removeItem('usi');
				localStorage.removeItem('pmn');
				localStorage.removeItem('bh');
				localStorage.removeItem('bhl');
				window.location.replace(`${window.location.origin}/${process.env.APP_NAME}/#/login`)
				return Promise.reject(res);
			}
			if (res.code !== 0) {
				message.error(res.msg);
			}
		}
		return res;
	},
	function (error) {
		// if (error.code === 'ECONNABORTED' && error.message.indexOf('timeout') !== -1) {
		// 	var config = error.config;
		// 	config.__retryCount = config.__retryCount || 0;
		//
		// 	if (config.__retryCount >= config.retry) {
		// 		// Reject with the error
		// 		//window.location.reload();
		// 		return Promise.reject(error);
		// 	}
		//
		// 	// Increase the retry count
		// 	config.__retryCount += 1;
		//
		// 	// Create new promise to handle exponential backoff
		// 	var backoff = new Promise(function(resolve) {
		// 		setTimeout(function() {
		// 			//console.log('resolve');
		// 			resolve();
		// 		}, config.retryDelay || 1);
		// 	});
		//
		// 	return backoff.then(function() {
		// 		return axios(config);
		// 	});
		// } else {
		// 	return Promise.reject(error);
		// }
		loading(false);
		isLoading = true;
		return Promise.reject(error);
	}
);

const $http = {
	login: (api, params) => {
		// params = qs.stringify(params);
		return new Promise((resolve, reject) => {
			http.post(api, params).then(res => {
				if (res.code === 0) {
					_isLogin = true;
				}
				resolve(res)
			}).catch(e => {
				reject(e)
			})
		})
	},
	post: (api, params, loading) => {
		isLoading = `${loading}` ? loading : true;
		// params = qs.stringify(params);
		return new Promise((resolve, reject) => {
			http.post(api, params, loading).then(res => {
				resolve(res)
			}).catch(e => {
				reject(e)
			})
		})
	},
	restPost: (api, params, loading) => {
		isLoading = `${loading}` ? loading : true;
		// params = qs.stringify(params);
		return new Promise((resolve, reject) => {
			http.post(`${api}/${params}`, loading).then(res => {
				resolve(res)
			}).catch(e => {
				reject(e)
			})
		})
	},
	get: (api, params, loading) => {
		isLoading = `${loading}` ? loading : true;
		// params = qs.stringify(params);
		return new Promise((resolve, reject) => {
			http.get(`${api}?${Math.random()}`, { params, loading }).then(res => {
				resolve(res)
			}).catch(e => {
				reject(e)
			})
		})
	},
	restGet: (api, params, loading) => {
		// params = qs.stringify(params);
		isLoading = `${loading}` ? loading : true;
		return new Promise((resolve, reject) => {
			http.get(`${api}/${params}?${Math.random()}`, loading).then(res => {
				resolve(res)
			}).catch(e => {
				reject(e)
			})
		})
	},
	del: (api, params, loading) => {
		isLoading = `${loading}` ? loading : true;
		// params = qs.stringify(params);
		return new Promise((resolve, reject) => {
			http.post(`${api}/${params}`, loading).then(res => {
				resolve(res)
			}).catch(e => {
				reject(e)
			})
		})
	},
	put: (api, params) => {
		isLoading = `${loading}` ? loading : true;
		// params = qs.stringify(params);
		return new Promise((resolve, reject) => {
			http.post(`${api}/${params}`, loading).then(res => {
				resolve(res)
			}).catch(e => {
				reject(e)
			})
		})
	},
	export: (api, params, callback) => {
		isLoading = `${loading}` ? loading : true;
		// params = qs.stringify(params);
		http.post(api, params, {responseType: 'blob'}).then(res => {
			callback(res)
		}).catch(e => {
			message.error('导出失败')
		})
	},
};

export default $http;
