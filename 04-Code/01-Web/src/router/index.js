import React from 'react';
import { HashRouter, Route, Switch } from 'react-router-dom';
import Layout from '@/layout/Index';
import Login from '@/views/login/index';
import AuthRouter from '@/utils/AuthRouter';
import BigScreen from '@/views/bigScreen';
const Router = () => {
	return (
		<HashRouter>
			<Switch>
				<Route component={Login} exact path="/login" />
        <Route component={BigScreen} exact path="/bigScreen" />
				<AuthRouter path="/" component={Layout} />
			</Switch>
		</HashRouter>
	);
};

export default Router;
