import Error404 from '../views/error/Error404';
import Error500 from '../views/error/Error500';

// 首页
import Home from '../views/home';
// 数据大屏
import Dashboard from '../views/bigScreen';
// 开户管理
// import AccountPre from '../views/account/modules/pre'; // 预开户
import AccountUser from '../views/account/modules/user'; // 用户开户
import AccountBatch from '../views/account/modules/batch'; // 用户开户
// 收费管理
import ChargePayment from '../views/charge/modules/payment'; // 柜台缴费
import PettyDebit from '../views/charge/modules/pettyDebit'; // 小额借记
import Withhold from '../views/charge/modules/withhold'; // 代扣
import ChargeOrder from '../views/charge/modules/order'; // 订单管理
import OrderRefundRecord from '../views/charge/modules/orderRefundRecord'; // 订单退款记录
import ChargeOrderInvoiceRecord from '../views/charge/modules/orderInvoiceRecord'; // 订单发票打印记录
import ChargeSpecial from '../views/charge/modules/special'; // 特抄收费
import ChargeSpecialRecord from '../views/charge/modules/specialRecord'; // 特抄收费记录
import ChargeSpecialRecordInvoiceRecord from '../views/charge/modules/specialRecordInvoiceRecord'; // 特抄收费发票打印记录
import ThirdPartyChargeOrderRecord from '../views/charge/modules/ThirdPartyChargeOrderRecord'; // 第三方支付记录
import ThirdPartyChargeOrderRefundRecord from '../views/charge/modules/ThirdPartyChargeOrderRefundRecord'; // 第三方支付退款记录
import WaterMeterSell from '../views/charge/modules/waterMeterSell';//水表销售
import WaterMeterSellRecord from '../views/charge/modules/waterMeterSellRecord';//水表销售记录
// 用户管理
import UserInfos from '../views/users/modules/infos'; // 用户信息
import UserDetail from '../views/users/modules/infos/view'; // 用户信息详情
import UserEdit from '../views/users/modules/infos/edit'; // 用户信息编辑
import UserOwners from '../views/users/modules/owners'; // 产权人信息
import UserPoor from '../views/users/modules/poor'; // 特困户信息
import UserSubsistence from '../views/users/modules/subsistence'; // 低保户信息
import Withholding from '../views/users/modules/withHolding'; // 代扣信息
import Debit from '../views/users/modules/debit'; // 小额借记信息
import UserTax from '../views/users/modules/tax'; // 增值税资料管理
// import UserInvoice from '../views/users/modules/invoice'; // 发票信息
import UserInfoChange from '../views/users/modules/infoChange'; // 用户信息变更记录
import SameHousehold from '../views/users/modules/sameHousehold'; // 同户管理
import WaterMeterCRD from '../views/users/modules/waterMeterCRD'; // 换表/拆表/复装
import UserWaterMeterChange from '../views/users/modules/waterMeterChange'; // 换表记录
import UserDismantle from '../views/users/modules/dismantle'; // 拆表记录
import UserWaterMeterReload from '../views/users/modules/waterMeterReload'; // 复装记录
import UserCancelAccount from '../views/users/modules/cancelAccount'; // 批量销户
import UserCancellation from '../views/users/modules/cancellation'; // 销户记录
import UserWithdraw from '../views/users/modules/withdraw'; // 提现记录
import WaterUseKindChange from '../views/users/modules/waterUseKindChange'; // 用水分类变更记录
import AddPopulationRecord from '../views/users/modules/addPopulationRecord'; // 人口核增记录
// IC卡管理
import IcReplacement from '../views/ic/modules/replacement'; // 补卡
import IcReplacementRecord from '../views/ic/modules/replacementRecord'; // 补卡记录
import IcManagementCard from '../views/ic/modules/managementCard'; // 管理卡制作
import IcTerminal from '../views/ic/modules/terminal'; // IC卡终端管理
// 远传表管理
import RemoteUsers from '../views/remote/modules/users'; // 远传表用户
import RemoteConcentrator from '../views/remote/modules/concentrator'; // 集中器管理
import RemoteOnOffRecord from '../views/remote/modules/onOffRecord'; // 开关阀记录
import RemoteBill from '../views/remote/modules/bill'; // 远传表账单
import RemoteBillMinusRecord from '../views/remote/modules/billMinusRecord'; // 远传表账单核减记录
import RemoteBillPrintInvoiceRecord from '../views/remote/modules/billPrintInvoiceRecord'; // 账单发票打印记录
import RemoteData from '../views/remote/modules/datas'; // 远传表数据
import RemoteAbnormal from '../views/remote/modules/abnormal'; // 远传表异常数据
// 机械表管理
import MechanicalUsers from '../views/mechanical/modules/users'; // 机械表用户
import MechanicalAmr from '../views/mechanical/modules/amr'; // 抄表任务
import MechanicalBill from '../views/mechanical/modules/bill'; // 机械表账单
import MechanicalBillAdjustmentRecord from '../views/mechanical/modules/billAdjustmentRecord'; // 账单调整记录
import MechanicalBillMinusRecord from '../views/mechanical/modules/billMinusRecord'; // 账单核减记录
import MechanicalBillPrintInvoiceRecord from '../views/mechanical/modules/billPrintInvoiceRecord'; // 账单发票打印记录
import MechanicalTrack from '../views/mechanical/modules/track'; // 抄表轨迹
// 水表管理
import WaterMeterManagement from '../views/waterMeter';
// 系统设置
import SysTranscribeCycle from '../views/system/modules/transcribeCycle'; // 抄表周期配置
import SysTranscriber from '../views/system/modules/transcriber'; // 抄表员管理
import SysAudit from '../views/system/modules/audit'; // 审批流配置
import SysAuditRole from '../views/system/modules/auditRole'; // 审批流角色配置
import SysBanks from '../views/system/modules/banks'; // 水司银行账户信息
import SysBankInfo from '../views/system/modules/bankInfo'; // 水司银行账户信息
import SysTaxInfo from '../views/system/modules/taxInfo'; // 水司税务信息
import Sysareas from '../views/system/modules/areas'; // 册本信息
import SysStaff from '../views/system/modules/staff'; // 员工管理
import SysRoll from '../views/system/modules/roll'; // 角色管理
import SysDepart from '../views/system/modules/depart'; // 部门管理
// import SysDepartLeader from '../views/system/modules/departLeader'; // 部门领导管理
import SysWaterQuality from '../views/system/modules/waterQuality'; // 用水性质
import SysWaterQualityAdd from '../views/system/modules/waterQuality/add'; // 用水性质 - 添加
import SysWaterQualityEdit from '../views/system/modules/waterQuality/edit'; // 用水性质 - 添加
import SysDictionary from '../views/system/modules/dictionary'; // 数据字典
import SysParameters from '../views/system/modules/parameters'; // 系统参数
import SysModules from '../views/system/modules/modules'; // 系统模块
import SysWaterFile from '../views/system/modules/waterFile'; // 水价文件
// 票据管理
import BillReceipt from '../views/bill/modules/receipt'; // 票据领用
import BillReceiptRecord from '../views/bill/modules/receiptRecord'; // 领用记录
import BillIssuingRecord from '../views/bill/modules/issuingRecord'; // 开具记录
import BillTemplate from '../views/bill/modules/template'; // 票据模板
// 消息管理
import Message from '../views/message/modules/sendRecord';
import News from '../views/news';
import SendSms from '../views/message/modules/sendSms';
import MessageTemp from '../views/message/modules/sendTempRecord';
// 公告消息
import AnnouncementList from '../views/announcement/modules/list'; // 公告列表
import AnnouncementPublish from '../views/announcement/modules/publish'; // 发布公告
import AnnouncementModify from '../views/announcement/modules/modify'; // 发布公告
// 流程管理
import ProcessApply from '../views/process/modules/apply'; // 我的申请
import ProcessApproval from '../views/process/modules/approval'; // 我的审批
import ProcessApplyQuery from '../views/process/modules/applyQuery'; // 申请查询
import ProcessApplyApproval from '../views/process/modules/applyApproval'; // 公众号申请审批

//统计报表
import ChargeReport from '../views/reportForm/modules/chargeReport';  //收费报表
import WaterAmountReport from '../views/reportForm/modules/waterAmountReport';  //水量报表
import OweReport from '../views/reportForm/modules/oweReport';  //欠费报表
import CustomerReport from '../views/reportForm/modules/customerReport';  //用户报表
import SynthesizeReport from '../views/reportForm/modules/synthesizeReport';  //综合报表



export const routes = [
	{ path: '/error/404', component: Error404, permission: 1 },
	{ path: '/error/500', component: Error500, permission: 1 },
	// 首页
	{ path: '/home', component: Home, permission: 'REVENUE:DASHBOARD' },
	// 数据大屏
	{ path: '/dashboard', component: Dashboard, permission: 'REVENUE:BIG_SCREEN' },
	// 开户管理
	// { path: '/accountManagement/pre', component: AccountPre, permission: 'REVENUE:OPEN_ACCOUNT_MANAGEMENT:PRE_OPEN_ACCOUNT' }, // 预开户
	{ path: '/accountManagement/user', component: AccountUser, permission: 'REVENUE:OPEN_ACCOUNT_MANAGEMENT:OPEN_ACCOUNT' }, // 用户开户
	{ path: '/accountManagement/bath', component: AccountBatch, permission: 'REVENUE:OPEN_ACCOUNT_MANAGEMENT:BATCH_OPEN_ACCOUNT' }, // 批量开户
	// 收费管理
	{ path: '/chargeManagement/payment', component: ChargePayment, permission: 'REVENUE:PAY_COST_MANAGEMENT:PAY_COST_COUNTER' }, // 柜台缴费
	{ path: '/chargeManagement/pettyDebit', component: PettyDebit, permission: 'REVENUE:PAY_COST_MANAGEMENT:SMALL_DEBIT' }, // 小额借记
	{ path: '/chargeManagement/withhold', component: Withhold, permission: 'REVENUE:PAY_COST_MANAGEMENT:WITHHOLDING' }, // 小额借记
	{ path: '/chargeManagement/order', component: ChargeOrder, permission: 'REVENUE:PAY_COST_MANAGEMENT:ORDER_MANAGEMENT' }, // 订单管理
	{ path: '/chargeManagement/orderRefundRecord', component: OrderRefundRecord, permission: 'REVENUE:PAY_COST_MANAGEMENT:ORDER_REFUND_RECORD' }, // 订单退款记录
	{ path: '/chargeManagement/orderInvoiceRecord', component: ChargeOrderInvoiceRecord, permission: 'REVENUE:PAY_COST_MANAGEMENT:ORDER_PRINT_INVOICE_RECORD' }, // 订单发票打印记录
	{ path: '/chargeManagement/special', component: ChargeSpecial, permission: 'REVENUE:PAY_COST_MANAGEMENT:SPECIAL_COPY_FEE' }, // 特抄收费
	{ path: '/chargeManagement/specialRecord', component: ChargeSpecialRecord, permission: 'REVENUE:PAY_COST_MANAGEMENT:SPECIAL_COPY_FEE_RECORD' }, // 特抄收费记录
	{ path: '/chargeManagement/specialRecordInvoiceRecord', component: ChargeSpecialRecordInvoiceRecord, permission: 'REVENUE:PAY_COST_MANAGEMENT:SPECIAL_RECORD_PRINT_INVOICE_RECORD' }, // 特抄收费发票打印记录
	{ path: '/chargeManagement/ThirdPartyChargeOrderRecord', component: ThirdPartyChargeOrderRecord, permission: 'REVENUE:PAY_COST_MANAGEMENT:THIRD_PARTY_RECORD' }, // 第三方支付记录
	{ path: '/chargeManagement/ThirdPartyChargeOrderRefundRecord', component: ThirdPartyChargeOrderRefundRecord, permission: 'REVENUE:PAY_COST_MANAGEMENT:THIRD_PARTY_REFUND_RECORD' }, // 第三方支付退款记录
	{ path: '/chargeManagement/waterMeterSell', component: WaterMeterSell, permission:'REVENUE:WATER_METER_SELL' },//水表销售
	{ path: '/chargeManagement/waterMeterSellRecord', component: WaterMeterSellRecord, permission:'REVENUE:WATER_METER_SELL_RECORD' },//水表销售记录
	// 用户管理
	{ path: '/userManagement/users', component: UserInfos, permission: 'REVENUE:USER_MANAGEMENT:USER_INFO' }, // 用户信息
	{ path: '/userManagement/owners', component: UserOwners, permission: 'REVENUE:USER_MANAGEMENT:OWNERS_INFO' }, // 产权人信息
	{ path: '/userManagement/poor', component: UserPoor, permission: 'REVENUE:USER_MANAGEMENT:POOR_HOUSEHOLD_MANAGEMENT' }, // 特困户信息
	{ path: '/userManagement/subsistence', component: UserSubsistence, permission: 'REVENUE:USER_MANAGEMENT:SUBSISTENCE_MANAGEMENT' }, // 低保户信息
	{ path: '/userManagement/withholding', component: Withholding, permission: 'REVENUE:USER_MANAGEMENT:WITHHOLDING_MANAGEMENT' }, // 代扣信息
	{ path: '/userManagement/debit', component: Debit, permission: 'REVENUE:USER_MANAGEMENT:DEBIT_MANAGEMENT' }, // 小额借记
	{ path: '/userManagement/tax', component: UserTax, permission: 'REVENUE:USER_MANAGEMENT:VAT_MANAGEMENT' }, // 增值税资料管理
	{ path: '/userManagement/infoChange', component: UserInfoChange, permission: 'REVENUE:USER_MANAGEMENT:USER_INFO_CHANGE_RECORD' }, // 用户信息变更记录
	{ path: '/userManagement/sameHousehold', component: SameHousehold, permission: 'REVENUE:USER_MANAGEMENT:HOUSEHOLD_MANAGEMENT' }, // 同户管理
	{ path: '/userManagement/cancelAccount', component: UserCancelAccount, permission: 'REVENUE:USER_MANAGEMENT:BATCH_PIN_WITHHOULD' }, // 销户记录
	{ path: '/userManagement/cancellation', component: UserCancellation, permission: 'REVENUE:USER_MANAGEMENT:PIN_HOUSEHOLDS_RECORD' }, // 销户记录
	{ path: '/userManagement/withdraw', component: UserWithdraw, permission: 'REVENUE:USER_MANAGEMENT:WITHDRAWAL_RECORD' }, // 提现记录
	{ path: '/userManagement/waterUseKindChange', component: WaterUseKindChange, permission: 'REVENUE:USER_MANAGEMENT:WATER_USE_KIND_CHANGE' }, // 用水性质变更记录
	{ path: '/userManagement/addPopulationRecord', component: AddPopulationRecord, permission: 'REVENUE:USER_MANAGEMENT:ADD_POPULATION_RECORD' }, // 人口核增记录
	// 用户信息
	{ path: '/users/detail', component: UserDetail, permission: 1 },
	{ path: '/users/edit', component: UserEdit, permission: 1 },
	// IC卡管理
	{ path: '/icManagement/replacement', component: IcReplacement, permission: 'REVENUE:IC_CARD_MANAGEMENT:REISSUE_CARD' }, // 补卡
	{ path: '/icManagement/replacementRecord', component: IcReplacementRecord, permission: 'REVENUE:IC_CARD_MANAGEMENT:REISSUE_CARD_RECORD' }, // 补卡记录
	{ path: '/icManagement/managementCard', component: IcManagementCard, permission: 'REVENUE:IC_CARD_MANAGEMENT:MANAGEMENT_CARD_MAKING' }, // 管理卡制作
	{ path: '/icManagement/terminal', component: IcTerminal, permission: 'REVENUE:IC_CARD_MANAGEMENT:IC_CARD_TERMINAL_MANAGEMENT' }, // IC卡终端管理
	// 远传表管理
	{ path: '/remoteWaterMeterManagement/users', component: RemoteUsers, permission: 'REVENUE:REMOTE_WATER_METER_MANAGEMENT:USERS' }, // 远传表用户
	{ path: '/remoteWaterMeterManagement/concentrator', component: RemoteConcentrator, permission: 'REVENUE:REMOTE_WATER_METER_MANAGEMENT:CONCENTRATOR_MANAGEMENT' }, // 集中器管理
	{ path: '/remoteWaterMeterManagement/onOffRecord', component: RemoteOnOffRecord, permission: 'REVENUE:REMOTE_WATER_METER_MANAGEMENT:ON_OFF_RECORD' }, // 开关阀记录
	{ path: '/remoteWaterMeterManagement/bill', component: RemoteBill, permission: 'REVENUE:REMOTE_WATER_METER_MANAGEMENT:REMOTE_BILL' }, // 远传表账单
	{ path: '/remoteWaterMeterManagement/billMinusRecord', component: RemoteBillMinusRecord, permission: 'REVENUE:REMOTE_WATER_METER_MANAGEMENT:BILL_REDUCTION_RECORD' }, // 账单核减记录
	{ path: '/remoteWaterMeterManagement/billPrintInvoiceRecord', component: RemoteBillPrintInvoiceRecord, permission: 'REVENUE:REMOTE_WATER_METER_MANAGEMENT:BILL_PRINT_INVOICE_RECORD' }, // 账单发票打印记录
	{ path: '/remoteWaterMeterManagement/data', component: RemoteData, permission: 'REVENUE:REMOTE_WATER_METER_MANAGEMENT:REMOTE_TABLE_DATA' }, // 远传表数据
	{ path: '/remoteWaterMeterManagement/abnormal', component: RemoteAbnormal, permission: 'REVENUE:REMOTE_WATER_METER_MANAGEMENT:REMOTE_TABLE_ABNORMAL' }, // 远传表异常数据
	// 机械表管理
	{ path: '/mechanicalWatchManagement/users', component: MechanicalUsers, permission: 'REVENUE:MECHANICAL_WATER_METER_MANAGEMENT:USERS' }, // 机械表用户
	{ path: '/mechanicalWatchManagement/amr', component: MechanicalAmr, permission: 'REVENUE:MECHANICAL_WATER_METER_MANAGEMENT:READING_TASK' }, // 抄表任务
	{ path: '/mechanicalWatchManagement/bill', component: MechanicalBill, permission: 'REVENUE:MECHANICAL_WATER_METER_MANAGEMENT:BILL' }, // 机械表账单
	{ path: '/mechanicalWatchManagement/billAdjustmentRecord', component: MechanicalBillAdjustmentRecord, permission: 'REVENUE:MECHANICAL_WATER_METER_MANAGEMENT:BILL_ADJUSTMENT_RECORD' }, // 账单调整核减记录
	{ path: '/mechanicalWatchManagement/billMinusRecord', component: MechanicalBillMinusRecord, permission: 'REVENUE:MECHANICAL_WATER_METER_MANAGEMENT:BILL_REDUCTION_RECORD' }, // 账单调整核减记录
	{ path: '/mechanicalWatchManagement/billPrintInvoiceRecord', component: MechanicalBillPrintInvoiceRecord, permission: 'REVENUE:MECHANICAL_WATER_METER_MANAGEMENT:BILL_PRINT_INVOICE_RECORD' }, // 账单发票打印记录
	{ path: '/mechanicalWatchManagement/track', component: MechanicalTrack, permission: 'REVENUE:MECHANICAL_WATER_METER_MANAGEMENT:METER_TRAJECTORY' }, // 抄表轨迹
	// 表务管理
	{ path: '/waterMeterManagement/list', component: WaterMeterManagement, permission: 'REVENUE:WATER_METER_MANAGEMENT:WATER_METER_INFO' }, // 水表信息
	{ path: '/waterMeterManagement/waterMeterCRD', component: WaterMeterCRD, permission: 'REVENUE:WATER_METER_MANAGEMENT:REPLACE_DISMANTLE_REFILL' }, // 换表/拆表/复装
	{ path: '/waterMeterManagement/waterMeterChange', component: UserWaterMeterChange, permission: 'REVENUE:WATER_METER_MANAGEMENT:IN_TABLE_RECORD' }, // 换表记录
	{ path: '/waterMeterManagement/dismantle', component: UserDismantle, permission: 'REVENUE:WATER_METER_MANAGEMENT:DOWN_TABLE_RECORD' }, // 拆表记录
	{ path: '/waterMeterManagement/reload', component: UserWaterMeterReload, permission: 'REVENUE:WATER_METER_MANAGEMENT:REFILL_RECORD' }, // 复装记录
	// 系统设置
	{ path: '/sysSettings/transcribeCycle', component: SysTranscribeCycle, permission: 'REVENUE:SYSTEM_SETTINGS:METER_READING_CYCLE_CONFIG' }, // 抄表周期配置
	{ path: '/sysSettings/transcriber', component: SysTranscriber, permission: 'REVENUE:SYSTEM_SETTINGS:METER_READER_MANAGEMENT' }, // 抄表员管理
	{ path: '/sysSettings/audit', component: SysAudit, permission: 'REVENUE:SYSTEM_SETTINGS:APPROVAL_FLOW_CONFIG' }, // 审批流配置
	{ path: '/sysSettings/auditRole', component: SysAuditRole, permission: 'REVENUE:SYSTEM_SETTINGS:APPROVAL_FLOW_ROLE_CONFIG' }, // 审批流角色配置
	{ path: '/sysSettings/banks', component: SysBanks, permission: 'REVENUE:SYSTEM_SETTINGS:ACCOUNT_BANK_MANAGEMENT' }, // 开户银行管理
	{ path: '/sysSettings/bankInfo', component: SysBankInfo, permission: 'REVENUE:SYSTEM_SETTINGS:BANK_ACCOUNT_MANAGEMENT' }, // 银行账户管理
	{ path: '/sysSettings/taxInfo', component: SysTaxInfo, permission: 'REVENUE:SYSTEM_SETTINGS:TAX_INFO_MANAGEMENT' }, // 水务信息管理
	{ path: '/sysSettings/areas', component: Sysareas, permission: 'REVENUE:SYSTEM_SETTINGS:AREA_MANAGEMENT' }, // 片区管理
	{ path: '/sysSettings/staff', component: SysStaff, permission: 'REVENUE:SYSTEM_SETTINGS:STAFF_MANAGEMENT' }, // 员工管理
	{ path: '/sysSettings/roll', component: SysRoll, permission: 'REVENUE:SYSTEM_SETTINGS:ROLE_MANAGEMENT' }, // 角色管理
	{ path: '/sysSettings/depart', component: SysDepart, permission: 'REVENUE:SYSTEM_SETTINGS:DEPARTMENT_MANAGEMENT' }, // 部门管理
	// { path: '/sysSettings/departLeader', component: SysDepartLeader }, // 部门领导管理
	{ path: '/sysSettings/waterQuality', component: SysWaterQuality, permission: 'REVENUE:SYSTEM_SETTINGS:WATER_CLASSIFICATION' }, // 用水分类
	{ path: '/sysSettings/dictionary', component: SysDictionary, permission: 'REVENUE:SYSTEM_SETTINGS:DATA_DICTIONARY' }, // 数据字典
	{ path: '/sysSettings/parameters', component: SysParameters, permission: 'REVENUE:SYSTEM_SETTINGS:SYSTEM_PARAMETERS' }, // 系统参数
	{ path: '/sysSettings/modules', component: SysModules, permission: 'REVENUE:SYSTEM_SETTINGS:MODULE_MANAGEMENT' }, // 模块管理
	{ path: '/sysSettings/waterFile', component: SysWaterFile, permission: 'REVENUE:SYSTEM_SETTINGS:WATER_METER_FILE_MANAGEMENT' }, // 水价文件管理

		// 用水分类
	{ path: '/waterQuality/add', component: SysWaterQualityAdd, permission: 'REVENUE:SYSTEM_SETTINGS:WATER_CLASSIFICATION:ADD' }, // 新增- 用水分类
	{ path: '/waterQuality/edit', component: SysWaterQualityEdit, permission: 'REVENUE:SYSTEM_SETTINGS:WATER_CLASSIFICATION:EDIT' }, // 编辑 - 用水分类
	{ path: '/waterQuality/view', component: SysWaterQualityAdd, permission: 'REVENUE:SYSTEM_SETTINGS:WATER_CLASSIFICATION:VIEW' }, // 查看 - 用水分类
	// 票据管理
	{ path: '/billManagement/receipt', component: BillReceipt, permission: 'REVENUE:INSTRUMENT_MANAGEMENT:PAPER_RECIPIENTS' }, // 票据领用
	{ path: '/billManagement/receiptRecord', component: BillReceiptRecord, permission: 'REVENUE:INSTRUMENT_MANAGEMENT:RECIPIENTS_RECORD' }, // 领用记录
	{ path: '/billManagement/issuingRecord', component: BillIssuingRecord, permission: 'REVENUE:INSTRUMENT_MANAGEMENT:ISSUE_RECORD' }, // 开具记录
	{ path: '/billManagement/template', component: BillTemplate, permission: 'REVENUE:INSTRUMENT_MANAGEMENT:PAPER_TEMPLATE' }, // 票据模板
	// 消息管理
	{ path: '/messageManagement/sendSms', component: SendSms,permission: 'REVENUE:MESSAGE_MANAGEMENT:SEND_SMS'}, // 发送短信
	{ path: '/messageManagement/sendRecord', component: Message,permission:'REVENUE:MESSAGE_MANAGEMENT:SEND_MESSAGE_RECORD'},//发送记录
	{ path: '/news', component: News, permission: 1 }, // 消息中心
	{ path: '/messageManagement/sendTempRecord', component: MessageTemp,permission:'REVENUE:MESSAGE_MANAGEMENT:SEND_MESSAGE_TEMP_RECORD'},//发送临时记录
	// 公告消息
	{ path: '/announcementMessage/list', component: AnnouncementList, permission: 'REVENUE:NOTICE_MESSAGE:LIST' }, // 公告列表
	{ path: '/announcementMessage/publish', component: AnnouncementPublish, permission: 'REVENUE:NOTICE_MESSAGE:PUBLISH' }, // 发布公告
	{ path: '/announcementMessage/modify', component: AnnouncementModify, permission: 'REVENUE:NOTICE_MESSAGE:MODIFY' }, // 发布公告
	// 流程管理
	{ path: '/processManagement/apply', component: ProcessApply, permission: 'REVENUE:PROCESS_MANAGEMENT:APPLY' }, // 我的申请
	{ path: '/processManagement/approval', component: ProcessApproval, permission: 'REVENUE:PROCESS_MANAGEMENT:APPROVAL' }, // 我的审批
	{ path: '/processManagement/applyQuery', component: ProcessApplyQuery, permission: 'REVENUE:PROCESS_MANAGEMENT:APPLY_SEARCH' }, // 申请查询
  { path: '/processManagement/applyApproval', component: ProcessApplyApproval, permission: 'REVENUE:PROCESS_MANAGEMENT:APPLYAPPROVAL' }, //公众号申请审批

		//统计报表
	{ path: '/reportManagement/chargeReport', component: ChargeReport, permission: 'REVENUE:REPORT_MANAGEMENT:CHARGE_REPORT' }, // 水费报表
	{ path: '/reportManagement/waterAmountReport', component: WaterAmountReport, permission: 'REVENUE:REPORT_MANAGEMENT:WATER_AMOUNT_REPORT' }, // 水量报表
	{ path: '/reportManagement/oweReport', component: OweReport, permission: 'REVENUE:REPORT_MANAGEMENT:OWE_REPORT' }, // 欠费报表
	{ path: '/reportManagement/customerReport', component: CustomerReport, permission: 'REVENUE:REPORT_MANAGEMENT:CUSTOMER_REPORT' }, // 用户报表
	{ path: '/reportManagement/synthesizeReport', component: SynthesizeReport, permission: 'REVENUE:REPORT_MANAGEMENT:SYNTHESIZE_REPORT' }, // 综合报表
];
