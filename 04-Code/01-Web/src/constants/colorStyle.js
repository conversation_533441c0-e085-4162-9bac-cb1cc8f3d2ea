//打印状态
const printColorMap = new Map();
printColorMap.set('打印中', '#87d068');
printColorMap.set('已完成', '#108ee9');
printColorMap.set('作废', '#f50');
//状态
const statusColorMap = new Map();
statusColorMap.set('正常', '#87d068');
statusColorMap.set('作废', '#f50');
//申请状态
const applyStatusColorMap = new Map();
applyStatusColorMap.set('进行中', 'green');
applyStatusColorMap.set('已拒绝', 'red');
applyStatusColorMap.set('已完成', 'blue');
applyStatusColorMap.set('待回填', 'gold');
//申请状态(退款)
const applyStatusTkColorMap = new Map();
applyStatusTkColorMap.set('审批中', '#87d068');
applyStatusTkColorMap.set('退款失败', '#f50');
applyStatusTkColorMap.set('已退款', '#108ee9');
applyStatusTkColorMap.set('待退款', '#d1ba0d');
//账单状态
const billStatusColorMap = new Map();
billStatusColorMap.set('结清', '#87d068');
billStatusColorMap.set('部分结清', '#d1ba0d');
billStatusColorMap.set('未结清', '#f5a623');
billStatusColorMap.set('作废', '#f50');
billStatusColorMap.set('红冲', '#f50');
//抄表状态状态
const transcribeStatusColorMap = new Map();
transcribeStatusColorMap.set('未抄表', '#87d068');
transcribeStatusColorMap.set('已抄表', '#108ee9');
transcribeStatusColorMap.set('已过期', '#d1ba0d');
transcribeStatusColorMap.set('作废', '#f50');
//用户状态
const userStatusColorMap = new Map();
userStatusColorMap.set('正常', '#87d068');
userStatusColorMap.set('异常', '#f50');
userStatusColorMap.set('欠费拆表', '#f50');
userStatusColorMap.set('销户', '#f50');
//水表状态
const waterMeterStatusColorMap = new Map();
waterMeterStatusColorMap.set('库存', 'green');
waterMeterStatusColorMap.set('预开户', 'lime');
waterMeterStatusColorMap.set('使用中', 'blue');
waterMeterStatusColorMap.set('返修', 'red');
waterMeterStatusColorMap.set('失窃', 'red');
waterMeterStatusColorMap.set('废弃', 'red');
//产权人状态
const ownStatusColorMap = new Map();
ownStatusColorMap.set('已生效', '#87d068');
ownStatusColorMap.set('未生效', '#f50');
//订单状态
const orderStatusColorMap = new Map();
orderStatusColorMap.set('已完成', '#87d068');
orderStatusColorMap.set('退款', '#f50');
orderStatusColorMap.set('红冲', '#f50');
orderStatusColorMap.set('已支付待刷卡', '#108ee9');
//特抄收费状态
const specialStatusColorMap = new Map();
specialStatusColorMap.set('待收费', '#d1ba0d');
specialStatusColorMap.set('核减中', '#d1ba0d');
specialStatusColorMap.set('已完成', '#87d068');
specialStatusColorMap.set('作废', '#dc910b');
specialStatusColorMap.set('红冲', '#f50');
specialStatusColorMap.set('待退款', '#1251cd');
//阀门状态
const valveStatusColorMap = new Map();
valveStatusColorMap.set('开阀', '#87d068');
valveStatusColorMap.set('关阀', '#f50');
valveStatusColorMap.set('半悬', '#8c8c8c');
//低保户状态
const lowStatusColorMap = new Map();
lowStatusColorMap.set('正常', '#87d068');
lowStatusColorMap.set('已过期', '#f50');
lowStatusColorMap.set('未生效', '#8c8c8c');
//终端机户状态
const terminalStatusColorMap = new Map();
terminalStatusColorMap.set('有效', '#87d068');
terminalStatusColorMap.set('无效', '#f50');
//提现状态
const withdrawStatusColorMap= new Map();
withdrawStatusColorMap.set('审批中', '#108ee9');
withdrawStatusColorMap.set('待提现', '#d1ba0d');
withdrawStatusColorMap.set('已提现', '#87d068');
withdrawStatusColorMap.set('提现失败', '#f50');
//销户状态
const cancellationStatusColorMap= new Map();
cancellationStatusColorMap.set('待销户', '#108ee9');
cancellationStatusColorMap.set('销户', '#f50');
//开票资料状态
const taxStatusColorMap = new Map();
taxStatusColorMap.set('启用', '#87d068');
taxStatusColorMap.set('停用', '#f50');
//开票资料状态
const withholdingStatusColorMap = new Map();
withholdingStatusColorMap.set('启用', '#87d068');
withholdingStatusColorMap.set('禁用', '#f50');
//开票资料状态
const sendColorMap = new Map();
sendColorMap.set('发送成功', '#87d068');
sendColorMap.set('发送失败', '#f50');
sendColorMap.set('发送中', '#d1ba0d');
const reviewStatusColorMap = new Map();
reviewStatusColorMap.set('同意', '#87d068');
reviewStatusColorMap.set('拒绝', '#f50');
reviewStatusColorMap.set('待审批', '#d1ba0d');
export {
	printColorMap,
	statusColorMap,
	applyStatusColorMap,
	billStatusColorMap,
	transcribeStatusColorMap,
	userStatusColorMap,
	waterMeterStatusColorMap,
	ownStatusColorMap,
	orderStatusColorMap,
	specialStatusColorMap,
	valveStatusColorMap,
	lowStatusColorMap,
	terminalStatusColorMap,
	withdrawStatusColorMap,
	cancellationStatusColorMap,
	taxStatusColorMap,
	withholdingStatusColorMap,
	sendColorMap,
	applyStatusTkColorMap,
	reviewStatusColorMap
};
