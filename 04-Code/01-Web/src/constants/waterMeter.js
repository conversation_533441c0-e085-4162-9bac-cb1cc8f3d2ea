// 水表类型
const WATER_METER_TYPE = [
	{
		label: '请选择',
		value: null
	},
	{
		label: 'IC卡表',
		value: 0
	},
	{
		label: '机械表',
		value: 1
	},
	{
		label: '远传表',
		value: 2
	}
];
// 水表种类
const WATER_METER_KIND = [
	{
		label: '请选择',
		value: null
	},
	{
		label: '机械表',
		value: 0
	},
	{
		label: '有线远传',
		value: 1
	},
	{
		label: '无线远传',
		value: 2
	},
	{
		label: '预付费2',
		value: 3
	},
	{
		label: '预付费5',
		value: 4
	},
	// {
	// 	label: '阶梯2',
	// 	value: 5
	// },
	// {
	// 	label: '阶梯5',
	// 	value: 6
	// },
	// {
	// 	label: '预付费4442',
	// 	value: 7
	// },
	// {
	// 	label: '无线远传-带天线',
	// 	value: 8
	// },
	// {
	// 	label: '卡转机械表2',
	// 	value: 9
	// },
	// {
	// 	label: '卡转机械表5',
	// 	value: 10
	// },
	{
		label: '泰安卡表',
		value: 13
	},
	{
		label: '新天卡表',
		value: 11
	},
];
// 机械表水表种类
const MECHANICAL_WATER_METER_KIND = [
	{
		label: '机械表',
		value: 0
	}
];
// 卡表水表种类
const IC_CARD_WATER_METER_KIND = [
	{
		label: '阶梯2',
		value: 5
	},
	{
		label: '阶梯5',
		value: 6
	},
];
// 卡表查表任务水表种类
const IC_CARD_WATER_METER_KIND_CHECK_METER = [
	{
		label: '阶梯2',
		value: 5
	},
	{
		label: '阶梯5',
		value: 6
	}
];
// 水表种类
const FAR_PASS_WATER_METER_KIND = [
	{
		label: '请选择',
		value: null
	},
	{
		label: '有线远传',
		value: 1
	},
	{
		label: '无线远传',
		value: 2
	}
];
// 水表厂家
const WATER_METER_MANUFACTURER = [
	{
		label: '请选择',
		value: null
	},
	{
		label: '扬州恒信',
		value: 0
	},
	{
		label: '江西三川',
		value: 2
	},
	{
		label: '江宏',
		value: 14
	},

	{
		label: '泰安',
		value: 15
	},

	{
		label: '河南新天',
		value: 16
	},
	{
		label: '宁夏隆基',
		value: 17
	},
];
// 水表口径
const WATER_METER_CALIBER = [
	{
		label: '请选择',
		value: null
	},
	{
		label: '15',
		value: '15'
	},
	{
		label: '20',
		value: '20'
	},
	{
		label: '25',
		value: '25'
	},
	{
		label: '40',
		value: '40'
	},
	{
		label: '50',
		value: '50'
	},
	{
		label: '80',
		value: '80'
	},
	{
		label: '100',
		value: '100'
	},
	{
		label: '110',
		value: '110'
	},
	{
		label: '150',
		value: '150'
	},
	{
		label: '200',
		value: '200'
	},
	{
		label: '300',
		value: '300'
	},
	{
		label: '400',
		value: '400'
	},
	{
		label: '500',
		value: '500'
	}

];
// 阀门状态
const VALVE_STATUE = [
	{
		label: '全部',
		value: -1
	},
	{
		label: '开阀',
		value: 0
	},
	{
		label: '关阀',
		value: 1
	},
	{
		label: '半悬',
		value: 2
	},
	{
		label: '无阀',
		value: 3
	},
];

const VALVE_STATUES = [
	{
		label: '开阀',
		value: 0
	},
	{
		label: '关阀',
		value: 1
	},
	{
		label: '半悬',
		value: 2
	},
	{
		label: '无阀',
		value: 3
	},
];
// 水表状态
const WATER_METER_STATUS = [
	{
		label: '请选择',
		value: null
	},
	{
		label: '库存',
		value: 0
	},
	{
		label: '预开户',
		value: 1
	},
	{
		label: '使用中',
		value: 2
	},
	{
		label: '返修',
		value: 3
	},
	{
		label: '废弃',
		value: 4
	},
	{
		label: '失窃',
		value: 5
	},
	{
		label: '已删除',
		value: 6
	}
];
// 拆表类型
const DISMANTLE_TYPE = [
	{
		label: '请选择',
		value: null
	},
	{
		label: '欠费拆表',
		value: 0
	},
	{
		label: '违章拆表',
		value: 1
	},
	{
		label: '其他拆表',
		value: 2
	},
	{
		label: '关阀停水',
		value: 3
	}
];
// 换表时旧表状态
const CHANGE_METER_STATUS = [
	{
		label: '请选择',
		value: null
	},
	{
		label: '返修',
		value: 3
	},
	{
		label: '废弃',
		value: 4
	},
	{
		label: '失窃',
		value: 5
	}
];
// 水表异常原因
const WATER_METER_ABNORMAL_TYPE = [
	{
		label: '请选择',
		value: null
	},
	{
		label: '倒装',
		value: 0
	},
	{
		label: '用水量过大',
		value: 1
	},
	{
		label: '30天及以上未上传数据',
		value: 2
	},
	{
		label: '用水量过小',
		value: 3
	},
	{
		label: '居民水量过大',
		value: 4
	},
	{
		label: '非居民水量过大',
		value: 5
	},
	{
		label: '有线表通讯异常',
		value: 6
	},
];
// 水表异常状态
const WATER_METER_ABNORMAL_STATUS = [
	{
		label: '请选择',
		value: null
	},
	{
		label: '未处理',
		value: 0
	},
	{
		label: '已处理',
		value: 1
	},
	{
		label: '待处理',
		value: 2
	},
];
export {
	WATER_METER_TYPE,
	WATER_METER_KIND,
	IC_CARD_WATER_METER_KIND,
	FAR_PASS_WATER_METER_KIND,
	WATER_METER_MANUFACTURER,
	WATER_METER_CALIBER,
	VALVE_STATUE,
	VALVE_STATUES,
	WATER_METER_STATUS,
	DISMANTLE_TYPE,
	CHANGE_METER_STATUS,
	WATER_METER_ABNORMAL_TYPE,
	WATER_METER_ABNORMAL_STATUS,
	MECHANICAL_WATER_METER_KIND,
	IC_CARD_WATER_METER_KIND_CHECK_METER
};
