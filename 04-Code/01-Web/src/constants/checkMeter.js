// 查表任务状态
const CHECK_METER_TASK_STATUS = [
	{
		label: '未查表',
		value: 0
	},
	{
		label: '已查表',
		value: 1
  },
  {
		label: '已过期',
		value: 2
  },
  {
		label: '作废',
		value: 3
  }
];
// 查表任务结果
const CHECK_METER_TASK_RESULT = [
	{
		label: '全部',
		value: null
	},
	{
		label: '正常',
		value: 0
	},
	/*{
		label: '水量丢失',
		value: 1
  },
  {
		label: '水量用超',
		value: 2
	}*/
	{
		label: '水表倒装',
		value: 3
	},
	{
		label: '欠费',
		value: 4
	},
	{
		label: '表内剩余金额大于总购水金额',
		value: 5
	},
	{
		label: '表内累计金额大于总购水金额',
		value: 6
	},
	{
		label: '表内累计金额小于总购水金额',
		value: 7
	},
	{
		label: '表内剩余金额大于计算剩余金额',
		value: 8
	},
	{
		label: '表内剩余金额小于计算剩余金额',
		value: 9
	},
	{
		label: '未入住',
		value: 10
	},
	{
		label: '用水不计费',
		value: 11
	},
	{
		label: '手动选择异常',
		value: 12
	},
];

export { CHECK_METER_TASK_STATUS,CHECK_METER_TASK_RESULT};
