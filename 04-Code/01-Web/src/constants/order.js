// 订单来源
const ORDER_SOURCE = [
  {
    label: '请选择',
    value: null
  },
  {
    label: '柜台',
    value: 0
  },
  {
    label: '微信',
    value: 1
  },
  {
    label: '小额借记',
    value: 2
  },
  {
    label: '代扣',
    value: 3
  },
  {
    label: '红冲',
    value: 4
  },
  {
    label: '换表',
    value: 5
  },
  {
    label: '拆表',
    value: 6
  },
  {
    label: '复装',
    value: 7
  },
  {
    label: '销户',
    value: 8
  },
  {
    label: '提现',
    value: 9
  },
	{
		label: '终端机',
		value: 10
	},
	{
		label: 'APP',
		value: 11
	},
	{
		label: '开户',
		value: 12
	},
	{
		label: '银行',
		value: 13
	},
{
		label: '补水',
		value: 14
},
];
// 付款方式
const CHARGE_WAY = [
  {
    label: '请选择',
    value: null
  },
  {
    label: '现金',
    value: 0
  },
  {
    label: '微信支付',
    value: 1
  },
  {
    label: 'POS机',
    value: 2
  },
  {
    label: '电汇',
    value: 3
  },
  {
    label: '小额借记',
    value: 4
  },
  {
    label: '代扣',
    value: 5
  },
  {
    label: '无',
    value: 6
  },
  {
    label: '终端机',
    value: 7
  },
  {
    label: '开户',
    value: 8
  },
  {
    label: '建设银行',
    value: 9
  },
	{
		label: '转账',
		value: 10
	},
		{
				label: '光大银行',
				value: 11
		},
		{
				label: '邮储生活缴费',
				value: 12
		},
	{
		label: '农业银行',
		value: 13
	},
	{
		label: '中行',
		value: 14
	},
];
// 付款方式
const CHARGE_WAYSPECIAL= [
	{
		label: '请选择',
		value: null
	},
	{
		label: '现金',
		value: 0
	},
	{
		label: '微信支付',
		value: 1
	},
	{
		label: 'POS机',
		value: 2
	},
];
// 订单状态
const ORDER_STATUS = [
  {
    label: '请选择',
    value: null
  },
  {
    label: '已退款',
    value: 0
  },
  {
    label: '已支付待刷卡',
    value: 1
  },
  {
    label: '已完成',
    value: 2
  },
  {
    label: '红冲',
    value: 3
  }
];
// 打印方式
const PRINT_TYPE = [
  {
    label: '请选择',
    value: null
  },
  {
    label: '未打印',
    value: 0
  },
  {
    label: '合并打印',
    value: 1
  },
  {
    label: '清污分离打印',
    value: 2
  }
];
// 是否开税票
const TAX_INVOICE = [
  {
    label: '请选择',
    value: null
  },
  {
    label: '否',
    value: 0
  },
  {
    label: '是',
    value: 1
  }
];
// 退款状态
const REFUND_STATUS = [
  {
    label: '请选择',
    value: null
  },
  {
    value: 0,
    label: '审批中'
  },
  {
    value: 1,
    label: '待退款'
  },
  {
    value: 2,
    label: '已退款'
  },
  {
    value: 3,
    label: '退款失败'
  },
	{
		value: 4,
		label: '退款中'
	},
	{
		value: 5,
		label: '退款异常'
	},
];

// 第三方支付记录状态
const CHARGE_THIRD_PART_ORDER_STATUS_MAP = [
		{
				label: '请选择',
				value: null
		},
		{
				label: '正常',
				value: 0
		},
		{
				label: '异常',
				value: 1
		},
		{
				label: '已退款',
				value: 2
		},
		{
				label: '退款中',
				value: 3
		},
		{
				label: '退款异常',
				value: 4
		},
		{
				label: '未知',
				value: 5
		},
		{
			label: '已处理',
			value: 6
		},
]

// 第三方支付记录状态
const CHARGE_RESULT_MAP = [
		{
				label: '请选择',
				value: null
		},
		{
				label: '成功',
				value: 0
		},
		{
				label: '失败',
				value: 1
		},
]

// 申请状态(3个参数)
const APPLY_STATUS_BY_OTHER = [
  { label: '请选择', value: null },
  { label: '进行中', value: 1 },
  { label: '已拒绝', value: 2 },
  { label: '已完成', value: 4 }
];
export { ORDER_SOURCE, CHARGE_WAY, ORDER_STATUS, PRINT_TYPE, TAX_INVOICE, REFUND_STATUS, APPLY_STATUS_BY_OTHER
		,CHARGE_WAYSPECIAL, CHARGE_THIRD_PART_ORDER_STATUS_MAP, CHARGE_RESULT_MAP};
