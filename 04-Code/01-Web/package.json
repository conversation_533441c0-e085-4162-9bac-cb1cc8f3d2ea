{"name": "reacthq", "version": "1.1.3", "private": true, "scripts": {"start:devOut": "cross-env ENV=devOut node scripts/start.js", "start:dev": "cross-env ENV=dev node scripts/start.js", "start:localhost": "cross-env ENV=localhost node scripts/start.js", "start": "node scripts/start.js", "build": "cross-env ENV=dev node --max_old_space_size=4096 scripts/build.js", "build:devOut": "cross-env ENV=devOut node --max_old_space_size=4096 scripts/build.js", "build:dev": "cross-env ENV=dev node --max_old_space_size=4096 scripts/build.js", "build:prodIn": "cross-env ENV=prodIn node --max_old_space_size=4096 scripts/build.js", "build:prodOut": "cross-env ENV=prodOut node --max_old_space_size=4096 scripts/build.js"}, "dependencies": {"add": "2.0.6", "antd": "3.19.6", "axios": "0.19.0", "decimal.js": "10.3.1", "copy-to-clipboard": "3.2.0", "core-js": "^3.6.4", "draft-js": "^0.11.2", "draftjs-to-html": "0.8.4", "draftjs-to-markdown": "0.5.1", "echarts": "4.2.1", "for-editor": "0.2.6", "html-to-draftjs": "1.4.0", "identity-obj-proxy": "3.0.0", "is-wsl": "1.1.0", "qs": "^6.9.1", "query-string": "6.9.0", "rc-print": "^1.0.4", "react": "16.8.6", "react-amap": "^1.2.8", "react-custom-scrollbars": "4.2.1", "react-dev-utils": "9.0.1", "react-dom": "16.8.6", "react-draft-wysiwyg": "1.13.2", "react-hot-loader": "4.11.1", "react-particles-js": "2.6.0", "react-redux": "7.1.0", "react-router-dom": "5.0.1", "react-transition-group": "4.1.1", "react-zmage": "^0.8.5", "redux": "4.0.1", "redux-immutable": "4.0.0", "redux-logger": "3.0.6", "redux-thunk": "2.3.0", "resolve": "1.10.0", "semver": "6.0.0", "ts-pnp": "1.1.2", "bizcharts": "3.1.0", "@antv/data-set": "0.8.9"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all", "ie >= 10"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version", "ie >= 10"]}, "jest": {"collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts"], "setupFiles": ["react-app-polyfill/jsdom"], "setupFilesAfterEnv": [], "testMatch": ["<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}", "<rootDir>/src/**/*.{spec,test}.{js,jsx,ts,tsx}"], "testEnvironment": "jest-environment-jsdom-fourteen", "transform": {"^.+\\.(js|jsx|ts|tsx)$": "<rootDir>/node_modules/babel-jest", ".+\\.css$": "<rootDir>/config/jest/cssTransform.js", "^(?!.*\\.(js|jsx|ts|tsx|css|json)$)": "<rootDir>/config/jest/fileTransform.js"}, "transformIgnorePatterns": ["[/\\\\]node_modules[/\\\\].+\\.(js|jsx|ts|tsx)$", "^.+\\.module\\.(css|sass|scss)$"], "modulePaths": [], "moduleNameMapper": {"^react-native$": "react-native-web", "^.+\\.module\\.(css|sass|scss)$": "identity-obj-proxy"}, "moduleFileExtensions": ["web.js", "js", "web.ts", "ts", "web.tsx", "tsx", "json", "web.jsx", "jsx", "node"], "watchPlugins": ["jest-watch-typeahead/filename", "jest-watch-typeahead/testname"]}, "babel": {"presets": [["@babel/preset-env", {"targets": {"browsers": ["last 2 versions", "ie >= 10"]}, "corejs": "2", "useBuiltIns": "usage"}], "@babel/react"], "plugins": [["@babel/plugin-transform-template-literals", {"loose": true}], ["@babel/plugin-transform-runtime", {"corejs": "2"}], ["@babel/plugin-proposal-class-properties", {"loose": true}], ["import", {"libraryName": "antd", "style": "css"}], ["react-hot-loader/babel"]]}, "devDependencies": {"@babel/core": "7.4.3", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-transform-classes": "^7.8.6", "@babel/plugin-transform-react-jsx-self": "7.2.0", "@babel/plugin-transform-react-jsx-source": "7.2.0", "@babel/plugin-transform-runtime": "^7.8.3", "@babel/plugin-transform-template-literals": "^7.8.3", "@babel/polyfill": "^7.8.7", "@babel/preset-env": "7.7.6", "@babel/preset-react": "^7.8.3", "@babel/preset-stage-0": "^7.8.3", "@babel/runtime": "^7.8.7", "@babel/runtime-corejs2": "^7.8.7", "@hot-loader/react-dom": "16.8.6", "@svgr/webpack": "4.1.0", "@typescript-eslint/eslint-plugin": "1.6.0", "@typescript-eslint/parser": "1.6.0", "babel-eslint": "10.0.1", "babel-jest": "24.8.0", "babel-loader": "8.0.5", "babel-plugin-import": "1.12.0", "babel-plugin-named-asset-import": "0.3.2", "camelcase": "5.2.0", "case-sensitive-paths-webpack-plugin": "2.2.0", "cross-env": "^7.0.3", "css-loader": "2.1.1", "dotenv": "6.2.0", "dotenv-expand": "4.2.0", "eslint": "5.16.0", "eslint-config-react-app": "4.0.1", "eslint-loader": "2.1.2", "eslint-plugin-flowtype": "2.50.1", "eslint-plugin-import": "2.16.0", "eslint-plugin-jsx-a11y": "6.2.1", "eslint-plugin-react": "7.12.4", "eslint-plugin-react-hooks": "1.5.0", "file-loader": "3.0.1", "fs-extra": "^8.1.0", "html-webpack-plugin": "^4.0.0-beta.11", "jest": "24.7.1", "jest-environment-jsdom-fourteen": "0.1.0", "jest-resolve": "24.7.1", "jest-watch-typeahead": "0.3.0", "mini-css-extract-plugin": "0.5.0", "node-sass": "^4.12.0", "optimize-css-assets-webpack-plugin": "5.0.1", "pnp-webpack-plugin": "1.2.1", "postcss-flexbugs-fixes": "4.1.0", "postcss-loader": "3.0.0", "postcss-normalize": "7.0.1", "postcss-preset-env": "6.6.0", "postcss-safe-parser": "4.0.1", "sass-loader": "^7.1.0", "style-loader": "0.23.1", "terser-webpack-plugin": "1.2.3", "url-loader": "1.1.2", "webpack": "^4.29.6", "webpack-dev-server": "3.2.1", "webpack-manifest-plugin": "2.0.4", "workbox-webpack-plugin": "4.2.0", "yarn": "1.16.0"}}