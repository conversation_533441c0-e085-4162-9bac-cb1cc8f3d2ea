import React, {Component} from 'react';
import E from 'wangeditor';
import {url} from '../URL';

export default class WangEditor extends Component {
    componentDidMount() {
        const { setHtmlContent } = this.props;
        // 创建富文本
        const editor = new E(this.node);
        // 获取最新的 html 内容
        editor.config.onchange = (newHtml) => {
            setHtmlContent(newHtml);
        };
        // 编辑区域 focus（聚焦）和 blur（失焦）时触发的回调函数
        editor.config.onblur = (newHtml) => {};
        editor.config.onfocus = (newHtml) => {};
        // 配置 server 图片接口地址
        editor.config.uploadImgServer = `${url}revenue/upload`;
        // 回调函数
        editor.config.uploadImgHooks = {
            // 图片上传并返回了结果，想要自己把图片插入到编辑器中
            // 例如服务器端返回的不是 { errno: 0, data: [...] } 这种格式，可使用 customInsert
            customInsert: function (insertImgFn, result) {
                // result 即服务端返回的接口
                // insertImgFn 可把图片插入到编辑器，传入图片 src ，执行函数即可
                insertImgFn(`${url}${result.datas.src}`);
            },
        };
        editor.config.uploadFileName = 'file';
        // 不显示的按钮
        editor.config.excludeMenus = ['emoticon'];
        // 限制上传图片大小 2M
        editor.config.uploadImgMaxSize = 2 * 1024 * 1024;
        // 限制图片类型
        editor.config.uploadImgAccept = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
        editor.create();
    }
    render() {
        // const { editorContent } = this.state; // 把值传给父组件
        return (
            <div
                ref={(wangeDitor) => {
                    this.node = wangeDitor;
                }}
            />
        );
    }
}
