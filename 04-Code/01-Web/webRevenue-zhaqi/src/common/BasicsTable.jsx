import React, {Fragment} from 'react'
import {Pagination, Table} from "@icedesign/base";

const BasicsTable = (props) => {
  return (
    <Fragment>
      <Table dataSource={props.dataSource} isLoading={props.dataLoading}
             rowSelection={props.rowSelection ? {
               ...props.rowSelection,
               selectedRowKeys: props.selectedRowKeys
             } : void (0)}>
        {
          props.columns.map((item) => {
            return <Table.Column key={item.key} title={item.title} dataIndex={item.dataIndex} cell={item.cell}
                                 align="center" width={item.width}/>
          })
        }
      </Table>
      <div style={{display: 'flex', justifyContent: 'flex-end'}}>
        <Pagination
          style={{marginTop: 15}}
          pageSizeSelector="dropdown"
          onChange={(value) => props.changePage(value)}
          onPageSizeChange={(value) => props.onPageSizeChange(value)}
          total={props.total}
          pageSize={props.pageSize}
          current={props.page}
          size="small"
          pageSizeList={props.pageSizeList}
        />
        <div style={{marginTop: 17, marginLeft: 10}}>共 {props.total} 条记录</div>
      </div>
    </Fragment>
  )
}
export default BasicsTable
