import React, { Component } from 'react';
import {
  Input,
  Button,
  Icon,
  Grid,
  Table,
  Pagination,
  Form,
  Field,
  Feedback,
  Select,
  Dialog,
  Tag
} from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import qs from 'qs';
import { url } from '../../../components/URL/index'
import { LoginURL } from '../../../components/URL/LoginURL'

const FormItem = Form.Item;
const { Combobox } = Select;
const { Row, Col } = Grid;

export default class MachineryMeterUser extends Component {
  constructor(props) {
    super(props);
    this.state = {
      areaList: [],  //片区
      regionList: [], //小区
      feeNameList: [], //用水性质
      formSource: [],//表格数据
      roleNameList: [], //查询操作员
      page: 1,
      pageSize: 10,
      totalSize: 0,
      dataLoading: false,
      roleName: [],
      areaCopy: null,
      createId: sessionStorage.getItem("stuffId"),
      createName: sessionStorage.getItem("realName"),
      loginURL: LoginURL,
      visible: false, // 修改水量区间
      selectRows: {}
    }
    this.field = new Field(this, { autoUnmount: true });
  }

  componentWillMount() {
    this.queryFeeName()
    this.queryArea()
    this.queryRegion()
    this.queryRoleName()
    this.queryCustomer(1, 10) //查询机械表用户
  }

  //查询机械表用户
  queryCustomer(page, pageSize) {
    this.setState({ dataLoading: true });
    let values = this.field.getValues();
    values.page = page;
    values.pageSize = pageSize;
    axios({
      method: 'post',
      url: `${url}/revenue/copyTask/queryCustomer`,
      data: qs.stringify(values),
    })
      .then((response) => {
        this.setState({
          formSource: response.data.datas,
          page: page,
          pageSize: pageSize,
          totalSize: response.data.totalSize,
          dataLoading: false
        })
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
        this.setState({ dataLoading: false, })
      })
  }

  //查询片区
  queryArea() {
    axios({
      method: 'get',
      url: url + 'revenue/area/getAll'
    })
      .then((response) => {
        let areaList = []
        response.data.datas.map((item) => {
          areaList.push({ label: item.name, value: item.id })
        })
        this.setState({ areaList: areaList })
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
      })
  }

  //查询小区
  queryRegion() {
    axios({
      method: 'post',
      url: `${url}revenue/region/getRegionListJiXie`,
      //data: qs.stringify(areaId)
    }).then(response => {
      let regionList = []
      response.data.datas.map((item) => {
        regionList.push({ label: item.regionName, value: item.id })
      })
      this.setState({ regionList: regionList })

    }).catch(error => {
      Feedback.toast.error("请求异常", error);
    })
  }

  //查询用水性质
  queryFeeName() {
    axios({
      method: 'post',
      url: `${url}revenue/fee/queryList`,
    })
      .then((response) => {
        let feeNameList = []
        response.data.datas.map((item) => {
          feeNameList.push({ label: item.name, value: item.id })
        })
        this.setState({ feeNameList: feeNameList })
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
      })
  }

  //查询操作员
  queryRoleName() {
    axios({
      method: 'get',
      url: `${url}revenue/staff/getAllCname` + '?n=' + Math.random(),
    })
      .then((response) => {
        if (response.data.code == 0) {
          let roleNameList = []
          response.data.datas.map((item) => {
            roleNameList.push({label: item.label, value: item.label})
          })
          this.setState({roleNameList: roleNameList})
        }
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
      })
  }

  //翻页
  changePage(page) {
    const { pageSize } = this.state;
    this.queryCustomer(page, pageSize);
    window.scrollTo(0, 500)
  }

  //改变每页显示
  changePageSize(pageSize) {
    this.queryCustomer(1, pageSize)
  }

  /*表格操作栏*/
  renderOper(value, index, record) {
    return (
      <div>
        <Button title="修改" type="primary" onClick={() => { this.viewModify(record) }} ><Icon type="edit" /></Button>
      </div>
    )
  }

  viewModify = (record) => {
    this.setState({
      visible: true,
      selectRows: record,
    })
  }

  //重置
  reset() {
    this.field.reset()
    this.setState({ areaCopy: null })
  }

  //片区onchange
  onChange(value, type) {
    this.field.setValue('areaId', value);
    //区册查询
    axios({
      method: 'post',
      url: `${url}revenue/region/getRegionListJiXie`,
      data: qs.stringify({ areaId: value })
    }).then(response => {
      let regionList = []
      response.data.datas.map((item) => {
        regionList.push({ label: item.regionName, value: item.id })
      })
      this.setState({ regionList: regionList })
    }).catch(error => {
      Feedback.toast.error("请求异常", error)
    })
    if (type == 0) {
      this.field.reset('regionId')
    }
  }

  // 导出用户信息
  downLoadUserInfo() {
    let values = this.field.getValues();
    let url1 = `${url}revenue/copyTask/exportCustomer?1=1`;
    if (values.cno) {
      url1 += '&cno=' + values.cno;
    }
    if (values.watermeterId) {
      url1 += '&watermeterId=' + values.watermeterId;
    }
    if (values.address) {
      url1 += '&address=' + values.address;
    }
    if (values.daysFromLastAccountMin) {
      url1 += '&daysFromLastAccountMin=' + values.daysFromLastAccountMin;
    }
    if (values.daysFromLastAccountMax) {
      url1 += '&daysFromLastAccountMax=' + values.daysFromLastAccountMax;
    }
    if (values.hno) {
      url1 += '&hno=' + values.hno;
    }
    if (values.areaId) {
      url1 += '&areaId=' + values.areaId;
    }
    if (values.status) {
      url1 += '&status=' + values.status;
    }
    if (values.feeId) {
      url1 += '&feeId=' + values.feeId;
    }
    if (values.cname) {
      url1 += '&cname=' + values.cname;
    }
    if (values.regionId) {
      url1 += '&regionId=' + values.regionId;
    }
    if (values.copyName) {
      url1 += '&copyName=' + values.copyName;
    }
    window.open(encodeURI(url1), 'about:blank');
  }


  /*表格状态栏*/
  renderStatus(value) {
    if (value == 0) {
      return (
        <Tag shape="readonly" className="user-status-0">销户</Tag>
      )
    } else if (value == 1) {
      return (
        <Tag shape="readonly" className="user-status-1">正常</Tag>
      )
    } else if (value == 2) {
      return (
        <Tag shape="readonly" className="user-status-3">非正常</Tag>
      )
    } else if (value == 3) {
      return (
        <Tag shape="readonly" className="user-status-3">换机械表</Tag>
      )
    }else if (value == 4) {
      return (
        <Tag shape="readonly" className="user-status-2">封户</Tag>
      )
    } else if (value == 5) {
      return (
        <Tag shape="readonly" className="user-status-0">无水户</Tag>
      )
    } else if (value == 6) {
      return (
        <Tag shape="readonly" className="user-status-2">不用</Tag>
      )
    } else {
      return (
        <Tag shape="readonly" className="user-status-0">拆迁</Tag>
      )
    }
  }


  updateTunnageRange = () => {
    const { selectRows } = this.state;
    this.setState({
      visible: true,
    })
    this.field.validate((errors, values) => {
      if (typeof Number(values.waterVolumeRangeMax) !== "number" || typeof Number(values.waterVolumeRangeMin) !== "number") {
        Feedback.toast.prompt('请输入数字')
      } else if (Number(values.waterVolumeRangeMax) <= Number(values.waterVolumeRangeMin)) {
        Feedback.toast.prompt('最小水量区间应小于最大水量区间')
      } else if (Number(values.waterVolumeRangeMax) < 0 || Number(values.waterVolumeRangeMin) < 0) {
        Feedback.toast.prompt('请输入大于零的正整数')
      } else {
        const postData = {
          cno: selectRows.cno,
          maxTunnage: values.waterVolumeRangeMax,
          minTunnage: values.waterVolumeRangeMin,
          updateUID: sessionStorage.getItem("stuffId"),
          updateName: sessionStorage.getItem("realName")
        }
        axios({
          method: 'post',
          url: `${url}revenue/copyTask/updateTunnageRange`,
          data: postData,
        })
          .then((response) => {
            if (response.data.code === '0') {
              Feedback.toast.success('修改成功')
              this.setState({
                visible: false,
              })
            }
            this.queryCustomer(1, 10)
          })
      }
    })
  }

  render() {
    const { regionList, areaList, feeNameList, roleNameList, page, totalSize, pageSize, dataLoading, formSource, selectRows } = this.state;
    const { init } = this.field

    return (
      <div>
        <IceContainer title="机械表用户管理">
          <Form field={this.field}>
            <div style={{ display: 'flex', justifyContent: 'space-around' }}>
              <div style={{ display: 'flex', flexDirection: 'column' }}>
                <FormItem label="&emsp;用户编号：">
                  <Input {...init('cno')} placeholder="--请输入--" style={{ width: 160 }} />
                </FormItem>
                <FormItem label="&emsp;水表编号：">
                  <Input {...init('watermeterId')} placeholder="--请输入--" style={{ width: 160 }} />
                </FormItem>
                <FormItem label="&emsp;用户地址：">
                  <Input {...init('address')} style={{ width: 160 }} placeholder="--请输入--" />
                </FormItem>
                <FormItem label="未出账天数：">
                  <Row  style={{ width: 160 }}>
                    <Col span={8}>
                      <Input style={{ width: 55 }} {...init('daysFromLastAccountMin')} />
                    </Col>
                    <Col span={8} style={{ textAlign: 'center', lineHeight: '200%' }}>~</Col>
                    <Col span={8}>
                      <Input style={{ width: 55 }} {...init('daysFromLastAccountMax')} />
                    </Col>
                  </Row>
                </FormItem>
              </div>
              <div style={{ display: 'flex', flexDirection: 'column' }}>
                <FormItem label="用户户号：">
                  <Input {...init('hno')} placeholder="--请输入--" style={{ width: 160 }} />
                </FormItem>
                <FormItem label="&emsp;&emsp;片区：">
                  <Select {...init('areaId')} placeholder="请选择"
                    dataSource={areaList} style={{ width: 160 }} />
                </FormItem>
                <FormItem label="用水性质：">
                  <Select {...init('feeId')} placeholder="请选择"
                    dataSource={feeNameList} style={{ width: 160 }} />
                </FormItem>
                <FormItem label="用户状态：">
                  <Select placeholder="请选择" style={{ width: '170px' }} {...init('status')}
                          dataSource={[
                            { label: '请选择', value: '' },
                            { label: '正常', value: '1' },
                            { label: '非正常', value: '2' },
                            { label: '换机械表', value: '3' },
                            { label: '封户', value: '4' },
                            { label: '无水户', value: '5' },
                            { label: '不用', value: '6' }
                          ]}
                  />
                </FormItem>
              </div>
              <div style={{ display: 'flex', flexDirection: 'column' }}>
                <FormItem label="用户名称：">
                  <Input {...init('cname')} placeholder="--请输入--" style={{ width: 160 }} />
                </FormItem>
                <FormItem label="&emsp;&emsp;小区：">
                  <Combobox
                    {...init('regionId')}
                    placeholder="--请选择小区--"
                    fillProps="label"
                    hasClear
                    style={{ width: 160 }} dataSource={regionList} />
                </FormItem>
                <FormItem label="&emsp;抄表员：">
                  <Select {...init('copyName')} placeholder="请选择"
                    dataSource={roleNameList} style={{ width: 160 }} />
                </FormItem>
              </div>
            </div>
            <div style={{ textAlign: 'center' }}>
              <Button type="primary" className="button" onClick={() => this.queryCustomer(1, 10)}
                style={{ marginRight: 10 }}>
                <Icon type="search" />查询
              </Button>
              <Button type="secondary" className="button" onClick={() => this.reset()} style={{ marginRight: 10 }}>
                <Icon type="refresh" />重置
              </Button>
            </div>
          </Form>
        </IceContainer>
        <IceContainer title="用户列表">
          <Button className="button" type="primary" onClick={() => this.downLoadUserInfo()} style={{ marginBottom: 10 }}>
            <Icon type="download" style={{ color: "#ffffff" }} />
              导出用户信息
            </Button>
          <Table dataSource={formSource} isLoading={dataLoading}>
            <Table.Column title="用户编号" dataIndex="cno" align="center" />
            <Table.Column title="用户户号" dataIndex="hno" align="center" />
            <Table.Column title="用户名称" dataIndex="cname" align="center" />
            <Table.Column title="用户状态" dataIndex="status" align="center" width={120} cell={(value) => this.renderStatus(value)} />
            <Table.Column title="用户地址" dataIndex="address" align="center" />
            <Table.Column title="水表编号" dataIndex="watermeterId" align="center" />
            <Table.Column title="片区" dataIndex="areaName" align="center" />
            <Table.Column title="小区" dataIndex="regionName" align="center" />
            <Table.Column title="用水性质" dataIndex="feeName" align="center" />
            <Table.Column title="抄表员" dataIndex="copyName" align="center" />
            <Table.Column title="最后出账日期" dataIndex="lastBillTime" align="center" />
            <Table.Column title="未出账天数" dataIndex="daysFromLastAccount" align="center" />
            <Table.Column title="最后示数" dataIndex="lastNum" align="center" />
            <Table.Column title="水量区间" dataIndex="tunnageRange" align="center" />
            <Table.Column title="操作" cell={(value, index, record) => this.renderOper(value, index, record)}
              align="center" width={120} />
          </Table>
          <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Pagination
              pageSizeSelector="dropdown"
              onPageSizeChange={(value) => this.changePageSize(value)}
              pageSizeList={[10, 30, 50, 100]}
              style={{ marginTop: 15 }}
              current={page}
              pageSize={pageSize}
              total={totalSize}
              size="small"
              onChange={(value) => this.changePage(value)}
            />
            <div style={{ lineHeight: '53px', marginLeft: 10 }}>共{totalSize}条记录</div>
          </div>
        </IceContainer>

        <Dialog
          visible={this.state.visible}
          onOk={this.updateTunnageRange}
          onCancel={() => { this.setState({ visible: false }) }}
          onClose={() => { this.setState({ visible: false }) }}
          title="修改区间水量"
          footerAlign="center"
          style={{ width: "30%" }}
        >
          <FormItem>
            <Row >
              <Col span={6} style={{ lineHeight: '200%' }}>区间水量：</Col>
              <Col span={7}>
                <Input style={{ width: 60 }}
                  {...init('waterVolumeRangeMin', { initValue: selectRows.minTunnage })} />
              </Col>
              <Col span={3} style={{ lineHeight: '200%' }}>~</Col>
              <Col span={8}>
                <Input style={{ width: 60 }}
                  {...init('waterVolumeRangeMax', { initValue: selectRows.maxTunnage })} />
              </Col>
            </Row>
          </FormItem>
        </Dialog>
      </div>
    )
  }
}
