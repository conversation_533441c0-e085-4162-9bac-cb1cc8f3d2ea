import React, {Component} from 'react';
import {Input, Button, Icon, Table, Pagination, Form, Field, Balloon, Dialog, Feedback, Select, Radio} from '@icedesign/base';
import IceContainer from '@icedesign/container'
import axios from 'axios';
import qs from 'qs';
import {url} from '../../../components/URL/index'
import supplementCard from "../../../common/supplementCard"
import {areaCode} from '../../../components/areaCode/areaCode'
import CheckCardDialog from './components/CheckCardDialog'
import HXCardDialog from './components/HXCardDialog'
import HXCheckCardDialog from './components/HXCheckCardDialog'

const FormItem = Form.Item;
const Tooltip = Balloon.Tooltip
const {Combobox} = Select;
const {Group: RadioGroup} = Radio;

export default class ReplacementCard extends Component {

  constructor(props) {
    super(props);
    this.state = {
      formValue: [],
      page: 1,
      pageSize: 10,
      totalSize: 0,
      changeVisible: false,
      visible: false,
      dataLoading: false,
      record: '',
      orderValues: {},
      cardValues: [],
      code: '',
      areaCopy: [],
      areas: [],
      flag: '',
      time: 0,
      save: false,
      status: false,
      isUse: false,
      roleNameList: [],
      regionList:[],
      createId: sessionStorage.getItem("stuffId"),
      createName: sessionStorage.getItem("realName"),
    }
    this.field = new Field(this, {autoUnmount: true})
  }

  componentDidMount() {
    this.getArea()
    this.queryRoleName()
    this.queryRegion()
  }

  /*查询操作员*/
  queryRoleName = () => {
    axios({
      method: 'get',
      url: `${url}revenue/iccard/createName` + '?n=' + Math.random(),
    })
      .then((response) => {
        if (response.data.code == 0) {
          this.setState({
            roleNameList: response.data.datas
          })
        }
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
      })
  }

  //查询
  queryUser(page, pageSize) {
    this.setState({dataLoading: true});
    let values = this.field.getValues();
    values.page = page
    values.pageSize = pageSize
    axios({
      method: 'post',
      url: `${url}revenue/user/query/ic`,
      data: qs.stringify(values),
    })
      .then((response) => {
        this.setState({
          formValue: response.data.datas,
          page: page,
          pageSize: pageSize,
          totalSize: response.data.totalSize,
          dataLoading: false
        })
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
        this.setState({dataLoading: false,})
      })
  }

  //翻页
  changePage(page) {
    const {pageSize} = this.state;
    this.queryUser(page, pageSize);
  }

  //改变pageSize
  onPageSizeChange(pageSize) {
    this.queryUser(1, pageSize);
  }

  //表格操作
  renderOper(record) {
    const edit = (<Icon type="survey" size="xs" style={{color: "#1DC11D", cursor: 'pointer'}}
                        onClick={() => this.openEditDialog(record)}/>)
    const hxEdit = (<HXCardDialog record={record}/>)
    if (record.watermeterKind != '预付费4442') {
      return (
        <Tooltip trigger={edit} align='t' text='补卡'/>
      )
    } else {
      return (
        <Tooltip trigger={hxEdit} align='t' text='补卡'/>
      )
    }
  }

  //补卡按钮
  openEditDialog(record) {
    this.field.setValue('cardNo1', record.cardNo)
    this.field.setValue('replacerId', this.state.createId)
    this.setState({visible: true, record: record})
    this.queryLastOrder(record.cno)
  }

  //查询最后一笔有效订单
  queryLastOrder(cno) {
    axios({
      method: 'post',
      url: `${url}revenue/order/findLastOrdersByCnoAndStatus`,
      data: qs.stringify({cno: cno, resultSize: 1}),
    }).then((response) => {
      if (response.data.code == "0") {
        this.setState({cardValues: response.data.datas})
      }
    }).catch((error) => {
      Feedback.toast.error("请求错误：" + error);
    })
  }

  /*关闭弹窗*/
  handleCancel() {
    this.setState({visible: false})
  }

  //补卡调用接口添加记录
  yffreissue(record) {
    const {cardValues, createId, createName} = this.state
    this.field.validate((error, values) => {
      if (error) {
        this.setState({save: false})
        return;
      }
      let list = []
      let tunnage = cardValues.length > 0 ? cardValues[0].tunnage : '0'
      let totalTunnage = cardValues.length > 0 ? cardValues[0].totalTunnage : '0'
      list.cno = record.cno
      list.isUse = values.isUse1
      if (record.watermeterKind == '预付费5') {
        list.reissueWater = totalTunnage
      } else {
        list.reissueWater = list.isUse == '0' ? '0' : tunnage
      }
      list.cardNo = values.cardNo1
      list.amount = values.amount1
      list.createId = createId
      list.createName = createName
      list.replacerId = createId
      list.replacerName = createName
      list.watermeterId = record.watermeterId
      axios({
        method: 'post',
        url: `${url}revenue/iccard/reissue`,
        data: qs.stringify(list),
      }).then((response) => {
        if (response.data.code == '0') {
          Feedback.toast.success('补卡成功');
          this.setState({visible: false, save: false});
        } else {
          Feedback.toast.error('补卡失败，请重试')
        }
      }).catch((error) => {
        Feedback.toast.error("请求错误：" + error);
      })
    })
  }

  //调用补卡dll控件
  writeCards() {
    this.setState({save: true})
    const {record, cardValues} = this.state
    let water = 0
    let times = cardValues.length > 0 ? cardValues[0].totalTimes : 0
    let totalwater = cardValues.length > 0 ? cardValues[0].totalTunnage : 0
    if (this.field.getValue('isUse1') == '1') {
      //未使用查询最后一笔有效订单
      if (cardValues.length > 0) {
        water = cardValues[0].tunnage
      }
    }
    let cradNo = this.field.getValue('cardNo1')
    let i = 1
    try {
      if (record.watermeterKind == '预付费5') {
        i = hxdll.bfxkconfirm(cradNo, record.watermeterId, times, water, totalwater, 'yff', areaCode)
        if (i == 0) {
          if (cardValues.length > 0) {
            axios({
              method: 'post',
              url: `${url}revenue/order/complete`,
              data: qs.stringify({id: cardValues[0].id}),
            })
              .then((response) => {
                if (response.data.code == '0') {
                  this.yffreissue(record)
                }
              })
              .catch((error) => {
                Feedback.toast.error("请求错误：" + error);
              })
          } else {
            this.yffreissue(record)
          }
        } else {
          this.setState({save: false});
          Feedback.toast.error('补卡操作错误：' + supplementCard(i))
        }
      }else {
        i = hxdll.bfxkconfirm(cradNo, '1000000000', times, water, totalwater, 'yff', areaCode)
        if (i == 0) {
          if (cardValues.length > 0) {
            axios({
              method: 'post',
              url: `${url}revenue/order/complete`,
              data: qs.stringify({id: cardValues[0].id}),
            })
              .then((response) => {
                if (response.data.code == '0') {
                  this.yffreissue(record)
                }
              })
              .catch((error) => {
                Feedback.toast.error("请求错误：" + error);
              })
          } else {
            this.yffreissue(record)
          }
        } else {
          this.setState({save: false});
          Feedback.toast.error('补卡操作错误：' + supplementCard(i))
        }
      }
    }
    catch (e) {
      alert(e);
    }
  }

  //重置
  reset() {
    this.field.reset()
    this.setState({areaCopy: null})
  }

  //渲染区域
  area() {
    const {areas} = this.state;
    return areas && areas.length > 0 ? areas.map((item) => {
      return <Option value={item.id}> {item.name} </Option>
    }) : void (0)
  }

  //区域查询
  getArea() {
    axios({
      method: "get",
      url: `${url}revenue/area/getAll`,
    }).then(response => {
      this.setState({
        areas: response.data.datas
      })
    }).catch(error => {
      Feedback.toast.error("AJAX请求错误", error)
    })
  }

  //查询小区
  queryRegion() {
    let areaId=this.field.getValue('areaId')
    axios({
      method: 'post',
      url: `${url}revenue/region/regionlist`,
      data: qs.stringify(areaId)
    }).then(response => {
      let regionList = []
      response.data.datas.map((item) => {
        regionList.push({label: item.regionName, value: item.id})
      })
      this.setState({regionList: regionList})

    }).catch(error => {
      Feedback.toast.error("请求异常", error);
    })
  }

  //区册下拉
  areaCopy() {
    const {areaCopy} = this.state
    if (areaCopy == null || areaCopy == undefined) {
      return <Option value='1' disabled> 请先选择区域 </Option>
    }
    else {
      return areaCopy && areaCopy.length > 0 ? areaCopy.map((item) => {
        return <Option value={item.id}>{item.regionName}</Option>
      }) : void (0)
    }
  }

  //区域onchange
  onChange(value) {
    this.field.setValue('areaId', value);
    //区册查询
    let areaId = [];
    areaId.areaId = value;
    axios({
      method: 'post',
      url: `${url}revenue/region/paging`,
      data: qs.stringify(areaId)
    }).then(response => {
      this.setState({
        areaCopy: response.data.datas
      })
    }).catch(error => {
      Feedback.toast.error("请求异常", error);
    })
    this.field.reset('regionId')
  }

  //字段过长隐藏
  omit(value) {
    const look = (
      <section style={{whiteSpace: "nowrap", textOverflow: 'ellipsis', overflow: 'hidden'}}>{value}</section>)
    return <Tooltip trigger={look} align='t' text={value} style={{backgroundColor: '#f9f9f9'}}/>
  }

  render() {
    const footer = (
      <div align="center">
        <a onClick={() => this.writeCards()}>
          <Button size="medium" type="primary" loading={this.state.save}>
            补卡
          </Button>
        </a>
        <Button size="medium" onClick={() => this.handleCancel()}>
          取消
        </Button>
      </div>
    )

    const {regionList,roleNameList, formValue, page, totalSize, pageSize, record, dataLoading, visible, createName} = this.state
    const {init} = this.field
    const formItemLayout = {
      labelCol: {fixedSpan: 8}
    }
    return (

      <div>

        <IceContainer title="补卡">

          <Form field={this.field}>
            <div style={{display: 'flex', justifyContent: 'space-around'}}>
              <div style={{display: 'flex', flexDirection: 'column'}}>
                <FormItem label="用户编号：">
                  <Input {...init('cno')} placeholder="--请输入--" style={{width: 170}}/>
                </FormItem>
                <FormItem label="片&emsp;&emsp;区：">
                  <Select {...init('areaId')} placeholder="请选择" onChange={(value) => this.onChange(value)}
                          style={{width: '170px'}}>
                    {this.area()}
                  </Select>
                </FormItem>
                <FormItem label="用户地址：">
                  <Input {...init('address')} style={{width: 170}} placeholder="--请输入--"/>
                </FormItem>
              </div>
              <div style={{display: 'flex', flexDirection: 'column'}}>
                <FormItem label="用户卡号：">
                  <Input {...init('cardNo')} style={{width: 170}} placeholder="--请输入--"/>
                </FormItem>

                <FormItem label="小&emsp;&emsp;区：">
                  <Combobox
                    {...init('regionId')}
                    placeholder="--请选择小区--"
                    fillProps="label"
                    hasClear
                    style={{width: 170}} dataSource={regionList}/>
                </FormItem>

              </div>
              <div style={{display: 'flex', flexDirection: 'column'}}>
                <FormItem label="水表编号：">
                  <Input {...init('watermeterId')} style={{width: 170}} placeholder="--请输入--"/>
                </FormItem>
                <FormItem label="水表种类：">
                  <Select style={{width: '170px'}} {...init('watermeterKind')}>
                    <Select.Option value="预付费2">预付费2</Select.Option>
                    <Select.Option value="预付费5">预付费5</Select.Option>
                    <Select.Option value="预付费4442">预付费4442</Select.Option>
                  </Select>
                </FormItem>
              </div>
            </div>
          </Form>

          <div style={{display: 'flex', justifyContent: 'center'}}>
            <Button type="primary" className="button" onClick={() => this.queryUser()} style={{marginRight: 10}}>
              <Icon type="search"/>查询
            </Button>
            <Button type="secondary" className="button" onClick={() => this.reset()} style={{marginRight: 10}}>
              <Icon type="refresh"/>重置
            </Button>
            <CheckCardDialog roleNameList={roleNameList}/>
            <HXCheckCardDialog roleNameList={roleNameList}/>
          </div>
        </IceContainer>

        <IceContainer title="用户列表">
          <Table dataSource={formValue} isLoading={dataLoading}>
            <Table.Column title="用户编号" dataIndex="cno" align="center"/>
            <Table.Column title="用户卡号" dataIndex="cardNo" align="center"/>
            <Table.Column title="水表编号" dataIndex="watermeterId" align="center"/>
            <Table.Column title="用户名称" dataIndex="cname" align="center"/>
            <Table.Column title="用户地址" dataIndex="address" align="center" cell={(value) => this.omit(value)}/>
            <Table.Column title="小区" dataIndex="regionName" align="center"/>
            <Table.Column title="水表种类" dataIndex="watermeterKind" align="center"/>
            <Table.Column title="操作" cell={(value, index, record) => this.renderOper(record)}
                          align="center" width="80px"/>
          </Table>
          <div style={{display: 'flex', justifyContent: 'flex-end'}}>
            <Pagination
              pageSizeSelector="dropdown"
              onPageSizeChange={(current) => this.onPageSizeChange(current)}
              style={{marginTop: 15}}
              current={page}
              pageSize={pageSize}
              total={totalSize}
              size="small"
              onChange={(current) => this.changePage(current)}
            />
            <div style={{lineHeight: '53px', marginLeft: 10}}>共{totalSize}条记录</div>
          </div>
        </IceContainer>

        <Dialog style={{width: 550}} visible={visible}
                onClose={() => this.handleCancel()}
                onCancel={() => this.handleCancel()}
                footer={footer}
                title="补卡操作">

          <Form field={this.field}>

            <FormItem {...formItemLayout} label="用户卡号：">
              <Input  {...init('cardNo1')} style={{width: 230}} readOnly/>
            </FormItem>

            <FormItem {...formItemLayout} label="用户名：">
              <section style={{textAlign: 'left'}}>
                <div style={{marginTop: 5}}>{record.cname}</div>
              </section>
            </FormItem>


            {
              record.watermeterKind == '预付费2' ?
                <FormItem {...formItemLayout} label="提示：">
                  <section style={{textAlign: 'left'}}>
                    <div style={{color: '#ff0000', marginTop: 5}}>已使用：补发水量为0，未使用：补发上一笔订单购水量</div>
                  </section>
                </FormItem> : void(0)
            }

            {
              record.watermeterKind == '预付费2' ?
                <FormItem {...formItemLayout} label="使用状态：">
                  <RadioGroup {...init('isUse1')}
                              dataSource={[
                                {value: '1', label: "未使用"},
                                {value: '0', label: "已使用"}
                              ]}
                              defaultValue={'1'}
                  />
                </FormItem> : void(0)
            }

            <FormItem label="收费(元)：" {...formItemLayout} >
              <Input {...init('amount1', {rules: [{required: true, message: '必填'}]})} style={{width: 230}}/>
            </FormItem>

            <FormItem label="补卡人：" {...formItemLayout}>
              <Input value={createName} style={{width: 230, backgroundColor: '#eee'}} readOnly/>
            </FormItem>

            <FormItem label="换卡原因：" {...formItemLayout} >
              <Input multiple {...init('reason1')}
                     style={{width: 230}}/>
            </FormItem>

          </Form>

        </Dialog>

      </div>
    )
  }
}
