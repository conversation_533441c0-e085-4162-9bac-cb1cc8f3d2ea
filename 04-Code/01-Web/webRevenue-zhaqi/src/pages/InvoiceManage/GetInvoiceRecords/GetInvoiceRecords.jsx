/* eslint-disable react/no-unused-state */
import React, {Component} from 'react';
import {Input, Button, Icon, Table, Pagination, moment, Form, Field, Feedback, DatePicker,Select} from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import {url} from '../../../components/URL/index'
import qs from 'qs';

const FormItem = Form.Item
const {RangePicker} = DatePicker;
const {Combobox} = Select

export default class GetInvoiceRecords extends Component {

  constructor(props) {
    super(props);
    this.state = {
      formValue: [],
      recipientList:[],
      createNameList:[],
      page: 1,
      pageSize: 10,
      totalSize: 0,
      title: 1,
      selectUser: '',
      visible: false,
      dataLoading: false,
      openValve: true,
    }
    this.field = new Field(this, {autoUnmount: true});
  }

  componentDidMount() {
    this.queryDate(1,10);
    this.querCreateName()
    this.querRecipient()
  }

  /*查询*/
  queryDate(page, pageSize) {
    this.setState({dataLoading: true});
    let values = this.field.getValues();
    values.page = page;
    values.pageSize = pageSize;
    axios({
      method: 'post',
      url: `${url}revenue/invoiceReceive/search`,
      data: qs.stringify(values),
    })
      .then((response) => {
          this.setState({
            formValue: response.data.datas,
            page: response.data.page,
            pageSize: response.data.pageSize,
            totalSize: response.data.totalSize,
            dataLoading: false
          })
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
        this.setState({dataLoading: false,})
      })
  }

  /*切换分页*/
  changePage(page) {
    const {pageSize} = this.state
    this.queryDate(page, pageSize)
  }

  //查询操作员
  querCreateName() {
    axios({
      method: 'get',
      url: `${url}revenue/invoiceReceive/createName`,
    })
      .then((response) => {
        let createNameList = []
        response.data.datas.map((item) => {
          createNameList.push({label: item.label, value: item.value})
        })
        this.setState({createNameList: createNameList})
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
      })
  }

  //查询领用人
  querRecipient() {
    axios({
      method: 'get',
      url: `${url}revenue/invoiceReceive/recipient`,
    })
      .then((response) => {
        let recipientList = []
        response.data.datas.map((item) => {
          recipientList.push({label: item.label, value: item.value})
        })
        this.setState({recipientList: recipientList})
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
      })
  }


  /*格式化日期*/
  formatData(record) {
    const time = record.dateTime ? moment(record.dateTime).format('YYYY-MM-DD h:mm:ss') : void (0);
    return time;
  }

  formateDate = (value, str) => {
    return str;
  }

  //时间onchang
  timeOnchange(val, str) {
    this.field.setValue("time1", str[0]);
    this.field.setValue("time2", str[1]);
  }

  //重置
  reset() {
    this.field.reset();
  }

  onChange = (checked) => {
    console.log(checked);
  }

  render() {
    const {formValue, page, totalSize, pageSize, dataLoading,createNameList,recipientList} = this.state
    const {init} = this.field
    return (
      <div>

        <IceContainer title="搜索">
          <Form direction="hoz" field={this.field} style={{display: 'flex', justifyContent: 'space-around'}}>
            <FormItem label="领用日期：">
              <RangePicker
                onChange={(val, str) => this.timeOnchange(val, str)}
                placeholder="领用日期"
              />
            </FormItem>
            <FormItem label="领用人：">
              <Select {...init('createId')}
                      placeholder="--请选领用人--"
                      style={{width: 180}}
                      dataSource={recipientList}/>
            </FormItem>
            <FormItem label="操作员：">
              <Combobox
                {...init('recipientId')}
                placeholder="--请选择--"
                fillProps="label"
                hasClear
                style={{width: 180}} dataSource={createNameList}/>
            </FormItem>
            <Button type="primary" className="button" onClick={() => this.queryDate()}>
              <Icon type="search"/>查询
            </Button>
            <Button type="secondary" className="button" onClick={() => this.reset()} style={{marginLeft: 20}}>
              <Icon type="refresh"/>重置
            </Button>
          </Form>
        </IceContainer>

        <IceContainer title="领用记录">
          <Table dataSource={formValue} isLoading={dataLoading}>
            <Table.Column title="序号" dataIndex="id" align="center"/>
            <Table.Column title="领用日期" dataIndex="createTime" align="center"/>
            <Table.Column title="领用人" dataIndex="recipient" align="center"/>
            <Table.Column title="操作员" dataIndex="createName" align="center"/>
            <Table.Column title="发票代码" dataIndex="invoiceCode" align="center"/>
            <Table.Column title="起始发票号" dataIndex="startNumber" align="center"/>
            <Table.Column title="截止发票号" dataIndex="endNumber" align="center"/>
          </Table>
          <Pagination
            style={{textAlign: 'right', marginTop: 15}}
            current={page}
            pageSize={pageSize}
            total={totalSize}
            size="small"
            onChange={(current) => this.changePage(current)}
          />
        </IceContainer>

      </div>
    )
  }
}
