/* eslint-disable react/no-unused-state */
import {Balloon, Feedback, Field, moment, Pagination, Table,Icon} from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import React, {Component} from 'react';
import {url} from '../../../components/URL/index';
import AddFormwork from './componens/AddFormwork'
import UpDateFormwork from './componens/UpDateFormwork'
import getLodop from '../../../common/LodopFuncs'

const Tooltip = Balloon.Tooltip
var LODOP = getLodop();
export default class Formwork extends Component {

  constructor(props) {
    super(props);
    this.state = {
      formValue: [],
      current: 1,
      pageSize: 10,
      totalSize: 0,
      visible: false,
      dataLoading: false,
      createId: sessionStorage.getItem("stuffId"),
      createName: sessionStorage.getItem("realName")
    }
    this.field = new Field(this, {autoUnmount: true});
  }

  componentDidMount() {
    this.queryFormwork()
  }

  //查询发票模板
  queryFormwork() {
    this.setState({dataLoading: true})
    axios({
      method: 'post',
      url: `${url}revenue/printTemplate/query`,
    })
      .then((response) => {
        this.setState({
          formValue: response.data.datas,
          current: response.data.page,
          pageSize: response.data.pageSize,
          totalSize: response.data.totalSize,
          dataLoading: false
        });
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
        this.setState({dataLoading: false})
      });
  }

  /*表格操作栏*/
  renderOper(record) {
    const edit = (<UpDateFormwork queryFormwork={this.queryFormwork.bind(this)} record={record}/>)
    const design = (<Icon type="set" size="small" style={{color: "#1DC11D", cursor: 'pointer'}} onClick={() => this.design(record)} />)
    return (
      <span>
        <Tooltip trigger={edit} align='t' text='修改'/>
        <Tooltip trigger={design} align='t' text='维护'/>
      </span>
    )
  }

  //打印维护
  design(record){
    let temp=record.templateCode.replace("LODOP.PRINT_INIT(\"\");", "LODOP.PRINT_INIT(\"${record.templateName}\");")
    eval(temp);
    LODOP.PRINT_SETUP();
  }



  /*格式化日期*/
  formatData(value) {
    const time = value ? moment(value).format('YYYY-MM-DD') : void (0);
    return time;
  }

  render() {
    const {formValue, dataLoading} = this.state
    return (
      <div>
        <IceContainer title="发票模板">
          <div style={{textAlign: 'right', marginBottom: 10}}>
            <AddFormwork queryFormwork={this.queryFormwork.bind(this)}/>
          </div>
          <Table dataSource={formValue} isLoading={dataLoading}>
            <Table.Column title="发票名称" dataIndex="templateName" align="center"/>
            <Table.Column title="创建人" dataIndex="createName" align="center"/>
            <Table.Column title="创建时间" dataIndex="createTime" align="center" cell={(value) => this.formatData(value)}/>
            <Table.Column title="操作" cell={(value, index, record) => this.renderOper(record)} align="center"
                          width={100}/>
          </Table>
        </IceContainer>
      </div>
    )
  }
}
