/* eslint-disable react/no-unused-state */
import React, {Component} from 'react';
import {
  Input,
  Button,
  Icon,
  Table,
  Pagination,
  Form,
  Field,
  Feedback,
  DatePicker,
  Select,
  Grid,
  Upload
} from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import qs from 'qs';
import {url} from '../../../components/URL/index';
import {Link} from 'react-router-dom';
import UpdateDialog from './components/updateDialog'

const FormItem = Form.Item
const {Row} = Grid;
const {RangePicker} = DatePicker;
export default class BeforeUserOpen extends Component {

  constructor(props) {
    super(props);
    this.state = {
      formValue: [],
      propertiesList: [],
      page: 1,
      pageSize: 10,
      total: 0,
      visible: false,
      dataLoading: false,
      openValve: true,
      stuffId: sessionStorage.getItem('stuffId'),
      stuffName: sessionStorage.getItem('realName'),
      areaList: [],
      regionList: [],
      selectedRowKeys: [],
      selectedRecord: [],
    }
    this.field = new Field(this, {autoUnmount: true});
    // 表格可以勾选配置项
    this.rowSelection = {
      // 表格发生勾选状态变化时触发，ids可以将所有勾选的行ID获取到
      onChange: (ids, records) => {
        this.setState({
          selectedRowKeys: ids,
          selectedRecord: records,
        });
      },
      // 支持针对特殊行进行定制
      getProps: (record) => {
        return {
          disabled: record.watermeterType != '远传表',
          //disabled: record.watermeterKind == '有线远传',
        };
      },
    };
  }

  componentDidMount() {
    let param = this.props.location.state;
    if (param != null) {
      this.field.setValues({...param.searchValue})
    }
    this.refreshTable(1, 10);
    this.queryWaterMeterName()
  }

  refreshTable = (page, pageSize) => {
    this.setState({
      dataLoading: true,
    });
    let values = this.field.getValues();
    values.page = page;
    values.pageSize = pageSize;
    axios({
      method: 'post',
      url: `${url}revenue/preOpening/query`,
      data: qs.stringify(values),
    })
      .then((response) => {
        this.setState({
          dataLoading: false,
        });
        let jsondata = response.data;
        this.setState({
          formValue: jsondata.datas,
          page: page,
          pageSize: pageSize,
          total: jsondata.totalSize,
          selectedRowKeys: [],
          selectedRecord: [],
        });
      })
      .catch((error) => {
        this.setState({
          dataLoading: false,
        });
        Feedback.toast.error('axios请求异常:' + error);
      });
  };

  //查询
  doSearch = () => {
    const {pageSize} = this.state;
    this.refreshTable(1, pageSize);
  }

  //重置
  reset = () => {
    this.field.reset();

  }

  //翻页
  changePage = (page) => {
    const {pageSize} = this.state;
    this.refreshTable(page, pageSize);
  }

  //改变pageSize
  onPageSizeChange = (pageSize) => {
    this.refreshTable(1, pageSize);
  }

  //重置
  reset = () => {
    this.field.reset();
  }

  watermeterKindFm = (value) => {
    if (value == 1) {
      return '有线远传';
    }
    else {
      return '无线远传';
    }
  }

  openUser = (value, index, record) => {
    const {page, pageSize} = this.state
    if (record.status == '0') {
      return (
        <div style={{display: 'flex', justifyContent: 'space-between'}}>

          <UpdateDialog record={record} refreshTable={() => this.refreshTable(page, pageSize)}/>

          <Link to={{
            pathname: '/user/UserOpen',
            state: {record: record, searchValue: this.field.getValues()}
          }}>
            <span style={{color: "#1DC11D", cursor: "pointer"}}>开户</span>
          </Link>
        </div>
      )
    }
  }

  uploadSuccess = (response) => {
    if (response.code == 0) {
      Feedback.toast.success('上传成功');
      this.refreshTable(1, 10);
    }
  }

  uploadError = (response) => {
    if (response.response.code == 1) {
      alert(response.response.msg);
    }
  }

  statusFm = (value) => {
    if (value == '1') {
      return '已开户';
    } else if (value == '0') {
      return '待开户';
    } else if (value == '2') {
      return '异常用户';
    }
  }

  batchClose = () => {
    const {selectedRecord, stuffId, stuffName} = this.state;
    if (selectedRecord.length == 0) {
      alert('至少选择一条记录');
      return;
    }

    for (let i = 0; i < selectedRecord.length - 1; i++) {
      for (let j = i + 1; j < selectedRecord.length; j++) {
        if (selectedRecord[i].watermeterKind != selectedRecord[i + 1].watermeterKind) {
          alert('无线和有线远传表不能同时关阀，请筛选');
          return;
        }
      }
    }

    let values = {};
    values.createId = stuffId;
    values.createName = stuffName;
    values.remoteWatermeters = selectedRecord;
    values.valve = 2;
    axios({
      method: 'POST',
      url: `${url}revenue/remoteWatermeter/switchValve`,
      data: values,
    })
      .then((response) => {
        var jsondata = response.data;
        if (jsondata.code == 0) {
          alert("关阀指令发送成功");
          //this.refreshTable();
        }
        else {
          alert(jsondata.msg);
        }
      })
      .catch((error) => {
        Feedback.toast.error("ajax请求出错: " + error);
      });
  }

  timeOnchange = (val, str) => {
    this.field.setValue("csTime", str[0]);
    this.field.setValue("ceTime", str[1]);
  }

  /*渲染阀门状态*/
  renderState(value) {
    if (value == '1') {
      return <span style={{color: '#1DC11D'}}>开阀</span>;
    }
    else if (value == '2') {
      return <span style={{color: '#ff0000'}}>关阀</span>;
    }
    else {
      return <span style={{color: '#ff0000'}}>半悬</span>;
    }
  }

  //查询用水性质
  queryWaterMeterName() {
    axios({
      method: 'post',
      url: `${url}revenue/fee/queryList`,
    })
      .then((response) => {
        let propertiesList = []
        response.data.datas.map((item) => {
          propertiesList.push({label: item.name, value: item.id})
        })
        this.setState({propertiesList: propertiesList})
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
      })
  }


  //导出
  downloadFile() {
    let values = this.field.getValues();
    let url1 = `${url}revenue/preOpening/exportExcel?n=1`;
    if (values.watermeterId) {
      url1 += '&watermeterId=' + values.watermeterId;
    }
    if (values.address) {
      url1 += '&address=' + values.address;
    }
    if (values.watermeterKind) {
      url1 += '&watermeterKind=' + values.watermeterKind;
    }
    if (values.status) {
      url1 += '&status=' + values.status;
    }
    if (values.valve) {
      url1 += '&valve=' + values.valve;
    }
    if (values.feeId) {
      url1 += '&feeId=' + values.feeId;
    }
    if (values.csTime) {
      url1 += '&csTime=' + values.csTime;
    }
    if (values.ceTime) {
      url1 += '&ceTime=' + values.ceTime;
    }
    window.open(encodeURI(url1), 'about:blank');
  }

  render() {
    const {formValue, page, total, pageSize, dataLoading, selectedRowKeys, areaList, regionList, propertiesList} = this.state;
    const {init} = this.field;
    const formItemLayout = {
      labelCol: {fixedSpan: 5}
    }
    return (
      <div>

        <IceContainer title="预开户">
          <Form direction="hoz" field={this.field}>

            <Row justify="space-between">

              <FormItem label="水表编号：" {...formItemLayout}>
                <Input {...init('watermeterId')} placeholder="请输入" style={{width: 180}}/>
              </FormItem>

              <FormItem label="地址：" {...formItemLayout}>
                <Input {...init('address')} placeholder="请输入" style={{width: 180}}/>
              </FormItem>

              <FormItem label="水表种类：">
                <Select
                  {...init('watermeterKind')}
                  placeholder="请选择"
                  dataSource={[
                    {label: "请选择", value: ''},
                    {label: '预付费2', value: '预付费2'},
                    {label: '预付费5', value: '预付费5'},
                    {label: '预付费4442', value: '预付费4442'},
                    {label: '无线远传', value: '无线远传'},
                    {label: '有线远传', value: '有线远传'},
                    {label: '机械表', value: '机械表'}
                  ]}
                  style={{width: 211}}
                />
              </FormItem>

            </Row>

            <Row justify="space-between">

              <FormItem label="开户状态：" {...formItemLayout}>
                <Select
                  {...init('status')}
                  placeholder="请选择"
                  dataSource={[
                    {label: "请选择", value: ''},
                    {label: '待开户', value: '0'},
                    {label: '已开户', value: '1'},
                    {label: '异常用户', value: '2'},
                  ]}
                  style={{width: 180}}
                />
              </FormItem>

              <FormItem label="阀门状态：" {...formItemLayout}>
                <Select
                  {...init('valve')}
                  placeholder="请选择"
                  dataSource={[
                    {label: "请选择", value: ''},
                    {label: '开阀', value: '1'},
                    {label: '关阀', value: '2'},
                    {label: '半悬', value: '3'},
                  ]}
                  style={{width: 180}}
                />
              </FormItem>

              <FormItem label="开户时间：">
                <RangePicker
                  onChange={(val, str) => this.timeOnchange(val, str)}
                  style={{width: 211}}/>
              </FormItem>

            </Row>

            <Row>
              <FormItem label="用水性质：" {...formItemLayout}>
                <Select {...init('feeId', {})} placeholder="--请选择--"
                        style={{width: 180}}
                        dataSource={propertiesList}
                />
              </FormItem>
            </Row>

          </Form>
          <div align="center">
            <Button type="primary" className="button" onClick={() => this.doSearch()}>
              <Icon type="search"/>查询
            </Button>
            <Button type="secondary" className="button" onClick={() => this.reset()} style={{marginLeft: 20}}>
              <Icon type="refresh"/>重置
            </Button>
          </div>
        </IceContainer>

        <IceContainer title="预开户列表">
          <div align="right">
            <a href={`${url}revenue/preOpening/downExcel`}
               target="view_window">
              <Button type="primary" className="button">
                <Icon type="download"/>
                下载模板
              </Button>
            </a>
            <Upload
              action={`${url}revenue/preOpening/importExcel`}
              onSuccess={this.uploadSuccess}
              onError={this.uploadError}
              showUploadList={false}
            >
              <Button type="primary" className="button" style={{marginLeft: 20}}>
                <Icon type="skip"/>
                导入数据
              </Button>
            </Upload>
          </div>
          <Button className="button" type="primary" onClick={this.batchClose} style={{marginBottom: 10}}>
            <Icon type="stop" style={{color: "#ffffff"}}/>
            批量关阀
          </Button>
          <Button className="button" type="primary" onClick={()=>this.downloadFile()}
                  style={{marginLeft: 10, marginBottom: 10, width: 120}}>
            <Icon type="share" style={{color: "#ffffff"}}/>
            导出
          </Button>
          <Table dataSource={formValue} isLoading={dataLoading}
                 primaryKey="id"
                 rowSelection={{
                   ...this.rowSelection,
                   selectedRowKeys: selectedRowKeys,
                 }}
          >
            <Table.Column title="水表口径" dataIndex="watermeterCaliber" align="center" />
            <Table.Column title="水表编号" dataIndex="watermeterId" align="center"/>
            <Table.Column title="锁号" dataIndex="lockNo" align="center"/>
            <Table.Column title="安装位置" dataIndex="location" align="center"/>
            <Table.Column title="地址" dataIndex="address" align="center"/>
            <Table.Column title="水表类型" dataIndex="watermeterType" align="center"/>
            <Table.Column title="水表种类" dataIndex="watermeterKind" align="center"/>
            <Table.Column title="水表厂家" dataIndex="watermeterCompany" align="center"/>
            <Table.Column title="用水性质" dataIndex="feeName" align="center"/>
            <Table.Column title="预开户状态" dataIndex="status" align="center" cell={this.statusFm}/>
            <Table.Column title="开户时间" dataIndex="openTime" align="center"/>
            <Table.Column title="开阀状态" dataIndex="valve" align="center" cell={(value) => this.renderState(value)}/>
            <Table.Column title="操作" cell={this.openUser} align="center"/>
          </Table>
          <div style={{display: 'flex', justifyContent: 'flex-end'}}>
            <Pagination
              style={{textAlign: 'right', marginTop: 15}}
              pageSizeSelector="dropdown"
              onChange={this.changePage}
              onPageSizeChange={this.onPageSizeChange}
              total={total}
              pageSize={pageSize}
              current={page}
              size="small"
              pageSizeList={[10, 30, 50, 100]}
            />
            <div style={{lineHeight: '58px', marginLeft: 10}}>共 {total} 条记录</div>
          </div>
        </IceContainer>

      </div>
    )
  }
}
