import React, { Component } from 'react';
import { Select, Input, Button, Feedback, Form, Field, moment } from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import qs from 'qs';
import { url } from '../../../../components/URL/index'
import { areaCode, systemCode42 } from '../../../../components/areaCode/areaCode'
import watermeterKindNo from "../../../../common/watermeterKindNo";
import cardStatus from "../../../../common/cardStatus";
import withRouter from "react-router-dom/es/withRouter";

const FormItem = Form.Item;
const { Combobox } = Select

@withRouter
export default class CardWatermeter extends Component {

  constructor(props) {
    super(props);
    this.state = {
      regionList: [], //小区
      openButton: false,
      flag: false,
      watermeterValue: null,
      searchValue: {},
      createId: sessionStorage.getItem("stuffId") ? sessionStorage.getItem("stuffId") : '1235467',
      createName: sessionStorage.getItem("realName"),
    }
    this.field = new Field(this, { autoUnmount: true })
  }

  componentWillMount() {
    let param = this.props.location.state
    if (param != undefined || param != null) {
      this.field.setValues({ ...param.record });
      this.setState({ searchValue: param.searchValue, })
    }
  }

  //片区联动小区
  areaOnChange(value) {
    this.field.setValue('areaId', value);
    this.field.reset('regionId')
    axios({
      method: 'post',
      url: `${url}revenue/region/getRegionListIC`,
      data: qs.stringify({ areaId: value })
    }).then(response => {
      let regionList = []
      response.data.datas.map((item) => {
        regionList.push({ label: item.regionName, value: item.id })
      })
      this.setState({ regionList: regionList })
    }).catch(error => {
      Feedback.toast.error("网络连接错误")
    })
  }

  //失去焦点查询水表
  handleOnBlur() {
    let watermeterId = this.field.getValue('watermeterId')
    axios({
      method: 'post',
      url: `${url}revenue/watermeter/get`,
      data: qs.stringify({ watermeterId: watermeterId }),
    })
      .then((response) => {
        if (response.data.code == "0") {
          let value = response.data.datas
          if (value.watermeterType == '机械表') {
            alert("机械表请从机械表标签页开户");
            this.field.reset('watermeterId')
            this.field.reset('watermeterType')
            this.field.reset('watermeterKind')
          } else {
            this.field.setValues({ ...response.data.datas })
            this.setState({ watermeterValue: value })
          }
        } else if (response.data.msg == '水表不存在') {
          this.setState({ flag: true, watermeterValue: null })
          this.field.reset('watermeterType')
          this.field.reset('watermeterKind')
        } else {
          Feedback.toast.error(response.data.msg);
          this.field.reset('watermeterId')
        }

      })
      .catch((error) => {
        Feedback.toast.error("网络连接错误")
      })
  }


  //校验信用额度
  checkedCreditLine = (rule, value, callback) => {
    if (value) {
      if (value.indexOf('.') > 0) {
        callback('信用额度不能为小数');
      }
      else {
        callback();
      }
    } else {
      callback();
    }
  }

  //改变水表类型
  changeWaterType(value) {
    this.field.setValue('watermeterType', value)
    this.field.reset('watermeterKind')
    let temp = []
    if (value == 'IC卡表') {
      temp = [
        { label: '预付费2', value: '预付费2' },
        { label: '预付费5', value: '预付费5' },
        { label: '预付费4442', value: '预付费4442' },
      ];
    }
    else if (value == '远传表') {
      temp = [
        { label: '无线远传', value: '无线远传' },
        { label: '有线远传', value: '有线远传' }
      ];
    }
    this.setState({
      waterKind: temp,
    });
  }

  //表单验证
  doSave() {
    this.field.validate((errors, values) => {
      if (errors) {
        return;
      } else {
        this.setState({ openButton: true });
        axios({
          method: 'post',
          url: `${url}revenue/user/getCno`,
        }).then((response) => {
          if (response.data.code == '0') {
            let cno = response.data.datas
            this.open(values, cno)
          } else {
            this.setState({ openButton: false });
            Feedback.toast.error("错误：" + response.data.msg);
          }
        }).catch((error) => {
          this.setState({ openButton: false });
          Feedback.toast.error("网络连接错误" + error)
        })
      }
    })
  }

  //开户
  open(values, cno) {
    const { createId, createName } = this.state
    let dataList = {}
    let watermeterId = this.field.getValue('watermeterId')
    let watermeterKind = this.field.getValue('watermeterKind')
    let type = watermeterKindNo(watermeterKind)
    let userCno = ''
    if (values.watermeterType == 'IC卡表') {
      if (values.watermeterCompany == '扬州恒信') {
        userCno = '10' + cno
      } else {
        userCno = '13' + cno
      }
    } else if (values.watermeterType == '远传表') {
      userCno = '11' + cno
    }
    let cardNo = ''
    if (watermeterKind == '预付费5') {
      cardNo = watermeterId
    } else {
      cardNo = userCno
    }
    dataList.cno = userCno
    dataList.cname = values.cname
    dataList.phone = values.phone
    dataList.domicileNum = values.domicileNum
    dataList.areaId = values.areaId
    dataList.creditLine = values.creditLine
    dataList.regionId = values.regionId
    dataList.remark = values.remark
    dataList.feeId = values.feeId
    dataList.identityCard = values.identityCard
    dataList.address = values.address
    dataList.createId = createId
    dataList.createName = createName
    dataList.customerWatermeters = [
      {
        watermeterId: watermeterId,
        cardNo: cardNo,
        watermeterType: values.watermeterType,
        watermeterCompany: values.watermeterCompany,
        watermeterCaliber: values.watermeterCaliber,
        watermeterKind: watermeterKind,
        lockNo: values.lockNo,
        location: values.location,
        wheelNumber: values.wheelNumber,
        cno: userCno
      }
    ]

    if (values.watermeterType == 'IC卡表') {
      if (type == '1') {
        //钥匙开卡
        try {
          let i = hxdll.newuser(userCno, '0000000000', 'yff', areaCode, type)
          if (i == 0) {
            axios({
              method: 'post',
              url: `${url}revenue/user/open`,
              data: dataList
            }).then((response) => {
              this.setState({ openButton: false })
              if (response.data.code === "0") {
                Feedback.toast.success("开户成功")
                let param = this.props.location.state;
                if (param != null || param != undefined) {
                  this.setBeforeOpenUser(param.record.id);
                }
                alert("开户成功!客户的用户编号为：" + userCno)
                this.field.reset()
              } else {
                Feedback.toast.error("开户失败:" + response.data.msg)
              }
            }).catch((error) => {
              this.setState({ openButton: false })
              Feedback.toast.error("网络连接错误");
            })
          } else {
            this.setState({ openButton: false })
            Feedback.toast.error('开卡失败：' + cardStatus(i))
          }
        }
        catch (e) {
          this.setState({ openButton: false })
          Feedback.toast.error("读卡控件失败或浏览器不支持,请使用IE")
        }
      } else if (type == '3') {
        //圆卡开卡
        try {
          let i = hxdll.newuser(watermeterId, watermeterId, 'yff', areaCode, type)
          if (i === 0) {
            axios({
              method: 'post',
              url: `${url}revenue/user/open`,
              data: dataList
            })
              .then((response) => {
                this.setState({ openButton: false })
                if (response.data.code === "0") {
                  Feedback.toast.success("开户成功")
                  let param = this.props.location.state;
                  if (param != null || param != undefined) {
                    this.setBeforeOpenUser(param.record.id);
                  }
                  alert("开户成功!客户的用户编号为：" + userCno)
                  this.field.reset('watermeterId')
                  this.field.reset('watermeterType')
                  this.field.reset('watermeterCompany')
                  this.field.reset('watermeterCaliber')
                  this.field.reset('watermeterKind')
                  this.field.reset('location')
                  this.field.reset('wheelNumber')
                } else {
                  Feedback.toast.error("开户失败:" + response.data.msg)
                }
              })
              .catch((error) => {
                this.setState({
                  openButton: false,
                });
                Feedback.toast.error("请求错误：" + error);
              })
          } else {
            this.setState({
              openButton: false,
            });
            Feedback.toast.error('开卡失败错误代码：' + cardStatus(i))
          }
        }
        catch (e) {
          this.setState({ openButton: false })
          alert('浏览器不支持ActiveX控件，请使用IE');
        }
      } else if (type == '6') {
        //预付费卡4442
        try {
          watermeterId = watermeterId.substring(0, 8)
          let cno = 'FFFFFF' + userCno
          //系统码|子表号|电子表号|购买量|关阀报警|囤积限量|购水次数|有效卡标志| IC卡号|用户编码|操作员|总购量|
          let i = SZHXMETERCARD_Web.HXCD_4442_UserCard_Web(systemCode42 + '|1|' + watermeterId + '|0|3|1500|0|1|1|' + cno + '|').split('|')
          if (i[0] > 0) {
            axios({
              method: 'post',
              url: `${url}revenue/user/open`,
              data: dataList
            })
              .then((response) => {
                this.setState({ openButton: false, })
                if (response.data.code === "0") {
                  let param = this.props.location.state;
                  if (param != null || param != undefined) {
                    this.setBeforeOpenUser(param.record.id);
                  }
                  Feedback.toast.success("开户成功")
                  alert("开户成功!客户的用户编号为：" + userCno)
                  this.field.reset('watermeterId')
                  this.field.reset('watermeterType')
                  this.field.reset('watermeterCompany')
                  this.field.reset('watermeterCaliber')
                  this.field.reset('watermeterKind')
                  this.field.reset('location')
                  this.field.reset('wheelNumber')
                } else {
                  Feedback.toast.error("开户失败:" + response.data.msg)
                }
              })
              .catch((error) => {
                this.setState({
                  openButton: false,
                });
                Feedback.toast.error("请求错误：" + error);
              })
          } else {
            this.setState({ openButton: false })
            Feedback.toast.error('开卡失败:' + i[1])
          }
        }
        catch (e) {
          this.setState({ openButton: false, })
          alert('浏览器不支持ActiveX控件，请使用IE');
        }
      }
    } else {
      axios({
        method: 'post',
        url: `${url}revenue/user/open`,
        data: dataList
      })
        .then((response) => {
          this.setState({ openButton: false, })
          if (response.data.code == "0") {
            Feedback.toast.success("开户成功");
            let param = this.props.location.state;
            if (param != null || param != undefined) {
              this.setBeforeOpenUser(param.record.id);
            }
            alert("开户成功!客户的用户编号为：" + userCno)
            this.field.reset()
          } else {
            this.setState({ openButton: false, })
            Feedback.toast.error("开户失败:" + response.data.msg)
          }
        })
        .catch((error) => {
          this.setState({ openButton: false, })
          Feedback.toast.error("网络连接错误")
        })
    }
  }

  //修改预开户的用户状态
  setBeforeOpenUser(id) {
    axios({
      method: 'post',
      url: `${url}revenue/preOpening/update`,
      data: qs.stringify({ id: id })
    }).then((response) => {
      if (response.data.code == "0") {
        Feedback.toast.success("状态修改成功")
      } else {
        Feedback.toast.error("状态修改失败")
      }
    }).catch((error) => {
      Feedback.toast.error("网络连接错误")
    })
  }

  render() {
    const { flag, watermeterValue, openButton, regionList } = this.state
    const { areaList, propertiesList } = this.props
    const { init } = this.field
    const watermeterCaliber = [{ label: 15, value: '15' }, { label: 20, value: '20' }, { label: 25, value: '25' }]
    return (
      <div>

        <IceContainer title="用户资料登记">
          <Form field={this.field}>
            <div style={{ display: 'flex', justifyContent: 'space-around' }}>

              <div style={{ display: 'flex', flexDirection: 'column' }}>

                <FormItem label="用户名称：">
                  <Input {...init('cname', { rules: [{ required: true, message: "请输入用户名称" }] })} placeholder="--请输入--"
                    style={{ width: 180 }} maxLength={60} />
                </FormItem>

                <FormItem label="选择片区：">
                  <Select placeholder="--请输入--" {...init('areaId', {
                    rules: [{ required: true, message: "请选择片区" }]
                  })}
                    style={{ width: 180 }} dataSource={areaList} onChange={(value) => this.areaOnChange(value)} />
                </FormItem>

                <FormItem label="选择小区：">
                  <Combobox
                    {...init('regionId', { rules: [{ required: true, message: "请选择小区" }] })}
                    placeholder="--请先选择片区--"
                    fillProps="label"
                    hasClear
                    style={{ width: 180 }} dataSource={regionList} />
                </FormItem>

                <FormItem label="用户地址：" style={{ marginLeft: 3 }}>
                  <Input {...init('address')} placeholder="--请输入--"
                    style={{ width: 300 }} maxLength={60} />
                </FormItem>

              </div>

              <div style={{ display: 'flex', flexDirection: 'column' }}>

                <FormItem label="联系号码：">
                  <Input maxLength={11}  {...init('phone',
                    {
                      rules: [{ min: 11, max: 11, message: "手机号码长度为11位" }]
                    }
                  )}
                    placeholder="--请输入--" style={{ width: 180 }} />
                </FormItem>

                <FormItem label="用水性质：">
                  <Combobox
                    {...init('feeId', { rules: [{ required: true, message: "请选择用水性质" }] })}
                    placeholder="--请选择或输入--"
                    fillProps="label"
                    hasClear
                    style={{ width: 180 }} dataSource={propertiesList} />
                </FormItem>

                <FormItem label="&emsp;身份证号：">
                  <Input maxLength={18}  {...init('identityCard', {
                    rules: [
                      { min: 18, max: 18, message: "身份证号长度为18位" },
                    ]
                  })} placeholder="--请输入--"
                    style={{ width: 180 }} />
                </FormItem>

              </div>

              <div style={{ display: 'flex', flexDirection: 'column' }}>

                <FormItem label="户籍人数(人)：">
                  <Input {...init('domicileNum', { rules: [{ required: true, message: "请填写户籍人数" }], initValue: 3 })}
                    placeholder="--请输入--"
                    style={{ width: 180 }} />
                </FormItem>

                <FormItem label="&emsp;信用额度(元)：">
                  <Input {...init('creditLine',
                    { initValue: 0, rules: [{ validator: this.checkedCreditLine }] }
                  )}
                    placeholder="--请输入--" style={{ width: 180 }}

                  />
                </FormItem>

                <FormItem label="&emsp;&emsp;&emsp;&emsp;&emsp;备注：">
                  <Input {...init('remark')} placeholder="--请输入--" style={{ width: 180 }} />
                </FormItem>

              </div>

            </div>
          </Form>

        </IceContainer>

        <IceContainer title="水表信息录入">
          <Form field={this.field}>
            <div style={{ display: 'flex', justifyContent: 'space-around' }}>

              <div style={{ display: 'flex', flexDirection: 'column' }}>

                <FormItem label="水表编号：">
                  <Input  {...init('watermeterId', {
                    rules: [
                      { required: true, message: '水表编号必填' },
                      { pattern: /^\S*$/, message: '不能输入空格' }
                    ]
                  })}
                    style={{ width: '160px' }} onBlur={() => this.handleOnBlur()} />
                </FormItem>

                <FormItem label="水表类型：">
                  {
                    flag ?
                      <Select placeholder="请选择" style={{ width: '160px' }} {...init('watermeterType', {
                        rules: [{ required: true, message: '水表类型必填' }]
                      })}
                        onChange={(value) => this.changeWaterType(value)}
                        disabled={watermeterValue ? true : false}
                      >
                        <Select.Option value="IC卡表">IC卡表</Select.Option>
                      </Select>
                      :
                      <Select placeholder="请选择"
                        style={{ width: '160px', color: 'black' }} {...init('watermeterType', {
                          rules: [{ required: true, message: '水表类型必填' }]
                        })}
                        onChange={(value) => this.changeWaterType(value)}
                        disabled={watermeterValue ? true : false}
                      >
                        <Select.Option value="IC卡表">IC卡表</Select.Option>
                        <Select.Option value="远传表">远传表</Select.Option>
                      </Select>
                  }
                </FormItem>

                <FormItem label="水表厂家：">
                  <Select  {...init('watermeterCompany', {
                    rules: [{ required: true, message: '水表厂家必填' }],
                  })}
                    style={{ width: '160px', color: 'black' }} maxLength={30}
                    dataSource={[
                      { label: '请选择', value: '' },
                      { label: '扬州恒信', value: '扬州恒信' },
                      { label: '深圳华旭', value: '深圳华旭' }
                    ]}
                    disabled={watermeterValue ? true : false} />
                </FormItem>

              </div>

              <div style={{ display: 'flex', flexDirection: 'column' }}>

                <FormItem label="水表口径：">
                  <Select {...init('watermeterCaliber', {
                    rules: [{ required: true, message: "水表口径必填" }]
                  })}
                    placeholder="--请选择--"
                    style={{ width: 160, color: 'black' }}
                    dataSource={watermeterCaliber}
                    disabled={watermeterValue ? true : false}
                    defaultValue={watermeterValue ? watermeterValue.watermeterCaliber : '15'}
                  />
                </FormItem>

                <FormItem label="水表种类：">
                  <Select placeholder="请先选择水表类型"
                    style={{ width: '160px', color: 'black' }} {...init('watermeterKind', {
                      rules: [{
                        required: true,
                        message: '必填'
                      }]
                    })}
                    dataSource={this.state.waterKind}
                    disabled={watermeterValue ? true : false}
                  />
                </FormItem>

                <FormItem label="&emsp;&emsp;表锁号：">
                  <Input {...init('lockNo')} placeholder="--请输入--" style={{ width: 160 }} />
                </FormItem>

              </div>

              <div style={{ display: 'flex', flexDirection: 'column' }}>

                <FormItem label="安装位置：">
                  <Select placeholder="--请选择--" style={{ width: '160px' }} {...init('location', {
                    rules: [{ required: true, message: '安装位置必填' }], initValue: '厨房'
                  })}
                  >
                    <Select.Option value="厨房">厨房</Select.Option>
                    <Select.Option value="卫生间">卫生间</Select.Option>
                    <Select.Option value="车库">车库</Select.Option>
                    <Select.Option value="管道井">管道井</Select.Option>
                    <Select.Option value="仓房">仓房</Select.Option>
                  </Select>
                </FormItem>

                <FormItem label="字轮基数：">
                  <Input {...init('wheelNumber', { rules: [{ required: true, message: '字轮基数必填' }], initValue: '0' })}
                    style={{ width: '160px', color: 'black' }} maxLength={10}
                    disabled={watermeterValue ? true : false} />
                </FormItem>

              </div>

            </div>

            <div style={{ textAlign: 'center', marginTop: 20 }}>
              <Button type="primary" className="button" onClick={() => this.doSave()} loading={openButton}>
                开户
              </Button>
            </div>
          </Form>
        </IceContainer>

      </div>
    )
  }
}
