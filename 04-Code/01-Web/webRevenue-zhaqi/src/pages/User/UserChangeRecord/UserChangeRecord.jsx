import React, {Component} from 'react';
import {Input, Button, Icon, Grid, Table, Balloon, moment, Form, Field, Dialog, Feedback, Select, Radio,} from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import qs from 'qs';
import {url} from '../../../components/URL/index'

const FormItem = Form.Item;
const Toast = Feedback.toast;
const {Group: RadioGroup} = Radio;
const Tooltip = Balloon.Tooltip;

export default class UserChangeRecord extends Component {

  constructor(props) {
    super(props);
    this.state = {
      formValue: [],
      title: 1,
      selectUser: '',
      addVisible: false,
      dataLoading: false,
      list: [],
      status: 0,
      watermeterValue: '',
      tableValue: []
    }
    this.field = new Field(this, {autoUnmount: true});
  }

  componentDidMount() {
    this.queryUser()
  }

  /*查询*/
  queryUser() {
    const {current} = this.state;
    this.setState({dataLoading: true});
    let values = this.field.getValues();
    values.page = current
    /*axios({
      method: 'post',
      url: `${url}revenue/user/query`,
      data: qs.stringify(values),
    })
      .then((response) => {
        this.setState({
          formValue: response.data.datas,
          page: response.data.page,
          pageSize: response.data.pageSize,
          totalSize: response.data.totalSize,
          dataLoading: false
        })
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
        this.setState({dataLoading: false,})
      })*/
    this.setState({dataLoading: false,})
  }

  /*表格操作*/
  renderOper(value, index, record) {

    const add = (<Icon type="survey" size="xs" style={{color: "#1DC11D", cursor: "pointer"}}
                       onClick={() => this.setState({addVisible: true, selectUser: record, tableValue: []})}/>)
    return (
      <div className="operation">
        <Tooltip trigger={add} align='t' text='新表添加'/>
      </div>
    )
  }

  /*格式化日期*/
  formatData(value, index, record) {
    const time = record.createTime ? moment(record.createTime).format('YYYY-MM-DD h:mm:ss') : void (0);
    return time;
  }

  /*关闭弹窗*/
  handleCancel() {
    this.setState({addVisible: false, editVisible: false, status: 0})
  }

  /*渲染表格中状态栏*/
  renderStatus = (value) => {
    if (value == 0) {
      return <span style={{color: '#ff0000', cursor: 'pointer'}}>停用</span>
    } else {
      return <span style={{color: '#1DC11D'}}>启用</span>
    }
  }

  render() {
    const {formValue, addVisible, dataLoading, status, tableValue} = this.state
    const {init} = this.field
    const formItemLayout = {
      labelCol: {fixedSpan: 6},
      wrapperCol: {fixedSpan: 12}
    }
    return (
      <div>

        <section>

          <IceContainer title="搜索">
            <Form direction="hoz" field={this.field} style={{display: 'flex', justifyContent: 'space-around'}}>
              <FormItem label="用户编号：">
                <Input {...init('cno')} placeholder="请输入用户编号"/>
              </FormItem>
              <FormItem label="身份证号：">
                <Input {...init('identityCard')} placeholder="请输用户名称"/>
              </FormItem>
              <FormItem label="联系电话：">
                <Input {...init('phone')} placeholder="请输入联系电话"/>
              </FormItem>
              <Button type="primary" className="button" onClick={() => this.doSearch()}>
                <Icon type="search"/>查询
              </Button>
            </Form>

          </IceContainer>

          <IceContainer title="用户列表">
            <div>
              <Table dataSource={formValue} isLoading={dataLoading}>
                <Table.Column title="用户名" dataIndex="cname" align="center"/>
                <Table.Column title="身份证号" dataIndex="identityCard" align="center"/>
                <Table.Column title="用户地址" dataIndex="address" align="center"/>
                <Table.Column title="联系电话" dataIndex="phone" align="center"/>
                <Table.Column title="用水性质" dataIndex="fname" align="center"/>
                <Table.Column title="所属片区" dataIndex="area" align="center"/>
                <Table.Column title="修改人" dataIndex="area" align="center"/>
                <Table.Column title="用户状态" dataIndex="status" align="center" cell={this.renderStatus}/>
                <Table.Column title="开户日期" dataIndex="createTime"
                              cell={(value, index, record) => this.formatData(value, index, record)}
                              align="center"/>
                <Table.Column title="操作" cell={(value, index, record) => this.renderOper(value, index, record)}
                              align="center" width="100"/>
              </Table>
            </div>
          </IceContainer>

        </section>

        <Dialog style={{width: 900}}
                visible={addVisible}
                onClose={() => this.handleCancel()}
                onCancel={() => this.handleCancel()}
                onOk={() => this.handleOk()}
                title="新表添加">

          <Form field={this.field}>
            <div style={{display: 'flex'}}>
              <FormItem {...formItemLayout} label="水表编号：">
                <Input  {...init('wid', {rules: [{required: true, message: "请输入水表编号"}, { pattern: /^\S*$/, message: '不能输入空格' }]})} placeholder="请输入新表编号"/>
              </FormItem>

              <Button type="primary" onClick={() => this.doSearch()} style={{marginLeft: 20}}>
                <Icon type="search"/>查询
              </Button>
            </div>


            {
              status == 1 ?
                <FormItem {...formItemLayout} label="安装位置：" style={{marginTop: 20}}>
                  <Input {...init('location')} placeholder="请输入安装位置"/>
                </FormItem> :
                void (0)
            }

          </Form>

        </Dialog>

      </div>
    )
  }
}
