import React, {Component} from 'react';
import IceContainer from '@icedesign/container';
import {Icon, Select, Button, Tab, Table, Pagination, Feedback, Form, Field, DatePicker, moment} from '@icedesign/base';
import axios from "axios/index";
import qs from 'qs'
import {url} from "../../../components/URL";

const FormItem = Form.Item
const TabPane = Tab.TabPane
const {Option} = Select
const {MonthPicker} = DatePicker
const { Combobox } = Select;

export default class TaskDistributionTable extends Component {

  constructor(props) {
    super(props);
    this.state = {
      formValue: [],
      disabled: false,
      current: 1,
      pageSize: 1,
      totalSize: 0,
      dataLoading: false,
      selectedRowKeys: [],
      selectedRecord: [],
      regions: [],
      roleName:[],
      areaOrNull:'',
    }
    this.field = new Field(this, {autoUnmount: true});

    this.rowSelection = {

      onChange: (ids, records) => {

        this.setState({
          selectedRowKeys: ids,
          selectedRecord: records,
        })
      },
      getProps: record => {
        return {
          disabled: record.id === 100306660941
        };
      }
    }
  }

  componentDidMount() {
    this.queryTask()
    this.queryRoleName()
    this.getArea()
  }

  /*查询未派发的任务*/
  queryTask() {

      this.setState({dataLoading: true});
      //查询未派发的任务
      let values = this.field.getValues()

      values.page = 1;
      axios({
        method: 'post',
        url: `${url}revenue/copyTask/query`,
        data: qs.stringify(values),
      })
        .then((response) => {
          this.setState({
            formValue: response.data.datas,
            current: 1,
            pageSize: 10,
            totalSize: response.data.totalSize,
            dataLoading: false
          })
        })
        .catch((error) => {
          Feedback.toast.error("请求错误：" + error);
          this.setState({dataLoading: false,})
        })
  }

  /*任务切换分页*/
  changePage(pageIndex){
    this.setState({dataLoading: true})
      let values = []
      values = this.field.getValues();
      values.page = pageIndex
      axios({
        method: 'post',
        url: `${url}revenue/copyTask/query`,
        data: qs.stringify(values),
      })
        .then((response) => {
          this.setState({
            formValue: response.data.datas,
            current: response.data.page,
            pageSize: response.data.pageSize,
            totalSize: response.data.totalSize,
            dataLoading: false
          })
        })
        .catch((error) => {
          Feedback.toast.error("请求错误：" + error);
          this.setState({dataLoading: false,})
        })
  }

  /*查询角色*/
  queryRoleName() {
    axios({
      method: 'post',
      url: `${url}revenue/staff/findByRoleName`,
      data: qs.stringify({roleName: '抄表员'})
    })
      .then((response) => {
        this.setState({
          roleName: response.data.datas,
        })
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
      })
  }
   /*渲染角色*/
   renderRoleName() {
    const {roleName} = this.state
    return roleName && roleName.length > 0 ? roleName.map((item) => {
      return <Option value={item.userId}>{item.realName}</Option>
    }) : void(0)
  }


  /*任务派发*/
  dispatch() {
    const {selectedRecord} = this.state
    let value = selectedRecord
    value.map((item) => {
      item.uid = this.field.getValue('uid')
      item.uname = this.field.getValue('uname');
    })
    this.field.validate((errors, values) => {
      if (errors) {
        return
      } else {
        if (value.length > 0) {
          axios({
            method: 'post',
            url: `${url}revenue/copyTask/add`,
            data: {list: value}
          })
            .then((response) => {
              if (response.data.code == "0") {
                Feedback.toast.success('任务派发成功');
                this.queryTask()
                this.field.reset()
              } else {
                Feedback.toast.error(response.data.msg);
              }
            })
            .catch((error) => {
              Feedback.toast.error("请求错误：" + error);
              this.setState({dataLoading: false,})
            })
        } else {
          Feedback.toast.error('请选择至少一个区域派发');
        }
      }
    })
  }

/*渲染地区*/
area() {
  const {regions} = this.state
  return regions && regions.length > 0 ? regions.map((item) => {
    return <Option value={item.id}>{item.areaName}</Option>
  }) : void (0)
}

  //格式化时间
  formateDate=(value,str)=>{
    return str;
  }

    //区域下拉
    area() {
      const { areas } = this.state;
      return areas && areas.length > 0 ? areas.map((item) => {
          return <Option value={item.id}> {item.name} </Option>
      }) : void (0)
  }

  //区域查询
  getArea() {
      axios({
          method: "get",
          url: `${url}revenue/area/getAll`,
      }).then(response => {
          this.setState({
              areas: response.data.datas
          })
      }).catch(error => {
          Feedback.toast.error("AJAX请求错误", error)
      })
  }




  //区册下拉
  areaCopy() {
      const { areaCopy } = this.state;

      if (areaCopy == null || areaCopy == undefined || areaCopy == []) {
          return <Option value='1' disabled > 请先选择区域 </Option>
      }
      else {
          return areaCopy && areaCopy.length > 0 ? areaCopy.map((item) => {
              return <Option value={item.id}> {item.regionName} </Option>
          }) : void (0)
      }
  }

  //区域onchange
  areaOnChange(value, option){
      this.field.setValue('areaId', value);
      this.setState({
        areaOrNull:value,
      })
      //区册查询
      let areaId=[];

      areaId.areaId=value;
      axios({
          method: 'post',
          url: `${url}revenue/region/paging`,
          data: qs.stringify(areaId)
      }).then(response => {
          this.setState({
              areaCopy: response.data.datas
          })
      }).catch(error => {
          Feedback.toast.error("请求异常", error);
      })

      }

    //重置
    reset() {
      this.field.reset();
      this.setState({
      areaCopy: null,
      })
    }
      onChangeRoleName=(value, option)=>{
        this.field.setValue("uid",value);
        this.field.setValue("uname",option.label);
      }

  render() {
    const {init} = this.field
    const {areaOrNull,formValue, dataLoading, current, pageSize, totalSize, selectedRowKeys} = this.state;

    return (
      <div className='Metermanage'>
        <IceContainer title="选择抄表区域">
          <Form direction="hoz" field={this.field}>
          <FormItem label="区域：">
            <Select {...init('areaId')} placeholder="请选择区域" style={{ width: '180px' }}  onChange={(value,option) => this.areaOnChange(value,option)}>
                {this.area()}
            </Select>
          </FormItem>
          <Form.Item label="区册:" labelCol={{ fixedSpan: 4 }}>
            <Combobox
            {...init("regionIds")}
            placeholder="请选择区册"
            hasClear
            fillProps="label"
            style={{ width: 180 }}
            multiple
            >
             {this.areaCopy()}
             </Combobox>
            </Form.Item>
            <Button type="primary" className="button" onClick={() => this.queryTask()} style={{marginLeft: 20}}>
              <Icon type="search"/>查询
            </Button>
            <Button type="secondary" className="button" onClick={() => this.reset()} style={{marginLeft: 20}}>
                  <Icon type="refresh"/>重置
            </Button>
          </Form>
        </IceContainer>
        <IceContainer title="区域信息列表">
        <Form direction="hoz" field={this.field} style={{display: 'flex', justifyContent: 'flex-end'}}>

                <FormItem label="抄表员：">
                  <Select placeholder="请选择" style={{width: 80}} {...init('uid', {
                    rules: [{required: true, message: "请选抄表员"}]})}
                    onChange={this.onChangeRoleName}
                    >
                     {this.renderRoleName()}
                  </Select>
                </FormItem>
          <Button type="primary" onClick={() => this.dispatch()}>任务派发</Button> :

          </Form>
              <Table dataSource={formValue} isLoading={dataLoading}
              rowSelection={{
                ...this.rowSelection,
                selectedRowKeys: selectedRowKeys,
              }} primaryKey="cno"
              >

                <Table.Column title="卡号" dataIndex="cno" align="center"/>
                <Table.Column title="用户名" dataIndex="cname" align="center"/>
                <Table.Column title="水表编号" dataIndex="watermeterId" align="center"/>
                <Table.Column title="区域" dataIndex="areaName" align="center"/>
                <Table.Column title="区册" dataIndex="regionName" align="center"/>
                <Table.Column title="安装地址" dataIndex="location" align="center"/>
                <Table.Column title="用户地址" dataIndex="address" align="center"/>
                <Table.Column title="上次抄表日期" dataIndex="copyTime" align="center"/>
                <Table.Column title="用水性质" dataIndex="waterName" align="center"/>
                <Table.Column title="上期行度" dataIndex="lastNum" align="center"/>
              </Table>
              <Pagination
                style={{textAlign: 'right', marginTop: 15}}
                onChange={(current) => this.changePage(current)}
                current={current}
                pageSize={pageSize}
                total={totalSize}
                size="small"
              />
        </IceContainer>
      </div>
    )
  }
}
