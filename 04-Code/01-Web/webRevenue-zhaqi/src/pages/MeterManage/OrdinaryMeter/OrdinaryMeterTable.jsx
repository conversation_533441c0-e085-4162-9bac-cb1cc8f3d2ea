/* eslint-disable react/no-unused-state */
import React, {Component} from 'react';
import {Grid,Select,Input, Button, Icon, Table, Pagination, moment, Form, Field, Feedback, DatePicker, Tab} from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import qs from 'qs';
import {url} from '../../../components/URL/index'

const FormItem = Form.Item
const {RangePicker } = DatePicker;
const { Row } = Grid;
export default class OrdinaryMeterTable extends Component {
  static displayName = 'OrdinaryMeterTable';

  static propTypes = {};

  static defaultProps = {};

  constructor(props) {
    super(props);
    this.state = {
      cFormValue:[],
      cCurrent:1,
      cPageSize:1,
      cTotal:1,
      dataLoading: false,
      roleName:[],
    }
    this.field = new Field(this, {autoUnmount: true});
  }

  componentDidMount() {
    this.queryDate();
    this.queryRoleName();
  }

  /*查询*/
  queryDate() {
    const {cCurrent} = this.state;
    let values = this.field.getValues();
    this.setState({dataLoading: true});
      //普通表
      values.page = cCurrent
      axios({
        method: 'post',
        url: `${url}revenue/copyTask/bquery`,
        data: qs.stringify(values),
      })
        .then((response) => {
          this.setState({
            cFormValue: response.data.datas,
            cCurrent: response.data.page,
            cPageSize: response.data.pageSize,
            cTotal: response.data.totalSize,
            dataLoading: false
          })
        })
        .catch((error) => {
          Feedback.toast.error("请求错误：" + error);
          this.setState({dataLoading: false,})
        })
  }

   /*查询角色*/
   queryRoleName() {
    axios({
      method: 'post',
      url: `${url}revenue/staff/findByRoleName`,
      data: qs.stringify({roleName: '抄表员'})
    })
      .then((response) => {
        this.setState({
          roleName: response.data.datas,
        })
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
      })
  }

   /*渲染角色*/
   renderRoleName() {
    const {roleName} = this.state
    return roleName && roleName.length > 0 ? roleName.map((item) => {
      return <Option value={item.userId}>{item.realName}</Option>
    }) : void(0)
  }

  //普通表分页
  changePage1(current){
    this.setState({dataLoading: true});
    let values = this.field.getValues();
    values.page = current

    axios({
      method: 'post',
      url: `${url}revenue/copyTask/bquery`,
      data: qs.stringify(values),
    })
    .then((response) => {
      this.setState({
        cFormValue: response.data.datas,
        cCurrent: current,
        cPageSize: response.data.pageSize,
        cTotal: response.data.totalSize,
        dataLoading: false
      })
    })
    .catch((error) => {
      Feedback.toast.error("请求错误：" + error);
      this.setState({dataLoading: false,})
    })
  }

  rowIndex = (value, index) => {
    const { cPageSize, cCurrent } = this.state;
    if(cCurrent == 1){
      return index + 1;
    }
    else {
      return (index+1)+ (cCurrent-1)*cPageSize;
    }
  }

  queryset(){
    this.field.reset();
  }
  rowIndex1 = (value, index) => {
    const { pageSize, current } = this.state;
    if(current == 1){
      return index + 1;
    }
    else {
      return (index+1)+ (current-1)*pageSize;
    }
  }

  /*格式化日期*/
  formatData(record) {
    const time = record.dateTime ? moment(record.dateTime).format('YYYY-MM-DD h:mm:ss') : void (0);
    return time;
  }

  formateDate=(value,str)=>{
    return str;
  }

  //时间onchang
  timeOnchange(val, str,type){
      if(type=='register'){
          this.field.setValue("rsTime",str[0]);
          this.field.setValue("reTime",str[1]);
      }
      else{
          this.field.setValue("vsTime",str[0]);
          this.field.setValue("veTime",str[1]);
      }
  }

  render() {
    const {cFormValue, dataLoading, cCurrent,cPageSize,cTotal} = this.state
    const {init} = this.field
    return (
      <div>
        <IceContainer title="搜索">
          <Form direction="hoz" field={this.field}>
            <Row>
            <FormItem label="卡号：" >
              <Input {...init('cno')} placeholder="请输入卡号"/>
            </FormItem>
            <FormItem label="抄表员：" labelCol={{ fixedSpan: 5 }}>
              <Select {...init('uid')} style={{width: 150}}>
               {this.renderRoleName()}
             </Select>
            </FormItem>
            <FormItem label="抄表时间：" labelCol={{ fixedSpan: 6 }}>
            <RangePicker onChange={(val, str) => this.timeOnchange(val, str,"register")}
                       placeholder="请选择抄表区间"
                    />
            </FormItem>
            </Row>
          </Form>
          <div style={{ marginRight: 20}} align="right">
            <Button type="primary" className="button" onClick={() => this.queryDate()} style={{marginRight: 20}}>
              <Icon type="search"/>查询
            </Button>
            <Button type="secondary" className="button" onClick={() => this.queryset()}>
              <Icon type="refresh" />  重置
            </Button>
          </div>

        </IceContainer>

        <IceContainer title="普通表数据列表">
              <Table dataSource={cFormValue} isLoading={dataLoading}>
              <Table.Column title="序号" cell={this.rowIndex} align="center" width={60}/>
              <Table.Column title="水表编号" dataIndex="watermeterId" align="center"/>
              <Table.Column title="卡号" dataIndex="cno" align="center"/>
              <Table.Column title="用户名" dataIndex="cname" align="center"/>
              <Table.Column title="区域" dataIndex="areaName" align="center"/>
              <Table.Column title="区册" dataIndex="regionName" align="center"/>
              <Table.Column title="上期行度" dataIndex="lastNum" align="center"/>
              <Table.Column title="本期行度" dataIndex="thisNum" align="center"/>
              <Table.Column title="抄表日期" dataIndex="copyTime" align="center"/>
              <Table.Column title="抄表员" dataIndex="uname" align="center"/>
              </Table>
              <Pagination
                style={{textAlign: 'right', marginTop: 15}}
                current={cCurrent}
                pageSize={cPageSize}
                total={cTotal}
                size="small"
                onChange={(current) => this.changePage1(current)}
              />
        </IceContainer>
      </div>
    )
  }
}
