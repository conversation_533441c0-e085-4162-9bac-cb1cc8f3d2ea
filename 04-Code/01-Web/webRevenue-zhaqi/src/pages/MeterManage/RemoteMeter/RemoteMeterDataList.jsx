/* eslint-disable react/no-unused-state */
import React, {Component} from 'react';
import {Switch ,Input, Button, Icon, Table, Pagination, moment, Form, Field, Feedback, DatePicker, Tab} from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import qs from 'qs';
import {url} from '../../../components/URL/index'

const FormItem = Form.Item
const TabPane = Tab.TabPane
const { MonthPicker, YearPicker, RangePicker } = DatePicker;

export default class RemoteMeterTable extends Component {
  static displayName = 'RemoteMeterTable';

  static propTypes = {};

  static defaultProps = {};

  constructor(props) {
    super(props);
    this.state = {
      formValue: [],
      current: 1,
      pageSize: 1,
      totalSize: 0,
      title: 1,
      selectUser: '',
      visible: false,
      dataLoading: false,
      openValve: true,
    }
    this.field = new Field(this, {autoUnmount: true});
  }

  componentDidMount() {
    this.queryDate();
  }
  /*查询*/
  queryDate() {
    const {current} = this.state;
    let values = this.field.getValues();
    this.setState({dataLoading: true});
    values.page = current
    axios({
      method: 'post',
      url: `${url}revenue/copy/query`,
      data: qs.stringify(values),
    })
      .then((response) => {
        this.setState({
          formValue: response.data.datas,
          current: response.data.page,
          pageSize: response.data.pageSize,
          totalSize: response.data.totalSize,
          dataLoading: false
        })
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
        this.setState({dataLoading: false,})
      });
  }

  /*切换分页*/
  changePage(pageIndex) {

    this.setState({current: pageIndex})
    this.setState({dataLoading: true});
    let values = this.field.getValues();
    values.page = pageIndex

    axios({
      method: 'post',
      url: `${url}revenue/copy/query`,
      data: qs.stringify(values),
    })
      .then((response) => {

        this.setState({
          formValue: response.data.datas,
          current: pageIndex,
          pageSize: response.data.pageSize,
          totalSize: response.data.totalSize,
          dataLoading: false
        })
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
        this.setState({dataLoading: false,})
      });
  }

  rowIndex = (value, index) => {

    const { cPageSize, cCurrent } = this.state;
    if(cCurrent == 1){
      return index + 1;
    }
    else {
      return (index+1)+ (cCurrent-1)*cPageSize;
    }
  }

  rowIndex1 = (value, index) => {

    const { pageSize, current } = this.state;
    if(current == 1){
      return index + 1;
    }
    else {
      return (index+1)+ (current-1)*pageSize;
    }
  }

  /*格式化日期*/
  formatData(record) {
    const time = record.dateTime ? moment(record.dateTime).format('YYYY-MM-DD') : void (0);
    return time;
  }

  /*渲染阀门状态*/
  renderState(value) {
    let state = value === '1' ? <span style={{color:'#1DC11D'}}>开启</span> : <span style={{color:'#ff0000'}}>关闭</span>
    return state
  }

  formateDate=(value,str)=>{
    return str;
  }

   //时间onchang
   timeOnchange(val, str,type){
    if(type=='register'){
        this.field.setValue("re",val);
        this.field.setValue("rsTime",str[0]);
        this.field.setValue("reTime",str[1]);
    }
    else{
        this.field.setValue("vs",val);
        this.field.setValue("vsTime",str[0]);
        this.field.setValue("veTime",str[1]);
    }
  }
  //重置
  reset() {
    this.field.reset();
  }

  /*渲染switch*/
  turnOnOrOff = (value, index, record) => {
    if(record.valveState == '1'){
      return (
      <Switch checkedChildren="关闭"  unCheckedChildren="开启" defaultChecked={1} onChange={this.onChange}/>
      )
    }
    else{
      return (<Switch checkedChildren="开启"  unCheckedChildren="关闭" onChange={this.onChange}/>)
    }
  }

  onChange = (checked) => {
   console.log(checked);
  }

  render() {
    const {formValue,current, totalSize, pageSize, dataLoading} = this.state
    const {init} = this.field
    return (
      <div>
        <IceContainer title="搜索">
          <Form direction="hoz" field={this.field} style={{display: 'flex', justifyContent: 'space-around'}}>
              <FormItem label="水表编号：">
                <Input {...init('tableNo')} placeholder="请输入ID"/>
              </FormItem>
            <FormItem label="抄表时间：">
            <RangePicker
                        {...init('dateTime')}
                       onChange={(val, str) => this.timeOnchange(val, str,"register")}
                       placeholder="请选择抄表时间"
                    />
            </FormItem>
            <Button type="primary" className="button" onClick={() => this.queryDate()}>
              <Icon type="search"/>查询
            </Button>
            <Button type="secondary" className="button" onClick={() => this.reset()} style={{marginLeft: 20}}>
                  <Icon type="refresh"/>重置
            </Button>
          </Form>
        </IceContainer>
        <IceContainer title="远传表数据列表">
              <Table dataSource={formValue} isLoading={dataLoading} >
               <Table.Column title="序号" cell={this.rowIndex1} align="center"/>
                <Table.Column title="水表编号" dataIndex="tableNo" align="center"/>
                <Table.Column title="字轮基数" dataIndex="wheelReading" align="center"/>
                <Table.Column title="抄表时间" dataIndex="dateTime"
                              cell={(value, index, record) => this.formatData(record)} align="center"/>
                <Table.Column title="阀门状态" dataIndex="valveState" cell={(value) => this.renderState(value)}
                              align="center"/>
                <Table.Column title="操作" cell={this.turnOnOrOff}/>
              </Table>
              <Pagination
                style={{textAlign: 'right', marginTop: 15}}
                current={current}
                pageSize={pageSize}
                total={totalSize}
                onChange={(current) => this.changePage(current)}
              />
        </IceContainer>
      </div>
    )
  }
}
