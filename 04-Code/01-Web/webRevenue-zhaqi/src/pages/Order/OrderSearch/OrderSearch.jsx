import React, {Component} from 'react';
import {
  <PERSON>oon,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  DatePicker,
  <PERSON>edback,
  Field,
  Form,
  Icon,
  Input,
  moment,
  Pagination,
  Select,
  Table,
} from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import qs from 'qs';
import HenxCardBreak from './components/HenxCardBreak'
import {url} from '../../../components/URL/index'
import readCard from "../../../common/readCard"
import sellWateCard from "../../../common/sellWateCard"
import ViewOrderDetail from '../../../common/ViewOrderDetail'
import {areaCode, systemCode42} from '../../../components/areaCode/areaCode'
import PrintInfos
  from '../../InvoiceManage/MakeOutinvoiceRecords/components/Print';
import getLodop from "../../../common/LodopFuncs";
import WaterPrice from "../../../common/waterPrice";
import Invoicing from "./components/Invoicing";

const FormItem = Form.Item;
const { RangePicker } = DatePicker;
const { Combobox } = Select
const Tooltip = Balloon.Tooltip
export default class OrderSearch extends Component {

  constructor(props) {
    super(props);
    this.state = {
      areaList: [],  //片区
      regionList: [], //小区
      feeNameList: [], //用水性质
      formValue: [],  //表格数据
      roleNameList: [], //查询操作员
      selectedRecord: [],    //选中表格数据
      formwork: [],    //发票模板
      dataLoading: false,
      printStatus: false,   //批量打印状态
      page: 1,
      pageSize: 10,
      totalSize: 0,
      dialogVisible: false,
      reallyTotalFee: 0,
      sewageTotalFee: 0,
      waterTotalFee: 0,
      totalTunnage: 0,
      sourceTotalFee: 0,
      visible: false,
      stuffId: sessionStorage.getItem("stuffId"),
      createName: sessionStorage.getItem("createName"),
      modifyOrderStatusModal: false,
    }
    this.field = new Field(this, { autoUnmount: true })
    this.rowSelection = {
      onChange: (ids, records) => {
        this.setState({ selectedRowKeys: ids, selectedRecord: records })
      },
      getProps: record => {
        return {
          disabled: record.isNote != '2'
        };
      }
    }
  }

  componentDidMount() {
    let cno = this.props.location.state;
    if (cno == undefined) {
      this.field.setValue("csTime", moment(new Date()).format('YYYY-MM-DD'));
      this.field.setValue("ceTime", moment(new Date()).format('YYYY-MM-DD'));
      this.queryOrder(1, 10)
    }
    else {
      this.field.setValue('cno', cno.cno);
      this.doSearch(cno)
    }
    this.queryFeeName()
    this.queryArea()
    this.queryRoleName()
  }

  //查询表格数据
  queryOrder(page, pageSize) {
    this.setState({ dataLoading: true, selectedRowKeys: [] })
    let values = this.field.getValues()
    values.page = page
    values.pageSize = pageSize
    axios({
      method: 'post',
      url: url + 'revenue/order/query',
      data: qs.stringify(values)
    })
      .then((response) => {
        this.querSumReallyAndWaterAndSewageFee(values)
        this.setState({
          formValue: response.data.datas,
          page: response.data.page,
          pageSize: response.data.pageSize,
          totalSize: response.data.totalSize,
          dataLoading: false,
        })
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
        this.setState({ dataLoading: false, })
      })
  }

  /*切换分页*/
  changePage(page) {
    const { pageSize } = this.state
    window.scrollTo(0, 500)
    this.queryOrder(page, pageSize)
    this.setState({ selectedRowKeys: [] }) //重置批量勾选
  }

  //改变显示记录数
  changePageSize = (pageSize) => {
    this.queryOrder(1, pageSize)
  }

  /*子组件改变父组件的state*/
  onChangeState(stateName) {
    this.setState(stateName)
  }

  //查询实收总金额，总清水费，总污水费
  querSumReallyAndWaterAndSewageFee(values) {
    axios({
      method: 'post',
      url: url + 'revenue/order/sumReallyAndWaterAndSewageFee',
      data: qs.stringify(values)
    })
      .then((response) => {
        if (response.data.code == '0') {
          let resultDates = response.data.datas
          this.setState({
            reallyTotalFee: resultDates.reallyTotalFee,
            sewageTotalFee: resultDates.sewageTotalFee,
            waterTotalFee: resultDates.waterTotalFee,
            totalTunnage: resultDates.totalTunnage,
            sourceTotalFee: resultDates.sourceTotalFee
          })
        } else {
          Feedback.toast.error(response.data.msg);
        }

      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
        this.setState({ dataLoading: false, })
      });
  }

  //调用dll控件写卡,写卡成功后调用接口改变订单状态
  dllSalewater(record) {
    let cardno = record.cardNo
    let times = Number(record.totalTimes)
    let totalwater = record.totalTunnage
    let water = 0
    try {
      let i = hxdll.user_card()
      let type = i.substring(1, 2)
      let cardNo = 0
      if (type == '3') {
        cardNo = i.substring(2, 12)
      } else {
        cardNo = i.substring(2, 10)
        water = record.tunnage
      }
      if (i == 10) {
        Feedback.toast.error("读卡失败:无卡")
      } else if (i == 100) {
        Feedback.toast.error("读卡失败:读卡失败")
      } else if (i == 101) {
        Feedback.toast.error("读卡失败:读卡失败")
      } else if (i == 102) {
        Feedback.toast.error("读卡失败:非用户卡")
      } else {
        //如果读卡器上有卡或者卡类型正确执行售水
        //判断卡是不是本人的
        if (cardNo != record.cardNo) {
          alert("读卡器上卡与用户卡号不匹配");
          return;
        } else {
          let result = hxdll.sellconfirm(cardno, areaCode, times - 1, water, totalwater, 0, 0, 0, 0, 0, type)
          if (result == 0) {
            axios({
              method: 'post',
              url: `${url}revenue/order/complete`,
              data: qs.stringify({ id: record.id }),
            })
              .then((response) => {
                if (response.data.code == '0') {
                  Feedback.toast.success("写卡成功")
                  this.queryOrder()
                } else {
                  Feedback.toast.error("写卡失败" + response.data.msg)
                }
              })
              .catch((error) => {
                Feedback.toast.error("请求错误：" + error);
              })
          } else {
            Feedback.toast.error('写卡失败：' + sellWateCard(result) + '请重新尝试')
          }
        }
      }
    } catch (e) {
      alert('浏览器不支持ActiveX控件，请使用IE');
    }

  }

  //调用dll控件写卡,写卡成功后调用接口改变订单状态(华旭4428)
  dllSalewaterhuax(record) {
    let cno = 'FFFFFF' + record.cardNo
    let watermeterId = record.watermeterId.substring(0, 8)
    let times = record.totalTimes
    let water = record.tunnage
    try {
      //系统码|子表号|电子表号|购买量|关阀报警|囤积限量|购水次数|有效卡标志| IC卡号|用户编码|
      let parameter = systemCode42 + '|1|' + watermeterId + '|' + water + '|3|1500|' + times + '|0|1|' + cno + '|'
      let result = SZHXMETERCARD_Web.HXCD_4442_UserCard_Web(parameter).split("|")
      if (result[0] > 0) {

        axios({
          method: 'post',
          url: `${url}revenue/order/complete`,
          data: qs.stringify({ id: record.id }),
        })
          .then((response) => {
            if (response.data.code == '0') {
              Feedback.toast.success("写卡成功")
              this.queryOrder()
            } else {
              Feedback.toast.error("写卡失败" + response.data.msg)
            }
          })
          .catch((error) => {
            Feedback.toast.error("请求错误：" + error);
          })
      } else {
        Feedback.toast.error("写卡失败" + result[1])
      }
    } catch (e) {
      Feedback.toast.error('浏览器不支持ActiveX控件，请使用IE');
    }
  }
  setModifyOrderStatusModal = (record) => {
    this.setState({ modifyOrderStatusModal: true, currentRecordId: record.id });
  };

  onModifyOrderStatusModalClose = () => {
    this.setState({ modifyOrderStatusModal: false, currentRecordId: null });
  };

  modifyOrderStatus = () => {
    const createId = sessionStorage.getItem('stuffId');
    const createName = sessionStorage.getItem('realName');
    axios
    .post(`${url}revenue/order/updateOrderStatus`, {
      id: this.state.currentRecordId,
      createName,
      createId,
    })
    .then((response) => {
      const { status, data } = response;
      if (status === 200 && data.code === '0') {
        Feedback.toast.success('操作成功！');
        this.queryOrder(1, 10);
      } else {
        Feedback.toast.error('操作失败！');
      }
      this.onModifyOrderStatusModalClose();
    });
  };

  //写卡按钮
  writeCard(record) {
    let result = 0
    if (record.watermeterKind === '预付费4442') {
      try {
        let result = (SZHXMETERCARD_Web.HXCD_4442_DisposeData_Web('1|1|', "")).split("|")
        if (result[0] == 1) {
          if (result[2] == 0) {
            Feedback.toast.error('上次缴费未刷表')
          } else {
            this.dllSalewaterhuax(record)
          }
        } else if (result[0] == 9) {
          this.dllSalewaterhuax(record)
        } else {
          Feedback.toast.error(result[1])
        }
      } catch (error) {
        Feedback.toast.error('浏览器不支持ActiveX控件，请使用IE');
      }
    } else {
      try {
        let readstr = hxdll.user_card()
        let type = readstr.substring(1, 2)
        if (readstr.length > 5) {
          if (type == '3') {
            //圆卡
            this.dllSalewater(record)
          } else if (type == '1') {
            let state = readstr.substr(-4)
            if (state == '0000') {
              //钥匙卡
              this.dllSalewater(record)
            } else {
              alert("该卡还未在水表上刷过卡无法写卡,请提醒用户回去刷卡。")
            }
          }
        } else {
          Feedback.toast.error("读卡失败:" + readCard(readstr))
        }
      } catch (e) {
        Feedback.toast.error('浏览器不支持ActiveX控件，请使用IE');
      }
    }
  }

  /*表格操作*/
  renderOper(record) {
    let flag = moment(record.createTime).format('YYYY-MM-DD') == moment(new Date()).format('YYYY-MM-DD')
    const createId = sessionStorage.getItem('stuffId');
    return (
      /**
       * 1.卡表
       *   2.1 判断订单来源是否是微信
       *       2.1.1 若是微信，则判断其订单状态是否位“已支付未刷卡”，若是，则可以退
       *       2.1.2 若订单状态是“已完成”，则判断是否是当天订单 如果是则可以退,否则不可退（如果是卡表，则需要判断是否是已使用）
       *   2.2 若订单来源是柜台，则判断是否是当天订单 如果是则可以退 不是则不能退
       *
       * 2.非卡表
       *   2.1 判断是否是当天订单，否则不可以
       *   2.2 若是当天，则判断是否是充值还是账单缴费，若是账单缴费不可退款，否则可以退
       */

      <div style={{ display: 'flex', justifyContent: 'space-around' }}>

        <ViewOrderDetail record={record} type={1} />

        {
          /*卡表*/
          record.watermeterType === 'IC卡表' ?
            //判断是否是扬州恒信
            //判断是否是微信
            record.source == '微信' ?
              record.status == '1' ?
                <HenxCardBreak record={record}
                  queryOrder={this.queryOrder.bind(this)} /> : record.status == '3' && flag ?
                  <HenxCardBreak record={record} queryOrder={this.queryOrder.bind(this)} /> :
                  <Icon title='不可退' type="history" size="small" style={{ color: "#A9A9A9", cursor: 'pointer' }} />
              :
              //判断是否是柜台
              flag && record.status != '2' ?
                <HenxCardBreak record={record} queryOrder={this.queryOrder.bind(this)} /> :
                <Icon title='不可退' type="history" size="small" style={{ color: "#A9A9A9", cursor: 'pointer' }} />
            :
            /*机械表*/
            record.watermeterType === '机械表' ?
              record.status == '3' || record.status == '0' ?
                <HenxCardBreak record={record} queryOrder={this.queryOrder.bind(this)} /> :
                <Icon title='不可退' type="history" size="small" style={{ color: "#A9A9A9", cursor: 'pointer' }} />
              :
              /*远传表*/
              /*判断是否是当日订单*/
              flag ?
                /*判断充值还是缴费*/
                 record.status == '3' ?
                  <HenxCardBreak record={record} queryOrder={this.queryOrder.bind(this)} /> :
                  <Icon title='不可退' type="history" size="small" style={{ color: "#A9A9A9", cursor: 'pointer' }} /> :
                  <Icon title='不可退' type="history" size="small" style={{ color: "#A9A9A9", cursor: 'pointer' }} />
        }

        {
          record.status == '1' ?
            <Icon title='写卡' type="image-text" size="small" style={{ color: "#FFA003", cursor: 'pointer' }}
              onClick={() => this.writeCard(record)}
            />
            :
            <Icon title='已写卡' type="image-text" size="small" style={{ color: "#A9A9A9", cursor: 'pointer' }} />
        }

        {
          (record.watermeterType == 'IC卡表'||record.watermeterType == '远传表') ?
            <PrintInfos record={record} type="print" /> : ''
        }
        {record.watermeterType === 'IC卡表' &&
        record.status === '3' &&
        (createId === '7b48eca4d2fd4cb8b13887360ee88ff4' || createId === '265a8646f3b641978d2daa4bd10c2e38'|| createId === 'a4dcaff9ff46443485e651a234eb8a37'
        || createId === '00fc8edce47d44b2a3d24db10d46386e'|| createId === '98b7e742d1d84b9cacb5378c42b6e552'
        ) ? (
          // <BalloonConfirm
          //     onConfirm={() => this.modifyOrderStatus(record, createId, createName)}
          //     // onCancel={}
          //     title="确定要修改订单状态为已支付待刷卡吗"
          //     style={{ width: 180 }}
          // >
          // 	</BalloonConfirm>
          <Icon
            title="修改订单状态"
            type="process"
            size="small"
            onClick={() => this.setModifyOrderStatusModal(record)}
          />
        ) : null}
        {(record.status === '1' || record.status === '3') &&
        (record.isTax === '0' || record.isTax === '2'
                || record.isTax === '3') ? (
                <Invoicing record={record}
                           queryOrder={() => this.queryOrder(1, 10)}/>
        ) : (
                void 0
        )}
      </div>
    )
  }

  doSearch = (cno) => {
    if (cno == undefined) {
      return
    } else {
      this.setState({ dataLoading: true });
      axios({
        method: 'post',
        url: url + 'revenue/order/query?n=' + Math.random(),
        data: qs.stringify(cno)
      })
        .then((response) => {
          this.querSumReallyAndWaterAndSewageFee(cno)
          this.setState({
            formValue: response.data.datas,
            current: response.data.page,
            pageSize: response.data.pageSize,
            totalSize: response.data.totalSize,
            dataLoading: false,
          })
        })
        .catch((error) => {
          Feedback.toast.error("请求错误：" + error);
          this.setState({ dataLoading: false, })
        });
    }

  }

  /*渲染表格中状态栏*/
  renderPayWay = (value) => {
    if (value == 0) {
      return <span>刷卡</span>
    } else if (value == 1) {
      return <span>现金</span>
    } else  if (value == 2){
      return <span>微信</span>
    }else  if (value == 7){
      return <span>蒙速办-微信</span>
    }
  }

  //重置
  reset() {
    this.field.reset('cno');
    this.field.reset('areaId');
    this.field.reset('regionId');
    this.field.reset('payWay');
    this.field.reset('statusli');
    this.field.reset('cardNo');
    this.field.reset('watermeterType');
    this.field.reset('watermeterKind');
    this.field.reset('createId');
    this.field.reset('startAmount');
    this.field.reset('endAmount');
    this.field.reset('watermeterId');
    this.field.reset('source');
    this.field.reset('feeId');
    this.field.reset('startTunnage');
    this.field.reset('endTunnage');
    this.field.reset('isNote');
    this.setState({ selectedRowKeys: [] }) //重置批量勾选
  }

  //读卡
  doRead() {
    try {
      //读恒信卡
      let readtype = hxdll.chk_card()
      //读华旭卡
      let readtypeHuaxu = (SZHXMETERCARD_Web.HXCD_4442_DisposeData_Web('1|1|', "")).split("|")
      if (readtype > 1 && readtypeHuaxu[0] < 1) {
        if (readtype == '11' || readtype == '25') {
          let readstr = hxdll.user_card()
          let type = readstr.substring(1, 2)
          let cardNo = 0
          if (type == '3' || type == '5') {
            cardNo = readstr.substring(2, 12)
          } else {
            cardNo = readstr.substring(2, 10)
          }
          if (readstr.length > 5) {
            this.field.setValue('cardNo', cardNo)
            this.queryOrder(1, 10)
          } else {
            Feedback.toast.error("恒信卡读卡失败：" + readCard(readstr));
          }
        } else {
          switch (readtype) {
            case 0:
              Feedback.toast.error("恒信卡读卡失败：设备失败")
              return
            case 1:
              Feedback.toast.error("恒信卡读卡失败：读卡器无卡")
              return
            case 2:
              Feedback.toast.error("恒信卡读卡失败：不存在的卡型，非恒信卡")
              return
            case 3:
              Feedback.toast.error("恒信卡读卡失败：读卡失败")
              return
            case 4:
              Feedback.toast.error("恒信卡读卡失败：坏卡")
              return
            default:
              Feedback.toast.error("恒信卡读卡失败：非用户卡")
              return
          }
        }
      } else if (readtypeHuaxu[0] > -1 && readtype < 2) {
        if (readtypeHuaxu[0] == 1 || readtypeHuaxu[0] == 9) {
          if (readtypeHuaxu[0] == 9) {
            let cardNo = readtypeHuaxu[3].substring(6, readtypeHuaxu[3].length)
            this.field.setValue('cardNo', cardNo)
            this.queryOrder(1, 10)
          } else {
            let cardNo = readtypeHuaxu[26].substring(6, readtypeHuaxu[26].length)
            this.field.setValue('cardNo', cardNo)
            this.queryOrder(1, 10)
          }
        } else {
          Feedback.toast.error("华旭卡读卡失败：" + readtypeHuaxu[1])
        }
      } else if (readtypeHuaxu[0] > -1 && readtype > 1) {
        Feedback.toast.error("读卡器上存在多张卡请检查！")
      } else if (readtype == 1 && readtypeHuaxu[1] == '未插卡!') {
        Feedback.toast.error("读卡器无卡")
      } else {
        Feedback.toast.error("设备失败")
      }

    } catch (e) {
      Feedback.toast.error("网络连接错误")
    }
  }

  /*渲染订单状态*/
  renderStatus = (value) => {
    switch (value) {
      case '0':
        return <span style={{ color: '#ff5711' }}>未完成</span>
      case '1':
        return <span style={{ color: '#ffa631' }}>已支付未刷卡</span>
      case '2':
        return <span style={{ color: '#ff0000' }}>退款</span>
      case '3':
        return <span style={{ color: '#1DC11D' }}>已完成</span>
    }
  }

  //渲染发票状态
  renderNote = (value) => {
    if (value == 2) {
      return <span>未开发票</span>
    } else {
      return <span>已开发票</span>
    }
  }

  renderTax = (value) => {
    if (value == 0) {
      return <span>未开电票</span>
    } else {
      return <span>已开电票</span>
    }
  }

  /*格式化日期*/
  formatData(value) {
    const time = value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : void (0);
    return time;
  }

  //时间onchang
  timeOnchange(val, str) {
    this.field.setValue("csTime", str[0]);
    this.field.setValue("ceTime", str[1]);
  }

  /*关闭弹窗*/
  onClose = () => {
    this.setState({ visible: false })
  }

  //查询用水性质
  queryFeeName() {
    axios({
      method: 'post',
      url: `${url}revenue/fee/queryList`,
    })
      .then((response) => {
        let feeNameList = []
        response.data.datas.map((item) => {
          feeNameList.push({ label: item.name, value: item.id })
        })
        this.setState({ feeNameList: feeNameList })
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
      })
  }

  //查询片区
  queryArea() {
    axios({
      method: 'get',
      url: url + 'revenue/area/getAll'
    })
      .then((response) => {
        let areaList = []
        response.data.datas.map((item) => {
          areaList.push({ label: item.name, value: item.id })
        })
        this.setState({ areaList: areaList })
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
      })

  }

  //查询操作员
  queryRoleName() {
    axios({
      method: 'post',
      url: `${url}revenue/order/createName`,
    })
      .then((response) => {
        if (response.data.code == 0) {
          this.setState({
            roleNameList: response.data.datas
          })
        }
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
      })
  }

  //字段过长隐藏
  omit(value) {
    const look = (
      <section style={{ whiteSpace: "nowrap", textOverflow: 'ellipsis', overflow: 'hidden' }}>{value}</section>)
    return <Tooltip trigger={look} align='t' text={value} style={{ backgroundColor: '#f9f9f9' }} />
  }

  //片区onchange
  onChange(value) {
    this.field.setValue('areaId', value);
    this.field.reset('regionId');
    this.field.reset('createId');
    //区册查询
    axios({
      method: 'post',
      url: `${url}revenue/region/regionlist`,
      data: qs.stringify({ areaId: value })
    }).then(response => {
      let regionList = []
      response.data.datas.map((item) => {
        regionList.push({ label: item.regionName, value: item.id })
      })
      this.setState({ regionList: regionList })
    }).catch(error => {
      Feedback.toast.error("请求异常", error)
    })
    //操作员联动
    //区册查询
    axios({
      method: 'post',
      url: `${url}revenue/order/createName`,
      data: qs.stringify({ areaId: value })
    })
      .then((response) => {
        if (response.data.code == 0) {
          this.setState({
            roleNameList: response.data.datas
          })
        }
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
      })
  }

  //打印打开打印弹窗
  printAll() {
    const { selectedRecord } = this.state
    if (selectedRecord.length > 0) {
      axios({
        method: 'post',
        url: `${url}revenue/printTemplate/get`,
        data: qs.stringify({templateName: '带阶梯的卡表模板'}),
      }).then(response => {
        if (response.data.code == "0") {
          this.setState({formwork: response.data.datas});
          this.print()
        }
      }).catch(error => {
        Feedback.toast.error("系统异常请稍后再试", error);
      })
      axios({
        method: 'post',
        url: `${url}revenue/printTemplate/get`,
        data: qs.stringify({templateName: '带阶梯的远传表模板'}),
      }).then(response => {
        let ajaxData = response.data;
        if (ajaxData.code == "0") {
          this.setState({formworkYC: ajaxData.datas});
        }
      }).catch(error => {
        Feedback.toast.error("系统异常请稍后再试", error);
      })
    } else {
      Feedback.toast.error("请至少勾选一笔订单");
    }
  }

  //批量打印
  print() {
    this.setState({ printStatus: true })
    let LODOP = getLodop()
    const {
      formwork,
      formworkYC,
      stuffId,
      createName,
      selectedRecord
    } = this.state
    //模板数据
    let templateCode = formwork.templateCode
    let templateCodeYC = formworkYC.templateCode
    //打印记录所需参数
    let values = []
    values.printName = createName
    values.printId = stuffId
    values.invoiceType = '0'
    values.printType = '2'
    values.invoiceCode = this.field.getValue('invoiceCode')
    values.isNote = '1'
    //循环打印
    selectedRecord.map((item, index) => {
      values.orderId = item.id
      values.cno = item.cno
      values.cname = item.cname
      values.createName = item.createName
      values.createId = item.createId
      values.createTime = moment(item.createTime).format('YYYY-MM-DD HH:mm:ss')
      values.waterTotalFee = item.waterTotalFee
      values.sewageTotalFee = item.sewageTotalFee
      values.waterSourceFee = item.waterSourceFee
      values.reallyFee = item.reallyFee
      values.tunnage = item.tunnage
      values.lastTunnage = item.totalTunnage
      values.feeString = item.feeString
      let lastSave = '上次结存：' + item.account
      let thisSave = '本次结存：' + item.changeAmount
      let flag = ''
      values.invoiceNo = this.field.getValue("invoiceNo")
      //水资源费
      let sourceTotalFee = item.sourceTotalFee ? item.sourceTotalFee : '0'
      let wsf = this.test(record.descr).v1;
      let qsf = this.test(record.descr).v2;
      //判断是什么类型的表
      if (item.watermeterType == 'IC卡表') {
        //水价明细
        let waterPrice = WaterPrice(item)
        // let temp = templateCode.replace("日期", moment(item.createTime).format('YYYY-MM-DD'))
        //   .replace("用户编号", item.cno)
        //   .replace("用户名", item.cname)
        //   .replace("应收金额1", item.reallyFee)
        //   .replace("水量1", item.tunnage)
        //   .replace("清水单价", waterPrice)
        //   .replace("污水单价", item.sewageTotalFee)
        //   .replace("操作员", item.createName)
        //   .replace("日期1", moment(item.createTime).format('YYYY-MM-DD'))
        //   .replace("用户编号1", item.cno)
        //   .replace("用户名1", item.cname)
        //   .replace("应收金额2", item.reallyFee)
        //   .replace("水量2", item.tunnage)
        //   .replace("清水单价1", waterPrice)
        //   .replace("污水单价1", item.sewageTotalFee)
        //   .replace("操作员1", item.createName)
        //   .replace("发票代码1", '')
        //   .replace("发票号码1", '')
        //   .replace("发票代码2", '')
        //   .replace("发票号码2", '')
        //   .replace("水资源费1", sourceTotalFee)
        //   .replace("水资源费2", sourceTotalFee)
        //   .replace("上次结存1", '')
        //   .replace("本次结存1", '')
        //   .replace("上次结存2", '')
        //   .replace("本次结存2", '')
        //   .replace("现示数1", item.totalTunnage)
        //   .replace("现示数2", item.totalTunnage)

        let temp = templateCode.replace("日期",
          moment(new Date()).format('YYYY-MM-DD'))
        .replace("用户名", item.cname)
        .replace("用户编号", item.cno)
        .replace("用水性质1", item.feeName)
        .replace("水量1", item.tunnage)
        .replace("单价1", item.descr)
        .replace("单价q", qsf)
        .replace("单价w", wsf)
        .replace("金额1", item.reallyFee)
        .replace("操作员", item.createName)
        .replace("日期1", moment(item.createTime).format('YYYY-MM-DD'))
        .replace("用户名1", item.cname)
        .replace("用户编号1", item.cno)
        .replace("用水性质2", item.feeName)
        .replace("水量2", item.tunnage)
        .replace("单价2", item.descr)
        .replace("单价q2", qsf)
        .replace("单价w2", wsf)
        .replace("金额2", record.reallyFee)
        .replace("操作员1", item.createName)

        eval(temp);
        flag = LODOP.PRINT()
        if (flag == 1) {
          axios({
            method: 'post',
            url: `${url}revenue/invoiceRecord/save`,
            data: qs.stringify(values),
          }).then(response => {
            if (response.data.code == '0') {
              //打印完刷新页面
              if (index == selectedRecord.length - 1) {
                this.queryRef()
              }
            }
          }).catch(error => {
            Feedback.toast.error("系统异常请稍后再试" + error);
          })
        }
      } else if (item.watermeterType == '远传表') {
        //水价明细
        let waterPrice = WaterPrice(item)
        let wsf = this.test(record.descr).v1;
        let qsf = this.test(record.descr).v2;
        // let temp =
        //   templateCodeYC
        //   .replace("日期", moment(item.createTime).format('YYYY-MM-DD'))
        //   .replace("用户编号", item.cno)
        //   .replace("用户名", item.cname)
        //   .replace("应收金额1", item.reallyFee)
        //   .replace("水量1", '')
        //   .replace("清水单价", waterPrice)
        //   .replace("污水单价", item.sewageTotalFee)
        //   .replace("操作员", item.createName)
        //   .replace("日期1", moment(item.createTime).format('YYYY-MM-DD'))
        //   .replace("用户编号1", item.cno)
        //   .replace("用户名1", item.cname)
        //   .replace("应收金额2", item.reallyFee)
        //   .replace("水量2", '')
        //   .replace("清水单价1", waterPrice)
        //   .replace("污水单价1", item.sewageTotalFee)
        //   .replace("操作员1", item.createName)
        //   .replace("发票代码1", '')
        //   .replace("发票号码1", '')
        //   .replace("发票代码2", '')
        //   .replace("发票号码2", '')
        //   .replace("水资源费1", item.sourceTotalFee)
        //   .replace("水资源费2", item.sourceTotalFee)
        //   .replace("上次结存1", lastSave)
        //   .replace("本次结存1", thisSave)
        //   .replace("上次结存2", lastSave)
        //   .replace("本次结存2", thisSave)
        //   .replace("现示数1", '')
        //   .replace("现示数2", '')
        console.log("远传表!!");

        let temp = templateCodeYC
        .replace("日期", moment(item.createTime).format('YYYY-MM-DD'))
        .replace("用户编号", item.cno)
        .replace("用户名", item.cname)
        .replace("应收金额1", item.reallyFee)
        .replace("单价q", item.feeString)
        .replace("单价w", wsf)
        .replace("操作员", item.createName)
        .replace("日期1", moment(item.createTime).format('YYYY-MM-DD'))
        .replace("用户编号1", item.cno)
        .replace("用户名1", item.cname)
        .replace("应收金额2", item.reallyFee)
        .replace("单价q2", item.feeString)
        .replace("单价w2", wsf)
        .replace("操作员1", item.createName)
        .replace("发票代码1", '')
        .replace("发票号码1", '')
        .replace("发票代码2", '')
        .replace("发票号码2", '')
        .replace("水资源费1", item.sourceTotalFee)
        .replace("水资源费2", item.sourceTotalFee)
        .replace("上次结存1", lastSave)
        .replace("本次结存1", thisSave)
        .replace("上次结存2", lastSave)
        .replace("本次结存2", thisSave)
        .replace("现示数1", '')
        .replace("现示数2", '')
        .replace("用水性质1", record.feeName)
        .replace("用水性质2", record.feeName)
        .replace("单价1", record.descr)
        .replace("单价2", record.descr)
        .replace("金额1", record.reallyFee)
        .replace("金额2", record.reallyFee)

        eval(temp);
        flag = LODOP.PRINT()
        if (flag == 1) {
          axios({
            method: 'post',
            url: `${url}revenue/invoiceRecord/save`,
            data: qs.stringify(values),
          }).then(response => {
            if (response.data.code == '0') {
              //打印完刷新页面
              if (index == selectedRecord.length - 1) {
                this.queryRef()
              }
            }
          }).catch(error => {
            Feedback.toast.error("系统异常请稍后再试" + error);
          })
        }
      }
    })
    this.setState({visible: false})
  }

  test(str) {
    return str.split('+')
    .filter(i => i.trim() !== '')
    .map(i => {
      const item = i.split(':')
      return {name: item[0], value: item[1]}
    })
    .reduce((total, item) => {
      if (item.name === '污水费') {
        total.v1 = item.value
      }
      if (item.name === '基础水价') {
        total.v2 = item.value
      }
      if (item.name[0] === '第') {
        if (total.v2 === undefined) {
          total.v2 = ''
        }
        total.v2 += item.value
        total.v2 += ' '
      }
      return total
    }, {})

  }

  //批量打印后刷新剩余的为打印订单
  queryRef() {
    this.setState({dataLoading: true, selectedRowKeys: []})
    let values = this.field.getValues()
    values.page = 1
    values.pageSize = 10
    axios({
      method: 'post',
      url: url + 'revenue/order/query',
      data: qs.stringify(values)
    })
      .then((response) => {
        this.setState({
          formValue: response.data.datas,
          page: response.data.page,
          pageSize: response.data.pageSize,
          totalSize: response.data.totalSize,
          dataLoading: false,
          printStatus: false,
          selectedRecord: []
        })
        Feedback.toast.success("打印成功！")
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
        this.setState({ dataLoading: false, })
      })
  }

  //导出
  downloadFile = () => {
    let values = this.field.getValues();
    let url1 = `${url}revenue/orderReport/all?n=1`;
    if (values.cno) {
      url1 += '&cno=' + values.cno;
    }
    if (values.areaId) {
      url1 += '&areaId=' + values.areaId;
    }
    if (values.regionId) {
      url1 += '&regionId=' + values.regionId;
    }
    if (values.statusli) {
      url1 += '&statusli=' + values.statusli;
    }
    if (values.csTime) {
      url1 += '&csTime=' + values.csTime;
    }
    if (values.ceTime) {
      url1 += '&ceTime=' + values.ceTime;
    }
    if (values.isNote) {
      url1 += '&isNote=' + values.isNote;
    }
    if (values.cardNo) {
      url1 += '&cardNo=' + values.cardNo;
    }
    if (values.watermeterType) {
      url1 += '&watermeterType=' + values.watermeterType;
    }
    if (values.watermeterKind) {
      url1 += '&watermeterKind=' + values.watermeterKind;
    }
    if (values.createIds) {
      url1 += '&createIds=' + values.createIds;
    }
    if (values.startAmount) {
      url1 += '&startAmount=' + values.startAmount;
    }
    if (values.endAmount) {
      url1 += '&endAmount=' + values.endAmount;
    }
    if (values.watermeterId) {
      url1 += '&watermeterId=' + values.watermeterId;
    }
    if (values.source) {
      url1 += '&source=' + values.source;
    }
    if (values.feeId) {
      url1 += '&feeId=' + values.feeId;
    }
    if (values.startTunnage) {
      url1 += '&startTunnage=' + Number(values.startTunnage);
    }
    if (values.endTunnage) {
      url1 += '&endTunnage=' + Number(values.endTunnage);
    }
    if (values.payWay) {
      url1 += '&payWay=' + values.payWay;
    }
    if (values.watermeterCompany) {
      url1 += '&watermeterCompany=' + values.watermeterCompany;
    }
    if (values.address) {
      url1 += '&address=' + values.address;
    }
    window.open(encodeURI(url1), 'about:blank');
  }

  //导出订单
  downOrder() {
    let values = this.field.getValues();
    let url1 = `${url}revenue/orderReport/OrderDetailCount?n=1`;
    if (values.cno) {
      url1 += '&cno=' + values.cno;
    }
    if (values.areaId) {
      url1 += '&areaId=' + values.areaId;
    }
    if (values.regionId) {
      url1 += '&regionId=' + values.regionId;
    }
    if (values.statusli) {
      url1 += '&statusli=' + values.statusli;
    }
    if (values.csTime) {
      url1 += '&csTime=' + values.csTime;
    }
    if (values.ceTime) {
      url1 += '&ceTime=' + values.ceTime;
    }
    if (values.isNote) {
      url1 += '&isNote=' + values.isNote;
    }
    if (values.cardNo) {
      url1 += '&cardNo=' + values.cardNo;
    }
    if (values.watermeterType) {
      url1 += '&watermeterType=' + values.watermeterType;
    }
    if (values.watermeterKind) {
      url1 += '&watermeterKind=' + values.watermeterKind;
    }
    if (values.createIds) {
      url1 += '&createIds=' + values.createIds;
    }
    if (values.startAmount) {
      url1 += '&startAmount=' + values.startAmount;
    }
    if (values.endAmount) {
      url1 += '&endAmount=' + values.endAmount;
    }
    if (values.watermeterId) {
      url1 += '&watermeterId=' + values.watermeterId;
    }
    if (values.source) {
      url1 += '&source=' + values.source;
    }
    if (values.feeId) {
      url1 += '&feeId=' + values.feeId;
    }
    if (values.startTunnage) {
      url1 += '&startTunnage=' + Number(values.startTunnage);
    }
    if (values.endTunnage) {
      url1 += '&endTunnage=' + Number(values.endTunnage);
    }
    if (values.payWay) {
      url1 += '&payWay=' + values.payWay;
    }
    if (values.watermeterCompany) {
      url1 += '&watermeterCompany=' + values.watermeterCompany;
    }
    if (values.address) {
      url1 += '&address=' + values.address;
    }
    window.open(encodeURI(url1), 'about:blank');
  }

  render() {
    const { selectedRowKeys, regionList, roleNameList, feeNameList, areaList, formValue, page, pageSize, totalSize, dataLoading, reallyTotalFee, sewageTotalFee, waterTotalFee, totalTunnage, sourceTotalFee } = this.state;
    const { init } = this.field
    const formItemLayout = {
      labelCol: {
        fixedSpan: 7
      }
    }
    const footer = (
      <div style={{ marginTop: 20 }} align="center">
        <Button type="primary" loading={this.state.save} onClick={() => this.print()}>
          确定打印
        </Button>
        <Button onClick={this.onClose} style={{ marginLeft: 20 }}>
          取消
        </Button>
      </div>
    )
    return (
      <div>
        <IceContainer title="订单管理">
          <Form field={this.field}>
            <div style={{ display: 'flex', justifyContent: 'space-around' }}>

              <div style={{ display: 'flex', flexDirection: 'column' }}>
                <FormItem label="用户编号：">
                  <Input {...init('cno')} placeholder="--请输入--" style={styles.searchInputWidth} />
                </FormItem>

                <FormItem label="选择片区：">
                  <Select placeholder="--请输入--" {...init('areaId')}
                    style={{ width: 180 }} dataSource={areaList} onChange={(value) => this.onChange(value, 0)} />
                </FormItem>

                <FormItem label="选择小区：">
                  <Combobox
                    {...init('regionId')}
                    placeholder="--请先选择片区--"
                    fillProps="label"
                    hasClear
                    style={{ width: 180 }} dataSource={regionList} />
                </FormItem>

                <FormItem label="订单状态：">
                  <Select {...init('statusli')} style={styles.searchInputWidth} multiple
                    dataSource={[
                      { label: '请选择', value: '' },
                      { label: '未完成', value: 0 },
                      { label: '已支付待刷卡', value: 1 },
                      { label: '退款', value: 2 },
                      { label: '已完成', value: 3 },
                    ]}
                    defaultValue={[1, 3]}
                  />
                </FormItem>

                <FormItem label="提交时间：">
                  <RangePicker
                    onChange={(val, str) => this.timeOnchange(val, str)}
                    style={{ width: 211 }}
                    defaultValue={[moment(new Date()).format('YYYY-MM-DD'), moment(new Date()).format('YYYY-MM-DD')]} />
                </FormItem>

                <FormItem label="开票状态：">
                  <Select placeholder="请选择" style={styles.searchInputWidth} {...init('isNote')}
                    dataSource={[
                      { label: '已开发票', value: '1' },
                      { label: '未开发票', value: '2' },
                    ]}
                  />
                </FormItem>

                <FormItem label="电子发票状态：">
                  <Select placeholder="请选择" style={styles.searchInputWidth} {...init('isTax')}
                          dataSource={[
                            { label: '已开电子票', value: '0' },
                            { label: '未开电子票', value: '1' },
                          ]}
                  />
                </FormItem>
              </div>

              <div style={{ display: 'flex', flexDirection: 'column' }}>
                <FormItem label="用户卡号：">
                  <Input {...init('cardNo')} placeholder="--请输入--" style={styles.searchInputWidth} />
                </FormItem>
                <FormItem label="水表类型：">
                  <Select placeholder="请选择" style={styles.searchInputWidth} {...init('watermeterType')}
                    dataSource={[
                      { label: '请选择', value: '' },
                      { label: 'IC卡表', value: 'IC卡表' },
                      { label: '机械表', value: '机械表' },
                      { label: '远传表', value: '远传表' },
                    ]}
                  />
                </FormItem>
                <FormItem label="水表种类：">
                  <Select placeholder="请选择" style={styles.searchInputWidth} {...init('watermeterKind')}
                    dataSource={[
                      { label: '请选择', value: '' },
                      { label: '预付费2', value: '预付费2' },
                      { label: '预付费5', value: '预付费5' },
                      { label: '预付费4442', value: '预付费4442' },
                      { label: '机械表', value: '机械表' },
                      { label: '无线远传', value: '无线远传' },
                      { label: '有线远传', value: '有线远传' }
                    ]}
                  />
                </FormItem>
                <FormItem label="&emsp;操作员：">
                  <Combobox
                    {...init('createIds')}
                    placeholder="--请选择--"
                    multiple
                    fillProps="label"
                    hasClear
                    style={styles.searchInputWidth} dataSource={roleNameList} />
                </FormItem>
                <FormItem label="金额区间：">
                  <Input {...init('startAmount')} htmlType="number" style={{ width: 85 }} />
                  <span>—</span>
                  <Input {...init('endAmount')} htmlType="number" style={{ width: 85 }} />
                </FormItem>
                <FormItem label="水表厂家：">
                  <Select {...init('watermeterCompany')} placeholder="请输入" style={{ width: '170px' }}
                    dataSource={[
                      { label: '请选择', value: '' },
                      { label: '扬州恒信', value: '扬州恒信' },
                      { label: '深圳华旭', value: '深圳华旭' },
                      { label: '机械表厂家', value: '机械表厂家' }
                    ]} />
                </FormItem>
              </div>

              <div style={{ display: 'flex', flexDirection: 'column' }}>
                <FormItem label="水表编号：">
                  <Input {...init('watermeterId')} placeholder="--请输入--" style={styles.searchInputWidth} />
                </FormItem>
                <FormItem label="订单来源：">
                  <Select {...init('source')} style={styles.searchInputWidth}
                    dataSource={[
                      { label: '请选择', value: '' },
                      { label: '柜台', value: '柜台' },
                      { label: '微信', value: '微信' },
                      { label: '平账', value: '平账' },
                      { label: '蒙速办', value: '蒙速办' },
                    ]}
                  >
                  </Select>
                </FormItem>
                <FormItem label="用水性质：">
                  <Select {...init('feeId')} placeholder="请选择"
                    dataSource={feeNameList} style={{ width: 180 }} />
                </FormItem>
                <FormItem label="水量区间：">
                  <Input {...init('startTunnage')} htmlType="number" style={{ width: 85 }} />
                  <span>—</span>
                  <Input {...init('endTunnage')} htmlType="number" style={{ width: 85 }} />
                </FormItem>
                <FormItem label="支付方式：">
                  <Select {...init('payWay')} style={styles.searchInputWidth}
                    dataSource={[
                      { label: '请选择', value: '' },
                      { label: '现金', value: 1 },
                      { label: '刷卡', value: 0 },
                      { label: '微信', value: 2 },
                      { label: '蒙速办-微信', value: 7 },
                    ]} />
                </FormItem>
                <FormItem label="用户地址：">
                  <Input {...init('address')} style={{ width: 250 }} placeholder="请输入" />
                </FormItem>
              </div>

            </div>
          </Form>

          <div style={{ textAlign: 'center' }}>
            <Button type="primary" className="button" onClick={() => this.queryOrder(1, 10)}
              style={{ marginRight: 10 }}>
              <Icon type="search" />查询
            </Button>
            <Button type="secondary" className="button" onClick={() => this.reset()} style={{ marginRight: 10 }}>
              <Icon type="refresh" />重置
            </Button>
            <Button type="primary" className="button" onClick={() => this.doRead()}>
              <Icon type="text" />读卡
            </Button>
          </div>

        </IceContainer>

        <IceContainer title="订单列表">

          <div style={{ marginBottom: 10 }}>

            <Button type="primary" className="button" onClick={this.downloadFile}>
              <Icon type="download" />
              导出订单报表
            </Button>

            <Button type="primary" className="button" onClick={() => this.downOrder()} style={{ marginLeft: 10 }}>
              <Icon type="download" />
              导出订单
            </Button>

            <Button type="primary" className="button" style={{ marginLeft: 10 }} onClick={() => this.printAll()}
              loading={this.state.printStatus}>
              <Icon type="print" />
              批量打印
            </Button>

          </div>

          <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: 10 }}>
            <div>实收总金额：{reallyTotalFee ? reallyTotalFee : 0}元</div>
            <div style={{ marginLeft: 10 }}>总清水费：{waterTotalFee ? waterTotalFee : 0}元</div>
            <div style={{ marginLeft: 10 }}>总污水费：{sewageTotalFee ? sewageTotalFee : 0}元</div>
            <div style={{ marginLeft: 10 }}>总水资源税：{sourceTotalFee ? sourceTotalFee : 0}元</div>
            <div style={{ marginLeft: 10 }}>总吨数：{totalTunnage ? totalTunnage : 0} 吨</div>
          </div>

          <Table dataSource={formValue} isLoading={dataLoading}
            rowSelection={{ ...this.rowSelection, selectedRowKeys: selectedRowKeys, }} primaryKey="id">
            <Table.Column title="用户编号" dataIndex="cno" align="center" />
            <Table.Column title="用户名称" dataIndex="cname" align="center" />
            <Table.Column title="用户卡号" dataIndex="cardNo" align="center" />
            <Table.Column title="水表编号" dataIndex="watermeterId" align="center" />
            <Table.Column title="水表种类" dataIndex="watermeterKind" align="center" />
            <Table.Column title="水表厂家" dataIndex="watermeterCompany" align="center" />
            <Table.Column title="订单来源" dataIndex="source" align="center" />
            <Table.Column title="支付方式" dataIndex="payWay" align="center" cell={this.renderPayWay} />
            <Table.Column title="用水性质" dataIndex="feeName" align="center" />
            <Table.Column title="订单水量" dataIndex="tunnage" align="center" cell={(value) => value ? value : 0} />
            <Table.Column title="实收金额" dataIndex="reallyFee" align="center" />
            <Table.Column title="订单状态" dataIndex="status" align="center" cell={this.renderStatus} />
            <Table.Column title="用户地址" dataIndex="address" align="center" cell={(value) => this.omit(value)} />
            <Table.Column title="提交时间" dataIndex="createTime" align="center"
              cell={(value) => this.formatData(value)} />
            <Table.Column title="开票状态" dataIndex="isNote" align="center" cell={this.renderNote} />
            <Table.Column title="电子发票" dataIndex="isTax" align="center" cell={this.renderTax} />
            <Table.Column title="操作员" dataIndex="createName" align="center" />
            <Table.Column title="操作" cell={(value, index, record) => this.renderOper(record)}
              align="center" width={120} />
          </Table>

          <div style={{ display: 'flex', justifyContent: 'flex-end' }}>

            <Pagination
              pageSizeSelector="dropdown"
              onPageSizeChange={this.changePageSize}
              pageSizeList={[10, 30, 50, 100]}
              style={{ marginTop: 15 }}
              current={page}
              pageSize={pageSize}
              total={totalSize}
              size="small"
              onChange={(current) => this.changePage(current)}
            />

            <div style={{ lineHeight: '53px', marginLeft: 10 }}>共{totalSize}条记录</div>

          </div>

        </IceContainer>

        {/* <Dialog
          minMargin={10}
          style={{width: 700}}
          visible={this.state.visible}
          onClose={this.onClose}
          footer={footer}
          title='批量打印发票确认'
          footerAlign="center"
        >
          <p style={{fontSize: "16px", marginLeft: "50px", marginBottom: "25px"}}>请核对发票信息:</p>

          <Form field={this.field}>

            <Row>
              <FormItem {...formItemLayout} label="发票代码：">
                <Input {...init('invoiceCode')}
                       style={{width: "160px"}}
                />
              </FormItem>

              <FormItem {...formItemLayout} label="发票号码：">
                <Input {...init('invoiceNo', {rules: [{required: true, message: '发票号码必填'}]})} style={{width: "160px"}}/>
              </FormItem>

            </Row>


          </Form>
        </Dialog>*/
          <Dialog
            visible={this.state.modifyOrderStatusModal}
            onOk={this.modifyOrderStatus}
            onCancel={this.onModifyOrderStatusModalClose}
            onClose={this.onModifyOrderStatusModalClose}
            title="修改订单状态"
            footerAlign="center"
          >
            确定要修改订单状态为已支付待刷卡吗
          </Dialog>
        }

      </div>
    )
  }
}

const styles = {
  searchInputWidth: {
    width: 180,
  },
}
