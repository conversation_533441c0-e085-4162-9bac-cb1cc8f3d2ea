.head {
  height: 300px;
  width: 100%;
  .top {
    display: flex;
    justify-content: space-between;
    background-color: #e9e9e9;
    padding: 20px;
    height: 60px;
    .title {
      font-size: 16px;
      font-weight: bold;
    }
  }
  .content {
    width: 100%;
    padding: 20px 20px 0 20px;
    background-color: white;
  }
  .userSearch {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 10px;
  }
  .basicDetailTitle {
    margin: 10px 0;
    font-size: 16px;
  }

  .infoColumn {
    font-size: 14px;
    width: 100%;
    margin-bottom: 20px;
  }

  .infoItem {
    margin-bottom: 18px;
    list-style: none;
    font-size: 14px
  }

  .infoItemLabel {
    min-width: 70px;
    font-size: 16px;
  }

  .infoItemValue {
    color: #333;
  }

  .attachLabel {
    min-width: 70px;
    color: #999;
    float: left;
  }

  .attachPics {
    width: 80px;
    height: 80px;
    border: 1px solid #eee;
    margin-right: 10px
  }


}
