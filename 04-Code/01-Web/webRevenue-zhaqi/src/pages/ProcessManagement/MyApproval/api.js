import axios from "axios";
import qs from "qs";
import { url } from "../../../components/URL";

export const getNotPageList = (params) => {
  return axios.post(
    `${url}api/process/review/my/toReview`,
    qs.stringify(params)
  );
};

export const getHasPageList = (params) => {
  return axios.post(
    `${url}api/process/review/my/hasReview`,
    qs.stringify(params)
  );
};

export const getApplyRecordDetails = (params) => {
  return axios.get(
    `${url}api/process/apply/getChangeApplyByApplyId?id=${params}`
  );
};

export const getApprovalRecordDetails = (params) => {
  return axios.get(`${url}api/process/review/list/${params}`);
};

export const setDetailsReview = (params) => {
  return axios.post(`${url}api/process/review/review`, params);
};
