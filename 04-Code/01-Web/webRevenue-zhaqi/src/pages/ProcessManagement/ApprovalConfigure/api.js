import axios from "axios";
import qs from "qs";
import { url } from "../../../components/URL";

export const getPageList = (params) => {
  return axios.post(`${url}api/sys/audit/listPage`, qs.stringify(params));
};

export const addRecord = (params) => {
  return axios.post(`${url}api/sys/audit/save`, params);
};

export const getRecordDetails = (params) => {
  return axios.get(`${url}api/sys/audit/get/${params}`);
};

export const deleteRecord = (params) => {
  return axios.post(`${url}api/sys/audit/delete/${params}`);
};

export const getApprovalRole = () => {
  return axios.get("http://192.168.31.99:8095/oa/roleManager/listAllRole");
};
