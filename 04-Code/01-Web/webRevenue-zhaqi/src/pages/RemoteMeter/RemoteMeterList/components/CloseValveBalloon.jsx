import React, { Component } from 'react';
import { Button, Balloon, Icon, Feedback } from '@icedesign/base';
import axios from 'axios';
import { url } from '../../../../components/URL/index';
import qs from 'qs';

export default class CloseValveBalloon extends Component {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      stuffId: sessionStorage.getItem('stuffId'),
      stuffName: sessionStorage.getItem('realName'),
    };
  }

  handleHide = (visible, record, code) => {
    const { stuffId, stuffName } = this.state;
    let values = {};
    values.createId = stuffId;
    values.createName = stuffName;
    values.valve = 2;
    let remoteArr = [];
    remoteArr.push(record);
    values.remoteWatermeters = remoteArr;
    if (code === 1) {
      axios({
        method: 'POST',
        url: `${url}revenue/remoteWatermeter/switchValve`,
        data: values,
      })
      .then((response) => {
        var jsondata = response.data;
        if(jsondata.code == 0){
          alert("关阀指令发送成功");
          this.props.refreshTable();
        }
        else{
          alert(jsondata.msg);
        }
      })
      .catch((error) => {
        Feedback.toast.error("ajax请求出错: "+error);
      });
    }
    this.setState({
      visible: false,
    });
  };

  handleVisible = (visible) => {
    this.setState({ visible });
  };

  render() {
    const { record } = this.props;
    const closeTrigger = (
      <a title="关闭">
        <Icon type="stop" size="small" style={{ cursor: 'pointer',color: "#FF3333"}}/>
      </a>
    );
    const content = (
      <div>
        <div style={styles.contentText}>确认关闭阀门吗？</div>
        <Button
          id="confirmBtn"
          size="small"
          type="normal"
          shape="warning"
          style={{ marginRight: '5px' }}
          onClick={(visible) => this.handleHide(visible, record, 1)}
        >
          确认
        </Button>
        <Button
          id="cancelBtn"
          size="small"
          onClick={(visible) => this.handleHide(visible, record, 0)}
        >
          关闭
        </Button>
      </div>
    );

    return (
      <Balloon
        trigger={closeTrigger}
        triggerType="click"
        visible={this.state.visible}
        onVisibleChange={this.handleVisible}
      >
        {content}
      </Balloon>
    );
  }
}

const styles = {
  contentText: {
    padding: '5px 0 15px',
  },
};
