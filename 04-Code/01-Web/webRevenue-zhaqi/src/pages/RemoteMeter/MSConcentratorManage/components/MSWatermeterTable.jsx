import React, { Component } from 'react'
import { Grid, Input, Button, Icon, Table, Pagination, Feedback, Form, Field } from '@icedesign/base'
import IceContainer from '@icedesign/container'
import axios from 'axios'
import qs from 'qs'
import { url } from '../../../../components/URL/index'
import FoundationSymbol from 'foundation-symbol';
import {Link} from 'react-router-dom';

const FormItem = Form.Item
const { Row } = Grid;

export default class MSWatermeterTable extends Component {

    constructor(props) {
        super(props);
        this.state = {
            formValue: [],
            page: 1,
            pageSize: 10,
            total: 0,
            dataLoading: false,
            dataSource: [],
            searchValue:{}
        }
        this.field = new Field(this, { autoUnmount: true })
    }

    componentWillMount() {
        const param = this.props.location.state
        if (param == undefined) {
            this.props.history.push('/remoteManage/MSconcentrator');
            return;
        }
        const record = param.record;
        this.field.setValue('cid', record.cid);
        this.setState({
            searchValue: param.searchValue,
        })
        this.refreshTable(1, 10);
    }

    refreshTable = (page, pageSize) => {
        this.setState({
            dataLoading: true,
        });
        let values = this.field.getValues();
        values.page = page;
        values.pageSize = pageSize;
        axios({
            method: 'post',
            url: `${url}revenue/minsheng/concentrator/watermeterList`,
            data: qs.stringify(values),
        })
            .then((response) => {
                this.setState({
                    dataLoading: false,
                });
                let jsondata = response.data;
                this.setState({
                    dataSource: jsondata.datas,
                    page: page,
                    pageSize: pageSize,
                    total: jsondata.totalSize,
                });
            })
            .catch((error) => {
                this.setState({
                    dataLoading: false,
                });
                Feedback.toast.error('axios请求异常:' + error);
            });
    };

    //查询
    doSearch = () => {
        const { pageSize } = this.state;
        this.refreshTable(1, pageSize);
    }

    //重置
    reset = () => {
        this.field.reset('watermeterId');
        this.field.reset('contactState');
    }

    //翻页
    changePage = (page) => {
        const { pageSize } = this.state;
        this.refreshTable(page, pageSize);
    }

    //改变pageSize
    onPageSizeChange = (pageSize) => {
        this.refreshTable(1, pageSize);
    }

    rowIndex = (value, index) => {
        const { pageSize, page } = this.state;
        if (page == 1) {
            return index + 1;
        }
        else {
            return (index + 1) + (page - 1) * pageSize;
        }
    }

  /*渲染阀门状态*/
  renderState(value) {
    if (value == '1') {
      return <span style={{color: '#1DC11D'}}>开阀</span>;
    }
    else if (value == '2') {
      return <span style={{color: '#ff0000'}}>关闭</span>;
    }
    else {
      return <span style={{color: '#ff0000'}}>半悬</span>;
    }
  }

    render() {
        const { page, pageSize, total, dataSource, dataLoading, searchValue } = this.state
        const { init } = this.field
        const formItemLayout = {
            labelCol: { fixedSpan: 6 }
        }
        return (
            <div>
                <div style={{ marginBottom: 5 }}>
                    <Link to={{
                        pathname: `/remoteManage/MSconcentrator`,
                        state:{searchValue: searchValue}
                    }}>
                        <Button className="button" type="primary">
                            <FoundationSymbol type="backward" style={{ marginRight: 5 }} size="small" />
                            返回
                        </Button>
                    </Link>
                </div>
                <IceContainer title="搜索">
                    <Form field={this.field}>
                        <Row justify="space-around">
                            <Input {...init('cid')} htmlType="hidden" />
                            <FormItem label="水表编号：" {...formItemLayout}>
                                <Input {...init('watermeterId')} placeholder="请输入" style={{ width: '160px' }} />
                            </FormItem>
                            <FormItem label="通讯状态：" {...formItemLayout}>
                                <Input {...init('contactState')}
                                    style={{ width: '160px' }}
                                />
                            </FormItem>
                        </Row>
                    </Form>
                    <div align="center">
                        <Button type="primary" className="button" onClick={() => this.doSearch()} style={{ marginRight: 30 }}>
                            <Icon type="search" />查询
                            </Button>
                        <Button type="secondary" className="button" onClick={() => this.reset()} >
                            <Icon type="refresh" />重置
                            </Button>
                    </div>
                </IceContainer>
                <IceContainer title="民生水表列表">
                    <Table dataSource={dataSource} isLoading={dataLoading}>
                        <Table.Column title="序号" cell={this.rowIndex} align="center" width={80} />
                        <Table.Column title="水表编号" dataIndex="watermeterId" align="center" />
                        <Table.Column title="水表地址" dataIndex="fixedAddress" align="center" />
                        <Table.Column title="通道号" dataIndex="channel" align="center" />
                        <Table.Column title="字轮读数" dataIndex="thisNum" align="center" />
                        <Table.Column title="通讯状态" dataIndex="contactState" align="center" />
                        <Table.Column title="通讯时间" dataIndex="contactTime" align='center' />
                        <Table.Column title="阀门状态" dataIndex="valve" align='center' cell={(value) => this.renderState(value)}/>
                    </Table>
                    <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
                        <Pagination
                            style={{ textAlign: 'right', marginTop: 15 }}
                            pageSizeSelector="dropdown"
                            onChange={this.changePage}
                            onPageSizeChange={this.onPageSizeChange}
                            total={total}
                            pageSize={pageSize}
                            current={page}
                            size="small"
                            pageSizeList={[10, 30, 50, 100]}
                        />
                        <div style={{ lineHeight: '58px', marginLeft: 10 }}>共 {total} 条记录</div>
                    </div>
                </IceContainer>
            </div>
        )
    }
}

