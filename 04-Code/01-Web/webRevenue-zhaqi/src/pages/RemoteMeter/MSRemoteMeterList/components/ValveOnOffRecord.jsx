import React, { Component } from 'react';
import { Dialog, Button, Form, Input, Field, TreeSelect, Grid, Radio, DatePicker, moment, Icon, Feedback } from '@icedesign/base';
import axios from 'axios';
import qs from 'qs';

const { Row, Col } = Grid;
const { Group: RadioGroup } = Radio;

export default class ValveOnOffRecord extends Component {
  static displayName = 'ValveOnOffRecord';

  static defaultProps = {};

  constructor(props) {
    super(props);
    this.state = {
        save:false,
        submit:false,
        visible: false,
        dataIndex: null,
        dataSource: [],
        stuffId: sessionStorage.getItem("stuffId"),
        url: localStorage.getItem("url"),
        save:false,
        submit:false,
    };
    this.field = new Field(this,{
      autoUnmount: true
    });
  }

  onClose = () => {
      this.setState({
        visible: false,
      });
  };

  onOpen = (record) => {
    this.field.setValues({ ...record });
    this.setState({
        visible: true,
    });
  }

  render() {
    const init = this.field.init;
    const { record } = this.props;
    const formItemLayout = {
      labelCol : {
        fixedSpan: 6,
      },
      wrapperCol :{
        span: 14,
      }
    };
    const footer = (
      <div style={{ marginTop: 20}} align="center">
        <Button type="primary" onClick={this.onClose} >
           关闭
        </Button>
      </div>
    );

    return (
    <div style={styles.buttonStyle} >
        <a onClick={ () => this.onOpen(record)} title="查看" >
          <Icon type="browse" style={{ color: "#3399ff",cursor:"pointer"}} />
        </a>
        <Dialog
           style={{ width: 700 }}
           visible={this.state.visible}
           onClose={this.onClose}
           footer={footer}
           footerAlign="center"
           title="查看阀门开关记录"
        >
        <Form direction="ver" field={this.field} >
        <Row>
          <Form.Item label="水表编号：" {...formItemLayout}>
            <Input { ...init('userName', { initValue: sessionStorage.getItem("realName") }) } readOnly="true" />
          </Form.Item>
          <Form.Item label="集中器号：" {...formItemLayout}>
            <Input { ...init('deptName', { initValue: sessionStorage.getItem("deptName") }) } readOnly="true"/>
          </Form.Item>
        </Row>
        </Form>
      </Dialog>
    </div>
    );
  }
}
const styles = {
    buttonStyle:{ 
        display: 'inline-block', 
        marginRight: '2px',
    }
};