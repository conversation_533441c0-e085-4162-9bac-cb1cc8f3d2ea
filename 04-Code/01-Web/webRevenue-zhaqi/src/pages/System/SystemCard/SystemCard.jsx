import React, {Component} from 'react';
import {Input, Button, Dialog, Field, Form, Feedback, Select} from '@icedesign/base';
import IceContainer from '@icedesign/container';
import errorCard from "../../../common/errorCard"
import {systemCode42} from "../../../components/areaCode/areaCode";

const FormItem = Form.Item

export default class FeeSearch extends Component {

  constructor(props) {
    super(props);
    this.state = {
      cardType: [],
      userCard: false,
      checkCard: false,
      statusCard: false,
      hxCard: false,
      hxUserCard: false,
      type: [],
      watermeterCompany: [],
      createId: sessionStorage.getItem("stuffId"),
      createName: sessionStorage.getItem("realName")
    };
    this.field = new Field(this, {autoUnmount: true});
  }

  componentDidMount() {
  }

  //渲染管理卡类型
  changeWaterType = (value, option) => {
    this.field.setValue('watermeterKind', value);
    let temp = [];
    if (value == '3') {
      temp = [
        {label: '检查卡', value: 7},
        {label: '清除卡', value: 5},
        {label: '开关卡', value: 6},
        {label: '校验卡', value: 8},
      ];
    }
    else if (value == '1') {
      temp = [
        {label: '检查卡', value: 3},
        {label: '测零卡', value: 0},
        {label: '开关卡', value: 1},
        {label: '清除卡', value: 2},
        {label: '功能卡', value: 4},
      ];
    } else {
      temp = [
        {label: '检查卡', value: '检查卡'},
      ];
    }
    this.setState({cardType: temp});
  }

  //管理卡
  cardMange() {
    this.field.validate((errors, values) => {
      if (errors) {
        return
      } else {
        let cardType = Number(this.field.getValue('cardType'))
        let watermeterKind = this.field.getValue('watermeterKind')
        try {
          if (watermeterKind ==='3') {
            let i = hxdll.gl_card(cardType,'');
            if (i == 0) {
              alert('制作成功')
            } else {
              alert('制作失败' + this.resultError(i))
            }
          } else if (watermeterKind == '1') {
            let i = -1
            if (cardType == 3) {
              i = hxdll.copycardmade()
            } else {
              i = hxdll.gl_card(cardType);
            }
            if (i == 0) {
              alert('制作成功')
            } else {
              alert('制作失败' + this.resultError(i))
            }
          } else {
            let result = (SZHXMETERCARD_Web.HXCD_4442_WriteQueryCard_Web(systemCode42 +'|')).split('|')
            if (result[0] < 0) {
              alert("制作失败"+result[1]);
            } else if (result[0] > 0) {
              alert("制作成功");
            }
          }
        }
        catch (e) {
          alert('浏览器不支持ActiveX控件，请使用IE'+e);
        }
      }
    })
  }

  resultError(i) {
    switch (i) {
      case 0:
        return '成功';
      case 1:
        return '设备失败';
      case 2:
        return '设备失败';
      case 3:
        return '读卡失败';
      case 4:
        return '坏卡';
    }
  }

  //修正卡
  correction(number) {
    if (number == 0) {
      try {
        var i = hxdll.err0();
        if (i == 0) {
          alert('错误0修正成功')
        } else {
          alert('错误0修正失败:' + errorCard(i))
        }
      }
      catch (e) {
        alert('浏览器不支持ActiveX控件，请使用IE');
      }
    } else {
      try {
        var i = hxdll.err00();
        if (i == 0) {
          alert('错误00修正成功')
        } else {
          alert('错误00修正失败:' + errorCard(i))
        }
      }
      catch (e) {
        alert('浏览器不支持ActiveX控件，请使用IE');
      }
    }

  }

  //读取用户卡
  readUserCard() {
    try {
      let readstr = hxdll.user_card()
      let type = readstr.substring(1, 2)
      this.setState({type: type})
      let cardNo = ''
      let water = ''
      let total = ''
      let time = ''
      let tempTypy = readstr.substring(12, 14)  //圆卡的刷表状态  01：未刷表  03:已刷表
      if (type == '3') {
        cardNo = readstr.substring(2, 12)
        //圆卡
        //判断刷表未刷表 03:未刷表  01:已刷表
        if (tempTypy == '03') {
          let waterThis = readstr.substring(14, 18)  //表本次
          let WaterAll = readstr.substring(18, 22)  //表累计
          let waterlast = readstr.substring(22, 26)   //表剩余
          this.field.setValue('waterThis', parseInt(waterThis, 16))
          this.field.setValue('WaterAll', parseInt(WaterAll, 16))
          this.field.setValue('waterlast', parseInt(waterlast, 16))
        } else if (tempTypy == '01') {
          let cardThis = readstr.substring(18, 22) //卡本次
          let cardTotal = readstr.substring(14, 18)  //卡累计
          this.field.setValue('cardThis', parseInt(cardThis, 16))
          this.field.setValue('cardTotal', parseInt(cardTotal, 16))
        }
      } else {
        //钥匙卡
        cardNo = readstr.substring(2, 10)
        time = parseInt(readstr.substring(18, 20), 16) == 0 ? '0' : readstr.substring(18, 20)
        water = readstr.substring(20, 24)
        if (parseInt(water, 16) != 0) {
          this.field.setValue('isUse', "未使用")
        } else {
          this.field.setValue('isUse', "已使用")
        }
        /*   axios({
             method: 'post',
             url: `${url}revenue/order/findLastOrderByCnoAndStatus`,
             data: qs.stringify({cardNo: cardNo}),
           }).then((response) => {
             if (response.data.code == "0") {
               let totalTimes = response.data.datas ? response.data.datas.totalTimes : 0
               let isUse = Number(totalTimes) == Number(time)
               if (isUse) {
                 this.field.setValue('isUse', "已使用")
               } else {
                 this.field.setValue('isUse', "未使用")
               }
             } else {
               this.field.setValue('isUse', "已使用")
             }
           }).catch((error) => {
             Feedback.toast.error("系统繁忙请稍后再试");
           })*/
      }

      if (readstr.length > 5) {
        this.setState({userCard: true})
        this.field.setValue('cardNo', cardNo)
        this.field.setValue('water', parseInt(water, 16))
        this.field.setValue('time', parseInt(time, 16))
        this.field.setValue('total', parseInt(total, 16))
      } else {
        if (readstr == '0') {
          Feedback.toast.error("设备失败");
        } else if (readstr == '1') {
          Feedback.toast.error("卡型错误");
        } else if (readstr == '2') {
          Feedback.toast.error("卡型错误");
        } else if (readstr == '3') {
          Feedback.toast.error("读卡失败");
        } else if (readstr == '4') {
          Feedback.toast.error("非用户卡,请确认");
        }
      }
    }
    catch (e) {
      alert('浏览器不支持ActiveX控件，请使用IE');
    }
  }

  //钥匙卡读取检查卡
  readCheckCard() {
    try {
      let readstr = hxdll.copy_card()
      let cardNo = readstr.substring(0, 8)
      let time = readstr.substring(16, 18)
      let total = readstr.substring(18, 22)
      let water = readstr.substring(22, 26)
      let last = readstr.substring(26, 30)
      if (readstr.length > 1) {
        this.setState({checkCard: true})
        this.field.setValue('cardNo', cardNo)
        this.field.setValue('time', parseInt(time, 16))
        this.field.setValue('total', parseInt(total, 16))
        this.field.setValue('water', parseInt(water, 16))
        this.field.setValue('last', parseInt(last, 16))
      } else {
        if (readstr == '1') {
          Feedback.toast.error("连接失败");
        } else if (readstr == '2') {
          Feedback.toast.error("读卡失败");
        } else if (readstr == '3') {
          Feedback.toast.error("非检查卡");
        }
      }
    }
    catch (e) {
      alert('浏览器不支持ActiveX控件，请使用IE');
    }
  }

  //圆扣读取检查卡
  readCheckCardS() {
    try {
      let readstr = hxdll.user_card()
      let type = readstr.substring(1, 2)   //卡类型
      this.setState({type: type})
      let cardNo = ''
      let water = ''
      let total = ''
      let time = ''
      let tempTypy = readstr.substring(12, 14)  //圆卡的刷表状态  01：未刷表  03:已刷表
      if (type == '3') {
        cardNo = readstr.substring(2, 12)
        //圆卡
        //判断刷表未刷表
        if (tempTypy == '03') {
          let waterThis = readstr.substring(14, 18)  //表本次
          let WaterAll = readstr.substring(18, 22)  //表累计
          let waterlast = readstr.substring(22, 26)   //表剩余
          this.field.setValue('waterThis', parseInt(waterThis, 16))
          this.field.setValue('WaterAll', parseInt(WaterAll, 16))
          this.field.setValue('waterlast', parseInt(waterlast, 16))
        } else if (tempTypy == '01') {
          let cardThis = readstr.substring(18, 22) //卡本次
          let cardTotal = readstr.substring(14, 18)  //卡累计
          this.field.setValue('cardThis', parseInt(cardThis, 16))
          this.field.setValue('cardTotal', parseInt(cardTotal, 16))
        }
      } else if (type == '4') {
        Feedback.toast.error('该检查卡还未使用请回去贴表');
      }

      if (readstr.length > 5 && type != '4') {
        this.setState({userCard: true})
        this.field.setValue('cardNo', cardNo)
        this.field.setValue('water', parseInt(water, 16))
        this.field.setValue('time', parseInt(time, 16))
        this.field.setValue('total', parseInt(total, 16))
      } else {
        if (readstr == '0') {
          Feedback.toast.error("设备失败");
        } else if (readstr == '1') {
          Feedback.toast.error("卡型错误");
        } else if (readstr == '2') {
          Feedback.toast.error("卡型错误");
        } else if (readstr == '3') {
          Feedback.toast.error("读卡失败");
        } else if (readstr == '4') {
          Feedback.toast.error("非用户卡,请确认");
        }
      }
    }
    catch (e) {
      alert('系统繁忙请稍后再试');
    }
  }

  //华旭卡读取检查卡
  hxCheckCard() {
    try {
      let readtypeHuaxu = (SZHXMETERCARD_Web.HXCD_4442_DisposeData_Web('1|1|', "")).split("|")
      if (readtypeHuaxu[1] == '0') {
        Feedback.toast.error('该检查卡还未使用请回去贴表');
      } else if (readtypeHuaxu[0] == '3') {
        this.field.setValue('watertotaltunnage', readtypeHuaxu[4])                 //表累计量
        this.field.setValue('date', readtypeHuaxu[7])                              //购水日期
        this.field.setValue('lastTunnage', readtypeHuaxu[8])                       //水表余量
        this.field.setValue('time', readtypeHuaxu[10])                             //购水次数
        this.field.setValue('totalTunnage', readtypeHuaxu[11])                     //总购量
        this.setState({hxCard: true})
      } else {
        Feedback.toast.error('该卡非检查卡');
      }
    }
    catch (e) {
      Feedback.toast.error("网络连接错误")
    }
  }

  //华旭卡读取用户卡
  hxUserCard() {
    try {
      let readtypeHuaxu = (SZHXMETERCARD_Web.HXCD_4442_DisposeData_Web('1|1|', "")).split("|")
      console.log(readtypeHuaxu)
      if (readtypeHuaxu[0] == '1') {
        this.field.setValue('cardNo', readtypeHuaxu[26].substring(6,readtypeHuaxu[26].length))                 //用户编号
        if(readtypeHuaxu[2]=='0'){                                   //使用状态
          this.field.setValue('status', '未刷表')
        }else {
          this.field.setValue('status', '已刷表')
        }
        this.field.setValue('tunnage', readtypeHuaxu[3])              //购水量
        this.field.setValue('time', readtypeHuaxu[4])                //购水次数
        this.setState({hxUserCard: true})
      } else {
        Feedback.toast.error('该卡非用户卡');
      }
    }
    catch (e) {
      Feedback.toast.error("网络连接错误")
    }
  }

  //关闭弹窗
  handleCancel() {
    this.setState({userCard: false, checkCard: false, statusCard: false, hxCard: false, hxUserCard: false})
  }

  //卡清零
  clearCard() {
    let cardNo = this.field.getValue('cardNo')
    try {
      let i = hxdll.sellwater()
      if (i == 10) {
        Feedback.toast.error("读卡失败:无卡")
      } else if (i == 100) {
        Feedback.toast.error("读卡失败:读卡失败")
      } else if (i == 101) {
        Feedback.toast.error("读卡失败:读卡失败")
      } else if (i == 102) {
        Feedback.toast.error("读卡失败:非用户卡")
      } else {
        //卡清零
        let result = hxdll.sellconfirm(cardNo, '475001', 0, 0, 0, 0, 0, 0, 0, 0, '1')
        if (result == 0) {
          this.setState({userCard: false})
          alert("卡清零成功")
        }
        else {
          alert("卡清零失败")
        }
      }
    } catch (e) {
      alert('浏览器不支持ActiveX控件，请使用IE');
    }
  }

  //读取卡状态
  readStatus() {
    try {
      let readstr = hxdll.chk_card()
      if (readstr == '6') {
        this.setState({statusCard: true})
        this.field.setValue('status', '测零卡')
      } else if (readstr == '8') {
        this.setState({statusCard: true})
        this.field.setValue('status', '清除卡')
      } else if (readstr == '9') {
        this.setState({statusCard: true})
        this.field.setValue('status', '检查卡')
      } else if (readstr == '10') {
        this.setState({statusCard: true})
        this.field.setValue('status', '功能卡')
      } else if (readstr == '11') {
        this.setState({statusCard: true})
        this.field.setValue('status', '用户卡')
      } else if (readstr > '100') {
        this.setState({statusCard: true})
        this.field.setValue('status', '开关卡')
      } else if (readstr == '20') {
        Feedback.toast.error("射频卡读卡失败");
      } else if (readstr == '21') {
        Feedback.toast.error("射频卡开关卡");
      } else if (readstr == '22') {
        this.setState({statusCard: true})
        this.field.setValue('status', '射频卡检查卡')
      } else if (readstr == '23') {
        this.setState({statusCard: true})
        this.field.setValue('status', '射频卡校验卡')
      } else if (readstr == '24') {
        this.setState({statusCard: true})
        this.field.setValue('status', '射频卡检查卡')
      } else if (readstr == '25') {
        this.setState({statusCard: true})
        this.field.setValue('status', '射频卡用户卡')
      } else if (readstr == '27') {
        this.setState({statusCard: true})
        this.field.setValue('status', '射频卡其他卡')
      } else if (readstr == '0') {
        Feedback.toast.error("设备失败");
      } else if (readstr == '1') {
        Feedback.toast.error("无卡");
      } else if (readstr == '2') {
        Feedback.toast.error("不存在的卡型，非恒信卡");
      } else if (readstr == '3') {
        Feedback.toast.error("读卡失败");
      } else if (readstr == '4') {
        Feedback.toast.error("坏卡");
      } else if (readstr == '5') {
        Feedback.toast.error("写卡失败");
      }
    }
    catch (e) {
      alert('浏览器不支持ActiveX控件，请使用IE');
    }
  }

  changeName(value) {
    this.field.reset('watermeterKind')
    this.field.reset('cardType')
    this.field.setValue('name', value);
    let watermeterCompany = [];
    if (value == '扬州恒信') {
      watermeterCompany = [
        {label: "预付费5", value: '3'},
        {label: "预付费2", value: '1'},
      ];
    }
    else {
      watermeterCompany = [
        {label: '预付费4442', value: '0'},
      ];
    }
    this.setState({
      watermeterCompany: watermeterCompany,
    });
  }

  render() {
    const {cardType, userCard, checkCard, type, statusCard, watermeterCompany, hxCard, hxUserCard} = this.state
    const {init} = this.field
    const formItemLayout = {
      labelCol: {
        fixedSpan: 8,
      }
    }
    const footer = (
      <Button size="medium" onClick={() => this.handleCancel()}>
        取消
      </Button>
    )
    return (
      <section>

        <IceContainer title="修正卡">
          <h6>提示：错误修正：当水表显示错误0或者00时,点击按钮修正</h6>
          <div style={{marginTop: 10}}>
            <span style={{fontWeight: 'bold', fontSize: 18}}>错误:0</span>
            <Button type="primary" style={{marginLeft: 45, width: 100}}
                    onClick={() => this.correction(0)}
            >修正</Button>
          </div>

          <div style={{marginTop: 10}}>
            <span style={{fontWeight: 'bold', fontSize: 18}}>错误:00</span>
            <Button type="primary" style={{marginLeft: 35, width: 100}} onClick={() => this.correction(1)}>修正</Button>
          </div>
        </IceContainer>

        <IceContainer title="管理卡">

          <Form field={this.field}>

            <FormItem label="厂家名称：" {...formItemLayout}>
              <Select  {...init('name')} style={{width: 180}}
                       dataSource={[
                         {label: "扬州恒信", value: '扬州恒信'},
                         {label: "深圳华旭", value: '深圳华旭'},
                       ]}
                       onChange={(value) => this.changeName(value)}
              />
            </FormItem>

            <FormItem label="水表种类：" {...formItemLayout}>
              <Select  {...init('watermeterKind', {rules: [{required: true, message: "请水表种类"}]})} style={{width: 180}}
                       dataSource={watermeterCompany}
                       onChange={this.changeWaterType}
              />
            </FormItem>

            <FormItem label="管理卡类型：" {...formItemLayout}>
              <Select placeholder="请先选择管理卡类型" {...init('cardType', {rules: [{required: true, message: "请填写管理卡类型"}]})}
                      style={{width: 180}}
                      dataSource={cardType}
              />
            </FormItem>


            <div style={{marginLeft: 100}}>
              <Button type="primary" style={{width: 100}} className="button" onClick={() => this.cardMange()}>
                制作
              </Button>
            </div>
          </Form>
        </IceContainer>

        <IceContainer title="读取卡">

          <Button type="primary" style={{width: 120, marginLeft: 20}} className="button"
                  onClick={() => this.readUserCard()}>
            读取用户卡
          </Button>

          <Button type="primary" style={{width: 150, marginLeft: 20}} className="button"
                  onClick={() => this.readCheckCard()}>
            钥匙卡读取检查卡
          </Button>

          <Button type="primary" style={{width: 150, marginLeft: 20}} className="button"
                  onClick={() => this.readCheckCardS()}>
            圆扣卡读取检查卡
          </Button>

          <Button type="primary" style={{width: 150, marginLeft: 20}} className="button"
                  onClick={() => this.hxUserCard()}>
            华旭卡读取用户卡
          </Button>

          <Button type="primary" style={{width: 150, marginLeft: 20}} className="button"
                  onClick={() => this.hxCheckCard()}>
            华旭卡读取检查卡
          </Button>

          <Button type="primary" style={{width: 120, marginLeft: 20}} className="button"
                  onClick={() => this.readStatus()}>
            读取卡状态
          </Button>

          <Dialog style={{width: 450}} visible={userCard} isFullScreen={true}
                  title='用户卡'
                  footer={footer}
                  onCancel={() => this.handleCancel()}
                  onClose={() => this.handleCancel()}>

            {
              type == '3' ?
                <Form field={this.field}>
                  <FormItem label="用户卡号：" {...formItemLayout}>
                    <Input {...init("cardNo")} readOnly/>
                  </FormItem>

                  {
                    this.field.getValue('cardThis') ?
                      <FormItem label="卡内本次水量(m³)：" {...formItemLayout}>
                        <Input {...init("cardThis")} readOnly/>
                      </FormItem> : void (0)
                  }

                  {
                    this.field.getValue('cardTotal') ?
                      <FormItem label="卡内累计水量(m³)：" {...formItemLayout}>
                        <Input {...init("cardTotal")} readOnly/>
                      </FormItem> : void (0)
                  }

                  {
                    this.field.getValue('waterThis') ?
                      <FormItem label="表本次水量(m³)：" {...formItemLayout}>
                        <Input {...init("waterThis")} readOnly/>
                      </FormItem> :
                      void (0)
                  }

                  {
                    this.field.getValue('WaterAll') ?
                      <FormItem label="表累计水量(m³)：" {...formItemLayout}>
                        <Input {...init("WaterAll")} readOnly/>
                      </FormItem> :
                      void (0)
                  }

                  {
                    this.field.getValue('WaterAll') ?
                      <FormItem label="表剩余水量(m³)：" {...formItemLayout}>
                        <Input {...init("waterlast")} readOnly/>
                      </FormItem> :
                      void (0)
                  }

                </Form> :
                type == '1' ?
                  <Form field={this.field}>
                    <FormItem label="用户卡号" {...formItemLayout}>
                      <Input {...init("cardNo")} readOnly/>
                    </FormItem>

                    <FormItem label="购水次数" {...formItemLayout}>
                      <Input {...init("time")} readOnly/>
                    </FormItem>

                    <FormItem label="本次水量(m³)" {...formItemLayout}>
                      <Input {...init("water")} readOnly/>
                    </FormItem>

                    <FormItem label="使用状态" {...formItemLayout}>
                      <Input {...init("isUse")} readOnly/>
                    </FormItem>
                    <div style={{textAlign: 'center'}}>
                      <Button type="primary" onClick={() => this.clearCard()}>
                        卡清零
                      </Button>
                    </div>
                  </Form> : void (0)
            }


          </Dialog>

          <Dialog style={{width: 400}} visible={checkCard}
                  title='检查卡'
                  footer={footer}
                  onCancel={() => this.handleCancel()}
                  onClose={() => this.handleCancel()}>

            <Form field={this.field}>
              <FormItem label="用户卡号" {...formItemLayout}>
                <Input {...init("cardNo")} readOnly/>
              </FormItem>

              <FormItem label="表内购水次数" {...formItemLayout}>
                <Input {...init("time")} readOnly/>
              </FormItem>

              <FormItem label="表内累计水量(m³)" {...formItemLayout}>
                <Input {...init("total")} readOnly/>
              </FormItem>

              <FormItem label="表内本次水量(m³)" {...formItemLayout}>
                <Input {...init("water")} readOnly/>
              </FormItem>

              <FormItem label="表内剩余水量(m³)" {...formItemLayout}>
                <Input {...init("last")} readOnly/>
              </FormItem>

            </Form>
          </Dialog>

          <Dialog style={{width: 400}} visible={statusCard}
                  title='卡状态'
                  footer={footer}
                  onCancel={() => this.handleCancel()}
                  onClose={() => this.handleCancel()}>

            <Form field={this.field}>
              <FormItem label="卡状态" {...formItemLayout}>
                <Input {...init("status")} readOnly/>
              </FormItem>
            </Form>
          </Dialog>

          <Dialog style={{width: 450}} visible={hxCard} isFullScreen={true}
                  title='华旭检查卡'
                  footer={footer}
                  onCancel={() => this.handleCancel()}
                  onClose={() => this.handleCancel()}>

            <Form field={this.field}>

              <FormItem label="表内累计量：" {...formItemLayout}>
                <Input {...init("watertotaltunnage")} readOnly/>
              </FormItem>

              <FormItem label="插卡日期：" {...formItemLayout}>
                <Input {...init("date")} readOnly/>
              </FormItem>

              <FormItem label="水表余量(m³)：" {...formItemLayout}>
                <Input {...init("lastTunnage")} readOnly/>
              </FormItem>

              <FormItem label="刷卡次数：" {...formItemLayout}>
                <Input {...init("time")} readOnly/>
              </FormItem>

              <FormItem label="累计总购水量：" {...formItemLayout}>
                <Input {...init("totalTunnage")} readOnly/>
              </FormItem>

            </Form>


          </Dialog>

          <Dialog style={{width: 450}} visible={hxUserCard} isFullScreen={true}
                  title='华旭用户卡'
                  footer={footer}
                  onCancel={() => this.handleCancel()}
                  onClose={() => this.handleCancel()}>

            <Form field={this.field}>

              <FormItem label="用户卡号：" {...formItemLayout}>
                <Input {...init("cardNo")} readOnly/>
              </FormItem>

              <FormItem label="使用状态：" {...formItemLayout}>
                <Input {...init("status")} readOnly/>
              </FormItem>

              <FormItem label="购水次数：" {...formItemLayout}>
                <Input {...init("time")} readOnly/>
              </FormItem>

              <FormItem label="本次购水量：" {...formItemLayout}>
                <Input {...init("tunnage")} readOnly/>
              </FormItem>

            </Form>


          </Dialog>

        </IceContainer>

      </section>
    )
  }
}
