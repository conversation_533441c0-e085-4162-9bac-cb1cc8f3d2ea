/* eslint-disable react/no-unused-state */
import React, {Component} from 'react';
import {Input, Button, Icon, Balloon, Table, Dialog, Field, Form, Feedback} from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import qs from 'qs';
import {url} from '../../../components/URL/index'

const FormItem = Form.Item
const Tooltip = Balloon.Tooltip

export default class FeeSearch extends Component {
  static displayName = 'FeeSearch';

  static propTypes = {};

  static defaultProps = {};

  constructor(props) {
    super(props);
    this.state = {
      formValue: [],
      visible: false,
      dataLoading: false,
      selectRecord: '',
      createId: sessionStorage.getItem("stuffId"),
      createName: sessionStorage.getItem("realName"),
    }
    this.field = new Field(this, {autoUnmount: true});
  }

  componentDidMount() {
    this.queryAll();
  }

  /*查询*/
  queryAll() {
    this.setState({dataLoading: true});
    axios({
      method: 'post',
      url: url + 'revenue/water/query',
    })
      .then((response) => {
        this.setState({
          formValue: response.data.datas,
          dataLoading: false,
        })
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
        this.setState({dataLoading: false,})
      })
  }

  /*操作表格*/
  renderOper(record) {
    const edit = (<Icon type="survey" size="xs" style={{color: '#1DC11D',cursor: 'pointer'}}
                        onClick={() => this.openDialog(record)}/>)
    return (
      <div style={styles.oper}>
        <Tooltip trigger={edit} align='t' text='修改'/>
      </div>
    )
  }

  /*修改打开弹窗*/
  openDialog(record) {
    this.field.setValue('id', record.id)
    this.field.setValue('name', record.name)
    this.setState({visible: true, selectRecord: record})
  }

  /*提交*/
  handleOk() {
    const {selectRecord, createId, createName} = this.state
    this.field.validate((errors, values) => {
      if (errors) {
        return
      } else {
        values.createId = createId
        values.createName = createName
        if (selectRecord) {
          values.id = selectRecord.id
          //修改
          axios({
            method: 'post',
            url: `${url}revenue/water/modify`,
            data: qs.stringify(values)
          }).then((response) => {
            if (response.data.code == "0") {
              Feedback.toast.success("修改成功")
              this.setState({visible: false, selectRecord: ''})
              this.queryAll()
            } else {
              Feedback.toast.error("修改失败")
            }
          }).catch((error) => {
            Feedback.toast.error("请求错误：" + error)
          })
        } else {
          values.createId = createId
          values.createName = createName
          //添加
          axios({
            method: 'post',
            url: `${url}revenue/water/add`,
            data: qs.stringify(values)
          }).then((response) => {
            if (response.data.code === "0") {
              Feedback.toast.success("添加成功");
              this.setState({visible: false, selectRecord: ''})
              this.queryAll()
            } else {
              Feedback.toast.error("添加失败");
            }
          }).catch((error) => {
            Feedback.toast.error("请求错误：" + error);
          })
        }
      }
    })
  }

  /*关闭弹窗*/
  handleCancel() {
    this.setState({
      visible: false,
      selectRecord: ''
    })
  }

  render() {
    const {formValue, visible, dataLoading, selectRecord} = this.state
    const formItemLayout = {
      labelCol: {fixedSpan: 7},
      wrapperCol: {span: 12}
    }
    const {init} = this.field
    return (
      <IceContainer title="用水性质列表">

        <div style={styles.right}>
          <Button type="primary" style={styles.button} onClick={() => this.setState({visible: true, feeStair: []})}>
            <Icon type="add" size="xs" style={{marginRight: '4px'}}/>添加资费
          </Button>
        </div>

        <Table dataSource={formValue} isLoading={dataLoading}>
          <Table.Column title="ID" dataIndex="id" align="center"/>
          <Table.Column title="用水性质" dataIndex="name" align="center"/>
          <Table.Column title="操作人" dataIndex="createName" align="center"/>
          <Table.Column title="操作" cell={(value, index, record) => this.renderOper(record)}
                        align="center" />
        </Table>

        <Dialog style={{width: 400}} visible={visible} isFullScreen={true}
                title={selectRecord ? '修改用水性质' : '添加用水性质'}
                onCancel={() => this.handleCancel()}
                onOk={() => this.handleOk()}
                onClose={() => this.handleCancel()}>

          <Form field={this.field}>
            {
              selectRecord ?
                <FormItem {...formItemLayout} label="ID：">
                  <Input {...init("id")} disabled={true}/>
                </FormItem> : void(0)
            }


            <FormItem {...formItemLayout} label="用水性质：">
              <Input {...init("name", {rules: [{required: true, message: "用水性质必填"}]})}/>
            </FormItem>
          </Form>

        </Dialog>

      </IceContainer>
    )
  }
}

const styles = {
  formRow: {
    marginBottom: '18px',
  },
  formCol: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: '20px',
  },
  label: {
    lineHeight: '28px',
    paddingRight: '10px',
  },
  button: {
    borderRadius: '4px',
  },
  center: {
    textAlign: 'right',
  },
  right: {
    textAlign: 'right',
    marginBottom: '20px'
  },
  addNewItem: {
    background: '#F5F5F5',
    height: 32,
    lineHeight: '32px',
    marginTop: 20,
    cursor: 'pointer',
    textAlign: 'center',
  },
  oper: {
    display: 'flex',
    justifyContent: 'space-evenly'
  }
};
