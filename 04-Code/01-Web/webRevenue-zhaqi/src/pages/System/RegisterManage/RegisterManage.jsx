/* eslint-disable react/no-unused-state */
import React, {Component} from 'react';
import {
  Input,
  Button,
  Icon,
  Table,
  Pagination,
  Dialog,
  Field,
  Form,
  Feedback,
  Balloon,
  Select,
  Checkbox
} from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import qs from 'qs';
import {url} from '../../../components/URL/index'
import DeleteBalloon from './components/DeleteBalloon'
import {LoginURL} from '../../../components/URL/LoginURL';

const FormItem = Form.Item
const Tooltip = Balloon.Tooltip
const {Option} = Select
const {Combobox} = Select;
const {Group: CheckboxGroup} = Checkbox

export default class RegisterManage extends Component {

  constructor(props) {
    super(props)
    this.state = {
      formValue: [],
      isLoading: false,
      current: 1,
      pageSize: 10,
      total: 1,
      totalSize: 0,
      visible: false,
      selectRecord: '',
      areaCopy: null,
      roleName: [],
      type1: '',
      createId: sessionStorage.getItem("stuffId"),
      createName: sessionStorage.getItem("realName"),
      buttons: [],
      loginURL: LoginURL,
    }
    this.field = new Field(this, {autoUnmount: true})
    this.checkBoxOnChange = this.checkBoxOnChange.bind(this);

  }

  componentDidMount() {
    this.queryRegister();
    this.getArea();
    this.queryRoleName();
    this.field.setValue('watermeterType', this.state.type1);
    this.getMenuByRole();
  }

  //获取按钮权限
  getMenuByRole = () => {
    const {loginURL} = this.state;
    axios.get(loginURL + '/getAuthority', {
      withCredentials: true,
    })
      .then((response) => {
        let jsondata = response.data;
        if (jsondata.statusCode == 0) {
          this.setState({
            buttons: jsondata.buttons,
          });
        }
        else {
          alert('系统忙，请稍后重试');
        }
      })
      .catch((error) => {
        console.log('axios请求异常:' + error);
      });
  }

  /*查询*/
  queryRegister(type) {
    const {current, pageSize} = this.state;
    this.setState({dataLoading: true});
    if (type == 1) {
      let values = this.field.getValues();
      values.page = current
      values.pageSize = pageSize
      values.areaId = this.field.getValue('areaId1')
      values.id = this.field.getValue('regionId1')
      values.cid = this.field.getValue('cid1')
      axios({
        method: 'post',
        url: `${url}revenue/region/paging`,
        data: qs.stringify(values)
      })
        .then((response) => {
          this.setState({
            formValue: response.data.datas,
            current: response.data.page,
            pageSize: response.data.pageSize,
            totalSize: response.data.totalSize,
          })
          this.setState({dataLoading: false,})
        })
        .catch((error) => {
          Feedback.toast.error("请求错误：" + error);
          this.setState({dataLoading: false,})
        })
    } else {
      axios({
        method: 'post',
        url: `${url}revenue/region/paging`,
        data: qs.stringify()
      })
        .then((response) => {
          this.setState({
            formValue: response.data.datas,
            current: response.data.page,
            pageSize: response.data.pageSize,
            totalSize: response.data.totalSize,
          })
          this.setState({dataLoading: false,})
        })
        .catch((error) => {
          Feedback.toast.error("请求错误：" + error);
          this.setState({dataLoading: false,})
        })
    }

  }

  /*切换分页*/
  changePage(pageIndex) {
    const {pageSize} = this.state
    this.setState({dataLoading: true});
    let values = this.field.getValues();
    values.page = pageIndex
    values.pageSize = pageSize
    values.areaId = this.field.getValue('areaId1')
    values.id = this.field.getValue('regionId1')
    values.cid = this.field.getValue('cid1')
    axios({
      method: 'post',
      url: `${url}revenue/region/paging`,
      data: qs.stringify(values)
    })
      .then((response) => {
        this.setState({
          formValue: response.data.datas,
          current: response.data.page,
          pageSize: response.data.pageSize,
          totalSize: response.data.totalSize,
          dataLoading: false
        })
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
      })

  }

  changePageSize = (pageSize) => {
    this.setState({dataLoading: true, current: 1, pageSize: pageSize});
    let values = []
    values.pageSize = pageSize
    axios({
      method: 'post',
      url: `${url}revenue/region/paging`,
      data: qs.stringify(values),
    })
      .then((response) => {
        this.setState({
          formValue: response.data.datas,
          current: response.data.page,
          pageSize: response.data.pageSize,
          totalSize: response.data.totalSize,
          dataLoading: false
        })
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
        this.setState({dataLoading: false,})
      });
  };

  /*表格操作栏*/
  renderOper(record) {
    const edit = (<Icon type="survey" size="ashbin" style={{color: "#1DC11D", cursor: 'pointer', marginRight: 15}}
                        onClick={() => this.openDialog('update', record)}/>)
    const {buttons} = this.state;
    /* return buttons.length > 0 ? buttons.map(item => {
       if (item.menuPath == '/System/CommunityManage' && item.buttonCode == 'edit') {
         return <Tooltip trigger={edit} align='t' text='修改'/>;
       }
       else if (item.menuPath == '/System/CommunityManage' && item.buttonCode == 'delete') {
         return <DeleteBalloon id={record.id} queryRegister={this.queryRegister.bind(this)}/>
       }
     }) : void(0)*/
    return <Tooltip trigger={edit} align='t' text='修改'/>
  }

  /*编辑打开弹窗*/
  openDialog(type, record) {
    if (type === 'add') {
      this.setState({visible: true, type: type})
    }
    else {
      let list = []
      if (record.watermeterType) {
        list = record.watermeterType.split(',')
      }
      this.field.setValues({...record})
      if (list) {
        this.field.setValue('watermeterType', list)
      }
      this.setState({visible: true, type: type, selectRecord: record})
    }

  }

  /*关闭弹窗*/
  handleCancel() {
    this.setState({visible: false})
  }

  /*弹窗确定回掉函数*/
  handleOk() {
    const {type, selectRecord, createId, createName, type1} = this.state
    this.field.validate((errors, values) => {
      if (errors) {
        return
      } else {
        values.id = selectRecord.id
        values.createId = createId
        values.createName = createName
        values.watermeterType = type1
        if (type === 'update') {
          axios({
            method: 'post',
            url: `${url}revenue/region/update`,
            data: qs.stringify(values),
          })
            .then((response) => {
              if (response.data.code == "0") {
                Feedback.toast.success("修改成功")
                this.setState({visible: false})
                this.queryRegister(1)
              } else {
                Feedback.toast.error("修改失败")
              }
            })
            .catch((error) => {
              Feedback.toast.error("请求错误：" + error);
            })
        } else {
          axios({
            method: 'post',
            url: `${url}revenue/region/save`,
            data: qs.stringify(values)
          })
            .then((response) => {
              if (response.data.code == "0") {
                Feedback.toast.success("添加成功");
                this.queryRegister()
                this.setState({visible: false})
              } else {
                Feedback.toast.error("添加失败");
              }
            })
            .catch((error) => {
              Feedback.toast.error("请求错误：" + error);
            })
        }

      }
    })
  }

  //区域查询
  getArea() {
    axios({
      method: "get",
      url: `${url}revenue/area/getAll`,
    }).then(response => {
      this.setState({
        areas: response.data.datas
      })
    }).catch(error => {
      Feedback.toast.error("AJAX请求错误", error)
    })
  }

  //区域onchange并查询区册
  areaChange(value) {
    this.field.setValue('areaId1', value);
    //区册查询
    let areaId = []
    areaId.areaId = value
    axios({
      method: 'post',
      url: `${url}revenue/region/paging`,
      data: qs.stringify(areaId)
    }).then(response => {
      this.setState({
        areaCopy: response.data.datas
      })
    }).catch(error => {
      Feedback.toast.error("请求异常", error);
    });
  }

  //区域下拉
  area() {
    const {areas} = this.state;
    return areas && areas.length > 0 ? areas.map((item) => {
      return <Option value={item.id}> {item.name} </Option>
    }) : void (0)
  }

  //区册下拉
  areaCopy() {
    const {areaCopy} = this.state
    if (areaCopy == null || areaCopy == undefined) {
      return <Option value='1' disabled> 请先选择区域 </Option>
    }
    else {
      return areaCopy && areaCopy.length > 0 ? areaCopy.map((item) => {
        return <Option value={item.id}>{item.regionName}</Option>
      }) : void (0)
    }
  }

  /*查询抄表员*/
  queryRoleName() {
    axios({
      method: 'post',
      url: `${url}revenue/staff/findByRoleName`,
      data: qs.stringify({roleName: '抄表员'})
    })
      .then((response) => {
        this.setState({
          roleName: response.data.datas,
        })
      })
      .catch((error) => {
        Feedback.toast.error("请求错误：" + error);
      })
  }

  /*渲染抄表员*/
  renderRoleName() {
    const {roleName} = this.state
    return roleName && roleName.length > 0 ? roleName.map((item) => {
      return <Option value={item.userId}>{item.realName}</Option>
    }) : void(0)
  }

  //抄表员onChange
  roleNameOnchange(value, option, type) {
    if (type == 'search') {
      this.field.setValue('cid1', value)
    } else if (type == 'role') {
      this.field.setValue('cid', value)
      this.field.setValue('cname', option.label)
    } else {
      this.field.setValue('repairmanId', option.label);
      this.field.setValue('repairmanName', option.label);
    }
  }

  //重置
  reset() {
    this.field.reset();
    this.setState({
      areaCopy: null
    })
  }

  //Dialog的区域onchange
  onChangeDialog = (value, option) => {
    this.field.setValue('areaId', value);
    //区册查询
    let areaId = [];
    areaId.areaId = value;

    axios({
      method: 'post',
      url: `${url}revenue/region/paging`,
      data: qs.stringify(areaId)
    }).then(response => {
      this.setState({
        areaCopy: response.data.datas
      })
    }).catch(error => {
      Feedback.toast.error("请求异常", error);
    });
    this.field.setValue("regionName1", '');
  }

  /*复选框选择*/
  checkBoxOnChange(selectedItems) {
    this.field.setValue('watermeterType', selectedItems)
    let menuId = ''
    for (let i = 0; i < selectedItems.length; i++) {
      menuId += selectedItems[i];
      if (i != selectedItems.length - 1 && menuId != '') {
        menuId += ',';
      }
    }
    this.setState({type1: menuId})
  }

  /*表格水表类型操作*/
  renderWatermetertype(value) {
    if (value) {
      let list = value.split(',')
      let str = ""
      list.map((item, index) => {
        if (item == '0') {
          str += 'IC卡表'
        } else if (item == '1') {
          str += '机械表'
        } else if (item == '2') {
          str += '远传表'
        }
        if (index != list.length - 1 && str != "") {
          str += ','
        }
      })
      return str
    }
  }

  /*表格抄表周期操作*/
  renderCopyPeriod(value) {
    if (value == '0') {
      value = '单月'
    } else if (value == '1') {
      value = '双月'
    } else {
      value = '每月'
    }
    return value
  }

  render() {
    const {init} = this.field
    const {formValue, dataLoading, current, pageSize, totalSize, visible, type} = this.state
    const formItemLayout = {
      labelCol: {fixedSpan: 6},
      wrapperCol: {fixedSpan: 12}
    }
    let list = [
      {
        value: "0",
        label: "IC卡表",
      },
      {
        value: "1",
        label: "机械表"
      },
      {
        value: "2",
        label: "远传表",
      }
    ]
    return (
      <div>

        <IceContainer title="搜索">

          <Form direction="hoz" field={this.field} style={{textAlign: 'center'}}>
            <FormItem label="请选择片区：">
              <Select {...init('areaId1')} style={{width: '150px'}} onChange={(value) => this.areaChange(value)}>
                {this.area()}
              </Select>
            </FormItem>
            <FormItem label="请选择小区：">
              <Combobox {...init('regionId1')} style={{width: '150px'}}
                        fillProps="label"
                        hasClear>
                {this.areaCopy()}
              </Combobox>
            </FormItem>
            <FormItem label="抄表人员：">
              <Select  {...init('cid1')} onChange={(value, option) => this.roleNameOnchange(value, option, 'search')}
                       style={{width: '150px'}}>
                {this.renderRoleName()}
              </Select>
            </FormItem>
            <Button type="primary" className="button" onClick={() => this.queryRegister()}>
              <Icon type="search"/>查询
            </Button>
            <Button type="secondary" className="button" onClick={() => this.reset()} style={{marginLeft: '10px'}}>
              <Icon type="refresh"/>重置
            </Button>
          </Form>

        </IceContainer>

        <IceContainer title="小区列表">
          {/*  {
            buttons.length > 0 ? buttons.map(item => {
                if (item.menuPath == '/System/CommunityManage' && item.buttonCode == 'add') {
                  return (
                    <div style={{textAlign: 'right', marginBottom: 10}}>
                      <Button type="primary" onClick={() => this.openDialog('add')}>
                        <Icon type="add" size="xs"/>新增
                      </Button>
                    </div>
                  )
                }
              })
              : void(0)
          }*/}
          <div style={{textAlign: 'right', marginBottom: 10}}>
            <Button type="primary" onClick={() => this.openDialog('add')}>
              <Icon type="add" size="xs"/>新增
            </Button>
          </div>
          <Table dataSource={formValue} isLoading={dataLoading}>
            <Table.Column title="片区名称" dataIndex="areaName" align="center"/>
            <Table.Column title="小区名称" dataIndex="regionName" align="center"/>
            <Table.Column title="抄表类型" dataIndex="watermeterType" align="center"
                          cell={(value) => this.renderWatermetertype(value)}/>
            <Table.Column title="抄表周期" dataIndex="copyPeriod" align="center"
                          cell={(value) => this.renderCopyPeriod(value)}/>
            <Table.Column title="抄表员" dataIndex="cname" align="center"/>
            <Table.Column title="抄表员电话" dataIndex="copyTel" align="center"/>
            <Table.Column title="维修员" dataIndex="repairmanName" align="center"/>
            <Table.Column title="维修员电话" dataIndex="repairTel" align="center"/>
            <Table.Column title="备注" dataIndex="remarks" align="center"/>
            <Table.Column title="操作" cell={(value, index, record) => this.renderOper(record)}
                          align='center'/>
          </Table>
          <div style={{display: 'flex', justifyContent: 'flex-end'}}>
            <Pagination
              pageSizeSelector="dropdown"
              onPageSizeChange={this.changePageSize}
              style={{marginTop: 15}}
              current={current}
              pageSize={pageSize}
              total={totalSize}
              onChange={(current) => this.changePage(current)}
            />
            <div style={{lineHeight: '53px', marginLeft: 10}}>共{totalSize}条记录</div>
          </div>

        </IceContainer>

        <Dialog style={{width: 500}} visible={visible} isFullScreen={true}
                onClose={() => this.handleCancel()}
                onCancel={() => this.handleCancel()}
                title={type === 'add' ? '添加小区' : '修改小区信息'}
                onOk={() => this.handleOk()}>

          <Form field={this.field}>
            <FormItem label="选择片区：" {...formItemLayout}>
              <Select {...init('areaId', {rules: [{required: true, message: '片区名称必填'}]})}
                      style={{width: '240px'}} onChange={this.onChangeDialog}>
                {this.area()}
              </Select>
            </FormItem>
            <FormItem {...formItemLayout} label="小区名称：">
              <Input  {...init('regionName', {rules: [{required: true, message: '小区名称必填'}]})}
                      placeholder="--请输入--"/>
            </FormItem>
            <FormItem {...formItemLayout} label="抄表周期：">
              <Select style={{width: '240px'}} {...init('copyPeriod', {
                rules: [{required: true, message: '抄表日期必填'}]
              })}>
                <Option value='0'>单月</Option>
                <Option value='1'>双月</Option>
                <Option value='2'>每月</Option>
              </Select>
            </FormItem>

            <FormItem {...formItemLayout} label="抄表类型：">
              <CheckboxGroup
                {...init('watermeterType', {rules: [{required: true, message: '请选抄表类型'}]})}
                dataSource={list}
                onChange={(value) => this.checkBoxOnChange(value)}
              />
            </FormItem>

            <FormItem label="抄表人员：" {...formItemLayout}>
              <Select style={{width: '240px'}} {...init('cid')}
                      onChange={(value, option) => this.roleNameOnchange(value, option, 'role')}>
                {this.renderRoleName()}
              </Select>
            </FormItem>

            <FormItem {...formItemLayout} label="抄表员电话：">
              <Input {...init('copyTel')} placeholder="--请输入--"/>
            </FormItem>


            <FormItem label="维修人员：" {...formItemLayout}>
              <Select style={{width: '240px'}} {...init('repairmanName')}
                      onChange={(value, option) => this.roleNameOnchange(value, option, 'repair')}>
                {this.renderRoleName()}
              </Select>
            </FormItem>

            <FormItem {...formItemLayout} label="维修员电话：">
              <Input {...init('repairTel')} placeholder="--请输入--"/>
            </FormItem>

            <FormItem label="备&emsp;&emsp;注：" {...formItemLayout} >
              <Input multiple {...init('remarks')} placeholder="--请输入--"/>
            </FormItem>

          </Form>

        </Dialog>

      </div>
    )
  }
}
