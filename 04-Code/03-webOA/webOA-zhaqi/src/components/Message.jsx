import React, { Component } from 'react';
import { <PERSON><PERSON>, Badge, Feedback } from '@icedesign/base';
import { Link } from 'react-router-dom';
import FoundationSymbol from 'foundation-symbol';
import axios from 'axios';
import { withRouter } from 'react-router-dom';
import { OAURL } from '../components/URL/OAURL';
var myTime;
export default class Message extends Component{

    constructor(props){
      super(props);
      this.state = {
        stuffId: sessionStorage.getItem("stuffId"),
        url: OAURL,
        count: 0,
        totalTime: 0,
      };
    }

    componentWillMount(){
      const { stuffId } = this.state;
      if(stuffId == null || stuffId == undefined){
        return;
      }
      //this.state.timer = setInterval(this.getMessageCount, 300000);
    }

    componentDidMount(){
      this.getMessageCount();
    }

    componentWillUnmount(){
      this.clearTime();
    }

    getMessageCount = () => {
      const { stuffId, url } = this.state;
      axios.get(url+'/hqmessage/count',{
        params:{
           stuffId: stuffId,
           n: Math.random(),
        }
      })
      .then(response =>{
        let jsondata = response.data;
        if(jsondata.statusCode == 0){
          this.setState({
              count: jsondata.obj,
          })
          this.refresh();
        }
        
      })
      .catch(error => {
        Feedback.toast.error(error);
      })
    }

    refresh = () => {
      myTime = setTimeout(() => {
        // console.log(new Date(), "接口调用时间")
          this.getMessageCount();
        }, 5000)
    }

    clearTime = () => {
      clearTimeout(myTime);
    }


    render(){
      const { stuffId, count } = this.state;
      return <div style={{ display: "flex", alignItems: "center", fontSize: 12, marginRight: 20}}>
        <Balloon trigger={<div className="ice-design-header-userpannel" style={{ display: "flex", alignItems: "center", fontSize: 12 }}>
          <div >
          <Badge count={count}>
            <FoundationSymbol type="message" size="small" style={{ color: "white"}}/>
          </Badge>
          <span style={{ marginLeft: 10 }}>
            <font color="white">消息 </font> 
          </span>
          </div>
              </div>} closable={false} className="user-profile-menu">
            <ul>
              <li className="user-profile-menu-item">
                  你有 {count} 消息
              </li>
              <li className="user-profile-menu-item"> 
                <Link to={"/message"}>
                  查看全部消息
                </Link>  
              </li>
            </ul>
          </Balloon>
        </div>;
    }
}