import { Tab } from "@icedesign/base";
import IceContainer from "@icedesign/container";

const TabPane = Tab.TabPane;

export default class CustomTabs extends React.Component {
  static defaultProps = {
    dataSource: {},
  };

  constructor(props) {
    super(props);
    this.state = {
      panes: [],
      activeKey: "",
    };
  }

  componentDidMount() {
    this.addTabpane(this.props.dataSource);
  }

  addTabpane = (dataSource) => {
    this.setState({
      activeKey: dataSource.key,
    });
    this.setState((prevState) => {
      const { panes } = prevState;
      panes.push(dataSource);
      return { panes };
    });
  };

  /* eslint-disable eqeqeq */
  remove = (targetKey) => {
    let activeKey = this.state.activeKey;
    let lastIndex;
    this.state.panes.forEach((item, i) => {
      if (item.key == targetKey) {
        lastIndex = i - 1;
      }
    });
    const panes = this.state.panes.filter((pane) => pane.key != targetKey);
    if (lastIndex >= 0 && activeKey == targetKey) {
      activeKey = panes[lastIndex].key;
    }
    this.setState({ panes, activeKey });
  };

  onClose = (targetKey) => {
    this.remove(targetKey);
  };

  onChange = (activeKey) => {
    this.setState({ activeKey });
  };

  render() {
    const state = this.state;
    const { dataSource, compClass } = this.props;
    return (
      <div>
        <IceContainer>
          <Tab
            type="wrapped"
            activeKey={state.activeKey}
            closeable
            onChange={this.onChange}
            onClose={this.onClose}
            className="custom-tab"
          >
            {state.panes.map((item) => (
              <TabPane tab={item.tab} key={item.key} closeable={item.closeable}>
                {compClass}
              </TabPane>
            ))}
          </Tab>
        </IceContainer>
      </div>
    );
  }
}
