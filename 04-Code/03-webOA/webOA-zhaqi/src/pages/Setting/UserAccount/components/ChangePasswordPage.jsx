/* eslint react/no-string-refs:0 */
import React, { Component } from 'react';
import IceContainer from '@icedesign/container';
import { Input, Grid, Button, Feedback } from '@icedesign/base';
import {
  Form<PERSON>inderWrapper as IceFormBinderWrapper,
  FormBinder as IceForm<PERSON>inder,
  FormError as IceFormError,
} from '@icedesign/form-binder';
import axios from 'axios';
import { LoginURL } from '../../../../components/URL/LoginURL';

const { Row, Col } = Grid;

export default class ChangePasswordPage extends Component {
  static displayName = 'ChangePasswordPage';

  static propTypes = {};

  static defaultProps = {};

  constructor(props) {
    super(props);
    this.state = {
      value: {
        passwd: '',
        newPasswd: '',
        rePasswd: '',
      },
      url: LoginURL,
      stuffId: sessionStorage.getItem("stuffId"),
      publicKey: '',
    };
  }

  componentWillMount(){
    const { url } = this.state;
    axios.get(url+'/publicKey'+'?n='+Math.random(),{
      withCredentials: true,
    })
    .then((response) => {
      let jsondata = response.data;
      if(jsondata.statusCode == 0){
        this.setState({
          publicKey: jsondata.publicKey,
        })
      }
      else{
        Feedback.toast.error(jsondata.msg);
      }
    })
    .catch(error => {
      Feedback.toast.error("系统繁忙，请稍后重试"+error);
    })
  }

  checkPasswd = (rule, values, callback) => {
    if(!values){
      callback('原始密码必填');
    } 
    else {
      callback();
    }
  }

  checkNewPasswd = (rule, values, callback) => {
    if(!values){
      callback('新密码必填');
    } 
    else {
      callback();
    }
  }

  checkPasswd2 = (rule, values, callback, stateValues) => {
    if (values && values !== stateValues.newPasswd) {
      callback('两次输入密码不一致');
    } else {
      callback();
    }
  };

  formChange = (value) => {
    this.setState({
      value,
    });
  };

  validateAllFormField = () => {
    const { url, stuffId, publicKey } = this.state;
    this.refs.form.validateAll((errors, values) => {
      if(errors) {
        return;
      }
      var encrypt = new JSEncrypt();
      encrypt.setPublicKey(publicKey);
      var oldPasswd = encrypt.encrypt(values.passwd);
      var newPasswd = encrypt.encrypt(values.newPasswd);
      axios({
        method: "post",
        url: url+"/resetuserpwd",
        data: {
          userId: stuffId,
          userPwd: oldPasswd,
          userNewPwd: newPasswd,
        },
        withCredentials: true,      
      })
      .then(response => {
        let jsondata =  response.data;
        if(jsondata.statusCode == 0){
          Feedback.toast.success('修改密码成功');
        }
        else{
          alert(jsondata.msg);
        }
      })
      .catch(errors => {
        Feedback.toast.error('系统繁忙，请稍后重试：'+errors);
      })
    });
  };

  render() {
    return (
      <div className="change-password-form">
        <IceContainer title="修改密码">
          <IceFormBinderWrapper
            value={this.state.value}
            onChange={this.formChange}
            ref="form"
          >
            <div>
              <Row style={styles.formItem}>
                <Col xxs="7" s="4" l="3" style={styles.formLabel}>
                  原始密码：
                </Col>
                <Col xxs="16" s="10" l="7">
                  <IceFormBinder
                    name="passwd"
                    required
                    validator={this.checkPasswd}
                  >
                    <Input
                      htmlType="password"
                      size="large"
                      placeholder="请输入原始密码"
                      autocomplete="off"
                    />
                  </IceFormBinder>
                  </Col>
                  <Col>
                  <IceFormError name="passwd" />
                </Col>
              </Row>
              <Row style={styles.formItem}>
                <Col xxs="7" s="4" l="3" style={styles.formLabel}>
                  新密码：
                </Col>
                <Col xxs="16" s="10" l="7">
                  <IceFormBinder
                    name="newPasswd"
                    required
                    validator={this.checkNewPasswd}
                  >
                    <Input
                      htmlType="password"
                      size="large"
                      placeholder="请输入新密码"
                      autocomplete="off"
                    />
                  </IceFormBinder>
                  </Col>
                  <Col>
                  <IceFormError name="newPasswd" />
                </Col>
              </Row>

              <Row style={styles.formItem}>
                <Col xxs="7" s="4" l="3" style={styles.formLabel}>
                  确认密码：
                </Col>
                <Col xxs="16" s="10" l="7">
                  <IceFormBinder
                    name="rePasswd"
                    required
                    validator={(rule, values, callback) =>
                      this.checkPasswd2(
                        rule,
                        values,
                        callback,
                        this.state.value
                      )
                    }
                  >
                    <Input
                      htmlType="password"
                      size="large"
                      placeholder="请再次输入新密码"
                      autocomplete="off"
                    />
                  </IceFormBinder>
                  </Col>
                  <Col>
                  <IceFormError name="rePasswd" />
                </Col>
              </Row>
            </div>
          </IceFormBinderWrapper>
          <Row wrap style={{ marginTop: 30, marginLeft: 120 }}>
            <Col>
              <Button
                type="primary"
                onClick={this.validateAllFormField}
              >
                提 交
              </Button>
            </Col>
          </Row>
        </IceContainer>
      </div>
    );
  }
}

const styles = {
  formItem: {
    marginBottom: 25,
  },
  formLabel: {
    height: '32px',
    lineHeight: '32px',
    textAlign: 'right',
  }
};