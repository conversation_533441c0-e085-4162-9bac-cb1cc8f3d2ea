import React, { Component } from 'react';
import { Feedback, Table, Field, Select, Form, Input, Button, Pagination, DatePicker, Grid, Icon } from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import UserAccountInfo from './UserAccountInfo';
import DeleteBalloon from './DeleteBalloon';
import ViewUserAccountInfo from './ViewUserAccountInfo';
import ResetPasswordButton from './ResetPasswordButton';
import { OAURL } from '../../../../components/URL/OAURL';
import {LoginURL} from '../../../../components/URL/LoginURL';

const { Row, Col } = Grid;
const Toast = Feedback.toast;

export default class UserAccountTable extends Component {
  static dispalyName = 'UserAccountTable';

  constructor(props) {
    super(props);
    this.state = {
      dataLoading: true,
      dataSource: [],
      deptTree:[],
      total: 0,
      pageSize: 10,
      page: 1,
      url: OAURL,
      buttons: [],
      loginURL: LoginURL,
    };
    this.field = new Field(this);
  }

  //初始化获取数据
  componentDidMount() {
    this.refreshTable();
    this.getMenuByRole();
  };

  //刷新table
  refreshTable = () => {
    const { url, page, pageSize } = this.state;
    let searchValue = this.field.getValues();
    axios.get( url + '/user/all'+"?n="+Math.random(),{
      params:{
        page: page,
        pageSize: pageSize,
        userName: searchValue.userName,
        realName: searchValue.realName,
        flag: searchValue.flag,
      }
    })
     .then((response) => {
      let jsondata = response.data;
      let list = jsondata.userlist;
      if(jsondata.statusCode == 0){
        this.setState({
          dataSource: list.userlist,
          total: list.total,
          page: page,
          pageSize: pageSize,
          dataLoading: false,
        });
      }
     })
     .catch((error) => {
        Feedback.toast.error('系统繁忙，请稍后重试:' +error);
     });
  }

  //获取部门
  getAlldeptList = () => {
    axios.get(url+'/depart/tree'+'?n='+Math.random())
    .then((response) => {
        var jsondate = response.data;
        if (jsondate.statusCode == 0) {
            this.setState({
              deptTree: jsondate.treeNode,
            });
        }
    })
    .catch((error) => {
      Feedback.toast.error("系统繁忙，请稍后重试：" + error);
    });
  }

  //获取按钮权限
  getMenuByRole = () => {
    const {loginURL} = this.state;
    axios.get(loginURL + '/getAuthority', {
      withCredentials: true,
    })
      .then((response) => {
        let jsondata = response.data;
        if (jsondata.statusCode == 0) {
          this.setState({
            buttons: jsondata.buttons,
          });
        }
        else {
          alert(jsondata.msg);
        }
      })
      .catch((error) => {
        Toast.error('系统繁忙，请稍后重试:' + error);
      });
  }

   //发送ajax方法
   doAxiosMethod = (page, pageSize) => {
    const { url } = this.state;
    let searchValue = this.field.getValues();
    axios.get(url+'/user/all'+'?n='+Math.random(), {
      params:  {
        page: page,
        pageSize: pageSize,
        userName: searchValue.userName,
        realName: searchValue.realName,
        flag: searchValue.flag,
     }
    })
    .then((response) => {
      let jsondata = response.data;
      let list = jsondata.userlist;
      if(jsondata.statusCode == 0){
        this.setState({
          dataLoading: false,
          dataSource: list.userlist,
          page: page,
          pageSize: pageSize,
          total: list.total,
        });
      }
      else{
        Feedback.toast.error('后台返回数据错误');
      }
      })
      .catch((error) => {
        Feedback.toast.error('系统繁忙，请稍后重试:' +error);
      });
   }


  //查询
  doSearch = () => {
    this.setState({
      dataLoading: true,
    });
    this.doAxiosMethod(1, 10);
  };

  //清空
  doClear = () => {
    this.field.reset();
  };

  //翻页
  changePage = (pageNo) => {
    const { pageSize } = this.state;
    this.setState({
      dataLoading: true,
    });
    this.doAxiosMethod(pageNo, pageSize);
};
  //改变显示记录数
  changePageSize = (pageSize) => {
    this.setState({
      dataLoading: true,
    });
    this.doAxiosMethod(1, pageSize);
  };

  //带分页的序号格式化
  indexFm = (value, index) => {
    const { pageSize, page } = this.state;
    if(page == 1){
      return index + 1;
    }
    else {
      return (index+1)+ (page-1)*pageSize;  
    }  
  }

  //操作按钮的格式化
  rowOptButton = (value, index, record) => {
    const {buttons} = this.state;
    let canEdit, canDelete, canReset = false;
    for (let i = 0; i < buttons.length; i++) {
      if (buttons[i].menuPath == '/setting/userAccount' && buttons[i].buttonCode == 'edit') {
        canEdit = true;
      }
       else if(buttons[i].menuPath == '/setting/userAccount' && buttons[i].buttonCode == 'delete'){
        canDelete = true;
      }
      else if(buttons[i].menuPath == '/setting/userAccount' && buttons[i].buttonCode == 'reset'){
        canReset = true;
      }
    }
    return (
      <span style={{display: "flex", justifyContent:"space-around" }}>
        <ViewUserAccountInfo
          record={record}
        />
        {
          canEdit ?
            <UserAccountInfo
              record={record}
              opt='edit'
              refreshTable={this.refreshTable}
            />
          :
          void(0)
        }
        {
          canDelete ? 
            <DeleteBalloon
              id={record.userId}
              refreshTable={this.refreshTable}
            />
          :
          void(0)
        }
        {
          canReset ?
            <ResetPasswordButton
              id={record.userId}
            />
          :
          void(0)
        }
      </span>
    );
  };

  //申请状态格式化
  stateFm = (value) => {
    if (value == 1) {
      return "有效";
    }
    else{
      return "无效";
    }
  }; 
 
  render() {
    const { init } = this.field;
    const formItemLayout = {
      labelCol: {
        fixedSpan: 4,
      }
    };
    return (
      <div>
        <IceContainer title="搜索">         
            <Form direction="hoz" field={this.field}>
              <Row justify="space-between">
              <Form.Item label="员工账号">
                <Input {...init('userName')} style={{width: 180}} autocomplete="off"/>
              </Form.Item>
              <Form.Item label="员工姓名" {...formItemLayout}>
                <Input {...init('realName')} style={{width: 180}} autocomplete="off"/>
              </Form.Item>
              <Form.Item label="状态" {...formItemLayout}>
                <Select {...init('flag')}
                  style={{ width: '150px' }}
                  dataSource={[
                    { label: '有效', value: 1 },
                    { label: '无效', value: 0 },
                  ]}
                  style={{width: 180}}
                />
              </Form.Item>
              {/* <Form.Item label="部门" {...formItemLayout}>
                <TreeSelect                 
                {...init('dname')}
                 dataSource={this.state.deptTree}
                 style={{width: 140}}
                />
              </Form.Item> */}
              </Row>
              <div align="center">             
                <Button type="primary" onClick={this.doSearch} size="medium" style={{ marginRight: '15px' }} className="button">  
                  <Icon type="search"/>
                  查询
                </Button>
                <Button type="secondary"  onClick={this.doClear} size="medium" style={{ marginRight: '10px' }} className="button">
                  <Icon type="refresh"/>
                  重置
                </Button>
              </div>
            </Form>
          </IceContainer>

          <IceContainer title="账号列表">
          <UserAccountInfo refreshTable={this.refreshTable} />
          <Table
            dataSource={this.state.dataSource}
            style={{ marginTop: 5 }}
            isLoading={this.state.dataLoading}
            primaryKey="id"
          >
            <Table.Column title="序号" cell={this.indexFm} width={55} align="center" />
            <Table.Column title="员工账号" dataIndex="userName"  align="center" />
            <Table.Column title="员工姓名" dataIndex="realName"  align="center" />
            <Table.Column title="状态" dataIndex="flag"  align="center" cell={this.stateFm}/>
            <Table.Column title="最后登录时间" dataIndex="lastLoginTime" align="center" />
            <Table.Column title="最后登录IP" dataIndex="lastLoginIp" align="center" />
            <Table.Column title="操作" cell={this.rowOptButton} align="center" />
          </Table>
          <div style={{display:'flex',justifyContent:'flex-end'}}>
            <Pagination
              pageSizeSelector="dropdown"
              onChange={this.changePage}
              onPageSizeChange={this.changePageSize}
              total={this.state.total}
              pageSize={this.state.pageSize}
              current={this.state.page}
              size="small"
              style={{ textAlign: 'right', marginTop: 15 }}
              pageSizeList={[10,30,50,100]}
            />        
            <div style={{lineHeight:'53px',marginLeft:10}}>共 {this.state.total} 条记录</div>
          </div>       
        </IceContainer>
      </div>
    );
  }

}

const styles = {
  operationStyle:{
    display: "flex",
    justifyContent:"space-around",
  }
}