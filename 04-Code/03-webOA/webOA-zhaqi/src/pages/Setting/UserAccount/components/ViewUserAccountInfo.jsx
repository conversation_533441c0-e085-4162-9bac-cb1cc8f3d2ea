import React, { Component } from 'react';
import {<PERSON><PERSON>back, Dialog, Button, Form, Input, Field, TreeSelect, Grid, Radio, DatePicker, Select , Icon } from '@icedesign/base';
import axios from 'axios';
import { OAURL } from '../../../../components/URL/OAURL';


const { Row, Col } = Grid;
const { Group: RadioGroup } = Radio;

export default class ViewUserAccountInfo extends Component {
  static displayName = "ViewUserAccountInfo";

  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      url: OAURL,
    };
    this.field = new Field(this);
  }

  //打开查看对话框
  onOpen = (record) => {
    const { url } = this.state;
    axios.get(url+'/user/one'+'?n='+Math.random(), {
      params: {
        userId: record.userId,
      }
    })
    .then((response) => {
      let jsondata = response.data;
      if(jsondata.statusCode == 0){
        
        this.field.setValues({...jsondata.user});
        this.setState({
          visible: true,
        });
      }
      else {
        Feedback.toast.error('后台返回数据错误');
      }
    })
    .catch((error) => {
      Feedback.toast.error('系统繁忙，请稍后重试:' + error);
    });
  };

  onOpen1 = (record) => {
    this.field.setValues({ ...record });
    this.setState({
        visible: true,
    });
  };

  //关闭对话框
  onClose = () => {
    this.setState({
      visible: false
    });
  };

  render() {
    const init = this.field.init;
    const { record } = this.props;
    const formItemLayout = {
      labelCol: {
        fixedSpan: 5
      },
      wrapperCol: {
        span: 14
      }
    };
    const footer = (
      <div style={{ marginTop: 20}} align="center">
        <Button type="primary" onClick={this.onClose} style={{ marginLeft: 20}} >
           关闭
        </Button>
      </div>
    );
    return (
      <div style={styles.buttonStyle}>
        <a onClick={() => this.onOpen(record)} title="查看">
          <Icon type="browse" style={{ color: "#3399ff",cursor:"pointer"}}/>
        </a>
        <Dialog
          style={{ width: 700}}
          visible={this.state.visible}
          onClose={this.onClose}
          footer={footer}
          title="查看员工账号"
        >
          <Form direction="ver" field={this.field}>
            <Row>
              <Input {...init("userId")} htmlType="hidden" />
            </Row>
            <Row>
              <Col>
              <Form.Item label="员工姓名：" {...formItemLayout}>
                <Input {...init("realName")} 
                  style={{ width: 150}}
                  readOnly
                />
              </Form.Item>  
              </Col>
              <Col>            
              <Form.Item label="员工工号：" {...formItemLayout}>
                <Input {...init("wno")} 
                  style={{ width: 150}}
                  readOnly
                />
              </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col>
              <Form.Item label="员工性别：" {...formItemLayout}>
                <Select {...init("sex")} 
                  style={{ width: 150}}
                  dataSource={[
                    { label: '女', value: '1' },
                    { label: '男', value: '0' },
                  ]}
                  readOnly
                />
              </Form.Item>  
              </Col>
              <Col>            
              <Form.Item label="手机号码：" {...formItemLayout}>
                <Input {...init("phone")} 
                  style={{ width: 150}}
                  readOnly
                />
              </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col>
              <Form.Item label="员工部门：" {...formItemLayout}>
                <Input htmlType="hidden" {...init("did")}/>
                <Input  {...init("dname")} 
                  style={{ width: "150px" }}
                  readOnly
                />
              </Form.Item>
              </Col>
              <Col>
              <Form.Item label="所属角色：" {...formItemLayout}>
              <Input htmlType="hidden" {...init('role')}/>
              <Input
                { ...init('roleManagerName')}  readOnly style={{ width: "150px" }} readOnly
              />
              </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col>
              <Form.Item label="入职时间：" {...formItemLayout}>
                <Input {...init("workTime")} 
                  style={{ width: 150}}
                  readOnly
                />
              </Form.Item>  
              </Col>
              <Col>            
              <Form.Item label="员工状态：" {...formItemLayout}>
                <Input {...init("status")} 
                  style={{ width: 150}}
                  readOnly
                />
              </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col>
              <Form.Item label="登录账号：" {...formItemLayout}>
                <Input {...init("userName")} style={{ width: "150px" }} readOnly/>
              </Form.Item>
              </Col>
              <Col>
              <Form.Item label="账号状态：" {...formItemLayout}>
                <RadioGroup {...init("flag")} style={{ width: "150px" }} 
                  dataSource={[
                    { label: "启用", value: "1"},
                    { label: "禁用", value: "0"}
                  ]}
                  disabled
                />
              </Form.Item>
              </Col>
            </Row>
          </Form>
        </Dialog>
      </div>
    );
  }
}
const styles = {
  buttonStyle: {
    display: "inline-block",
    marginRight: "2px"
  }
};
