import React, { Component } from 'react';
import {Icon,  Button, Balloon, Feedback } from '@icedesign/base';
import axios from 'axios';
import { OAURL } from '../../../../components/URL/OAURL';

export default class DeleteBalloon extends Component {

  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      url: OAURL,
    };
  }

  handleHide = (visible, id, code) => {
    const { url } = this.state;
    if (code === 1) {
      axios.get( url+'/user/delete'+'?n='+Math.random(), {
        params: {
          userId: id,
        }
      })
      .then((response) => {
        let jsondata = response.data;
        if(jsondata.statusCode == 0){
          Feedback.toast.success('删除成功');
          this.props.refreshTable();
        }
        else {
          Feedback.toast.error('后台返回数据错误');
        }
      })
      .catch((error) => {
        Feedback.toast.error('系统繁忙，请稍后重试:' + error);
      });
    }
    this.setState({
      visible: false,
    });
  };

  handleVisible = (visible) => {
    this.setState({ visible });
  };

  render() {
    const { id } = this.props;
    const visibleTrigger = (
      <a title="删除">
      <Icon size="small" type="ashbin" style={{ color: "#3399ff",cursor:"pointer"}}/>
    </a>
    );

    const content = (
      <div>
        <div style={styles.contentText}>确认删除？</div>
        <Button
          id="confirmBtn"
          size="small"
          type="normal"
          shape="warning"
          style={{ marginRight: '5px' }}
          onClick={visible => this.handleHide(visible, id, 1)}
        >
          确认
        </Button>
        <Button
          id="cancelBtn"
          size="small"
          onClick={visible => this.handleHide(visible, 0)}
        >
          取消
        </Button>
      </div>
    );

    return (
      <Balloon
        trigger={visibleTrigger}
        triggerType="click"
        visible={this.state.visible}
        onVisibleChange={this.handleVisible}
      >
        {content}
      </Balloon>
    );
  }
}

const styles = {
  contentText: {
    padding: '5px 0 15px',
  },
};
