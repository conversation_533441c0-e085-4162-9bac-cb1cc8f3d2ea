import React, { Component } from 'react';
import IceContainer from '@icedesign/container';
import { Button } from '@icedesign/base';
import AddMenu from './components/AddMenu';
import AddMenuButton from './components/AddMenuButton';

export default class Menu extends Component {
  static displayName = 'Menu';

  constructor(props){
    super(props);
    this.state = {

    };
  }

  render() {
      return (
          <div>
             <IceContainer>
                 <AddMenu/>
                 <AddMenuButton/>
             </IceContainer>
          </div>
      );
  }
}