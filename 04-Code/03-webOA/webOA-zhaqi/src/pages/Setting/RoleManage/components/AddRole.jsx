import React, { Component } from 'react';
import { Feedback, Tree, Field, Form, Grid, Button, Input,  Tag,DatePicker, Select, Checkbox } from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import { Link } from 'react-router-dom';
import qs from 'qs';
import { OAURL } from '../../../../components/URL/OAURL';

const { Group: CheckboxGroup } = Checkbox;
const { Row, Col } = Grid;
const FormItem = Form.Item;
const { Node: TreeNode } = Tree;

export default class AddRole extends Component {
    static displayName = "AddRole";
    static defaultProps = {};

    constructor(props) {
    super(props);
    this.state = { 
        flag: false,
        data:[],
        selectedKeys: [],
        url: OAURL,
        visible: "none",
        node:[],
        menuButtonList: [],
     };
    this.field = new Field(this, {
        autoUnmount: true
    });
    }

    componentWillMount() {
        var chekedid = this.props.location.state;
        const { url } = this.state;
        if ( chekedid!= null && chekedid.id == "") {
            axios.get(url + "/roleManager/init/treeMenu"+'?n='+Math.random())
                .then((response) => {
                    let jsondata = response.data;
                    if (jsondata.statusCode == 0) {
                        this.setState({
                            data: jsondata.obj.treeMenu,
                            selectedKeys: [],
                        })
                    }
                    else {
                        Feedback.toast.error(jsondata.msg);
                    }
                })
                .catch((error) => {
                    Feedback.toast.error('系统繁忙，请稍后重试:' + error);
                });
        }
        else if(chekedid!= null && chekedid.id != ""){
            //编辑
            axios.get(url + "/roleManager/init/treeMenu"+'?n='+Math.random(), {
                params: {
                    roleId: chekedid.id
                }
            })
                .then((response) => {
                    let jsondata = response.data;
                    if (jsondata.statusCode == 0) {                        
                        this.setState({
                            data: jsondata.obj.treeMenu,
                            selectedKeys: jsondata.obj.roleDetail.menuId.split(","),
                        })
                        this.field.setValues({...jsondata.obj.roleDetail});
                        // this.field.setValue('roleName', jsondata.obj.roleDetail.roleName);
                        // this.field.setValue('roleCode', jsondata.obj.roleDetail.roleCode);
                        // this.field.setValue('remarks', jsondata.obj.roleDetail.remarks);
                    }
                    else {
                        Feedback.toast.error(jsondata.msg);
                    }
                }).
                catch((error) => {
                    Feedback.toast.error('系统繁忙，请稍后重试:' + error);
                });
        }
        else if( chekedid == null){
            this.props.history.push('/setting/role'); 
        }
    };
    //时间格式化
    formateDate = (value, str) => {
        return str;
    };

    //要展开的节点
    loopNode = (data) =>{
        data.map(item => {
           this.state.node.push(item.id);        
           item.children && item.children.length ? this.loopNode(item.children) : null  
        });
        return(this.state.node); 
    }

    //关闭窗口
    onCloseDialog = () => {
        this.setState({ visible: false });
    };

    onOpen = () => {
        this.setState({ visible: true });
    };


 //选中或取消树节点的回调函数
 onCheck=(keys, info) => {
    this.setState({
        selectedKeys:keys,
   })
}
//保存按钮
addRoleSubmit = () => {
    var id = this.props.location.state.id;
    var menuId = '';
    const { selectedKeys, url } = this.state;
    //把数组转化成字符串
    for (var i = 0; i < selectedKeys.length; i++) {
        menuId += selectedKeys[i];
        if (i != selectedKeys.length - 1) {
            menuId += ',';
        }
    }
    this.field.validate((errors, values) => {
        if (errors) {
            Feedback.toast.error("Errors in form!!!");
            return;
        }
        values.menuId = menuId;
        values.id = id;
        
        //post方法向后台传值
        axios({
            method: 'post',
            url: url+'/roleManager/addOrUpdate',
            data: qs.stringify(values),
        })
        .then(response => {
            var jsondata = response.data;
            if (jsondata.statusCode == 0) {    
                window.scrollTo(1,1);                
                Feedback.toast.success(jsondata.msg);
                this.props.history.push('/setting/role');                                      
            }
            else {
                Feedback.toast.error(jsondata.msg);
            }
        })
        .catch(error => {
            Feedback.toast.error("ajax请求异常 " + error);
        });
    });
};

onSelect = (selectedKey, extra) => {
   const { data } = this.state;
   let menuButton = [];
   data.map(item => {
       item.children.map( childItem => {
            childItem.children.map( lastItem => {
                if( selectedKey[0] == lastItem.id && lastItem.button!= null){
                    menuButton = lastItem.button;
                }
            })
       })  
   });
   this.setState({
        menuButtonList: menuButton,
   })
}

renderMenuButton = () => {
    const { menuButtonList } = this.state;
    return menuButtonList && menuButtonList.length > 0 ? menuButtonList.map((item) => {
        return <Checkbox  value={item.Id}><span style={{ color: 'red'}}>{item.name}</span></Checkbox >
      }) : void(0)
}
   
    render() { 
        const init = this.field.init;
        const fromItemLayout = { 
            labelCol: { fixedSpan: 6 }, 
            wrapperCol: { span: 14 } 
        };
        const loop = data =>
            data.map(item => {
                return (
            <TreeNode label={<span style={{ color: "#008B00" }}>{item.name}</span>} key={item.id}>
                {item.children && item.children.length ? loop(item.children) : null}
            </TreeNode>
                    );
            });
        const list = [
            {label: '新增', value: '1'}
        ]
        return (
        <div style={styles.formContent}>
           <IceContainer title="基本信息">
             <Form direction="ver" field={this.field}>
                <Row>
                  <Col>
                  <FormItem label="角色名称：" {...fromItemLayout}>
                    <Input {...init("roleName", {
                        rules: [{ required: true, message: "必填"}]})} style={{ width: 200 }} placeholder="请输入角色名称" autoComplete="off" />
                  </FormItem>
                  </Col>
                  <Col>
                  <FormItem label="角色编码：" {...fromItemLayout}>
                    <Input {...init("roleCode")}  style={{ width: 200 }} placeholder="请输入角色编码" autoComplete="off" />
                  </FormItem>
                  </Col>
                </Row>
                {/* <Row>
                  <Col>
                  <FormItem label="角色类型：" {...fromItemLayout}>
                    <Select {...init("type", {
                        rules: [{ required: true, message: "必填"}]})} style={{ width: 200 }} placeholder="请选择角色类型" autoComplete="off" 
                        dataSource={[
                            { label: "通用角色",value: false },
                            { label: "特定角色",value: true },
                        ]}/>
                  </FormItem>
                  </Col>
                  <Col>
                  <FormItem label="职位权重：" {...fromItemLayout}>
                    <Select {...init("weight", {
                        rules: [{ required: true, message: "必填"}]})}  style={{ width: 200 }} placeholder="权重越大，职位越高" autoComplete="off" 
                        dataSource={[
                            { label: 1,value: 1 },
                            { label: 2,value: 2 },
                            { label: 3,value: 3 },
                            { label: 4,value: 4 },
                            { label: 5,value: 5 },
                        ]}
                        />
                  </FormItem>
                  </Col>
                </Row> */}
                <Row>
                    <FormItem label="角色描述：" {...fromItemLayout}>
                        <Input multiple style={{ width: 580 }} {...init("remarks")} placeholder="请添加备注" autoComplete="off" />
                    </FormItem>
                </Row>
                               
             </Form>
           </IceContainer>
           
           <IceContainer title="授权菜单">
           <div style={{ display: 'table' }}>
             <div>
                <Tree
                    checkable   
                    defaultExpandAll    
                    // defaultExpandedKeys={this.loopNode(this.state.data)}                        
                    checkedKeys={this.state.selectedKeys}                        
                    onCheck={this.onCheck}
                    // onSelect={this.onSelect}
                    showLine
                >  
                    {loop(this.state.data)}    
                </Tree>
             </div>
             {/* <div style={{ width: 300, display: 'table-cell', verticalAlign: "middle"}}>
                <div style={{ fontWeight: 600, marginBottom: 20, }}>菜单对应的按钮列表</div>
                <CheckboxGroup
                    style={{ color: "#008B00" }}
                >
                {this.renderMenuButton()}
                </CheckboxGroup>
             </div> */}
           </div>
            
            <div align="center">                
                <Button type="primary" onClick={this.addRoleSubmit}>确定</Button>
                <Link to={{
                          pathname: `/setting/role`,
                          }}> <Button type="primary">取消</Button> 
                </Link> 
            </div>
           </IceContainer>
        </div>
        );
}
}
const styles = {
    colstyle: {
        display: 'inline-block',
        marginRight: '5px',
    },
    formContent: {
        width: '80%',
        position: 'relative',
    },
};
