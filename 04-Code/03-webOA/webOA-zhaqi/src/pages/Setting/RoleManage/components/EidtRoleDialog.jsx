import React, { Component } from 'react';
import { Dialog, Field, Form, Grid, Button, Input, Tag, DatePicker, Select, Checkbox } from '@icedesign/base';
import Axios from 'axios';
import { OAURL } from '../../../../components/URL/OAURL';

const { Row, Col } = Grid;
const FormItem = Form.Item
export default class EidtRoleDialog extends Component {
    static displayName = 'EidtRoleDialog';
    static defaultProps = {};

    constructor(props) {
        super(props);
        this.state = {
            url:OAURL,
            visible: false,
            dataIndex: null,
        };
        this.field = new Field(this);
    }


    //关闭窗口
    onCloseDialog = () => {
        this.setState({
            visible: false
        });
    };

    onOpen = (index, record) => {
        this.field.setValues({ ...record });
        this.setState({
            visible: true,
            dataIndex: index,
        });
    };

    RoleFormSubmit = () => {
        this.field.validate((error, values) => {
            if (error) {
                return;
            }
            const { url } = this.state;
            // axios.post(url+'/roleManager/addOrUpdate',{

            // })
            this.props.getFormValues(dataIndex, values);
            this.setState({
                visible: false,
            });
        });
    };

    render() {
        const init = this.field.init;
        const { index, record } = this.props;
        const fromItemLayout = {
            labelCol: {
                fixedSpan: 5,
            },
            wrapperCol: {
                span: 14,
            },

        };
        const footer = (
            <div align="center">
                <a onClick={this.RoleFormSubmit} href="javascript:;">
                <Button size="medium" type="primary" >
                    确定
                </Button>
                </a>
                <a onClick={this.onCloseDialog} href="javascript:;">
                <Button size="medium">
                    取消
                </Button>
                </a>
            </div>
        );
        return <div style={styles.colstyle}>
            <Button size="small" type="primary" onClick={() => this.onOpen(index, record)}>
              编辑
            </Button>
            <Dialog style={{ width: 400 }} visible={this.state.visible} closable="esc,mask,close" footer={footer} onClose={this.onCloseDialog} title="查看角色信息">
              <Form direction="ver" field={this.field}>
                <Row>
                  <FormItem label="角色名称：" {...fromItemLayout}>
                    <Input {...init("roleName", {
                        initValue:sessionStorage.getItem('roleName'),
                        rules: [{ required: true, message: "必填" }]
                      })} style={{ width: 200 }} />
                  </FormItem>
                </Row>
                <Row>
                  <FormItem label="角色描述：" {...fromItemLayout}>
                    <Input multiple style={{ width: 200 }} {...init("remarks",
                   {initValue:sessionStorage.getItem('remarks')}
                )} />
                  </FormItem>
                </Row>
              </Form>
            </Dialog>
          </div>;
    }
}
const styles = {
    colstyle: {
        display: 'inline-block',
        marginRight: '5px',
    },
};
