 #dashboard-page {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    justify-content: space-between;
}

/* 通用 */
.iceContainer{
   width: 49%;
   box-sizing:'border-box'
}

.iceContainer .window{
    height: 240px;
    overflow: hidden;
 }

.iceContainer .title{
    position: relative;
    margin-bottom: 20px;
    border-left: 5px solid 	#3080fe;
    padding-left: 10px;
    line-height: 22px; 
    color: black;
    font-size: 15px;
}

.iceContainer .more{
    position: absolute;
    right: 0px;
    font-size: 13px;
    color: #1e73fd;
}

.iceContainer .itemContent{
    position: relative;
    color: #454973;
    font-size: 14px;
    display: flex;
    flex-direction: row;
    cursor: pointer;
    margin: 25px 0;
    text-decoration: none;
}

.iceContainer .nodeList ul li .hover{
    color: #1e73fd;
}

.iceContainer .itemCircle{
    border: 1px solid #3080fe;
    border-radius: 50%;
    width: 7px;
    height: 7px;
    margin-right: 10px;
    display: inline-block;
}

.iceContainer .itemTime{
    position: absolute;
    right: 0px;
}

/* 快捷入口 */
.iceContainer .shortcut_box{
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    height:200px;
}

.iceContainer .shortcut_box .shortcut{
    width: 25%;
    cursor: pointer;
    /* display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center; */
}

.iceContainer .shortcut a {
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
}

.iceContainer .shortcut_box .icon_box{
    width:40px;
    height: 40px;
    border-radius: 9px;
    text-align: center;
    padding: 8px 0;
}




.iceContainer .shortcut_box .icon{
    width: 24px;
    height: 24px;
}

.iceContainer .shortcut_box .name{
    color: #454973;
    font-size:12px;
    text-align: center;
}

.iceContainer .shortcut_top{
    position: relative;
}

.iceContainer .shortcut_top .title{
    margin-top: 0;
    border-left: 5px solid #3080fe;
    line-height: 20px;
    padding-left: 10px;
    margin-bottom: 20px;
    color: #000;
}

.iceContainer .shortcut_top .set{
    position: absolute;
    right: 0;
    top: 0;
    cursor: pointer;
}

.iceContainer .custom-calendar-guide{
    margin-top:-35px;
}