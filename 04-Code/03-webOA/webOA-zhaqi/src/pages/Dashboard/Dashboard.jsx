import React, { Component } from 'react';

import StatisticalCard from './components/StatisticalCard';
// import QuickNavigation from './components/QuickNavigation';
// import DataStatistics from './components/DataStatistics';

// import RealTimeStatistics from './components/RealTimeStatistics';

import LatestNews from './components/LatestNews';
import QuickNavigation from './components/QuickNavigation';
import OverviewChart from './components/OverviewChart';

// import './Dashboard.css';
import NoticeList from './components/NoticeList/NoticeList';
import Shortcut from './components/Shortcut/Shortcut';
import PendingApprove from './components/PendingApprove/PendingApprove';
import CalendarCard from './components/CalendarCard/CalendarCard';


export default class Dashboard extends Component {
  static displayName = 'Dashboard';

  constructor(props) {
    super(props);
    this.state = {};
  }

  render() {
    return (
      <div>
        <StatisticalCard/>
        <div id="dashboard-page">
        <PendingApprove/>
        <NoticeList/>
        <Shortcut/>
        <CalendarCard/>
        {/* <DataStatistics />

        <RealTimeStatistics /> */}

          {/* <OverviewChart/> */}
          {/* <QuickNavigation/> */}
        {/* <LatestNews /> */}
      </div>
      </div>
      
    );
  }
}
