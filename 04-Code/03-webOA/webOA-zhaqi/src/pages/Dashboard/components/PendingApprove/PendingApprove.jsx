import React, { Component } from 'react';
import {Feedback, moment } from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import { OAURL } from '../../../../components/URL/OAURL';
import { Link } from 'react-router-dom';
import '../../Dashboard.css';
import qs from 'qs';

export default class PendingApprove extends Component {
   static dispalyName = 'PendingApprove';
 
   constructor(props) {
     super(props);
     this.state = {
       url: OAURL,
       stuffId: sessionStorage.getItem("stuffId"),
       pendingList:[],
     };
   }

   //初始化获取数据
   componentDidMount(){
     this.getPendingApproveList();
   };

   //刷新table
   getPendingApproveList = () => {
    const { stuffId } = this.state;
    let params = {};
    params.page = 1;
    params.pageSize = 5;
    params.userId = stuffId;
    params.flag = '0';
    //获取公告列表
    axios({
      method: 'post',
      url: `${OAURL}/process/getListByPage`,
      data: qs.stringify(params)
    })
    .then(response => {
      let jsondata = response.data;
      this.setState({
        dataLoading: false,
      })
      if(jsondata.statusCode == 0){
        this.setState({
          pendingList: jsondata.list,
        })
      }
    })
    .catch(error => {
      Feedback.toast.error('系统繁忙，请稍后重试：'+error);
    })
    }

   render() {
     const { pendingList } = this.state;
     return (
       <div className="iceContainer">
         <IceContainer> 
           <div className="window">
            <div className="title">
                  待审批
                  <Link to="/process/approval" className="more">
                      更多
                  </Link>  
            </div>
                <ul id="pendingList">
                  {pendingList.map((item, index) => {
                    return (
                      <a className="itemContent" key={index} href="#/process/approval">  
                        <span>
                          <span className="itemCircle" />
                          <span className="itemTitle" >{item.applyTitle}</span>
                        </span>
                          <span className="itemTime">[{moment(item.applyTime).format('MM-DD')}]</span>
                      </a>
                    );
                  })}
              </ul>
           </div>
           
         </IceContainer>
       </div>
     );
   }
}