/* eslint global-require: 0 */
import React, { Component } from 'react';
import { Grid } from '@icedesign/base';
import IceContainer from '@icedesign/container';

const { Row, Col } = Grid;

const navigation = [
  {
    img: require('./images/TB1wdncx1SSBuNjy0FlXXbBpVXa-200-200.png'),
    title: '待审核',
    color: '#EF83C4',
    count: '30',
  },
  {
    img: require('./images/TB11ED_xYGYBuNjy0FoXXciBFXa-200-200.png'),
    title: '我的任务',
    color: '#37D1AB',
    count: '120',
  },
  {
    img: require('./images/TB1Kvg3x4GYBuNjy0FnXXX5lpXa-200-200.png'),
    title: '我的公告',
    color: '#ffa001',
    count: '160',
  },
  {
    img: require('./images/TB1aAH_xYGYBuNjy0FoXXciBFXa-200-200.png'),
    title: '总员工数',
    color: '#42C0EA',
    count: '450',
  }
];

export default class QuickNavigation extends Component {
  static displayName = 'QuickNavigation';

  static propTypes = {};

  static defaultProps = {};

  constructor(props) {
    super(props);
    this.state = {};
  }

  render() {
    return (
      <div>
        <IceContainer>
      <Row wrap gutter={20}>
        {navigation.map((item, index) => {
          return (
            <Col xxs="12" l="6" key={index}>
              <div style={{ background: item.color, padding: 20, borderRadius: 7 }}>
                <div style={styles.navItem}>
                  <div style={styles.imgWrap}>
                    <img src={item.img} alt="" style={styles.img} />
                  </div>
                  <div style={styles.infoWrap}>
                    <p style={styles.count}>{item.count}</p>
                    <h5 style={styles.title}>{item.title}</h5>
                  </div>
                </div>
              </div>
            </Col>
          );
        })}
      </Row>       
      </IceContainer>
      </div>
    );
  }
}

const styles = {
  navItem: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: '#fff',
    marginBottom: '0px',
  },
  imgWrap: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '62px',
    height: '62px',
    borderRadius: '50%',
    background: '#fff',
  },
  img: {
    width: '30px',
  },
  infoWrap: {
    display: 'flex',
    flexDirection: 'column',
    marginLeft: '15px',
  },
  count: {
    fontWeight: 'bold',
    fontSize: '14px',
    margin: '0',
  },
  title: {
    margin: '2px 0',
  },
};
