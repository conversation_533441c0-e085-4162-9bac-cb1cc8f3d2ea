/* eslint global-require: 0 */
import React, {Component} from 'react';
import {Grid,Feedback} from '@icedesign/base';
import IceContainer from '@icedesign/container';
import {RevenueURL} from "../../../../components/URL/RevenueURL";
import axios from "axios/index";

const {Row, Col} = Grid;


export default class QuickNavigation extends Component {
  constructor(props) {
    super(props);
    this.state = {
      month:{wx:0,wxbs:0,gt:0,gtbs:0},
      lastDay:{wx:0,wxbs:0,gt:0,gtbs:0},
      day:{wx:0,wxbs:0,gt:0,gtbs:0},
    };
  }

  componentWillMount() {
     this.queryOrderSource()
  }

  //查询订单数据
  queryOrderSource() {
    axios({
      method: 'post',
      url: `${RevenueURL}/homePageData/orderSource`,
    })
      .then((response) => {
        if (response.data.code) {
          let orderSource=response.data.datas
          let month=orderSource[0]
          let lastDay=orderSource[1]
          let day=orderSource[2]
          this.setState({
            month:month,
            lastDay:lastDay,
            day:day
          })

        } else {
          Feedback.toast.error(response.data.msg);
        }
      })
      .catch((error) => {
        Feedback.toast.error("系统繁忙请稍后再试" );
      })
  }

  render() {
    const {month,lastDay,day}=this.state
    const navigation = [
      {
        img: 'https://img.alicdn.com/tfs/TB1wdncx1SSBuNjy0FlXXbBpVXa-200-200.png',
        title: '本月柜台订单／金额 ',
        color: '#f8623b',
        count: `${month.gtbs}/${month.gt}`,
      },
      {
        img: 'https://img.alicdn.com/tfs/TB11ED_xYGYBuNjy0FoXXciBFXa-200-200.png',
        title: '本月微信订单／金额',
        color: '#37D1AB',
        count: `${month.wxbs}/${month.wx}`,
      },
      {
        img: 'https://img.alicdn.com/tfs/TB1Kvg3x4GYBuNjy0FnXXX5lpXa-200-200.png',
        title: '昨日柜台订单／金额',
        color: '#ffa001',
        count: `${lastDay.gtbs}/${lastDay.gt}`,
      },
      {
        img: 'https://img.alicdn.com/tfs/TB1aAH_xYGYBuNjy0FoXXciBFXa-200-200.png',
        title: '昨日微信订单／金额',
        color: '#42C0EA',
        count: `${lastDay.wxbs}/${lastDay.wx}`,
      },
      {
        img: 'https://img.alicdn.com/tfs/TB1BMGtyntYBeNjy1XdXXXXyVXa-200-200.png',
        title: '今日柜台订单／金额',
        color: '#5798F2',
        count: `${day.gtbs}/${day.gt}`,
      },
      {
        img: 'https://img.alicdn.com/tfs/TB1IQ2_xYGYBuNjy0FoXXciBFXa-200-200.png',
        title: '今日微信订单／金额',
        color: '#B277C9',
        count: `${day.wxbs}/${day.wx}`,
      },
      /* {
         img: 'https://img.alicdn.com/tfs/TB1wQD_xYGYBuNjy0FoXXciBFXa-200-200.png',
         title: '总用户数',
         color: '#EF83C4',
         count: '235',
       },*/
    ];

    return (
      <Row wrap gutter={20}>
        {navigation.map((item, index) => {
          return (
            <Col xxs="12" l="8" key={index}>
              <IceContainer style={{background: item.color}}>
                <div style={styles.navItem}>
                  <div style={styles.imgWrap}>
                    <img src={item.img} alt="" style={styles.img}/>
                  </div>
                  <div style={styles.infoWrap}>
                    <h5 style={styles.title}>{item.title}</h5>
                    <p style={styles.count}>{item.count}</p>
                  </div>
                </div>
              </IceContainer>
            </Col>
          );
        })}
      </Row>
    );
  }
}

const styles = {
  navItem: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: '#fff',
  },
  imgWrap: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '62px',
    height: '62px',
    borderRadius: '50%',
    background: '#fff',
  },
  img: {
    width: '30px',
  },
  infoWrap: {
    display: 'flex',
    flexDirection: 'column',
    marginLeft: '15px',
  },
  count: {
    fontWeight: 'bold',
    fontSize: '16px',
    margin: '0',
    textAlign:'center'
  },
  title: {
    margin: '2px 0',
  },
};
