import React, { Component } from 'react';
import axios from 'axios';
import qs from 'qs';
import { OAURL } from '../../../../components/URL/OAURL';
import IceContainer from '@icedesign/container';
import {I<PERSON>, Feedback, <PERSON>alog, Button, Tree  } from '@icedesign/base';
import { LoginURL } from '../../../../components/URL/LoginURL';
import { ImageUrl } from '../../../../components/URL/ImageUrl';
import '../../Dashboard.css'

const { Node: TreeNode } = Tree;


export default class Shortcut extends Component {
  constructor(props){
    super(props);
    this.state = {
      visible: false,
      selectedKeys: [], //当前选中的key
      url: OAURL,
      userId:sessionStorage.getItem("stuffId"),
      loginURL:LoginURL,
      iconData:[],
      treeData:[],

    }
  }

  componentWillMount(){
    // this.defaultShow();
    this.getMyShortcut();
    this.getMenuByRole();
  }

  onOpen = () => {
    this.setState({
      visible: true,
      // selectedKeys:[]
    });

  }

  onClose = () => {
    this.setState({
      visible: false
    });
  }


  onCheck = (keys, info) => {
    this.setState({
      selectedKeys:keys,
    })
  }

  //默认显示8个
  defaultShow = () =>{
    const {url} = this.state;
    let userId = this.state.userId
    axios({
      method:'post',
      url:url+'/shortcut/findByUserId',
    })
      .then((response) => {
        let jsondata = response.data;
        this.setState({
          iconData: jsondata
        });
        if(jsondata){
          var list = [];
          for(let i =0; i<jsondata.length;i++){
            list.push(jsondata[i].menuId);
          }
          this.setState({
            selectedKeys:list,
          })
        }
        
      })
      .catch((error) => {
        Feedback.toast.error('系统繁忙，请稍后重试:' + error);
      });
  }

  //获取快捷方式
  getMyShortcut = () =>{
    const {url} = this.state;
    let userId = this.state.userId
    axios({
      method:'post',
      url:url+'/shortcut/findByUserId',
      data:qs.stringify({
        userId
      })
    })
      .then((response) => {
        let jsondata = response.data;
        if(jsondata.length == 0){
          this.defaultShow();
        }else{
          this.setState({
            iconData: jsondata
          });
          if(jsondata){
            var list = [];
            for(let i =0; i<jsondata.length;i++){
              list.push(jsondata[i].menuId);
            }
            this.setState({
              selectedKeys:list,
            })
          }
        }
        
      })
      .catch((error) => {
        Feedback.toast.error('系统繁忙，请稍后重试:' + error);
      });
  }

  //获取所有菜单
  getMenuByRole = () => {
    const {loginURL} = this.state;
    axios.get(loginURL + '/getAuthority', {
      withCredentials: true,
    })
      .then((response) => {
        let jsondata = response.data;
        if (jsondata.statusCode == 0) {
          this.setState({
            treeData: jsondata.OA,
          });
        }
        else {
          alert(jsondata.msg);
        }
      })
      .catch((error) => {
        Feedback.toast.error('系统繁忙，请稍后重试:' + error);
      });
  }

  //保存菜单
  editIcon = () =>{
    if(this.state.selectedKeys == ''){
      Feedback.toast.error('未选中任何菜单');
    }else{
      const {url} = this.state;
      let userId = this.state.userId;
      let list = this.state.selectedKeys;
      if(list.length > 8){
        Feedback.toast.error('快捷方式最多选8个')
      }else{
        axios({
          method:'post',
          url:url+'/shortcut/addShortcutList',
          data:{
            userId,
            listMenuId: list
          }
        })
          .then((response) => {
            let jsondata = response.data;
            if(jsondata.statusCode == 0){
              Feedback.toast.success(jsondata.msg);
              this.getMyShortcut();
              this.onClose();
            }else{
              Feedback.toast.error(msg);
            }
          })
          .catch((error) => {
            Feedback.toast.error('系统繁忙，请稍后重试:' + error);
          });
      }
    }
    

  }


  render(){
    const footer = (
      <div>
        <Button type="primary" onClick={this.editIcon}>
          保存
        </Button>
        <Button  onClick={this.onClose} style={{ marginLeft: 20}}>
          取消
        </Button>
      </div>
    );
    const loop = data =>
      data.map(item => {
        return (
          <TreeNode
            label={item.name}
            key={item.id} 
            url={item.path} 
            image={item.imageUrl} 
            disabled={item.children && item.children.length}>
            {item.children && item.children.length ? loop(item.children) : null}
          </TreeNode>
        );
      });

    return(
      <div className="iceContainer">
        <IceContainer>
          <div className="window">
            <div className="shortcut_top">
              <h4 className="title">
                快捷入口
              </h4>
              <div className="set" onClick={this.onOpen}>
                <Icon type="set" style={{color:'#3080FE'}} />
              </div>
            </div>
            
            <div className="shortcut_box">
              {
                this.state.iconData.map((item,index)=>{
                  if(index == 0 || index == 7){
                    var color = '#EE706D'
                  }else if(index == 1 ||index ==4){
                    var color = '#5E83FB'
                  }else if(index == 2 || index == 5){
                    var color = '#F7DA47'
                  }else if(index == 3 || index == 6){
                    var color = '#58CA9A'
                  }
                  return(
                    <div className="shortcut">
                      <a href={'#'+item.url}>
                        <div className="icon_box" style={{background:color}}>
                          <img className="icon" src={`${ImageUrl}/`+item.imageAddress} alt=""/>
                        </div>
                        <p className="name">{item.name}</p>
                      </a>
                    </div>
                  )
                })
              }
            </div>
          </div>
          
        </IceContainer>

        <Dialog
          style={{width:'800px',height:'600px'}}
          visible={this.state.visible}
          closable="esc,mask,close"
          onCancel={this.onClose}
          onClose={this.onClose}
          title="设置"
          footer={footer}
          footerAlign="center"
          autoFocus={false}
        >
          <Tree
            checkable   
            checkedKeys={this.state.selectedKeys}  
            // defaultExpandedKeys={this.state.selectedKeys}                      
            onCheck={this.onCheck}
            showLine
            checkStrictly
            defaultExpandAll
          >  
            {loop(this.state.treeData)}    
          </Tree>
        </Dialog>

      </div>
      
    )
  }

}