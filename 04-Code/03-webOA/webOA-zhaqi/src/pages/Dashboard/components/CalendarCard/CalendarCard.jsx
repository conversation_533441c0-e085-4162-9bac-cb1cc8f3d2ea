import React, { Component } from 'react';
import {Feedback,Calendar  } from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import { OAURL } from '../../../../components/URL/OAURL';
import { Link } from 'react-router-dom';
import '../../Dashboard.css';


export default class CalendarCard extends Component {
   static dispalyName = 'CalendarCard';
 
   constructor(props) {
     super(props);
     this.state = {
       url: OAURL,
       stuffId: sessionStorage.getItem("stuffId"),
       noticeList:[],
     };
   }

   //初始化获取数据
   componentDidMount(){
     this.getNoticceList();
   };
   //刷新table
   getNoticceList = () => {
    const { url } = this.state;
    //获取公告列表
    axios.get(url+'/notice/all', {
        params: {
          page: 1,
          pageSize: 5,
        }
      })
      .then((response) => {
        let jsondata = response.data;
        let list = jsondata.noticelist;
        if (jsondata.statusCode == 0) {
          this.setState({
            noticeList: list.noticelist,
          });
        }
      })
      .catch((error) => {
        Feedback.toast.error('系统繁忙，请稍后重试:' + error);
      });
    }

   render() {
     const { noticeList } = this.state;
     return (
       <div className="iceContainer">
         <IceContainer> 
           <div className="window">
            <div className="title">
                  日历
                  {/* <Link to="/announcements/announcementsList" className="more">
                      更多
                  </Link>   */}
            </div>
            <div className="custom-calendar-guide">
              <Calendar type="card" language={"zh-cn"} value={new Date()} />
            </div>
           </div>
         </IceContainer>
       </div>
     );
   }
}