import React, { Component } from 'react';
import {Icon, Button, Balloon, Feedback } from '@icedesign/base';
import PropTypes from 'prop-types';
import axios from 'axios';

export default class DeleteBalloon extends Component {
  static propTypes = {
    refreshTable: PropTypes.func,
  };

  static defaultProps = {
    refreshTable: () => {},
  };

  constructor(props) {
    super(props);
    this.state = {
      visible: false,
    };
  }

  //确定删除，id是删除记录的id
  handleHide = (visible, code, id) => {
    if (code === 1) {
      //后台删除成功，再更新table
      axios({
        method: 'post',
        url: 'url',
        params: {
          id: id,
        }
      })
      .then((response) => {
          var jsondata = response.data;
          if(jsondata.statusCode == 0) {
              Feedback.toast.sucess('删除成功');
              //调用父页面刷新table的方法
              this.props.refreshTable();
              this.setState({
                  visible: false,
              });
          }
          else{
              Feedback.toast.error('删除失败');
          }
      })
      .catch((error) => {
          Feedback.toast.error('ajax请求异常：' +error);
      });
    }
  };

  handleVisible = (visible) => {
    this.setState({ visible });
  };

  render() {
    const visibleTrigger = (
      <a title="删除">
        <Icon size="small" type="ashbin" style={{ color: "#3399ff",cursor:"pointer"}}/>
      </a>
    );
    const { id } = this.props;
    const content = (
      <div>
        <div style={styles.contentText}>确认删除？</div>
        <Button
          id="confirmBtn"
          size="small"
          type="normal"
          shape="warning"
          style={{ marginRight: '5px' }}
          onClick={(visible) => this.handleHide(visible, 1, id)}
        >
          确认
        </Button>
        <Button
          id="cancelBtn"
          size="small"
          onClick={(visible) => this.handleHide(visible, 0)}
        >
          关闭
        </Button>
      </div>
    );

    return (
      <Balloon
        trigger={visibleTrigger}
        triggerType="click"
        visible={this.state.visible}
        onVisibleChange={this.handleVisible}
      >
        {content}
      </Balloon>
    );
  }
}

const styles = {
  contentText: {
    padding: '5px 0 15px',
  },
};
