import React, {Component} from 'react';
import { Table, Pagination, Field, Input, Button, Form } from '@icedesign/base';
import axios from 'axios';

export default class ContactTable extends Component{
    static displayName = "ContactTable";

    static propTypes = {};

    static defaultProps = {};

    constructor(props){
        super(props);
        this.state = {
            aa: 1,
            dataSource: [],
            index: 0,
            dataLoading: false,
            pagination:{
                page: 1,
                total: 0,
                pageSize: 10

            }
        };
        this.field = new Field(this);
    }

    componentDidMount() {
        this.setState({
            dataLoading: true,
        });
        axios.get('./mock/contact-book.json')
        .then((response) => {
            var jsondata = response.data;
            
            if(jsondata.statusCode == 0){
                this.setState({
                    dataSource: jsondata.contactList,
                    pagination: jsondata.pagination,
                    dataLoading: false
                    
                });
            }
            else{
                alert("后台数据获取失败");
            }
        })
        .catch((error) => {
            alert(error);
        });
    }
    changePage = (pageNo, aa) => {
        
        this.setState({
            dataLoading: true,
        });
        axios.get('/mock/contact-book.json', {
            params: {
                page: pageNo,
            }
        })
        .then((response) => {
            var jsondata = response.data;
            if(jsondata.statusCode == 0){
                this.setState({
                    dataSource: jsondata.contactList,
                    pagination: jsondata.pagination,
                    dataLoading: false
                    
                });
            }
            else{
                alert("后台数据获取失败");
            }
        })
        .catch((error) => {
            alert(error);
        });
    };
    changePageSize = (pageSize) => {
        this.setState({
            dataLoading: true,
        });
        axios.get('/mock/contact-book.json', {
            params: {
                pageSize: pageSize,
            }
        })
        .then((response) => {
            var jsondata = response.data;
            if(jsondata.statusCode == 0){
                this.setState({
                    dataSource: jsondata.contactList,
                    pagination: jsondata.pagination,
                    dataLoading: false
                    
                });
            }
            else{
                alert("后台数据获取失败");
            }
        })
        .catch((error) => {
            alert(error);
        });
    }
    rowIndex = (value, index) =>{
        return index + 1;
    };

    doSearch = (e) => {
        this.field.validate((error, value) => {
            let nameOrTel = value.nameOrTel;
            alert(nameOrTel);
            axios.get('/uuu',{
                params:{
                    nameOrTel: nameOrTel,
                }
            })
        });
       
    };

    render(){
        const { init } = this.field;
        const {
            dataSource,
            pagination,
            dataLoading
        } = this.state;

      return(
        <div>
            <div style={{marginTop: 20,marginBottom: 5}} align="right">
            <Form direction="hoz" field={this.field}>
                <Form.Item >
                    <Input {...init('nameOrTel')} ref="nameOrTel" placeholder="请输入要搜索的名字或电话"/>
                </Form.Item>
                <Form.Item>
                    <Button type="primary"  onClick={this.doSearch}>搜索</Button>
                </Form.Item>
            </Form>
            </div>
            <Table dataSource={this.state.dataSource} isLoading={this.state.dataLoading}>
               <Table.Column title="序号" cell={this.rowIndex} align="center"/>
               <Table.Column title="姓名" dataIndex="name" align="center"/>
               <Table.Column title="手机" dataIndex="tel" align="center"/>
               <Table.Column title="电话" dataIndex="phone" align="center"/>
               <Table.Column title="电子邮箱" dataIndex="email" align="center"/>
               <Table.Column title="部门" dataIndex="dept" align="center"/>
            </Table> 
            <Pagination
                pageSizeSelector="dropdown"
                onChange={this.changePage}
                onPageSizeChange={this.changePageSize}
                current={pagination.page}
                pageSize={pagination.pageSize}
                total={pagination.total}
                size="small"
                style={{marginTop: "30px", marginLeft:"250px", marginRight:"50px"}}
            />
        </div>
      );
    }



}