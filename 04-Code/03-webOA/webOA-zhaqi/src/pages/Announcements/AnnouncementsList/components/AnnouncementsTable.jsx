import React, { Component } from 'react';
import { Feedback, Table, Field, Select, Form, Input, Button, Pagination, DatePicker } from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import ViewAnnouncements from './ViewAnnouncements';
import EditAnnouncements from './EditAnnouncements';
import DeleteBalloon from './DeleteBalloon';
import { OAURL } from '../../../../components/URL/OAURL';
import {LoginURL} from '../../../../components/URL/LoginURL';

export default class GetMaterialTable extends Component {
  static dispalyName = 'GetMaterialTable';

  constructor(props) {
    super(props);
    this.state = {
      url: OAURL,
      loginURL: LoginURL,
      dataLoading: true,
      dataSource: [],
      total: 0,
      page: 1,
      pageSize: 10,
      buttons: [],
    };
    this.field = new Field(this);
  }

  //初始化获取数据
  componentDidMount() {
    this.freshTabel();
    this.getMenuByRole();
  };

  //刷新table
  freshTabel = () => {
    const {url} =this.state;
    axios.get(url+'/notice/all', {
      params: {
      page: 1,
      pageSize: 10
    }})
      .then((response) => {
        let jsondata = response.data;
        let list = jsondata.noticelist
        if (jsondata.statusCode == 0) {
          this.setState({
            dataLoading: false,
            dataSource: list.noticelist,
            total: list.total,
            page: 1,
            pageSize: list.pageSize,
          });
        }
        else {
          alert('后台返回数据错误');
        }
      })
      .catch((error) => {
        alert('系统繁忙，请稍后重试:' + error);
      });
  };

   //获取按钮权限
   getMenuByRole = () => {
    const {loginURL} = this.state;
    axios.get(loginURL + '/getAuthority', {
      withCredentials: true,
    })
      .then((response) => {
        let jsondata = response.data;
        if (jsondata.statusCode == 0) {
          this.setState({
            buttons: jsondata.buttons,
          });
        }
        else {
          alert(jsondata.msg);
        }
      })
      .catch((error) => {
        Feedback.toast.error('系统繁忙，请稍后重试:' + error);
      });
  }

  //查询
  doSearch = () => {
    this.setState({
      dataLoading: true,
    });
    const { page,pageSize,url} = this.state;
    let searchValue = this.field.getValues();
    axios.get(url+'/notice/all', {
      params: {
        searchKey: searchValue.searchKey,
        page: page,
        pageSize: pageSize,
      }
    }).then((response) => {
        
        let jsondata = response.data;
        let list = jsondata.noticelist;
         
        if(jsondata.statusCode == 0){
          this.setState({
            dataSource: list.noticelist,
            total: list.total,
            page: 1,
            pageSize: list.pageSize,
            dataLoading: false,
          });
        }
      })
      .catch((error) => {
         Feedback.toast.error('系统繁忙，请稍后重试:' + error);
      });
  };

  reset = () => {
    this.field.reset();
  }

  //翻页
  changePage = (pageNo) => {
    const {url} =this.state;
    this.setState({
      dataLoading: true,
    });
    let searchValue = this.field.getValues();
    const { pageSize } = this.state;
    axios.get(url+'/notice/all', {
      params: {
        searchKey: searchValue.searchKey,
        page: pageNo,
        pageSize: pageSize,
      }
    }).then((response) => {
      let jsondata = response.data;
      let list = jsondata.noticelist //要改的
      if (jsondata.statusCode == 0) {
        this.setState({
          dataSource: list.noticelist, //改
          total: list.total,
          page: pageNo,
          pageSize: list.pageSize,
          dataLoading: false,
        });
      }
    }).catch((error) => {
      Feedback.toast.error('系统繁忙，请稍后重试' + error);
    })
  }

   //改变显示记录数
   changePageSize = (pageSize) => {
    this.setState({
      dataLoading: true,
    });
    const { page,url } = this.state;
    let searchValue = this.field.getValues();
    axios.get(url+'/notice/all', {
      params: {
        searchKey:searchValue.searchKey,
        page: page,
        pageSize: pageSize,
      }
    })
      .then((response) => {
        let jsondata = response.data;
        let list = jsondata.noticelist;
         
        if(jsondata.statusCode == 0){
          this.setState({
            dataSource: list.noticelist,
            total: list.total,
            page: 1,
            pageSize: list.pageSize,
            dataLoading: false,
          });
        }
      })
      .catch((error) => {
         Feedback.toast.error('系统繁忙，请稍后重试:' + error);
      });
  };

  //带分页的序号格式化
  rowIndex = (value, index) => {
    const { pageSize, page } = this.state;
    if (page == 1) {
      return index + 1;
    }
    else {
      return (index + 1) + (page - 1) * pageSize;
    }
  }

  //操作按钮的格式化
  rowOptButton = (value, index, record) => {
    const {buttons} = this.state;
    let canEdit, canDelete = false;
    for (let i = 0; i < buttons.length; i++) {
      if (buttons[i].menuPath == '/announcements/announcementsList' && buttons[i].buttonCode == 'edit') {
        canEdit = true;
      }
       else if(buttons[i].menuPath == '/announcements/announcementsList' && buttons[i].buttonCode == 'delete'){
        canDelete = true;
      }
    }
    return (
      <span style={{ display: "flex", justifyContent: "space-around" }}>
        <ViewAnnouncements
          record={record}
        />
        {
          canEdit ?
            <EditAnnouncements
              record={record}
              freshTabel={this.freshTabel}
            />
          :
          void(0)
        }
        {
          canDelete ?
            <DeleteBalloon
              id={record.inid}
              freshTabel={this.freshTabel}
            />
          : 
          void(0)
        }
      </span>
    );
  };

  //公告类别格式化
  typeFm = (value, index, record) => {
    if (record.type == "0") {
      return "工作通知";
    }
    else {
      return "活动通知";
    }
  };

  render() {
    const { init } = this.field;
    return (
      <div>
        <IceContainer title="公告列表">
          <div align="right">
            <Form direction="hoz" field={this.field}>
              <Input style={{ width: 200 }} {...init('searchKey')} placeholder="请输入主题或内容" />
              <Form.Item>
                <Button type="primary" onClick={this.doSearch} className="button" size="small" style={{marginLeft: 10}}>搜索</Button>
                <Button type="secondary" onClick={this.reset} className="button" size="small" style={{marginLeft: 10}}>重置</Button>
              </Form.Item>
            </Form>
          </div>
          <Table
            dataSource={this.state.dataSource}
            isLoading={this.state.dataLoading}
            primaryKey="inid"
          >
            <Table.Column title="序号" cell={this.rowIndex} align="center" />
            <Table.Column title="标题" dataIndex="title" align="center" />
            <Table.Column title="主题" dataIndex="theme" align="center" />
            <Table.Column title="公告类别" dataIndex="type" cell={this.typeFm} align="center" />
            <Table.Column title="发送人" dataIndex="sender" align="center" />
            <Table.Column title="时间" dataIndex="time" align="center" />
            
            <Table.Column title="操作" cell={this.rowOptButton} width={200} align="center" />
          </Table>
          <Pagination
            pageSizeSelector="dropdown"
            onChange={this.changePage}
            onPageSizeChange={this.changePageSize}
            total={this.state.total}
            pageSize={this.state.pageSize}
            current={this.state.page}
            size="small"
            style={{ marginTop: "30px", marginLeft: "250px", marginRight: "50px" }}
          />
        </IceContainer>
      </div>
    );
  }

}