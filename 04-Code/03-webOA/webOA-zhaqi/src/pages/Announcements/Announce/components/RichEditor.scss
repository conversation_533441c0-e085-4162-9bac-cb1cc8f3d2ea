@charset "UTF-8";

/* fallback */
@font-face {
  font-family: 'Material Icons';
  font-style: normal;
  font-weight: 400;
  src: url(https://fonts-gstatic.proxy.ustclug.org/s/materialicons/v34/2fcrYFNaTjcS6g4U3t-Y5UEw0lE80llgEseQY3FEmqw.woff2)
    format('woff2');
}

.material-icons {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

.rich-editor {
  input,
  textarea {
    font-family: 'Roboto', sans-serif;
    line-height: 1.4;
    background: #eee;
  }

  ul {
    list-style: disc;
    margin-left: 1em;
  }
  ol {
    list-style: decimal;
    margin-left: 1em;
  }

  p {
    margin: 0;
  }

  pre {
    padding: 10px;
    background-color: #eee;
    white-space: pre-wrap;
  }

  :not(pre) > code {
    font-family: monospace;
    background-color: #eee;
    padding: 3px;
  }

  img {
    max-width: 100%;
    max-height: 20em;
  }

  img.active {
    box-shadow: 0 0 0 2px blue;
  }

  blockquote {
    border-left: 2px solid #ddd;
    margin-left: 0;
    margin-right: 0;
    padding-left: 10px;
    color: #aaa;
    font-style: italic;
  }

  blockquote[dir='rtl'] {
    border-left: none;
    padding-left: 0;
    padding-right: 10px;
    border-right: 2px solid #ddd;
  }

  table {
    border-collapse: collapse;
  }

  td {
    padding: 10px;
    border: 2px solid #ddd;
  }

  input {
    box-sizing: border-box;
    font-size: 0.85em;
    width: 100%;
    padding: 0.5em;
    border: 2px solid #ddd;
    background: #fafafa;
  }

  input:focus {
    outline: 0;
    border-color: blue;
  }

  /**
   * Icons.
   */

  .material-icons {
    font-size: 18px;
    vertical-align: text-bottom;
  }

  /**
   * App.
   */

  .nav {
    padding: 10px 15px;
    color: #aaa;
    background: #000;
  }

  .nav-title {
    margin-right: 0.5em;
  }

  .nav-links {
    float: right;
  }

  .nav-link {
    margin-left: 1em;
    color: #aaa;
    text-decoration: none;
  }

  .nav-link:hover {
    color: #fff;
    text-decoration: underline;
  }

  .tabs {
    padding: 15px 15px;
    background-color: #222;
    text-align: center;
    margin-bottom: 30px;
  }

  .tab {
    color: #777;
    display: inline-block;
    text-decoration: none;
    padding: 0.2em 0.5em;
    border-radius: 0.2em;
    margin-bottom: 0.2em;
  }

  .tab:hover {
    background: #333;
  }

  .tab + .tab {
    margin-left: 0.5em;
  }

  .tab.active {
    color: white;
    background: #333;
  }

  /**
   * Example.
   */

  .example,
  .error {
    max-width: 42em;
    margin: 0 auto 20px;
    padding: 20px;
  }

  .example {
    background: #fff;
  }

  .error {
    background: #fffae0;
  }

  .error .info {
    background: #fbf1bd;
    white-space: pre;
    overflow-x: scroll;
    margin-bottom: 0;
  }

  .rich-editor-body > * > * + * {
    margin-top: 1em;
  }

  .rich-editor-menu > * {
    display: inline-block;
  }

  .rich-editor-menu > * + * {
    margin-left: 15px;
  }

  .button {
    color: #ccc;
    cursor: pointer;
  }

  .button[data-active='true'] {
    color: black;
  }

  .rich-editor-toolbar-menu {
    position: relative;
    padding: 1px 18px 17px;
    margin: 0 -20px;
    border-bottom: 2px solid #eee;
    margin-bottom: 20px;
  }

  .rich-editor-toolbar-menu .search {
    position: relative;
  }

  .rich-editor-toolbar-menu .search-icon {
    position: absolute;
    top: 0.5em;
    left: 0.5em;
    color: #ccc;
  }

  .rich-editor-toolbar-menu .search-box {
    padding-left: 2em;
    width: 100%;
  }

  .hover-menu {
    padding: 8px 7px 6px;
    position: absolute;
    z-index: 1;
    top: -10000px;
    left: -10000px;
    margin-top: -6px;
    opacity: 0;
    background-color: #222;
    border-radius: 4px;
    transition: opacity 0.75s;
  }

  .hover-menu .button {
    color: #aaa;
  }

  .hover-menu .button[data-active='true'] {
    color: #fff;
  }

  .emoji.selected {
    outline: 2px solid blue;
  }

  .check-list-item + .check-list-item {
    margin-top: 0;
  }

  .check-list-item {
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  .check-list-item.checked {
    opacity: 0.666;
    text-decoration: line-through;
  }

  .check-list-item > span:first-child {
    margin-right: 0.75em;
  }

  .check-list-item > span:last-child {
    flex: 1;
  }

  .check-list-item > span:last-child:focus {
    outline: none;
  }

  .word-counter {
    margin-top: 10px;
    padding: 12px;
    background-color: #ebebeb;
    display: inline-block;
  }
}
