import React, { Component } from 'react';
import {<PERSON>edback, Icon, Button, Table, Pagination, Field, Form, Grid, Input, NumberPicker, Dialog } from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import { OAURL } from '../../../../components/URL/OAURL';
import qs from 'qs';
import {Link} from 'react-router-dom';
import './OutOfStock.css'
import FoundationSymbol from 'foundation-symbol';

const { Row } = Grid;

export default class OutOfStock extends Component {

  constructor(props) {
    super(props);
    // 表格可以勾选配置项
    this.rowSelection = {
      // 表格发生勾选状态变化时触发，ids可以将所有勾选的行ID获取到
      onChange: (ids, records) => {
        // debugger
        this.setState({
          selectedRowKeys: ids,
        });
      },
      onSelect: (selected, record, records) => {
        const { selectedRecord } = this.state;
        if(selected){
          selectedRecord.push(record);
          this.setState({
            selectedRecord,
          })
        }
        else{
          let index = selectedRecord.indexOf(record);
          if(index > -1){
            selectedRecord.splice(index, 1);
          }
          this.setState({
            selectedRecord,
          })
        }
      }
    };
    this.state = {
      visible: false,
      url: OAURL,
      stuffId: sessionStorage.getItem('stuffId'),//当前登录人ID
      stuffName: sessionStorage.getItem('realName'),//当前登录人姓名
      deptId: sessionStorage.getItem("deptId"),//当前登录人部门Id
      deptName: sessionStorage.getItem("deptName"),//当前登录人部门名称
      total: 0,
      pageSize: 10,
      page: 1,
      dataLoading: false,
      selectedRowKeys: [],
      selectedRecord: [],
      dataSource_1: [],
      dataSource_2: [],
    };
    this.field = new Field(this);
  }

  componentWillMount(){
    let param = this.props.location.state
    if (param == undefined) {
        this.props.history.push('./waitLeaveStore');
        return;
    }else{
      this.refreshTable_1();
      this.refreshTable_2();
    }
    
  }


  //刷新table
  refreshTable_1 = () => {
    const { url } = this.state;
    const param = this.props.location.state;
    let record = param.record;
    axios({
      method: 'get',
      url: url+'/buyMaterial/view',
      params: {
        id: record.id
      },
    })
    .then((response) => {
      let jsondata = response.data;
      if(jsondata.statusCode == 0){
        this.setState({
          dataSource_1: jsondata.obj.applyMaterailList,
          dataLoading: false,
        });
      }
     })
     .catch((error) => {
        Feedback.toast.error('系统繁忙，请稍后重试:' +error);
     });
  }

  //刷新table
  refreshTable_2 = () => {
    const { url, page, pageSize, deptId } = this.state;
    this.setState({
      dataLoading: true,
    })
    let values = this.field.getValues();
    values.page = page;
    values.pageSize = pageSize;
    values.loginUserDeptId = deptId;
    axios({
      method: 'post',
      url: url+'/material/haveStoreList',
      data: qs.stringify(values)
    })
    .then((response) => {
      let jsondata = response.data;
      if(jsondata.statusCode == 0){
        this.setState({
          dataSource_2: jsondata.list,
          dataLoading: false,
          page: 1,
          pageSize: 10,
          total: jsondata.total,
        });
      }
     })
     .catch((error) => {
        Feedback.toast.error('系统繁忙，请稍后重试:' +error);
     });
  }


  

  

  //搜索
  doAxiosMethod = ( page, pageSize ) => {
    const { url, deptId } = this.state;
    this.setState({
      dataLoading: true,
    })
    let values = this.field.getValues();
    values.page = page;
    values.pageSize = pageSize;
    values.loginUserDeptId = deptId;
    axios({
      method: 'post',
      url: url+'/material/haveStoreList',
      data: qs.stringify(values)
    })
    .then((response) => {
      let jsondata = response.data;
      if(jsondata.statusCode == 0){
        this.setState({
          dataSource_2: jsondata.list,
          dataLoading: false,
          page: page,
          pageSize: pageSize,
          total: jsondata.total,
        });
      }
     })
     .catch((error) => {
        Feedback.toast.error('系统繁忙，请稍后重试:' +error);
     });
  }

  //搜索
  doSearch = () => {
    this.setState({
      dataLoading : true,
    });
    this.doAxiosMethod(1,10);
  };

  //清空
  doClear = () => {
    this.field.reset();
   };

   //翻页
   changePage = (pageNo) => {
    const { pageSize } = this.state;
    this.setState({
      dataLoading: true,
    });
    this.doAxiosMethod(pageNo, pageSize);
  };
  //改变显示记录数
  changePageSize = (pageSize) => {
    this.setState({
        dataLoading: true,
    });
    this.doAxiosMethod(1, pageSize);
  };

  //带分页的序号格式化
  indexFm = (value, index) => {
    const { pageSize, page } = this.state;
    if (page == 1) {
      return index + 1;
    }
    else {
      return (index + 1) + (page - 1) * pageSize;
    }
  }



  //出库数量输入框
  inputFm = (value,index, record) =>{
    return(
      <NumberPicker 
        type="inline" 
        defaultValue={1}
        min={1}
        max={record.storeNum}
        inputWidth={80}
        onChange={(value) => this.numOnchange(index, value)}
      />
    )
  }

  numOnchange = (index, value) =>{
    let { selectedRecord } = this.state;
    selectedRecord[index].num = value;
    this.setState({
      selectedRecord,
    })
  }

  numFm = (value ) =>{
    if(!value){
      return 0
    }else{
      return value
    }
  }

  submit = () =>{
    const { stuffId, stuffName, deptId, deptName } = this.state;
    var someth = this.state.selectedRecord;
    var param = this.props.location.state.record;
    var remarks = this.field.getValue('remarks');
    var id = param.id; //申请单的id
    var createId = stuffId; //当前登录人id
    var createName = stuffName; //当前登录人姓名
    var departmentId = deptId; //当前登录人部门id
    var departmentName = deptName; //当前登录人部门名称
    let list=[];
    for(let i = 0; i<someth.length; i++){
      if(someth[i].num == undefined){
        someth[i].num = 1
      }
      list.push(
        {
          id : someth[i].id, //材料id
          standard : someth[i].standard, //材料规格
          name : someth[i].name, //材料名称
          unit: someth[i].unit, //单位
          storeNum : someth[i].num, //出库数量
          applyName : param.userName, //申请人姓名
          applyDept : param.deptName, //申请人部门
          createId : createId, //当前登录人id
          createName : createName, //当前登录人姓名
          deptId: departmentId, //当前登录人部门id
          deptName: departmentName, //当前登录人部门名称
        }
      )
    }
    var values={};
    values.id = id;
    values.remarks = remarks;
    values.materialList = list;
    values.userId = createId;
    values.userName = createName;

    const { url } = this.state;
    this.setState({
      save: true,
    })
    axios({
      method: 'post',
      url: url+'/waitLeaveStore/outStore',
      // data: qs.stringify(values)
      data: values
    })
    .then((response) => {
      let jsondata = response.data;
      if(jsondata.statusCode == 0){
        this.props.history.push('/material/waitLeaveStore');
        Feedback.toast.success('出库成功');
      }
    })
    .catch((error) => {
        Feedback.toast.error('系统繁忙，请稍后重试:' +error);
    });
    
    
  }

  onOpen = () => {
    var someth = this.state.selectedRecord;
    if(someth == ''){
      Feedback.toast.error('没有选中任何材料出库，请确认选择出库材料');
    }else{
      this.setState({
        visible: true
      });
    }
    
  };

  onClose = () => {
    this.setState({
      visible: false
    });
  };

  render() {
    const { init } = this.field;
    const formItemLayout = {
      labelCol : {
      fixedSpan: 6,
      }
    };
    const footer = (
      <div align="center">
        <Button
          type="primary" 
          onClick={this.submit} 
          loading={this.state.save} 
          style={{marginRight: 30}}
        >
          出库
        </Button>
        <Button
          onClick={this.onClose}
        >
          取消
        </Button>
      </div>  
    );
    return (
      <div>
        <div
          style={{marginBottom:'5px'}}
        >
          <Link
            to={{
              pathname: `./waitLeaveStore`,
          }}>
              <Button className="button" type="primary">
                  <FoundationSymbol type="backward" style={{ marginRight: 5 }} size="small" />
                  返回
              </Button>
          </Link>
        </div>
        
        <IceContainer title="申请领购材料列表">
          <Table
            dataSource={this.state.dataSource_1}
            style={{ marginTop: 5 }}
            isLoading={this.state.dataLoading}
          >
            <Table.Column title="材料名称" dataIndex="name" align="center" />
            <Table.Column title="规格" dataIndex="standard" align="center" />
            <Table.Column title="单位" dataIndex="unit" align="center" />
            <Table.Column title="申请数量" dataIndex="appCount" align="center"/>
          </Table>
        </IceContainer>

        <IceContainer title="选择出库材料">
          <Form direction="hoz" field={this.field}>
            <Row justify="space-around">
              <Form.Item label="材料名称：" {...formItemLayout}>
                <Input
                  {...init('name')} 
                  style={{ width: '180px' }} 
                  placeholder="请输入" 
                />
              </Form.Item>
              <Form.Item label="材料规格：" {...formItemLayout}>
                <Input
                  {...init('standard')} 
                  style={{ width: '180px' }} 
                  placeholder="请输入" 
                />
              </Form.Item>
            </Row>
          </Form>
          <div align="center">
            <Button type="primary" onClick={this.doSearch} size="medium" className="button">
              <Icon type="search" />
              搜索
            </Button>
            <Button style={{ marginLeft: '30px' }} type="secondary" onClick={this.doClear} size="medium" className="button">
              <Icon type="refresh" />
              重置
            </Button>
          </div>
          <Table
            className="table_2"
            dataSource={this.state.dataSource_2}
            style={{ marginTop: 5 }}
            isLoading={this.state.dataLoading}
            primaryKey="id"
            rowSelection={{
              ...this.rowSelection,
              selectedRowKeys: this.state.selectedRowKeys,
            }}
          >
            <Table.Column title="序号" cell={this.indexFm} width={55} align="center" />
            <Table.Column title="材料名称" dataIndex="name" align="center" />
            <Table.Column title="规格" dataIndex="standard" align="center" />
            <Table.Column title="单位" dataIndex="unit" align="center" />
            <Table.Column title="库存数量" dataIndex="storeNum" align="center"/>
            {/* <Table.Column title="出库数量" cell={this.inputFm} align="center"/> */}
          </Table>
          <Pagination
            pageSizeSelector="dropdown"
            onChange={this.changePage}
            onPageSizeChange={this.changePageSize}
            total={this.state.total}
            pageSize={this.state.pageSize}
            current={this.state.page}
            size="small"
            style={{ marginTop: "30px", marginLeft:"250px", marginRight:"50px"}}
          />
        </IceContainer>

        <IceContainer title="已选出库材料">
          <Table
            dataSource={this.state.selectedRecord}
            style={{ marginTop: 5 }}
          >
            <Table.Column title="材料名称" dataIndex="name" align="center" />
            <Table.Column title="规格" dataIndex="standard" align="center" />
            <Table.Column title="单位" dataIndex="unit" align="center" />
            <Table.Column title="库存数量" dataIndex="storeNum" align="center"/>
            <Table.Column title="出库数量" dataIndex="num" cell={this.inputFm} align="center"/>
          </Table>
          <Form>
          <Form.Item
            label="备注：" 
            style={{marginTop:'20px',fontSize:'16px',fontWeight:'bold',color:'#333'}}
          >
            <Input
              {...init('remarks')} 
              style={{ width: 500, height: 30}} 
              placeholder="请输入"
            />
          </Form.Item>
          </Form>
          <div align="center">
              <Button
                className="button"
                type="primary" 
                onClick={this.onOpen} 
                loading={this.state.save} 
                style={{marginRight: 30}}
              >
                出库
              </Button>

              <Dialog
                footer={footer}
                visible={this.state.visible}
                closable="esc,mask,close"
                onClose={this.onClose}
                title="提示"
              >
                <h3>请核对信息，是否确认出库？</h3>
              </Dialog>

              <Link to={{
                  pathname: `./waitLeaveStore`,
              }}>
                <Button
                  className="button"
                >
                  返回
                </Button>
              </Link>
            </div>     
        </IceContainer>
        
      </div>
    );
  }
}

const styles = {
  contentText: {
    padding: '5px 0 15px',
  },
};
