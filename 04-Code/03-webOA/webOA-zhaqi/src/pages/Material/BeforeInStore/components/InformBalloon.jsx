import React, { Component } from 'react';
import {Feedback, Icon, Button, Balloon } from '@icedesign/base';
import axios from 'axios';
import { OAURL } from '../../../../components/URL/OAURL';
import qs from 'qs';

export default class InformBalloon extends Component {

  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      url: OAURL,
      status: '',
      loginName : sessionStorage.getItem('realName'),//当前登录人的名字
      userId : sessionStorage.getItem("stuffId"),//当前登录人的id
    };
  }

  //初始化获取数据
  componentWillMount() {
    let record = this.props.record;
    this.setState({
      status : record.status
    })
  };

  handleVisible = (visible) => {
    this.setState({ visible });
  };

  //停用
  handleInform = (visible, id, code) => {
    
    if (code == 1) {

      const { url } = this.state;
      let record = this.props.record;
      axios({
        method: "post",
        url: url + "/material/sendMessage",
        data: {
                applyId : record.id,
                stuffId : record.userId,
                applyName : record.userName, //申请人的名字
                userName : this.state.loginName, //当前登录人的名字
                userId : this.state.userId, //当前登录人的id
              }
      })
      .then((response) => {
        let statusCode = response.data.statusCode;
        if( statusCode == 0){
          Feedback.toast.success('通知成功');
          this.props.refreshTable();
        }
        else{
          Feedback.toast.error('通知失败');
        }
      })
      .catch((error) => {
        Feedback.toast.error("系统繁忙，请稍后再试");
      });
    }
    this.setState({
      visible: false,
    });
  };



  render() {
    const { id } = this.props.record;
    const informIcon = (
      <span title="通知申请人">
        <Icon size="small" type="comments" style={{ color: "#3399ff",cursor:"pointer"}}/>
      </span>
    );

    const content = (
      <div>
        <div style={styles.contentText}>是否发送领取通知？</div>
        <Button
          id="confirmBtn"
          size="small"
          type="normal"
          shape="warning"
          style={{ marginRight: '5px' }}
          onClick={visible => this.handleInform(visible, id, 1)}
        >
          确认
        </Button>
        <Button style={{ display: 'inline-block'}}
          id="cancelBtn"
          size="small"
          onClick={visible => this.handleInform(visible, id, 0)}
        >
          取消
        </Button>
      </div>
    );


    

    
    return (
      <div>
        <Balloon
          trigger={informIcon}
          triggerType="click"
          visible={this.state.visible}
          onVisibleChange={this.handleVisible}
          style={{ width: 160 }}
        >
          {content}
        </Balloon>
      </div>
    );
  }
}

const styles = {
  contentText: {
    padding: '5px 0 15px',
  },
};
