import React, { Component } from 'react';
import {Icon, Upload, <PERSON><PERSON>back, Dialog, Button, Form, Input, Field, TreeSelect, Grid, Radio, DatePicker, moment, NumberPicker, Table } from '@icedesign/base';
import axios from 'axios';
import qs from 'qs';
import { OAURL } from '../../../../components/URL/OAURL';
import IceContainer from '@icedesign/container';
import {Link} from 'react-router-dom';
import FoundationSymbol from 'foundation-symbol';

const { Row, Col } = Grid;

export default class ApplyDetails extends Component {
  static displayName = "ViewBuyMaterialInfo";

  constructor(props) {
    super(props);
    this.state = {
      url: OAURL,
      searchValue:{},
      record:{},
      dataSource:[],
      from: null,
      dataMaterialList:[],
      remarks:[],
      storage:'',
    };
    this.field = new Field(this, { autoUnmount: true});
  }

  //初始化获取下拉框数据
  componentDidMount() {
    const param = this.props.location.state;
    if (param == undefined) {
        this.props.history.push('./waitLeaveStore');
        return;
    }
    if(param.opt == 'view'){
      const record = param.record;
      this.getApplyDetail(record.id)
      this.field.setValues({...record});
      this.setState({
        searchValue: param.searchValue,
        record: record,
        from: param.from,
      })
    }
    if(param.storage == 1){
      const record = param.record;
      this.getMaterialList(record.id);
      this.setState({
          storage:1
        }
      );
    }
  }

  //获取申请单详情
  getApplyDetail = (applyId) => {
    const { url } = this.state;
    axios({
      method: 'get',
      url: url+'/buyMaterial/view',
      params: {
        id: applyId
      },
    })
    .then((response) => {
      let jsondata = response.data;
      if(jsondata.statusCode == 0){
        this.field.setValues({...jsondata.obj});
        this.setState({
          record: jsondata.obj,
          dataSource: jsondata.obj.applyMaterailList,
          outStoreRemarks: jsondata.obj.outStoreRemarks
        });
      }
    })
    .catch((error) => {
      Feedback.toast.error('系统繁忙，请稍后重试:' +error);
    });
  }

  //获取出库列表
  getMaterialList = (applyId) => {
    const { url } = this.state;
    axios({
      method: 'post',
      url: url+'/materialRecord/findAll',
      data: qs.stringify(
        {
          applyId: applyId
        }
      ),
    })
    .then((response) => {
      this.setState({
        dataMaterialList: response.data,
      });
    })
    .catch((error) => {
      Feedback.toast.error('系统繁忙，请稍后重试:' +error);
    });
  }


  render() {
    const init = this.field.init;
    const { stuffId, opt, searchValue, record, from } = this.state;
    const { dataSource } = this.state;
    const formItemLayout = {
      labelCol: {
        fixedSpan: 6
      },
    };
    return (
      <div>
        <div style={{ marginBottom: 5 }}>
        {
          from == 'apply' ?
            <Link to={{
                pathname: `/apply/buymaterialApply`,
                state:{searchValue: searchValue}
            }}>
                <Button className="button" type="primary">
                    <FoundationSymbol type="backward" style={{ marginRight: 5 }} size="small" />
                    返回
                </Button>
            </Link>
          :
            <Link to={{
                pathname: `/material/waitLeaveStore`,
                state:{searchValue: searchValue}
              }}>
                <Button className="button" type="primary">
                    <FoundationSymbol type="backward" style={{ marginRight: 5 }} size="small" />
                    返回
                </Button>
            </Link>
          }
        </div>
        <IceContainer title="材料领购申请单">
        <Form direction="ver" field={this.field}>
            <Input {...init("id")} htmlType="hidden" /> {/* 申请单id */}
            <Input {...init("userId", { initValue: stuffId })} htmlType="hidden" /> {/* 员工id */}
            <Input {...init("deptId", { initValue: sessionStorage.getItem("deptId") })} htmlType="hidden" /> {/* 部门id */}
            <Input {...init("type", { initValue: 1 })} htmlType="hidden" /> {/* type=1为采购申请；type=0为材料申请 */}
            <Row>
              <Form.Item label="申请人：" {...formItemLayout}>
                <Input {...init("userName", { initValue: sessionStorage.getItem("realName") })} readOnly style={{ width: 160 }}/>
              </Form.Item>
              <Form.Item label="部门：" {...formItemLayout}>
                <Input {...init("deptName", { initValue: sessionStorage.getItem("deptName") })} readOnly="true" style={{ width: 160 }}/>
              </Form.Item>
            </Row>
            <Row justify="space-between">
              <Form.Item label="申请事由：" {...formItemLayout}>
                <Input
                  multiple
                  {...init("remarks")}
                  style={{ width: "500px" }}
                  readOnly
                />
              </Form.Item>
            </Row>
            <Row>
              <Form.Item label="附件：" {...formItemLayout}>
              {
                record.attachment ?
                    <a href={`${OAURL}/file/download?fileName=` + record.attachment}
                      className="next-form-text-align">
                      {record.fileName}
                    </a>
                  :
                  void(0)
              }
              </Form.Item>
            </Row>
          </Form>

          
          

        </IceContainer>
        <IceContainer title="申请材料列表">
          <Table
            dataSource={dataSource}
            style={{ marginTop: 10 }}
            primaryKey="id"
          >
            <Table.Column title="材料名称" dataIndex="name"  align="center" />
            <Table.Column title="规格" dataIndex="standard" align="center" />
            <Table.Column title="单位" dataIndex="unit" align="center" />
            <Table.Column title="申请数量" dataIndex="appCount"  align="center" />
          </Table>  
        </IceContainer>

        {
          this.state.storage == 1 ? (
            <IceContainer title="出库记录">
              <Table
                dataSource={this.state.dataMaterialList}
                style={{ marginTop: 5 }}
              >
                <Table.Column title="材料名称" dataIndex="materialName" align="center" />
                <Table.Column title="规格" dataIndex="standard" align="center" />
                <Table.Column title="单位" dataIndex="unit" align="center" />
                <Table.Column title="出库数量" dataIndex="num" align="center"/>
                <Table.Column title="操作人" dataIndex="createName" align="center"/>
                <Table.Column title="出库时间" dataIndex="createTime" align="center"/>
              </Table>
              <Form>
              <Form.Item
                label="备注：" 
                style={{marginTop:'20px',fontSize:'16px',fontWeight:'bold',color:'#333'}}
              >
                <Input
                  value={this.state.outStoreRemarks}
                  readOnly
                  style={{ width: 500, height: 30}} 
                />
              </Form.Item>
              </Form>
            </IceContainer>
          ):(
            ''
          )
        }
        <div align="center">
          {
            from == 'apply' ?
              <Link to={{
                  pathname: `/apply/buymaterialApply`,
                  state:{searchValue: searchValue}
              }}>
                <Button onClick={this.onClose} type="primary" className="button">
                  关闭
                </Button>
              </Link>
            :
            <Link to={{
                pathname: `/material/waitLeaveStore`,
                state:{searchValue: searchValue}
              }}>
              <Button onClick={this.onClose} type="primary" className="button">
                关闭
              </Button>
            </Link>
          }
          </div>  
      </div>
    );
  }
}
const styles = {
  buttonStyle: {
    display: "inline-block",
    marginRight: "2px"
  },
};
