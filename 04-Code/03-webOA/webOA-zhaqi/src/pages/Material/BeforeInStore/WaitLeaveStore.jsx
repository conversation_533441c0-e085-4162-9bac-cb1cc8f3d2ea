import React, { Fragment, Component } from 'react';
import { Icon, Feedback, Table, Field, Select, Form, Button, Pagination, DatePicker, Grid, moment } from '@icedesign/base';
import IceContainer from '@icedesign/container';
import IceLabel from '@icedesign/label';
import axios from 'axios';
import qs from 'qs';
import { OAURL } from '../../../components/URL/OAURL';
import { Link } from 'react-router-dom';
import ApplyProgress from './components/ApplyProgress';
import OutOfStock from './components/OutOfStock'
import InformBalloon from './components/InformBalloon'

const { RangePicker } = DatePicker;
const { Row } = Grid;
const { Combobox } = Select;

export default class WaitLeaveStore extends Component {

  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      selectedRowKeys: [],
      selectedRecord: [],
      dataLoading: true,
      dataSource: [],
      total: 0,
      pageSize: 10,
      page: 1,
      url: OAURL,
      approveRecord: [],
      loading: false,
      staffList:[],
      staffId: sessionStorage.getItem('stuffId'),
    };
    this.field = new Field(this);
  }

  // 初始化获取数据
  componentDidMount() {
    this.refreshTable();
    this.getStaffList();
  }

  // 刷新table
  refreshTable = () => {
    const { url, page, pageSize, staffId} = this.state;
    const values = this.field.getValues();
    values.page = page;
    values.pageSize = pageSize;
    values.applyState = '2';
    values.viewStaffId = staffId;
    axios({
      method: 'post',
      url: `${url}/waitLeaveStore/list`,
      data: qs.stringify(values),
    })
      .then((response) => {
        const jsondata = response.data;
        if (jsondata.statusCode == 0) {
          this.setState({
            dataSource: jsondata.list,
            dataLoading: false,
            page: page,
            pageSize: pageSize,
            total: jsondata.total,
          });
        } 
      })
      .catch((error) => {
        Feedback.toast.error(`系统繁忙，请稍后重试:${error}`);
      });
  }

  /**
    * 获取员工列表
  */
 getStaffList = () => {
  const { url } = this.state;
  axios.get(url + '/staff/fortasklist' + '?n=' + Math.random())
    .then((response) => {
      var jsondate = response.data;
      if (jsondate.statusCode == 0) {
        this.setState({
          staffList: jsondate.fortasklist,
        });
      }
      else {
        Feedback.toast.error("后台数据返回失败");
      }
    })
    .catch((error) => {
      Feedback.toast.error("系统繁忙，请稍后重试:：" + error);
    });
}

  // 发送ajax方法
  doAxiosMethod = (page, pageSize) => {
    const { url, staffId } = this.state;
    const values = this.field.getValues();
    values.page = page;
    values.pageSize = pageSize;
    values.applyState = '2';
    values.viewStaffId = staffId;
    axios({
      method: 'post',
      url: `${url}/waitLeaveStore/list`,
      data: qs.stringify(values),
    })
      .then((response) => {
        const jsondata = response.data;

        if (jsondata.statusCode == 0) {
          this.setState({
            dataSource: jsondata.list,
            dataLoading: false,
            page: page,
            pageSize: pageSize,
            total: jsondata.total,
          });
        } else {
          Feedback.toast.error('后台返回数据错误');
        }
      })
      .catch((error) => {
        Feedback.toast.error(`系统繁忙，请稍后重试:${error}`);
      });
  }

  // 查询
  doSearch = () => {
    this.setState({
      dataLoading: true,
    });
    this.doAxiosMethod(1, 10);
  };

  // 清空
  doClear = () => {
    this.field.reset();
  };

  // 翻页
  changePage = (pageNo) => {
    const { pageSize } = this.state;
    this.setState({
      dataLoading: true,
    });
    this.doAxiosMethod(pageNo, pageSize);
  };

  // 改变显示记录数
  changePageSize = (pageSize) => {
    this.setState({
      dataLoading: true,
    });
    this.doAxiosMethod(1, pageSize);
  };

  // 带分页的序号格式化
  indexFm = (value, index) => {
    const { pageSize, page } = this.state;
    if (page == 1) {
      return index + 1;
    }

    return (index + 1) + (page - 1) * pageSize;
  }

  // 操作按钮的格式化
  rowOptButton = (value, index, record) => {
    return (
      <span style={{ display: 'flex', justifyContent: 'space-around' }}>
        <Link to={{
          pathname: '/material/ApplyDetails',
          state: {storage: record.storage, opt: 'view', from: 'beforeStore', record: record, searchValue: this.field.getValues() }
        }}>
          <Icon title="查看" type="browse" style={{ color: "#3399ff", cursor: "pointer" }} size="small" />
        </Link>
        
        {
          record.storage == 0 ?
            (
              <Fragment>
                <InformBalloon record={record} refreshTable={this.refreshTable} />
                <Link to={{
                  pathname: './OutOfStock',
                  state: { record: record }
                }}>
                  <Icon title="出库" type="share" style={{ color: "#3399ff", cursor: "pointer" }} size="small" />
                </Link>
              </Fragment>
            ):(
              <Fragment>
                <Icon title="通知申请人" size="small" type="comments" style={{ color: "#BDBDBD",cursor:"not-allowed"}} />
                <Icon title="出库"  size="small"  type="share" style={{ color: "#BDBDBD", cursor: "not-allowed" }} />
              </Fragment>
            )
        }
      </span>
    );
  };

  
  // 申请状态格式化
  applyProgress = (value, index, record) => {
    return (
      <ApplyProgress record={record}/>
    )
  };

  // 查看审批进行的状态
  viewApprovalProcess = (record) => {
    const { url } = this.state;
    axios.get(`${url}/process/status`, {
      params: {
        applyId: record.id,
      },
    })
      .then((response) => {
        const jsondata = response.data;

        if (jsondata.statusCode == 0) {
          this.setState({
            approveRecord: jsondata.data,
          });
        } else {
          Feedback.toast.error('后台数据返回失败');
        }
      })
      .catch((error) => {
        Feedback.toast.error(`系统繁忙，请稍后重试：${error}`);
      });
    this.setState({
      visible: true,
    });
  }

  //时间onchang
  timeOnchange = (val, str) => {
    this.field.setValue('time',val);
    this.field.setValue("csTime", str[0]);
    this.field.setValue("ceTime", str[1]);
  }

  timeFm = (value) => {
    if (value) {
      return moment(value).format('YYYY-MM-DD');
    }
  }

  statusFm = (value) =>{
    if (value == 0) {
      return <IceLabel status="default">待出库</IceLabel>
    }else{
      return <IceLabel status="success">已出库</IceLabel>
    }
  }

  render() {
    const { init } = this.field;
    const { staffList } = this.state;
    const formItemLayout = {
      labelCol: {
        fixedSpan: 6,
      },
    };
    return (
      <div>

        <IceContainer title="搜索">
          <Form direction="hoz" field={this.field}>
            <Row justify="space-between">
              <Form.Item label="申请人：" >
                <Combobox
                  {...init('userId')}
                  placeholder="--请选择--"
                  fillProps="label"
                  hasClear
                  style={{ width: 160 }} dataSource={staffList}
                />
              </Form.Item>

              <Form.Item label="申请日期：" {...formItemLayout}>
                <RangePicker {...init('time')}
                  onChange={(val, str) => this.timeOnchange(val, str)}
                  style={{ width: 220 }} />
              </Form.Item>
              <Form.Item label="出库状态：" {...formItemLayout}>
                <Select {...init('storage')}
                  style={{ width: 160 }} 
                  dataSource={[
                    {label: '待出库', value: '0'},
                    {label: '已出库', value: '1'},
                    {label: '请选择', value: ''},
                  ]}
                />
              </Form.Item>
            </Row>
          </Form>
          <div align="center">

            <Button type="primary" onClick={this.doSearch} size="medium" className="button">
              <Icon type="search" />
              搜索
            </Button>
            <Button style={{ marginLeft: '30px' }} type="secondary" onClick={this.doClear} size="medium"
              className="button">
              <Icon type="refresh" />
              重置
            </Button>

          </div>
        </IceContainer>

        <IceContainer title="出库列表">

          <Table
            dataSource={this.state.dataSource}
            style={{ marginTop: 5 }}
            isLoading={this.state.dataLoading}
            primaryKey="id"
          >
            <Table.Column title="序号" cell={this.indexFm} width={55} align="center" />
            <Table.Column title="申请人" dataIndex="userName" align="center" />
            <Table.Column title="申请名称" dataIndex="applyTitle" align="center" />
            <Table.Column title="申请时间" dataIndex="applyTime" align="center" cell={this.timeFm} />
            <Table.Column title="出库状态" dataIndex="storage" align="center" cell={this.statusFm} />
            <Table.Column title="领用进度" dataIndex="applyState" cell={this.applyProgress} align="center" />
            <Table.Column title="操作" cell={this.rowOptButton} align="center" width={200} />
          </Table>

          <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Pagination
              pageSizeSelector="dropdown"
              onChange={this.changePage}
              onPageSizeChange={this.changePageSize}
              total={this.state.total}
              pageSize={this.state.pageSize}
              current={this.state.page}
              size="small"
              style={{ textAlign: 'right', marginTop: 15 }}
              pageSizeList={[10, 30, 50, 100]}
            />
            <div style={{ lineHeight: '53px', marginLeft: 10 }}>共 {this.state.total} 条记录</div>
          </div>

        </IceContainer>

      </div>
    );
  }
}
