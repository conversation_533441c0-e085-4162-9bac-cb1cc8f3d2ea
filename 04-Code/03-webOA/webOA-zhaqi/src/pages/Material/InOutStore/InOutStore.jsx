import React, { Component } from 'react';
import IceContainer from '@icedesign/container';
import { Icon, Table, Grid, Pagination, Input, Select, Button, DatePicker, Form, Field, Feedback, moment } from '@icedesign/base';
import axios from 'axios';
import { OAURL } from '../../../components/URL/OAURL';
import qs from 'qs';
import IceLabel from '@icedesign/label';

const { Row } = Grid;
const { RangePicker } = DatePicker;
const { Combobox } = Select;

export default class InOutStore extends Component {
  static displayName = 'InOutStore';
  static propTypes = {};
  static defalutProps = {};

  constructor(props) {
    super(props);
    this.state = {
      dataSource: [],
      dataKey: "",
      dataLoading: true,
      page: 1,
      total: 0,
      pageSize: 10,
      url: OAURL,
      staffList:[],
      deptId: sessionStorage.getItem('deptId'),
    };
    this.field = new Field(this);
  }

  componentDidMount() {
    this.refreshTable();
    this.getStaffList();
  };

  //刷新table
  refreshTable = () => {
    const { url, page, pageSize, deptId } = this.state;
    let values = this.field.getValues();
    values.page = page;
    values.pageSize = pageSize;
    values.loginUserDeptId = deptId;
    axios({
      method: 'post',
      url: url + '/materialRecord/allOwnRecord',
      data: qs.stringify(values)
    })
      .then((response) => {
        let jsondata = response.data;
        if (jsondata.statusCode == 0) {
          this.setState({
            dataSource: jsondata.list,
            total: jsondata.total,
            dataLoading: false,
          });
        }
        else {
          Feedback.toast.error('后台返回数据错误');
        }
      })
      .catch((error) => {
        Feedback.toast.error('系统繁忙，请稍后重试:' + error);
      });
  }

  /**
    * 获取员工列表
  */
  getStaffList = () => {
    const { url } = this.state;
    axios.get(url + '/staff/fortasklist' + '?n=' + Math.random())
      .then((response) => {
        var jsondate = response.data;
        if (jsondate.statusCode == 0) {
          this.setState({
            staffList: jsondate.fortasklist,
          });
        }
        else {
          Feedback.toast.error("后台数据返回失败");
        }
      })
      .catch((error) => {
        Feedback.toast.error("系统繁忙，请稍后重试:：" + error);
      });
  }

  doAxiosMethod = (page, pageSize) => {
    const { url, deptId } = this.state;
    let values = this.field.getValues();
    values.page = page;
    values.pageSize = pageSize;
    values.loginUserDeptId = deptId;
    axios({
      method: 'post',
      url: url + '/materialRecord/allOwnRecord',
      data: qs.stringify(values)
    })
      .then((response) => {
        let jsondata = response.data;
        if (jsondata.statusCode == 0) {
          this.setState({
            dataSource: jsondata.list,
            total: jsondata.total,
            dataLoading: false,
            page: page,
            pageSize: pageSize,
          });
        }
        else {
          Feedback.toast.error('后台返回数据错误');
        }
      })
      .catch((error) => {
        Feedback.toast.error('系统繁忙，请稍后重试:' + error);
      });
  }
  //查询
  doSearch = () => {
    this.setState({
      dataLoading: true,
    });
    this.doAxiosMethod(1, 10);
  };
  //重置
  doClear = () => {
    this.field.reset();
  };
  //翻页
  changePage = (pageNo) => {
    const { pageSize } = this.state;
    this.setState({
      dataLoading: true,
    });
    this.doAxiosMethod(pageNo, pageSize);
  };

  //改变显示记录
  changePageSize = (pageSize) => {
    this.setState({
      dataLoading: true,
    });
    this.doAxiosMethod(1, pageSize);
  };

  inOrOutFm = (value) => {
    if (value == 'in') {
      return <IceLabel status="success">入库</IceLabel>
    }
    else {
      return <IceLabel status="primary">出库</IceLabel>
    }
  }

  //时间onchang
  timeOnchange = (val, str) => {
    this.field.setValue("csTime", str[0]);
    this.field.setValue("ceTime", str[1]);
  } 

  timeFm = (value) => {
    if(value){
      return moment(value).format('YYYY-MM-DD');
    }
  } 

  numFm = (value, index, record) => {
    if(value){
      return value+ ' ' +record.unit;
    }
  }

  render() {
    const { init } = this.field;
    const { staffList } = this.state;
    const formItemLayout = {
      labelCol: {
        fixedSpan: 6,
      }
    };
    return (
      <div>
        <IceContainer title="搜索">
          <Form direction="hoz" field={this.field}>
            <Row justify="space-between">
              <Form.Item label="材料名称：" {...formItemLayout}>
                <Input {...init('materialName')} style={{ width: '180px' }} placeholder="请输入" />
              </Form.Item>
              <Form.Item label="申请人：" {...formItemLayout}>
                <Combobox
                  {...init('applicantId')}
                  placeholder="--请选择--"
                  fillProps="label"
                  hasClear
                  style={{ width: 180 }} dataSource={staffList}
                />
              </Form.Item>
              <Form.Item label="出入库类型：" {...formItemLayout}>
                <Select {...init('type')} style={{ width: '180px' }}
                  dataSource={[
                    { label: '出库', value: 'out' },
                    { label: '入库', value: 'in' },
                  ]}
                />
              </Form.Item>

              {/* <Form.Item label="物品类别：">
                            <Select {...init('type')}
                                style={{ width: '150px' }}
                                placeholder="请选择类别"
                                dataSource={[
                                    { label: '耗材', value: '耗材' },
                                    { label: '小型设备', value: '小型设备' },
                                    { label: '大型设备', value: '大型设备' },
                                    { label: '其他', value: '其他' },
                                ]}
                            />
                        </Form.Item> */}
            </Row>
            <Row>
              <Form.Item label="出入库日期：" {...formItemLayout}>
                <RangePicker
                  onChange={(val, str) => this.timeOnchange(val, str)}
                  style={{ width: 220 }}
                />
              </Form.Item>
            </Row>
          </Form>
          <div align="center">
            <Button type="primary" onClick={this.doSearch} size="medium" className="button">
              <Icon type="search" />
              搜索
            </Button>
            <Button style={{ marginLeft: '30px' }} type="secondary" onClick={this.doClear} size="medium" className="button">
              <Icon type="refresh" />
              重置
            </Button>
          </div>
        </IceContainer>
        <IceContainer title="出入库记录">
          <Table
            dataSource={this.state.dataSource}
            style={{ marginTop: 5 }}
            isLoading={this.state.dataLoading}
          >
            <Table.Column title="出/入库" dataIndex="type" cell={this.inOrOutFm} align="center" />
            <Table.Column title="材料名称" dataIndex="materialName" align="center" />
            <Table.Column title="规格" dataIndex="standard" align="center" />
            <Table.Column title="出/入库数量" dataIndex="num" align="center" cell={this.numFm}/>
            <Table.Column title="申请人" dataIndex="applicantName" align="center" />
            <Table.Column title="申请部门" dataIndex="deptName" align="center" />
            <Table.Column title="操作人" dataIndex="createName" align="center" />
            <Table.Column title="出/入库日期" dataIndex="createTime" align="center" cell={this.timeFm}/>
            <Table.Column title="备注" dataIndex="remarks" align="center"/>
          </Table >
          <Pagination
            pageSizeSelector="dropdown"
            onChange={this.changePage}
            onPageSizeChange={this.changePageSize}
            current={this.state.page}
            pageSize={this.state.pageSize}
            total={this.state.total}
            size="small"
            style={{ marginTop: "30PX", marginLeft: "250px", marginRight: "50px" }}

          />
        </IceContainer>

      </div>
    );
  }

}