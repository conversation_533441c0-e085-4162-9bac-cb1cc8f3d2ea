import React, { Component } from 'react';
import { Icon, Table, Field, Select, Form, Input, Button, Pagination, Feedback, moment } from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import { OAURL } from '../../../../components/URL/OAURL';
import { LoginURL } from '../../../../components/URL/LoginURL';
import qs from 'qs';
import MaterialInfo from './MaterialInfo';
import EditDialog from'./components/EditDialog'
import DeleteBalloon from './components/DeleteBalloon';

export default class MateriaTable extends Component {
  static dispalyName = "MateriaTable";
  static propTypes = {};

  static defaultProps = {};

  constructor(props) {
    super(props);
    this.state = {
      dataSource: [],
      total: 0,
      pageSize: 10,
      page: 1,
      url: OAURL, 
      loginURL: LoginURL,
      dataLoading: false,
      deptId: sessionStorage.getItem("deptId"),//部门Id
      buttons: [],
    }
    this.field = new Field(this);
  }

  //初始化获取数据
  componentDidMount() {
    this.refreshTable();
    this.getMenuByRole();
  };

  //刷新table
  refreshTable = () => {
    const { url, page, pageSize, deptId } = this.state;
    this.setState({
      dataLoading: true,
    })
    let values = this.field.getValues();
    values.page = page;
    values.pageSize = pageSize;
    values.loginUserDeptId = deptId;
    axios({
      method: 'post',
      url: `${url}/material/all`,
      data: qs.stringify(values),
    })
      .then((response) => {
        let jsondata = response.data;
        if (jsondata.statusCode == 0) {
          this.setState({
            dataSource: jsondata.list,
            total: jsondata.total,
            dataLoading: false,
          });
        }
        else {
          Feedback.toast.error('后台返回数据错误');
        }
      })
      .catch((error) => {
        Feedback.toast.error('系统繁忙，请稍后重试:' + error);
      });
  }

   //获取按钮权限
   getMenuByRole = () => {
    const {loginURL} = this.state;
    axios.get(loginURL + '/getAuthority', {
      withCredentials: true,
    })
      .then((response) => {
        let jsondata = response.data;
        if (jsondata.statusCode == 0) {
          this.setState({
            buttons: jsondata.buttons,
          });
        }
        else {
          alert(jsondata.msg);
        }
      })
      .catch((error) => {
        Feedback.toast.error('系统繁忙，请稍后重试:' + error);
      });
  }

  doAxiosMethod = (page, pageSize) => {
    const { url, deptId } = this.state;
    let values = this.field.getValues();
    values.page = page;
    values.pageSize = pageSize;
    values.loginUserDeptId = deptId;
    axios({
      method: 'post',
      url: `${url}/material/all`,
      data: qs.stringify(values),
    })
      .then((response) => {
        let jsondata = response.data;
        if (jsondata.statusCode == 0) {
          this.setState({
            dataSource: jsondata.list,
            total: jsondata.total,
            dataLoading: false,
            page: page,
            pageSize: pageSize,
          });
        }
        else {
          Feedback.toast.error('后台返回数据错误');
        }
      })
      .catch((error) => {
        Feedback.toast.error('系统繁忙，请稍后重试:' + error);
      });
  }

  //查询
  doSearch = () => {
    this.setState({
      dataLoading: true,
    });
    this.doAxiosMethod(1, 10);
  };

  //清空
  doClear = () => {
    this.field.reset();
  };

  //翻页
  changePage = (pageNo) => {
    const { pageSize } = this.state;
    this.setState({
      dataLoading: true,
    });
    this.doAxiosMethod(pageNo, pageSize);
  };

  //改变显示记录数
  changePageSize = (pageSize) => {
    this.setState({
      dataLoading: true,
    });
    this.doAxiosMethod(1, pageSize);
  }

  indexFm = (value, index) => {
    const { pageSize, page } = this.state;
    if (page == 1) {
      return index + 1;
    }
    else {
      return (index + 1) + (page - 1) * pageSize;
    }
  }

  materialTypeFm = (value) => {
    if (value == '0') {
      return "耗材";
    }
    else if (value == '1') {
      return "大型设备";
    }
    else if (value == '2') {
      return "小型设备";
    }
    else if (value == '3') {
      return "其他";
    }
  }

  // 操作按钮的格式化
  rowOptButton = (value, index, record) => {
    const { buttons } = this.state;
    let canEdit, canDelete = false;
    for (let i = 0; i < buttons.length; i++) {
      if (buttons[i].menuPath == '/material/store' && buttons[i].buttonCode == 'edit') {
        canEdit = true;
      }
       else if(buttons[i].menuPath == '/material/store' && buttons[i].buttonCode == 'delete'){
        canDelete = true;
      }
    }

    if(record.storeNum <= 0){
      return (
        <span style={{ display: 'flex', justifyContent: 'space-around' }}>
          {
            canEdit ?
              <EditDialog record={record} refreshTable={this.refreshTable} />
            :
            void(0)
          }
          {
            canDelete ?
              <DeleteBalloon id={record.id} refreshTable={this.refreshTable}/>
            :
            void(0)
          }
        </span>
      );
    }
    else{
      return(
        <span style={{ display: 'flex', justifyContent: 'space-around' }}>
        {
            canEdit ?
              <EditDialog record={record} refreshTable={this.refreshTable} />
            :
            void(0)
          }
          {
            canDelete ?
              <Icon type="ashbin" size="small"  title="库存不为0，不可删除" style={{ color: "gray"}}/>  
            :
            void(0)
          }
        </span>
      )
    }
  };

  timeFm = (value) => {
    if(value){
      return moment(value).format('YYYY-MM-DD');
    }
  }



  render() {
    const { init } = this.field;
    const formItemLayout = {
      labelCol: {
        fixedSpan: 6,
      }
    };
    return (
      <div>
        <IceContainer title="搜索">
          <Form direction="hoz" field={this.field}>
            <Form.Item label="材料名称：" {...formItemLayout}>
              <Input style={{ width: '180px' }} {...init('name')} placeholder="请输入" />
            </Form.Item>
            <Form.Item label="规格：" {...formItemLayout}>
              <Input style={{ width: '180px' }} {...init('standard')} placeholder="请输入" />
            </Form.Item>
            {/* <Form.Item label="物品类别：" {...formItemLayout}>
                    <Select {...init('type')} 
                       style={{ width: '180px'}}
                       dataSource={[
                         { label: '耗材', value:'0' },
                         { label: '小型设备', value:'1' },
                         { label: '大型设备', value:'2' },
                         { label: '其他', value:'3' },
                       ]}
                    />
                </Form.Item>                */}
          </Form>
          <div align="center">
            <Button type="primary" onClick={this.doSearch} size="medium" className="button">
              <Icon type="search" />
              搜索
                </Button>
            <Button style={{ marginLeft: '30px' }} type="secondary" onClick={this.doClear} size="medium" className="button">
              <Icon type="refresh" />
              重置
                </Button>
          </div>
        </IceContainer>
        <IceContainer title="材料库存">
          <MaterialInfo
            refreshTable={this.refreshTable}
          />
          <Table dataSource={this.state.dataSource} style={{ marginTop: 5 }}
            isLoading={this.state.dataLoading}>
            <Table.Column title="序号" cell={this.indexFm} width={60} align="center"/>
            <Table.Column title="材料名称" dataIndex="name" align="center"/>
            <Table.Column title="规格" dataIndex="standard" align="center"/>
            <Table.Column title="库存数量" dataIndex="storeNum" width={100} align="center"/>
            <Table.Column title="单位" dataIndex="unit" width={100} align="center"/>
            <Table.Column title="创建人" dataIndex="createName" align="center"/>
            {/* <Table.Column title="材料所属部门" dataIndex="deptName" /> */}
            <Table.Column title="入库时间" dataIndex="createTime" cell={this.timeFm} align="center"/>
            <Table.Column title="操作" cell={this.rowOptButton} align="center"/>
          </Table>
          <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Pagination
              pageSizeSelector="dropdown"
              onChange={this.changePage}
              onPageSizeChange={this.changePageSize}
              total={this.state.total}
              pageSize={this.state.pageSize}
              current={this.state.page}
              size="small"
              style={{ textAlign: 'right', marginTop: 15 }}
              pageSizeList={[10, 30, 50, 100]}
            />
            <div style={{ lineHeight: '53px', marginLeft: 10 }}>共 {this.state.total} 条记录</div>
          </div>
        </IceContainer>
      </div>
    )
  }

}