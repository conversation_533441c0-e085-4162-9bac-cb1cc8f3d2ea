import React, {Component,Fragment} from 'react';
import {Grid, Form, Field, Tab,  Dialog, Button, Input, Select, DatePicker, moment, Icon, Feedback, Upload  } from '@icedesign/base';
import axios from 'axios';
import { OAURL } from '../../../../components/URL/OAURL';
import '../OtherApply.css';
import qs from 'qs';
const {Row, Col} = Grid;
const Toast = Feedback.toast;
const FormItem = Form.Item;

const { RangePicker } = DatePicker;


export default class MyApplyEdit extends Component{
  constructor(props){
    super(props);
    this.state = {
      url: OAURL,
      visible: false,
      url: OAURL,   
      formData: null, 
      loading:false,
      //需要显示的文件列表
      fileList:[          
        // {name: 'AA.txt',downloadURL:'www.baidu.com'},
        // {name: 'AA.txt',downloadURL:'www.baidu.com'}
      ],
      attachmentList: [],
      fileNameList: [],

      userId: sessionStorage.getItem('stuffId'),
      userName: sessionStorage.getItem('realName'),
    };
    this.field = new Field(this,{
      autoUnmount: true
    });
  }

  //初始化获取数据
  componentWillMount(){

  }

  //打开弹窗
  onOpen = () => {
    this.setState({
      attachmentList: [],//清空文件
      fileNameList: [],//清空文件
      fileList:[], //清空文件
    },
      ()=>{
      const record = this.props.record;
      if(record.fileName){
        let fileShowName = record.fileName.split(',');
        let attachmenShow = record.attachment.split(',');

        let fileList = [];
        let attachment = [];

        for(let i=0; i<fileShowName.length; i++){
          fileList.push({
              name: fileShowName[i],
              attachment:attachmenShow[i],
              downloadURL:`${OAURL}/file/download?fileName=` + attachmenShow[i],
            },);
        }
        this.setState({
          fileList: fileList,
          attachmentList:[record.attachment],
          fileNameList:[record.fileName],
        })
      }
      const { url } = this.state;
      axios({
        method: "post",
        url: url + "/FlowInstance/findById",
        data:qs.stringify({id:record.id})
      })
        .then((response) => {

          let formData = response.data.obj;
          for(let i = 0; i < formData.length; i++){
            if(formData[i].componentType == 'select_multiple' || formData[i].componentType == 'rangePicker'){
              if(formData[i].realValue){
                //提交的值
                this.field.setValue(formData[i].propertyName, formData[i].realValue);

                //回显的值
                let array = formData[i].realValue.split(',');
                this.field.setValue(formData[i].propertyName+'hid', array);
              }
            }else{
              this.field.setValue(formData[i].propertyName, formData[i].realValue);
            }
          }
          this.setState(
            {
              formData: formData,
              visible: true,
            }
          )
          
        })
        .catch((error) => {
          Toast.error('获取失败' + error);
        });

      }
    )
    
  };

  //表单保存
  onSave = () =>{
    this.field.validate((errors, values) => {
      if(errors){
        return;
      }
      else{
        this.setState({
          loading:true
        });
        const { formData, userId, userName, url} = this.state;
        const { record } = this.props;
        let param = {};
        param.id = record.id;
        param.name = record.name;
        param.flowId = record.flowId;
        param.applicantId = userId;
        param.applicantName = userName;
        var valueList = [];
        for(let i=0; i<formData.length; i++){
          let temp = this.field.getValue(formData[i].propertyName);
          let json = {};
          json.flowInstanceId = record.id;
          json.componentId = formData[i].id;
          json.componentTitle = formData[i].title;
          json.location = formData[i].location;
          json.realValue = temp;
          valueList.push(json);
        }
        param.list = valueList;

        if(this.state.attachmentList){
          param.attachment = this.state.attachmentList.join(','); //附件编号(id)
          param.fileName = this.state.fileNameList.join(','); //附件原名
        }
        
        if(record.status == 3){
          //该申请单状态为已拒绝，将申请单id置null，点击提交的时候，先保存后提交
          param.id = null;
          param.flowName = record.flowName;
          let url = `${OAURL}/FlowInstance/addFlowInstance`;
          this.doAddOrUpdateMethod(url,param);
        }
        else{
          let url = `${OAURL}/FlowInstance/modifyById`;
          this.doAddOrUpdateMethod(url,param);
        }
      }
    })
  }

  doAddOrUpdateMethod = (url, param) => {
    axios({
      method: "post",
      url: url,
      data: param,
    })
      .then((response) => {
        this.setState({
          visible: false,
          loading: false
        });
        let jsondata = response.data;
        if(jsondata.statusCode == 0){
          Toast.success('保存成功');
          this.props.refreshTable();
        }
        else{
          Toast.error(jsondata.msg);
        }
      })
      .catch((error) => {
        this.setState({
          loading: false
        });
        Toast.error('系统繁忙，请稍后重试' + error);
      });
  }

  //表单提交
  onSubmit = () =>{
    this.field.validate((errors, values) => {
      if(errors){
        return;
      }
      else{
        this.setState({
          loading:true
        });

        const { formData, userId, userName } = this.state;
        const { record } = this.props;
        let param = {};
        param.id = record.id;
        param.name = record.name;
        param.flowId = record.flowId;
        param.applicantId = userId;
        param.applicantName = userName;
        var valueList = [];
        for(let i=0; i<formData.length; i++){
          let temp = this.field.getValue(formData[i].propertyName);
          let json = {};
          json.flowInstanceId = record.id;
          json.componentId = formData[i].id;
          json.componentTitle = formData[i].title;
          json.location = formData[i].location;
          json.realValue = temp;
          valueList.push(json);
        }
        param.list = valueList;

        if(this.state.attachmentList){
          param.attachment = this.state.attachmentList.join(','); //附件编号(id)
          param.fileName = this.state.fileNameList.join(','); //附件原名
        }

        if(record.status == 3){
          //该申请单状态为已拒绝，将申请单id置null，点击提交的时候，先保存后提交
          param.id = null;
          param.flowName = record.flowName;
          let url = `${OAURL}/FlowInstance/saveAndpostApply`;
          this.doPostMethod(param, url);
        }
        else{
          let url = `${OAURL}/FlowInstance/updateAndpostApply`;
          this.doPostMethod(param, url);
        }
      }
    })
  }

  /**
   * 提交申请: 拒绝后再次提交和修改提交
   */
  doPostMethod = (param, url) => {
    axios({
      method: "post",
      url: url,
      data: param,
    })
    .then((response) => {
      let jsondata = response.data;
      this.setState({
        visible: false,
        loading: false,
      });
      if(jsondata.statusCode == 0){
        Toast.success('提交成功');
        this.props.refreshTable();
      }
      else{
        Toast.error('提交失败：'+jsondata.msg);
      }
    })
    .catch((error) => {
      this.setState({
        loading: false,
      });
      Toast.error('获取失败' + error);
    });
  }

  onCloseDialog = () => {
    this.setState({
      visible: false,
    });
  };

  //格式化时间

  dataFm = (value) => {
    if(value){
      return moment(value).format('YYYY-MM-DD');
    }
  }


  // 渲染控件
  generate = () =>{
    const {init, getError, setError} = this.field;
    const formItemLayout = {
      labelCol : {
        fixedSpan: 10,
      },
      wrapperCol :{
        span: 16,
      }
    };
    if(this.state.formData){
      const formList = this.state.formData;
      
      return formList.map((item,index)=>{
        
        //单行输入框
        if(item.componentType == 'input_single'){
          let propertyName = item.propertyName; //init字段
          let rules; //是否必填
          if(item.isRequired == 1){
            rules = [{ required: true, message: "必填" }];
          }else{
            rules = '';
          }
          return (
            <div className="right_layout" key={index}>
              <FormItem label={item.title+'：'}  {...formItemLayout} className="formItem">
                <Input 
                  placeholder={item.placeHolder} 
                  {...init(propertyName, { rules: rules})}
                />
              </FormItem>
            </div>
            
          )
        }
  
        //多行输入框
        if(item.componentType == 'input_multiple'){
          let propertyName = item.propertyName; //init字段
          let rules; //是否必填
          if(item.isRequired == 1){
            rules = [{ required: true, message: "必填" }];
          }else{
            rules = '';
          }
          return (
            <div className="right_layout" key={index}>
              <FormItem label={item.title+'：'} {...formItemLayout} className="formItem">
                <Input 
                  placeholder={item.placeHolder} 
                  {...init(propertyName, { rules: rules})}
                  multiple
                 />
              </FormItem>
            </div>
          )
        }
  
        //单项选择
        if(item.componentType == 'select_single'){
          //解析选项
          let options = item.options.split(',');
          let dataSource = [];
          for(let i = 0; i<options.length; i++){
            dataSource.push(
              {
                label:options[i], value:options[i]
              }
            );
          }

          let propertyName = item.propertyName; //init字段
          let rules; //是否必填

          if(item.isRequired == 1){
            rules = [{ required: true, message: "必填" }];
          }else{
            rules = '';
          }


          return (
            <div className="right_layout" key={index}>
              <FormItem label={item.title+'：'} {...formItemLayout} className="formItem">
                <Select 
                  style={{width:'100%'}} 
                  placeholder={item.placeHolder} 
                  dataSource={dataSource}
                  {...init(propertyName, { rules: rules})}
                />
              </FormItem>
            </div>
            
          )
        }
        
        //多项选择
        if(item.componentType == 'select_multiple'){
          //解析选项
          let options = item.options.split(',');
          let dataSource = [];
          for(let i = 0; i<options.length; i++){
            dataSource.push(
              {
                label:options[i], value:options[i]
              }
            );
          }

          let propertyName = item.propertyName; //init字段
          let rules; //是否必填

          if(item.isRequired == 1){
            rules = [{ required: true, message: "必填" }];
          }else{
            rules = '';
          }
         
          return (
            <div className="right_layout" key={index}>
              <FormItem label={item.title+'：'} {...formItemLayout} className="formItem">
                <Select 
                  style={{width:'100%'}} 
                  placeholder={item.placeHolder}
                  dataSource={dataSource}
                  multiple
                  {...init(propertyName+'hid',
                    { rules: rules}
                  )}
                  onChange={(valueArr, options)=>{
                    if(valueArr){
                      let value = valueArr.join(',');
                      this.field.setValue(propertyName, value);
                      this.field.setValue(propertyName+'hid', valueArr);
                    }
                  }}
                />
                <span style={{ color: "red" }}>{getError(propertyName)}</span>
              </FormItem>
            </div>
            
          )
        }
  
        //时间选择
        if(item.componentType == 'datePicker'){

          let propertyName = item.propertyName; //init字段
          let rules; //是否必填

          if(item.isRequired == 1){
            rules = [{ required: true, message: "必填" }];
          }else{
            rules = '';
          }

          return (																													
            <div className="right_layout" key={index}>
              <FormItem label={item.title+'：'} {...formItemLayout} className="formItem">
                <DatePicker 
                  placeholder={item.placeHolder}
                  {...init(propertyName, {getValueFromEvent: this.dataFm, rules:rules})} 
                />
              </FormItem>           
            </div>
          )
        }
        
        //时间段选择
        if(item.componentType == 'rangePicker'){

          let propertyName = item.propertyName; //init字段
          let rules; //是否必填

          if(item.isRequired == 1){
            rules = [{ required: true, message: "必填" }];
          }else{
            rules = '';
          }
          
          

          return (
            <div className="right_layout" key={index}>
              <FormItem label={item.title+'：'} {...formItemLayout} className="formItem">
                <RangePicker 
                  {...init(propertyName+'hid',
                    { rules: rules}
                  )}
                  onChange={(date,formData)=>{
                    if(formData){
                      let value = formData.join(',');
                      this.field.setValue(propertyName, value);
                      this.field.setValue(propertyName+'hid', date);
                    }
                    
                  }}
                />
              </FormItem>
            </div>          
            
          )
        }
        
        
        
      })
    }
  }


  //附件上传成功
  uploadSuccess = (response)=>{
    let jsondata = response.data;
    if(response.code == 0){
      this.setState({
        attachmentList:[...this.state.attachmentList,jsondata.attachment],//附件名称(编号)
        fileNameList:[...this.state.fileNameList,jsondata.fileName],//附件名称(附件原名)
      })
    }
    
    Toast.success('附件上传成功');
  }
  //附件上传失败
  uploadError = (response) => {
      Toast.error(response.response.msg);
  }

  //移出文件
  onRemove = (file,filelist) =>{
    let nameList = [];
    let attachmentList = [];

    for(let i=0;i<filelist.length;i++){
      nameList.push(filelist[i].name);
      attachmentList.push(filelist[i].attachment);
    }
    this.setState({
      fileNameList:nameList,
      attachmentList:attachmentList
    })
  }

  render(){
    const { init, getError, setError} = this.field;
    const {visible} = this.state;
    const {record} = this.props;
    const formItemLayout = {
      labelCol : {
        fixedSpan: 10,
      },
      wrapperCol :{
        span: 16,
      }
    };
    const footer = (
      <div>
      <Button type="secondary" loading={this.state.loading} onClick={this.onSave}>
         保存
      </Button>
      <Button type="primary" loading={this.state.loading} onClick={this.onSubmit} style={{ marginLeft: 20}}>
         提交
      </Button>
      <Button  onClick={this.onCloseDialog} style={{ marginLeft: 20}}>
         取消
      </Button>
      </div>
    );
    return (
      <div>
        <a title="编辑" >
          <Icon 
            type="edit" 
            style={{ color: "#3399ff",cursor:"pointer" }} 
            size="small" 
            onClick={this.onOpen}
          />
        </a>
        <Dialog
          style={{ width: 800 }}
          visible={visible}
          onClose={this.onCloseDialog}
          footer={footer}
          footerAlign="center"
          title={record.name}
        >
          <Form direction="ver" field={this.field}>
            {this.generate()}
            <FormItem label="附件：" {...formItemLayout}>
              <Upload
                action={`${OAURL}/file/upload`}
                onSuccess={this.uploadSuccess}
                onError={this.uploadError}
                defaultFileList={this.state.fileList}
                onRemove={this.onRemove}
              >
                <Button type="primary" className="button">上传附件</Button>
              </Upload>
            </FormItem>
          </Form>
        </Dialog>
      </div>
        
    )
  }
}