import React, {Component,Fragment} from 'react';
import {Grid, Form, Field, Tab,  Dialog, Button, Input, Select, DatePicker, moment, Feedback, Upload } from '@icedesign/base';
import axios from 'axios';
import { OAURL } from '../../../../components/URL/OAURL';
import '../OtherApply.css';
import qs from 'qs';
import { ImageUrl } from '../../../../components/URL/ImageUrl';

const {Row, Col} = Grid;
const Toast = Feedback.toast;
const FormItem = Form.Item;

const { RangePicker } = DatePicker;


export default class ApplyDialog extends Component{
  constructor(props){
    super(props);
    this.state = {
      url: OAURL,
      visible: false,
      url: OAURL,   
      formData: null, 
      loading:false,
      attachmentList: [],
      fileNameList: [],
      userId: sessionStorage.getItem('stuffId'),
      userName: sessionStorage.getItem('realName'),
    };
    this.field = new Field(this,{
      autoUnmount: true
    });
  }

  //初始化获取数据
  componentWillMount(){
    
  }


  onOpen = () => {
    const item = this.props.item;
    const { url } = this.state;
    axios({
      method: "post",
      url: url + "/flowTemplate/findById",
      data:qs.stringify({id:item.id})
    })
      .then((response) => {
        this.setState(
          {
            formData: response.data,
          },
          ()=>{
            this.setState({
              visible: true,
              attachmentList: [],//清空文件
              fileNameList: [],//清空文件
            });
          }
        )
        
      })
      .catch((error) => {
        Toast.error('获取失败' + error);
      });

    
  };

  //表单保存
  onSave = () =>{
    this.field.validate((errors, values) => {
      if(errors){
        return;
      }
      else{
        this.setState({
          loading:true
        });
        let param = {};
        const { formData, userId, userName } = this.state;
        const formList = formData.dynamicFormList;
        if(formData){
          param.flowId = formData.id;
          param.flowName = formData.name;
          param.applicantId = userId;
          param.applicantName = userName;
          
          var valueList = [];
          for(let i=0; i<formList.length; i++){
            let temp = this.field.getValue(formList[i].propertyName);
            let json = {};
            json.componentId = formList[i].id;
            json.componentTitle = formList[i].title;
            json.location = formList[i].location;
            json.realValue = temp;
            valueList.push(json);
          }
          param.list = valueList;


          if(this.state.attachmentList){
            param.attachment = this.state.attachmentList.join(','); //附件编号(id)
            param.fileName = this.state.fileNameList.join(','); //附件原名
          }

          const {url} = this.state;
          axios({
            method: "post",
            url: url + "/FlowInstance/addFlowInstance",
            data: param,
          })
            .then((response) => {
              if(response.data.statusCode == 0){
                Toast.success('保存成功');
                this.setState({
                  visible: false,
                  loading: false
                });
              }else{
                Toast.error('保存失败:'+response.data.msg);
                this.setState({
                  loading: false,
                });
              }
              
              
            })
            .catch((error) => {
              this.setState({
                loading: false,
              });
              Toast.error('获取失败' + error);
            });

        }
      }
      
    })
  }

  //表单提交
  onSubmit = () =>{
    this.field.validate((errors, values) => {
      if(errors){
        return;
      }
      else{
        this.setState({
          loading:true
        });
        let param = {};
        const { formData, userId, userName } = this.state;
        const formList = formData.dynamicFormList;
        if(formData){
          param.flowId = formData.id;
          param.flowName = formData.name;
          param.applicantId = userId;
          param.applicantName = userName;
          
          var valueList = [];
          for(let i=0; i<formList.length; i++){
            let temp = this.field.getValue(formList[i].propertyName);
            let json = {};
            json.componentId = formList[i].id;
            json.componentTitle = formList[i].title;
            json.location = formList[i].location;
            json.realValue = temp;
            valueList.push(json);
          }
          param.list = valueList;

          if(this.state.attachmentList){
            param.attachment = this.state.attachmentList.join(','); //附件编号(id)
            param.fileName = this.state.fileNameList.join(','); //附件原名
          }

          const {url} = this.state;
          axios({
            method: "post",
            url: url + "/FlowInstance/saveAndpostApply",
            data: param,
          })
            .then((response) => {
              if(response.data.statusCode == 0){
                this.setState({
                  visible: false,
                  loading: false,
                });
                Toast.success('提交成功');
              }else{
                Toast.error('提交失败:'+response.data.msg);
                this.setState({
                  loading: false,
                });
              }
              
              
            })
            .catch((error) => {
              Toast.error('提交失败' + error);
              this.setState({
                loading: false,
              });
            });

        }
      }
      
    })
  }

  onCloseDialog = () => {
    this.setState({
      visible: false,
    });
  };

  //格式化时间

  dataFm = (value) => {
    if(value){
      return moment(value).format('YYYY-MM-DD');
    }
  }


  // 渲染控件
  generate = () =>{
    const {init, getError, setError} = this.field;
    const formItemLayout = {
      labelCol : {
        fixedSpan: 10,
      },
      wrapperCol :{
        span: 16,
      }
    };
    if(this.state.formData){
      const formList = this.state.formData.dynamicFormList;
      
      return formList.map((item,index)=>{
        
        //单行输入框
        if(item.componentType == 'input_single'){
          let propertyName = item.propertyName; //init字段
          let rules; //是否必填
          if(item.isRequired == 1){
            rules = [{ required: true, message: "必填" }];
          }else{
            rules = '';
          }
          return (
            <div className="right_layout" key={index}>
              <FormItem label={item.title+'：'}  {...formItemLayout} className="formItem">
                <Input 
                  placeholder={item.placeHolder} 
                  {...init(propertyName, { rules: rules})}
                />
              </FormItem>
            </div>
            
          )
        }
  
        //多行输入框
        if(item.componentType == 'input_multiple'){
          let propertyName = item.propertyName; //init字段
          let rules; //是否必填
          if(item.isRequired == 1){
            rules = [{ required: true, message: "必填" }];
          }else{
            rules = '';
          }
          return (
            <div className="right_layout" key={index}>
              <FormItem label={item.title+'：'} {...formItemLayout} className="formItem">
                <Input 
                  placeholder={item.placeHolder} 
                  {...init(propertyName, { rules: rules})}
                  multiple
                 />
              </FormItem>
            </div>
          )
        }
  
        //单项选择
        if(item.componentType == 'select_single'){
          //解析选项
          let options = item.options.split(',');
          let dataSource = [];
          for(let i = 0; i<options.length; i++){
            dataSource.push(
              {
                label:options[i], value:options[i]
              }
            );
          }

          let propertyName = item.propertyName; //init字段
          let rules; //是否必填

          if(item.isRequired == 1){
            rules = [{ required: true, message: "必填" }];
          }else{
            rules = '';
          }


          return (
            <div className="right_layout" key={index}>
              <FormItem label={item.title+'：'} {...formItemLayout} className="formItem">
                <Select 
                  style={{width:'100%'}} 
                  placeholder={item.placeHolder} 
                  dataSource={dataSource}
                  {...init(propertyName, { rules: rules})}
                />
              </FormItem>
            </div>
            
          )
        }
        
        //多项选择
        if(item.componentType == 'select_multiple'){
          //解析选项
          let options = item.options.split(',');
          let dataSource = [];
          for(let i = 0; i<options.length; i++){
            dataSource.push(
              {
                label:options[i], value:options[i]
              }
            );
          }

          let propertyName = item.propertyName; //init字段
          let rules; //是否必填

          if(item.isRequired == 1){
            rules = [{ required: true, message: "必填" }];
          }else{
            rules = '';
          }
         
          return (
            <div className="right_layout" key={index}>
              <FormItem label={item.title+'：'} {...formItemLayout} className="formItem">
                <Select 
                  style={{width:'100%'}} 
                  placeholder={item.placeHolder}
                  dataSource={dataSource}
                  multiple
                  {...init(propertyName+'hid',
                    { rules: rules}
                  )}
                  onChange={(valueArr, options)=>{
                      if(valueArr){
                        let value = valueArr.join(',');
                        this.field.setValue(propertyName, value);
                        this.field.setValue(propertyName+'hid', valueArr);
                      }
                  }}
                  
                />
                <span style={{ color: "red" }}>{getError(propertyName)}</span>
              </FormItem>
            </div>
            
          )
        }
  
        //时间选择
        if(item.componentType == 'datePicker'){

          let propertyName = item.propertyName; //init字段
          let rules; //是否必填

          if(item.isRequired == 1){
            rules = [{ required: true, message: "必填" }];
          }else{
            rules = '';
          }

          return (																													
            <div className="right_layout" key={index}>
              <FormItem label={item.title+'：'} {...formItemLayout} className="formItem">
                <DatePicker 
                  placeholder={item.placeHolder}
                  {...init(propertyName, {getValueFromEvent: this.dataFm, rules:rules})} 
                />
              </FormItem>           
            </div>
          )
        }
        
        //时间段选择
        if(item.componentType == 'rangePicker'){

          let propertyName = item.propertyName; //init字段
          let rules; //是否必填

          if(item.isRequired == 1){
            rules = [{ required: true, message: "必填" }];
          }else{
            rules = '';
          }
          
          

          return (
            <div className="right_layout" key={index}>
              <FormItem label={item.title+'：'} {...formItemLayout} className="formItem">
                <RangePicker 
                  {...init(propertyName+'hid',
                    { rules: rules}
                  )}
                  onChange={(date,formData)=>{
                    if(formData){
                      let value = formData.join(',');
                      this.field.setValue(propertyName, value);
                      this.field.setValue(propertyName+'hid', date);
                    }
                  }}
                />
              </FormItem>
            </div>          
            
          )
        }
        
        
        
      })
    }
  }



  // 文件上传提示
  uploadSuccess = (response)=>{
    let jsondata = response.data;
    if(response.code == 0){
      this.setState({
        attachmentList:[...this.state.attachmentList,jsondata.attachment],//附件名称(编号)
        fileNameList:[...this.state.fileNameList,jsondata.fileName],//附件名称(附件原名)
      })
    }
    
    Toast.success('附件上传成功');
  }

  uploadError = (response) => {
      Toast.error(response.response.msg);
  }



  render(){
    const { init, getError, setError} = this.field;
    const {visible} = this.state;
    const {item} = this.props;
    const formItemLayout = {
      labelCol : {
        fixedSpan: 10,
      },
      wrapperCol :{
        span: 16,
      }
    };
    const footer = (
      <div>
      <Button type="secondary" loading={this.state.loading} onClick={this.onSave}>
         保存
      </Button>
      <Button type="primary" loading={this.state.loading} onClick={this.onSubmit} style={{ marginLeft: 20}}>
         提交
      </Button>
      <Button  onClick={this.onCloseDialog} style={{ marginLeft: 20}}>
         取消
      </Button>
      </div>
    );
    return (
      <Col span="6" style={{minWidth:'200px',marginBottom:'16px'}}>
        <div className="app" onClick={this.onOpen}>
          <img src={`${ImageUrl}/approve.png`} />
          <div className="app_name">{item.name}</div>
        </div>
        <Dialog
          style={{ width: 800 }}
          visible={visible}
          onClose={this.onCloseDialog}
          footer={footer}
          footerAlign="center"
          title={item.name}
        >
          <Form direction="ver" field={this.field}>
            {this.generate()}
            <FormItem label="附件：" {...formItemLayout}>
              <Upload
                action={`${OAURL}/file/upload`}
                onSuccess={this.uploadSuccess}
                onError={this.uploadError}
              >
                <Button type="primary" className="button">上传附件</Button>
              </Upload>
            </FormItem>
          </Form>
        </Dialog>
      </Col>
    )
  }
}