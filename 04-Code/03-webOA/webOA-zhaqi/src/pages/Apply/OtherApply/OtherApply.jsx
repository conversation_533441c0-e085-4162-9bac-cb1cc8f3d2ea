import React, {Component,Fragment} from 'react';
import {Grid, Icon, Form, Field, Tab, Card, Dialog, But<PERSON>, Feedback  } from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import { OAURL } from '../../../components/URL/OAURL';
import './OtherApply.css';
import ApplyDialog from './compontents/ApplyDialog'
import MyApply from './compontents/MyApply'

const {Row, Col} = Grid;
const Toast = Feedback.toast;
const FormItem = Form.Item;

const TabPane = Tab.TabPane;





export default class OtherApply extends Component{
  constructor(props){
    super(props);
    this.state = {
      visible: false,
      url: OAURL,
      user:{
        id : sessionStorage.getItem("stuffId"),
        name : sessionStorage.getItem("realName"),
        did : sessionStorage.getItem("deptId"),
        deptName : sessionStorage.getItem("deptName"),
      },
      appList:[],
      keyValue:false,
    };
    this.field = new Field(this);
  }

  //初始化获取数据
  componentWillMount(){
    const { url } = this.state;
    axios({
      method: "get",
      url: url + "/flowTemplate/findAllNOPaging",
    })
      .then((response) => {
        this.setState(
          {
            appList:response.data
          }
        )
        
      })
      .catch((error) => {
        Toast.error('获取失败' + error);
      });

  }

  onOpen = () => {
    this.setState({
      visible: true,
    });
  };

  onCloseDialog = () => {
    this.setState({
      visible: false,
    });
  };

  refresh = ()=>{
    const { appList } = this.state;
    if(appList && appList.length > 0){
      return appList.map( (item,index)=>{
        return(
          <ApplyDialog
            item={item}
            key={index}
          />
        )
      })
    }
    else{
      return(
        <div className="noData">
          暂无自定义审批
        </div>
      )
    }    
  }

  tabClick = () =>{
    this.setState({
      keyValue: true
    },()=>{
      if(this.state.keyValue){
        this.refs.MyApply.refreshTable();
      }
    })
    
  }


  render(){
    const {init} = this.field;
    
    return (
      
      <div>
        <IceContainer title="其他申请" >
          <Tab lazyLoad={true}>
            <TabPane tab="发起申请" key="1">
              <div id="app_box">
                <Row wrap>
                  {this.refresh()}                  
                </Row>
              </div>
            </TabPane>
            <TabPane tab="我的申请" key="2" onClick={this.tabClick}>
              <MyApply ref="MyApply" />
            </TabPane>
          </Tab>
        </IceContainer>
      </div>
    )
  }
}