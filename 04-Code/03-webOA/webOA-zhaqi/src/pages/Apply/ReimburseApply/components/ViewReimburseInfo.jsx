import React, { Component } from 'react';
import { Upload, Icon, <PERSON><PERSON>back, Dialog, Button, Form, Input, Field, Grid, Radio, DatePicker, moment, NumberPicker } from '@icedesign/base';
import axios from 'axios';
import { Select } from "@icedesign/base";
import qs from 'qs';
import { OAURL } from '../../../../components/URL/OAURL';
import RelateBuyMaterial from './RelateBuyMaterial';
import RelateGoBusiness from './RelateGoBusiness'
import IceLabel from '@icedesign/label';
import './ReimburseInfo.css';
import PurchaseDetails from './PurchaseDetails';
import ViewGoBusinessInfo from '../../GoBusinessApply/component/ViewGoBusinessInfo';

const { Row, Col } = Grid;
const { Group: RadioGroup } = Radio;
const { Combobox } = Select;

export default class ReimburseInfo extends Component {
  static displayName = "ReimburseInfo";

  constructor(props) {
    super(props);
    this.state = {
      stuffId: sessionStorage.getItem("stuffId"),
      url: OAURL,
      visible: false,
      allMaterialData: [],
      storeNumVisible: "none",
      save: false,
      submit: false,
      materialApplyList:[],
      goBusinessApplyList: [],
      fileList:[]
    };
    this.field = new Field(this, { autoUnmount: true });
  }

  //初始化
  componentWillMoun(){
    const { stuffId } = this.state;
  }

  //打开编辑或者新增对话框
  onOpen = (record) => {
    if(record){
      this.field.setValues({ ...record });
      let fileList = [];
      if(record.fileName){
        fileList.push({
          name: record.fileName,
          attachment: record.attachment,
          downloadURL:`${OAURL}/file/download?fileName=`+ record.attachment,
        })
      }    
      this.setState({
        visible: true,
        fileList: fileList,
      });
    }
    else{
      this.setState({
        visible: true,
      });
    }
  };

  //关闭对话框
  onClose = () => {
    this.setState({
      visible: false,
      relatedMApplyVisible:false,
      relatedBApplyVisible: false,

    });
  };


  

  moneyTimeFm = (value, str) => {
    return str;
  }

 

  getRelateApply = (param) => {
    axios({
      method: 'post',
      url: `${OAURL}/expenseapply/relatedApply`,
      data: param,
    })
    .then( response => {
      let jsondata = response.data;
      if(jsondata.statusCode == '0'){
        if(param.type == '0'){//采购申请
          let list = [];
          jsondata.list.map((item) => {
            let label = moment(item.applyTime).format('YYYY-MM-DD')+'采购申请';
            list.push({label: label, value: item.id})
          })
          this.setState({
            materialApplyList: list,
          })
        }
        else if(param.type == '1'){//出差申请
          let temp = [];
          jsondata.list.map((item) => {
            let label = moment(item.applyTime).format('YYYY-MM-DD')+ item.businessPlace+'差旅申请';
            temp.push({label: label, value: item.bid})
          })
          this.setState({
            goBusinessApplyList: temp,
          })
        }
      }
    })
  }

  //显示文件列表
  showFileList = ()=>{
    return this.state.fileList.map( (item)=>{
      return(
        <a
          title="点击下载"
          href={item.downloadURL}
          style={styles.attachment}
          className="next-form-text-align"
          key={item.attachment}
        >                     
          {item.name}
        </a>
      )
    })
}

  /**
   * 查看申请单详情
   */
  viewRelateApply = (flag, title) => {
    let record = {};
    record.id = this.field.getValue('applicationId');
    if(flag == '0'){//领购申请详情
      return(
        <span>
          <PurchaseDetails
            record={record}
            title={title}
          />
        </span>
      )
    }
    else if(flag == '1'){//出差申请详情
      return(
        <span>
          <ViewGoBusinessInfo
            record={record}
            title={title}
          />
        </span>
      )
    }
  }


  render() {
    const init = this.field.init;
    const { record, opt } = this.props;
    const { stuffId, materialApplyList, goBusinessApplyList } = this.state;
    const formItemLayout = {
      labelCol: {
        fixedSpan: 9
      }
    };
    const formItemLayout1 = {
      labelCol: {
        fixedSpan: 6
      }
    };
    const footer = (
      <div align="center">
        <Button type="primary" onClick={this.onClose} >
          关闭
        </Button>
      </div>
    );

    return (
      <div className="reimbursdeInfo">
        <a onClick={() => this.onOpen(record)} title="编辑">
          <Icon type="browse" style={{ color: "#3399ff", cursor: "pointer" }} size="small" />
        </a>
        <Dialog
          className="dialog"
          style={{ width: 900 }}
          visible={this.state.visible}
          onClose={this.onClose}
          footer={footer}
          footerAlign="center"
          title={opt == 'edit' ? "修改报销申请" : "新增报销申请"}
          overlayProps={"name"}
          autoFocus
        >
          <Form direction="ver" field={this.field}>
            <Input {...init('userId', { initValue: stuffId })} htmlType="hidden" />
            <Input {...init('deptId', { initValue: sessionStorage.getItem("deptId") })} htmlType="hidden" />
            <Row>
              <Form.Item label="申请人：" {...formItemLayout1}>
                <Input {...init("userName", { initValue: sessionStorage.getItem("realName") })} readOnly />
              </Form.Item>
              <Form.Item label="部门：" {...formItemLayout}>
                <Input {...init("deptName", { initValue: sessionStorage.getItem("deptName") })} readOnly="true" />
              </Form.Item>
            </Row>
            {/* <Row>
              <Form.Item label="岗位：" {...formItemLayout}>
                <Input {...init("userPosition",{initValue: sessionStorage.getItem('userPosition')})} readOnly="true" />
              </Form.Item>
              <Form.Item label="工号：" {...formItemLayout}>
                <Input {...init("userWno",{initValue:sessionStorage.getItem('userJobNum')})} readOnly="true" />
              </Form.Item>
            </Row> */}
            <Row>
              <Form.Item label="报销类别：" {...formItemLayout1}>
                <Select {...init('type', {rules:[{ required: true, message: '必填'}]})}
                  dataSource={[
                    { label: '材料报销', value: '0' },
                    { label: '差旅报销', value: '1' },
                    { label: '成本报销', value: '2' },
                  ]}
                  onChange={this.selectType}
                  style={{ width: 160 }}
                  disabled
                />
              </Form.Item>
              {
                this.field.getValue('type') == '0' ?
                  <Form.Item label="关联的领购申请：" {...formItemLayout}>
                    <Input {...init("applicationId")} htmlType="hidden" />
                    <a className="next-form-text-align" style={{ cursor: "pointer", color: "green" }} > 
                      {
                        record.applicationName ?
                        this.viewRelateApply(0, record.applicationName)
                        :
                        '无'
                      }
                      </a>
                  </Form.Item>
                  :
                  void(0)
              }
              {
                this.field.getValue('type') == '1' ?
                  <Form.Item label="关联的差旅申请：" {...formItemLayout}>
                    <Input {...init("applicationId")} htmlType="hidden" />
                    <a className="next-form-text-align" style={{ cursor: "pointer", color: "green" }} 
                      onClick={() => this.viewRelateApply(1, record.applicationName)}> 
                      {
                        record.applicationName ?
                        this.viewRelateApply(1, record.applicationName)
                        :
                        '无'
                      }
                    </a>
                  </Form.Item>
                  :
                  void(0)
              }
            </Row>
            {
              this.field.getValue('type') == '1' ?
              <Row>
                <Form.Item label="是否有借款：" {...formItemLayout1}>
                <RadioGroup {...init('hasLoan')}
                  style={{ width: 160 }}
                  placeholder="请选择关联的差旅申请"
                  dataSource={[
                    {label: '没有', value: '0'},
                    {label: '有', value: '1'}
                  ]}
                  disabled
                />
                </Form.Item>
              </Row>
              :
              void(0)
            }
            <Row>
              <Form.Item label="附件：" {...formItemLayout1}>
                {this.showFileList()}
              </Form.Item>
            </Row>
            {/* <Row>
              <Form.Item label="费用发生时间：" {...formItemLayout1}>
                <DatePicker {...init('moneyTime', {
                  getValueFromEvent: this.moneyTimeFm,
                  rules: [{ required: true, message: "必填" }]
                })}
                />
              </Form.Item>
              <Form.Item label="报销金额（元）：" {...formItemLayout}>
                <Input {...init('expenseAmount', {
                  rules: [{ required: true, message: "必填" }]
                })} style={{ width: 160 }} htmlType="number" placeholder="请输入报销金额" />
              </Form.Item>
            </Row> 
            <Row justify="space-between" style={{marginTop:'25px'}}>
              <Form.Item label="申请事由：" {...formItemLayout1}>
                <Input
                  multiple
                  {...init("remarks", {
                    initValue: sessionStorage.getItem('remarks'),
                    rules: [{ required: true, message: "必填" }]
                  })}
                  style={{ width: "435px" }}
                />
              </Form.Item>
            </Row> */}
            <Row>
                <div className="zy">摘要</div>
                <div className="je">金额/元</div>
                <div className="dj">附单据数/张</div>
            </Row>
            <Row>
              <div className="zy">
                <Input {...init('digest_1')} className="input_zy" readOnly />
              </div>
              <div className="je">
                <Input {...init('money_1')} className="input_je" htmlType="number" readOnly />
              </div>
              <div className="dj">
                <Input {...init('attBilNum_1')} className="input_fd" htmlType="number" readOnly />
              </div>
            </Row>
            <Row>
              <div className="zy">
                <Input {...init('digest_2')} className="input_zy" readOnly/>
              </div>
              <div className="je">
                <Input {...init('money_2')} className="input_je" htmlType="number" readOnly/>
              </div>
              <div className="dj">
                <Input {...init('attBilNum_2')} className="input_fd" htmlType="number" readOnly/>
              </div>
            </Row>
            <Row>
              <div className="zy">
                <Input {...init('digest_3')} className="input_zy" readOnly/>
              </div>
              <div className="je">
                <Input {...init('money_3')} className="input_je" htmlType="number" readOnly/>
              </div>
              <div className="dj">
                <Input {...init('attBilNum_3')} className="input_fd" htmlType="number" readOnly/>
              </div>
            </Row>
            <Row>
              <div className="zy">
                <Input {...init('digest_4')} className="input_zy" readOnly/>
              </div>
              <div className="je">
                <Input {...init('money_4')} className="input_je" htmlType="number" readOnly/>
              </div>
              <div className="dj">
                <Input {...init('attBilNum_4')} className="input_fd" htmlType="number" readOnly/>
              </div>
            </Row>
            <Row>
              <div className="zy last">
                总计：
              </div>
              <div className="je last">
                <Input {...init('totalMoney')} className="input_je" readOnly/>
              </div>
              <div className="dj last">
                <Input {...init('totalBilNum')} className="input_fd" readOnly/>
              </div>
            </Row>
          </Form>
        </Dialog>
      </div>
    );
  }
}
const styles = {
  buttonStyle: {
    display: "inline-block",
    marginRight: "2px"
  }
};
