import React, { Component } from 'react';
import { Dialog, Icon, Feedback, Table, Field, Select, Form, Input, Button, Pagination, DatePicker, Grid, moment } from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import qs from 'qs';
import ApproveRecord from '../../../../components/ApproveRecord';
import { OAURL } from '../../../../components/URL/OAURL';
import ViewGoBusinessInfo from '../../GoBusinessApply/component/ViewGoBusinessInfo'
const { RangePicker } = DatePicker;
const { Row } = Grid;
export default class RelateGoBusiness extends Component {

  constructor(props) {
    super(props);
    // 表格可以勾选配置项
    this.rowSelection = {
      // 表格发生勾选状态变化时触发，ids可以将所有勾选的行ID获取到
      onChange: (ids, records) => {
        this.setState({
          selectedRowKeys: ids,
          selectedRecord: records,
        });
      },
      mode: "single",
    };
    this.state = {
      visible: false,
      selectedRowKeys: [],
      selectedRecord: [],
      dataLoading: true,
      dataSource: [],
      total: 0,
      pageSize: 10,
      page: 1,
      url: OAURL,
      stuffId: sessionStorage.getItem("stuffId"),
      approveRecord: [],
      loading: false,
    };
    this.field = new Field(this);
  }

  //初始化获取数据
  componentDidMount() {
    const { relateApplyId } = this.props;
    this.setState({
        selectedRowKeys: relateApplyId,
    })
  };

  onOpen = () => {
    this.setState({
      visible: true,
    })
    this.refreshTable();
  }

  //刷新table
  refreshTable = () => {
    const {url, stuffId} = this.state;
    axios.get(url + "/business/one", {
      params: {
        page: 1,
        pagesize: 10,
        n: Math.random(),
        applyState: '2',
        userId: stuffId,
      }
    })
      .then(response => {
        let jsondata = response.data;
        let apply = jsondata.businessApply;
        if (jsondata.statusCode == 0) {
          this.setState({
            dataLoading: false,
            dataSource: apply.businessApply,
            page: 1,
            pageSize: 10,
            total: apply.total,
          });
        }
        else {
          Feedback.toast.error('后台返回数据错误');
        }
      })
      .catch((error) => {
        Feedback.toast.error('系统繁忙，请稍后重试:' + error);
      });
  }

  //出差时间的格式
  timeF = (value, index, record) => {
    return record.startTime + " 至 " + record.endTime

  };

  //交通工具的格式化
  Typeformate = (value, index, record) => {
    var cars = '';
    for (var i = '0', len = record.type.length; i < len; i++) {
      let typ = record.type[i];
      if (typ == '0') {
        cars += '飞机，';
      }
      else if (typ == '1') {
        cars += '火车，';
      }
      else if (typ == '2') {
        cars += '汽车，';
      }
      else if (typ == '3') {
        cars += '其他，';
      }
    }
    return cars.substring(0, cars.length - 1);
  };

  //申请状态的格式化
  applyStateF = (value, index, record) => {
    let canshu = '4';
    if (record.applyState == '0') {
      return '已保存';
    }
    else if (record.applyState == '1') {
      return <span>进行中
            <ApproveRecord
              id={record.bid}
              type={canshu}
            />
          </span>
    }
    else if (record.applyState == '2') {
      return <span>已完成
            <ApproveRecord
              id={record.bid}
              type={canshu}
            />
          </span>
    }
    else if (record.applyState == '3') {
      return <span>已拒绝
            <ApproveRecord
              id={record.bid}
              type={canshu}
            />
          </span>
    }
  };

  //发送ajax方法
  doAxiosMethod = (page, pageSize) => {
    const {url, stuffId} = this.state;
    let searchValues = this.field.getValues();
    let applyTimeRange = searchValues.applyTime;
    if (applyTimeRange != undefined && applyTimeRange.length == 2) {
      searchValues.applyTime = applyTimeRange[0];
      searchValues.applyTime2 = applyTimeRange[1];
    }
    axios.get(url + '/business/one', {
      params: {
        userId: stuffId,
        page: page,
        pageSize: pageSize,
        applyTime: searchValues.applyTime,
        applyTime2: searchValues.applyTime2,
        applyState: '2',
      }
    })
      .then((response) => {
        let jsondata = response.data;
        if (jsondata.statusCode == 0) {
          this.setState({
            dataLoading: false,
            dataSource: jsondata.businessApply.businessApply,
            page: page,
            pageSize: pageSize,
            total: jsondata.businessApply.total,
          });
        }
        else {
          Feedback.toast.error('后台返回数据错误');
        }
      })
      .catch((error) => {
        Feedback.toast.error('系统繁忙，请稍后重试:' + error);
      });
  }

  //查询
  doSearch = () => {
    this.setState({
      dataLoading: true,
    });
    this.doAxiosMethod(1, 10);
  };

  // 清空
  doClear = () => {
    this.field.reset();
  };

  //翻页
  changePage = (pageNo) => {
    const {pageSize} = this.state;
    this.setState({
      dataLoading: true,
    });
    this.doAxiosMethod(pageNo, pageSize);
  };

  //改变显示记录
  changePageSize = (pageSize) => {
    this.setState({
      dataLoading: true,
    });
    this.doAxiosMethod(1, pageSize);
  };

  //带分页的序号格式化
  indexFm = (value, index) => {
    const {pageSize, page} = this.state;
    if (page == 1) {
      return index + 1;
    }
    else {
      return (index + 1) + (page - 1) * pageSize;
    }
  }

  //申请状态格式化
  applyStateFm = (value, index, record) => {
    if (record.applyState == 0) {
      return '已保存';
    }
    else if (record.applyState == 1) {
      return <span>进行中
        <ApproveRecord
          id={record.id}
          type={'3'}
        />
        </span>
    }
    else if (record.applyState == 2) {
      return <span>已完成
      <ApproveRecord
        id={record.id}
        type={'3'}
      />
      </span>
    }
    else if (record.applyState == 3) {
      return <span>已拒绝
      <ApproveRecord
        id={record.id}
        type={'3'}
      />
      </span>
    }
  };

  //查看审批进行的状态
  viewApprovalProcess = (record) => {
    const { url } = this.state;
    axios.get(url + '/process/status', {
      params: {
        applyId: record.id
      }
    })
      .then((response) => {
        let jsondata = response.data;

        if (jsondata.statusCode == 0) {
          this.setState({
            approveRecord: jsondata.data,
          })
        }
        else {
          Feedback.toast.error('后台数据返回失败');
        }
      })
      .catch((error) => {
        Feedback.toast.error('系统繁忙，请稍后重试：' + error);
      });
    this.setState({
      visible: true,
    });
  }

  //关闭弹窗
  onClose = () => {
    const { selectedRecord } = this.state;
    if(selectedRecord.length > 0){
      this.props.setRelateApply(selectedRecord, 1);
    }
    this.setState({visible: false,})
  }

  formateDate = (value, str) => {
    return str;
  };

  //时间onchang
  timeOnchange = (val, str) => {
    this.field.setValue("csTime", str[0]);
    this.field.setValue("ceTime", str[1]);
    this.field.setValue('createTime1',val);
  }

  applyTimeFm = (value) => {
    if(value){
      return moment(value).format('YYYY-MM-DD');
    }
  }

  operationFm = (value, index, record) =>{
    let record1 = {};
    record1.id = record.bid;
    return (
      <ViewGoBusinessInfo 
        record={record1}
        title={"查看详情"}
      />
    )
}

  render() {
    const { init } = this.field;
    const { approveRecord } = this.state;
    const formItemLayout = {
      labelCol: {
        fixedSpan: 6,
      }
    };
    const footer = (
      <div style={{marginTop: 10}}>
        <Button onClick={this.onClose} type="primary">
          确定
        </Button>
      </div>
    );

    return (
      <div>
        <Button type="primary" size="small" className="button" onClick={this.onOpen}>
          选择关联申请
        </Button>

        <Dialog
          style={{ width: 900 }}
          visible={this.state.visible}
          onClose={this.onClose}
          footer={footer}
          footerAlign="center"
          title="关联的差旅申请"
          isFullScreen
        >
          <Form direction="hoz" field={this.field}>
            <Row justify="space-around">
              <Form.Item label="申请人">
                <Input style={{ width: '150px' }} {...init('userName')} placeholder="请输入" />
              </Form.Item>
              <Form.Item label="申请时间">
                <RangePicker {...init('createTime')}
                             onChange={(val, str) => this.timeOnchange(val, str)}
                />
              </Form.Item>
            </Row>
          </Form>
          <div align="center">
            <Button type="primary" onClick={this.doSearch} size="medium" className="button">
              <Icon type="search" />
              搜索
            </Button>
            <Button style={{ marginLeft: '30px' }} type="secondary" onClick={this.doClear} size="medium" className="button">
              <Icon type="refresh" />
              重置
            </Button>
          </div>

          <div title="申请列表" style={{ marginTop: 10}}>
            <Table
              dataSource={this.state.dataSource}
              style={{marginTop: 5}}
              isLoading={this.state.dataLoading}
              rowSelection={{
                ...this.rowSelection,
                selectedRowKeys: this.state.selectedRowKeys,
              }}
              primaryKey="bid"
            >
              <Table.Column title="申请人" dataIndex="userName" align="center"/>
              <Table.Column title="出差日期" cell={this.timeF} align="center"/>
              <Table.Column title="出差地" dataIndex="businessPlace" align="center"/>
              <Table.Column title="出差人员" dataIndex="participantNames" align="center"/>
              <Table.Column title="交通工具" dataIndex="type" cell={this.Typeformate} align="center"/>
              <Table.Column title="费用申请" dataIndex="businessFee" align="center"/>
              <Table.Column title="申请时间" dataIndex="applyTime" align="center" cell={this.applyTimeFm}/>
              <Table.Column title="申请状态" dataIndex="applyState" cell={this.applyStateF} align="center"/>
              <Table.Column title="操作"  cell={this.operationFm} align="center" />
            </Table>
            <Pagination
              pageSizeSelector="dropdown"
              onChange={this.changePage}
              onPageSizeChange={this.changePageSize}
              current={this.state.page}
              pageSize={this.state.pageSize}
              total={this.state.total}
              size="small"
              style={{marginTop: "30PX", marginLeft: "250px", marginRight: "50px"}}
            />
          </div>
        </Dialog>

      </div>
    );
  }
}
