import React, { Component } from 'react';
import { Upload, Icon, <PERSON><PERSON>back, Dialog, Button, Form, Input, Field, Grid, Radio, DatePicker, moment, NumberPicker } from '@icedesign/base';
import axios from 'axios';
import { Select } from "@icedesign/base";
import qs from 'qs';
import { OAURL } from '../../../../components/URL/OAURL';
import RelateBuyMaterial from './RelateBuyMaterial';
import RelateGoBusiness from './RelateGoBusiness'
import IceLabel from '@icedesign/label';
import './ReimburseInfo.css';

const { Row, Col } = Grid;
const { Group: RadioGroup } = Radio;
const { Combobox } = Select;

export default class ReimburseInfo extends Component {
  static displayName = "ReimburseInfo";

  constructor(props) {
    super(props);
    this.state = {
      stuffId: sessionStorage.getItem("stuffId"),
      url: OAURL,
      visible: false,
      save: false,
      submit: false,
      fileList:[],
      relateApplyId:[],
    };
    this.field = new Field(this, { autoUnmount: true });
  }

  //初始化
  componentWillMoun(){
    const { stuffId } = this.state;
  }

  //打开编辑或者新增对话框
  onOpen = (record) => {
    if(record){
      this.field.setValues({ ...record });
      let fileList = [];
      if(record.fileName){
        fileList.push({
          name: record.fileName,
          attachment: record.attachment,
          downloadURL:`${OAURL}/file/download?fileName=`+ record.attachment,
        })
      }
      let relateApplyId = [];
      if(record.applicationId){
        relateApplyId.push(record.applicationId);
      }  

      this.setState({
        visible: true,
        fileList: fileList,
        relateApplyId: relateApplyId,
      });
    }
    else{
      this.setState({
        visible: true,
      });
    }
  };

  //关闭对话框
  onClose = () => {
    this.setState({
      visible: false,
    });
  };

  //是新增按钮还是编辑按钮
  editOrAdd = (record, opt) => {
    if (opt == "edit") {
      return (
        <a onClick={() => this.onOpen(record)} title="编辑">
          <Icon type="edit" style={{ color: "#3399ff", cursor: "pointer" }} size="small" />
        </a>
      );
    } else {
      return (
        <Button
          className="button"
          type="primary"
          onClick={() => this.onOpen(record)}
        >
          <Icon type="add" />
          新增
        </Button>
      );
    }
  };

  //保存
  ReimburseSave = () => {
    const { url } = this.state;
    this.field.validate((errors, values) => {
      if (errors) {
        return;
      }

      if(values.totalMoney == undefined){
        Feedback.toast.error('总金额为0，请至少填写一行');
        return;
      }
      this.setState({
        save: true,
      });
      //如果是拒绝的申请，直接修改后保存为新的申请
      if(values.applyState == '3'){
        values.id = null;
      }
      //后台保存成功，再更新table
      axios({
        method: 'post',
        url: url + '/expenseapply/addorupdate',
        data: qs.stringify(values),
      })
        .then((response) => {

          this.setState({
            save: false,
          });
          var jsondata = response.data;
          if (jsondata.statusCode == 0) {
            Feedback.toast.success(jsondata.msg);
            this.props.refreshTable();
            this.setState({
              visible: false,
            });
          }
          else {
            Feedback.toast.error(jsondata.msg);
          }
        })
        .catch((error) => {
          this.setState({
            save: false,
          });
          Feedback.toast.error("ajax请求异常 " + error)
        });
    });
  };

  //提交
  ReimburseSubmit = () => {
    const { url } = this.state;
    this.field.validate((error, values) => {
      if (error) {
       return;
      }
      if(values.totalMoney == undefined){
        Feedback.toast.error('总金额为0，请至少填写一行');
        return;
      }
      this.setState({
        submit: true,
      });
      //如果是拒绝的申请，直接修改后保存为新的申请
      if(values.applyState == '3'){
        values.id = null;
      }
      //后台保存成功，再更新table
      axios({
        method: "post",
        url: url + "/expenseapply/saveAndPost",
        data: qs.stringify(values),
      })
        .then(response => {
          this.setState({
            submit: false,
          });
          let jsondata = response.data;
          if (jsondata.statusCode == 0) {
            Feedback.toast.success('提交成功');
            this.props.refreshTable();
            this.setState({
              visible: false
            });
          } else {
            Feedback.toast.error(jsondata.msg);
          }
        })
        .catch(error => {
          this.setState({
            submit: false,
          });
          Feedback.toast.error("ajax请求异常 " + error);
        });
    });
  };

  moneyTimeFm = (value, str) => {
    return str;
  }

  uploadSuccess = (response) => {
    let jsondata = response.data;
    if (response.code == 0) {
      this.field.setValue('attachment', jsondata.attachment);
      this.field.setValue('fileName', jsondata.fileName);
      Feedback.toast.success('附件上传成功');
    }
  }

  uploadError = (response) => {
    if (response.response.code == 1) {
      Feedback.toast.error(response.response.msg);
    }
  }

  selectType = (value) => {
    if(value){
      const { stuffId } = this.state;
      let param = {};
      param.userId = stuffId;
      if(value == '0'){
        param.type = '0';
        //this.getRelateApply(param);
        this.field.setValue('type', value);
      }
      else if(value == '1'){
        param.type = '1';
        //this.getRelateApply(param);
        this.field.setValue('type', value);
      }
      else{
        this.field.setValue('type', value);
      }
    }
  }

  getRelateApply = (param) => {
    axios({
      method: 'post',
      url: `${OAURL}/expenseapply/relatedApply`,
      data: param,
    })
    .then( response => {
      let jsondata = response.data;
      if(jsondata.statusCode == '0'){
        if(param.type == '0'){//采购申请
          let list = [];
          jsondata.list.map((item) => {
            let label = moment(item.applyTime).format('YYYY-MM-DD')+'采购申请';
            list.push({label: label, value: item.id})
          })
          this.setState({
            materialApplyList: list,
          })
        }
        else if(param.type == '1'){//出差申请
          let temp = [];
          jsondata.list.map((item) => {
            let label = moment(item.applyTime).format('YYYY-MM-DD')+ item.businessPlace+'差旅申请';
            temp.push({label: label, value: item.bid})
          })
          this.setState({
            goBusinessApplyList: temp,
          })
        }
      }
    })
  }

  chooseApply = (value, extra) => {
    if(value){
      this.field.setValue('applicationId', value);
      this.field.setValue('applicationName', extra.label);
    }
  }

  //设置关联的申请
  setRelateApply = (rdata, type) =>{
    let applicationName = '';
    let applicationId = '';
    for(let i=0; i<rdata.length; i++){
      if(type == 0){
        applicationName = moment(rdata[i].applyTime).format("YYYY-MM-DD") + rdata[i].applyTitle; 
        applicationId = rdata[i].id;
      }
      else{
        applicationName = moment(rdata[i].applyTime).format("YYYY-MM-DD") + rdata[i].applyTitle +"【"+rdata[i].businessPlace+"】"; 
        applicationId = rdata[i].bid;
      }
    }
    this.field.setValue('applicationName', applicationName);
    this.field.setValue('applicationId', applicationId);
  }

  computerTotalMoney1 = (value) => {
    let money2 = this.field.getValue('money_2');
    let money3 = this.field.getValue('money_3');
    let money4 = this.field.getValue('money_4');
    let total = 0;
    if(money2){
      total = Number(total)+Number(money2);
    }
    if(money3){
      total = Number(total) + Number(money3);
    }
    if(money4){
      total = Number(total) + Number(money4);
    }
    if(value){
      total = Number(total) + Number(value);
      this.field.setValue('money_1', value);
      this.field.setValue('totalMoney', total);
    }
    else{
      this.field.reset('money_1');
      this.field.setValue('totalMoney', total);
    }
  }

  computerTotalMoney2 = (value) => {
    let money1 = this.field.getValue('money_1');
    let money3 = this.field.getValue('money_3');
    let money4 = this.field.getValue('money_4');
    let total = 0;
    if(money1){
      total = Number(total)+Number(money1);
    }
    if(money3){
      total = Number(total) + Number(money3);
    }
    if(money4){
      total = Number(total) + Number(money4);
    }
    if(value){
      total = Number(total) + Number(value);
      this.field.setValue('money_2', value);
      this.field.setValue('totalMoney', total);
    }
    else{
      this.field.reset('money_2');
      this.field.setValue('totalMoney', total);
    }
  }

  computerTotalMoney3 = (value) => {
    let money1 = this.field.getValue('money_1');
    let money2 = this.field.getValue('money_2');
    let money4 = this.field.getValue('money_4');
    let total = 0;
    if(money2){
      total = Number(total)+Number(money2);
    }
    if(money1){
      total = Number(total) + Number(money1);
    }
    if(money4){
      total = Number(total) + Number(money4);
    }
    if(value){
      total = Number(total) + Number(value);
      this.field.setValue('money_3', value);
      this.field.setValue('totalMoney', total);
    }
    else{
      this.field.reset('money_3');
      this.field.setValue('totalMoney', total);
    }
  }

  computerTotalMoney4 = (value) => {
    let money1 = this.field.getValue('money_1');
    let money2 = this.field.getValue('money_2');
    let money3 = this.field.getValue('money_3');
    let total = 0;
    if(money1){
      total = Number(total) + Number(money1);
    }
    if(money2){
      total = Number(total)+Number(money2);
    }
    if(money3){
      total = Number(total) + Number(money3);
    }
    if(value){
      total = Number(total) + Number(value);
      this.field.setValue('money_4', value);
      this.field.setValue('totalMoney', total);
    }
    else{
      this.field.reset('money_4');
      this.field.setValue('totalMoney', total);
    }
  }

  computertTotalBilNum = (value, flag) => {
    let note1 = this.field.getValue('attBilNum_1');
    let note2 = this.field.getValue('attBilNum_2');
    let note3 = this.field.getValue('attBilNum_3');
    let note4 = this.field.getValue('attBilNum_4');
    let total = 0;
    if(flag == 1){
      if(note2){
        total = Number(total)+Number(note2);
      }
      if(note3){
        total = Number(total) + Number(note3);
      }
      if(note4){
        total = Number(total) + Number(note4);
      }
    }
    else if(flag == 2){
      if(note1){
        total = Number(total) + Number(note1);
      }
      if(note3){
        total = Number(total) + Number(note3);
      }
      if(note4){
        total = Number(total) + Number(note4);
      }
    }
    else if(flag == 3){
      if(note1){
        total = Number(total) + Number(note1);
      }
      if(note2){
        total = Number(total)+Number(note2);
      }
      if(note4){
        total = Number(total) + Number(note4);
      }
    }
    else if(flag == 4){
      if(note1){
        total = Number(total) + Number(note1);
      }
      if(note2){
        total = Number(total)+Number(note2);
      }
      if(note3){
        total = Number(total) + Number(note3);
      }
    }
    if(flag == 1){
      if(value){
        total = Number(total) + Number(value);
        this.field.setValue('attBilNum_1', value);
        this.field.setValue('totalBilNum', total);
      }
      else{
        this.field.reset('attBilNum_1');
        this.field.setValue('totalBilNum', total);
      }
    }
    else if(flag == 2){
      if(value){
        total = Number(total) + Number(value);
        this.field.setValue('attBilNum_2', value);
        this.field.setValue('totalBilNum', total);
      }
      else{
        this.field.reset('attBilNum_2');
        this.field.setValue('totalBilNum', total);
      }
    }
    else if(flag == 3){
      if(value){
        total = Number(total) + Number(value);
        this.field.setValue('attBilNum_3', value);
        this.field.setValue('totalBilNum', total);
      }
      else{
        this.field.reset('attBilNum_3');
        this.field.setValue('totalBilNum', total);
      }
    }
    else if(flag == 4){
      if(value){
        total = Number(total) + Number(value);
        this.field.setValue('attBilNum_4', value);
        this.field.setValue('totalBilNum', total);
      }
      else{
        this.field.reset('attBilNum_4');
        this.field.setValue('totalBilNum', total);
      }
    }
  }

  render() {
    const init = this.field.init;
    const { record, opt } = this.props;
    const { stuffId, relateApplyId } = this.state;
    const formItemLayout = {
      labelCol: {
        fixedSpan: 9
      }
    };
    const formItemLayout1 = {
      labelCol: {
        fixedSpan: 6
      }
    };
    const footer = (
      <div align="center">
        <Button type="primary" loading={this.state.save} onClick={this.ReimburseSave} style={{ marginRight: 30 }}>
          确定
        </Button>
        <Button type="secondary" loading={this.state.submit} onClick={this.ReimburseSubmit} style={{ marginRight: 30 }}>
          提交
        </Button>
        <Button onClick={this.onClose} >
          取消
        </Button>
      </div>
    );

    return (
      <div className="reimbursdeInfo">
        {this.editOrAdd(record, opt)}
        <Dialog
          className="dialog"
          style={{ width: 900 }}
          visible={this.state.visible}
          onClose={this.onClose}
          footer={footer}
          footerAlign="center"
          title={opt == 'edit' ? "修改报销申请" : "新增报销申请"}
          overlayProps={"name"}
          autoFocus
        >
          <Form direction="ver" field={this.field}>
            <Input {...init('userId', { initValue: stuffId })} htmlType="hidden" />
            <Input {...init('deptId', { initValue: sessionStorage.getItem("deptId") })} htmlType="hidden" />
            <Row>
              <Form.Item label="申请人：" {...formItemLayout1}>
                <Input {...init("userName", { initValue: sessionStorage.getItem("realName") })} readOnly />
              </Form.Item>
              <Form.Item label="部门：" {...formItemLayout}>
                <Input {...init("deptName", { initValue: sessionStorage.getItem("deptName") })} readOnly="true" />
              </Form.Item>
            </Row>
            {/* <Row>
              <Form.Item label="岗位：" {...formItemLayout}>
                <Input {...init("userPosition",{initValue: sessionStorage.getItem('userPosition')})} readOnly="true" />
              </Form.Item>
              <Form.Item label="工号：" {...formItemLayout}>
                <Input {...init("userWno",{initValue:sessionStorage.getItem('userJobNum')})} readOnly="true" />
              </Form.Item>
            </Row> */}
            <Row>
              <Form.Item label="报销类别：" {...formItemLayout1}>
                <Select {...init('type', {rules:[{ required: true, message: '必填'}]})}
                  dataSource={[
                    { label: '材料报销', value: '0' },
                    { label: '差旅报销', value: '1' },
                    { label: '成本报销', value: '2' },
                  ]}
                  onChange={this.selectType}
                  style={{ width: 160 }}
                />
              </Form.Item>
              </Row>
              {
                this.field.getValue('type') == '0' ?
                  <Row>
                    <Form.Item label="关联的领购申请：" {...formItemLayout1}>
                      <Input {...init('applicationName')} hasClear
                        style={{ width: 300 }}
                      />
                    </Form.Item>
                    <RelateBuyMaterial
                      setRelateApply = {this.setRelateApply}
                      relateApplyId={relateApplyId}
                    />
                  </Row>
                  :
                  void(0)
              }
              {
                this.field.getValue('type') == '1' ?
                  <Row>
                    <Form.Item label="关联的差旅申请：" {...formItemLayout1}>
                      <Input {...init('applicationName')} hasClear
                        style={{ width: 300 }}
                      />
                    </Form.Item>
                    <RelateGoBusiness
                      setRelateApply = {this.setRelateApply}
                      relateApplyId={relateApplyId}
                    />
                  </Row>
                  :
                  void(0)
              }
            {
              this.field.getValue('type') == '1' ?
              <Row>
                <Form.Item label="是否有借款：" {...formItemLayout1}>
                <RadioGroup {...init('hasLoan', { rules: [{ required: true, message: '必填' }] })}
                  style={{ width: 160 }}
                  placeholder="请选择关联的差旅申请"
                  dataSource={[
                    {label: '没有', value: '0'},
                    {label: '有', value: '1'}
                  ]}
                />
                </Form.Item>
              </Row>
              :
              void(0)
            }
            <Row>
              <Form.Item label="上传附件：" {...formItemLayout1}>
                <Upload
                  action={`${OAURL}/file/upload`}
                  onSuccess={this.uploadSuccess}
                  onError={this.uploadError}
                  limit={1}
                  defaultFileList={this.state.fileList}
                >
                  <Button type="primary" className="button">
                    点击上传附件
              </Button>
                </Upload>
                <Input {...init('attachment')} htmlType="hidden" />
                <Input {...init('fileName')} htmlType="hidden" />
              </Form.Item>
            </Row>
            {/* <Row>
              <Form.Item label="费用发生时间：" {...formItemLayout1}>
                <DatePicker {...init('moneyTime', {
                  getValueFromEvent: this.moneyTimeFm,
                  rules: [{ required: true, message: "必填" }]
                })}
                />
              </Form.Item>
              <Form.Item label="报销金额（元）：" {...formItemLayout}>
                <Input {...init('expenseAmount', {
                  rules: [{ required: true, message: "必填" }]
                })} style={{ width: 160 }} htmlType="number" placeholder="请输入报销金额" />
              </Form.Item>
            </Row> 
            <Row justify="space-between" style={{marginTop:'25px'}}>
              <Form.Item label="申请事由：" {...formItemLayout1}>
                <Input
                  multiple
                  {...init("remarks", {
                    initValue: sessionStorage.getItem('remarks'),
                    rules: [{ required: true, message: "必填" }]
                  })}
                  style={{ width: "435px" }}
                />
              </Form.Item>
            </Row> */}
            <Row>
                <div className="zy">摘要</div>
                <div className="je">金额/元</div>
                <div className="dj">附单据数/张</div>
            </Row>
            <Row>
              <div className="zy">
                <Input {...init('digest_1')} className="input_zy" />
              </div>
              <div className="je">
                <Input {...init('money_1')} className="input_je" onChange={this.computerTotalMoney1} htmlType="number" />
              </div>
              <div className="dj">
                <Input {...init('attBilNum_1')} className="input_fd" onChange={(value) => this.computertTotalBilNum(value, 1)} htmlType="number" />
              </div>
            </Row>
            <Row>
              <div className="zy">
                <Input {...init('digest_2')} className="input_zy"/>
              </div>
              <div className="je">
                <Input {...init('money_2')} className="input_je" onChange={this.computerTotalMoney2} htmlType="number"/>
              </div>
              <div className="dj">
                <Input {...init('attBilNum_2')} className="input_fd" onChange={(value) => this.computertTotalBilNum(value, 2)} htmlType="number"/>
              </div>
            </Row>
            <Row>
              <div className="zy">
                <Input {...init('digest_3')} className="input_zy"/>
              </div>
              <div className="je">
                <Input {...init('money_3')} className="input_je" onChange={this.computerTotalMoney3} htmlType="number"/>
              </div>
              <div className="dj">
                <Input {...init('attBilNum_3')} className="input_fd" onChange={(value) => this.computertTotalBilNum(value, 3)} htmlType="number"/>
              </div>
            </Row>
            <Row>
              <div className="zy">
                <Input {...init('digest_4')} className="input_zy"/>
              </div>
              <div className="je">
                <Input {...init('money_4')} className="input_je" onChange={this.computerTotalMoney4} htmlType="number"/>
              </div>
              <div className="dj">
                <Input {...init('attBilNum_4')} className="input_fd" onChange={(value) => this.computertTotalBilNum(value, 4)} htmlType="number"/>
              </div>
            </Row>
            <Row>
              <div className="zy last">
                总计：
              </div>
              <div className="je last">
                <Input {...init('totalMoney')} className="input_je" readOnly/>
              </div>
              <div className="dj last">
                <Input {...init('totalBilNum')} className="input_fd" readOnly/>
              </div>
            </Row>
          </Form>
        </Dialog>
      </div>
    );
  }
}
const styles = {
  buttonStyle: {
    display: "inline-block",
    marginRight: "2px"
  }
};
