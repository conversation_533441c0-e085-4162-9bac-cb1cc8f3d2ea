import React, { Component } from 'react';
import {Upload, Icon, <PERSON><PERSON><PERSON>, <PERSON>alog, Button, Form, Input, Field, TreeSelect, Grid, Radio, DatePicker, moment, NumberPicker } from '@icedesign/base';
import axios from 'axios';
import { Select } from "@icedesign/base";
import qs from 'qs';
import { OAURL } from '../../../../components/URL/OAURL';

const { Row } = Grid;
const leaveType = [
  { label: "病假", value: '0' },
  { label: "事假", value: '1' },
  { label: "年假", value: '2' },
  { label: "产假", value: '3' },
  { label: "婚假", value: '4' },
  { label: "哺乳期", value: '5' },
  { label: "带薪休假", value: '6' },
  { label: "丧假", value: '7' },
  { label: "调休", value: '8' },
];

export default class LeaveWorkInfo extends Component {
  static displayName = "LeaveWorkInfo";

  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      storeNumVisible: "none",
      stuffId: sessionStorage.getItem("stuffId"),
      url: OAURL,
      save: false,
      submit: false,
    };
    this.field = new Field(this, { autoUnmount: true});
  }

  //打开编辑或者新增对话框
  onOpen = (record) => {
    this.field.setValues({ ...record });
    this.setState({
      visible: true,
    });
  };

  //关闭对话框
  onClose = () => {
    this.setState({
      visible: false
    });
  };

  //是新增按钮还是编辑按钮
  editOrAdd = (record, opt) => {
    if (opt == "edit") {
      return(
        <a onClick={() => this.onOpen(record)} title="编辑">
          <Icon type="edit" style={{ color: "#3399ff",cursor:"pointer"}} size="small"/>
        </a>
      );
    } 
    else {
      return (
        <Button
          type="primary"
          onClick={() => this.onOpen(record)}
          className="button"
        >
          <Icon type="add"/>
          新增
        </Button>
      );
    }
  };

  //保存
  leaveWorkSave = () => {
    const { url } = this.state;
    this.field.validate((errors, values) => {
      if (errors) {
        Feedback.toast.error("Errors in from!!!");
        return;
      }
      this.setState({
        save: true,
       });
      //后台保存成功，再更新table
      axios({
        method: 'post',
        url: url+'/leaveApply/save',
        data: qs.stringify(values),
      })
      .then((response) => {
        this.setState({
          save: false,
         });
        var jsondata = response.data;
        if (jsondata.statusCode == 0) {
            Feedback.toast.success(jsondata.message);
            this.props.refreshTable();
            this.setState({
              visible: false,
            });
        }
        else {
            Feedback.toast.error(jsondata.message);
        }
      })
      .catch((error) => {
        this.setState({
          save: false,
         });
        Feedback.toast.error("ajax请求异常 " + error)
      });
    });
  };

  //另存为
  leaveWorkSave1 = () => {
    const { url } = this.state;
    this.field.validate((errors, values) => {
      if (errors) {
        Feedback.toast.error("Errors in from!!!");
        return;
      }
      this.setState({
        save: true,
      });
      values.id = null;
      //后台保存成功，再更新table
      axios({
        method: 'post',
        url: url+'/leaveApply/save',
        data: qs.stringify(values),
      })
      .then((response) => {
        this.setState({
          save: false,
         });
        var jsondata = response.data;
        if (jsondata.statusCode == 0) {
            Feedback.toast.success(jsondata.message);
            this.props.refreshTable();
            this.setState({
              visible: false,
            });
        }
        else {
            Feedback.toast.error(jsondata.message);
        }
      })
      .catch((error) => {
        this.setState({
          save: false,
         });
        Feedback.toast.error("ajax请求异常 " + error)
      });
    });
  };

  //开始时间格式化
  stimeFm = (value, str) => {
    
    let endtime = this.field.getValue('endTime');
    if(endtime!= undefined && str != ""){
      let stime = new Date(str);
      let etime = new Date(endtime);
      let total = parseInt(etime - stime)/(3600000*24)+1;
      this.field.setValue('totalTime', total);
    }
    return str;
  }
  //结束时间格式化
  etimeFm = (value, str) => {
    
    let startime = this.field.getValue('startTime');
    if(startime != undefined && str != ""){
      let stime = new Date(startime);
      let etime = new Date(str);
      let total = parseInt(etime - stime)/(3600000*24)+1;
      this.field.setValue('totalTime', total);
    }
    return str;
  }

//提交审批
leaveWorkSubmit = () => {
  const { url } = this.state;
  this.field.validate((error, values) => {
    if (error) {
      Feedback.toast.error("Errors in from!!!");
      return;
    }
    this.setState({
      submit: true,
     });
    //后台保存成功，再更新table
    axios({
      method: "post",
      url: url + "/leaveApply/saveAndPost",
      data: qs.stringify(values),
    })
    .then(response => {
      this.setState({
        submit: false,
      });
      let jsondata = response.data;
      if (jsondata.statusCode == 0) {
        Feedback.toast.success('提交成功');
        this.props.refreshTable();
        this.setState({
          visible: false
        });
      } else {
        Feedback.toast.error(jsondata.msg);
      }
    })
    .catch(error => {
      this.setState({
        submit: false,
        });
      Feedback.toast.error("ajax请求异常 " + error);
    });
  });
};

//重新提交
leaveWorkSubmit1 = () => {
  const { url } = this.state;
  this.field.validate((error, values) => {
    if (error) {
      Feedback.toast.error("Errors in from!!!");
      return;
    }
    this.setState({
      submit: true,
     });
     values.id = null;
    //后台保存成功，再更新table
    axios({
      method: "post",
      url: url + "/leaveApply/saveAndPost",
      data: qs.stringify(values),
    })
      .then(response => {
        this.setState({
          submit: false,
         });
        let jsondata = response.data;
        if (jsondata.statusCode == 0) {
          Feedback.toast.success('提交成功');
          this.props.refreshTable();
          this.setState({
            visible: false
          });
        } else {
          Feedback.toast.error(jsondata.msg);
        }
      })
      .catch(error => {
        this.setState({
          submit: false,
         });
        Feedback.toast.error("ajax请求异常 " + error);
      });
  });
};

footerFm = (record) => {
  if(record != undefined){
    if(record.applyState == 3){
      return(
        <div align="center">
          <Button type="primary" onClick={this.leaveWorkSave1} loading={this.state.save} >
            另存为
          </Button>
          <Button type="secondary" onClick={this.leaveWorkSubmit1} loading={this.state.submit} style={{marginLeft: 20}}>
            重新提交
          </Button>
          <Button onClick={this.onClose} style={{ marginLeft: 20}}>
            取消
          </Button>
        </div>
      );
    }
    else{
      return(
        <div align="center">
          <Button type="primary" onClick={this.leaveWorkSave} loading={this.state.save}>
            确定
          </Button>
          <Button type="secondary" onClick={this.leaveWorkSubmit} loading={this.state.submit} style={{marginLeft: 20}}>
            提交
          </Button>
          <Button onClick={this.onClose} style={{ marginLeft: 20}}>
            取消
          </Button>
        </div>
      );
    }
  }
  else{
    return(
      <div align="center">
        <Button type="primary" onClick={this.leaveWorkSave} loading={this.state.save}>
          确定
        </Button>
        <Button type="secondary" onClick={this.leaveWorkSubmit} loading={this.state.submit} style={{marginLeft: 20}}>
          提交
        </Button>
        <Button onClick={this.onClose} style={{ marginLeft: 20}}>
          取消
        </Button>
      </div>
    );
  }
}

  uploadSuccess = (response) => {
    let jsondata = response.data;
    if(response.code == 0){
      this.field.setValue('attachment', jsondata.attachment);
      this.field.setValue('fileName', jsondata.fileName);
    }
    Feedback.toast.success('附件上传成功');
  }

  uploadError = (response) => {
    if (response.response.code == 1){
      Feedback.toast.error(response.response.msg);
    } 
  }

  render() {
    const init = this.field.init;
    const { record, opt } = this.props;
    const { stuffId } = this.state;
    const formItemLayout = {
      labelCol: {
        fixedSpan: 6
      }
    };
    const footer = (
      <div align="center">
        <Button type="primary" onClick={this.leaveWorkSave} loading={this.state.save} style={{marginRight: 30}}>
           确定
        </Button>
        <Button type="secondary" onClick={this.leaveWorkSubmit} loading={this.state.submit} style={{marginRight: 30}}>
           提交
        </Button>
        <Button onClick={this.onClose} style={{ marginLeft: 20}}>
           取消
        </Button>
      </div>
    );

    return (
      <div style={styles.buttonStyle}>
        {this.editOrAdd(record, opt)}
        <Dialog
          style={{ width: 700}}
          visible={this.state.visible}
          onClose={this.onClose}
          footer={this.footerFm(record)}
          title={opt== 'edit'? "修改请假申请":'新增请假申请'}
          minMargin={10}
        >
          <Form direction="ver" field={this.field}>
          <Form.Item>
          <Input {...init("id")} htmlType="hidden" /> {/* 申请单id */}
            <Input {...init("userId", { initValue: stuffId })} htmlType="hidden" /> {/* 员工id */}
            <Input {...init("deptId", { initValue: sessionStorage.getItem("deptId") })} htmlType="hidden" /> {/* 部门id */}
            <Input {...init("applyTitle")} htmlType="hidden" /> {/* 申请单name */}
            <Input {...init("createTime")} htmlType="hidden" /> {/* 创建时间 */}  
            <Input {...init("serialNum")} htmlType="hidden" /> {/* 序列号 */}   
            <Input {...init("applyState")} htmlType="hidden" /> {/* 申请状态 */}
          </Form.Item>
            <Row>
              <Form.Item label="申请人：" {...formItemLayout}>
                <Input {...init("userName", {
                  initValue: sessionStorage.getItem("realName")})}  
                  readOnly />
              </Form.Item>              
              <Form.Item label="部门：" {...formItemLayout}>
                <Input {...init("deptName", 
                { initValue: sessionStorage.getItem('deptName')})} readOnly="true" />
              </Form.Item>
            </Row>
            {/* <Row>
              <Form.Item label="岗位：" {...formItemLayout}>
                <Input {...init("userPosition",{
                  initValue: sessionStorage.getItem('userPosition')
                })} readOnly="true" />
              </Form.Item>
              <Form.Item label="工号：" {...formItemLayout}>
                <Input {...init("userJobNum",{
                  initValue: sessionStorage.getItem("userJobNum")
                })} readOnly="true" />
              </Form.Item>
            </Row> */}
            <Row>
              <Form.Item label="请假类型：" {...formItemLayout}>
              <Select
                { ...init('type', {
                  initValue:sessionStorage.getItem('type'),
                  rules: [{ required: true, message: "必填" }]
                })}
                hasClear
                dataSource={leaveType}
                style={{ width: 160}}
              />
              </Form.Item>
            </Row>
            <Row>
              <Form.Item label="请假时间：" {...formItemLayout}>
              <DatePicker 
                {...init("startTime", {
                  initValue: sessionStorage.getItem('startTime'),
                  rules: [{ required: true, message: "必填" }], getValueFromEvent: this.stimeFm
                })}
                style={{width: 160}}
                />
              </Form.Item>
              <Form.Item label="&emsp;至" >
                <DatePicker {...init('endTime', {initValue:sessionStorage.getItem('endtime'), getValueFromEvent: this.etimeFm })} 
                  style={{width: 160}}
                />
              </Form.Item>
            </Row>
            <Row>
              <Form.Item label="总时长（天）：" {...formItemLayout}>
                <Input {...init('totalTime', {initValue:sessionStorage.getItem('totalTime'), rules: [{ required: true, message: "必填"}]})} autoComplete="off"/>
              </Form.Item>
            </Row>
            <Row>
              <Form.Item label="申请事由：" {...formItemLayout}>
                <Input
                  multiple
                  {...init("remarks", {
                    initValue: sessionStorage.getItem('remarks'),
                    rules: [{ required: true, message: "必填" }]
                  })}
                  style={{ width: "500px" }}
                />
              </Form.Item>
            </Row>
            <Row>
          <Form.Item label="上传附件：" {...formItemLayout}>
              <Upload                   
                action={`${OAURL}/file/upload`}
                onSuccess={this.uploadSuccess}
                onError={this.uploadError}
                limit={1}
              >
                <Button type="primary" className="button">
                  点击上传附件
              </Button>
            </Upload>
            <Input {...init('attachment')} htmlType="hidden"/>
            <Input {...init('fileName')} htmlType="hidden"/>
          </Form.Item>
        </Row>
          </Form>
        </Dialog>
      </div>
    );
  }
}
const styles = {
  buttonStyle: {
    display: "inline-block",
    marginRight: "2px"
  }
};
