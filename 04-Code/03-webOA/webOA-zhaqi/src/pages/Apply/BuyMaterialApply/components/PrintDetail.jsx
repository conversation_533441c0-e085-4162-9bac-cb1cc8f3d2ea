import React, { Component } from 'react';
import { Step ,Timeline, Form, Input, Field, Select, Grid, DatePicker, Icon, Table,Feedback } from '@icedesign/base';
import Print from 'rc-print';
import axios from 'axios';
import { OAURL } from '../../../../components/URL/OAURL';
import IceContainer from '@icedesign/container';
import  './table.css';

const { Row, Col } = Grid;
const { Item: TimelineItem } = Timeline;

export default class PrintDetail extends Component {
  static displayName = 'PrintDetail';

  static defaultProps = {};

  constructor(props) {
    super(props);
    this.state = {
        visible: false,
        url: OAURL,
        approveRecord:[],
        dataSource:[],
    };
    this.field = new Field(this);
  }

  componentWillMount(){
    const { record } = this.props;
    this.field.setValues({...record});
    this.getApplyDetail(record.id)
    this.viewApprovalProcess();
  }

  //获取申请单详情
  getApplyDetail = (applyId) => {
    const { url } = this.state;
    axios({
      method: 'get',
      url: url+'/buyMaterial/view',
      params: {
        id: applyId
      },
    })
    .then((response) => {
      let jsondata = response.data;
      if(jsondata.statusCode == 0){
        this.field.setValues({...jsondata.obj});
        this.setState({
          record: jsondata.obj,
          dataSource: jsondata.obj.applyMaterailList,
        });
      }
    })
    .catch((error) => {
      Feedback.toast.error('系统繁忙，请稍后重试:' +error);
    });
  }

  //查看审批记录
  viewApprovalProcess = () => {
    const { url } = this.state;
    const { record } = this.props;
    axios.get(url+'/process/status',{
      params:{
        applyId: record.id,
        type: '3',
      }
    })
    .then((response) => {
      let jsondata = response.data;
      if(jsondata.statusCode == 0 ){
        
          this.setState({
            approveRecord:jsondata.data,
          })
      }
    })
    .catch((error) => {
      Feedback.toast.error('系统繁忙，请稍后重试：'+error);
    })
  }

  render() {
    const init = this.field.init;
    const { approveRecord, dataSource } = this.state;
    
    const formItemLayout = {
      labelCol : {
        fixedSpan: 6,
      },
      wrapperCol :{
        span: 14,
      }
    };
    
    const otherStyle = `
      @media print{
          .black{
            font-size:10px;
          }
      }
  `;
    return (
    <div style={styles.buttonStyle} >
        <a onClick={ () => this.refs.test.onPrint()} title="打印" >
          <Icon type="form" style={{ color: "#3399ff",cursor:"pointer"}} size="small"/>
        </a>
        <Print
          ref="test" insertHead={true}  otherStyle={otherStyle} lazyRender	
        >
          <div>
          <h2 align="center" style={{marginTop: 20, marginBottom: 20, fontWeight: 600}}>材料领购申请单</h2>
          <Form direction="ver" field={this.field}  className="tableType1">
            <Row>
              <Form.Item label="申请人：" {...formItemLayout}>
                <Input {...init("userName", { initValue: sessionStorage.getItem("realName") })} readOnly style={{ width: 160 }}/>
              </Form.Item>
              <Form.Item label="部门：" {...formItemLayout}>
                <Input {...init("deptName", { initValue: sessionStorage.getItem("deptName") })} readOnly="true" style={{ width: 160 }}/>
              </Form.Item>
            </Row>
            <Row justify="space-between">
              <Form.Item label="申请事由：" {...formItemLayout}>
                <Input
                  {...init("remarks")}
                  style={{ width: "450px", height: 50 }}
                  readOnly
                />
              </Form.Item>
            </Row>
          </Form>
          <IceContainer title="领购材料列表">
            <div className="tableType">
            <Table
              dataSource={dataSource}
              primaryKey="id"
            >
              <Table.Column title="材料名称" dataIndex="name"  align="center" />
              <Table.Column title="规格" dataIndex="standard" align="center" />
              <Table.Column title="单位" dataIndex="unit" align="center" />
              <Table.Column title="申请数量" dataIndex="appCount"  align="center" />
            </Table>  
            </div>
          </IceContainer>
          <IceContainer title="审批记录">
          <div className="tableType">
          <Table 
            dataSource={approveRecord} 
          >
            <Table.Column title="标题" dataIndex="title" />     
            <Table.Column title="操作人" dataIndex="content" width={100}/>
            <Table.Column title="操作时间" dataIndex="time" width={170}/>
            <Table.Column title="审批意见" dataIndex="comment" />
            <Table.Column title="审批结果" dataIndex="result"  width={100}/>
          </Table>
          </div>
          </IceContainer>
          </div>
        </Print>
      </div>
    );
  }
}

const styles = {
  buttonStyle:{ 
      display: 'inline-block!important', 
      marginRight: '20px',
  },
  
};