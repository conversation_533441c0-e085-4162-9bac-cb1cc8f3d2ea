import React, { Component } from 'react';
import {Upload, Icon,<PERSON><PERSON>back, Dialog, Button, Form, Input, Field, TreeSelect, Grid, Radio, DatePicker, moment, NumberPicker } from '@icedesign/base';
import axios from 'axios';
import qs from 'qs';
import { OAURL } from '../../../../components/URL/OAURL';

const { Row, Col } = Grid;

export default class LoanApplyInfo extends Component {
  static displayName = "LoanApplyInfo";

  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      stuffId: sessionStorage.getItem("stuffId"),
      url: OAURL,
      save:false,
      submit:false,
    };
    this.field = new Field(this, { autoUnmount: true});
  }

  //打开编辑或者新增对话框
  onOpen = ( record ) => {
    this.field.setValues({ ...record });
    this.setState({
      visible: true,
    });
  };

  //关闭对话框
  onClose = () => {
    this.setState({
      visible: false
    });
  };

  //是新增按钮还是编辑按钮
  editOrAdd = (record, opt) => {
    if (opt == "edit") {
      return(
        <a onClick={() => this.onOpen(record)} title="编辑">
          <Icon type="edit" style={{ color: "#3399ff",cursor:"pointer"}} size="small"/>
        </a>
      );
    } else {
      return (
        <Button
          className="button"
          type="primary"
          onClick={() => this.onOpen(record)}
        >
          <Icon type="add"/>
          新增
        </Button>
      );
    }
  };

  //新增和修改保存
  loanSave = () => {
    
    const {url} = this.state;
    this.field.validate((errors, values) => {
      
      if (errors) {
        Feedback.toast.error("表单验证失败！");
        return;
      }
      this.setState({
        save:true,
      })
      //后台保存成功，再更新table
      axios({
        method: "post",
        url: url+'/loanApply/addOrUpdate',
        data: qs.stringify(values),        
      })
      .then(response => {
        this.setState({
          save:false,
        })
        
        var jsondata = response.data;
        if (jsondata.statusCode == 0) {
          
          Feedback.toast.success(jsondata.msg);
          this.props.refreshTable();
          this.setState({
            visible: false
          });
        } else {
          Feedback.toast.error(jsondata.msg);
        }
      })
      .catch(error => {
        this.setState({
          save:false,
        })
        Feedback.toast.error("ajax请求异常 " + error);
      });
    });
  };

    //新增和修改提交
    loanSubmit = () => {
      const { url } = this.state;
      this.field.validate((errors, values) => {
        if (errors) {
          Feedback.toast.error("表单验证失败！");
          return;
        }
        this.setState({
          submit:true,
        })
        //后台保存成功，再更新table
        axios({
          method: "post",
          url: url+'/loanApply/saveAndPost',
          data: qs.stringify(values),        
        })
        .then(response => {
          this.setState({
            submit:false,
          })
          var jsondata = response.data;
          if (jsondata.statusCode == 0) {
            Feedback.toast.success('提交成功');
            this.props.refreshTable();
            this.setState({
              visible: false
            });
          } else {
            Feedback.toast.error('提交失败');
          }
        })
        .catch(error => {
          this.setState({
            submit:false,
          })
          Feedback.toast.error("ajax请求异常 " + error);
        });
      });
    };

    //另存为
  loanSave1 = () => {
    const {url} = this.state;
    this.field.validate((errors, values) => {
      if (errors) {
        Feedback.toast.error("表单验证失败！");
        return;
      }
      this.setState({
        save:true,
      })
      values.id = null;
      //后台保存成功，再更新table
      axios({
        method: "post",
        url: url+'/loanApply/addOrUpdate',
        data: qs.stringify(values),        
      })
      .then(response => {
        this.setState({
          save:false,
        })
        
        var jsondata = response.data;
        if (jsondata.statusCode == 0) {
          
          Feedback.toast.success(jsondata.msg);
          this.props.refreshTable();
          this.setState({
            visible: false
          });
        } else {
          Feedback.toast.error(jsondata.msg);
        }
      })
      .catch(error => {
        this.setState({
          save:false,
        })
        Feedback.toast.error("ajax请求异常 " + error);
      });
    });
  };

  //重新提交
  loanSubmit1 = () => {
    const { url } = this.state;
    this.field.validate((errors, values) => {
      if (errors) {
        Feedback.toast.error("表单验证失败！");
        return;
      }
      this.setState({
        submit:true,
      })
      values.id = null;
      //后台保存成功，再更新table
      axios({
        method: "post",
        url: url+'/loanApply/saveAndPost',
        data: qs.stringify(values),        
      })
      .then(response => {
        this.setState({
          submit:false,
        })
        var jsondata = response.data;
        if (jsondata.statusCode == 0) {
          Feedback.toast.success('提交成功');
          this.props.refreshTable();
          this.setState({
            visible: false
          });
        } else {
          Feedback.toast.error('提交失败');
        }
      })
      .catch(error => {
        this.setState({
          submit:false,
        })
        Feedback.toast.error("ajax请求异常 " + error);
      });
    });
  };

  footerFm = (record) => {
    if(record != undefined ){
      if(record.applyState == 3){
        return(
          <div>
            <Button type="primary" loading={this.state.save} onClick={this.loanSave1}>
                另存为
            </Button>
            <Button type="secondary" loading={this.state.submit} onClick={this.loanSubmit1} style={{ marginLeft: 20}}>
                重新提交
            </Button>
            <Button onClick={this.onClose} style={{ marginLeft: 20}}>
                取消
            </Button>
          </div>
        );
      }
      else{
        return(
          <div>
            <Button type="primary" loading={this.state.save} onClick={this.loanSave}>
                保存
            </Button>
            <Button type="secondary" loading={this.state.submit} onClick={this.loanSubmit} style={{ marginLeft: 20}}>
                提交
            </Button>
            <Button onClick={this.onClose} style={{ marginLeft: 20}}>
                取消
            </Button>
          </div>
        );
      }
    }
    else{
      return(
        <div>
          <Button type="primary" loading={this.state.save} onClick={this.loanSave}>
              保存
          </Button>
          <Button type="secondary" loading={this.state.submit} onClick={this.loanSubmit} style={{ marginLeft: 20}}>
              提交
          </Button>
          <Button onClick={this.onClose} style={{ marginLeft: 20}}>
              取消
          </Button>
        </div>
      );
    }
  }

  uploadSuccess = (response) => {
    let jsondata = response.data;
    if(response.code == 0){
      this.field.setValue('attachment', jsondata.attachment);
      this.field.setValue('fileName', jsondata.fileName);
    }
    Feedback.toast.success('附件上传成功');
  }

  uploadError = (response) => {
    if (response.response.code == 1){
      Feedback.toast.error(response.response.msg);
    } 
  }

  render() {
    const init = this.field.init;
    //id为申请单id，auditId为审批id，opt为操作类型，取值有view和flow
    const { record, opt } = this.props;
    const { stuffId } = this.state;
    const formItemLayout = {
      labelCol: {
        fixedSpan: 7
      },
    };
    const footer = (
      <div style={{ marginTop: 20}} align="center">
        <Button type="primary" loading={this.state.save} onClick={this.loanSave}>
           保存
        </Button>
        <Button type="primary" loading={this.state.submit} onClick={this.loanSubmit} style={{ marginLeft: 20}}>
           提交
        </Button>
        <Button onClick={this.onClose} style={{ marginLeft: 20}}>
           取消
        </Button>
      </div>
    );

    return (
      <div style={styles.buttonStyle}>
        {this.editOrAdd(record, opt)}
        <Dialog
          style={{ width: 650}}
          visible={this.state.visible}
          onClose={this.onClose}
          footer={this.footerFm(record)}
          footerAlign="center"
          title={opt == 'edit'?"修改借款申请":"新增借款申请"}
        >
          <Form direction="ver" field={this.field}>
            <Input {...init("id")} htmlType="hidden" /> {/* 申请单id */}
            <Input {...init("userId", { initValue: stuffId })} htmlType="hidden" /> {/* 员工id */}
            <Input {...init("deptId", { initValue: sessionStorage.getItem("deptId") })} htmlType="hidden" /> {/* 部门id */}
            <Input {...init("applyTitle")} htmlType="hidden" /> {/* 申请单name */}
            <Input {...init("createTime")} htmlType="hidden" /> {/* 创建时间 */}  
            <Input {...init("serialNum")} htmlType="hidden" /> {/* 序列号 */}   
            <Input {...init("applyState")} htmlType="hidden" /> {/* 申请状态 */} 
            <Row>
              <Form.Item label="申请人：" {...formItemLayout}>
                <Input {...init("userName", {
                  initValue: sessionStorage.getItem("realName") })} 
                  readOnly 
                  />
              </Form.Item>              
              <Form.Item label="部门：" {...formItemLayout}>
                <Input {...init("deptName", { 
                initValue: sessionStorage.getItem("deptName") })} 
                readOnly="true" 
                />
                <Input {...init("deptId", 
                { initValue: sessionStorage.getItem("deptId") })} 
                style={{ display: "none" }} 
                />
              </Form.Item>
            </Row>
            {/* <Row>
              <Form.Item label="岗位：" {...formItemLayout}>
                <Input {...init("userPosition", 
                { initValue: sessionStorage.getItem("userPosition") })} 
                readOnly="true" 
                />
              </Form.Item>
              <Form.Item label="工号：" {...formItemLayout}>
                <Input {...init("userJobNum", 
                { initValue: sessionStorage.getItem("userJobNum") })} 
                readOnly="true" 
                />
              </Form.Item>
            </Row> */}
            <Row>
              <Form.Item label="借款金额（元）：" {...formItemLayout}>
                <Input {...init('loanAmount', 
                { rules: [{ required: true, message: "必填"}]})} 
                autoComplete="off"
                htmlType="number"
                />
              </Form.Item>
            </Row>
            <Row>
              <Form.Item label="借款事由：" {...formItemLayout}>
                <Input
                  multiple
                  {...init("remarks", {
                    rules: [{ required: true, message: "必填" }]
                  })}
                  style={{ width: "450px" }}
                />
              </Form.Item>
            </Row>
            <Row>
              <Form.Item label="上传附件：" {...formItemLayout}>
                  <Upload                   
                    action={`${OAURL}/file/upload`}
                    onSuccess={this.uploadSuccess}
                    onError={this.uploadError}
                    limit={1}
                  >
                    <Button type="primary" className="button">
                      点击上传附件
                  </Button>
                </Upload>
                <Input {...init('attachment')} htmlType="hidden"/>
                <Input {...init('fileName')} htmlType="hidden"/>
              </Form.Item>
            </Row>
          </Form>
        </Dialog>
      </div>
    );
  }
}
const styles = {
  buttonStyle: {
    display: "inline-block",
    marginRight: "2px"
  }
};
