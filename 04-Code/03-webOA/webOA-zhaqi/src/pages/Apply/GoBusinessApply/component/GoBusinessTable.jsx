import React, { Component } from 'react';
import IceContainer from '@icedesign/container';
import { Icon, Feedback, Table, Field, Pagination, Button, Form, Select, Grid, DatePicker, Input } from '@icedesign/base';
import DeleteBalloon from './DeleteBalloon';
import axios from 'axios';
import qs from 'qs';
import GoBusinessInfo from './GoBusinessInfo';
import ViewGoBusinessInfo from './ViewGoBusinessInfo';
import ApproveRecord from '../../../../components/ApproveRecord';
import PrintDetail from './PrintDetail';
import { OAURL } from '../../../../components/URL/OAURL';

const { RangePicker } = DatePicker;
const { Row } = Grid;
export default class GoBusinessTable extends Component {
    static displayName = 'GoBusinessTable';
    static propTypes = {};
    static defalutProps = {};
    constructor(props) {
        super(props);
        //表格可以勾选配置项
        this.rowSelection = {
            //表格发生勾选状态变化时触发，ids可以将需哦有勾选的行ID获取到
            onChange: (ids, records) => {
                this.setState({
                    selectedRowKeys: ids,
                    selectRowRecord: records
                });
            },
            //支持针对特殊行进行定制
            getProps: (record) => {
                return {
                    disabled: record.applyState != 0,
                };
            },
            mode:"single"
        };
        this.state = {
            selectedRowKeys: [],
            dataLoading: false,
            dataSource: [],
            approveRecord:[],
            total: 0,
            page: 1,
            pageSize: 10,
            url: OAURL,
            stuffId: sessionStorage.getItem("stuffId"),
            loading:false,
        };
        this.field = new Field(this);
    }
    // 初始化获取数据
    componentDidMount() {
      this.refreshTable();
    }

    //刷新table
    refreshTable = () =>{
    const {url, stuffId} = this.state;
    axios.get(url+"/business/one",{
        params: {
          userId: stuffId,
          page:1,
          pagesize:10,
          n: Math.random(),
        }})
        .then(response => {
            let jsondata = response.data;
            let apply = jsondata.businessApply;
            if (jsondata.statusCode == 0) {
              this.setState({
                dataLoading: false,
                dataSource: apply.businessApply,
                page: 1,
                pageSize:10,
                total: apply.total,
              });
            }
            else {
              Feedback.toast.error('后台返回数据错误');
            }
          })
          .catch((error) => {
            Feedback.toast.error('系统繁忙，请稍后重试:' + error);
          });
      };

    //出差时间的格式
    timeF = (value, index, record) => {
        return record.startTime + " 至 " + record.endTime

    };

    //交通工具的格式化
    Typeformate = (value, index, record) => {
        var cars = '';
        for (var i = '0', len = record.type.length; i < len; i++) {
            let typ = record.type[i];
            if (typ == '0') {
                cars += '飞机，';
            }
            else if (typ == '1') {
                cars += '火车，';
            }
            else if (typ == '2') {
                cars += '汽车，';
            }
            else if (typ == '3') {
                cars += '其他，';
            }
        }
        return cars.substring(0, cars.length - 1);
    };

    //申请状态的格式化
    applyStateF = (value, index, record) => {        
        let canshu='4';
        if (record.applyState == '0') {
            return '已保存';
        }
        else if (record.applyState == '1') {         
            return <span style={{ color: '#ff9900'}}>进行中 
            <ApproveRecord 
              id={record.bid}
              type={canshu}
            />
          </span>
        }
        else if (record.applyState == '2') {
            return <span style={{ color: '#0000EE'}}>已完成
            <ApproveRecord 
              id={record.bid}
              type={canshu}
            />
          </span>
        }
        else if (record.applyState == '3') {
            return <span style={{color: 'red'}}>已拒绝
            <ApproveRecord 
              id={record.bid}
              type={canshu}
            />
          </span>
        }
    };

 //发送ajax方法
 doAxiosMethod = (page, pageSize) => {
    const { url, stuffId } = this.state;
    let searchValues = this.field.getValues();
    let applyTimeRange = searchValues.applyTime;
    if(applyTimeRange != undefined && applyTimeRange.length == 2){
      searchValues.applyTime = applyTimeRange[0];
      searchValues.applyTime2 = applyTimeRange[1];
    }
    axios.get(url+'/business/one',{
    params:{
        userId: stuffId,
        page: page,
        pageSize: pageSize,
        applyTime: searchValues.applyTime,
        applyTime2: searchValues.applyTime2,
        applyState:searchValues.applyState
    }} )
    .then((response) => {
      let jsondata = response.data;
        if (jsondata.statusCode == 0) {
            this.setState({
                dataLoading: false,
                dataSource: jsondata.businessApply.businessApply,
                page: page,
                pageSize:pageSize,
                total: jsondata.businessApply.total,
             });
         }
      else{
        Feedback.toast.error('后台返回数据错误');
      }
      })
      .catch((error) => {
        Feedback.toast.error('系统繁忙，请稍后重试:' +error);
      });
   }

    //提交审批
    submitBApplies = () => {
        this.setState({
            loading:true,
        });
        let { selectRowRecord, url } = this.state;            
        axios({
            method: 'post',
            url: url+'/business/post',
            data: qs.stringify(selectRowRecord[0]),
          })
          .then((response) => {  
            this.setState({
                loading:false,
            });
            let jsondata = response.data;
            if (jsondata.statusCode == 0) {
              this.refreshTable();
              Feedback.toast.success("提交成功")
            }
            else {
              Feedback.toast.error("提交失败")
            }
          })
          .catch((error) => {
            this.setState({
                loading:false,
            });
            Feedback.toast.error("ajax请求异常 " + error)
          });
        }

    //查询
    doSearch = () => {
        this.setState({
            dataLoading: true,
        });
     this.doAxiosMethod(1,10);
    };

    // 清空
    doClear = () => {
        this.field.reset();
    };
    //翻页
    changePage = (pageNo) => {
        const { pageSize } = this.state;
        this.setState({
            dataLoading: true,
        });
        this.doAxiosMethod(pageNo,pageSize);
    };
    //改变显示记录
    changePageSize = (pageSize) => {
        this.setState({
            dataLoading: true,
        });
        this.doAxiosMethod(1, pageSize);
    };

//操作按钮格式化
    rowButton = (value, index, record) => {
        if(record.applyState==0 || record.applyState == 3){      
        return (
            <span style={{display: "flex", justifyContent:"space-around" }}>
               <ViewGoBusinessInfo
                    record={record}
                    type1={record.type}
                    opt='view'
                />
                <GoBusinessInfo
                    record={record}
                    type1={record.type}
                    opt='edit'
                    refreshTable={this.refreshTable}
                />
                <DeleteBalloon
                    id={record.bid}
                    refreshTable={this.refreshTable}
                />
                <PrintDetail
                    record={record}
                />
            </span>
        );
    }  else{
        return (
            <span style={{display: "flex", justifyContent:"space-around" }}>
               <ViewGoBusinessInfo
                    record={record}
                    type1={record.type}
                    opt='view'
                />
               <a title="不可编辑">
                <Icon type="survey" style={{ cursor:"pointer",color: "#BDBDBD"}} size="small"/>
               </a>
               <a title="不可删除">
                    <Icon type="ashbin" style={{ cursor:"pointer",color: "#BDBDBD"}} size="small"/>
                </a>
                {/* <DeleteBalloon
                    id={record.bid}
                    refreshTable={this.refreshTable}
                /> */}
                <PrintDetail
                    record={record}
                />
            </span>
        );
    }
    };
 
//时间格式化
    Fm=(r, t)=>{
        return t;
    }

     //带分页的序号格式化
  indexFm = (value, index) => {
    const { pageSize, page } = this.state;
    if(page == 1){
      return index + 1;
    }
    else {
      return (index+1)+ (page-1)*pageSize;  
    }  
  }

render() {
    const { init } = this.field;
    const { approveRecord } = this.state;
    const formItemLayout = {
        labelCol : {
        fixedSpan: 6,
        }
    };
    return (
        <div>
            <IceContainer title="搜索">
                <Form direction="hoz" field={this.field}>
                    <Row justify="space-around">
                        <Form.Item label="申请状态：" {...formItemLayout}>
                            <Select {...init('applyState')}
                                style={{ width: '180px' }}
                                placeholder="请选择状态"
                                dataSource={[
                                    {
                                        label: '已保存', value: '0'
                                    },
                                    {
                                        label: '进行中', value: '1'
                                    },
                                    {
                                        label: '已完成', value: '2'
                                    },
                                    {
                                        label: '已拒绝', value: '3'
                                    },
                                ]}
                            />
                        </Form.Item>
                        <Form.Item label="申请时间："{...formItemLayout}>
                            <RangePicker {...init('applyTime',{getValueFromEvent: this.Fm})} style={{ width: '220px' }}/>
                        </Form.Item>
                    </Row>
                    <div align="center">
                        <Button type="primary" onClick={this.doSearch} size="medium" className="button">
                            <Icon type="search" />
                            搜索
                        </Button>
                        <Button style={{ marginLeft: '30px' }} type="secondary" onClick={this.doClear} size="medium" className="button">
                            <Icon type="refresh" />
                            重置
                        </Button>
                    </div>
                </Form>
            </IceContainer>
            <IceContainer title="申请列表"> 
                <div style={{ marginBottom: 5 }}>
                    <GoBusinessInfo
                        refreshTable={this.refreshTable}
                    />
                    <Button type="primary"
                        onClick={this.submitBApplies}
                        loading={this.state.loading}
                        disabled={!this.state.selectedRowKeys.length}
                        className="button"
                        style={{marginLeft: 30}}
                    >
                        <Icon type="share"/>
                        提交
                    </Button>
                </div>

                <Table
                    dataSource={this.state.dataSource}
                    style={{ marginTop: 5 }}
                    isLoading={this.state.dataLoading}
                    rowSelection={{
                        ...this.rowSelection,
                        selectedRowKeys: this.state.selectedRowKeys,
                    }}
                    primaryKey="bid"
                > 
                    <Table.Column title="序号" cell={this.indexFm} width={55} align="center" /> 
                    <Table.Column title="申请人" dataIndex="userName" align="center" />
                    <Table.Column title="出差日期" cell={this.timeF} align="center" />
                    <Table.Column title="出差地" dataIndex="businessPlace" align="center" />
                    <Table.Column title="出差人员" dataIndex="participantNames" align="center" />
                    <Table.Column title="交通工具" dataIndex="type" cell={this.Typeformate} align="center" />
                    <Table.Column title="费用申请" dataIndex="businessFee" align="center" />
                    <Table.Column title="申请时间" dataIndex="applyTime" align="center" />
                    <Table.Column title="申请状态" dataIndex="applyState" cell={this.applyStateF} align="center" />
                    <Table.Column title="操作" cell={this.rowButton} align="center" width={150}/>
                </Table>
                <Pagination
                    pageSizeSelector="dropdown"
                    onChange={this.changePage}
                    onPageSizeChange={this.changePageSize}
                    current={this.state.page}
                    pageSize={this.state.pageSize}
                    total={this.state.total}
                    size="small"
                    style={{ marginTop: "30PX", marginLeft: "250px", marginRight: "50px" }}

                />
            </IceContainer>
        </div>
    );
}



} 