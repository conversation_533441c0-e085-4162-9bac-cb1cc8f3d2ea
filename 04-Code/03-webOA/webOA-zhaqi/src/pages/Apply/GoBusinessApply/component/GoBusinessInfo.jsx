import React, { Component } from 'react';
import { Upload, Feedback, Icon, Dialog, Field, Form, Grid, Button, Input, DatePicker, Checkbox } from '@icedesign/base';
import qs from 'qs';
import axios from 'axios';
import { OAURL } from '../../../../components/URL/OAURL';
import PublicChooseUser from '../../../../components/PublicChooseUser';

const { Row } = Grid;
const FormItem = Form.Item;
const { Group: CheckboxGroup } = Checkbox;
const list = [
  {
    value: '0',
    label: "飞机"
  },
  {
    value: '1',
    label: "火车"
  },
  {
    value: '2',
    label: "汽车"
  }
];


export default class GoBusinessInfo extends Component {
  static displayName = 'GoBusinessInfo';
  static defaultProps = {};

  constructor(props) {
    super(props);
    this.state = {
      startTime: '',
      endTime: '',
      type1: '',
      visible: false,
      stuffId: sessionStorage.getItem("stuffId"),
      url: OAURL,
      save: false,
      submit: false,
      fileList:[]
    };
    this.field = new Field(this, { autoUnmount: true });
  }

  onChange = (selectedItems) => {
    this.field.setValue("type", selectedItems);
    let menuId = ''
    for (let i = 0; i < selectedItems.length; i++) {
      menuId += selectedItems[i];
      if (i != selectedItems.length - 1) {
        menuId += ',';
      }
    }
    this.setState({
      type1: menuId
    })

  }

  //时间格式化
  formateDate = (value, str) => {
    return str;
  };

  //关闭窗口
  onCloseDialog = () => {
    this.setState({
      visible: false
    });
  };

  //打开编辑或者新增对话框
  onOpen = (record) => {
    if (record == undefined) {
      this.setState({
        visible: true,
      });
    }
    else {
      const { type1 } = this.props;
      let type2 = type1.split(",");
      record.type = type2;

      this.field.setValues({ ...record });

      let fileList = [];
      fileList.push({
        name: record.fileName,
        attachment: record.attachment,
        downloadURL:`${OAURL}/file/download?fileName=`+ record.attachment,
      })

      this.setState({
        visible: true,
        fileList: fileList,
      });
    }
  };

  //保存
  businessSave = () => {
    const { url } = this.state;
    this.field.validate((error, values) => {
      if (error) {
        Feedback.toast.error("Errors in from!!!");
        return;
      }
      else {
        this.setState({
          save: true,
        });
        values.type = this.state.type1;
        //已拒绝的申请，重新保存为新的申请  
        if (values.applyState == '3') {
          values.bid = null;
        }
        //后台保存成功，再更新table
        axios({
          method: "post",
          url: url + "/business/save",
          data: qs.stringify(values), //转换成实体
          //  data: values,  // map 接收 不要转换实体
        })
          .then(response => {
            this.setState({
              save: false,
            });
            let jsondata = response.data;
            if (jsondata.statusCode == 0) {
              Feedback.toast.success('保存成功');
              this.props.refreshTable();
              this.setState({
                visible: false
              });
            } else {
              Feedback.toast.error(jsondata.msg);
            }
          })
          .catch(error => {
            this.setState({
              save: false,
            });
            Feedback.toast.error("ajax请求异常 " + error);
          });
      }
    });
  };

  //提交
  businessSubmit = () => {
    const { url } = this.state;
    this.field.validate((error, values) => {
      if (error) {
        Feedback.toast.error("Errors in from!!!");
        return;
      }
      this.setState({
        submit: true,
      });
      values.type = this.state.type1;
      //已拒绝的申请，重新保存为新的申请  
      if (values.applyState == '3') {
        values.bid = null;
      }
      //后台保存成功，再更新table
      axios({
        method: "post",
        url: url + "/business/saveAndPost",
        data: qs.stringify(values),
      })
        .then(response => {
          this.setState({
            submit: false,
          });
          let jsondata = response.data;
          if (jsondata.statusCode == 0) {
            Feedback.toast.success('提交成功');
            this.props.refreshTable();
            this.setState({
              visible: false
            });
          } else {
            Feedback.toast.error(jsondata.msg);
          }
        })
        .catch(error => {
          this.setState({
            submit: false,
          });
          Feedback.toast.error("ajax请求异常 " + error);
        });
    });
  };

  //是新增按钮还是编辑按钮
  editOrAdd = (record, opt) => {
    if (opt == 'edit') {
      return (
        <a onClick={() => this.onOpen(record)} title="编辑">
          <Icon type="survey" style={{ color: "#3399ff", cursor: "pointer" }} size="small" />
        </a>
      );
    }
    else {
      return (
        <Button className="button" type="primary"
          onClick={() => this.onOpen(record)}>
          <Icon type="add" />
          新增
        </Button>
      );
    }
  };
  changeStartTime = (data, f) => {

    this.field.setValue('startTime', f)
    this.setState({
      startTime: data,
    });


    if (this.state.endTime != '' && data != null && this.state.endTime != null) {

      //转化时间
      let startTimeF = new Date(data).getTime().toString();
      let endTimeF = new Date(this.state.endTime).getTime().toString();
      if (parseFloat(startTimeF) > parseFloat(endTimeF)) {

        Feedback.toast.error("结束时间不能小于开始时间");
        this.field.setValue("totalDay", 0);
      }
      else {

        let totalDay = endTimeF - startTimeF;
        //转化时间
        let totalDay1 = (Math.floor(totalDay / (24 * 3600 * 1000))) + 2;
        //赋值
        this.field.setValue("totalDay", totalDay1);
      }
    }
    else {
      this.field.setValue("totalDay", 0);
    }
  }
  changeEndTime = (data, f) => {
    this.field.setValue('endTime', f)

    this.setState({
      endTime: data,
    });

    if (this.state.startTime != '' && data != null && this.state.startTime != null) {
      //转化时间
      let startTimeF = new Date(this.state.startTime).getTime().toString();
      let endTimeF = new Date(data).getTime().toString();
      if (parseFloat(startTimeF) > parseFloat(endTimeF)) {

        Feedback.toast.error("结束时间不能小于开始时间");
        this.field.setValue("totalDay", 0);
      }
      else {
        let totalDay = endTimeF - startTimeF;
        //转化时间
        let totalDay1 = (Math.floor(totalDay / (24 * 3600 * 1000))) + 2;
        //赋值
        this.field.setValue("totalDay", totalDay1);
      }
    }
    else {
      this.field.setValue("totalDay", 0);
    }

  }

  uploadSuccess = (response) => {
    let jsondata = response.data;
    if (response.code == 0) {
      this.field.setValue('attachment', jsondata.attachment);
      this.field.setValue('fileName', jsondata.fileName);
    }
    Feedback.toast.success('附件上传成功');
  }

  uploadError = (response) => {
    if (response.response.code == 1) {
      Feedback.toast.error(response.response.msg);
    }
  }

  //任务参与人数据回填
  chooseInpartUser = (rdata) => {
    let userNames = '';
    let userIds = '';
    for (let i = 0; i < rdata.length; i++) {
      userNames += rdata[i].label + ',';
      userIds += rdata[i].value + ',';
    }
    this.field.setValue('participantNames', userNames.substring(0, userNames.length - 1));
    this.field.setValue('participantIds', userIds.substring(0, userIds.length - 1));
  };

  render() {
    const init = this.field.init;
    const { record, opt } = this.props;
    const { stuffId } = this.state;
    const formItemLayout = {
      labelCol: {
        fixedSpan: 6,
      },

    };
    const footer = (
      <div>
        <a onClick={this.businessSave} href="javascript:;">
          <Button size="medium" type="primary" loading={this.state.save} style={{marginRight: 30}}>
            保存
          </Button>
        </a>
        <a onClick={this.businessSubmit} href="javascript:;">
          <Button size="medium" type="primary" loading={this.state.submit} style={{marginRight: 30}}>
            提交
          </Button>
        </a>
        <a onClick={this.onCloseDialog} href="javascript:;">
          <Button size="medium" >
            取消
          </Button>
        </a>
      </div>
    );
    return (
      <div style={styles.colstyle}>
        {this.editOrAdd(record, opt)}
        <Dialog
          style={{ width: 700 }}
          visible={this.state.visible}
          footer={footer}
          footerAlign="center"
          onClose={this.onCloseDialog}
          title={opt == 'edit' ? "修改出差申请" : "新增出差申请"}
          isFullScreen
        >
          <Form direction="ver" field={this.field}>
            <Input {...init("bid")} htmlType="hidden" /> {/* 申请单id */}
            <Input {...init("userId", { initValue: stuffId })} htmlType="hidden" /> {/* 员工id */}
            <Input {...init("deptId", { initValue: sessionStorage.getItem("deptId") })} htmlType="hidden" /> {/* 部门id */}
            <Input {...init("applyTitle")} htmlType="hidden" /> {/* 申请单name */}
            <Input {...init("createTime")} htmlType="hidden" /> {/* 创建时间 */}
            <Input {...init("serialNum")} htmlType="hidden" /> {/* 序列号 */}
            <Input {...init("applyState")} htmlType="hidden" /> {/* 申请状态 */}
            <Row>
              <Form.Item label="申请人：" {...formItemLayout}>
                <Input {...init("userName", {
                  initValue: sessionStorage.getItem("realName")
                })}
                  readOnly
                  style={{ width: 160 }} />
              </Form.Item>
              <Form.Item label="部门：" {...formItemLayout}>
                <Input {...init("deptName", {
                  initValue: sessionStorage.getItem("deptName")
                })}
                  readOnly="true"
                  style={{ width: 160 }} />
              </Form.Item>
            </Row>
            {/* <Row>
              <Form.Item label="岗位：" {...formItemLayout}>
                <Input {...init("userPosition", {
                  initValue: sessionStorage.getItem("userPosition")
                })}
                  readOnly
                  style={{ width: 160 }} />
              </Form.Item>
              <Form.Item label="工号：" {...formItemLayout}>
                <Input {...init("userJobNum", { initValue: sessionStorage.getItem("userJobNum") })} readOnly style={{ width: 160 }} />
              </Form.Item>
            </Row> */}
            <Row>
              <FormItem label="出差人员：" {...formItemLayout}>
                <Input {...init("participantIds")} style={{ display: "none" }} />
                <Input {...init("participantNames")} hasClear style={{ width: "440px" }}
                  placeholder="请点击+选择出差人"
                  readOnly
                />
                <PublicChooseUser
                  type='1'
                  chooseInpartUser={this.chooseInpartUser}
                />
              </FormItem>
            </Row>
            <Row>
              <FormItem label="出差地：" {...formItemLayout}>
                <Input
                  {...init('businessPlace', {
                    initValue: sessionStorage.getItem("businessPlace"),
                    rules: [{ required: true, message: '必填' }]
                  })}
                />
              </FormItem>
              <FormItem label="费用（元）：" {...formItemLayout}>
                <Input
                  {...init('businessFee', { rules: [{ required: true, message: '必填' }] })}
                  htmlType="number"
                />
              </FormItem>
            </Row>
            <Row>
              <Form.Item label="交通工具：" {...formItemLayout}>
                <CheckboxGroup className="next-form-text-align"
                  {...init('type', { rules: [{ required: true, message: '必填' }] })}
                  dataSource={list}
                  onChange={this.onChange}
                />
              </Form.Item>
            </Row>
            <Row>
              <Form.Item label="出差日期：" {...formItemLayout}>
                <DatePicker
                  {...init("startTime", {
                    initValue: sessionStorage.getItem("startTime"),
                    getValueFromEvent: this.formateDate,
                    rules: [{ required: true, message: "必填" }],
                  })}
                  // onChange={this.changeStartTime}
                  style={{ width: 160 }}
                />
              </Form.Item>
              <Form.Item label="&emsp;至" >
                <DatePicker {...init('endTime', {
                  initValue: sessionStorage.getItem("endTime"),
                  getValueFromEvent: this.formateDate,
                })}
                  // onChange={this.changeEndTime}
                  style={{ width: 160 }}
                />
              </Form.Item>
            </Row>
            <Row>
              <FormItem label="出差天数：" {...formItemLayout}  >
                <Input {...init('totalDay', {
                  rules: [{ required: true, message: "必填" }]
                })}
                />
              </FormItem>
            </Row>
            <Row>
              <FormItem label="出差事由：" {...formItemLayout}  >
                <Input {...init('remarks', {
                  initValue: sessionStorage.getItem("remarks"),
                  rules: [{ required: true, message: "必填" }]
                })}
                  multiple
                  style={{ width: "460px" }} />
              </FormItem>
            </Row>
            <Row>
              <Form.Item label="上传附件：" {...formItemLayout}>
                <Upload
                  action={`${OAURL}/file/upload`}
                  onSuccess={this.uploadSuccess}
                  onError={this.uploadError}
                  limit={1}
                  defaultFileList={this.state.fileList}
                >
                  <Button type="primary" className="button">
                    点击上传附件
                  </Button>
                </Upload>
                <Input {...init('attachment')} htmlType="hidden" />
                <Input {...init('fileName')} htmlType="hidden" />
              </Form.Item>
            </Row>
          </Form>
        </Dialog>
      </div>
    );















  }
}
const styles = {
  colstyle: {
    display: 'inline-block',
    marginRight: '5px',
  },
};
