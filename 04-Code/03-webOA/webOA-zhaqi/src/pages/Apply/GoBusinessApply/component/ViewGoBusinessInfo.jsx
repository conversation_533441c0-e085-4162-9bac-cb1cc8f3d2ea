import React, { Component } from 'react';
import {Checkbox, NumberPicker, Dialog, Button, Form, Input, Field, Select, Grid, Radio, DatePicker, moment, Feedback, Icon } from '@icedesign/base';
import axios from 'axios';
import { OAURL } from '../../../../components/URL/OAURL';
import { json } from 'graphlib';

const { Row, Col } = Grid;
const list = [
  {
      value: '0',
      label: "飞机"
  },
  {
      value: '1',
      label: "火车"
  },
  {
      value: '2',
      label: "汽车"
  }
];
const { Group: CheckboxGroup } = Checkbox;

export default class ViewGoBusinessInfo extends Component {
  static displayName = 'ViewGoBusinessInfo';

  static defaultProps = {};

  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      isVisible: "none",
      url: OAURL,   
      attachment: '',
      fileName: '',  
    };
    this.field = new Field(this, { autoUnmount: true });
  }

  onClose = () => {
    this.setState({
      visible: false,
    });
  };

  componentDidMount() {
    let opt = this.props.opt;
    if (opt == 'flow') {
      this.setState({
        isVisible: "block",
      });
    }
  }

  onOpen = (record) => {
    const { type1 } = this.props;
    let type2 = type1.split(",");
    record.type = type2;
    this.field.setValues({ ...record });
    this.setState({
      visible: true,
    });
  }

//审批查看申请单详情
onOpenQ = (record) => {
  const { url } = this.state;
  axios.get(url + '/process/applyDetail', {
    params: {
      applyId: record.id,
      type: '4',
      n: Math.random(),
    }
  })
    .then((response) => {
      let jsondata = response.data;
      if (jsondata.statusCode == 0) {
        this.field.setValues({ ...jsondata.apply[0] });
        this.setState({
          visible: true,
          attachment: jsondata.apply[0].attachment,
          fileName: jsondata.apply[0].fileName,
        });
      }
      else {
        Feedback.toast.error('后台数据返回失败');
      }
    })
    .catch((error) => {
      Feedback.toast.error('系统繁忙，请稍后重试：' + error);
    })
}

  //opt为view时，为名称上查看页面；如果为flow，则为签审页面
  //id为申请单的id，
  viewStyleFm = (opt, record, title) => {
    if (opt == "view") {
      return (
        <a onClick={() => this.onOpen(record)} title="查看" >
          <Icon type="browse" style={{ color: "#3399ff",cursor:"pointer" }} size="small" />
        </a>
      );
    }
    else {
      return (
        <a onClick={() => this.onOpenQ(record)}  style={{ color: "#3399ff",cursor:"pointer" }}>
          {title}
        </a>
      );
    }
  };

  footerFm = (opt) => {
    if (opt == 'flow') {
      return (
        <span>
          <Button type="primary" onClick={this.agreeApply}>
            同意
          </Button>
          <Button type="secondary" onClick={this.rejectApply} style={{ marginLeft: 30 }}>
            拒绝
          </Button>
        </span>
      );
    }
    else {
      return (
        <Button type="primary" onClick={this.onClose}>
          关闭
        </Button>
      );
    }
  };

  //发送axios请求
  axiosMethod = (url) => {
    let auditId = this.props.auditId;
    let comment = this.field.getValue('comment');
    axios({
      method: 'post',
      url: url,
      data: {
        comment: comment,
        auditId: auditId
      }
    })
      .then((response) => {
        var jsondata = response.data;
        if (jsondata.statusCode == 0) {
          Feedback.toast.sucess("审批完成");
          this.props.refreshTable();
          this.setState({
            visible: false,
          });
        }
        else {
          Feedback.toast.error("审批失败");
        }
      })
      .catch((error) => {
        Feedback.toast.error("ajax请求异常 " + error);
      });
  }
  //同意申请
  agreeApply = () => {
    let url = '';
    this.axiosMethod(url);
  }

  //拒绝申请
  rejectApply = () => {
    let url = '';
    this.axiosMethod(url);
  }

  render() {
    const init = this.field.init;
    //id为申请单id，auditId为审批id，opt为操作类型，取值有view和flow
    const {record, auditId, opt, title } = this.props;
    const formItemLayout = {
      labelCol: {
        fixedSpan: 6,
      }
    };
    const { attachment, fileName } = this.state;
    return (
      <div style={styles.buttonStyle}>
        {this.viewStyleFm(opt, record, title)}
        <Dialog
          minMargin={10}
          style={{ width: 700 }}
          visible={this.state.visible}
          onClose={this.onClose}
          footer={this.footerFm(opt)}
          title={"查看出差申请"}
          footerAlign="center"
        >
          <Form direction="ver" field={this.field}>
            <Input {...init("id")} htmlType="hidden" />   {/* 申请单id */}
            <Input {...init("userId")} htmlType="hidden" />    {/* 员工id */}
            <Input {...init("deptId")} htmlType="hidden" />     {/* 部门id */}
            <Row>
              <Form.Item label="申请人：" {...formItemLayout}>
                <Input {...init("userName")} readOnly />
              </Form.Item>
              <Form.Item label="部门：" {...formItemLayout}>
                <Input {...init("deptName")} readOnly="true" />
              </Form.Item>
            </Row>
            {/* <Row>
              <Form.Item label="岗位：" {...formItemLayout}>
                <Input {...init("userPosition")} readOnly="true" />
              </Form.Item>
              <Form.Item label="工号：" {...formItemLayout}>
                <Input {...init("userJobNum")} readOnly="true" />
              </Form.Item>
            </Row> */}
            <Row>
              <Form.Item label="出差人员：" {...formItemLayout}>
                <Input {...init("participantIds")} style={{ display: "none"}}/>
                <Input {...init("participantNames")}  style={{ width: "435px" }} readOnly="true"
                />
              </Form.Item>
            </Row>
            <Row>
              <Form.Item label="出差地方：" {...formItemLayout}>
                <Input {...init("businessPlace")} readOnly />
              </Form.Item>
              <Form.Item label="费用（元）：" {...formItemLayout}>
                <Input 
                  {...init('businessFee')}
                  readOnly
                />
              </Form.Item>
            </Row>
            <Row>
              <Form.Item label="交通工具：" {...formItemLayout}>
                <CheckboxGroup className="next-form-text-align"  
                    {...init('type')} readOnly
                    dataSource={list}                  
                    style={{ width: 160 }}
                />
              </Form.Item>
            </Row>
            <Row>
              <Form.Item label="出差日期：" {...formItemLayout} >
                <Input  {...init("startTime")} style={{ width: 160 }} readOnly />
              </Form.Item>
              <Form.Item label="&emsp;至" >
                <Input {...init('endTime')} style={{ width: 160 }} readOnly />
              </Form.Item>
            </Row>
            <Row>
                <Form.Item label="出差天数：" {...formItemLayout}  >
                  <Input {...init('totalDay')}  
                   readOnly
                  />
                </Form.Item>
            </Row>
            <Row>
              <Form.Item label="出差事由：" {...formItemLayout}>
                <Input {...init("remarks")} style={{ width: 440, height: 70 }} />
              </Form.Item>
            </Row>
            <Row>
              {
                record.attachment ?
                  <Form.Item label="附件：" {...formItemLayout}>
                  <a href={`${OAURL}/file/download?fileName=`+record.attachment} 
                    className="next-form-text-align" 
                    style={styles.attachment}>                     
                    {record.fileName}
                  </a>
                </Form.Item>
                :
                <Form.Item label="附件：" {...formItemLayout}>
                  <a href={`${OAURL}/file/download?fileName=`+attachment} 
                    className="next-form-text-align" 
                    style={styles.attachment}>                     
                    {fileName}
                  </a>
                </Form.Item>
              }
              
            </Row>
            
          </Form>
        </Dialog>
      </div>
    );
  }
}
const styles = {
  buttonStyle: {
    display: 'inline-block',
    marginRight: '2px',
  }
};