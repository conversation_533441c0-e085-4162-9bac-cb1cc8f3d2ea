/* eslint-disable no-trailing-spaces */
import React, { Component } from 'react';
import { Table, Feedback } from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import { OAURL } from '../../../components/URL/OAURL';

export default class EducateTableReadOnly extends Component {
  static displayName = 'EducateTable';
  static propTypes = {};

  static defaultProps = {};

  constructor(props) {
    super(props);
    this.state = {
      dataSource: [],
      stuffId: sessionStorage.getItem("stuffId"),
      url: OAURL,
    };
  }

  componentWillMount() {
    const { stuffId } = this.props;
    if(stuffId){
      this.getEducateList(stuffId);
    }
    else{
      const { stuffId } = this.state;
      this.getEducateList(stuffId);
    }
  }

  //获取教育经历列表
  getEducateList = (stuffId) => {
    const { url } =  this.state;
    axios.get(url+'/staff/study',{
      params:{
        sid: stuffId,
      }
    })
    .then((response) => {
      var jsondata = response.data;
      if(jsondata.statusCode == 0){
        this.setState({
          dataSource: jsondata.educateInfo,
        });
      }
      else{
        Feedback.toast.error("后台数据返回失败");
      }
    })
    .catch((error) => {
      Feedback.toast.error("ajax请求出错: "+ error);
    });
  }


  getEducateInfo = () => {
    return this.state.dataSource;
  };


  render() {
    return (
      <div>
        <IceContainer title="学习经历">
          <Table dataSource={this.state.dataSource} >
            <Table.Column title="教育类型" dataIndex="type" style={styles.tableStyle}/>
            <Table.Column title="毕业时间" dataIndex="etime"  style={styles.tableStyle}/>
            <Table.Column title="院校" dataIndex="school"  style={styles.tableStyle}/>
            <Table.Column title="专业" dataIndex="major" style={styles.tableStyle} />
            <Table.Column title="学历" dataIndex="edu"  style={styles.tableStyle} width={100}/>
            <Table.Column title="学位" dataIndex="degree" style={styles.tableStyle} width={100}/>
          </Table>
        </IceContainer>
      </div>
    );
  }
}

const styles = {
  tableStyle: {
    fontSize: "14px",
  },
  tableTitle: {
    fontSize: '15px',
    fontWeight: 'bold',
    color: 'black',
    marginTop: '20px',
    marginBottom: '10px',
    borderBottom: '1px solid #eee',
  }
}
