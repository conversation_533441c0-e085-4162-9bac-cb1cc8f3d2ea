/* eslint-disable no-trailing-spaces */
import React, { Component } from 'react';
import { Table, Feedback } from '@icedesign/base';
import IceContainer from '@icedesign/container';
import axios from 'axios';
import { OAURL } from '../../../components/URL/OAURL';

export default class WorkTableReadOnly extends Component {
  static displayName = 'WorkTable';

  static propTypes = {};

  static defaultProps = {};
  
  constructor(props) {
    super(props);
    this.state = {
      dataSource: [],
      stuffId: sessionStorage.getItem("stuffId"),
      url: OAURL,
    };
  }

  componentDidMount() {
    const { stuffId } = this.props;
    if(stuffId){
      this.getWorkList(stuffId);
    }
    else{
      const { stuffId } = this.state;
      this.getWorkList(stuffId);
    }
  }

  getWorkList = (stuffId) => {
    const { url } = this.state;
    axios.get(url+'/staff/work',{
      params:{
        sid: stuffId,
      }
    })
    .then((response) => {
      var jsondata = response.data;
      if(jsondata.statusCode == 0){
        this.setState({
          dataSource: jsondata.workInfo,
        });
      }
      else{
        Feedback.toast.error("后台数据返回失败");
      }
    })
    .catch((error) => {
      Feedback.toast.error("ajax请求异常：" + error);
    }); 
  }



  getWorkInfo = () => {
    return this.state.dataSource;
  };
  

  render() {
    return (
      <div>
        <IceContainer title="工作经历">
          <Table dataSource={this.state.dataSource} isLoading={this.state.dataIsLoading}>
            <Table.Column title="开始时间" dataIndex="stime" align="center" />
            <Table.Column title="结束时间" dataIndex="etime" align="center" />
            <Table.Column title="公司" dataIndex="company" align="center" />
            <Table.Column title="职位" dataIndex="role" align="center" />
            <Table.Column title="备注" dataIndex="remarks" align="center" />
          </Table>
        </IceContainer>
      </div>
    );
  }
}
const style = {
  tableTitle: {
    fontSize: '15px',
    fontWeight: 'bold',
    color: 'black',
    marginTop: '20px',
    marginBottom: '10px',
    borderBottom: '1px solid #eee',
  }
}