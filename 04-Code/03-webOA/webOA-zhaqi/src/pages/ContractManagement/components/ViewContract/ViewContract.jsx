import React, {Component,Fragment} from 'react';
import {Field, Input, Grid, DatePicker, Button, Icon,  Form, Checkbox,Upload} from '@icedesign/base';
import IceContainer from '@icedesign/container';
import {Feedback} from "@icedesign/base/index";
import qs from 'qs';
import axios from 'axios';
import { OAURL } from '../../../../components/URL/OAURL';
import PublicChooseUser from '../../../../components/PublicChooseUser/PublicChooseUser';
import { Link } from 'react-router-dom';
const {Row, Col} = Grid;
const Toast = Feedback.toast;
const FormItem = Form.Item;

export default class ViewContract extends Component{
  constructor(props){
    super(props);
    this.state={
      url: OAURL,
      attachmentList: [],
      fileNameList: [],
      fileList:[//需要显示的文件列表
        // {name: "AA.txt"},
        // {name:"AA.txt"}
      ]
    };
    this.field = new Field(this);
  }

  //初始化获取数据
  componentDidMount() {
    let state = this.props.location.state;
    const { url } = this.state;
    axios({
      method: "post",
      url: url + "/archives/one",
      data:qs.stringify(state), //转换成实体
    })
      .then((response) => {
        this.setState(
          {
            item: response.data.obj,
            page: state.page,
            pageSize: state.pageSize
          },
          ()=>{this.setForm()}
        )
        
      })
      .catch((error) => {
        Toast.error('获取失败' + error);
      });
  };





  setForm = () => {
    let record = this.state.item;
    this.field.setValues(record);
    
    let checked = record.remind;
    if(checked == '0'){
      checked = false;
    }else{
      checked = true;
    }
    this.setState({
      visible: true,
      checked: checked
    });	

    //获取文件列表
    if(record.filename){
      let fileShowName = record.filename.split(',');
      let attachmenShow = record.attachment.split(',');

      let fileList = [];
      let attachment = [];

      for(let i=0; i<fileShowName.length; i++){
        fileList.push({
            name: fileShowName[i],
            attachment:attachmenShow[i],
            downloadURL:`${OAURL}/file/download?fileName=` + attachmenShow[i],
          },);
      }
      this.setState({
        fileList: fileList
      })
    }
  };

  
  onClose = () => {
    this.setState({
      visible: false
    });
  };



 

  //格式化时间
  dataFm = (value) => {
    if(value){
      return moment(value).format('YYYY-MM-DD');
    }
  }

 


  //显示文件列表
  showFileList = ()=>{
    
      return this.state.fileList.map( (item)=>{
        return(
          <a 
            href={item.downloadURL}
            style={styles.attachment}
            className="next-form-text-align"
            key={item.attachment}
          >                     
            {item.name}
          </a>
        )
      })
  }
  

  render(){
    const {init} = this.field;
    const formItemLayout = {
      labelCol : {
        fixedSpan: 6,
      },
      wrapperCol :{
        span: 14,
      }
    };
    
    return (
      
      <div>
        <IceContainer title="查看合同">
          <Form direction="ver" field={this.field}>
            <Row wrap>
              <Col span="8">
                <FormItem label="合同编号："  {...formItemLayout}>
                  <Input {...init('no')} placeholder="" readOnly />
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="合同名称：" {...formItemLayout}>
                  <Input {...init('name')} placeholder="" readOnly />
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="合同类型：" {...formItemLayout}>
                  <Input {...init('type')} placeholder="" readOnly />
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="签订日期：" {...formItemLayout}>
                  <DatePicker {...init('signTime', {getValueFromEvent: this.dataFm})} disabled />
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="合同甲方：" {...formItemLayout}>
                  <Input {...init('partyA')} placeholder="" readOnly />
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="合同乙方：" {...formItemLayout}>
                  <Input {...init('partyB')} placeholder="" readOnly />
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="合同开始日期：" {...formItemLayout}>
                  <DatePicker {...init('startTime', {getValueFromEvent: this.dataFm})} disabled />
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="合同结束日期：" {...formItemLayout}>
                  <DatePicker {...init('endTime', {getValueFromEvent: this.dataFm})} disabled />
                </FormItem>
              </Col>
              <Col span="24">
                <FormItem label="附件：" {...formItemLayout}>
                  
                  {this.showFileList()}
                </FormItem>
              </Col>
              
              {this.state.checked ? (
                <Fragment>
                  <Col span="24">
                    <FormItem label="是否添加提醒：" {...formItemLayout}>
                    <Checkbox
                      style={{marginTop:7}}
                      checked={this.state.checked}
                      
                    />
                    </FormItem>
                  </Col>
                  <Col span="8">
                    <FormItem label="提醒标题：" {...formItemLayout}>
                      <Input readOnly {...init('remindTitle', { rules: [{ required: true, message: "必填" }]})} placeholder="请输入合同编号"/>
                    </FormItem>
                  </Col>
                  <Col span="8">
                    <FormItem label="提醒日期：" {...formItemLayout}>
                      <DatePicker disabled {...init('remindTime', {getValueFromEvent: this.dataFm, rules: [{ required: true, message: "必填" }]})} />
                    </FormItem>
                  </Col>
                  <Col span="24">
                    <FormItem label="提醒员工：" {...formItemLayout}>
                      
                      <Input 
                        style={{ width: 195, display: "inline-block"}} 
                        readOnly
                        placeholder="请点击+选择提醒人员"
                        value={this.state.remindStaffName}
                        {...init('remindStaffName', { rules: [{ required: true, message: "必填" }]})}
                      />
                      <Input {...init("remindStaffId")} htmlType="hidden"/>     
                      
                    </FormItem>
                  </Col>
                  
                </Fragment>
              ) : (
                ''
              )}
              
            </Row>
          </Form>
					<div align="center">                
            <Link 
              to={{
                pathname: `/stuff/ContractManagement`,
                state: { page:this.state.page, pageSize:this.state.pageSize }
              }}> 
              <Button type="primary">返回</Button> 
            </Link> 
           </div>
        </IceContainer>
      </div>
    )
  }
}

const styles = {
  attachment:{
    // fontStyle:'oblique',
    width:'330px',
    height:'28px',
    paddingLeft:'8px',
    marginBottom:'5px',
    fontSize: 12,
    // color:'#333',
    display:'block',
    background:'#f4f4f4'
   },
};