import React, {Component,Fragment} from 'react';
import {Field, Input, Grid, DatePicker, Select, Button, Icon, Dialog, Form, Checkbox, Upload, moment} from '@icedesign/base';
import IceContainer from '@icedesign/container';
import {Feedback} from "@icedesign/base/index";
import qs from 'qs';
import axios from 'axios';
import { OAURL } from '../../../../components/URL/OAURL';
import PublicChooseUser from '../../../../components/PublicChooseUser/PublicChooseUser';

const {Row, Col} = Grid;
const Toast = Feedback.toast;
const FormItem = Form.Item;
const { Combobox } = Select;

export default class AddDialog extends Component{
  field = new Field(this,{
    autoUnmount: true
  });
  constructor(props){
    super(props);
    this.state={
      url: OAURL,
      visible: false,
      checked: false,
      user:{
        id : sessionStorage.getItem("stuffId"),
        name : sessionStorage.getItem("realName"),
      },
      attachmentList: [],
      fileNameList: []
    };
    // this.field = new Field(this);
  }

  //初始化获取数据
  componentWillMount(){
    
  }

  onOpen = () => {
    let user = this.state.user;

    this.setState({
      visible: true,
      checked: false,
      attachmentList:[],
      fileNameList: []
    });
  };

  onClose = () => {
    
    this.setState({
      visible: false
    });
  };

  addSave = () =>{
    // alert('保存成功');
    this.field.validate((errors, values) => {
      if(errors){
        return;
      }
      let user = this.state.user;
      values.createId = user.id;
      values.createName = user.name;



      let checked = this.state.checked;

      if(checked){
        checked = 1;
      }else{
        checked = 0
      }
      values.remind = checked; //是否勾选


      let attachmentList = this.state.attachmentList.join(',');
      let fileNameList = this.state.fileNameList.join(',');
      values.attachment = attachmentList;  //附件名称(编号)
      values.filename = fileNameList;  //附件原名


      const {url} = this.state;
      axios({
        method: "post",
        url: url + "/archives/add",
        data:qs.stringify(values), //转换成实体
      })
        .then((response) => {
          // debugger
          Toast.success('添加成功');
          this.onClose();
          this.props.refreshTable();
          
        })
        .catch((error) => {
          Toast.error('提交失败' + error);
        });
      
    });

    
  }

  

  //格式化时间

  dataFm = (value) => {
    if(value){
      return moment(value).format('YYYY-MM-DD');
    }
  }

  //提醒人员数据回填
  chooseInchargeUser = (rdata) => {
    let userNames = '';
    let userIds = '';
    for(let i=0; i<rdata.length; i++){
      userNames += rdata[i].label + ',';
      userIds += rdata[i].value + ',';
    }
    this.field.setValue('remindStaffName', userNames.substring(0,userNames.length-1));
    this.field.setValue('remindStaffId', userIds.substring(0, userIds.length-1));
  };

  // 文件上传提示
  uploadSuccess = (response)=>{
    let jsondata = response.data;
    if(response.code == 0){
      this.setState({
        attachmentList:[...this.state.attachmentList,jsondata.attachment],//附件名称(编号)
        fileNameList:[...this.state.fileNameList,jsondata.fileName],//附件名称(附件原名)
      })
    }
    
    Toast.success('附件上传成功');
  }

  uploadError = (response) => {
    // if (response.response.code != 0){
      Toast.error(response.response.msg);
    // } 
    // Toast.error('附件上传失败');
  }
  


  // 合同类型输入

  //更新类型选择列表
  onInputFocus = () => {
    const {url} = this.state;
    axios({
      method: "get",
      url: url + "/archives/typeList",
    })
      .then((response) => {
        let values = response.data;
        // debugger
        if (values.msg == '查询成功') {
          this.setState({
            selectList: values.obj
          });
        }
        else {
          Feedback.toast.error('后台返回数据错误');
        }
      })
      .catch((error) => {
        Toast.error('系统繁忙，请稍后重试:' + error);
      });
    
  }

  //合同类型输入时触发
  onInputUpdate = (value)=>{
    this.field.setValue("type", value);
    // this.setState({
    //   selectValue:value
    // });
  }

  //选择合同类型时触发
  onSelectType = (value) => {
    // this.selectList();
    this.field.setValue("type", value);
    // this.setState({
    //   selectValue:value
    // });
  }


  render(){
    const {init} = this.field;
    const formItemLayout = {
      labelCol : {
        fixedSpan: 6,
      },
      wrapperCol :{
        span: 14,
      }
    };
    
    return (
      <div>
        <Button type="primary" size="medium" className="button" onClick={this.onOpen}>添加合同</Button>
        <Dialog
          style={{ width: 1000 }}
          visible={this.state.visible}
          onOk={this.addSave}
          onCancel={this.onClose}
          onClose={this.onClose}
          footerAlign="center"
          title="添加合同"
          autoFocus={false}
        >
          <Form direction="ver" field={this.field}>
            <Row wrap>
              <Col span="8">
                <FormItem label="合同编号："  {...formItemLayout}>
                  <Input {...init('no', { rules: [{ required: true, message: "必填" }]})} placeholder="请输入合同编号"/>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="合同名称：" {...formItemLayout}>
                  <Input {...init('name', { rules: [{ required: true, message: "必填" }]})} placeholder="请输入合同名称"/>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="合同类型：" {...formItemLayout}>
                  <Combobox
                    onInputFocus={this.onInputFocus}
                    onInputUpdate={this.onInputUpdate}
                    filterLocal={true}
                    // value={this.state.selectValue}
                    fillProps="label"
                    placeholder="请输入合同类型"
                    onChange={this.onSelectType}
                    dataSource={this.state.selectList}
                    {...init('type', {rules: [{ required: true, message: "必填" }]})}
                  />
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="签订日期：" {...formItemLayout}>
                  <DatePicker {...init('signTime', {getValueFromEvent: this.dataFm, rules: [{ required: true, message: "必填" }]})} />
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="合同甲方：" {...formItemLayout}>
                  <Input {...init('partyA')} placeholder="请输入合同甲方"/>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="合同乙方：" {...formItemLayout}>
                  <Input {...init('partyB')} placeholder="请输入合同乙方"/>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="合同开始日期：" {...formItemLayout}>
                  <DatePicker {...init('startTime', {getValueFromEvent: this.dataFm, rules: [{ required: true, message: "必填" }]})} />
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="合同结束日期：" {...formItemLayout}>
                  <DatePicker {...init('endTime', {getValueFromEvent: this.dataFm, rules: [{ required: true, message: "必填" }]})} />
                </FormItem>
              </Col>
              <Col span="24">
                <FormItem label="附件：" {...formItemLayout}>
                  <Upload
                    action={`${OAURL}/file/upload`}
                    onSuccess={this.uploadSuccess}
                    onError={this.uploadError}
                  >

                    <Button type="primary" className="button">上传附件</Button>
                  </Upload>
                </FormItem>
              </Col>
              <Col span="24">
                <FormItem label="是否添加提醒：" {...formItemLayout}>
                <Checkbox
                  style={{marginTop:7}}
                  checked={this.state.checked}
                  indeterminate={this.state.indeterminate}
                  onChange={checked => {
                    this.setState({ checked: checked, indeterminate: false });
                  }}
                />
                </FormItem>
              </Col>
              
                {this.state.checked ? (
                  <Fragment>
                    <Col span="8">
                      <FormItem label="提醒标题：" {...formItemLayout}>
                        <Input {...init('remindTitle', { rules: [{ required: true, message: "必填" }]})} placeholder="请输入合同编号"/>
                      </FormItem>
                    </Col>
                    <Col span="8">
                      <FormItem label="提醒日期：" {...formItemLayout}>
                        <DatePicker {...init('remindTime', {getValueFromEvent: this.dataFm, rules: [{ required: true, message: "必填" }]})} />
                      </FormItem>
                    </Col>
                    <Col span="24">
                      <FormItem label="提醒员工：" {...formItemLayout}>
                        
                        <Input 
                          style={{ width: 195, display: "inline-block"}} 
                          readOnly
                          placeholder="请点击+选择提醒人员"
                          value={this.state.remindStaffName}
                          {...init('remindStaffName', { rules: [{ required: true, message: "必填" }]})}
                        />
                        <Input {...init("remindStaffId")} htmlType="hidden"/>     
                        <PublicChooseUser
                          type='0'
                          chooseInchargeUser={this.chooseInchargeUser}
                        />
                      </FormItem>
                    </Col>
                  </Fragment>
                ) : (
                  ''
                )}
              
              
            </Row>
          </Form>
        </Dialog>
      </div>
    )
  }
}

const styles = {
  required:{
    display: 'inline-block',
    marginRight: '4px',
    content: "*",
    fontFamily: 'SimSun',
    color: '#FA7070',
   },
};