import React, { Component } from 'react';
import { Dialog, Button, Form, Input, Field, DatePicker, Select, Upload, Grid } from '@icedesign/base';
const FormItem = Form.Item;
const { Row, Col } = Grid;

export default class AddTaskDialog extends Component {

    static displayName = 'AddTaskDialog';
    static defalutProps = {};
    constructor(props) {
        super(props);
        this.state = {
            visible: false,
        };
        this.field = new Field(this);
    }
       
    onOpen = () => {
        this.setState({
            visible: true,
        });
    };
    taskFormSubmit = () => {

        this.field.validate((errors, values) => {
            if (errors) {

                alert('Errors in form!!!');
                return;
            }
            this.props.addRowData(values);
            this.setState({
                visible: false,
            });
        });
    };
    onCloseDialog= () => {
        this.setState({
          visible: false
        });
      };
    
     //时间格式化
    formateDate = (value, str) => {
        return str;
    };
    render() {
        const init = this.field.init;
        const { index, record } = this.props;
        //wrapper:包装
        const fromItemLayout = {
            labelCol: {
                fixedSpan: 8,
            },
            wrapperCol: {
                span: 12.5,
            },

        };
        const footer = (
            <div> <a onClick={this.taskFormSubmit} href="javascript:;">
                  <Button size="medium" type="primary" >
                    提交
                </Button>
                  </a>
                <a onClick={this.onCloseDialog} href="javascript:;">
                <Button size="medium" type="primary">
                    取消
                </Button>
                </a>
            </div>

        );
       
        return (
            <div style={styles.colstyle}>
                <Button size="small" type="primary" onClick={() => this.onOpen()}>
                    新增
                </Button>
                <Dialog
                    style={{ width: 710 }}
                    visible={this.state.visible}
                    closable="esc,mask,close"
                    footer={footer}
                    onClose={this.onCloseDialog}
                    title="新增下级任务"
                >
                    <Form direction="ver" field={this.field}>
                        <Row >
                            <FormItem label="任务标题：" {...fromItemLayout}>
                                <Input
                                    {...init('name', { rules: [{ required: true, message: '必填' }] })}
                                     autoComplete="off" 
                                 />
                               
                            </FormItem>


                            <FormItem label="任务类别：" {...fromItemLayout}>
                                <Select className="next-form-text-align"
                                    dataSource={[
                                        {
                                            label: '日常工作', value: '0'
                                        },
                                        {
                                            label: '活动', value: '1'
                                        },
                                        {
                                            label: '其他', value: '2'
                                        },
                                    ]}
                                    {...init('type', { rules: [{ required: true, message: "必选" }] })}
                                />
                            </FormItem>

                        </Row>
                        <Row>                            
                            <FormItem label="任务责任人：" {...fromItemLayout}  >
                                <Input
                                    {...init('leader_name', { rules: [{ required: true, message: '必填' }] })}
                                />
                            </FormItem>
                            <FormItem label="任务状态：" {...fromItemLayout}>
                                <Select className="next-form-text-align"
                                    {...init('state', { rules: [{ required: true, message: "必选" }] })}
                                    dataSource={[
                                        {
                                            label: '进行中', value: '0'
                                        },
                                        {
                                            label: '已完成', value: '1'
                                        },
                                        {
                                            label: '已终', value: '2'
                                        },
                                    ]}
                                />
                            </FormItem>
                        </Row>
                        <Row>
                            <FormItem label="任务开始时间：" {...fromItemLayout}>
                                <DatePicker {...init('start_time', { getValueFromEvent: this.formateDate, rules: [{ required: true, message: '必填' }] })} />
                            </FormItem>
                            <FormItem label="任务分配人：" {...fromItemLayout}  >
                                <Input
                                    {...init('allocator_name', { rules: [{ required: true, message: '必填' }] })}
                                />
                            </FormItem>
                           
                        </Row>
                        <Row >
                        <FormItem label="任务结束时间：" {...fromItemLayout}>
                                <DatePicker {...init('end_time', { getValueFromEvent: this.formateDate, rules: [{ required: true, message: '必填' }] })} />
                            </FormItem>

                            <FormItem label="任务参与人：" {...fromItemLayout}  >
                                <Input
                                    {...init('participant_name')}
                                />
                            </FormItem>
                        </Row >
                        <FormItem label="任务描述：" {...fromItemLayout}  >
                            <Input multiple style={{ width: '250%' }}  {...init('description')}
                            />
                        </FormItem>
                        <FormItem label="附件：" {...fromItemLayout}  >
                            <Upload
                                listType="text"
                                action=""
                                accept="file/doc, file/text,file/docx"
                                {...init('attachment')}
                            >
                                <Button type="primary" style={{ margin: "0 0 10px" }}>
                                    上传附件
                                </Button>
                            </Upload>
                        </FormItem>

                    </Form>
                </Dialog>

            </div>

        );


    }
}
const styles = {
    colstyle: {
        display: 'inline-block',
        marginRight: '5px',
    },
};
