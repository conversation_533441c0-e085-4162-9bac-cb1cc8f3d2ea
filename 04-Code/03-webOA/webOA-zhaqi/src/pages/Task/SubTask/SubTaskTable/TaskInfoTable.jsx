import React, { Component } from 'react';
import IceContainer from '@icedesign/container';
import AddTaskDialog from './AddTaskDialog';
import { Table, Grid, Input, Button, Select, Form, Pagination, Field } from '@icedesign/base';
import EidtTaskDialog from './EidtTaskDialog';
import ViewTaskDialog from './ViewTaskDialog';
import DeleteTBalloon from './DeleteBalloon';
import axios from 'axios';

const { Row, Col } = Grid;
export default class TaskInfoTable extends Component {

    static displayName = 'TaskInfoTable';
    static propTypes = {};
    static defalutProps = {};

    constructor(props) {
        super(props);
        this.state = {
            dataSource: [],
            dataKey: "taskInfo",
            pagination: {
                page: 1,
                total: 100,
                pageSize: 10
            }
        };
        this.field = new Field(this);
    }


    componentDidMount() {
        axios.get('/mock/task.json')
            .then((response) => {
                var jsondata = response.data;
                this.setState({
                    dataSource: jsondata.taskInfo,
                });
            });
    }

    stateFormate = (value) => {
        if (value == 0) {
            return '进行中';
        }
        if (value == 1) {
            return '已完成';
        }
        else {
            return '已终';
        }

    };
    typeFormate = (value) => {
        if (value == 0) {
            return '日常工作';
        }
        if (value == 1) {
            return '活动';
        }
        else {
            return '其他';
        }
    };



    addRowData = (value) => {
        const { dataSource } = this.state;
        dataSource.push(value);
        this.setState({
            dataSource,
        });
    };

    removeRowData = (value, index) => {

        const { dataSource } = this.state;
        //splice:拼接
        dataSource.splice(index, 1);
        this.setState({
            dataSource,
        });

    };
    //翻页
    changePage = (pageNo) => {
        this.setState({
            dataLoading: true,
        });
        axios.get('/url', {
            params: {
                page: pageNo,
            }
        })
            .then((response) => {
                var jsondata = response.data;
                if (jsondata.statusCode == 0) {
                    this.setState({
                        dataSource: jsondata.contactList,
                        pagination: jsondata.pagination,
                        dataLoading: false,
                    });
                }
                else {
                    alert("后台数据获取失败");
                }
            })
            .catch((error) => {
                alert(error);
            });
    };
    //改变显示记录
    changePageSize = (pageSize) => {
        this.setState({
            dataLoading: true,
        });
        axios.get('/url', {
            params: {
                pageSize: pageSize,
            }
        }).then((response) => {
            var jsondata = jsondata.data;
            if (jsondata.statusCode == 0) {
                this.setState({
                    dataSource: jsondata.contactList,
                    pagination: jsondata.pagination,
                    dataLoading: false,
                });
            }
            else {
                alert("后台数据获取失败");
            }
        }).catch((error) => {
            alert(error);
        });
    };

    //查询
    doSearch = () => {
        let searchValue = this.field.getValues();
        axios.get('url', {
            params: searchValue,
        })
            .then((Response) => {
                let jsondata = response.data;
                if (jsondata.statusCode == 0) {
                    this.setState({
                        dataSource: jsondata.dataKey,
                    });
                }
                else {
                    alert('后台返回数据错误');
                }
            })
            .catch((error) => {
                alert('系统繁忙，请稍后重试:' + error);
            });

    };
    //重置
    doClear = () => {
        this.field.reset();
    };
    getFormValues = (dataIndex, values) => {
        const { dataSource } = this.state;
        const { dataKey } = this.state;
        dataSource[dataIndex] = values;
        this.setState({
            dataSource,
        });
    };


    rowOptButton = (value, index, record) => {
        return (
            //<span> 来组合行内元素，以便通过样式来格式化它们。
            <span>
                <ViewTaskDialog
                    index={index}
                    record={record}
                    getFormValues={this.getFormValues}
                />
                <EidtTaskDialog
                    index={index}
                    record={record}
                    getFormValues={this.getFormValues}
                />
                <DeleteTBalloon
                    removeRowData={() => this.removeRowData(value, index)}
                />
            </span>
        );
    };
    //Container:容器 Column:列
    render() {
        const { init } = this.field;
        return (
            <div>
                <IceContainer title="下级任务">
                    <div align="right">

                        <Form direction="hoz" field={this.field}>
                            <Form.Item label="任务标题">
                                <Input {...init('name')} style={{ width: '180px' }}
                                    placeholder="请输入要搜索的任务标题" />
                            </Form.Item>
                            <Form.Item label="任务状态">
                                <Select {...init('state')}
                                    style={{ width: '180px' }}
                                    placeholder="请选择状态"
                                    dataSource={[
                                        {
                                            label: '进行中', value: '0'
                                        },
                                        {
                                            label: '已完成', value: '1'
                                        },
                                        {
                                            label: '已终', value: '2'
                                        },
                                    ]}
                                />
                            </Form.Item>
                            <Form.Item>
                                <Button type="primary" onClick={this.doSearch}>搜索</Button>
                                <Button type="secondary" onClick={this.doClear}>重置 </Button>
                            </Form.Item>
                        </Form>
                    </div>
                    <div style={{ marginBottom: 5 }}>
                        <AddTaskDialog addRowData={this.addRowData} />
                    </div>
                    <Table dataSource={this.state.dataSource}>
                        <Table.Column title="任务标题" dataIndex="name" align="center" />
                        <Table.Column title="任务类别" dataIndex="type" align="center" cell={this.typeFormate} />
                        <Table.Column title="责任人" dataIndex="leader_name" align="center" />
                        <Table.Column title="任务状态" dataIndex="state" align="center" cell={this.stateFormate} />
                        <Table.Column title="任务开始时间" dataIndex="start_time" align="center" />
                        <Table.Column title="任务结束时间" dataIndex="end_time" align="center" />
                        <Table.Column title="操作" cell={this.rowOptButton} align="center" style={{ width: 200 }} />
                    </Table>
                    <Pagination
                        pageSizeSelector="filter"
                        pageSizeSelector="dropdown"
                        onChange={this.changePage}
                        onPageSizeChange={this.changePageSize}
                        current={this.state.pagination.page}
                        pageSize={this.state.pagination.pageSize}
                        total={this.state.pagination.total}
                        size="small"
                        style={{ marginTop: "30PX", marginLeft: "250px", marginRight: "50px" }}

                    />

                </IceContainer>
            </div>
        );
    }
}



