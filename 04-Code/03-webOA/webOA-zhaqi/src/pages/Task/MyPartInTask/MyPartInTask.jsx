import React, { Component } from 'react';
import { Field, Form, Select,  Input, Button, Feedback, Pagination, Grid, Icon, moment } from '@icedesign/base';
import axios from 'axios';
import IceContainer from '@icedesign/container';
import CustomTable from '../../../components/CustomTable';
import ViewTaskInfo from './components/ViewTaskInfo'
import { OAURL } from '../../../components/URL/OAURL';

const { Row } = Grid;
export default class MyPartInTask extends Component {
  static displayName = "MyPartInTask";

  static propTypes = {};

  static defaultProps = {};

  constructor(props) {
    super(props);
    this.state = {
      url: OAURL,
      stuffId: sessionStorage.getItem("stuffId"),
      dataSource: [],
      dataLoading: false,
      total: 0,
      page: 1,
      pageSize: 10,
    };
    this.field = new Field(this);
    this.columns = [
      {
        title: '序号',
        render: this.rowIndex,
        width: 60,
      },
      {
        title: '任务标题',
        dataIndex: 'name',
      },
      {
        title: '任务类别',
        dataIndex: 'type',
        render: this.taskTypeFm,
        width: 100,
      },
      {
        title: '责任人',
        dataIndex: 'lname',
      },
      {
        title: '任务状态',
        dataIndex: 'state',
        width: 85,
        render: this.stateFm,
      },
      {
        title: '任务起止时间',
        dataIndex: 'name',
        render: this.taskTimeFm,
      },
      {
        title: '操作',
        width: 60,
        render: (value, index, record) => {
          return(
            <span style={{display: "flex", justifyContent:"space-around" }}>
              <ViewTaskInfo
                index={index}
                record={record}
              />
            </span>
          );
        },
      },
    ];
  }
  //初始化
  componentDidMount() {
    this.refreshTable();
  }
  //刷新表格, tabKey指定刷新那个tab页
  refreshTable = () =>{
    const {url,stuffId} = this.state;
    this.setState({
      dataLoading: true,
    });
    axios.get(url+'/task/all',{
      params:{
        stuffId: stuffId,
        page: 1,
        pageSize: 10,
        qtype: 2,
      }
    })
    .then((response) =>{
      var jsondata = response.data;
      if(jsondata.statusCode == 0){
        this.setState({
          dataSource: jsondata.mytask.data,
          dataLoading: false,
          page: 1,
          pageSize: 10,
          total: jsondata.mytask.total,
        });
      }
      else{
        Feedback.toast.sucess('后台数据返回失败');
      }
    })
    .catch((error) => {
      Feedback.toast.error('系统繁忙，请稍后重试:：'+error);
    });
  };

  //查询
  doSearch = () => {
    this.setState({
      dataLoading : true,
    });
    this.doAxiosMethod(1,10);
  };

  //清空
  doClear = () => {
   this.field.reset();
  };

  //翻页
  changePage = (pageNo) => {
   const { pageSize } = this.state;
   this.setState({
     dataLoading: true,
   });
   this.doAxiosMethod(pageNo, pageSize);
 };

 //改变显示记录数
 changePageSize = (pageSize) => {
   this.setState({
       dataLoading: true,
   });
   this.doAxiosMethod(1, pageSize);
 };

 doAxiosMethod = ( page, pageSize ) => {
   const { stuffId, url } = this.state;
   let searchValues = this.field.getValues();
   
   axios.get(url+'/task/all', {
   params: {
      qtype: 2,
      stuffId: stuffId,      
      page: page,
      pageSize: pageSize,
      name: searchValues.name,
      type: searchValues.type,
      lname: searchValues.lname,
   }
   })
   .then((response) => {
     let jsondata = response.data;
     
     if(jsondata.statusCode == 0){
       this.setState({
         dataLoading: false,
         dataSource: jsondata.mytask.data,
         page: page,
         pageSize: pageSize,
         total: jsondata.mytask.total,
       });
     }
     else{
       Feedback.toast.error('后台返回数据错误');
     }
    })
    .catch((error) => {
      Feedback.toast.error('系统繁忙，请稍后重试:' +error);
    });
 }

  rowIndex = (value, index) => {
    const { pageSize, page } = this.state;
    if(page == 1){
      return index + 1;
    }
    else {
      return (index+1)+ (page-1)*pageSize;  
    }  
  };
  //任务起止时间格式化
  taskTimeFm = (value, index, record) => {
    const stime = record.stime ? moment(record.stime).format('YYYY-MM-DD') : void (0);
    const etime = record.etime ? moment(record.etime).format('YYYY-MM-DD') : void (0);
    return stime + " 至 " + etime;
  };
  //任务状态格式化
  stateFm = (value) => {
    switch(value){
      case '0':
        return '已保存';
      case '1': 
        return '进行中';
      case '2':
        return '已完成';
      case '3':
        return '已终止';
    }
  };
  //任务类型格式化
  taskTypeFm = (value) => {
    if(value == '0'){
      return '常规工作';
    }
    else if(value == '1'){
      return '物品采购';
    }
    else if(value == '2'){
      return '人员培训';
    }
    else if(value == '3'){
      return '人员招聘';
    }
    else if(value == '4'){
      return '其他';
    }
  }

  render() {
    const { init } = this.field;
    const { dataSource, dataLoading, total, page, pageSize } = this.state;
    const formItemLayout = {
      labelCol : {
        fixedSpan: 6,
      }
    };
    return (
      <div>
        <IceContainer title="搜索">
        <Form field={this.field} direction="hoz">
          <Row justify="space-between">
            <Form.Item label="任务标题：">
              <Input {...init('name')} 
                style={{ width: 180 }}
              />
            </Form.Item>
            <Form.Item label="任务类别：" {...formItemLayout}>
                <Select {...init('type')} 
                    dataSource={[
                      { label: '请选择', value: '' },
                      { label: '常规任务', value: '0' },
                      { label: '物品采购', value: '1' },
                      { label: '人员培训', value: '2' },
                      { label: '人员招聘', value: '3' },
                      { label: '其他', value: '4' },
                    ]}
                  style={{ width: 180 }}
                />
            </Form.Item> 
            <Form.Item label="任务负责人：" {...formItemLayout}>
              <Input {...init('lname')} style={{ width: '180px'}}/>                   
            </Form.Item> 
            </Row>
          </Form> 
            <div align="center"> 
                <Button type="primary"  onClick={this.doSearch} size="medium" className="button">
                  <Icon type="search"/>
                  查询
                </Button>
                <Button style={{ marginLeft: '30px' }} type="secondary"  onClick={this.doClear} size="medium" className="button">
                  <Icon type="refresh"/>
                  重置
                </Button>
            </div>
        </IceContainer>
        <IceContainer>
          <CustomTable
            dataSource={dataSource}
            columns={this.columns}
            isLoading={dataLoading}
          />
          <Pagination
                pageSizeSelector="dropdown"
                onChange={this.changePage}
                onPageSizeChange={this.changePageSize}
                total={total}
                pageSize={pageSize}
                current={page}
                size="small"
                style={{ marginTop: "30px", marginLeft:"250px", marginRight:"50px"}}
          />
        </IceContainer>
      </div>
    );
  }
}