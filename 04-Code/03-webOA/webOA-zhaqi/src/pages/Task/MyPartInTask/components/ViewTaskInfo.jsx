import React, { Component } from 'react';
import { Icon, Dialog, Button, Form, Input, Field, Grid, DatePicker, Upload, Select, Feedback}  from '@icedesign/base';
import { OAURL } from '../../../../components/URL/OAURL';

const { Row, Col } = Grid;
const FormItem = Form.Item;
const taskType = [
  { label: "常规工作", value: '0' }, 
  { label: "物品采购", value: '1' }, 
  { label: "其他", value: '2' }
];
export default class ViewTaskInfo extends Component {
  static displayName = 'ViewTaskInfo';

  static defaultProps = {};

  constructor(props) {
    super(props);
    this.state = {
        visible: false,
        dataIndex: null,
        dataSource: [],
        url:OAURL,
        stuffId: sessionStorage.getItem("stuffId"),        
    };
    this.field = new Field(this,{
      autoUnmount: true
    });
  }

  onClose = () => {
      this.setState({
        visible: false,
      });
  };

  onOpen = (index, record) => {
    this.field.setValues({ ...record });
    this.setState({
        visible: true,
        dataIndex: index,
    });
  }

 

  editOrAdd = (index, record, opt) => {
   return(    
         <a onClick={() => this.onOpen(index, record)} title="查看">
         <Icon type="browse" style={{ color: "#3399ff", cursor:"pointer" }} size="small" />
       </a>
      );
 }

 
  render() {
  const init = this.field.init;
  const { index, record, opt } = this.props;
  const {stuffId} = this.state;
  const formItemLayout = {
    labelCol : {
      fixedSpan: 6,
    },
    wrapperCol :{
      span: 14,
    }
  };
  const footer = (
    <div>
      <Button onClick={this.onClose} type="primary">
        关闭
      </Button>
    </div>
  );
  return <div style={styles.buttonStyle}>
      {this.editOrAdd(index, record, opt)}
      <Dialog 
        style={{ width: 800 }} 
        visible={this.state.visible} 
        onClose={this.onClose} 
        footer={footer}
        footerAlign="center"
        title="查看任务信息"
      >
        <Form direction="ver" field={this.field}>
          <Row justify="space-between">
            <Col>
            <Input {...init("aid", { initValue: stuffId })} htmlType="hidden" /> {/* 员工id */}
            <FormItem label="任务标题：" {...formItemLayout}>
              <Input {...init("name")} autoComplete="off" readOnly/>
            </FormItem>
            </Col>
            <Col>
            <FormItem label="任务类别：" {...formItemLayout}>
              <Select className="next-form-text-align" 
                dataSource={taskType} 
                style={{ width: '100%' }}
                {...init("type")} 
                disabled
              />
            </FormItem>
            </Col>
          </Row>
          <Row>
            <Col>
            <FormItem label="任务分配人：" {...formItemLayout}>
              <Input {...init("aname")}  readOnly="true" style={{ width: 215}}/>
            </FormItem>
            </Col>
            <Col>
            <FormItem label="任务责任人：" {...formItemLayout}>
              <Input {...init("lname")} 
                style={{ width: 195, display: "inline-block"}} 
                readOnly="true"
                placeholder="请点击+选择责任人"
              />  
            </FormItem>
            
            </Col> 
          </Row>  
          <Row justify="space-between">
            <Col>
            <FormItem label="任务参与人：" {...formItemLayout}>
              <Input {...init("pname")}  style={{ width: "80%" }} readOnly="true"
                placeholder="请点击+选择参与人"
              />
            </FormItem>
            </Col>
          </Row>            
          <Row>
            <FormItem label="任务期限：" {...formItemLayout}>
              <DatePicker {...init("stime")} 
              disabled
              />
            </FormItem>
            <FormItem label="至" labelCol={{ fixedSpan: 2}}>
              <DatePicker {...init("etime") }
              disabled
              />
            </FormItem>
          </Row>
          <Row justify="space-between">
            <Col>
            <FormItem label="任务描述：" {...formItemLayout}>
              <Input multiple style={{ width: "100%" }} {...init("description")} readOnly />
            </FormItem>
            </Col>
          </Row>
          <Row>
            {/* <FormItem label="附件：" {...formItemLayout}>
            <Upload                   
                action={url+"/file/upload"}
                onSuccess={this.uploadSuccess}               
                onError={this.uploadError}
                limit={1}
              >
                <Button type="primary">
                  点击上传附件
                </Button>
            </Upload>
            </FormItem> */}
          </Row>
        </Form>
      </Dialog>
    </div>;
  }
}
const styles = {
    buttonStyle:{ 
        display: 'inline-block', 
        marginRight: '2px',
    }
};