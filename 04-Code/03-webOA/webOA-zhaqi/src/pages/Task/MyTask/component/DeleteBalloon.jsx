import React, { Component } from 'react';
import {Feedback, Icon, Button, Balloon } from '@icedesign/base';
import axios from 'axios';
import { OAURL } from '../../../../components/URL/OAURL';

export default class DeleteBalloon extends Component {

  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      url: OAURL
    };
  }

  handleHide = (visible, code, id) => {
    const {url}=this.state;
    if (code === 1) {
      axios.get(url+'/task/delete', {
        params: {
          tid: id,
        }
      })
      .then((response) => {
        var jsondata = response.data;
        if(jsondata.statusCode == 0){
          //后台删除成功，则更新table
          this.props.refreshTable();
          Feedback.toast.success("删除成功");
        }
        else{
          Feedback.toast.error("删除失败");
        }
      })
      .catch((error) => {
        Feedback.toast.error("ajax请求出错: "+error);
      });
    }
    this.setState({
        visible:false,
    });
  };

  handleVisible = (visible) => {
    this.setState({ visible });
  };

  render() {
    const { id } = this.props;
    const visibleTrigger = (
      <a title="删除">
        <Icon size="small" type="ashbin" style={{ color: "#3399ff",cursor:"pointer"}}/>
      </a>
    );
    const content = (
      <div>
        <div style={styles.contentText}>确认删除？</div>
        <Button
          id="confirmBtn"
          size="small"
          type="normal"
          shape="warning"
          style={{ marginRight: '5px' }}
          onClick={(visible) => this.handleHide(visible, 1, id)}
        >
          确认
        </Button>
        <Button
          id="cancelBtn"
          size="small"
          onClick={(visible) => this.handleHide(visible, 0, id)}
        >
          取消
        </Button>
      </div>
    );

    return (
      <Balloon
        trigger={visibleTrigger}
        triggerType="click"
        visible={this.state.visible}
        onVisibleChange={this.handleVisible}
      >
        {content}
      </Balloon>
    );
  }
}

const styles = {
  contentText: {
    padding: '5px 0 15px',
  },
};
