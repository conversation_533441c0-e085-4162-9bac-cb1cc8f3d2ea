import React, { Component } from 'react';
import { Icon, Dialog, Button, Form, Input, Field, Grid, DatePicker, Upload, Select, Feedback}  from '@icedesign/base';
import axios from 'axios';
import qs from 'qs';
import PublicChooseUser from '../../../../components/PublicChooseUser/PublicChooseUser';
import { OAURL } from '../../../../components/URL/OAURL';

const { Row, Col } = Grid;
const FormItem = Form.Item;
const taskType = [
  { label: "请选择", value: '' }, 
  { label: "常规工作", value: '0' }, 
  { label: "物品采购", value: '1' }, 
  { label: '人员培训', value: '2' },
  { label: '人员招聘', value: '3' },
  { label: '稽查任务', value: '4' },
  { label: '其他', value: '5' },
];
export default class MyTaskInfo extends Component {
  static displayName = 'MyTaskInfo';

  static defaultProps = {};

  constructor(props) {
    super(props);
    this.state = {
        visible: false,
        dataIndex: null,
        dataSource: [],
        url:OAURL,
        stuffId: sessionStorage.getItem("stuffId"),        
    };
    this.field = new Field(this,{
      autoUnmount: true
    });
  }

  onClose = () => {
      this.setState({
        visible: false,
      });
  };

  onOpen = (index, record) => {
    this.field.setValues({ ...record });
    this.setState({
        visible: true,
        dataIndex: index,
    });
  }

  editOrAdd = (index, record, opt) => {
    if(opt == 'edit'){
      return(
         <a onClick={() => this.onOpen(index, record)} disabled={record.state != 0} title="编辑">
         <Icon type="edit" style={{ color: "#3399ff", cursor:"pointer" }} size="small" />
       </a>
      );
    }
    else{
      return(
        <Button type="primary" onClick={() => this.onOpen(index, record)} className="button">
          <Icon type="add"/>
            新增
        </Button>
      );
    }
  }

  //保存
  myTaskSave = () => {
    const { stuffId, url } = this.state;
    this.field.validate((errors, values) => {
      if (errors) {
        Feedback.toast.error("请填入必填项！");
        return;
      }
      //后台保存成功，再更新table
      axios({
        method: 'post',
        url: url+'/task/addorupdate',
        data: qs.stringify(values),
      })
      .then((response) => {
        var jsondata = response.data;
        if(jsondata.statusCode == 0){
          Feedback.toast.success("保存成功");
          this.props.refreshTable();
          this.setState({
            visible: false,
          });
        }
        else{
          Feedback.toast.error("保存失败");
        }
      })
      .catch((error) => {
        Feedback.toast.error("ajax请求异常 " + error)
      });
    });
  };
  
  //时间格式化
  formateDate = (value, str) => {
    return str;
  }
  //任务责任人数据回填
  chooseInchargeUser = (rdata) => {
    let userNames = '';
    let userIds = '';
    for(let i=0; i<rdata.length; i++){
      userNames += rdata[i].label + ',';
      userIds += rdata[i].value + ',';
    }
    this.field.setValue('lname', userNames.substring(0,userNames.length-1));
    this.field.setValue('lid', userIds.substring(0, userIds.length-1));
  };
  //任务参与人数据回填
  chooseInpartUser = (rdata) => {
    let userNames = '';
    let userIds = '';
    for(let i=0; i<rdata.length; i++){
      userNames += rdata[i].label + ',';
      userIds += rdata[i].value + ',';
    }
    this.field.setValue('pname', userNames.substring(0,userNames.length-1));
    this.field.setValue('pid', userIds.substring(0, userIds.length-1));
  };

  render() {
  const init = this.field.init;
  const { index, record, opt } = this.props;
  const {stuffId} = this.state;
  const formItemLayout = {
    labelCol : {
      fixedSpan: 6,
    },
    wrapperCol :{
      span: 14,
    }
  };
  const footer = (
    <div>
      <Button type="primary" onClick={this.myTaskSave}>
        保存
      </Button>
      <Button onClick={this.onClose} style={{ marginLeft: 20 }}>
        取消
      </Button>
    </div>
  );
  return <div style={styles.buttonStyle}>
      {this.editOrAdd(index, record, opt)}
      <Dialog 
        style={{ width: 800 }} 
        visible={this.state.visible} 
        onClose={this.onClose} 
        footer={footer}
        footerAlign="center"
        title="任务信息"
      >
        <Form direction="ver" field={this.field}>
          <Row justify="space-between">
            <Col>
            <Input {...init("aid", { initValue: stuffId })} htmlType="hidden" /> {/* 员工id */}
            <FormItem label="任务标题：" {...formItemLayout}>
              <Input {...init("name", { rules: [{ required: true, message: "必填" }]})} placeholder="请输入" autoComplete="off" />
            </FormItem>
            </Col>
            <Col>
            <FormItem label="任务类别：" {...formItemLayout}>
              <Select 
                dataSource={taskType} 
                style={{ width: '100%' }}
                {...init("type", { rules: [{ required: true, message: "必选" }] })} 
              />
            </FormItem>
            </Col>
          </Row>
          <Row>
            <Col>
            <FormItem label="任务分配人：" {...formItemLayout}>
              <Input {...init("aname", { initValue: sessionStorage.getItem("realName"), rules: [{ required: true, message: "必填" }] })}  readOnly="true" style={{ width: '100%' }}/>
            </FormItem>
            </Col>
            <Col>
            <FormItem label="任务责任人：" {...formItemLayout}>
              <Input {...init("lid")} htmlType="hidden"/>             
              <Input {...init("lname", { rules: [{ required: true, message: "必填" }]})} 
                style={{ width: 195, display: "inline-block"}} 
                readOnly="true"
                placeholder="请点击+选择责任人"
              />  
              <PublicChooseUser
                type='0'
                chooseInchargeUser={this.chooseInchargeUser}
              />
            </FormItem>
            </Col> 
          </Row>  
          <Row justify="space-between">
            <Col>
            <FormItem label="任务参与人：" {...formItemLayout}>
              <Input {...init("pid")} style={{ display: "none"}}/>
              <Input {...init("pname")}  style={{ width: "80%" }} readOnly="true"
                placeholder="请点击+选择参与人"
              />
              <PublicChooseUser
                type='1'
                chooseInpartUser={this.chooseInpartUser}
              />
            </FormItem>
            </Col>
          </Row>            
          <Row>
            <FormItem label="任务期限：" {...formItemLayout}>
              <DatePicker {...init("stime", { getValueFromEvent: this.formateDate,
                  rules: [{ required: true, message: "必填" }]})} 
              />
            </FormItem>
            <FormItem label="至" labelCol={{ fixedSpan: 2}}>
              <DatePicker {...init("etime", {getValueFromEvent: this.formateDate })}
              />
            </FormItem>
          </Row>
          <Row justify="space-between">
            <Col>
            <FormItem label="任务描述：" {...formItemLayout}>
              <Input multiple style={{ width: 500 }} {...init("description")} />
            </FormItem>
            </Col>
          </Row>
          <Row>
          </Row>
        </Form>
      </Dialog>
    </div>;
  }
}
const styles = {
    buttonStyle:{ 
        display: 'inline-block', 
        marginRight: '2px',
    }
};