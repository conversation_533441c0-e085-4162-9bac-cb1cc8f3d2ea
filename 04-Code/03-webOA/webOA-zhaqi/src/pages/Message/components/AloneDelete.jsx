import React, { Component } from 'react';
import { Button, Balloon, Icon, Feedback } from '@icedesign/base';
import axios from 'axios';
import { OAURL } from '../../../components/URL/OAURL';
import qs from 'qs';
export default class AloneDelete extends Component {

  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      url: OAURL,
    };
  }

  componentWillMount(){
    
  }

  handleHide = (visible, ids, code) => {
    const { url } = this.state;
    if (code === 1) {
      let values = {};
      values.ids = ids;
      axios({
        method: 'post',
        url: url + '/hqmessage/deleteMessage',
        data: values
      })
      .then((response) => {
        let jsondata = response.data;
        if (jsondata.statusCode == 0) {
          Feedback.toast.success('删除成功');
          this.props.refreshTable();
          this.setState({
            visible: false,
          });
        }else{
          Feedback.toast.sucerrorcess(jsondata.msg);
        }
      })
      .catch((error) => {
        Feedback.toast.error('ajax请求异常：' + error);
      });
    }
    this.setState({
      visible: false,
    });
  };

  handleVisible = (visible) => {
    let ids = this.props.record;
    if(ids == ''){
      Feedback.toast.error('至少选择一条消息');
    }else{
      this.setState({ visible });
    }
    
  };

  render() {
    const  ids  = this.props.record;
    const visibleTrigger = (
      <Button
        className="button"
        type="primary"
        shape="text"
      >
        删除
      </Button>
    );
    const content = (
      <div>
        <div style={styles.contentText}>确认删除？</div>
        <Button
          id="confirmBtn"
          size="small"
          type="normal"
          shape="warning"
          style={{ marginRight: '5px' }}
          onClick={(visible) => this.handleHide(visible, ids, 1)}
        >
          确认
        </Button>
        <Button
          id="cancelBtn"
          size="small"
          onClick={(visible) => this.handleHide(visible, ids, 0)}
        >
          关闭
        </Button>
      </div>
    );

    return (
      <div>
        <Balloon
          trigger={visibleTrigger}
          triggerType="click"
          visible={this.state.visible}
          onVisibleChange={this.handleVisible}
        >
          {content}
        </Balloon>
      </div>
      
    );
  }
}

const styles = {
  contentText: {
    padding: '5px 0 15px',
  },
};
