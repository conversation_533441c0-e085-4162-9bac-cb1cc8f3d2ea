/* eslint-disable react/jsx-tag-spacing */
import React, { Component } from 'react';
import { Dialog, Button, Form, Input, Field, DatePicker, Feedback } from '@icedesign/base';

const FormItem = Form.Item;

export default class EidtEducateDialog extends Component {
  static displayName = 'EidtEducateDialog';

  static defaultProps = { };

  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      dataIndex: null,
    };
    this.field = new Field(this);
  }

  workFormSubmit = () => {
    this.field.validate((errors, values) => {
      if (errors) {
        Feedback.toast.error('Errors in form!!!');
        return;
      }
      const { dataIndex } = this.state;
      this.props.getFormValues(dataIndex, values);
      this.setState({
        visible: false,
      });
    });
  };

  onOpen = (index,record) => {
    this.field.setValues({ ...record });
    this.setState({
      visible: true,
      dataIndex: index,
    });
  };

  onCloseDialog = () => {
    this.setState({
      visible: false,
    });
  };
  
  render() {
    const init = this.field.init;
    const { index, record } = this.props;
    const formItemLayout = {
      labelCol: {
        fixedSpan: 7,
      },
      wrapperCol: {
        span: 14,
      },
    };
    const footer = (
      <div>
      <Button type="primary" onClick={this.workFormSubmit}>
         确定
      </Button>
      <Button onClick={this.onCloseDialog} style={{ marginLeft: 20}}>
         取消
      </Button>
      </div>
    );
    return (
      <div style={styles.editDialog}> 
        <Button size="small" type="primary" onClick={() => this.onOpen(index,record)}> 
          编辑
        </Button>
        <Dialog
          style={{ width: 500 }}
          visible={this.state.visible}
          closable="esc,mask,close"
          footer={footer}
          footerAlign="center"
          onClose={this.onCloseDialog}
          title="编辑工作经历"
        >
          <Form direction="ver" field={this.field}>
            <Input {...init("sid")} htmlType="hidden"/>
            <FormItem label="开始时间：" {...formItemLayout}>
              <DatePicker {...init('stime', { getValueFromEvent: this.formateDate, rules: [{ required: true, message: '必填' }] })} style={styles.widthStyle}/>
            </FormItem>
            <FormItem label="结束时间：" {...formItemLayout}>
              <DatePicker {...init('etime', { getValueFromEvent: this.formateDate, rules: [{ required: true, message: '必填' }] })} style={styles.widthStyle}/>
            </FormItem>
            <FormItem label="公&emsp;&emsp;司：" {...formItemLayout}>
              <Input
                {...init('company', { rules: [{ required: true, message: '必填' }] })}
                style={styles.widthStyle}
              />
            </FormItem>
            <FormItem label="职&emsp;&emsp;位：" {...formItemLayout}>
              <Input
                {...init('role', { rules: [{ required: true, message: '必填' }] })}
                style={styles.widthStyle}
              />
            </FormItem>
            <FormItem label="备&emsp;&emsp;注：" {...formItemLayout}>
              <Input
                {...init('remarks', { rules: [{ required: true, message: '必填' }] })}
                style={styles.widthStyle}
              />
            </FormItem>
          </Form>
        </Dialog>
      </div>
    );
  }
}

const styles = {
  widthStyle:{
    width: "200px"
  },
  editDialog:{
    display: 'inline-block',
    marginRight: '5px',
  }
}