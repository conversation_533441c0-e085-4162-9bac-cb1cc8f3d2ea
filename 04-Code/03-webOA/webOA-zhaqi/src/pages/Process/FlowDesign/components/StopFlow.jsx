import React, { Component } from 'react';
import {Feedback, Icon, Button, Balloon } from '@icedesign/base';
import axios from 'axios';
import { OAURL } from '../../../../components/URL/OAURL';
export default class StopFlow extends Component {

  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      url: OAURL,
      status: '',
    };
  }

  //初始化获取数据
  componentWillMount() {
    let record = this.props.record;
    this.setState({
      status : record.status
    })
  };

  //停用
  handleStop = (visible, id, code) => {
    const { url } = this.state;
    if (code == 1) {
      axios({
        method: "post",
        url: url + "/flowTemplate/setStateDisable",
        data: {
          id : id,
          status: 2
        }
      })
      .then((response) => {
        let msg = response.data.msg;
        let statusCode = response.data.statusCode;
        if( statusCode == 0){
          Feedback.toast.success(msg);
          this.props.refreshTable();
          this.setState({
            status: 2
          })
        }
        else{
          Feedback.toast.error(msg);
        }
      })
      .catch((error) => {
        Feedback.toast.error("系统繁忙，请稍后再试");
      });
    }
    this.setState({
      visible: false,
    });
  };

  //启用
  handlePlay = (visible, id, code) => {
    const { url } = this.state;
    if (code == 1) {
      axios({
        method: "post",
        url: url + "/flowTemplate/setStateDisable",
        data: {
          id : id,
          status: 1
        }
      })
      .then((response) => {
        let msg = response.data.msg;
        let statusCode = response.data.statusCode;
        if( statusCode == 0){
          Feedback.toast.success(msg);
          this.props.refreshTable();
          this.setState({
            status: 1
          })
        }
        else{
          Feedback.toast.error(msg);
        }
      })
      .catch((error) => {
        Feedback.toast.error("系统繁忙，请稍后再试");
      });
    }
    this.setState({
      visible: false,
    });
  };

  handleVisible = (visible) => {
    this.setState({ visible });
  };

  render() {
    const { id } = this.props.record;
    const stopIcon = (
      <a title="停用">
        <Icon size="medium" type="stop" style={{ color: "#FF3232",cursor:"pointer"}}/>
      </a>
    );
    const playIcon = (
      <a title="启用">
        <Icon size="medium" type="play" style={{ color: "#2ECA9C",cursor:"pointer"}}/>
      </a>
    );

    const contentStop = (
      <div>
        <div style={styles.contentText}>确认停用吗？</div>
        <Button
          id="confirmBtn"
          size="small"
          type="normal"
          shape="warning"
          style={{ marginRight: '5px' }}
          onClick={visible => this.handleStop(visible, id, 1)}
        >
          确认
        </Button>
        <Button style={{ display: 'inline-block'}}
          id="cancelBtn"
          size="small"
          onClick={visible => this.handleStop(visible, id, 0)}
        >
          取消
        </Button>
      </div>
    );


    const contentPlay = (
      <div>
        <div style={styles.contentText}>确认启用吗？</div>
        <Button
          id="confirmBtn"
          size="small"
          type="normal"
          shape="warning"
          style={{ marginRight: '5px' }}
          onClick={visible => this.handlePlay(visible, id, 1)}
        >
          确认
        </Button>
        <Button style={{ display: 'inline-block'}}
          id="cancelBtn"
          size="small"
          onClick={visible => this.handlePlay(visible, id, 0)}
        >
          取消
        </Button>
      </div>
    );

    
    return (
      <div>
        {
          this.state.status == 1 ? (
            <Balloon
              trigger={stopIcon}
              triggerType="click"
              visible={this.state.visible}
              onVisibleChange={this.handleVisible}
              style={{ width: 160 }}
            >
              {contentStop}
            </Balloon>
          ) : (
            <Balloon
              trigger={playIcon}
              triggerType="click"
              visible={this.state.visible}
              onVisibleChange={this.handleVisible}
              style={{ width: 160 }}
            >
              {contentPlay}
            </Balloon>
          )
        }
      </div>
      
    );
  }
}

const styles = {
  contentText: {
    padding: '5px 0 15px',
  },
};
