import React, { Component } from 'react';
import {Icon, Upload, Feedback, Button, Form, Input, Field, Grid,Table, Tab } from '@icedesign/base';
import axios from 'axios';
import qs from 'qs';
import { OAURL } from '../../../../components/URL/OAURL';
import IceContainer from '@icedesign/container';
import {Link} from 'react-router-dom';
import FoundationSymbol from 'foundation-symbol';
import FormDesign from './FormDesign';
import FlowNodes from './FlowNodes';

const { Row, Col } = Grid;
const TabPane = Tab.TabPane;

export default class FlowFormDesignInfo extends Component {
  static displayName = "FlowFormDesignInfo";

  constructor(props) {
    super(props);
    this.state = {
      createId: sessionStorage.getItem("stuffId"),
      createName: sessionStorage.getItem("realName"),
      tabKey: "basicInfo",
      flowNodes: [],
      formList: [],
      tabKeyArray:[],
    };
    this.field = new Field(this, { autoUnmount: true});
  }

  //初始化获取下拉框数据
  componentWillMount() {
    const param = this.props.location.state
    if (param == undefined) {
        this.props.history.push('/process/flowDesign');
        return;
    }
    if(param.opt == 'edit'){
      const record = param.record;
      this.getFlowInfo(record.id);
    }
  }

  //根据流程id获取动态表单和流程节点数据
  getFlowInfo = (id) => {
    let values = {};
    values.id = id;
    axios({
      method: 'post',
      url: `${OAURL}/flowTemplate/findById`,
      data: qs.stringify(values),
    })
    .then( response => {
      let jsondata = response.data;
      this.field.setValues({...jsondata})
      this.setState({
        flowNodes: jsondata.nodeList,
        formList: jsondata.dynamicFormList,
      })
    })
    .catch(error => {
      Feedback.toast.error('系统繁忙，请稍后重试'+error);
    })
  }


  //切换tab页
  handleTabChange = (key) => {
    const { tabKeyArray } = this.state;
    if(tabKeyArray.indexOf(key) == -1){
      tabKeyArray.push(key);
    }
    this.setState({
      tabKey: key,
      tabKeyArray: tabKeyArray,
    });
  };

  publish = () =>{
    const { flowNodes, formList } = this.state;
    var flowDesignNodes = null;
    var formDesiagnList = null;
    try {
      flowDesignNodes = this.refs.flowNodes.getFlowNodes();
      if(flowDesignNodes == null || flowDesignNodes.length == 0){
        Feedback.toast.error('空的审批节点不允许保存');
        return;
      }
      for(let i=0; i<flowDesignNodes.length; i++){
        flowDesignNodes[i].location = i;
      }
    } catch (error) {
      flowDesignNodes = flowNodes;
    }

    try {
      formDesiagnList = this.refs.FormDesign.getFormList();
      if(formDesiagnList == null || formDesiagnList.length == 0){
        Feedback.toast.error('空的表单不允许保存');
        return;
      }
      for(let i=0; i<formDesiagnList.length; i++){
        formDesiagnList[i].location = i;
        formDesiagnList[i].propertyName = "property"+i;
      }
    } catch (error) {
      formDesiagnList = formList;
    }

    let flowBasic = this.field.getValues();
    const { createId, createName } = this.state;
    flowBasic.createId = createId;
    flowBasic.createName = createName;
    flowBasic.nodeList = flowDesignNodes;
    flowBasic.dynamicFormList = formDesiagnList;
    axios({
      method: 'post',
      url: `${OAURL}`+'/flowTemplate/addFlowTemplate',
      data: flowBasic,
    })
    .then(response => {
      let jsondata = response.data;
      if(jsondata.statusCode == 0){
        Feedback.toast.success('发布成功');
        this.props.history.push('/process/flowDesign');
      }
      else{
        Feedback.toast.error(jsondata.msg);
      }
    })
    .catch(error => {
      Feedback.toast.error("系统繁忙，请稍后重试"+error);
    })
  }

  render() {
    const init = this.field.init;
    const { searchValue, flowNodes, formList } = this.state;
    const formItemLayout = {
      labelCol: {
        fixedSpan: 6
      },
    };

    const publishContent = (
      <Button type="primary" onClick={this.publish} className="button"  size="large">
        发布
      </Button>
    );
    return (
      <div>
        <div style={{ marginBottom: 5 }}>
            <Link to={{
                pathname: `/process/flowDesign`,
                state:{searchValue: searchValue}
            }}>
                <Button className="button" type="primary">
                    <FoundationSymbol type="backward" style={{ marginRight: 5 }} size="small" />
                    返回
                </Button>
            </Link>
        </div>
        <IceContainer>
          <Tab onChange={this.handleTabChange} type="bar" tabBarExtraContent={publishContent}>
            <TabPane tab="第1步：基础设置" key="basicInfo"> 
              <Form field={this.field} style={{marginTop: 20}}>
                <Input {...init('id')} htmlType="hidden"/>
                <Row>
                  <Form.Item label="审批名称：" {...formItemLayout}> 
                    <Input {...init('name', { rules: [{ required: true, message: "必填"}]})}  
                      style={{ width: 400 }} placeholder="请输入"/>
                  </Form.Item> 
                </Row>
                <Row>
                  <Form.Item label="审批说明：" {...formItemLayout}>
                    <Input multiple {...init('remarks')}  style={{ width: 400 }} placeholder="请输入"/>
                  </Form.Item> 
                </Row>
              </Form> 
            </TabPane>
            <TabPane tab="第2步：表单设计" key="formDesign"> 
              <FormDesign ref="FormDesign" formList={formList}/>
            </TabPane>
            <TabPane tab="第3步：流程设计" key="flowDesign" flowNodes={flowNodes}> 
              <FlowNodes ref="flowNodes" flowNodes={flowNodes}/>
            </TabPane>
          </Tab>
        </IceContainer>
             
      </div>
    );
  }
}
