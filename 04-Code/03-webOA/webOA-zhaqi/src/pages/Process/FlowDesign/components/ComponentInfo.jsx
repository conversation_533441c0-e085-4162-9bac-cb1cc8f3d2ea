import React, { Component } from 'react';
import { Dialog, Button, Form, Input, Field, DatePicker, Feedback, Select, Checkbox } from '@icedesign/base';
import './flowDesign.css';

const FormItem = Form.Item;
const { Group: CheckboxGroup } = Checkbox;

export default class ComponentInfo extends Component {
  static displayName = 'ComponentInfo';

  static defaultProps = { };

  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      stuffId: sessionStorage.getItem("stuffId"),
      checked: false,
    };
    this.field = new Field(this,{
      autoUnmount: true
    });
  }

  save = () => {
    this.field.validate((errors, values) => {
      if (errors) {
        Feedback.toast.error('请输入控件标题');
        return;
      }
      this.props.addRowDate(values);
      this.setState({
        visible: false,
        checked: false,
      });
    });
  };

  formateDate = (value, str) =>{
    return str;
  };
  
  onOpen = () => {
    this.setState({
      visible: true,
    });
  };

  onCloseDialog = () => {
    this.setState({
      visible: false,
      checked: false,
    });
  };

  onChange = (checked) => {
    this.setState({
      checked: checked,
    });
    if(checked){
      this.field.setValue('isRequired', 1)
    }
    else{
      this.field.setValue('isRequired', 0)
    }
  }

  render() {
    const init = this.field.init;
    const { title, type } = this.props;
    const { visible, checked } = this.state;
    const formItemLayout = {
      labelCol: {
        fixedSpan: 7,
      },
      wrapperCol: {
        span: 14,
      },
    };
    
    const footer = (
      <div>
      <Button type="primary" onClick={this.save}>
         确定
      </Button>
      <Button onClick={this.onCloseDialog} style={{ marginLeft: 20}}>
         取消
      </Button>
      </div>
    );
    return (
      <div>
        <Button size="small" type="primary" className="button" onClick={() => this.onOpen()}>
          新增
        </Button>
        <Dialog
          style={{ width: 500 }}
          visible={visible}
          onClose={this.onCloseDialog}
          footer={footer}
          footerAlign="center"
          title={title}
        >
          <Form direction="ver" field={this.field}>
            <Input {...init('componentType', { initValue: type})} htmlType="hidden"/> 
            <FormItem label="标&emsp;&emsp;题：" {...formItemLayout}>
              <Input {...init('title', { rules: [{ required: true, message: '必填' }] })} className="inputStyle"/>
            </FormItem>
            <FormItem label="提示文字："{...formItemLayout}>
              <Input {...init('placeHolder')}  className="inputStyle"/>
            </FormItem>
            {
                type == 'select_single' ?
                  <FormItem label="选&emsp;&emsp;项："{...formItemLayout}>
                    <Input {...init('options', { rules: [{ required: true, message: '必填' }] })}  className="inputStyle" placeholder="请用英文逗号分隔每个选项"/>
                  </FormItem>
                : 
                void(0)
            }
            {
                type == 'select_multiple' ?
                  <FormItem label="选&emsp;&emsp;项："{...formItemLayout}>
                    <Input {...init('options', { rules: [{ required: true, message: '必填' }] })}  className="inputStyle" placeholder="请用英文逗号分隔每个选项"/>
                  </FormItem>
                : 
                void(0)
            }
            <FormItem label="是否必填："{...formItemLayout}>
              <Checkbox 
                checked={checked}
                onChange={(checked) => this.onChange(checked)}
                className="checkbox"
              />
            </FormItem>
          </Form>
        </Dialog>
      </div>
    );
  }
}

const styles = {
  widthStyle:{
    width: "200px"
  }
}
