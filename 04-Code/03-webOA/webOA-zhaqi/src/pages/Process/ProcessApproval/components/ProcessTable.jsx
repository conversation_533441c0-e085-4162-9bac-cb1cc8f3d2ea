import React, { Component } from 'react';
import { Tab, <PERSON><PERSON>, <PERSON><PERSON>back, <PERSON>, Icon, Pagination, Form, moment  } from '@icedesign/base';
import axios from 'axios';
import qs from 'qs';
import IceContainer from '@icedesign/container';
import CustomTable from '../../../../components/CustomTable/CustomTable';
import ApproveGetMaterialInfo from './ApproveGetMaterialInfo';
import ApproveGoBusinessInfo from './ApproveGoBusinessInfo';
import ApproveReimburseInfo from './ApproveReimburseInfo';
import ApproveLoanApplyInfo from './ApproveLoanApplyInfo';
import ApproveLeaveWorkInfo from './ApproveLeaveWorkInfo';
import ApproveAddWorkInfo from './ApproveAddWorkInfo';
import { OAURL } from '../../../../components/URL/OAURL';
import ViewApproveAddWorkInfo from './ViewApproveAddWorkInfo';
import ViewApproveLeaveWorkInfo from './ViewApproveLeaveWorkInfo';
import ViewApproveGetMaterialInfo from './ViewApproveGetMaterialInfo';
import ViewApproveLoanApplyInfo from './ViewApproveLoanApplyInfo';
import {Link} from 'react-router-dom';
import ApproveOtherInfo from './ApproveOtherInfo';
import IceLabel from '@icedesign/label';

const TabPane = Tab.TabPane;

export default class ProcessTable extends Component {
  static displayName = "ProcessTable";

  static propTypes = {};

  static defaultProps = {};

  constructor(props) {
    super(props);
    this.state = {
      pendingDataSurce: [],
      approvedDataSource: [],
      dataLoading: true,
      page: 1,
      pageSize: 10,
      total: 10,
      tabKey: 'pending',
      url: OAURL,
      stuffId: sessionStorage.getItem("stuffId"),
    };
    this.field = new Field(this);
    this.columns = [
      {
        title: '序号',
        align: 'center',
        width: 80,
        render: (value, index) => {
          return index+1;
        }
      },
      {
        title: '流程名称',
        dataIndex: 'applyTitle',
        align:'center',
      },
      {
        title: '流程类别',
        dataIndex: 'applyType',
        align:'center',
        render: (value) => {
          switch(value){
            case '0' :
              return "加班申请";
            case '1' :
              return "请假申请";
            case '2' :
              return "材料申请";
            case '3' :
              return "采购申请";  
            case '4' :
              return "出差申请";  
            case '5' :
              return "报销申请";
            case '6' :
              return "借款申请";
            case '7' :
              return "自定义流程";
          }
        }
      },
      {
        title: '申请人',
        dataIndex: 'applicantName',
        align:'center',
      },
      {
        title: '申请时间',
        dataIndex: 'applyTime',
        align:'center',
        render: (value) => {
          if(value){
            return moment(value).format('YYYY-MM-DD hh:mm:ss');
          }
        }
      },
      {
        title: '操作',
        width: 80,
        align:'center',
        render: (value, index, record) => {
          //加班申请
          if(record.type == '0'){
            return(
              <span>
                <ApproveAddWorkInfo
                  record={record}
                  opt="flow"
                  refreshTable={() => this.getPendingList()}
                />
              </span>
            );
          }
          //请假申请
          else if(record.type == '1'){
            return(
              <span>
                 <ApproveLeaveWorkInfo
                  record={record}
                  opt="flow"
                  refreshTable={() => this.getPendingList()}
                />
              </span>
            );
          }
          //材料
          else if(record.type == '2'){
            return(
              <span>
                <ApproveGetMaterialInfo
                   opt="flow"
                   record={record}
                   refreshTable={() => this.getPendingList()}
                />
              </span>
            );
          }
          //采购
          else if(record.type == '3'){
            return(
              <span>              
                {
                  <Link to={{
                    pathname: './approval/approveBuyMaterial',
                    state: { opt: 'edit', record: record, from:"flow"}
                  }}>
                    <a href="">
                      <Icon title="签审" type="browse" style={{ color: "#3399ff", cursor: "pointer" }} size="small" />
                    </a>
                  </Link>
                }
              </span>
            );
          }
          //出差
          else if(record.type == '4'){
            return(
              <span>              
                <ApproveGoBusinessInfo
                  record={record}
                  opt="flow"
                  refreshTable={() => this.getPendingList()}
                />
              </span>
            );
          }
          //报销
          else if(record.type == '5'){
            return(
              <span>
                <ApproveReimburseInfo
                  record={record}
                  opt="flow"
                  refreshTable={() => this.getPendingList()}
                />
              </span>
            );
          }
          //借款
          else if(record.type == '6'){
            return(
              <span>
                <ApproveLoanApplyInfo
                  record={record}
                  opt="flow"
                  refreshTable={() => this.getPendingList()}
                />
              </span>
            );
          }
          //自定义流程
          else if(record.type == '7'){
            let record1 = {};
            record1.auditId = record.taskId;
            record1.type = '7';
            record1.applyId = record.applyId;
            return(
              <span>
                <ApproveOtherInfo
                  record={record1}
                  opt="flow"
                  refreshTable={() => this.getPendingList()}
                />
              </span>
            )
          }
        },
      },
    ];
    this.columns1 = [
      {
        title: '流程名称',
        dataIndex: 'applyTitle',
        align:'center',
      },
      {
        title: '流程类别',
        dataIndex: 'applyType',
        align:'center',
        render: (value) => {
          switch(value){
            case '0' :
              return "加班申请";
            case '1' :
              return "请假申请";
            case '2' :
              return "材料申请";
            case '3' :
              return "采购申请";  
            case '4' :
              return "出差申请";  
            case '5' :
              return "报销申请";
            case '6' :
              return "借款申请";
            case '7' :
              return "自定义流程";
          }
        }
      },
      {
        title: '申请人',
        dataIndex: 'applicantName',
        align:'center',
      },
      {
        title: '审批时间',
        dataIndex: 'auditTime',
        align:'center',
        render: (value) => {
          if(value){
            return moment(value).format('YYYY-MM-DD hh:mm:ss');
          }
        }
      },
      {
        title: '审批结果',
        dataIndex: 'auditResult',
        align:'center',
        render: (value) => {
          if(value == '2'){
            return <IceLabel status="danger">拒绝</IceLabel>
          }
          else{
            return <IceLabel status="success">同意</IceLabel>;
          }
        }
      },
      {
        title: '操作',
        align:'center',
        width: 80,
        render: this.rowOperateRender
      }
    ];
  }
  //初始化
  componentDidMount() {
    this.getPendingList();
    this.getApprovedListByPage(1,10);
  }

  /**
   * 获取待审批记录
   */
  getPendingList = () => {
    const { stuffId } = this.state;
    let url = `${OAURL}/process/getPenddingList`;
    let params = {};
    params.flag = '0';
    params.userId = stuffId;
    this.doAjaxMethod(url, params, 1);
  }

  /**
   * 分页获取已审批的记录
   */
  getApprovedListByPage = (page, pageSize) => {
    const { stuffId } = this.state;
    let url = `${OAURL}/process/getListByPage`;
    let params = {};
    params.flag = '1';
    params.userId = stuffId;
    params.page = page;
    params.pageSize = pageSize;
    this.doAjaxMethod(url, params, 2);
    this.setState({
      page: page,
      pageSize: pageSize,
    });
  }

  /**
   * 分页
   */
  changePage = (page) => {
    const { pageSize } = this.state;
    this.getApprovedListByPage(page, pageSize);
  }

  /**
   * 改变页面大小
   */
  changePageSize = (pageSize) => {
    this.getApprovedListByPage(1, pageSize);
  }

  /**
   * 发送ajax请求
   */
  doAjaxMethod = (url, params, flag) => {
    axios({
      method: 'post',
      url: url,
      data: qs.stringify(params)
    })
    .then(response => {
      let jsondata = response.data;
      this.setState({
        dataLoading: false,
      })
      if(jsondata.statusCode == 0){
        if(flag == 1){
          this.setState({
            pendingDataSurce: jsondata.obj,
          })
        }
        else{
          this.setState({
            approvedDataSource: jsondata.list,
            total: jsondata.total,
          })
        }
      }
    })
    .catch(error => {
      Feedback.toast.error('系统繁忙，请稍后重试：'+error);
    })
  }

  /**
   * 行操作按钮
   */
  rowOperateRender = ( value, index, record ) => {
    if(record.applyType == '0'){
      return(
        <span>
          <ViewApproveAddWorkInfo
            record={record}
          />
        </span>
      )
    }
    else if(record.applyType == '1'){
      return(
        <span>
          <ViewApproveLeaveWorkInfo
            record={record}
          />
        </span>
      )
    }
    else if(record.applyType == '2'){
      return(
        <span>
          <ViewApproveGetMaterialInfo
            record={record}
          />
        </span>
      )
    }
    else if(record.applyType == '3'){
      return(
        <span>              
          {
            <Link to={{
              pathname: './approval/approveBuyMaterial',
              state: { opt: 'edit', record: record, from:"flow"}
            }}>
              <a>
                <Icon title="查看审批详情" type="browse" style={{ color: "#3399ff", cursor: "pointer" }} size="small" />
              </a>
            </Link>
          }
        </span>
      );
    }
    else if(record.applyType == '4'){
      return(
        <span>
          <ApproveGoBusinessInfo
            record={record}
            opt="flow"
          />
        </span>
      )
    }
    else if(record.applyType == '5'){
      return(
        <span>
          <ApproveReimburseInfo
            record={record}
            opt="flow"
          />
        </span>
      )
    }
    else if(record.applyType == '6'){
      return(
        <span>
          <ViewApproveLoanApplyInfo
            record={record}
            opt="flow"
          />
        </span>
      )
    }
    else if(record.applyType == '7'){
      //自定义流程查询审批详情，不是和其他同一张表
      let record1 = {};
      record1.auditId = record.taskId;
      record1.type = '7';
      record1.applyId = record.applyId;
      return(
        <span>
          <ApproveOtherInfo
            record={record1}
            opt="flow"
          />
        </span>
      )
    }
  }
  
  //任务状态格式化
  stateFm = (value) => {
    switch(value){
      case '0':
        return '已保存';
      case '1': 
        return '进行中';
      case '2':
        return '已完成';
      case '3':
        return '已终止';
    }
  };
 
  //切换tab页
  handleTabChange = (key) => {
    this.setState({
      tabKey: key,
    });
    if(key == 'approved'){
      this.getApprovedListByPage(1,10);
    }
  };

  render() {
    const { init } = this.field;
    const { pendingDataSurce, approvedDataSource, total, page, pageSize, dataLoading } = this.state;
    const formItemLayout = {
      labelCol : {
        fixedSpan: 6,
      }
    };
    return (
      <div>
        <IceContainer>
           <Tab onChange={this.handleTabChange}>
            <TabPane tab="待审批" key="pending">
              {/* <Button type="primary" size="small"
                disabled={!this.state.selectedRowKeys.length}
              >
                同意
              </Button>
              <Button type="secondary" size="small" 
                style={{ marginLeft: 10 }}
                disabled={!this.state.selectedRowKeys.length}
              >
                拒绝
              </Button> */}
              <CustomTable
                dataSource={pendingDataSurce}
                columns={this.columns}
                isLoading={dataLoading}
                primaryKey="auditId"
              />

            </TabPane>
            <TabPane tab="已审批" key='approved'>
              <CustomTable
                dataSource={approvedDataSource}
                columns={this.columns1}
                isLoading={dataLoading}
              />
               <div style={{display:'flex',justifyContent:'flex-end'}}>
                <Pagination
                    pageSizeSelector="dropdown"
                    onChange={this.changePage}
                    onPageSizeChange={this.changePageSize}
                    total={total}
                    pageSize={pageSize}
                    current={page}
                    size="small"
                    style={{ textAlign: 'right', marginTop: 15 }}
                    pageSizeList={[10,30,50,100]}
                />        
                <div style={{lineHeight:'53px',marginLeft:10}}>共 {total} 条记录</div>
              </div>    
            </TabPane>
           </Tab>
        </IceContainer>
      </div>
    );
  }
}