import React, { Component } from 'react';
import {Timeline,Select,  Dialog, Button, Form, Input, Field, Grid, Feedback, Icon, Tab } from '@icedesign/base';
import axios from 'axios';
import { OAURL } from '../../../../components/URL/OAURL';

const { Row } = Grid;
const { Item: TimelineItem } = Timeline;
export default class ViewApproveBuyMaterialInfo extends Component {
  static displayName = 'ViewApproveBuyMaterialInfo';

  static defaultProps = {};

  constructor(props) {
    super(props);
    this.state = {
        visible: false,
        url: OAURL,
        userId: sessionStorage.getItem('stuffId'),
        userName: sessionStorage.getItem('realName'),
        tabKey: 'approveApply',
        approveRecord:[],
        reject:false,
        agree:false,
        attachment: '',
        fileName: '',
    };
    this.field = new Field(this);
  }

    //初始化获取数据
  componentWillMount(){
    this.getApprovalRecords();
  };

  getApprovalRecords = () => {
    const { url } = this.state; 
    const { record } = this.props;
    axios.get(url+'/process/status',{
        params:{
          applyId: record.applyId,
          type:record.type,
          n: Math.random(),
        }
      }
    )
    .then((response) => {
        let jsondata = response.data;
        if(jsondata.statusCode == 0 ){
            this.setState({
              approveRecord:jsondata.data,
            })
        }
        else{
            Feedback.toast.error(jsondata.msg);
        }
    })
    .catch((error) => {
        Feedback.toast.error('系统繁忙，请稍后重试：'+error);
    })
  }

  onClose = () => {
    this.setState({
      visible: false
    })
  };

  //审批查看申请单详情
  onOpen = (record) => {
    const { url } = this.state; 
    axios.get(url+'/process/applyDetail',{
        params:{
          applyId: record.applyId,
          type: record.type,
          n: Math.random(),
        }
      }
    )
    .then((response) => {
        let jsondata = response.data;
        if(jsondata.statusCode == 0 ){
           this.field.setValues({ ...jsondata.apply[0]});
           this.setState({
              visible: true,
              attachment: jsondata.apply[0].attachment,
              fileName: jsondata.apply[0].fileName,
           }); 
        }
        else{
            Feedback.toast.error('后台数据返回失败');
        }
    })
    .catch((error) => {
        Feedback.toast.error('系统繁忙，请稍后重试：'+error);
    })
  }

  onChange = (key) => {
    this.setState({
       tabKey: key,
    })
  }

  render() {
    const init = this.field.init;
    const { record, from } = this.props;
    const { approveRecord, attachment, fileName } = this.state;
    const formItemLayout = {
      labelCol : {
        fixedSpan: 6,
      }
    };

    const footer = (
      <div>
          <Button onClick={this.onClose} style={{ marginLeft: 30}}>
            关闭
          </Button>
      </div>
    );

    return (
    <div style={styles.buttonStyle}>
        <a onClick={ () => this.onOpen(record)} title="查看审批详情" >
          <Icon type="browse" style={{ color: "#3399ff",cursor:"pointer"}} size="small"/>
        </a>
        <Dialog 
          minMargin={10} 
          style={{ width: 700 }}
          visible={this.state.visible} 
          onClose={this.onClose} 
          footer={footer} 
          footerAlign="center"
        >
        <Tab onChange={this.onChange}>
            <Tab.TabPane tab="审批详情" key="approveApply">
                <Form direction="ver" field={this.field}>
                    <Row>
                    <Form.Item label="申请人：" {...formItemLayout}>
                        <Input {...init("userName")} readOnly />
                    </Form.Item>
                    <Form.Item label="部门：" {...formItemLayout}>
                        <Input {...init("deptName")} readOnly />
                    </Form.Item>
                    </Row>
                    {/* <Row>
                    <Form.Item label="岗位：" {...formItemLayout}>
                        <Input {...init("userPosition")} readOnly />
                    </Form.Item>
                    <Form.Item label="工号：" {...formItemLayout}>
                        <Input {...init("userJobNum")} readOnly />
                    </Form.Item>
                    </Row> */}
                    <Row>
                    <Form.Item label="材料名称：" {...formItemLayout}>
                        <Input {...init('materialId')} htmlType="hidden"/> 
                        <Input { ...init('materialName', { rules: [{ required: true, message: "必填" }]})}
                        readOnly
                    />
                    </Form.Item>
                    <Form.Item label="库存：" {...formItemLayout} >
                        <Input  {...init("materialNum") } readOnly />
                    </Form.Item>
                    </Row>
                    <Row>
                      <Form.Item label="材料类型：" {...formItemLayout}> 
                        <Select  {...init('materialType',)} placeholder='请输入品牌' readOnly style={{ width: 160 }}
                          dataSource={[
                            { label: '耗材', value:'0' },
                            { label: '小型设备', value:'1' },
                            { label: '大型设备', value:'2' },
                            { label: '其他', value:'3' },
                        ]}
                        />
                      </Form.Item>
                      <Form.Item label="品牌："  {...formItemLayout} > 
                        <Input  {...init('materialBrand',)} placeholder='请输入品牌' readOnly style={{ width: 160 }}/>
                      </Form.Item>
                    </Row>
                    <Row>
                    <Form.Item label="采购数量：" {...formItemLayout}>
                        <Input {...init("applyNum")} readOnly/>
                    </Form.Item>
                    <Form.Item label="单价：" {...formItemLayout} > 
                        <Input   {...init('price')} readOnly/>
                    </Form.Item>
                    </Row>
                    <Row>
                      <Form.Item label="供应商：" {...formItemLayout} >
                        <Input {...init("provider")} readOnly  style={{ width: 160 }}/>
                      </Form.Item>
                      <Form.Item label="型号："  {...formItemLayout} style={{ display: this.state.brandDisplay}}>
                        <Input 
                            {...init("modelType")}
                            style={{ width: 160 }} readOnly 
                        />
                      </Form.Item>
                    </Row>
                    <Row>
                    <Form.Item label="申请事由：" {...formItemLayout}>
                        <Input style={{ width: 440, height:50 }} 
                        {...init("remarks")}
                        readOnly
                        />
                    </Form.Item>
                    </Row>
                    <Row>
                      <Form.Item label="附件：" {...formItemLayout}>
                          <a href={`${OAURL}/file/download?fileName=`+attachment} 
                          className="next-form-text-align" 
                          style={styles.attachment}>                     
                          {fileName}
                          </a>
                      </Form.Item>
                    </Row>
                    {
                      from=='message' ?
                      void(0)
                      :
                      <div>
                        <Row>
                        <Form.Item label="审批意见：" {...formItemLayout}>
                          <Input style={{ width: 440, height: 50 }}
                            {...init("comment")}
                            value={record.comment}
                            readOnly
                          />
                        </Form.Item>
                      </Row>
                      <Row>
                        <Form.Item label="审批结果：" {...formItemLayout}>
                          <Select {...init("auditResult" )} disabled
                            dataSource={[
                              { value: '1', label: '同意'},
                              { value: '2', label: '拒绝'}
                            ]}
                            value={record.auditResult}
                            style={{ width: 160 }}
                          />
                        </Form.Item>
                      </Row>
                    </div>
                    }
                </Form> 
            </Tab.TabPane>
            <Tab.TabPane tab="审批记录" key="viewApproveRecord">
              <Timeline className="timeline">
                {approveRecord.map(item => (
                  <TimelineItem 
                    title={<div className="timeline-item-title"> {item.title} </div>} 
                    content={<div className="timeline-item-content"> {item.content}&nbsp;&nbsp;&nbsp;{item.result}&nbsp;&nbsp;&nbsp;{item.comment} </div>} 
                    time={<div className="timeline-item-time"> {item.time} </div>} 
                    state={item.state}
                    icon="account-filling"
              />
           ))}
          </Timeline>
            </Tab.TabPane>
        </Tab>
      </Dialog>
      </div>
    );
  }
}
const styles = {
    buttonStyle:{ 
        display: 'inline-block', 
        marginRight: '2px',
    }
};