import React, { Component } from 'react';
import {Timeline, Tab, Select, Icon, Upload, Feedback, Dialog, Button, Form, Input, Field, TreeSelect, Grid, Radio, DatePicker, moment, NumberPicker, Table } from '@icedesign/base';
import axios from 'axios';
import qs from 'qs';
import { OAURL } from '../../../../components/URL/OAURL';
import IceContainer from '@icedesign/container';
import {Link} from 'react-router-dom';
import FoundationSymbol from 'foundation-symbol';

const { Row, Col } = Grid;
const { Item: TimelineItem } = Timeline;

export default class ApproveBuyMaterialInfo extends Component {
  static displayName = "ApproveBuyMaterialInfo";

  constructor(props) {
    super(props);
    this.state = {
      url: OAURL,
      searchValue:{},
      record:{},
      dataSource:[],
      isApprove: null,
      agree: false,
      reject: false,
      auditId:'',
      from: '',
      tabKey: 'approveApply',
      approveRecord:[],
      userId: sessionStorage.getItem('stuffId'),
      userName: sessionStorage.getItem('realName'),
      department:[],
      departSelect:[]
    };
    this.field = new Field(this, { autoUnmount: true});
  }

  //初始化获取下拉框数据
  componentDidMount() {
    const param = this.props.location.state
    if (param == undefined) {
        this.props.history.push('/process/approval');
        return;
    }
    const record = param.record;
    this.getApplyDetail(record.applyId);
    this.getApprovedDetail(record.auditId);
    this.getApprovalRecords(record.applyId);
    this.field.setValues({...record});
    this.setState({
      searchValue: param.searchValue,
      auditId: record.auditId,
      from: param.from,
    });
    this.getDepartment();
  }

  //获取申请单详情
  getApplyDetail = (applyId) => {
    const { url } = this.state;
    axios({
      method: 'get',
      url: url+'/buyMaterial/view',
      params: {
        id: applyId,
        n: Math.random(),
      },
    })
    .then((response) => {
      let jsondata = response.data;
      if(jsondata.statusCode == 0){
        this.field.setValues({...jsondata.obj});
        this.setState({
          record: jsondata.obj,
          dataSource: jsondata.obj.applyMaterailList,
        });
      }
    })
    .catch((error) => {
      Feedback.toast.error('系统繁忙，请稍后重试:' +error);
    });
  }

  //根据审批记录ID，获取审批详情
  getApprovedDetail = (auditId) => {
    const { url } = this.state;
    axios({
      method: 'get',
      url: url+'/process/getReviewDetail',
      params: {
        auditId: auditId,
        n: Math.random(),
      },
    })
    .then((response) => {
      let jsondata = response.data;
      if(jsondata.statusCode == 0){
        if(jsondata.obj.isBigLeader == 'is'){
          this.setState({
            isBigLeader: true,
            isApprove: jsondata.obj.flag,
          });
        }else{
          this.setState({
            isBigLeader: false,
            isApprove: jsondata.obj.flag,
          });
        }

        this.field.setValue('comment',jsondata.obj.comment);
        this.field.setValue('auditResult',jsondata.obj.auditResult);
        
      }
    })
    .catch((error) => {
      Feedback.toast.error('系统繁忙，请稍后重试:' +error);
    });
  }

  //获取审批记录
  getApprovalRecords = (applyId) => {
    const { url } = this.state; 
    axios.get(url+'/process/status',{
        params:{
          applyId: applyId,
          type: '3',
          n: Math.random(),
        }
      }
    )
    .then((response) => {
        let jsondata = response.data;
        if(jsondata.statusCode == 0 ){
            this.setState({
              approveRecord:jsondata.data,
            })
        }
        else{
            Feedback.toast.error(jsondata.msg);
        }
    })
    .catch((error) => {
        Feedback.toast.error('系统繁忙，请稍后重试：'+error);
    })
  }

  //同意申请
  agreeApply = () => {
    if(this.state.isBigLeader == true){
      let departSelect = this.state.departSelect
      if(departSelect !=''){
        this.setState({
          agree:true,
        });
        const { url } = this.state;
        this.axiosMethod(url+'/buyMaterial/complete');
      }else{
        Feedback.toast.error('请选择采购部门');
      }
    }else{
      this.setState({
        agree:true,
      });
      const { url } = this.state;
      this.axiosMethod(url+'/buyMaterial/complete');
    }
  }

  //拒绝申请
  rejectApply = () => {
    this.setState({
      reject:true,
    });
    const { url } = this.state;
    this.axiosMethod(url+'/buyMaterial/reject');
  }

  //发送axios请求
  axiosMethod = (url) => {
    const { userId, userName, auditId, from , departSelect} = this.state;
    let comment = this.field.getValue('comment');

    let values = {}; 
    values.auditId = auditId;
    values.comment = comment;
    values.userId = userId;
    values.userName = userName;
    values.buyDeptId = departSelect.value;
    values.buyDeptName = departSelect.label;
    axios({
      method: 'post',
      url: url,
      data: qs.stringify(values)
    })
    .then((response) => {
       this.setState({
        agree:false,
        reject:false,
      });
      var jsondata = response.data;
      if(jsondata.statusCode == 0){
        Feedback.toast.success("审批完成");
        if(from == 'flow'){
          // setTimeout(() => {this.props.refreshTable()}, 500);
          this.props.history.push('/process/approval');
        }
        else{
          // setTimeout(() => {this.props.setMsgReaded()},500);
          this.props.history.push('/message');
        }
        this.setState({
          visible: false,
        });
      }
      else{
        Feedback.toast.error(jsondata.msg);
      }
    })
    .catch((error) => {
      this.setState({
        agree:false,
        reject:false,
      });
      Feedback.toast.error("ajax请求异常 " + error);
    });
  }

  onChange = (key) => {
    this.setState({
       tabKey: key,
    })
  }

  //获取部门名称
  getDepartment = () =>{
    const { url } = this.state;
    axios({
      method: 'get',
      url: url+'/depart/tree',
    })
    .then((response) => {
      let jsondata = response.data;
      if(jsondata.statusCode == 0){
        let array = jsondata.treeNode;
        this.setState({
          department:array
        })
      }
      else{
        Feedback.toast.error(jsondata.msg);
      }
    })
    .catch((error) => {
      Feedback.toast.error("ajax请求异常 " + error);
    });
  }

  render() {
    const init = this.field.init;
    const { stuffId, searchValue, record, isApprove, from, approveRecord } = this.state;
    const { dataSource } = this.state;
    const formItemLayout = {
      labelCol: {
        fixedSpan: 6
      },
    };
    return (
      <div>
        <div style={{ marginBottom: 5 }}>
        {
          from ?
            from == 'message' ?
              <Link to={{
                pathname: `/message`,
                state:{searchValue: searchValue}
              }}>
                  <Button className="button" type="primary">
                      <FoundationSymbol type="backward" style={{ marginRight: 5 }} size="small" />
                      返回
                  </Button>
              </Link>
            :
              <Link to={{
                pathname: `/process/approval`,
                state:{searchValue: searchValue}
              }}>
                  <Button className="button" type="primary">
                      <FoundationSymbol type="backward" style={{ marginRight: 5 }} size="small" />
                      返回
                  </Button>
              </Link>
            :             
          void(0)
        } 
        </div>
        <IceContainer title="材料领购申请单">
        <Form direction="ver" field={this.field}>
            <Input {...init("id")} htmlType="hidden" /> {/* 申请单id */}
            <Input {...init("userId", { initValue: stuffId })} htmlType="hidden" /> {/* 员工id */}
            <Input {...init("deptId", { initValue: sessionStorage.getItem("deptId") })} htmlType="hidden" /> {/* 部门id */}
            <Input {...init("type", { initValue: 1 })} htmlType="hidden" /> {/* type=1为采购申请；type=0为材料申请 */}
            <Row>
              <Form.Item label="申请人：" {...formItemLayout}>
                <Input {...init("userName", { initValue: sessionStorage.getItem("realName") })} readOnly style={{ width: 160 }}/>
              </Form.Item>
              <Form.Item label="部门：" {...formItemLayout}>
                <Input {...init("deptName", { initValue: sessionStorage.getItem("deptName") })} readOnly="true" style={{ width: 160 }}/>
              </Form.Item>
            </Row>
            <Row justify="space-between">
              <Form.Item label="申请事由：" {...formItemLayout}>
                <Input
                  multiple
                  {...init("remarks")}
                  style={{ width: "500px" }}
                  readOnly
                />
              </Form.Item>
            </Row>
            <Row>
              <Form.Item label="附件：" {...formItemLayout}>
              {
                record.attachment ?
                    <a href={`${OAURL}/file/download?fileName=` + record.attachment}
                      className="next-form-text-align"
                      style={styles.attachment}>
                      {record.fileName}
                    </a>
                  :
                  void(0)
              }
              </Form.Item>
            </Row>
          </Form>
        </IceContainer>
          <IceContainer title="领购材料列表">
            <Table
                  dataSource={dataSource}
                  style={{ marginTop: 10 }}
                  primaryKey="id"
            >
              <Table.Column title="材料名称" dataIndex="name"  align="center" />
              <Table.Column title="规格" dataIndex="standard" align="center" />
              <Table.Column title="单位" dataIndex="unit" align="center" />
              <Table.Column title="申请数量" dataIndex="appCount"  align="center" />
            </Table>     
          </IceContainer>
          <IceContainer title="审批">
            <Tab onChange={this.onChange}>
              <Tab.TabPane tab="审批详情" key="approveApply">
              <Form direction="ver" field={this.field}>
                {
                  this.state.isBigLeader == true &&  isApprove == 0?(
                    <Row>
                      <Form.Item label="采购部门：" {...formItemLayout}>
                        <Select
                          style={{width:180}}
                          dataSource={this.state.department}
                          onChange={(value,option)=>{
                            this.setState({
                              departSelect: option
                            })
                          }}
                        />
                      </Form.Item>
                    </Row>
                  ):(
                    ''
                  )
                }
                <Row>
                  
                  <Form.Item label="审批意见：" {...formItemLayout}>
                    <Input
                      multiple
                      {...init("comment")}
                      style={{ width: "500px" }}
                    />
                  </Form.Item>
                </Row>
                {
                isApprove ?
                  isApprove == 0 ?
                    void(0)
                  :
                    <div>
                      <Row>
                        <Form.Item label="审批结果：" {...formItemLayout}>
                          <Select {...init("auditResult" )} disabled
                            dataSource={[
                              { value: '1', label: '同意'},
                              { value: '2', label: '拒绝'}
                            ]}
                            style={{ width: 160 }}
                          />
                        </Form.Item>
                      </Row>
                    </div>
                :
                void(0) 
              }
                </Form> 
              </Tab.TabPane>
              <Tab.TabPane tab="审批记录" key="viewApproveRecord">
                <Timeline className="timeline">
                  {approveRecord.map(item => (
                    <TimelineItem 
                      title={<div className="timeline-item-title"> {item.title} </div>} 
                      content={<div className="timeline-item-content"> {item.content}&nbsp;&nbsp;&nbsp;{item.result}&nbsp;&nbsp;&nbsp;{item.comment}&nbsp;&nbsp;&nbsp;{item.remarks} </div>} 
                      time={<div className="timeline-item-time"> {item.time} </div>} 
                      state={item.state}
                      icon="account-filling"
                  />
                  ))}
                </Timeline>
              </Tab.TabPane>
            </Tab>
              {
                isApprove ?
                  isApprove == 0 ?
                    <div align="center">
                        <Button type="primary" loading={this.state.agree} onClick={this.agreeApply}>
                          同意  
                        </Button>
                        <Button type="secondary" loading={this.state.reject} onClick={this.rejectApply} style={{ marginLeft: 30}}>
                          拒绝
                        </Button>
                        <Button onClick={this.onClose} style={{ marginLeft: 30}}>
                          取消
                        </Button>
                    </div>
                  :
                    <div align="center">
                      {
                        from ?
                          from == 'message' ?
                            <Link to={{
                              pathname: `/message`,
                              state:{searchValue: searchValue}
                            }}>
                                <Button onClick={this.onClose} style={{ marginLeft: 30}} type="primary" className="button">
                                  关闭
                                </Button>
                            </Link>
                          :
                            <Link to={{
                              pathname: `/process/approval`,
                              state:{searchValue: searchValue}
                            }}>
                                <Button onClick={this.onClose} style={{ marginLeft: 30}} type="primary" className="button">
                                  关闭
                                </Button>
                            </Link>
                          :             
                        void(0)
                      } 
                    </div>
                :
                void(0) 
              }
          </IceContainer>
      </div>
    );
  }
}
const styles = {
  buttonStyle: {
    display: "inline-block",
    marginRight: "2px"
  },
};
