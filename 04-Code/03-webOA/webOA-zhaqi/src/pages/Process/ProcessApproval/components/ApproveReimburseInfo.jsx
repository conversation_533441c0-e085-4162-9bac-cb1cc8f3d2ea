import React, {Component} from 'react';
import {Select, Radio, Timeline, Dialog, Button, Form, Input, Field, Grid, Feedback, Icon, Tab} from '@icedesign/base';
import axios from 'axios';
import {OAURL} from '../../../../components/URL/OAURL';
import ViewGoBusinessInfo from '../../../Apply/GoBusinessApply/component/ViewGoBusinessInfo';
import qs from 'qs';
import {Link} from 'react-router-dom';
import ViewPurchaseApprove from '../components/ViewPurchaseApprove'
import '../../../Apply/ReimburseApply/components/ReimburseInfo.css'
import PurchaseDetails from '../../../Apply/ReimburseApply/components/PurchaseDetails';

const {Group: RadioGroup} = Radio;
const {Row, Col} = Grid;
const {Item: TimelineItem} = Timeline;
export default class ApproveReimburseInfo extends Component {
  static displayName = 'ApproveReimburseInfo';

  static defaultProps = {};

  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      url: OAURL,
      userId: sessionStorage.getItem('stuffId'),
      userName: sessionStorage.getItem('realName'),
      tabKey: 'approveApply',
      approveRecord: [],
      agree: false,
      reject: false,
      attachment: '',
      fileName: '',
      applicationId: '',
      applicationName: '',
      isApprove: null,
    };
    this.field = new Field(this);
  }

  //初始化获取数据
  componentWillMount() {
    this.getApprovalRecords();
  };

  getApprovalRecords = () => {
    const {url} = this.state;
    const {record} = this.props;
    axios.get(url + '/process/status', {
        params: {
          applyId: record.applyId,
          type: record.type,
          n: Math.random(),
        }
      }
    )
      .then((response) => {
        let jsondata = response.data;
        if (jsondata.statusCode == 0) {
          this.setState({
            approveRecord: jsondata.data,
          })
        }
      })
      .catch((error) => {
        Feedback.toast.error('系统繁忙，请稍后重试：' + error);
      })
  }

  //根据审批记录ID，获取审批详情
  getApprovedDetail = (auditId) => {
    const {url} = this.state;
    axios({
      method: 'get',
      url: url + '/process/getReviewDetail',
      params: {
        auditId: auditId,
        n: Math.random(),
      },
    })
      .then((response) => {
        let jsondata = response.data;
        if (jsondata.statusCode == 0) {
          // this.field.setValues({...jsondata.obj});
          this.setState({
            isApprove: jsondata.obj.flag,
          });
          this.field.setValue('comment', jsondata.obj.comment);
          this.field.setValue('auditResult', jsondata.obj.auditResult);
        }
      })
      .catch((error) => {
        Feedback.toast.error('系统繁忙，请稍后重试:' + error);
      });
  }

  onClose = () => {
    const {opt, flag} = this.props;
    this.setState({
      visible: false
    });
    if (opt != 'flow' && flag != 1) {
      setTimeout(() => {
        this.props.setMsgReaded()
      }, 500);
    }
  };

  //审批查看申请单详情
  onOpen = (record) => {
    const {url} = this.state;
    //获取审批详情
    this.getApprovedDetail(record.auditId);
    axios.get(url + '/process/applyDetail', {
        params: {
          applyId: record.applyId,
          type: record.type,
          n: Math.random(),
        }
      }
    )
      .then((response) => {
        let jsondata = response.data;
        if (jsondata.statusCode == 0) {
          this.field.setValues({...jsondata.apply[0].obj});
          this.setState({
            visible: true,
            attachment: jsondata.apply[0].obj.attachment,
            fileName: jsondata.apply[0].obj.fileName,
            applicationId: jsondata.apply[0].obj.applicationId,
            applicationName: jsondata.apply[0].obj.applicationName,
          });
        }
        else {
          Feedback.toast.error('后台数据返回失败');
        }
      })
      .catch((error) => {
        Feedback.toast.error('系统繁忙，请稍后重试：' + error);
      })
  }

  //同意申请
  agreeApply = () => {
    this.setState({
      agree: true,
    });
    const {url} = this.state;
    this.axiosMethod(url + '/expenseapply/complete');
  }

  //拒绝申请
  rejectApply = () => {
    this.setState({
      reject: true,
    });
    const {url} = this.state;

    this.axiosMethod(url + '/expenseapply/reject');

  }

  //发送axios请求
  axiosMethod = (url) => {
    const {userId, userName} = this.state;
    const {record, opt, flag} = this.props;
    let auditId = record.auditId;
    let comment = this.field.getValue('comment');
    let values = {};
    values.auditId = auditId;
    values.comment = comment;
    values.userId = userId;
    values.userName = userName;
    axios({
      method: 'post',
      url: url,
      data: qs.stringify(values),
    })
      .then((response) => {
        this.setState({
          agree: false,
          reject: false,
        });
        var jsondata = response.data;
        if (jsondata.statusCode == 0) {
          Feedback.toast.success("审批完成");
          if (opt == 'flow') {
            setTimeout(() => {
              this.props.refreshTable()
            }, 500);
          }
          else if(flag != 1){
            setTimeout(() => {
              this.props.setMsgReaded()
            }, 500);
          }
          this.setState({
            visible: false,
          });
        }
        else {
          Feedback.toast.error("已审批,请勿重复提交");
        }
      })
      .catch((error) => {
        this.setState({
          agree: false,
          reject: false,
        });
        Feedback.toast.error("ajax请求异常 " + error);
      });
  }

  onChange = (key) => {
    this.setState({
      tabKey: key,
    })
  }

  viewRelateApply = (flag, title) => {
    let record = {};
    record.id = this.field.getValue('applicationId');
    if (flag == '0') {
      return (
        <span>
          <PurchaseDetails 
            record={record} 
            title={title} />
        </span>
      )
    }
    else if (flag == '1') {
      return (
        <span>
          <ViewGoBusinessInfo
            record={record}
            title={title}
          />
        </span>
      )
    }
  }

  render() {
    const init = this.field.init;

    const {record} = this.props;

    const {approveRecord, attachment, fileName, applicationName, applicationId, isApprove} = this.state;
    const formItemLayout = {
      labelCol: {
        fixedSpan: 9,
      },
    };

    const footer = (
      <div>
        <Button type="primary" loading={this.state.agree} onClick={this.agreeApply}>
          同意
        </Button>
        <Button type="secondary" loading={this.state.reject} onClick={this.rejectApply} style={{marginLeft: 30}}>
          拒绝
        </Button>
        <Button onClick={this.onClose} style={{marginLeft: 30}}>
          取消
        </Button>
      </div>
    );

    const footer1 = (
      <div>
        <Button onClick={this.onClose} style={{marginLeft: 30}} type="primary">
          关闭
        </Button>
      </div>
    )

    return (
      <div className="reimbursdeInfo" style={styles.buttonStyle}>
        <a onClick={() => this.onOpen(record)} title="签审">
          <Icon type="browse" style={{color: "#3399ff", cursor: "pointer"}} size="small"/>
        </a>
        <Dialog
          style={{width: 900}}
          visible={this.state.visible}
          onClose={this.onClose}
          footer={isApprove ? isApprove == 0 ? footer : footer1 : void(0)}
          footerAlign="center"
          title="报销申请审批"
          className="dialog"
        >
          <Tab onChange={this.onChange}>
            <Tab.TabPane tab="审批详情" key="approveApply">
              <Form direction="ver" field={this.field}>
                <Row>
                  <Form.Item label="申请人：" {...formItemLayout}>
                    <Input {...init("userName")} readOnly/>
                  </Form.Item>
                  <Form.Item label="部门：" {...formItemLayout}>
                    <Input {...init("deptName")} readOnly/>
                  </Form.Item>
                </Row>
                {/* <Row>
                    <Form.Item label="岗位：" {...formItemLayout}>
                        <Input {...init("userPosition")} readOnly />
                    </Form.Item>
                    <Form.Item label="工号：" {...formItemLayout}>
                        <Input {...init("userWno")} readOnly />
                    </Form.Item>
                    </Row> */}
                <Row>
                  <Form.Item label="报销类别：" {...formItemLayout}>
                    <Select {...init('type')}
                            dataSource={[
                              {label: '材料报销', value: '0'},
                              {label: '差旅报销', value: '1'},
                              {label: '成本报销', value: '2'},
                            ]}
                            onChange={this.selectType}
                            style={{width: 160}}
                            disabled
                    />
                  </Form.Item>
                  {
                    this.field.getValue('type') == '0' ?
                      <Form.Item label="关联的采购申请：" {...formItemLayout}>
                        <Input {...init("applicationId")} htmlType="hidden" value={applicationId}/>
                        <a className="next-form-text-align" style={{cursor: "pointer", color: "green"}}>
                          {
                            applicationName ?
                              this.viewRelateApply(0, applicationName)
                              :
                              '无'
                          }
                        </a>
                      </Form.Item>
                      :
                      void(0)
                  }
                  {
                    this.field.getValue('type') == '1' ?
                        <Form.Item label="关联的差旅申请：" {...formItemLayout}>
                          <Input {...init("applicationId")} htmlType="hidden" value={applicationId}/>
                          <a className="next-form-text-align" style={{cursor: "pointer", color: "green"}}>
                            {
                              applicationName ?
                                this.viewRelateApply(1, applicationName)
                                :
                                '无'
                            }
                          </a>
                        </Form.Item>
                      :
                      void(0)
                  }
                </Row>
                 
                {
                  this.field.getValue('type') == '1' ?
                    <Row>
                      <Form.Item label="是否有借款：" {...formItemLayout}>
                        <RadioGroup {...init('hasLoan')}
                                    style={{width: 160}}
                                    placeholder="请选择关联的差旅申请"
                                    dataSource={[
                                      {label: '没有', value: '0'},
                                      {label: '有', value: '1'}
                                    ]}
                                    disabled
                        />
                      </Form.Item>
                    </Row>
                    :
                    void(0)
                }
                {/* <Row>
                  <Form.Item label="费用发生时间：" {...formItemLayout}>
                    <Input {...init('moneyTime')} readOnly
                    />
                  </Form.Item>
                </Row>
                <Row>
                  <Form.Item label="报销金额（元）：" {...formItemLayout}>
                    <Input {...init('expenseAmount')} readOnly/>
                  </Form.Item>
                </Row>
                <Row>
                  <Form.Item label="申请事由：" {...formItemLayout}>
                    <Input style={{width: 440, height: 70}}
                           {...init("remarks")}
                           readOnly
                    />
                  </Form.Item>
                </Row> */}
                <Row>
                  <Form.Item label="附件：" {...formItemLayout}>
                    <a href={`${OAURL}/file/download?fileName=` + attachment} style={styles.attachment}
                       className="next-form-text-align"
                    >
                      {fileName}
                    </a>
                  </Form.Item>
                </Row>
                <Row>
                    <div className="zy">摘要</div>
                    <div className="je">金额/元</div>
                    <div className="dj">附单据数/张</div>
                </Row>
                <Row>
                  <div className="zy">
                    <Input {...init('digest_1')} className="input_zy" readOnly />
                  </div>
                  <div className="je">
                    <Input {...init('money_1')} className="input_je" htmlType="number" readOnly />
                  </div>
                  <div className="dj">
                    <Input {...init('attBilNum_1')} className="input_fd" htmlType="number" readOnly />
                  </div>
                </Row>
                <Row>
                  <div className="zy">
                    <Input {...init('digest_2')} className="input_zy" readOnly/>
                  </div>
                  <div className="je">
                    <Input {...init('money_2')} className="input_je" htmlType="number" readOnly/>
                  </div>
                  <div className="dj">
                    <Input {...init('attBilNum_2')} className="input_fd" htmlType="number" readOnly/>
                  </div>
                </Row>
                <Row>
                  <div className="zy">
                    <Input {...init('digest_3')} className="input_zy" readOnly/>
                  </div>
                  <div className="je">
                    <Input {...init('money_3')} className="input_je" htmlType="number" readOnly/>
                  </div>
                  <div className="dj">
                    <Input {...init('attBilNum_3')} className="input_fd" htmlType="number" readOnly/>
                  </div>
                </Row>
                <Row>
                  <div className="zy">
                    <Input {...init('digest_4')} className="input_zy" readOnly/>
                  </div>
                  <div className="je">
                    <Input {...init('money_4')} className="input_je" htmlType="number" readOnly/>
                  </div>
                  <div className="dj">
                    <Input {...init('attBilNum_4')} className="input_fd" htmlType="number" readOnly/>
                  </div>
                </Row>
                <Row>
                  <div className="zy last">
                    总计：
                  </div>
                  <div className="je last">
                    <Input {...init('totalMoney')} className="input_je" readOnly/>
                  </div>
                  <div className="dj last">
                    <Input {...init('totalBilNum')} className="input_fd" readOnly/>
                  </div>
                </Row>
                
                
                
                <Row style={{marginTop:'25px'}}>
                  <Form.Item label="审批意见：" {...formItemLayout}>
                    <Input style={{width: 440, height: 70}}
                           {...init("comment")}
                           autocomplete="off"
                    />
                  </Form.Item>
                </Row>
                {
                  isApprove ?
                    isApprove == 0 ?
                      void(0)
                      :
                      <div>
                        <Row>
                          <Form.Item label="审批结果：" {...formItemLayout}>
                            <Select {...init("auditResult")} disabled
                                    dataSource={[
                                      {value: '1', label: '同意'},
                                      {value: '2', label: '拒绝'}
                                    ]}
                                    style={{width: 160}}
                            />
                          </Form.Item>
                        </Row>
                      </div>
                    :
                    void(0)
                }
              </Form>
            </Tab.TabPane>
            <Tab.TabPane tab="审批记录" key="viewApproveRecord">
              <Timeline className="timeline">
                {approveRecord.map(item => (
                  <TimelineItem
                    title={<div className="timeline-item-title"> {item.title} </div>}
                    content={<div
                      className="timeline-item-content"> {item.content}&nbsp;&nbsp;&nbsp;{item.result}&nbsp;&nbsp;&nbsp;{item.comment} </div>}
                    time={<div className="timeline-item-time"> {item.time} </div>}
                    state={item.state}
                    icon="account-filling"
                  />
                ))}
              </Timeline>
            </Tab.TabPane>
          </Tab>
        </Dialog>
      </div>
    );
  }
}

const styles = {
  buttonStyle: {
    display: 'inline-block',
    marginRight: '2px',
  }
};
