import React, { Component } from 'react';
import { Timeline, Select, DatePicker, Dialog, Button, Form, Input, Field, Grid, Feedback, Icon, Tab } from '@icedesign/base';
import axios from 'axios';
import { OAURL } from '../../../../components/URL/OAURL';

const { Row, Col } = Grid;
const { Item: TimelineItem } = Timeline;
const leaveType = [
    { label: "病假", value: '0' },
    { label: "事假", value: '1' },
    { label: "年假", value: '2' },
    { label: "产假", value: '3' },
    { label: "婚假", value: '4' },
    { label: "哺乳期", value: '5' },
    { label: "带薪休假", value: '6' },
    { label: "丧假", value: '7' },
    { label: "调休", value: '8' },
];

export default class ViewApproveLeaveWorkInfo extends Component {
    static displayName = 'ViewApproveLeaveWorkInfo';

    static defaultProps = {};
    constructor(props) {
        super(props);
        this.state = {
            visible: false,
            url: OAURL,
            userId: sessionStorage.getItem('stuffId'),
            userName: sessionStorage.getItem('realName'),
            tabKey: 'approveApply',
            approveRecord: [],
            reject:false,
            agree:false,
            attachment: '',
            fileName: '',
        };
        this.field = new Field(this);
    }

    //初始化获取数据
    componentWillMount() {
        this.getApprovalRecords();
    };

    getApprovalRecords = () => {
        const { url } = this.state;
        const { record } = this.props;
        axios.get(url + '/process/status', {
            params: {
                applyId: record.applyId,
                type: record.type,
                n: Math.random(),
            }
        })
            .then((response) => {
                let jsondata = response.data;
                if (jsondata.statusCode == 0) {
                    this.setState({
                        approveRecord: jsondata.data,
                    })
                }
                else {
                    Feedback.toast.error(jsondata.msg);
                }
            })
            .catch((error) => {
                Feedback.toast.error('系统繁忙，请稍后重试：' + error);
            })
    }

    onClose = () => {
        this.setState({
            visible: false
        })
    };

    //审批查看申请单详情
    onOpen = (record) => {
        const { url } = this.state;
        axios.get(url + '/process/applyDetail', {
            params: {
                applyId: record.applyId,
                type: record.type,
                n: Math.random(),
            }
        })
        .then((response) => {
            let jsondata = response.data;
            if (jsondata.statusCode == 0) {
                if(jsondata.apply[0].obj){
                    this.field.setValues({ ...jsondata.apply[0].obj });
                    this.setState({
                        visible: true,
                        attachment: jsondata.apply[0].obj.attachment,
                        fileName: jsondata.apply[0].obj.fileName,
                    });
                }
            }
            else {
                Feedback.toast.error('后台数据返回失败');
            }
        })
        .catch((error) => {
            Feedback.toast.error('系统繁忙，请稍后重试：' + error);
        })
    }

    onChange = (key) => {
        this.setState({
            tabKey: key,
        })
    }

    render() {
        const init = this.field.init;
        const { record } = this.props;
        const { approveRecord, attachment, fileName } = this.state;
        const formItemLayout = {
            labelCol: {
                fixedSpan: 6,
            }
        };
        const footer = (
            <div>
                <Button onClick={this.onClose} style={{ marginLeft: 30 }}>
                    关闭
                </Button>
            </div>
        );

        return (
            <div style={styles.buttonStyle}>
                <a onClick={() => this.onOpen(record)} title="查看审批详情" >
                    <Icon type="browse" style={{ color: "#3399ff", cursor: "pointer" }} size="small" />
                </a>
                <Dialog
                    minMargin={10}
                    style={{ width: 700 }}
                    visible={this.state.visible}
                    onClose={this.onClose}
                    footer={footer}
                    footerAlign="center"
                >
                    <Tab onChange={this.onChange}>
                        <Tab.TabPane tab="审批详情" key="approveApply">
                            <Form direction="ver" field={this.field}>
                                <Row>
                                    <Form.Item label="申请人：" {...formItemLayout}>
                                        <Input {...init("userName")} readOnly style={{ width: 160 }}/>
                                    </Form.Item>
                                    <Form.Item label="部门：" {...formItemLayout}>
                                        <Input {...init("deptName")} readOnly style={{ width: 160 }}/>
                                    </Form.Item>
                                </Row>
                                {/* <Row>
                                    <Form.Item label="岗位：" {...formItemLayout}>
                                        <Input {...init("userPosition")} readOnly style={{ width: 160 }}/>
                                    </Form.Item>
                                    <Form.Item label="工号：" {...formItemLayout}>
                                        <Input {...init("userJobNum")} readOnly style={{ width: 160 }}/>
                                    </Form.Item>
                                </Row> */}
                                <Row>
                                    <Form.Item label="请假类型：" {...formItemLayout}>
                                        <Select
                                            {...init('type')}
                                            dataSource={leaveType}
                                            style={{ width: 160 }}
                                        />
                                    </Form.Item>
                                </Row>
                                <Row>
                                    <Form.Item label="请假时间：" {...formItemLayout}>
                                        <Input
                                            {...init("startTime")}
                                            readOnly
                                            style={{ width: 160 }}
                                        />
                                    </Form.Item>
                                    <Form.Item label="&emsp;至" >
                                        <Input {...init('endTime')}
                                            readOnly
                                            style={{ width: 160 }}
                                        />
                                    </Form.Item>
                                </Row>
                                <Row>
                                    <Form.Item label="总时长天：" {...formItemLayout}>
                                        <Input {...init('totalTime')} readOnly autoComplete="off" />
                                    </Form.Item>
                                </Row>
                                <Row>
                                    <Form.Item label="申请事由：" {...formItemLayout}>
                                        <Input style={{ width: 440, height: 70 }}
                                            {...init("remarks")}
                                            readOnly
                                        />
                                    </Form.Item>
                                </Row>
                                <Row>
                                    <Form.Item label="附件：" {...formItemLayout}>
                                        <a href={`${OAURL}/file/download?fileName=`+attachment} 
                                        className="next-form-text-align" 
                                        style={styles.attachment}>                     
                                        {fileName}
                                        </a>
                                    </Form.Item>
                                </Row>
                                <Row>
                                    <Form.Item label="审批理由：" {...formItemLayout}>
                                        <Input style={{ width: 440, height: 70 }}
                                            {...init("comment")} readOnly
                                            value={record.comment}
                                        />
                                    </Form.Item>
                                </Row>
                                <Row>
                                    <Form.Item label="审批结果：" {...formItemLayout}>
                                    <Select {...init("auditResult" )} disabled
                                        dataSource={[
                                            { value: '1', label: '同意'},
                                            { value: '2', label: '拒绝'}
                                        ]}
                                        value={record.auditResult}
                                        style={{ width: 160 }}
                                        />
                                    </Form.Item>
                                </Row>
                            </Form>
                        </Tab.TabPane>
                        <Tab.TabPane tab="审批记录" key="viewApproveRecord">
                            <Timeline className="timeline">
                                {approveRecord.map(item => (
                                    <TimelineItem
                                        title={<div className="timeline-item-title"> {item.title} </div>}
                                        content={<div className="timeline-item-content"> {item.content}&nbsp;&nbsp;&nbsp;{item.result}&nbsp;&nbsp;&nbsp;{item.comment} </div>}
                                        time={<div className="timeline-item-time"> {item.time} </div>}
                                        state={item.state}
                                        icon="account-filling"
                                    />
                                ))}
                            </Timeline>
                        </Tab.TabPane>
                    </Tab>
                </Dialog>
            </div>
        );
    }
}
const styles = {
    buttonStyle: {
        display: 'inline-block',
        marginRight: '2px',
    }
};