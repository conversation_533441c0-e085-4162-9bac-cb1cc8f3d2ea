import React, { Component } from 'react';
import {Select, Checkbox,Timeline,  Dialog, Button, Form, Input, Field, Grid, Feedback, Icon, Tab } from '@icedesign/base';
import axios from 'axios';
import { OAURL } from '../../../../components/URL/OAURL';
import qs from 'qs';

const { Row } = Grid;
const { Group: CheckboxGroup } = Checkbox;
const { Item: TimelineItem } = Timeline;
const Toast = Feedback.toast;
const FormItem = Form.Item;

export default class ApproveOtherInfo extends Component {

  constructor(props) {
    super(props);
    this.state = {
        visible: false,
        url: OAURL,
        userId: sessionStorage.getItem('stuffId'),
        userName: sessionStorage.getItem('realName'),
        tabKey: 'approveApply',
        reject:false,
        loading:false,
        isApprove: null,
        isReadOnly: false,
        fileList:[],
        dialogTitle:'',
    };
    this.field = new Field(this,{
      autoUnmount: true
    });
  }

  //初始化获取数据
  componentWillMount(){
  };

  //根据审批记录ID，获取审批结果详情
  getApprovedDetail = (auditId) => {
    const { url } = this.state;
    axios({
      method: "post",
      url: url + "/FlowInstance/getApproveDetail",
      data:qs.stringify(
        {
          id : auditId
        }
      ),
    })
      .then((response) => {
        let obj = response.data.obj
        let isReadOnly;
        if(obj.status == 0){
          isReadOnly = false;
        }else{
          isReadOnly = true;
          this.field.setValue('approveComments', response.data.obj.approveComments);
          this.field.setValue('approveResult', response.data.obj.approveResult);
        }
        this.setState({
          isApprove: obj.status,
          isReadOnly: isReadOnly
        }); 
      })
      .catch((error) => {
        Toast.error('请求失败' + error);
      });
  }

  //根据id查询申请单内容
  getApplyDetail = (applyId) =>{
    const { url } = this.state;
    axios({
      method: "post",
      url: url + "/FlowInstance/findHistoryById",
      data:qs.stringify(
        {
          flowInstanceId : applyId
        }
      ),
    })
      .then((response) => {
        this.setState({
          applyList: response.data.list,
          dialogTitle: response.data.flowName,
          visible: true,
          fileName : response.data.fileName,
          attachment : response.data.attachment,
        },()=>{
          if(this.state.fileName){
            let fileShowName = this.state.fileName.split(','); //文件名转数组
            let attachmentShow = this.state.attachment.split(','); //编号转数据
      
            let fileList = [];
            for(let i=0; i<fileShowName.length; i++){
              fileList.push({
                  name: fileShowName[i],
                  attachment: attachmentShow[i],
                  downloadURL: `${OAURL}/file/download?fileName=` + attachmentShow[i],
                },);
            }
            this.setState({
              fileList: fileList
            })
          }
        }); 
      })
      .catch((error) => {
        Toast.error('请求失败' + error);
      });
  }
  
  //获取审批记录
  getApprovalRecords = (applyId) => {
    const { url } = this.state; 
    axios({
      method: "post",
      url: url + "/FlowInstance/getApproveProcess",
      data:qs.stringify(
        {
          id : applyId
        }
      ),
    })
      .then((response) => {
        this.setState({
          approveRecord: response.data.obj,
        }); 
      })
      .catch((error) => {
        Toast.error('请求失败' + error);
      });
  }


  //渲染申请内容
  createApplyDetail = () =>{
    const formItemLayout = {
      labelCol : {
        fixedSpan: 6,
      },
      wrapperCol :{
        span: 18,
      }
    };
    if(this.state.applyList){
      return this.state.applyList.map( (item,index)=>{
        return(
          <FormItem label={item.componentTitle+'：'}  {...formItemLayout} className="formItem" key={index}>
            <div style={{height:'100%',lineHeight:'32px'}}>
              {item.realValue}
            </div>
          </FormItem>
        )
      })
    }
    
  }

  //渲染审批记录
  createApproveRecord = () =>{
    if(this.state.approveRecord){
      return this.state.approveRecord.map( (item,index)=>{
        return(
          <TimelineItem 
            title={<div className="timeline-item-title"> {item.name} </div>} 
            content={<div className="timeline-item-content"> {item.approvePersonName}&nbsp;&nbsp;&nbsp;{item.approveResult}&nbsp;&nbsp;&nbsp;{item.approveComments} </div>} 
            time={<div className="timeline-item-time"> {item.approveTime} </div>} 
            state={item.status}
            icon="account-filling"
            key={index}
          />
        )
      })
    }
    
  }
  
  //打开弹框
  onOpen = (record) => {
    this.getApplyDetail(record.applyId);//获取申请单内容
    this.getApprovedDetail(record.auditId);//获取审批结果
    this.getApprovalRecords(record.applyId); //获取审批记录
  }

  //显示文件列表
  showFileList = ()=>{
    return this.state.fileList.map( (item,index)=>{
      return(
        <a
          title="点击下载"
          href={item.downloadURL}
          style={styles.attachment}
          className="next-form-text-align"
          key={index}
        >                     
          {item.name}
        </a>
      )
    })
  }


  onClose = () => {
    const { opt, record, flag } = this.props;
    if(opt != 'flow' && flag!= 1){
      setTimeout( () => { this.props.setMsgReaded(record)},500);
    }
    this.setState({
      visible: false,
    })
  };

  //同意申请
  agreeApply = () => {
    this.setState({
      loading:true,
    });

    const { url } = this.state;
    this.axiosMethod(url+'/FlowInstance/completeApprove');
  }

  //拒绝申请
  rejectApply = () => {
    this.setState({
      loading:true,
    });

    const { url } = this.state;
    this.axiosMethod(url+'/FlowInstance/rejectApprove');
  }

  //发送同意/拒绝请求
  axiosMethod = (url) => {    
    let approvePersonId = this.state.userId;
    let approvePersonName = this.state.userName;
    let id = this.props.record.auditId;
    let approveComments = this.field.getValue('approveComments'); 
    axios({
      method: "post",
      url: url,
      data:qs.stringify(
        {
          approvePersonId, 
          approvePersonName, 
          id, 
          approveComments
        }
      ),
    })
      .then((response) => {
        this.setState({
          loading:false,
        });
        let jsomdata = response.data;
        if(jsomdata.statusCode == 0){
          const { opt, flag } = this.props;
          if(opt == 'flow'){
            setTimeout( () => { this.props.refreshTable()},500);
          }
          else if(flag != 1){
            setTimeout( () => { this.props.setMsgReaded()},500);
          }
          Toast.success("审批完成");
          this.setState({
            visible: false,
          })
        }
        else{
          Toast.error(jsomdata.msg);
        }
      })
      .catch((error) => {
        this.setState({
          loading:false,
        });
        Toast.error('请求失败' + error);
      });
    
  }

  onChange = (key) => {
    this.setState({
       tabKey: key,
    })
  }

  footerFm = () => {
    const { isApprove }= this.state;
    if(isApprove == 0){
      return(
        <div>
          <Button type="primary" loading={this.state.loading} onClick={this.agreeApply}>
            同意
         </Button>
          <Button type="secondary" loading={this.state.loading} onClick={this.rejectApply} style={{ marginLeft: 30 }}>
            拒绝
         </Button>
          <Button onClick={this.onClose} style={{ marginLeft: 30 }}>
            取消
         </Button>
        </div>
      );
    }
   else{
      return(
        <div>
          <Button type="primary" onClick={this.onClose} style={{ marginLeft: 30}}>
            关闭
          </Button>
        </div>
      );
   }
  }


  render() {
    const init = this.field.init;
    const { record } = this.props;
    const { isApprove, dialogTitle } = this.state;
    const formItemLayout = {
      labelCol : {
        fixedSpan: 6,
      },
      wrapperCol :{
        span: 18,
      }
    };

    return (
      <div style={styles.buttonStyle}>
        <a onClick={ () => this.onOpen(record)} title="签审" >
          <Icon type="browse" style={{ color: "#3399ff",cursor:"pointer"}} size="small"/>
        </a>
        <Dialog 
          style={{ width: 700 }}
          visible={this.state.visible} 
          onClose={this.onClose} 
          footer={this.footerFm()} 
          footerAlign="center"
          title={dialogTitle+"申请审批"}
        >
        <Tab onChange={this.onChange}>
            <Tab.TabPane tab="审批详情" key="approveApply">
                <Form direction="ver" field={this.field}>

                    {this.createApplyDetail()}

                    {
                      this.state.fileList !='' ?
                      <FormItem label="附件：" {...formItemLayout}>
                        {this.showFileList()}
                      </FormItem>
                      :
                      ''
                    }

                    <FormItem label={'审批意见：'}  {...formItemLayout} className="formItem">
                      <Input 
                        {...init('approveComments')}
                        readOnly={this.state.isReadOnly}
                        multiple
                      />
                    </FormItem>
                    {
                      isApprove ?
                        isApprove == 0 ?
                          void(0)
                        :
                          <div>
                            <Row>
                              <Form.Item label="审批结果：" {...formItemLayout}>
                                <Select {...init("approveResult" )} disabled
                                  dataSource={[
                                    { value: '1', label: '同意'},
                                    { value: '2', label: '拒绝'}
                                  ]}
                                  style={{ width: 160 }}
                                />
                              </Form.Item>
                            </Row>
                          </div>
                      :
                      void(0)
                    }
                </Form> 
            </Tab.TabPane>
            <Tab.TabPane tab="审批记录" key="viewApproveRecord">
              <Timeline className="timeline">
                {this.createApproveRecord()}
              </Timeline>
            </Tab.TabPane>
        </Tab>
      </Dialog>
      </div>
    );
  }
}
const styles = {
    buttonStyle:{ 
        display: 'inline-block', 
        marginRight: '2px',
    },
    attachment:{
      width:'330px',
      height:'28px',
      paddingLeft:'8px',
      marginBottom:'5px',
      fontSize: 12,
      // color:'#333',
      display:'block',
      background:'#f4f4f4'
     },
};