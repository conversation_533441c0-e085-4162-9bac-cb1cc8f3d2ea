import React, { Component } from 'react';
import {Timeline,  Dialog, Button, Form, Input, Field, Grid, Feedback, Icon, Tab } from '@icedesign/base';
import axios from 'axios';
import { OAURL } from '../../../../components/URL/OAURL';

const { Row } = Grid;
const { Item: TimelineItem } = Timeline;
export default class ApproveLoanApplyInfo extends Component {
  static displayName = 'ApproveLoanApplyInfo';
  static defaultProps = {};
  constructor(props) {
    super(props);
    this.state = {
        visible: false,
        url: OAURL,
        userId: sessionStorage.getItem('stuffId'),
        userName: sessionStorage.getItem('realName'),
        tabKey: 'approveApply',
        approveRecord:[],
        aloading:false,   //  同意 按钮控制是否载入状态
        rloading:false,   //  拒绝 按钮控制是否载入状态
        attachment: '',
        fileName: '',
    };
    this.field = new Field(this);
  }

    //初始化获取数据
  componentWillMount(){
    this.getApprovalRecords();
  };
  getApprovalRecords = () => {
    const { url } = this.state; 
    const { record } = this.props;
    axios.get(url+'/process/status',{
        params:{
          applyId: record.applyId,
          type: record.type,
          n: Math.random(),
        }
      }
    )
    .then((response) => {
        let jsondata = response.data;
         
        if(jsondata.statusCode == 0 ){
            this.setState({
              approveRecord:jsondata.data,
            })
        }
    })
    .catch((error) => {
        Feedback.toast.error('系统繁忙，请稍后重试：'+error);
    })
  }

  onClose = () => {
    this.setState({
      visible: false
    })
  };

  //审批查看申请单详情
  onOpen = (record) => {
    const { url } = this.state;
    axios.get(url+'/process/applyDetail',{
        params:{
          applyId: record.applyId,
          type: record.type,
          n: Math.random(),
        }
      }
    )
    .then((response) => {
        let jsondata = response.data;
        if(jsondata.statusCode == 0 ){
           this.field.setValues({ ...jsondata.apply[0]});
           this.setState({
              visible: true,
              attachment: jsondata.apply[0].attachment,
              fileName: jsondata.apply[0].fileName,
           }); 
        }
        else{
            Feedback.toast.error('后台数据返回失败');
        }
    })
    .catch((error) => {
        Feedback.toast.error('系统繁忙，请稍后重试：'+error);
    })
  }

  //同意申请
  agreeApply = () => {
    this.setState({          
      aloading:true,   // 同意按钮控制是否载入状态
    });
    const { url } = this.state;
    this.axiosMethod(url+'/loanApply/complete');
  }

  //拒绝申请
  rejectApply = () => {
    this.setState({          
      rloading:true,   // 拒绝按钮控制是否载入状态
    });
    const { url } = this.state;
    this.axiosMethod(url+'/loanApply/reject');
  }

  //发送axios请求
  axiosMethod = (url) => {
    
    const { userId, userName } = this.state;
    let auditId = this.props.record.auditId;
    let opt = this.props.opt;
    let comment = this.field.getValue('comment');
    
    axios({
      method: 'post',
      url: url,
      data: {
        userId: userId,
        userName: userName,
        comment: comment,
        reviewId: auditId
      }
    })
    .then((response) => {
      this.setState({          
        rloading:false,   // 按钮控制是否载入状态
        aloading:false,   // 按钮控制是否载入状态
      });
      
      var jsondata = response.data;
      if(jsondata.statusCode == 0){
        Feedback.toast.success("审批完成");     
        if(opt == 'flow'){
          setTimeout(() => {this.props.refreshTable()}, 500);
        }
        else{
          setTimeout(() => {this.props.setMsgReaded()},500);
        }
        this.setState({
          visible: false,
        });
        }
        else if(jsondata.statusCode == 101){
          Feedback.toast.error(jsondata.message);         
        }
        else{
        Feedback.toast.error("已审批,请勿重复提交");
      }
    })
    .catch((error) => {
      this.setState({          
        rloading:false,   // 按钮控制是否载入状态
        aloading:false,   // 按钮控制是否载入状态
      });
      Feedback.toast.error("ajax请求异常 " + error);
    });
  }

  onChange = (key) => {
    this.setState({
       tabKey: key,
    });
  }

  render() {
    const init = this.field.init;
    const { record } = this.props;
    const { approveRecord, attachment, fileName } = this.state;
    const formItemLayout = {
      labelCol : {
        fixedSpan: 6,
      },
    };

    const footer = (
      <div>
         <Button type="primary" onClick={this.agreeApply} loading={this.state.aloading}>
            同意  
          </Button>
          <Button type="secondary" onClick={this.rejectApply} style={{ marginLeft: 30}} loading={this.state.rloading}>
            拒绝
          </Button>
          <Button onClick={this.onClose} style={{ marginLeft: 30}}>
            取消
          </Button>
      </div>
    );

    return (
    <div style={styles.buttonStyle}>
        <a onClick={ () => this.onOpen(record)} title="签审" >
          <Icon type="survey" style={{ color: "#3399ff",cursor:"pointer"}} size="small"/>
        </a>
        <Dialog 
          minMargin={10} 
          style={{ width: 700 }}
          visible={this.state.visible} 
          onClose={this.onClose} 
          footer={footer} 
          footerAlign="center"
        >
        <Tab onChange={this.onChange}>
            <Tab.TabPane tab="审批详情" key="approveApply">
                <Form direction="ver" field={this.field}>
                    <Row>
                    <Form.Item label="申请人：" {...formItemLayout}>
                        <Input {...init("userName")} readOnly />
                    </Form.Item>
                    <Form.Item label="部门：" {...formItemLayout}>
                        <Input {...init("deptName")} readOnly />
                    </Form.Item>
                    </Row>
                    {/* <Row>
                    <Form.Item label="岗位：" {...formItemLayout}>
                        <Input {...init("userPosition")} readOnly />
                    </Form.Item>
                    <Form.Item label="工号：" {...formItemLayout}>
                        <Input {...init("userJobNum")} readOnly />
                    </Form.Item>
                    </Row> */}
                    <Row>
                    <Form.Item label="借款金额：" {...formItemLayout}>
                      <Input {...init('loanAmount', 
                      { rules: [{ required: true, message: "必填"}]})} 
                      autoComplete="off"
                      />
                    </Form.Item>
                    </Row>
                    <Row>
                    <Form.Item label="申请事由：" {...formItemLayout}>
                        <Input style={{ width: 440, height:70 }} 
                        {...init("remarks")}
                        readOnly
                        />
                    </Form.Item>
                    </Row>
                    <Row>
                      <Form.Item label="附件：" {...formItemLayout}>
                          <a href={`${OAURL}/file/download?fileName=`+attachment} 
                          className="next-form-text-align" 
                          style={styles.attachment}>                     
                          {fileName}
                          </a>
                      </Form.Item>
                    </Row>
                    <Row>
                    <Form.Item label="审批理由：" {...formItemLayout}>
                    <Input style={{ width: 440, height:70 }} 
                        {...init("comment" )} 
                        autocomplete="off"
                    />
                    </Form.Item>
                    </Row>
                </Form> 
            </Tab.TabPane>
            <Tab.TabPane tab="审批记录" key="viewApproveRecord">
              <Timeline className="timeline">
                {approveRecord.map(item => (
                  <TimelineItem 
                    title={<div className="timeline-item-title"> {item.title} </div>} 
                    content={<div className="timeline-item-content"> {item.content}&nbsp;&nbsp;&nbsp;{item.result}&nbsp;&nbsp;&nbsp;{item.comment} </div>} 
                    time={<div className="timeline-item-time"> {item.time} </div>} 
                    state={item.state}
                    icon="account-filling"
              />
           ))}
          </Timeline>
            </Tab.TabPane>
        </Tab>
      </Dialog>
      </div>
    );
  }
}
const styles = {
    buttonStyle:{ 
        display: 'inline-block', 
        marginRight: '2px',
    }
};