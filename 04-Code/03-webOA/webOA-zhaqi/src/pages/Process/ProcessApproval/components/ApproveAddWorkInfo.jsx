import React, { Component } from 'react';
import { moment, DatePicker, Radio, Timeline,  Dialog, Button, Form, Input, Field, Grid, Feedback, Icon, Tab } from '@icedesign/base';
import axios from 'axios';
import { OAURL } from '../../../../components/URL/OAURL';

const { Row } = Grid;
const { Item: TimelineItem } = Timeline;
const { Group: RadioGroup } = Radio;

export default class ApproveAddWorkInfo extends Component {
  static displayName = 'ApproveAddWorkInfo';

  static defaultProps = {};

  constructor(props) {
    super(props);
    this.state = {
        visible: false,
        url: OAURL,
        userId: sessionStorage.getItem('stuffId'),
        userName: sessionStorage.getItem('realName'),
        tabKey: 'approveApply',
        approveRecord:[],
        reject:false,
        agree:false,
        attachment: '',
        fileName: '',
    };
    this.field = new Field(this);
  }

    //初始化获取数据
  componentWillMount(){
    this.getApprovalRecords();
  };

  getApprovalRecords = () => {
    const { url } = this.state; 
    const { record } = this.props;
    axios.get(url+'/process/status',{
        params:{
          applyId: record.applyId,
          type: record.type,
          n: Math.random(),
        }
      }
    )
    .then((response) => {
        let jsondata = response.data;
        if(jsondata.statusCode == 0 ){
            this.setState({
              approveRecord:jsondata.data,
            })
        }
    })
    .catch((error) => {
        Feedback.toast.error('系统繁忙，请稍后重试：'+error);
    })
  }

  onClose = () => {
    this.setState({
      visible: false
    })
  };

  //审批查看申请单详情
  onOpen = (record) => {
    const { url } = this.state;
    axios.get(url+'/process/applyDetail',{
        params:{
          applyId: record.applyId,
          type: record.type,
          n: Math.random(),
        }
      }
    )
    .then((response) => {
        let jsondata = response.data;
        if(jsondata.statusCode == 0 ){
           this.field.setValues({ ...jsondata.apply[0]});
           this.setState({
              visible: true,
              attachment: jsondata.apply[0].attachment,
              fileName: jsondata.apply[0].fileName,
           }); 
        }
        else{
            Feedback.toast.error('后台数据返回失败');
        }
    })
    .catch((error) => {
        Feedback.toast.error('系统繁忙，请稍后重试：'+error);
    })
  }

  //同意申请
  agreeApply = () => {
    this.setState({
      agree:true,
    });
    const { url } = this.state;
    this.axiosMethod(url+'/workApply/complete');
  }

  //拒绝申请
  rejectApply = () => {
    this.setState({
      reject:true,
    });
    const { url } = this.state;
    this.axiosMethod(url+'/workApply/reject');
  }

  //发送axios请求
  axiosMethod = (urla) => {
    const { userId, userName } = this.state;
    let auditId = this.props.record.auditId;
    let opt = this.props.opt;
    let comment = this.field.getValue('comment');
    
    axios({
      method: 'post',
      url: urla,
      data: {
        userId: userId,
        userName: userName,
        comment: comment,
        reviewId: auditId
      }
    })
    .then((response) => {
      this.setState({
        agree:false,
        reject:false,
      });
      var jsondata = response.data;
      if(jsondata.statusCode == 0){
        Feedback.toast.success("审批完成");
        if(opt == 'flow'){
          setTimeout(() => {this.props.refreshTable()}, 500);
        }
        else{
          setTimeout(() => {this.props.setMsgReaded()},500);
        }
        this.setState({
          visible: false,
        });
      }
      else if(jsondata.statusCode == 102){
        Feedback.toast.success(jsondata.message);
        if(opt == 'flow'){
          this.props.refreshTable();
        }
        this.setState({
          visible: false,
        });
      }
      else {
        Feedback.toast.success(jsondata.message);
      }
    })
    .catch((error) => {
      this.setState({
        agree:false,
        reject:false,
      });
      Feedback.toast.error("ajax请求异常 " + error);
    });
  }

  onChange = (key) => {
    this.setState({
       tabKey: key,
    });
  }

  render() {
    const init = this.field.init;
    const { record } = this.props;
    const { approveRecord, attachment, fileName } = this.state;
    const formItemLayout = {
      labelCol : {
        fixedSpan: 6,
      },
    };

    const workType = [
      {
        value: '0',
        label: "工作日",
      },
      {
        value: '1',
        label: "节假日",
      },
      {
        value: '2',
        label: "休息日",
      },
      {
        value: '3',
        label: "其他",
      },
    ];

    const footer = (
      <div>
         <Button type="primary" loading={this.state.agree} onClick={this.agreeApply}>
            同意  
          </Button>
          <Button type="secondary" loading={this.state.reject} onClick={this.rejectApply} style={{ marginLeft: 30}}>
            拒绝
          </Button>
          <Button onClick={this.onClose} style={{ marginLeft: 30}}>
            取消
          </Button>
      </div>
    );

    return (
    <div style={styles.buttonStyle}>
        <a onClick={ () => this.onOpen(record)} title="签审" >
          <Icon type="survey" style={{ color: "#3399ff",cursor:"pointer"}} size="small"/>
        </a>
        <Dialog 
          minMargin={10} 
          style={{ width: 700 }}
          visible={this.state.visible} 
          onClose={this.onClose} 
          footer={footer} 
          footerAlign="center"
        >
        <Tab onChange={this.onChange}>
            <Tab.TabPane tab="审批详情" key="approveApply">
                <Form direction="ver" field={this.field}>
                    <Row>
                    <Form.Item label="申请人：" {...formItemLayout}>
                        <Input {...init("userName")} readOnly />
                    </Form.Item>
                    <Form.Item label="部门：" {...formItemLayout}>
                        <Input {...init("deptName")} readOnly />
                    </Form.Item>
                    </Row>
                    {/* <Row>
                    <Form.Item label="岗位：" {...formItemLayout}>
                        <Input {...init("userPosition")} readOnly />
                    </Form.Item>
                    <Form.Item label="工号：" {...formItemLayout}>
                        <Input {...init("userJobNum")} readOnly />
                    </Form.Item>
                    </Row> */}
                    <Row justify="space-between">
                        <Form.Item label="加班类别：" {...formItemLayout}  >
                        <RadioGroup {...init('type')}
                            dataSource={workType}
                        />
                        </Form.Item>
                    </Row>
                    <Row>
                        <Form.Item label="加班时间：" {...formItemLayout} >
                          <Input {...init('startTime')} readOnly/>  
                        </Form.Item>
                        <Form.Item label="&emsp;至" >
                          <Input {...init('endTime')} readOnly/>  
                        </Form.Item>
                        </Row>
                        <Row>
                        <Form.Item label="总时长(h)：" {...formItemLayout}>
                        <Input {...init('totalDay')} readOnly/>
                        </Form.Item>
                    </Row>
                    <Row>
                        <Form.Item label="申请事由：" {...formItemLayout}>
                            <Input style={{ width: 440, height:70 }} 
                            {...init("remarks")}
                            readOnly
                            />
                        </Form.Item>
                    </Row>
                    <Row>
                      <Form.Item label="附件：" {...formItemLayout}>
                        <a href={`${OAURL}/file/download?fileName=`+attachment} 
                          className="next-form-text-align" 
                          style={styles.attachment}>                     
                          {fileName}
                        </a>
                      </Form.Item>
                    </Row>
                    <Row>
                    <Form.Item label="审批理由：" {...formItemLayout}>
                    <Input style={{ width: 440, height:70 }} 
                        {...init("comment" )} 
                        autocomplete="off"
                    />
                    </Form.Item>
                    </Row>
                </Form> 
            </Tab.TabPane>
            <Tab.TabPane tab="审批记录" key="viewApproveRecord">
              <Timeline className="timeline">
                {approveRecord.map(item => (
                  <TimelineItem 
                    title={<div className="timeline-item-title"> {item.title} </div>} 
                    content={<div className="timeline-item-content"> {item.content}&nbsp;&nbsp;&nbsp;{item.result}&nbsp;&nbsp;&nbsp;{item.comment} </div>} 
                    time={<div className="timeline-item-time"> {item.time} </div>} 
                    state={item.state}
                    icon="account-filling"
              />
           ))}
          </Timeline>
            </Tab.TabPane>
        </Tab>
      </Dialog>
      </div>
    );
  }
}
const styles = {
    buttonStyle:{ 
        display: 'inline-block', 
        marginRight: '2px',
    }
}; 