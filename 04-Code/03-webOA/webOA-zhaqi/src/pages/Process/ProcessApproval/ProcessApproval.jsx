import React, { Component } from 'react';
import CustomBreadcrumb from '../../../components/CustomBreadcrumb';
import { Grid } from '@icedesign/base';
import ProcessTable from './components/ProcessTable';

export default class ProcessApproval extends Component {
  static displayName = 'ProcessApproval';

  constructor(props){
    super(props);
    this.state = {

    };
  }

  render() {
      const breadcrumb = [
        { text: '流程', link: ''},
        { text: '流程审批', link: '#/process/approval'}
      ];
      return (
          <div>
              <CustomBreadcrumb dataSource={breadcrumb} />    
              <ProcessTable/>
          </div>
      );
  }
}