import React, { Component } from 'react';
import CustomBreadcrumb from '../../../components/CustomBreadcrumb';
import { Grid } from '@icedesign/base';
import TemplateTable from './components/TemplateTable';


export default class ProcessTemplate extends Component {
  static displayName = 'ProcessTemplate';

  constructor(props){
    super(props);
    this.state = {

    };
  }

  render() {
      const breadcrumb = [
        { text: '流程', link: ''},
        { text: '流程审批', link: '#/setting/rolemanage'}
      ];
      return (
          <div>
              {/* <CustomBreadcrumb dataSource={breadcrumb} />     */}
              <TemplateTable/>
          </div>
      );
  }
}