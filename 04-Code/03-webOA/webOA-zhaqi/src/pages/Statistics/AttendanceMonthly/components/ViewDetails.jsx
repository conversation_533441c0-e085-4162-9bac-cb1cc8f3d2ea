import React, { Component } from 'react';
import {Icon, Dialog, Field, Form, Grid, Button, Input, Tag, DatePicker, Select, Checkbox } from '@icedesign/base';
const { Row, Col } = Grid;
const FormItem = Form.Item;

export default class ViewDetails extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataLoading: true,
            dataSource: [],
            total: 100,
            page: 1
        };
        this.field = new Field(this);
    }
    onCloseDialog = () => {
        this.setState({
            visible: false
        });
    };
    onOpen = (index, record) => {
        this.field.setValues({ ...record });
        this.setState({
            visible: true,
            dataIndex: index,
        });
    };

    render() {
        const init = this.field.init;
        const { index, record } = this.props;
        const fromItemLayout = {
            labelCol: {
                fixedSpan: 7,
            },
            wrapperCol: {
                span: 14,
            },

        };
        const footer = (
            <div>
                <a onClick={this.onCloseDialog} href="javascript:;">
                    <Button size="medium" type="primary">
                        关闭
                    </Button>
                </a>
            </div>

        );
        return (

            <div style={styles.colstyle}>
                <a onClick={() => this.onOpen(index, record)} title="查看" >
                    <Icon type="browse" style={{ color: "#3399ff",cursor:"pointer"}} size="small" />
                </a>
                <Dialog
                    style={{ width: 630 }}
                    visible={this.state.visible}
                    closable="esc,mask,close"
                    footer={footer}
                    onClose={this.onCloseDialog}
                    title="考勤详情"
                >
                    <Form field={this.field}>
                        <Row >
                            <Col span="11">
                                <FormItem label="姓名："  {...fromItemLayout}>
                                    <Input {...init('sname')} readOnly />
                                </FormItem>
                            </Col>
                            <Col span="11">
                                <FormItem label="工号："  {...fromItemLayout}>
                                    <Input {...init('ano')} readOnly />
                                </FormItem>
                            </Col>
                        </Row>
                        <Row>
                            <Col span="11">
                                <FormItem label="部门："  {...fromItemLayout}>
                                    <Input {...init('deptName')} readOnly />
                                </FormItem>
                            </Col>
                            <Col span="11">
                                <FormItem label="职位："  {...fromItemLayout}>
                                    <Input {...init('userPosition')} readOnly />
                                </FormItem>
                            </Col>
                        </Row>
                        <Row>
                            <Col span="11">
                                <FormItem label="迟到（次数）："  {...fromItemLayout}>
                                    <Input {...init('late')} readOnly  />
                                </FormItem>
                            </Col>
                            <Col span="11">
                                <FormItem label="迟到（分钟）："  {...fromItemLayout}>
                                    <Input {...init('lateTime')}  readOnly />
                                </FormItem>
                            </Col>
                        </Row>
                        <Row>
                            
                        </Row>

                        <Row>
                            <Col span="11">
                                <FormItem label="早退（次数）："  {...fromItemLayout}>
                                    <Input {...init('leaveEarly')} readOnly />
                                </FormItem>
                            </Col>
                            <Col span="11">
                                <FormItem label="早退（分钟）："  {...fromItemLayout}>
                                    <Input {...init('leaveEarlyTime')}  readOnly />
                                </FormItem>
                            </Col>
                        </Row>
                        <Row>
                            <Col span="11">
                                <FormItem label="事假："  {...fromItemLayout}>
                                    <Input {...init('affair')} readOnly />
                                </FormItem>
                            </Col>
                            <Col span="11">
                                <FormItem label="病假："  {...fromItemLayout}>
                                    <Input {...init('sick')}  readOnly />
                                </FormItem>
                            </Col>
                        </Row>
                        <Row>
                            <Col span="11">
                                <FormItem label="年假："  {...fromItemLayout}>
                                    <Input {...init('annual')} readOnly />
                                </FormItem>
                            </Col>
                            <Col span="11">
                                <FormItem label="产假："  {...fromItemLayout}>
                                    <Input {...init('maternity')}  readOnly />
                                </FormItem>
                            </Col>
                        </Row>
                        <Row>
                            <Col span="11">
                                <FormItem label="婚假："  {...fromItemLayout}>
                                    <Input {...init('marriage')} readOnly  />
                                </FormItem>
                            </Col>
                            <Col span="11">
                                <FormItem label="探亲假："  {...fromItemLayout}>
                                    <Input {...init('parental')}  readOnly />
                                </FormItem>
                            </Col>
                        </Row>
                        <Row>
                            <Col span="11">
                                <FormItem label="丧假："  {...fromItemLayout}>
                                    <Input {...init('bereavement')} readOnly  />
                                </FormItem>
                            </Col>
                            <Col span="11">
                                <FormItem label="其他假："  {...fromItemLayout}>
                                    <Input {...init('other')}  readOnly />
                                </FormItem>
                            </Col>
                        </Row>
                    </Form>
                </Dialog>
            </div>
        );

    }
}
const styles = {
    colstyle: {
        display: 'inline-block',
        marginRight: '5px',
    }
}